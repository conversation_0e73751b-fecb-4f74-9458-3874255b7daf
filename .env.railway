# Railway Production Environment Variables
# =======================================
# These variables are set automatically by Railway
# This file serves as documentation of expected variables

# Database Configuration (Railway)
# DATABASE_URL=postgresql://postgres:password@host:port/database  # Set by Railway
# REDIS_URL=redis://host:port  # Set by Railway

# Environment
ENVIRONMENT=production

# API Configuration
API_HOST=0.0.0.0
# PORT=8000  # Set by Railway

# Authentication
# JWT_SECRET_KEY=production-secret-key  # Set via Railway variables
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration - Allow Railway domains
CORS_ORIGINS=["https://*.railway.app", "https://*.up.railway.app", "https://betbet.com"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
DEBUG=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30