# Import Fix Report
Generated: Tue Jul 29 15:26:18 CAT 2025

## Files Fixed
- ✅ frontend/web/src/app/picks/page.tsx
- ✅ frontend/web/src/app/leaderboard/page.tsx
- ✅ frontend/web/src/app/expert-portal/page.tsx
- ✅ frontend/web/src/app/experts/[id]/page.tsx

## Changes Made

### Import Replacements
- `expertAnalystApi` → `api.experts`
- `walletApi` → `api.wallet`
- `multiServiceAPI` → `api`
- `realLeaderboardAPI` → `api.leaderboards`

### Type Imports
- Commented out old type imports
- Added temporary type definitions
- Created `frontend/web/src/lib/temp-types.ts`

## Next Steps

1. **Test the application** to ensure no import errors
2. **Update API calls** to match unified client methods
3. **Replace temporary types** with proper types from unified client
4. **Remove temp-types.ts** once proper types are in place

## Verification Commands

```bash
# Check for remaining old imports
grep -r "expert-analyst-api\|wallet-api\|multi-service-api" frontend/web/src/

# Check for TypeScript errors
npm run type-check
```
