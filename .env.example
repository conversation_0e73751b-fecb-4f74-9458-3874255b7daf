# Database Configuration - R<PERSON><PERSON>CE WITH SECURE VALUES
POSTGRES_DB=betbet_platform
POSTGRES_USER=betbet_user
POSTGRES_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD
POSTGRES_HOST=postgres
POSTGRES_PORT=5433

# Database URLs for each service
DATABASE_URL_GAMING=**********************************************************/betbet_gaming
DATABASE_URL_BETTING=**********************************************************/betbet_betting
DATABASE_URL_TRADING=**********************************************************/betbet_trading
DATABASE_URL_EXPERTS=**********************************************************/betbet_experts
DATABASE_URL_SPORTS=**********************************************************/betbet_sports
DATABASE_URL_LEADERBOARDS=**********************************************************/betbet_leaderboards

# Redis Configuration - REPLACE WITH SECURE VALUES
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_TO_SECURE_REDIS_PASSWORD

# JWT Configuration - REPLACE WITH CRYPTOGRAPHICALLY SECURE SECRET
JWT_SECRET_KEY=CHANGE_THIS_TO_CRYPTOGRAPHICALLY_SECURE_JWT_SECRET_64_CHARS_MIN
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Gateway Configuration
API_GATEWAY_PORT=8000
API_GATEWAY_HOST=0.0.0.0

# Service Ports
GAMING_SERVICE_PORT=8001
BETTING_SERVICE_PORT=8002
TRADING_SERVICE_PORT=8003
EXPERT_SERVICE_PORT=8004
SPORTS_SERVICE_PORT=8005
LEADERBOARDS_SERVICE_PORT=8006
WEBSOCKET_SERVICE_PORT=8007

# Frontend Configuration
WEB_FRONTEND_PORT=3000
ADMIN_FRONTEND_PORT=3001
MOBILE_EXPO_PORT=19006

# Gaming Engine URLs for Frontend
NEXT_PUBLIC_GAMING_ENGINE_URL=http://localhost:8000
NEXT_PUBLIC_GAMING_ENGINE_WS_URL=ws://localhost:8080

# External API Keys (replace with your actual keys)
ODDS_API_KEY=your-odds-api-key
SPORTS_DATA_API_KEY=your-sports-data-api-key
PAYMENT_GATEWAY_KEY=your-payment-gateway-key
EMAIL_SERVICE_API_KEY=your-email-service-key

# Environment
NODE_ENV=development
LOG_LEVEL=info

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:19006
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# PgAdmin Configuration - REPLACE WITH SECURE VALUES
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGLADMIN_DEFAULT_PASSWORD=CHANGE_THIS_TO_SECURE_PGADMIN_PASSWORD
PGADMIN_LISTEN_PORT=5050

# Monitoring and Health Check
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=3s
HEALTH_CHECK_RETRIES=3

# File Upload Configuration
MAX_FILE_SIZE=10MB
UPLOAD_PATH=/app/uploads

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_ENTRIES=1000

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Notification Configuration
PUSH_NOTIFICATION_KEY=your-push-notification-key
SMS_PROVIDER_API_KEY=your-sms-provider-key

# Analytics
ANALYTICS_API_KEY=your-analytics-key

# Feature Flags
ENABLE_MOBILE_APP=true
ENABLE_ADMIN_PANEL=true
ENABLE_TRADING=true
ENABLE_EXPERT_ANALYSIS=true
ENABLE_SPORTS_ANALYSIS=true
ENABLE_LEADERBOARDS=true