# Deployment Configuration Cleanup

## Summary

This cleanup addresses the audit finding regarding conflicting deployment configurations. The BetBet platform now uses a unified Docker Compose approach for both development and production deployments.

## Files Removed

The following conflicting deployment files have been identified for removal:

### 1. Legacy Single-Service Deployment Files
- `railway.json` - Configured only for gaming-engine service
- `vercel.json` - Configured only for gaming-engine service  
- `Dockerfile` (root level) - Gaming-engine specific Dockerfile
- `frontend/web/railway.json` - Conflicting web frontend config

### 2. Incomplete/Duplicate Files
- `docker-compose.production.yml` - Duplicate of our new `docker-compose.prod.yml`
- `frontend/web/docker-compose.production.yml` - Service-specific compose file
- `frontend/web/Dockerfile.production` - Legacy production Dockerfile

## Current Unified Deployment Strategy

### Development
- **Primary**: `docker-compose.yml` with `.env` file
- **Usage**: `docker-compose up -d`
- **Features**: Development tools, volume mounts, hot reload

### Production  
- **Primary**: `docker-compose.prod.yml` with `.env.production` file
- **Usage**: `./scripts/deploy-production.sh`
- **Features**: Security hardening, optimized images, no dev tools

## Recommended Actions

1. **Remove Conflicting Files**: Delete the legacy deployment files listed above
2. **Update Documentation**: Ensure README.md reflects the new unified strategy
3. **CI/CD Update**: Update any deployment pipelines to use the new configuration
4. **Team Training**: Brief team on new deployment processes

## Benefits of Unified Approach

1. **Consistency**: Same orchestration tool (Docker Compose) for all environments
2. **Simplicity**: Single configuration approach reduces confusion
3. **Security**: Production configuration has proper security hardening
4. **Maintainability**: Fewer configurations to maintain and update

## Deployment Commands

```bash
# Development
docker-compose up -d

# Production
cp .env.production.example .env.production
# Edit .env.production with secure credentials
./scripts/deploy-production.sh

# Production with checks only
./scripts/deploy-production.sh check
```

## Security Improvements

- All secrets moved to environment variables
- Production images run as non-root users
- Management tools disabled in production
- Network isolation with localhost-only binding for internal services
- Health checks and restart policies configured