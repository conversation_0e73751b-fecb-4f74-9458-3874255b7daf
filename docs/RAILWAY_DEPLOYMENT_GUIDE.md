# BetBet Platform - Railway Deployment Guide

## 🚀 Deployment Architecture

Railway deployment follows a microservices approach where each service is deployed independently:

### Services to Deploy:
1. **PostgreSQL Database** (Railway addon)
2. **API Gateway** (Node.js)
3. **Gaming Engine** (Python/FastAPI)
4. **Custom Betting** (Python/FastAPI)
5. **Odds Exchange** (Python/FastAPI)
6. **Expert Analysis** (Python/FastAPI)
7. **Sports Analysis** (Python/FastAPI)
8. **Leaderboards** (Python/FastAPI)
9. **Web Frontend** (Next.js)

## 📋 Step-by-Step Deployment

### Step 1: Database Setup
```bash
# Create PostgreSQL database on Railway
railway add postgresql

# Get database connection URL
railway variables
# Copy DATABASE_URL for use in services
```

### Step 2: Deploy Backend Services

Each service needs these environment variables:
```env
DATABASE_URL=postgresql://postgres:password@host:port/database
CORS_ORIGINS=["https://your-frontend.railway.app", "https://your-api-gateway.railway.app"]
SERVICE_PORT=8001  # (varies per service)
```

**Deploy each service:**
```bash
# Gaming Engine
cd services/gaming-engine
railway login
railway link  # Link to your Railway project
railway up

# Custom Betting
cd ../custom-betting
railway link
railway up

# Continue for all services...
```

### Step 3: Deploy API Gateway

**Environment Variables:**
```env
DATABASE_URL=postgresql://...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...

# Service URLs (update with actual Railway URLs)
GAMING_SERVICE_URL=https://gaming-engine-production.up.railway.app
CUSTOM_BETTING_SERVICE_URL=https://custom-betting-production.up.railway.app
ODDS_EXCHANGE_SERVICE_URL=https://odds-exchange-production.up.railway.app
EXPERT_ANALYSIS_SERVICE_URL=https://expert-analysis-production.up.railway.app
SPORTS_ANALYSIS_SERVICE_URL=https://sports-analysis-production.up.railway.app
LEADERBOARDS_SERVICE_URL=https://leaderboards-production.up.railway.app
```

### Step 4: Deploy Frontend

**Environment Variables:**
```env
NODE_ENV=production
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_API_BASE_URL=https://api-gateway-production.up.railway.app
NEXT_PUBLIC_WS_URL=wss://api-gateway-production.up.railway.app
```

## 🔧 Configuration Files

### API Gateway Service URLs (Update in services/api-gateway/src/index.js)

```javascript
const services = {
  gaming: {
    target: process.env.GAMING_SERVICE_URL || 'http://localhost:8001',
    pathRewrite: { '^/api/gaming': '' }
  },
  'custom-betting': {
    target: process.env.CUSTOM_BETTING_SERVICE_URL || 'http://localhost:8002',
    pathRewrite: { '^/api/custom-betting': '' }
  },
  // ... continue for all services
};
```

## 📦 Dockerfile Configurations

Each service already has production Dockerfiles:
- `services/*/Dockerfile.prod` for backend services
- `frontend/web/Dockerfile.prod` for frontend

## 🔑 Environment Variable Management

### 1. Centralized .env for local development
### 2. Railway environment variables for production
### 3. Clerk keys must match between frontend and backend

## 🌐 Domain Configuration

After deployment, you'll have URLs like:
- API Gateway: `https://api-gateway-production.up.railway.app`
- Frontend: `https://frontend-production.up.railway.app`
- Individual services: `https://service-name-production.up.railway.app`

## 🚀 Quick Deploy Commands

```bash
# 1. Deploy Database
railway add postgresql

# 2. Deploy API Gateway
cd services/api-gateway
railway up

# 3. Deploy Backend Services (in parallel)
for service in gaming-engine custom-betting odds-exchange expert-analysts sports-analysis leaderboards; do
  cd services/$service && railway up &
done

# 4. Deploy Frontend
cd frontend/web
railway up
```

## 📊 Health Checks

After deployment, verify all services:
```bash
curl https://your-api-gateway.railway.app/health
curl https://your-frontend.railway.app
```

## 🔍 Troubleshooting

1. **Database Connection Issues**: Check DATABASE_URL format
2. **CORS Errors**: Update CORS_ORIGINS with production URLs
3. **Authentication Issues**: Verify Clerk keys match
4. **Service Communication**: Ensure API Gateway has correct service URLs

## 📈 Scaling Considerations

- Each service can be scaled independently
- Database connection pooling is configured
- API Gateway handles load balancing
- Frontend is static and CDN-optimized

This setup provides a production-ready, scalable microservices architecture on Railway.