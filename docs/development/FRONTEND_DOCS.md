# BetBet Gaming Engine - Frontend Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Technology Stack](#technology-stack)
4. [Project Structure](#project-structure)
5. [Key Features](#key-features)
6. [Development Setup](#development-setup)
7. [Components Guide](#components-guide)
8. [State Management](#state-management)
9. [API Integration](#api-integration)
10. [Testing](#testing)
11. [Deployment](#deployment)
12. [Performance](#performance)
13. [Security](#security)

## Overview

The BetBet Gaming Engine frontend is a modern, responsive web application built with Next.js 14 and TypeScript. It provides a comprehensive P2P gaming platform with real-time multiplayer capabilities, tournament systems, spectator betting, and mobile-first responsive design.

### Core Features

- **Real-time Gaming**: WebSocket-powered live gaming sessions
- **Tournament System**: Complete bracket management and leaderboards
- **Spectator Betting**: Live betting on ongoing games with real-time odds
- **Mobile-First Design**: Responsive interface optimized for all devices
- **Authentication**: Secure JWT-based authentication system
- **State Management**: Zustand-powered global state management

## Architecture

### Frontend Architecture

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
├─────────────────────────────────────────────────────────┤
│  Next.js Pages  │  React Components  │  UI Components   │
├─────────────────────────────────────────────────────────┤
│                   Business Logic Layer                  │
├─────────────────────────────────────────────────────────┤
│   Zustand Store   │   Custom Hooks   │   Utilities      │
├─────────────────────────────────────────────────────────┤
│                  Data Access Layer                      │
├─────────────────────────────────────────────────────────┤
│   API Client      │   WebSocket      │   Local Storage  │
├─────────────────────────────────────────────────────────┤
│                   External Services                     │
├─────────────────────────────────────────────────────────┤
│   Gaming API      │   WebSocket      │   Authentication │
│                   │   Server         │   Service        │
└─────────────────────────────────────────────────────────┘
```

## Technology Stack

### Core Technologies

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: TailwindCSS + ShadCN UI
- **State Management**: Zustand
- **HTTP Client**: Axios
- **Real-time**: Socket.io Client
- **Testing**: Jest + React Testing Library

### Dependencies

```json
{
  "dependencies": {
    "next": "14.2.30",
    "react": "^18",
    "typescript": "^5",
    "zustand": "^5.0.6",
    "axios": "^1.10.0",
    "socket.io-client": "^4.8.1",
    "tailwindcss": "^3.4.1",
    "@radix-ui/*": "Various UI components"
  },
  "devDependencies": {
    "jest": "^30.0.4",
    "@testing-library/react": "^16.3.0",
    "@testing-library/jest-dom": "^6.6.3"
  }
}
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── games/             # Game-related pages
│   ├── sessions/          # Session management
│   ├── tournaments/       # Tournament system
│   ├── spectate/         # Spectator features
│   ├── leaderboard/      # Rankings and stats
│   └── profile/          # User profiles
├── components/            # Reusable React components
│   ├── layout/           # Layout components
│   └── ui/               # ShadCN UI components
├── lib/                  # Core utilities and services
│   ├── api.ts            # API client
│   ├── websocket.ts      # WebSocket management
│   └── utils.ts          # Helper utilities
├── store/                # State management
│   └── gameStore.ts      # Zustand store
├── types/                # TypeScript type definitions
└── __tests__/            # Test suites
    ├── components/       # Component tests
    ├── utils/            # Utility tests
    └── setup.ts          # Test configuration
```

## Key Features

### 1. Real-time Gaming System

#### Gaming Sessions
- **Session Creation**: Players can create custom gaming sessions
- **Session Browser**: Discover and join active sessions
- **Real-time Gameplay**: WebSocket-powered live interactions
- **Spectator Mode**: Watch games in progress

#### Game Types Supported
- **Trivia Games**: Question-based competitions
- **Card Games**: Poker, Blackjack variants
- **Puzzle Games**: Logic and strategy games
- **Action Games**: Fast-paced reaction games

### 2. Tournament System

#### Tournament Features
- **Bracket Management**: Single/double elimination tournaments
- **Registration System**: Entry fees and participant management
- **Live Brackets**: Real-time tournament progression
- **Prize Distribution**: Automated prize pool management

#### Tournament Types
- **Scheduled Tournaments**: Fixed start times
- **On-demand Tournaments**: Start when full
- **Ladder Tournaments**: Ongoing ranking system

### 3. Spectator Betting

#### Betting Features
- **Live Odds**: Real-time betting odds calculation
- **Multiple Bet Types**: Winner, score predictions, props
- **Betting Pool**: Shared prize pools
- **Live Chat**: Spectator interaction

### 4. Mobile-First Design

#### Responsive Features
- **Bottom Navigation**: Mobile-optimized navigation
- **Touch Gestures**: Swipe and tap interactions
- **Adaptive Layouts**: Screen size optimization
- **Performance**: Optimized for mobile networks

## Development Setup

### Prerequisites

```bash
Node.js >= 18.0.0
npm >= 8.0.0
```

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd frontend/web

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

### Environment Variables

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8002
NEXT_PUBLIC_WS_URL=ws://localhost:8003

# Authentication
NEXT_PUBLIC_JWT_SECRET=your-jwt-secret

# Development
NODE_ENV=development
```

## Components Guide

### Layout Components

#### Navigation Component
```typescript
// Usage
import Navigation from '@/components/layout/Navigation';

<Navigation onMobileMenuClick={handleMobileMenu} />
```

**Features:**
- Responsive design with mobile drawer
- User authentication state
- Connection status indicator
- Search functionality

#### MobileNavigation Component
```typescript
// Usage
import MobileNavigation from '@/components/layout/MobileNavigation';

<MobileNavigation />
```

**Features:**
- Bottom navigation bar
- Touch-friendly targets
- Active route highlighting
- User profile integration

### Game Components

#### GameSessionInterface
Real-time gaming interface with:
- Live game state display
- Participant management
- Chat integration
- Spectator controls

#### TournamentBracket
Interactive tournament visualization:
- Drag-and-drop bracket navigation
- Live match updates
- Prize pool display
- Progress indicators

### UI Components (ShadCN)

All UI components follow the ShadCN design system:

```typescript
// Button variants
<Button variant="default">Primary</Button>
<Button variant="outline">Secondary</Button>
<Button variant="ghost">Subtle</Button>

// Card layouts
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
  </CardHeader>
  <CardContent>Content</CardContent>
</Card>
```

## State Management

### Zustand Store Architecture

```typescript
// Store structure
interface GameStore {
  // Authentication
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  
  // Gaming State
  currentSession: GameSession | null;
  currentGameState: GameState | null;
  participants: Participant[];
  
  // Connection
  isConnected: boolean;
  connectionError: string | null;
  
  // Actions
  login: (user: User, token: string) => void;
  logout: () => void;
  setCurrentSession: (session: GameSession) => void;
  // ... more actions
}
```

### State Hooks

```typescript
// Authentication hook
const { user, isAuthenticated, login, logout } = useAuth();

// Gaming hook
const { currentSession, participants, isConnected } = useCurrentSession();

// Games data hook
const { games, sessions, featuredGames } = useGames();
```

## API Integration

### API Client Structure

```typescript
class ApiClient {
  private baseURL: string;
  private timeout: number;
  
  // Authentication
  async signIn(credentials: LoginCredentials): Promise<AuthResponse>
  async signUp(userData: RegisterData): Promise<AuthResponse>
  
  // Games
  async getGames(params?: GameFilters): Promise<Game[]>
  async getFeaturedGames(limit?: number): Promise<Game[]>
  
  // Sessions
  async createSession(data: CreateSessionData): Promise<GameSession>
  async joinSession(sessionId: string): Promise<void>
  
  // Tournaments
  async getTournaments(filters?: TournamentFilters): Promise<Tournament[]>
  async getTournamentBracket(tournamentId: string): Promise<TournamentBracket>
}
```

### WebSocket Integration

```typescript
class WebSocketClient {
  connect(sessionId: string): Promise<void>
  disconnect(): void
  sendMessage(type: string, data: any): void
  
  // Event handlers
  onGameStateUpdate(callback: (state: GameState) => void): void
  onParticipantUpdate(callback: (participants: Participant[]) => void): void
  onChatMessage(callback: (message: ChatMessage) => void): void
}
```

## Testing

### Test Structure

```
__tests__/
├── components/           # Component tests
│   ├── Navigation.test.tsx
│   └── HomePage.test.tsx
├── utils/               # Utility tests
│   └── test-utils.tsx   # Test helpers
├── store/               # State tests
│   └── gameStore.test.ts
└── setup.ts             # Jest configuration
```

### Testing Utilities

```typescript
// Custom render with providers
import { render, screen } from '../utils/test-utils';

// Mock data factories
const mockUser = createMockUser();
const mockGame = createMockGame();
const mockSession = createMockSession();

// Store testing
updateMockStore({ isAuthenticated: true, user: mockUser });
```

### Running Tests

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch

# Specific test file
npm test -- HomePage.test.tsx
```

## Deployment

### Production Build

```bash
# Build optimized bundle
npm run build

# Start production server
npm start
```

### Environment Configuration

#### Production Environment
```env
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=https://api.betbet.com
NEXT_PUBLIC_WS_URL=wss://ws.betbet.com
```

#### Staging Environment
```env
NODE_ENV=staging
NEXT_PUBLIC_API_BASE_URL=https://staging-api.betbet.com
NEXT_PUBLIC_WS_URL=wss://staging-ws.betbet.com
```

### Deployment Checklist

- [ ] Environment variables configured
- [ ] API endpoints accessible
- [ ] WebSocket connections working
- [ ] SSL certificates installed
- [ ] CDN configured for assets
- [ ] Error monitoring enabled
- [ ] Performance monitoring active

## Performance

### Optimization Strategies

#### Code Splitting
```typescript
// Dynamic imports for heavy components
const TournamentBracket = dynamic(() => import('./TournamentBracket'));
const SpectatorInterface = dynamic(() => import('./SpectatorInterface'));
```

#### Image Optimization
```typescript
// Next.js Image component
import Image from 'next/image';

<Image
  src="/game-thumbnail.jpg"
  alt="Game thumbnail"
  width={400}
  height={300}
  priority={isAboveTheFold}
/>
```

#### Performance Metrics

- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

### Monitoring

#### Performance Monitoring
- **Core Web Vitals**: Lighthouse integration
- **Real User Monitoring**: User experience metrics
- **Error Tracking**: JavaScript error monitoring

## Security

### Security Measures

#### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Token Refresh**: Automatic token renewal
- **Secure Storage**: HttpOnly cookies for sensitive data

#### XSS Prevention
```typescript
// Input sanitization
import DOMPurify from 'dompurify';

const sanitizedHTML = DOMPurify.sanitize(userInput);
```

#### CSRF Protection
- **SameSite Cookies**: CSRF token protection
- **Origin Validation**: Request origin verification

#### Content Security Policy
```typescript
// CSP headers
const securityHeaders = {
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-eval';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
  `
};
```

### Security Checklist

- [ ] Input validation and sanitization
- [ ] Secure authentication implementation
- [ ] HTTPS enforcement
- [ ] CSP headers configured
- [ ] Dependency vulnerability scanning
- [ ] Error message sanitization
- [ ] Rate limiting implementation

## Contributing

### Development Workflow

1. **Feature Development**
   ```bash
   git checkout -b feature/new-feature
   npm run dev
   # Develop feature
   npm test
   npm run build
   ```

2. **Code Quality**
   ```bash
   npm run lint        # ESLint checking
   npm run type-check  # TypeScript validation
   npm test           # Test coverage
   ```

3. **Pull Request**
   - Feature complete and tested
   - Documentation updated
   - Tests passing
   - Code reviewed

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration
- **Prettier**: Code formatting
- **Conventional Commits**: Commit message format

---

## Support and Maintenance

### Troubleshooting

#### Common Issues

1. **SWC Binary Error**
   ```bash
   # Fallback to Babel
   npm install --save-dev @babel/core @babel/preset-env @babel/preset-react @babel/preset-typescript
   ```

2. **WebSocket Connection Issues**
   ```typescript
   // Check connection status
   const { isConnected, connectionError } = useCurrentSession();
   ```

3. **Authentication Token Expiry**
   ```typescript
   // Token refresh handling
   api.interceptors.response.use(
     response => response,
     error => handleTokenRefresh(error)
   );
   ```

### Performance Monitoring

#### Key Metrics
- **Bundle Size**: Monitor JavaScript bundle size
- **Render Performance**: React DevTools profiling
- **Network Requests**: API response times
- **Memory Usage**: Memory leak detection

### Future Enhancements

#### Planned Features
- **Progressive Web App**: Offline functionality
- **Voice Chat**: Real-time voice communication
- **Advanced Analytics**: Detailed performance metrics
- **Internationalization**: Multi-language support

---

*This documentation is maintained by the BetBet Gaming Engine development team. For questions or contributions, please refer to the project repository.*