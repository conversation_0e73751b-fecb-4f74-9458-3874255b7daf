# BetBet Platform API Documentation

## Overview

The BetBet Platform provides a comprehensive REST API for gaming, betting, and trading operations. The API is documented using OpenAPI 3.0 specification and includes interactive documentation via Swagger UI.

## Quick Start

### 1. View API Documentation

When the platform is running, you can access the interactive API documentation at:

- **Swagger UI**: http://localhost:8000/api-docs
- **Redirect**: http://localhost:8000/docs (redirects to Swagger UI)
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 2. Authentication

All API endpoints (except public ones) require JWT authentication:

```bash
curl -H "Authorization: Bearer <your-jwt-token>" \
     http://localhost:8000/api/gaming/games
```

### 3. Rate Limiting

API requests are rate-limited:
- **Authenticated users**: 100 requests per 15-minute window
- **Unauthenticated**: 20 requests per 15-minute window

## API Structure

The API is organized into the following main modules:

### Gaming (`/api/gaming/`)
- Game management
- Session creation and management
- Tournament operations
- Player interactions

### Betting (`/api/betting/`)
- Betting market management
- Bet placement and management
- Market odds and outcomes
- Settlement operations

### Trading (`/api/trading/`)
- Order placement and management
- Market data access
- Position management
- Trading analytics

### Expert Analysis (`/api/experts/`)
- Expert predictions and analysis
- Subscription management
- Performance tracking
- Community interactions

### Sports Analysis (`/api/sports/`)
- Sports data and statistics
- Predictive analytics
- Real-time event updates
- Natural language queries

### Leaderboards (`/api/leaderboards/`)
- Player rankings and statistics
- Achievement tracking
- Competitive events
- Performance metrics

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

## Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## WebSocket API

Real-time features are available via WebSocket connections:

- **Endpoint**: `ws://localhost:8080/ws`
- **Authentication**: Send JWT token after connection
- **Message Format**: JSON

### WebSocket Message Types

```json
// Authentication
{
  "type": "authenticate",
  "token": "your-jwt-token"
}

// Subscribe to updates
{
  "type": "subscribe",
  "channels": ["market_updates", "game_events"]
}

// Send message
{
  "type": "send_message",
  "room": "game_123",
  "message": "Hello world"
}
```

## Development

### Adding New Endpoints

1. **Update OpenAPI Specification**: Edit `docs/api/openapi.yaml`
2. **Implement Service**: Add endpoint to appropriate service
3. **Update Proxy Rules**: Modify API Gateway routing if needed
4. **Test**: Use Swagger UI to test new endpoints

### Generating Client Code

You can generate client code from the OpenAPI specification:

```bash
# Install OpenAPI Generator
npm install -g @openapitools/openapi-generator-cli

# Generate TypeScript client
openapi-generator-cli generate \
  -i http://localhost:8000/openapi.json \
  -g typescript-axios \
  -o ./generated/typescript-client

# Generate Python client  
openapi-generator-cli generate \
  -i http://localhost:8000/openapi.json \
  -g python \
  -o ./generated/python-client
```

### API Testing

The platform includes comprehensive API tests:

```bash
# Run API tests
npm run test:api

# Run with coverage
npm run test:api:coverage

# Run specific test suite
npm run test:gaming-api
```

## Security

### Authentication
- JWT tokens with configurable expiration
- Refresh token support
- Role-based access control (RBAC)

### Rate Limiting
- Per-user rate limiting
- Configurable limits per endpoint
- Burst protection

### Input Validation
- Request parameter validation
- SQL injection prevention
- XSS protection

### HTTPS
In production, ensure all API calls use HTTPS:
- Configure SSL certificates
- Redirect HTTP to HTTPS
- Use secure headers

## Monitoring

### Metrics
API metrics are available at:
- **Prometheus**: http://localhost:9090 (production only)
- **Health checks**: http://localhost:8000/health

### Logging
- Structured JSON logging
- Request/response logging
- Error tracking with request IDs

## Support

For API support:
- **Documentation**: Review this guide and Swagger UI
- **Issues**: Report bugs via GitHub issues
- **Contact**: <EMAIL>

## Changelog

### Version 1.0.0
- Initial API release
- Core gaming, betting, and trading endpoints
- WebSocket support for real-time features
- Comprehensive OpenAPI documentation