openapi: 3.0.3
info:
  title: BetBet Platform API
  description: |
    Comprehensive API for the BetBet gaming, betting, and trading platform.
    
    ## Authentication
    All endpoints require JWT authentication via the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate-limited to prevent abuse:
    - 100 requests per 15-minute window per authenticated user
    - 20 requests per 15-minute window for unauthenticated requests
    
    ## WebSocket Connections
    Real-time features are available via WebSocket at `/ws` endpoint.
    
  version: 1.0.0
  contact:
    name: BetBet Platform Support
    email: <EMAIL>
  license:
    name: Proprietary
    
servers:
  - url: https://api.betbet.com
    description: Production API
  - url: https://staging-api.betbet.com
    description: Staging API
  - url: http://localhost:8000
    description: Local Development

security:
  - BearerAuth: []

paths:
  # Health Check
  /health:
    get:
      summary: Health Check
      description: Returns the health status of the API Gateway
      tags:
        - System
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "1.0.0"

  # Gaming API
  /api/gaming/games:
    get:
      summary: List Available Games
      description: Retrieve a list of all available games
      tags:
        - Gaming
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [strategy, action, puzzle, card]
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of games
          content:
            application/json:
              schema:
                type: object
                properties:
                  games:
                    type: array
                    items:
                      $ref: '#/components/schemas/Game'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
          
    post:
      summary: Create New Game
      description: Create a new game in the platform
      tags:
        - Gaming
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateGameRequest'
      responses:
        '201':
          description: Game created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Game'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /api/gaming/sessions:
    get:
      summary: List Game Sessions
      description: Retrieve active and recent game sessions
      tags:
        - Gaming
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, completed, waiting]
        - name: game_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of game sessions
          content:
            application/json:
              schema:
                type: object
                properties:
                  sessions:
                    type: array
                    items:
                      $ref: '#/components/schemas/GameSession'
                      
    post:
      summary: Create Game Session
      description: Create a new game session
      tags:
        - Gaming
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSessionRequest'
      responses:
        '201':
          description: Session created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GameSession'

  # Betting API
  /api/betting/markets:
    get:
      summary: List Betting Markets
      description: Retrieve available betting markets
      tags:
        - Betting
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [sports, gaming, custom]
        - name: status
          in: query
          schema:
            type: string
            enum: [active, settled, suspended]
      responses:
        '200':
          description: List of betting markets
          content:
            application/json:
              schema:
                type: object
                properties:
                  markets:
                    type: array
                    items:
                      $ref: '#/components/schemas/BettingMarket'

  /api/betting/bets:
    post:
      summary: Place Bet
      description: Place a bet on a market
      tags:
        - Betting
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaceBetRequest'
      responses:
        '201':
          description: Bet placed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Bet'
        '400':
          $ref: '#/components/responses/BadRequest'
        '402':
          description: Insufficient funds
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Trading API
  /api/trading/orders:
    get:
      summary: List Orders
      description: Retrieve user's trading orders
      tags:
        - Trading
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [open, filled, cancelled]
        - name: market
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                      
    post:
      summary: Place Order
      description: Place a new trading order
      tags:
        - Trading
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaceOrderRequest'
      responses:
        '201':
          description: Order placed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      
  schemas:
    Game:
      type: object
      required:
        - id
        - name
        - category
        - status
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Texas Hold'em Poker"
        description:
          type: string
          example: "Classic poker game with community cards"
        category:
          type: string
          enum: [strategy, action, puzzle, card]
          example: "card"
        status:
          type: string
          enum: [active, inactive, maintenance]
          example: "active"
        min_players:
          type: integer
          minimum: 1
          example: 2
        max_players:
          type: integer
          minimum: 1
          example: 10
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    
    GameSession:
      type: object
      required:
        - id
        - game_id
        - status
        - participants
      properties:
        id:
          type: string
          format: uuid
        game_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [waiting, active, completed, cancelled]
        participants:
          type: array
          items:
            $ref: '#/components/schemas/Participant'
        created_at:
          type: string
          format: date-time
        started_at:
          type: string
          format: date-time
        ended_at:
          type: string
          format: date-time
          
    Participant:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        username:
          type: string
        status:
          type: string
          enum: [joined, playing, finished, disconnected]
        score:
          type: integer
        joined_at:
          type: string
          format: date-time
    
    BettingMarket:
      type: object
      required:
        - id
        - title
        - category
        - status
        - outcomes
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          example: "Match Winner: Team A vs Team B"
        description:
          type: string
        category:
          type: string
          enum: [sports, gaming, custom]
        status:
          type: string
          enum: [active, settled, suspended]
        outcomes:
          type: array
          items:
            $ref: '#/components/schemas/MarketOutcome'
        closes_at:
          type: string
          format: date-time
        settles_at:
          type: string
          format: date-time
          
    MarketOutcome:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "Team A Wins"
        odds:
          type: number
          format: decimal
          example: 1.85
        is_winner:
          type: boolean
          nullable: true
    
    Bet:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user_id:
          type: string
          format: uuid
        market_id:
          type: string
          format: uuid
        outcome_id:
          type: string
          format: uuid
        stake:
          type: number
          format: decimal
          example: 10.00
        odds:
          type: number
          format: decimal
          example: 1.85
        potential_payout:
          type: number
          format: decimal
          example: 18.50
        status:
          type: string
          enum: [pending, won, lost, void]
        placed_at:
          type: string
          format: date-time
    
    Order:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user_id:
          type: string
          format: uuid
        market:
          type: string
          example: "BTC/USDT"
        side:
          type: string
          enum: [buy, sell]
        type:
          type: string
          enum: [market, limit, stop]
        quantity:
          type: number
          format: decimal
        price:
          type: number
          format: decimal
        filled_quantity:
          type: number
          format: decimal
          default: 0
        status:
          type: string
          enum: [open, filled, cancelled, partially_filled]
        created_at:
          type: string
          format: date-time
          
    CreateGameRequest:
      type: object
      required:
        - name
        - category
        - min_players
        - max_players
      properties:
        name:
          type: string
          maxLength: 100
        description:
          type: string
          maxLength: 500
        category:
          type: string
          enum: [strategy, action, puzzle, card]
        min_players:
          type: integer
          minimum: 1
          maximum: 100
        max_players:
          type: integer
          minimum: 1
          maximum: 100
          
    CreateSessionRequest:
      type: object
      required:
        - game_id
      properties:
        game_id:
          type: string
          format: uuid
        private:
          type: boolean
          default: false
        password:
          type: string
          minLength: 4
          maxLength: 20
          
    PlaceBetRequest:
      type: object
      required:
        - market_id
        - outcome_id
        - stake
      properties:
        market_id:
          type: string
          format: uuid
        outcome_id:
          type: string
          format: uuid
        stake:
          type: number
          format: decimal
          minimum: 0.01
          maximum: 10000.00
          
    PlaceOrderRequest:
      type: object
      required:
        - market
        - side
        - type
        - quantity
      properties:
        market:
          type: string
          example: "BTC/USDT"
        side:
          type: string
          enum: [buy, sell]
        type:
          type: string
          enum: [market, limit, stop]
        quantity:
          type: number
          format: decimal
          minimum: 0.00001
        price:
          type: number
          format: decimal
          minimum: 0.00001
        stop_price:
          type: number
          format: decimal
          minimum: 0.00001
    
    Error:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          example: "VALIDATION_ERROR"
        message:
          type: string
          example: "Invalid request parameters"
        details:
          type: object
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
          format: uuid
          
  responses:
    BadRequest:
      description: Invalid request parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
            
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "UNAUTHORIZED"
            message: "Valid JWT token required"
            
    Forbidden:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "FORBIDDEN"
            message: "Insufficient permissions for this operation"
            
    RateLimited:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "RATE_LIMITED"
            message: "Rate limit exceeded. Try again later."
      headers:
        X-RateLimit-Limit:
          schema:
            type: integer
          description: Request limit per time window
        X-RateLimit-Remaining:
          schema:
            type: integer
          description: Remaining requests in current window
        X-RateLimit-Reset:
          schema:
            type: integer
          description: Unix timestamp when rate limit resets

tags:
  - name: System
    description: System health and monitoring endpoints
  - name: Gaming
    description: Game management and session operations
  - name: Betting
    description: Betting markets and bet placement
  - name: Trading
    description: Trading operations and order management