# BetBet Platform - Unified API Routes
## 🎯 **Single Source of Truth for All API Routing**

### **📋 Core Principles:**
1. **All requests go through API Gateway** (`http://localhost:8000`)
2. **Consistent route patterns** across all services
3. **No direct service calls** from frontend
4. **Standardized endpoint naming**

---

## **🌐 API Gateway Routes**

### **Base URL:** `http://localhost:8000`

| Service | Frontend Route | Gateway Rewrite | Service Endpoint | Port |
|---------|---------------|-----------------|------------------|------|
| **Gaming Engine** | `/api/gaming/*` | `/api/v1/gaming/*` | `/api/v1/gaming/*` | 8001 |
| **Custom Betting** | `/api/custom-betting/*` | `/api/v1/custom-betting/*` | `/api/v1/custom-betting/*` | 8002 |
| **Trading** | `/api/trading/*` | `/api/v1/trading/*` | `/api/v1/trading/*` | 8003 |
| **Experts** | `/api/experts/*` | `/api/v1/experts/*` | `/api/v1/experts/*` | 8004 |
| **Sports** | `/api/sports/*` | `/api/v1/analysis/*` | `/api/v1/analysis/*` | 8005 |
| **Leaderboards** | `/api/leaderboards/*` | `/api/v1/leaderboards/*` | `/api/v1/leaderboards/*` | 8006 |
| **Wallet** | `/api/wallet/*` | `/api/v1/wallet/*` | `/api/v1/wallet/*` | 8007 |
| **Events** | `/api/events/*` | `/api/v1/events/*` | `/api/v1/events/*` | 8008 |
| **User Sync** | `/api/user-sync/*` | `/api/v1/user-sync/*` | `/api/v1/user-sync/*` | 8009 |

---

## **🎮 Gaming Engine Endpoints**

**Base:** `http://localhost:8000/api/gaming`

### **Games**
- `GET /games` → List available games
- `GET /games/{game_id}` → Get game details
- `POST /games/{game_id}/validate` → Validate game moves

### **Sessions**
- `GET /sessions` → List game sessions
- `POST /sessions` → Create new session
- `GET /sessions/{session_id}` → Get session details
- `POST /sessions/{session_id}/join` → Join session
- `POST /sessions/{session_id}/move` → Submit move

### **Analytics**
- `GET /analytics/overview` → Gaming overview
- `GET /analytics/users/{user_id}/stats` → User statistics
- `GET /analytics/users/{user_id}/activity` → User activity

### **Chess**
- `GET /chess/games` → List chess games
- `POST /chess/games` → Create chess game
- `GET /chess/games/{game_id}` → Get chess game

---

## **💰 Wallet Service Endpoints**

**Base:** `http://localhost:8000/api/wallet`

### **Balance**
- `GET /balance/{user_id}` → Get user balance
- `POST /balance/{user_id}/lock` → Lock funds
- `POST /balance/{user_id}/unlock` → Unlock funds

### **Transactions**
- `GET /transactions/{user_id}` → Get transaction history
- `POST /deposit` → Create deposit
- `POST /withdraw` → Create withdrawal
- `POST /transfer` → Transfer between users

### **Analytics**
- `GET /analytics/overview` → Wallet overview
- `GET /analytics/revenue` → Revenue analytics

---

## **🎯 Expert Analysis Endpoints**

**Base:** `http://localhost:8000/api/experts`

### **Experts**
- `GET /list` → List experts
- `GET /{expert_id}` → Get expert details
- `POST /` → Create expert (admin)

### **Picks**
- `GET /picks` → List expert picks
- `POST /picks` → Create pick
- `GET /picks/{pick_id}` → Get pick details

### **Analytics**
- `GET /analytics/overview` → Expert analytics
- `GET /analytics/{expert_id}/stats` → Expert performance

---

## **⚽ Sports Analysis Endpoints**

**Base:** `http://localhost:8000/api/sports`

### **Fixtures**
- `GET /fixtures` → List fixtures
- `GET /fixtures/{fixture_id}` → Get fixture details

### **Teams & Leagues**
- `GET /teams` → List teams
- `GET /leagues` → List leagues
- `GET /teams/{team_id}` → Get team details

### **Analytics**
- `GET /analytics/overview` → Sports analytics

---

## **📊 Leaderboards Endpoints**

**Base:** `http://localhost:8000/api/leaderboards`

### **Rankings**
- `GET /unified` → Unified leaderboard
- `GET /gaming` → Gaming leaderboard
- `GET /trading` → Trading leaderboard

### **Achievements**
- `GET /achievements` → List achievements
- `GET /achievements/{user_id}` → User achievements

---

## **🎲 Custom Betting Endpoints**

**Base:** `http://localhost:8000/api/custom-betting`

### **Markets**
- `GET /markets` → List markets
- `POST /markets` → Create market
- `GET /markets/{market_id}` → Get market details

### **Positions**
- `GET /positions` → List positions
- `POST /positions` → Create position

---

## **📈 Trading Endpoints**

**Base:** `http://localhost:8000/api/trading`

### **Markets**
- `GET /markets` → List trading markets
- `GET /markets/{market_id}` → Get market details

### **Orders**
- `GET /orders` → List orders
- `POST /orders` → Create order
- `GET /positions` → List positions

---

## **🔧 Health & System Endpoints**

### **Health Checks**
- `GET /health` → API Gateway health
- `GET /api/gaming/health` → Gaming service health
- `GET /api/wallet/health` → Wallet service health
- `GET /api/experts/health` → Expert service health
- `GET /api/sports/health` → Sports service health
- `GET /api/leaderboards/health` → Leaderboards service health
- `GET /api/custom-betting/health` → Custom betting service health
- `GET /api/trading/health` → Trading service health

### **Documentation**
- `GET /docs` → API Gateway docs
- `GET /api/gaming/docs` → Gaming service docs (redirected)

---

## **🔐 Authentication**

### **Public Routes (No Auth Required):**
- `/api/sports/fixtures`
- `/api/sports/leagues`
- `/api/leaderboards/public`
- `/health`
- `/docs`

### **Authenticated Routes:**
- All `/api/gaming/*` endpoints
- All `/api/wallet/*` endpoints
- All `/api/experts/*` endpoints
- All `/api/trading/*` endpoints
- All `/api/custom-betting/*` endpoints

### **Admin Routes:**
- `/api/*/admin/*` endpoints
- Market creation endpoints
- User management endpoints

---

## **📱 Frontend Integration**

### **Base Configuration:**
```typescript
const API_BASE_URL = 'http://localhost:8000';

export const API_ROUTES = {
  gaming: '/api/gaming',
  wallet: '/api/wallet',
  experts: '/api/experts',
  sports: '/api/sports',
  leaderboards: '/api/leaderboards',
  customBetting: '/api/custom-betting',
  trading: '/api/trading'
};
```

### **Example Usage:**
```typescript
// ✅ Correct - Through API Gateway
const response = await fetch(`${API_BASE_URL}/api/gaming/sessions`);

// ❌ Wrong - Direct service call
const response = await fetch('http://localhost:8001/api/v1/gaming/sessions');
```
