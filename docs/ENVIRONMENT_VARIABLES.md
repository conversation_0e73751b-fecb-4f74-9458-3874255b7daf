# BetBet Platform - Environment Variables
## 🔧 **Clean Configuration Guide**

This document defines the **ONLY** environment variables needed for the BetBet platform.

---

## **📋 Required Environment Variables**

### **Frontend (Web & Admin)**
```env
# API Gateway - SINGLE endpoint for all services
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# WebSocket Gateway - SINGLE endpoint for real-time features  
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# Optional: Environment identifier
NODE_ENV=development
```

### **Mobile App**
```env
# API Gateway - SINGLE endpoint for all services
API_GATEWAY_URL=http://localhost:8000

# WebSocket Gateway
WS_GATEWAY_URL=ws://localhost:8000

# Clerk Authentication
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
```

### **Backend Services**
```env
# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=betbet_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=betbet_secure_password

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=betbet_redis_password

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...

# Service Ports (Internal Only)
GAMING_ENGINE_PORT=8001
CUSTOM_BETTING_PORT=8002
TRADING_PORT=8003
EXPERTS_PORT=8004
SPORTS_PORT=8005
LEADERBOARDS_PORT=8006
WALLET_PORT=8007
EVENTS_PORT=8008
USER_SYNC_PORT=8009

# API Gateway
API_GATEWAY_PORT=8000
```

---

## **❌ Deprecated Variables (DO NOT USE)**

These variables are **deprecated** and should be removed:

```env
# ❌ REMOVE - Direct service URLs
NEXT_PUBLIC_GAMING_API_URL=http://localhost:8001
NEXT_PUBLIC_WALLET_API_URL=http://localhost:8007
NEXT_PUBLIC_EXPERT_API_URL=http://localhost:8004
NEXT_PUBLIC_SPORTS_API_URL=http://localhost:8005
NEXT_PUBLIC_TRADING_API_URL=http://localhost:8003
NEXT_PUBLIC_CUSTOM_BETTING_API_URL=http://localhost:8002
NEXT_PUBLIC_LEADERBOARDS_API_URL=http://localhost:8006

# ❌ REMOVE - Multiple WebSocket URLs
NEXT_PUBLIC_WS_BASE_URL=ws://localhost:8002
NEXT_PUBLIC_GAMING_WS_URL=ws://localhost:8001
NEXT_PUBLIC_TRADING_WS_URL=ws://localhost:8003

# ❌ REMOVE - Legacy API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000  # Use NEXT_PUBLIC_API_BASE_URL instead
API_BASE_URL=http://localhost:8000         # Use NEXT_PUBLIC_API_BASE_URL instead
```

---

## **🎯 Configuration Principles**

### **1. Single Gateway Approach**
- ✅ **One API endpoint**: `http://localhost:8000`
- ✅ **One WebSocket endpoint**: `ws://localhost:8000`
- ❌ **No direct service calls**: Never use `localhost:8001-8009` in frontend

### **2. Environment-Specific URLs**
```env
# Development
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# Staging
NEXT_PUBLIC_API_BASE_URL=https://api-staging.betbet.com

# Production
NEXT_PUBLIC_API_BASE_URL=https://api.betbet.com
```

### **3. Security Best Practices**
- ✅ **Public keys only** in frontend environment variables
- ✅ **Secret keys only** in backend environment variables
- ✅ **No hardcoded URLs** in source code

---

## **📁 Environment Files Structure**

### **Frontend Web (.env.local)**
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
```

### **Frontend Admin (.env.local)**
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
```

### **Mobile App (.env)**
```env
API_GATEWAY_URL=http://localhost:8000
WS_GATEWAY_URL=ws://localhost:8000
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
```

### **Docker Compose (.env)**
```env
# Database
POSTGRES_HOST=betbet-postgres
POSTGRES_PORT=5432
POSTGRES_DB=betbet_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=betbet_secure_password

# Redis
REDIS_HOST=betbet-redis
REDIS_PORT=6379
REDIS_PASSWORD=betbet_redis_password

# Clerk
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...
```

---

## **🔧 Migration Guide**

### **Step 1: Update Frontend Environment Files**
```bash
# Remove old variables
unset NEXT_PUBLIC_GAMING_API_URL
unset NEXT_PUBLIC_WALLET_API_URL
unset NEXT_PUBLIC_EXPERT_API_URL
# ... (remove all direct service URLs)

# Add new unified variables
echo "NEXT_PUBLIC_API_BASE_URL=http://localhost:8000" >> .env.local
echo "NEXT_PUBLIC_WS_URL=ws://localhost:8000" >> .env.local
```

### **Step 2: Update Code References**
```typescript
// ❌ Old pattern
const GAMING_API_URL = process.env.NEXT_PUBLIC_GAMING_API_URL;

// ✅ New pattern
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
```

### **Step 3: Verify Configuration**
```bash
# Check that only gateway URLs are used
grep -r "localhost:800[1-9]" frontend/
# Should return no results

# Check for correct gateway usage
grep -r "localhost:8000" frontend/
# Should show API Gateway usage only
```

---

## **✅ Verification Checklist**

- [ ] Only `NEXT_PUBLIC_API_BASE_URL` is used in frontend
- [ ] No direct service URLs (`localhost:8001-8009`) in frontend code
- [ ] All API calls go through API Gateway (`localhost:8000`)
- [ ] WebSocket connections use gateway URL
- [ ] Environment files are clean and minimal
- [ ] No deprecated variables in use
- [ ] Configuration works across all environments

---

## **🚨 Common Issues**

### **Issue: 404 Errors from API Calls**
**Cause**: Using direct service URLs instead of gateway
**Solution**: Update to use `NEXT_PUBLIC_API_BASE_URL=http://localhost:8000`

### **Issue: CORS Errors**
**Cause**: Direct service calls bypassing gateway
**Solution**: Ensure all requests go through API Gateway

### **Issue: Authentication Failures**
**Cause**: Mixed API clients with different auth patterns
**Solution**: Use unified API client with consistent auth

---

## **📞 Support**

If you encounter issues with environment configuration:

1. Check this guide for correct variable names
2. Verify no deprecated variables are in use
3. Ensure all frontend calls use the API Gateway
4. Test with the unified API client

**Remember**: The goal is **ONE API endpoint, ONE configuration pattern, ZERO direct service calls**.
