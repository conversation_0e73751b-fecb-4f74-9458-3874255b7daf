#!/usr/bin/env python3

import requests
import json

# Test wallet API endpoints
base_url = "http://localhost:8007"
test_user_id = "user_test123"

def test_wallet_balance():
    """Test getting wallet balance"""
    try:
        response = requests.get(f"{base_url}/api/v1/wallet/balance/{test_user_id}")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Balance retrieved: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Error retrieving balance")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_create_deposit():
    """Test creating a deposit (which might auto-create wallet)"""
    try:
        deposit_data = {
            "amount": 100.0,
            "payment_method": "demo",
            "currency": "USD"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/wallet/deposit?user_id={test_user_id}",
            json=deposit_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Deposit Status: {response.status_code}")
        print(f"Deposit Response: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ Deposit successful")
        else:
            print("❌ Deposit failed")
            
    except Exception as e:
        print(f"❌ Deposit exception: {e}")

if __name__ == "__main__":
    print("🧪 Testing Wallet API...")
    
    print("\n📊 Testing wallet balance...")
    test_wallet_balance()
    
    print("\n💰 Testing deposit (auto-create wallet)...")
    test_create_deposit()
    
    print("\n📊 Testing wallet balance after deposit...")
    test_wallet_balance()