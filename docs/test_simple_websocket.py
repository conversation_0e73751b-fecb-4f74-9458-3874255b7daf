#!/usr/bin/env python3
"""
Simple WebSocket test for the basic test endpoint
"""

import asyncio
import websockets
import json
import sys

async def test_simple_websocket():
    """Test simple WebSocket connection without authentication"""
    
    url = "ws://localhost:8001/ws/test"
    
    print(f"🔌 Testing simple WebSocket connection to: {url}")
    
    try:
        async with websockets.connect(url, timeout=5) as websocket:
            print("✅ WebSocket connection successful!")
            
            # Wait for welcome message
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=3)
                print(f"📥 Received welcome: {welcome}")
            except asyncio.TimeoutError:
                print("⏰ No welcome message received")
            
            # Send a test message
            test_message = "Hello WebSocket!"
            await websocket.send(test_message)
            print(f"📤 Sent message: {test_message}")
            
            # Wait for echo response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3)
                print(f"📥 Received echo: {response}")
            except asyncio.TimeoutError:
                print("⏰ No echo response received")
            
            print("✅ Simple WebSocket test successful!")
            return True
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ Connection closed: {e.code} - {e.reason}")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ Invalid status code: {e.status_code}")
        return False
    except ConnectionRefusedError:
        print("❌ Connection refused - server not running or wrong port")
        return False
    except asyncio.TimeoutError:
        print("❌ Connection timeout")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {type(e).__name__}: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple WebSocket Connection Test")
    print("=" * 40)
    
    try:
        success = asyncio.run(test_simple_websocket())
        if success:
            print("\n✅ Test completed successfully")
            sys.exit(0)
        else:
            print("\n❌ Test failed")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        sys.exit(1)
