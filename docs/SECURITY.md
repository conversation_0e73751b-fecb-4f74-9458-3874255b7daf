# BetBet Platform Security Guide

## 🔒 CRITICAL SECURITY REQUIREMENTS

### Environment Variables & Secrets Management

**NEVER COMMIT SENSITIVE DATA TO VERSION CONTROL**

1. **Required Secure Environment Variables:**
   ```bash
   # Generate cryptographically secure values
   POSTGRES_PASSWORD=<64+ character secure password>
   REDIS_PASSWORD=<64+ character secure password>
   JWT_SECRET_KEY=<128+ character cryptographically secure secret>
   PGADMIN_DEFAULT_EMAIL=<secure-admin-email>
   PGLADMIN_DEFAULT_PASSWORD=<64+ character secure password>
   ```

2. **Password Requirements:**
   - Minimum 64 characters for database passwords
   - Minimum 128 characters for JWT secrets
   - Use cryptographically secure random generators
   - Include uppercase, lowercase, numbers, and special characters

3. **JWT Secret Generation Example:**
   ```bash
   # Use a cryptographically secure method
   openssl rand -base64 128 | tr -d "\n"
   # OR
   python -c "import secrets; print(secrets.token_urlsafe(128))"
   ```

### Production Deployment Security

1. **Never Use Development Configurations in Production:**
   - Use `docker-compose.prod.yml` for production
   - Disable development tools (Pg<PERSON>dmin, Redis Commander)
   - Remove volume mounts that expose source code

2. **Network Security:**
   - Never expose management ports (5050, 8081) publicly
   - Use VPN or bastion hosts for admin access
   - Implement proper firewall rules

3. **Container Security:**
   - Run containers as non-root users
   - Use minimal base images
   - Regularly update dependencies
   - Scan images for vulnerabilities

### Database Security

1. **PostgreSQL Hardening:**
   ```sql
   -- Revoke public schema privileges
   REVOKE CREATE ON SCHEMA public FROM PUBLIC;
   REVOKE ALL ON DATABASE betbet_platform FROM PUBLIC;
   
   -- Create application-specific roles with minimal privileges
   CREATE ROLE betbet_app_role;
   GRANT CONNECT ON DATABASE betbet_platform TO betbet_app_role;
   GRANT USAGE ON SCHEMA public TO betbet_app_role;
   -- Grant only necessary table privileges
   ```

2. **Redis Security:**
   - Always use password authentication
   - Disable dangerous commands in production
   - Use TLS for connections
   - Regular backup and encryption at rest

### API Security

1. **Authentication & Authorization:**
   - Implement proper JWT validation
   - Use short-lived access tokens
   - Implement refresh token rotation
   - Rate limiting on all endpoints

2. **Input Validation:**
   - Validate all user inputs
   - Use parameterized queries
   - Implement CORS properly
   - Sanitize outputs

### Monitoring & Auditing

1. **Security Monitoring:**
   - Log all authentication attempts
   - Monitor for unusual database activity
   - Alert on failed login patterns
   - Track privileged operations

2. **Regular Security Tasks:**
   - Rotate secrets quarterly
   - Update dependencies monthly
   - Perform penetration testing
   - Review access logs weekly

## 🚨 INCIDENT RESPONSE

If secrets are compromised:

1. **Immediate Actions:**
   - Rotate all affected credentials immediately
   - Invalidate all JWT tokens
   - Check logs for unauthorized access
   - Reset all user sessions

2. **Investigation:**
   - Determine scope of compromise
   - Identify affected systems and data
   - Document timeline of events
   - Notify relevant stakeholders

## 📋 SECURITY CHECKLIST

Before production deployment:

- [ ] All environment variables use secure, randomly generated values
- [ ] No hardcoded secrets in code or configuration files
- [ ] Production docker-compose configuration created
- [ ] Management tools (PgAdmin, Redis Commander) disabled
- [ ] Database users have minimal required privileges
- [ ] All containers run as non-root users
- [ ] TLS certificates properly configured
- [ ] Firewall rules implemented
- [ ] Security monitoring enabled
- [ ] Backup and recovery procedures tested
- [ ] Incident response plan documented
- [ ] Security audit completed

## 🔧 TOOLS & COMMANDS

### Generate Secure Passwords
```bash
# 64-character password
openssl rand -base64 48 | tr -d "\n"

# 128-character JWT secret  
openssl rand -base64 96 | tr -d "\n"

# Alternative with Python
python -c "import secrets; print(secrets.token_urlsafe(64))"
```

### Security Scanning
```bash
# Scan Docker images
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image betbet-api-gateway

# Check for secrets in code
git-secrets --scan
truffleHog --regex --entropy=False .
```

---

**Remember: Security is not a one-time setup but an ongoing process. Regularly review and update these practices.**