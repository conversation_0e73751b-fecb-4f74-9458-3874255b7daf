# Frontend API Migration Guide
## 🚀 **Migrating to Unified API Routes**

This guide helps you migrate existing frontend code to use the new unified API routing system.

---

## **📋 Migration Checklist**

### **✅ Before Migration**
- [ ] Backup your current API client files
- [ ] Review current API calls in your components
- [ ] Identify direct service calls (port 8001-8009)
- [ ] Test current functionality

### **✅ After Migration**
- [ ] All API calls go through port 8000 (API Gateway)
- [ ] No direct service calls (localhost:8001, etc.)
- [ ] Consistent error handling
- [ ] Updated import statements

---

## **🔄 Code Migration Examples**

### **1. Replace Individual API Clients**

#### **❌ Before (Multiple Clients):**
```typescript
// Old approach - multiple API clients
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import { walletApi } from '@/lib/wallet-api';
import { apiClient } from '@/lib/api';

// Different base URLs and inconsistent patterns
const experts = await expertAnalystApi.getExperts();
const balance = await walletApi.getBalance(userId);
const sessions = await apiClient.get('/api/gaming/sessions');
```

#### **✅ After (Unified Client):**
```typescript
// New approach - single unified client
import api from '@/lib/api-client-unified';

// Consistent API through gateway
const experts = await api.experts.getExperts();
const balance = await api.wallet.getBalance(userId);
const sessions = await api.gaming.getSessions();
```

### **2. Update Direct Service Calls**

#### **❌ Before (Direct Service Calls):**
```typescript
// Direct calls to services - WRONG!
const response = await fetch('http://localhost:8001/api/v1/gaming/sessions');
const experts = await axios.get('http://localhost:8004/api/v1/experts/list');
```

#### **✅ After (Gateway Routing):**
```typescript
// All calls through API Gateway - CORRECT!
import api from '@/lib/api-client-unified';

const sessions = await api.gaming.getSessions();
const experts = await api.experts.getExperts();
```

### **3. Update Environment Variables**

#### **❌ Before (Multiple URLs):**
```env
NEXT_PUBLIC_GAMING_API_URL=http://localhost:8001
NEXT_PUBLIC_WALLET_API_URL=http://localhost:8007
NEXT_PUBLIC_EXPERT_API_URL=http://localhost:8004
```

#### **✅ After (Single Gateway URL):**
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

### **4. Update Component Imports**

#### **❌ Before:**
```typescript
import { ExpertAnalystApiClient } from '@/lib/expert-analyst-api';
import { WalletApiClient } from '@/lib/wallet-api';
import { ClerkApiService } from '@/lib/clerk-api';
```

#### **✅ After:**
```typescript
import api from '@/lib/api-client-unified';
// OR for specific APIs:
import { gamingApi, walletApi, expertsApi } from '@/lib/api-client-unified';
```

---

## **🎯 Service-Specific Migrations**

### **Gaming Engine**
```typescript
// ❌ Before
const stats = await clerkApi.getUserStats(userId);
const activity = await clerkApi.getUserActivity(userId);

// ✅ After
const stats = await api.gaming.getUserStats(userId);
const activity = await api.gaming.getUserActivity(userId);
```

### **Wallet Service**
```typescript
// ❌ Before
const balance = await walletApi.getBalance(userId);
const transactions = await walletApi.getTransactions(userId);

// ✅ After
const balance = await api.wallet.getBalance(userId);
const transactions = await api.wallet.getTransactions(userId);
```

### **Expert Analysis**
```typescript
// ❌ Before
const experts = await expertAnalystApi.getExperts({ verified_only: true });
const expert = await expertAnalystApi.getExpert(expertId);

// ✅ After
const experts = await api.experts.getExperts({ verified_only: true });
const expert = await api.experts.getExpert(expertId);
```

### **Sports Analysis**
```typescript
// ❌ Before
const fixtures = await fetch('/api/sports/fixtures');

// ✅ After
const fixtures = await api.sports.getFixtures();
```

---

## **🔧 Authentication Migration**

### **❌ Before (Multiple Auth Setups):**
```typescript
// Different auth patterns for each service
expertApi.setAuthToken(token);
walletApi.setAuthToken(token);
clerkApi.setAuthToken(token);
```

### **✅ After (Unified Auth):**
```typescript
// Single auth setup for all services
api.setAuthToken(token);
```

---

## **📊 Error Handling Migration**

### **❌ Before (Inconsistent Error Handling):**
```typescript
try {
  const experts = await expertApi.getExperts();
} catch (error) {
  // Different error formats from different services
  console.error('Expert API error:', error);
}

try {
  const balance = await walletApi.getBalance(userId);
} catch (error) {
  // Different error handling needed
  console.error('Wallet API error:', error);
}
```

### **✅ After (Consistent Error Handling):**
```typescript
try {
  const [experts, balance] = await Promise.all([
    api.experts.getExperts(),
    api.wallet.getBalance(userId)
  ]);
} catch (error) {
  // Consistent error format from unified client
  console.error('API error:', error.response?.data || error.message);
}
```

---

## **🎨 React Hook Migration**

### **❌ Before (Multiple Hooks):**
```typescript
import { useApi } from '@/hooks/useApi';
import { useMultiServiceApi } from '@/hooks/useApi';
import { useWalletApi } from '@/hooks/useWalletApi';

function MyComponent() {
  const gamingApi = useApi();
  const multiApi = useMultiServiceApi();
  const walletApi = useWalletApi();
  
  // Multiple API instances to manage
}
```

### **✅ After (Single Hook):**
```typescript
import { useUnifiedApi } from '@/hooks/useUnifiedApi';

function MyComponent() {
  const api = useUnifiedApi();
  
  // Single API instance for everything
  const loadData = async () => {
    const [stats, balance, experts] = await Promise.all([
      api.gaming.getUserStats(userId),
      api.wallet.getBalance(userId),
      api.experts.getExperts()
    ]);
  };
}
```

---

## **🚨 Common Migration Issues**

### **1. Port Numbers in URLs**
```typescript
// ❌ WRONG - Don't hardcode service ports
const url = 'http://localhost:8001/api/v1/gaming/sessions';

// ✅ CORRECT - Use unified routes
const sessions = await api.gaming.getSessions();
```

### **2. Mixed API Patterns**
```typescript
// ❌ WRONG - Mixing old and new patterns
const experts = await api.experts.getExperts(); // New
const balance = await oldWalletApi.getBalance(userId); // Old

// ✅ CORRECT - Use unified pattern consistently
const experts = await api.experts.getExperts();
const balance = await api.wallet.getBalance(userId);
```

### **3. Direct Fetch Calls**
```typescript
// ❌ WRONG - Direct fetch bypasses gateway
const response = await fetch('/api/gaming/sessions');

// ✅ CORRECT - Use unified client
const sessions = await api.gaming.getSessions();
```

---

## **✅ Migration Verification**

After migration, verify that:

1. **No direct service calls**: Search for `localhost:800[1-9]` in your code
2. **Consistent imports**: All API calls use the unified client
3. **Single auth setup**: Only one `setAuthToken` call needed
4. **Gateway routing**: All requests go through port 8000
5. **Error handling**: Consistent error format across all APIs

### **Quick Verification Script:**
```bash
# Search for old patterns that need migration
grep -r "localhost:800[1-9]" frontend/
grep -r "expert-analyst-api" frontend/
grep -r "wallet-api" frontend/
grep -r "multi-service-api" frontend/
```

---

## **🎯 Next Steps**

1. **Update one component at a time** to avoid breaking changes
2. **Test each migration** before moving to the next
3. **Remove old API client files** after migration is complete
4. **Update documentation** to reflect new patterns
5. **Train team members** on the unified API approach

---

## **📞 Support**

If you encounter issues during migration:

1. Check the [Unified API Routes Documentation](./UNIFIED_API_ROUTES.md)
2. Review the [API Client Examples](../frontend/web/src/lib/api-client-unified.ts)
3. Test individual endpoints using the health checks:
   ```typescript
   await api.system.healthAll(); // Check all services
   ```
