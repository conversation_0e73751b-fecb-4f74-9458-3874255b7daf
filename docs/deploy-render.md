# Deploy BetBet to Render.com

## Quick Setup (5 minutes)
1. Go to render.com and sign up
2. Connect your GitHub account
3. Create new Web Service
4. Point to our repository
5. Set:
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `python main.py`
   - Environment: `services/gaming-engine`

## Why Render?
- Free tier available
- Automatic HTTPS
- Easy database integration
- Better Python support than Railway
- More reliable deployments