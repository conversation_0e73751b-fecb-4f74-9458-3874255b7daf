#!/usr/bin/env python3
"""
Test script to verify all imports work before Railway deployment
"""

import sys
from pathlib import Path

# Add services to Python path
sys.path.insert(0, str(Path.cwd()))

def test_shared_services():
    """Test shared services imports"""
    print("🔍 Testing shared services imports...")
    
    try:
        from services.shared.core.database.connection import get_database_write, get_database_read, _db_manager
        print("✅ Database connection imports: OK")
    except ImportError as e:
        print(f"❌ Database connection imports: {e}")
        return False
    
    try:
        from services.shared.core.auth.jwt_handler import get_auth_manager
        from services.shared.core.auth.models import TokenData
        print("✅ Auth imports: OK")
    except ImportError as e:
        print(f"⚠️  Auth imports: {e} (will use fallback)")
    
    return True

def test_gaming_engine_imports():
    """Test gaming engine imports"""
    print("\n🎮 Testing gaming engine imports...")
    
    try:
        # Change to gaming engine directory for proper imports
        gaming_engine_path = Path.cwd() / "services" / "gaming-engine"
        sys.path.insert(0, str(gaming_engine_path))
        
        from app.api.dependencies import get_settings, get_database_session
        print("✅ Dependencies imports: OK")
    except ImportError as e:
        print(f"❌ Dependencies imports: {e}")
        return False
    
    try:
        from app.api.v1 import games, sessions, tournaments, spectator_bets, analytics
        print("✅ API routes imports: OK")
    except ImportError as e:
        print(f"❌ API routes imports: {e}")
        return False
    
    return True

def test_main_application():
    """Test main application imports"""
    print("\n🚀 Testing main application imports...")
    
    try:
        gaming_engine_path = Path.cwd() / "services" / "gaming-engine"
        sys.path.insert(0, str(gaming_engine_path))
        
        from app.main import app
        print("✅ Main application imports: OK")
        return True
    except ImportError as e:
        print(f"❌ Main application imports: {e}")
        return False

def main():
    """Run all import tests"""
    print("🎯 BetBet Platform - Import Test Suite")
    print("=" * 50)
    
    all_passed = True
    
    all_passed &= test_shared_services()
    all_passed &= test_gaming_engine_imports()
    all_passed &= test_main_application()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All imports successful! Ready for Railway deployment.")
        sys.exit(0)
    else:
        print("💥 Import errors found. Fix before deploying to Railway.")
        sys.exit(1)

if __name__ == "__main__":
    main()