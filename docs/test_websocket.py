#!/usr/bin/env python3
"""
Simple WebSocket test script to check if chess WebSocket endpoints are accessible
"""

import asyncio
import websockets
import json
import sys

async def test_chess_websocket():
    """Test chess WebSocket connection"""
    
    # Test token (this is a sample Clerk token format)
    test_token = "eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yenhDT2NnbkJxV2NrQ3plMVdvT3JSbnU1Q0QiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************.EKYXRELgRte_sUVe7eV097N-w30yGTvpDdmEltKHygUBpAwfdubiWVvsYH-pgcdLOjIoSdrbhCZcxhPk360IuY8xy3o_KGMCt0HpmBeHhWJWhoczuD6W3OHeQOun3PqPxdOe-PnFifvu2xouAe7TM2vYW5RP7COuwppBpIEN8RUaXYBK1OpOGlc49g8PakJLjHCtTO6OXFzcUFPPA_71DCQVpi2zdxUz62jWBKnmyfw2qlqo7s7fgtYlOzhNO9B5zJQD7pca8cOliLCn_rvWhdtLt9c3_kHwVkxNb0dJwcigCjvZPWhUv1wN7qLt1nzt3TIuzPVe80TUhP-ltbjweQ"
    
    # Test different WebSocket URLs
    test_urls = [
        f"ws://localhost:8001/ws/chess/chess-game-3?token={test_token}",
        f"ws://localhost:8001/ws/chess/test-game?token={test_token}",
        f"ws://localhost:8080/ws/chess/chess-game-3?token={test_token}",
    ]
    
    for url in test_urls:
        print(f"\n🔌 Testing WebSocket connection to: {url}")
        try:
            # Try to connect with a timeout
            async with websockets.connect(url, timeout=5) as websocket:
                print("✅ WebSocket connection successful!")
                
                # Send a test message
                test_message = {
                    "type": "ping",
                    "timestamp": "2024-01-15T12:00:00Z"
                }
                
                await websocket.send(json.dumps(test_message))
                print("📤 Sent ping message")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print(f"📥 Received response: {response}")
                except asyncio.TimeoutError:
                    print("⏰ No response received within timeout")
                
                break  # Success, no need to test other URLs
                
        except websockets.exceptions.ConnectionClosed as e:
            print(f"❌ Connection closed: {e.code} - {e.reason}")
        except websockets.exceptions.InvalidStatusCode as e:
            print(f"❌ Invalid status code: {e.status_code}")
        except ConnectionRefusedError:
            print("❌ Connection refused - server not running or wrong port")
        except asyncio.TimeoutError:
            print("❌ Connection timeout")
        except Exception as e:
            print(f"❌ Connection failed: {type(e).__name__}: {e}")
    
    print("\n🔍 Testing basic HTTP endpoints...")
    
    # Test if the gaming engine is responding
    import aiohttp
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8001/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Gaming engine health check: {data}")
                else:
                    print(f"❌ Gaming engine health check failed: {response.status}")
        except Exception as e:
            print(f"❌ Gaming engine not accessible: {e}")
        
        try:
            async with session.get("http://localhost:8080/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ WebSocket manager health check: {data}")
                else:
                    print(f"❌ WebSocket manager health check failed: {response.status}")
        except Exception as e:
            print(f"❌ WebSocket manager not accessible: {e}")

if __name__ == "__main__":
    print("🧪 BetBet Chess WebSocket Connection Test")
    print("=" * 50)
    
    try:
        asyncio.run(test_chess_websocket())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        sys.exit(1)
    
    print("\n✅ Test completed")
