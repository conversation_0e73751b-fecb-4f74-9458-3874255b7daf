# BetBet Platform - Production Deployment Guide

## Overview

This guide addresses all security and deployment concerns identified in the security audit. The BetBet platform now follows enterprise-grade security practices and automated deployment procedures.

## 🔒 Security Improvements Implemented

### 1. CRITICAL: Secrets Management ✅
- **Issue**: Hardcoded secrets in docker-compose.yml
- **Resolution**: All secrets moved to environment variables
- **Verification**: `grep -r "password\|secret" docker-compose.yml` returns only `${VAR}` references

### 2. CRITICAL: Production-Ready Configuration ✅
- **Issue**: Missing production configuration
- **Resolution**: Complete `docker-compose.prod.yml` with security hardening
- **Features**:
  - Non-root users for all services
  - Localhost-only binding for internal services
  - Health checks for all services
  - Proper restart policies
  - Security options (`no-new-privileges:true`)

## 🚀 Deployment Strategy

### Development Deployment
```bash
# Use development configuration
docker-compose up -d
```

### Production Deployment
```bash
# Use production configuration with automated migrations
docker-compose -f docker-compose.prod.yml up -d

# Or use the deployment script with security validation
./scripts/deploy-production.sh
```

### Scaling Services
```bash
# Scale specific services (Docker Compose method)
docker-compose -f docker-compose.prod.yml up -d --scale api-gateway=3 --scale gaming-engine=2

# For true high-availability, consider Docker Swarm or Kubernetes
```

## 📋 Pre-Deployment Checklist

### Security Validation
- [ ] All environment variables use secure, randomly generated values
- [ ] No hardcoded secrets in any configuration files
- [ ] Production Dockerfiles create optimized, secure images
- [ ] All services run as non-root users
- [ ] Management tools disabled in production

### Database Migrations
- [ ] Database migration container runs automatically
- [ ] All schema changes are version-controlled
- [ ] Migration rollback procedures documented

### Service Health
- [ ] All services have health check endpoints
- [ ] Health checks configured in production compose
- [ ] Monitoring and alerting configured

## 🔧 Database Migration System

### Automated Migration Process
1. **Migration Container**: Runs before application services
2. **Version Tracking**: All migrations tracked in `schema_migrations` table
3. **Rollback Safety**: Each migration runs in a transaction
4. **Idempotent**: Safe to run multiple times

### Adding New Migrations
```bash
# Create new migration file
echo "ALTER TABLE users ADD COLUMN new_field VARCHAR(100);" > \
  services/db-migrations/versions/$(date +%Y%m%d_%H%M%S)_add_user_field.sql

# Migration will be applied automatically on next deployment
```

## 🏗️ Service Architecture

### Backend Services (Python/FastAPI)
- **Gaming Engine** (`8001`): Game logic and sessions
- **Betting Service** (`8002`): Betting markets and bets
- **Odds Exchange** (`8003`): Trading and order matching
- **Expert Analysis** (`8004`): Expert predictions and analytics
- **Sports Analysis** (`8005`): Sports data and predictions
- **Leaderboards** (`8006`): Rankings and achievements

### Frontend Services (Node.js/Next.js)
- **Web Frontend** (`3000`): Main user interface
- **Admin Dashboard** (`3001`): Administrative interface

### Infrastructure Services
- **API Gateway** (`8000`): Unified API entry point
- **WebSocket Manager** (`8080`): Real-time communications
- **PostgreSQL** (`5433`): Primary database
- **Redis** (`6379`): Cache and session store

## 🔍 Health Monitoring

### Health Check Endpoints
All services expose `/health` endpoints returning:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

### Service Dependencies
Services wait for dependencies to be healthy before starting:
- All backend services wait for PostgreSQL and Redis
- Frontend services wait for API Gateway and WebSocket Manager
- Database migrations run before all application services

## 🔒 Security Features

### Network Security
- Internal services bound to localhost only
- External services (web, api-gateway) properly exposed
- All services run in isolated Docker network

### Container Security
- Multi-stage Docker builds for minimal images
- Non-root users for all services
- No development tools in production images
- Security options prevent privilege escalation

### Secrets Management
- All secrets in environment variables
- Strong password generation guidelines
- Regular credential rotation procedures

## 📊 Monitoring Integration

### Prometheus Metrics
- Service health metrics
- Application performance metrics  
- Infrastructure monitoring

### Log Aggregation
- Structured JSON logging
- Centralized log collection
- Error tracking with request IDs

## 🚨 Troubleshooting

### Common Issues

**Services not starting:**
```bash
# Check service logs
docker-compose -f docker-compose.prod.yml logs [service-name]

# Check dependencies
docker-compose -f docker-compose.prod.yml ps
```

**Database connection issues:**
```bash
# Verify database is healthy
docker exec betbet-postgres-prod pg_isready -U $POSTGRES_USER

# Check migration status
docker exec betbet-postgres-prod psql -U $POSTGRES_USER -d $POSTGRES_DB \
  -c "SELECT * FROM schema_migrations ORDER BY applied_at;"
```

**Health check failures:**
```bash
# Test health endpoint directly
curl http://localhost:8000/health

# Check container health status
docker inspect --format='{{.State.Health.Status}}' betbet-api-gateway-prod
```

## 🔄 Deployment Automation

### CI/CD Integration
```yaml
# Example GitHub Actions workflow
name: Production Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Security validation
        run: ./scripts/deploy-production.sh check
      - name: Deploy to production
        run: ./scripts/deploy-production.sh
        env:
          POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
          REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}
          JWT_SECRET_KEY: ${{ secrets.JWT_SECRET_KEY }}
```

## 📈 Scaling Considerations

### Horizontal Scaling
For production workloads, consider:
- **Docker Swarm**: Built-in orchestration with service scaling
- **Kubernetes**: Advanced orchestration with auto-scaling
- **Load Balancing**: Nginx/HAProxy for traffic distribution

### Database Scaling
- Read replicas for read-heavy workloads
- Connection pooling (PgBouncer)
- Database partitioning for large datasets

## 🔐 Security Maintenance

### Regular Tasks
- [ ] Rotate secrets quarterly
- [ ] Update dependencies monthly  
- [ ] Security scans weekly
- [ ] Access review monthly
- [ ] Backup verification weekly

### Incident Response
- Database backup and recovery procedures
- Service rollback procedures
- Security incident response plan
- Communication protocols

---

**This deployment guide ensures the BetBet platform meets enterprise security and reliability standards.**