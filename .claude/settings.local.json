{"permissions": {"allow": ["Bash(rm:*)", "Bash(docker logs:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npm install:*)", "Bash(npm start)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(docker-compose down:*)", "Bash(docker image rm:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(docker ps:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(./scripts/start.sh:*)", "<PERSON><PERSON>(docker run:*)", "Bash(grep:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python:*)", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5433 -U postgres -d betbet_db -c \"SELECT current_database();\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5433 -U postgres -d betbet_db -f database/schemas/wallet.sql)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(timeout:*)", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"\\dn\")", "Bash(npm run dev:*)", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -f database/migrations/005_create_chess_game.sql)", "Bash(PYTHONPATH=/Users/<USER>/Desktop/PROJECTS/BETBET/PLATFORM/BetBet python services/gaming-engine/main.py)", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"SELECT id, name, slug, category, is_active, is_featured FROM gaming_engine.games WHERE slug = ''chess'' OR name ILIKE ''%chess%'';\")", "<PERSON><PERSON>(docker restart:*)", "Bash(/dev/null)", "Bash(find:*)", "Bash(npm run build:*)", "Bash(sudo rm:*)", "Bash(npm ci:*)", "WebFetch(domain:localhost)", "Bash(PYTHONPATH=/Users/<USER>/Desktop/PROJECTS/BETBET/PLATFORM/BetBet/services python3 seed_sample_data.py)", "Bash(PYTHONPATH=/Users/<USER>/Desktop/PROJECTS/BETBET/PLATFORM/BetBet JWT_SECRET_KEY=\"BetBet_JWT_Super_Secure_Secret_Key_2024_!@#$%^&*()_+{}\" python3 seed_sample_data.py)", "Bash(psql:*)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db\" PYTHONPATH=/Users/<USER>/Desktop/PROJECTS/BETBET/PLATFORM/BetBet JWT_SECRET_KEY=\"BetBet_JWT_Super_Secure_Secret_Key_2024_!@#$%^&*()_+{}\" timeout 30 python3 seed_sample_data.py)", "Bash(PYTHONPATH=:*)", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"\nCREATE SCHEMA IF NOT EXISTS custom_betting;\n\nCREATE TABLE IF NOT EXISTS custom_betting.market_categories (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    name VARCHAR(100) NOT NULL UNIQUE,\n    description TEXT,\n    slug VARCHAR(120) UNIQUE,\n    is_active BOOLEAN DEFAULT TRUE,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n\nCREATE TABLE IF NOT EXISTS custom_betting.custom_bet_markets (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    creator_user_id UUID NOT NULL,\n    category_id UUID REFERENCES custom_betting.market_categories(id),\n    title VARCHAR(300) NOT NULL,\n    description TEXT,\n    slug VARCHAR(400) UNIQUE,\n    market_type VARCHAR(50) DEFAULT ''binary'',\n    creator_type VARCHAR(20) DEFAULT ''user'',\n    minimum_stake NUMERIC(15, 2) DEFAULT 1.00,\n    maximum_stake NUMERIC(15, 2),\n    total_volume NUMERIC(15, 2) DEFAULT 0,\n    total_participants INTEGER DEFAULT 0,\n    closes_at TIMESTAMP WITH TIME ZONE,\n    resolves_at TIMESTAMP WITH TIME ZONE,\n    status VARCHAR(20) DEFAULT ''open'',\n    resolution_source VARCHAR(200),\n    resolution_criteria TEXT,\n    winning_outcome_id UUID,\n    is_featured BOOLEAN DEFAULT FALSE,\n    dispute_count INTEGER DEFAULT 0,\n    last_disputed_at TIMESTAMP WITH TIME ZONE,\n    tags VARCHAR(50)[],\n    market_metadata JSONB,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    created_by UUID,\n    updated_by UUID\n);\n\nCREATE TABLE IF NOT EXISTS custom_betting.custom_bets (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    market_id UUID REFERENCES custom_betting.custom_bet_markets(id),\n    user_id UUID NOT NULL,\n    bet_amount NUMERIC(15, 2) NOT NULL,\n    predicted_outcome TEXT,\n    status VARCHAR(20) DEFAULT ''active'',\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n\n\\dt custom_betting.*;\n\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"\nCREATE SCHEMA IF NOT EXISTS custom_betting;\n\nCREATE TABLE IF NOT EXISTS custom_betting.market_categories (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    name VARCHAR(100) NOT NULL UNIQUE,\n    description TEXT,\n    slug VARCHAR(120) UNIQUE,\n    is_active BOOLEAN DEFAULT TRUE,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n\nCREATE TABLE IF NOT EXISTS custom_betting.custom_bet_markets (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    creator_user_id UUID NOT NULL,\n    category_id UUID REFERENCES custom_betting.market_categories(id),\n    title VARCHAR(300) NOT NULL,\n    description TEXT,\n    slug VARCHAR(400) UNIQUE,\n    market_type VARCHAR(50) DEFAULT ''binary'',\n    creator_type VARCHAR(20) DEFAULT ''user'',\n    minimum_stake NUMERIC(15, 2) DEFAULT 1.00,\n    maximum_stake NUMERIC(15, 2),\n    total_volume NUMERIC(15, 2) DEFAULT 0,\n    total_participants INTEGER DEFAULT 0,\n    closes_at TIMESTAMP WITH TIME ZONE,\n    resolves_at TIMESTAMP WITH TIME ZONE,\n    status VARCHAR(20) DEFAULT ''open'',\n    resolution_source VARCHAR(200),\n    resolution_criteria TEXT,\n    winning_outcome_id UUID,\n    is_featured BOOLEAN DEFAULT FALSE,\n    dispute_count INTEGER DEFAULT 0,\n    last_disputed_at TIMESTAMP WITH TIME ZONE,\n    tags VARCHAR(50)[],\n    market_metadata JSONB,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    created_by UUID,\n    updated_by UUID\n);\n\nCREATE TABLE IF NOT EXISTS custom_betting.custom_bets (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    market_id UUID REFERENCES custom_betting.custom_bet_markets(id),\n    user_id UUID NOT NULL,\n    bet_amount NUMERIC(15, 2) NOT NULL,\n    predicted_outcome TEXT,\n    status VARCHAR(20) DEFAULT ''active'',\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"SELECT column_name FROM information_schema.columns WHERE table_schema = ''custom_betting'' AND table_name = ''custom_bets'';\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -p 5432 -U postgres -d betbet_db -c \"SELECT column_name FROM information_schema.columns WHERE table_schema = ''custom_betting'' AND table_name = ''bet_participants'';\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -U postgres -d betbet_db -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''public'' AND table_name LIKE ''%bet%'';\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -U postgres -d betbet_db -c \"SELECT COUNT(*), COALESCE(SUM(bet_amount), 0) FROM gaming_engine.user_bets;\")", "Bash(PGPASSWORD=123Bubblegums psql -h localhost -U postgres -d betbet_db -c \"SELECT * FROM gaming_engine.user_bets ORDER BY created_at;\")", "Bash(npm cache clean:*)", "Bash(node:*)", "Bash(npx next build:*)", "Bash(NODE_ENV=development npx next dev --port 3002)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker:*)", "Bash(./scripts/test-custom-betting.sh:*)", "Bash(./scripts/seed-categories.sh:*)", "Bash(./scripts/test-custom-betting-complete.sh:*)", "Bash(./scripts/final-test.sh:*)", "Bash(cp:*)", "Bash(npx tsc:*)"], "deny": []}}