global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # PostgreSQL Database Metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 30s

  # BetBet Services
  - job_name: 'betbet-api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-gaming'
    static_configs:
      - targets: ['gaming-engine:8001']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-custom-betting'
    static_configs:
      - targets: ['custom-betting:8002']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-trading'
    static_configs:
      - targets: ['trading-engine:8003']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-experts'
    static_configs:
      - targets: ['expert-analysis:8004']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-sports'
    static_configs:
      - targets: ['sports-analysis:8005']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-leaderboards'
    static_configs:
      - targets: ['leaderboards:8006']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-wallet'
    static_configs:
      - targets: ['wallet-service:8007']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'betbet-websocket'
    static_configs:
      - targets: ['websocket-manager:8008']
    metrics_path: /metrics
    scrape_interval: 15s

  # Frontend Applications
  - job_name: 'betbet-web'
    static_configs:
      - targets: ['web:3000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'betbet-admin'
    static_configs:
      - targets: ['admin:3001']
    metrics_path: /metrics
    scrape_interval: 30s

  # Custom metrics endpoints for business logic
  - job_name: 'betbet-platform-metrics'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: /api/platform-metrics/prometheus
    scrape_interval: 30s

  - job_name: 'betbet-betting-metrics'
    static_configs:
      - targets: ['custom-betting:8002']
    metrics_path: /api/v1/custom-betting/metrics/prometheus
    scrape_interval: 30s
