# BetBet Platform - Production Alert Rules
# ========================================
# Critical alerts for production monitoring

groups:
  # Application Health Alerts
  - name: application.health
    interval: 30s
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: availability
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."
          runbook_url: "https://docs.betbet.com/runbooks/service-down"
      
      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: reliability
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }}% for {{ $labels.job }} on {{ $labels.instance }}"
          runbook_url: "https://docs.betbet.com/runbooks/high-error-rate"
      
      - alert: CriticalErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) * 100 > 10
        for: 2m
        labels:
          severity: critical
          category: reliability
        annotations:
          summary: "Critical error rate detected"
          description: "Error rate is {{ $value }}% for {{ $labels.job }} on {{ $labels.instance }}"
          runbook_url: "https://docs.betbet.com/runbooks/critical-error-rate"

  # Performance Alerts
  - name: application.performance
    interval: 30s
    rules:
      - alert: HighLatency
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket[5m])
          ) * 1000 > 100
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High latency detected"
          description: "95th percentile latency is {{ $value }}ms for {{ $labels.job }}"
          runbook_url: "https://docs.betbet.com/runbooks/high-latency"
      
      - alert: CriticalLatency
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket[5m])
          ) * 1000 > 200
        for: 2m
        labels:
          severity: critical
          category: performance
        annotations:
          summary: "Critical latency detected"
          description: "95th percentile latency is {{ $value }}ms for {{ $labels.job }}"
          runbook_url: "https://docs.betbet.com/runbooks/critical-latency"
      
      - alert: APIResponseTimeExceeded
        expr: |
          histogram_quantile(0.50, 
            rate(http_request_duration_seconds_bucket{job="gaming-engine"}[5m])
          ) * 1000 > 50
        for: 3m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "API response time SLA exceeded"
          description: "Median API response time is {{ $value }}ms (SLA: <50ms)"
          runbook_url: "https://docs.betbet.com/runbooks/api-sla-exceeded"

  # Gaming Engine Specific Alerts
  - name: gaming.engine
    interval: 15s
    rules:
      - alert: GameSessionCreationFailure
        expr: |
          rate(gaming_session_creation_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          category: business_logic
        annotations:
          summary: "Game session creation failures detected"
          description: "{{ $value }} game session creation failures per second"
          runbook_url: "https://docs.betbet.com/runbooks/session-creation-failure"
      
      - alert: ActiveGameSessionsHigh
        expr: gaming_active_sessions > 1000
        for: 1m
        labels:
          severity: warning
          category: capacity
        annotations:
          summary: "High number of active game sessions"
          description: "{{ $value }} active game sessions (threshold: 1000)"
          runbook_url: "https://docs.betbet.com/runbooks/high-active-sessions"
      
      - alert: WebSocketConnectionDrops
        expr: |
          rate(websocket_connections_dropped_total[5m]) > 1
        for: 3m
        labels:
          severity: warning
          category: connectivity
        annotations:
          summary: "WebSocket connection drops detected"
          description: "{{ $value }} WebSocket connections dropping per second"
          runbook_url: "https://docs.betbet.com/runbooks/websocket-drops"

  # Financial Transaction Alerts
  - name: financial.transactions
    interval: 10s
    rules:
      - alert: TransactionProcessingFailure
        expr: |
          rate(financial_transaction_failures_total[5m]) > 0
        for: 1m
        labels:
          severity: critical
          category: financial
        annotations:
          summary: "Financial transaction processing failures"
          description: "{{ $value }} transaction failures per second - IMMEDIATE ATTENTION REQUIRED"
          runbook_url: "https://docs.betbet.com/runbooks/transaction-failure"
      
      - alert: EscrowBalanceMismatch
        expr: |
          abs(escrow_total_deposited - escrow_total_held) > 0.01
        for: 30s
        labels:
          severity: critical
          category: financial
        annotations:
          summary: "Escrow balance mismatch detected"
          description: "Escrow balance mismatch: {{ $value }} - FINANCIAL INTEGRITY ISSUE"
          runbook_url: "https://docs.betbet.com/runbooks/escrow-mismatch"
      
      - alert: HighTransactionVolume
        expr: |
          rate(financial_transactions_total[5m]) > 100
        for: 5m
        labels:
          severity: warning
          category: capacity
        annotations:
          summary: "High transaction volume detected"
          description: "{{ $value }} transactions per second (review capacity)"
          runbook_url: "https://docs.betbet.com/runbooks/high-transaction-volume"

  # Infrastructure Alerts
  - name: infrastructure.resources
    interval: 30s
    rules:
      - alert: HighCPUUsage
        expr: |
          (100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"
          runbook_url: "https://docs.betbet.com/runbooks/high-cpu"
      
      - alert: HighMemoryUsage
        expr: |
          (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"
          runbook_url: "https://docs.betbet.com/runbooks/high-memory"
      
      - alert: DiskSpaceLow
        expr: |
          (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "Low disk space detected"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} ({{ $labels.mountpoint }})"
          runbook_url: "https://docs.betbet.com/runbooks/low-disk-space"

  # Database Alerts
  - name: database.postgresql
    interval: 30s
    rules:
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database {{ $labels.instance }} is down"
          runbook_url: "https://docs.betbet.com/runbooks/postgresql-down"
      
      - alert: PostgreSQLSlowQueries
        expr: |
          avg(rate(pg_stat_activity_max_tx_duration[5m])) > 5
        for: 3m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Average query duration is {{ $value }} seconds"
          runbook_url: "https://docs.betbet.com/runbooks/slow-queries"
      
      - alert: PostgreSQLConnectionsHigh
        expr: |
          pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL connections high"
          description: "{{ $value }}% of max connections in use"
          runbook_url: "https://docs.betbet.com/runbooks/high-db-connections"

  # Cache Alerts  
  - name: cache.redis
    interval: 30s
    rules:
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          category: cache
        annotations:
          summary: "Redis cache is down"
          description: "Redis instance {{ $labels.instance }} is down"
          runbook_url: "https://docs.betbet.com/runbooks/redis-down"
      
      - alert: RedisMemoryHigh
        expr: |
          redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          category: cache
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value }}%"
          runbook_url: "https://docs.betbet.com/runbooks/redis-memory-high"
      
      - alert: RedisSlowLog
        expr: |
          rate(redis_slowlog_length[5m]) > 0
        for: 3m
        labels:
          severity: warning
          category: cache
        annotations:
          summary: "Redis slow queries detected"
          description: "{{ $value }} slow Redis queries per second"
          runbook_url: "https://docs.betbet.com/runbooks/redis-slow-queries"

  # Security Alerts
  - name: security.monitoring
    interval: 30s
    rules:
      - alert: HighAuthenticationFailures
        expr: |
          rate(authentication_failures_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High authentication failure rate"
          description: "{{ $value }} authentication failures per second"
          runbook_url: "https://docs.betbet.com/runbooks/auth-failures"
      
      - alert: SuspiciousTrafficPattern
        expr: |
          rate(http_requests_total[1m]) > 1000
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Suspicious traffic pattern detected"
          description: "{{ $value }} requests per second - potential DDoS"
          runbook_url: "https://docs.betbet.com/runbooks/suspicious-traffic"
      
      - alert: UnauthorizedAPIAccess
        expr: |
          rate(http_requests_total{status="401"}[5m]) > 10
        for: 3m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High rate of unauthorized API access attempts"
          description: "{{ $value }} unauthorized requests per second"
          runbook_url: "https://docs.betbet.com/runbooks/unauthorized-access"