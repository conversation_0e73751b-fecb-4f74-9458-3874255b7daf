# BetBet Platform - Production Prometheus Configuration
# ===================================================
# Comprehensive monitoring for all services

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'betbet-production'
    region: 'us-east-1'

rule_files:
  - "alerts/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Gaming Engine API
  - job_name: 'gaming-engine'
    static_configs:
      - targets: ['gaming-engine:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: gaming-engine:8000

  # WebSocket Manager
  - job_name: 'websocket-manager'
    static_configs:
      - targets: ['websocket-manager:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Frontend Application
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics
    scrape_timeout: 10s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'betbet-postgres'

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'betbet-redis'

  # Nginx Load Balancer
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: '(.*)'
        target_label: instance
        replacement: 'betbet-host'

  # cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    honor_labels: true

  # Blackbox Exporter (External Monitoring)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://betbet.com
        - https://api.betbet.com/health
        - https://grafana.betbet.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Custom Application Metrics
  - job_name: 'gaming-sessions'
    static_configs:
      - targets: ['gaming-engine:8000']
    scrape_interval: 10s
    metrics_path: /metrics/gaming
    scrape_timeout: 5s
    params:
      module: ['gaming_sessions']

  # Real-time WebSocket Metrics
  - job_name: 'websocket-connections'
    static_configs:
      - targets: ['websocket-manager:8080']
    scrape_interval: 10s
    metrics_path: /metrics/websocket
    scrape_timeout: 5s

  # Financial Transaction Metrics
  - job_name: 'financial-transactions'
    static_configs:
      - targets: ['gaming-engine:8000']
    scrape_interval: 5s
    metrics_path: /metrics/financial
    scrape_timeout: 3s
    params:
      module: ['transactions']

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.betbet.com/api/v1/write"
    basic_auth:
      username: "prometheus"
      password: "${PROMETHEUS_REMOTE_WRITE_PASSWORD}"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 500000

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    min-block-duration: 2h
    max-block-duration: 2h