{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single"}}, "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT status as metric, COUNT(*) as value FROM custom_betting.custom_bet_markets GROUP BY status", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Markets by Status", "type": "piechart"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT COUNT(*) as value FROM custom_betting.custom_bet_markets", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Total Markets", "type": "stat"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT COALESCE(SUM(total_volume), 0) as value FROM custom_betting.custom_bet_markets", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Total Volume", "type": "stat"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT COUNT(*) as value FROM custom_betting.market_outcomes", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Total Outcomes", "type": "stat"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 0}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT created_at as time, COUNT(*) as value FROM custom_betting.custom_bet_markets WHERE $__timeFilter(created_at) GROUP BY DATE_TRUNC('hour', created_at) ORDER BY time", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "created_at", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Markets Created Over Time", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": ["betbet", "platform", "overview"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "BetBet Platform Overview", "uid": "betbet-platform-overview", "version": 1}