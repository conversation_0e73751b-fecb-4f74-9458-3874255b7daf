[tool:pytest]
# Root pytest configuration for centralized test directory
minversion = 6.0
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=services
    --cov-report=term-missing
    --cov-report=html:tests/coverage
    --cov-report=xml:tests/coverage.xml
    --junitxml=tests/junit.xml

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    security: Security tests
    slow: Slow running tests
    api: API tests
    db: Database tests
    ws: WebSocket tests
    auth: Authentication tests

# Ignore patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    node_modules
    frontend
    services/*/node_modules

# Filterwarnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Asyncio
asyncio_mode = auto