#!/usr/bin/env python3
"""
Custom Betting Platform - Sample Data Seeder
=============================================

Creates sample custom betting markets to demonstrate the platform functionality.
"""

import asyncio
import os
import sys
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from pathlib import Path
from uuid import uuid4

# Add parent directories to path for imports
sys.path.append(str(Path(__file__).parent.parent))

import structlog
from services.shared.core.database.connection import DatabaseSettings, initialize_database, close_database

# Configure logging
logger = structlog.get_logger()

def get_db_manager():
    """Get the global database manager"""
    from services.shared.core.database.connection import _db_manager
    return _db_manager

class CustomBettingSampleDataSeeder:
    """Sample data seeder for Custom Betting Platform"""
    
    async def initialize(self):
        """Initialize database connection"""
        try:
            # Get database URL from environment
            db_url = os.getenv("DATABASE_URL")
            if not db_url:
                logger.error("DATABASE_URL environment variable not set")
                return False
            
            # Initialize database
            db_settings = DatabaseSettings(DATABASE_URL=db_url)
            await initialize_database(db_settings)
            logger.info("Database connection initialized for seeding")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            return False
    
    async def create_sample_users(self):
        """Create sample users if they don't exist"""
        try:
            db_manager = get_db_manager()
            
            sample_users = [
                {
                    'id': '550e8400-e29b-41d4-a716-************',
                    'username': 'alice_predictor',
                    'email': '<EMAIL>',
                    'balance': 1000.00
                },
                {
                    'id': '550e8400-e29b-41d4-a716-************', 
                    'username': 'bob_bettor',
                    'email': '<EMAIL>',
                    'balance': 500.00
                },
                {
                    'id': '550e8400-e29b-41d4-a716-************',
                    'username': 'charlie_trader',
                    'email': '<EMAIL>', 
                    'balance': 2000.00
                }
            ]
            
            async with db_manager.get_raw_connection() as conn:
                for user in sample_users:
                    # Check if user exists
                    exists = await conn.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM public.users WHERE id = $1)",
                        user['id']
                    )
                    
                    if not exists:
                        await conn.execute(
                            """
                            INSERT INTO public.users (id, username, email, password_hash, balance, is_active, is_verified)
                            VALUES ($1, $2, $3, 'sample_hash', $4, true, true)
                            """,
                            user['id'], user['username'], user['email'], user['balance']
                        )
                        logger.info(f"✅ Created sample user: {user['username']}")
                    else:
                        logger.info(f"⏭️ User already exists: {user['username']}")
            
            return True
            
        except Exception as e:
            logger.error("Failed to create sample users", error=str(e))
            return False
    
    async def create_sample_custom_bets(self):
        """Create sample custom betting markets"""
        try:
            db_manager = get_db_manager()
            
            # Sample betting markets
            sample_bets = [
                {
                    'id': str(uuid4()),
                    'creator_user_id': '550e8400-e29b-41d4-a716-************',
                    'title': 'Will Bitcoin reach $100,000 by end of 2025?',
                    'description': 'A prediction market on whether Bitcoin (BTC) will reach or exceed $100,000 USD by December 31, 2025.',
                    'slug': 'bitcoin-100k-2025',
                    'event_criteria': 'Bitcoin price on any major exchange (Coinbase, Binance, Kraken) reaches $100,000 USD',
                    'verification_source': 'CoinMarketCap price data',
                    'verification_deadline': datetime.now() + timedelta(days=365),
                    'bet_type': 'binary',
                    'minimum_stake': Decimal('10.00'),
                    'maximum_stake': Decimal('1000.00'),
                    'deadline': datetime.now() + timedelta(days=360),
                    'settlement_deadline': datetime.now() + timedelta(days=365),
                    'category': 'cryptocurrency',
                    'tags': ['bitcoin', 'crypto', 'price-prediction'],
                    'outcome_definitions': {
                        'outcomes': [
                            {'key': 'yes', 'text': 'Yes, Bitcoin will reach $100,000'},
                            {'key': 'no', 'text': 'No, Bitcoin will not reach $100,000'}
                        ]
                    }
                },
                {
                    'id': str(uuid4()),
                    'creator_user_id': '550e8400-e29b-41d4-a716-************',
                    'title': 'Who will win the 2025 World Chess Championship?',
                    'description': 'Prediction market for the 2025 FIDE World Chess Championship.',
                    'slug': 'chess-championship-2025',
                    'event_criteria': 'Official FIDE announcement of World Chess Championship winner',
                    'verification_source': 'FIDE official website and press releases',
                    'verification_deadline': datetime.now() + timedelta(days=300),
                    'bet_type': 'multiple_choice',
                    'minimum_stake': Decimal('5.00'),
                    'maximum_stake': Decimal('500.00'),
                    'deadline': datetime.now() + timedelta(days=290),
                    'settlement_deadline': datetime.now() + timedelta(days=300),
                    'category': 'sports',
                    'tags': ['chess', 'championship', 'competition'],
                    'outcome_definitions': {
                        'outcomes': [
                            {'key': 'carlsen', 'text': 'Magnus Carlsen'},
                            {'key': 'nepomniachtchi', 'text': 'Ian Nepomniachtchi'},
                            {'key': 'firouzja', 'text': 'Alireza Firouzja'},
                            {'key': 'other', 'text': 'Other player'}
                        ]
                    }
                },
                {
                    'id': str(uuid4()),
                    'creator_user_id': '550e8400-e29b-41d4-a716-************',
                    'title': 'Will SpaceX successfully land on Mars in 2025?',
                    'description': 'Prediction market on whether SpaceX will achieve a successful Mars landing mission in 2025.',
                    'slug': 'spacex-mars-landing-2025',
                    'event_criteria': 'SpaceX spacecraft successfully lands on Mars surface and transmits confirmation',
                    'verification_source': 'Official SpaceX announcements and NASA confirmation',
                    'verification_deadline': datetime.now() + timedelta(days=365),
                    'bet_type': 'binary',
                    'minimum_stake': Decimal('25.00'),
                    'maximum_stake': Decimal('2000.00'),
                    'deadline': datetime.now() + timedelta(days=350),
                    'settlement_deadline': datetime.now() + timedelta(days=365),
                    'category': 'technology',
                    'tags': ['spacex', 'mars', 'space-exploration'],
                    'outcome_definitions': {
                        'outcomes': [
                            {'key': 'yes', 'text': 'Yes, successful Mars landing in 2025'},
                            {'key': 'no', 'text': 'No, no successful Mars landing in 2025'}
                        ]
                    }
                }
            ]
            
            async with db_manager.get_raw_connection() as conn:
                for bet in sample_bets:
                    # Check if bet already exists
                    exists = await conn.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM custom_betting.custom_bets WHERE slug = $1)",
                        bet['slug']
                    )
                    
                    if not exists:
                        # Insert custom bet
                        await conn.execute(
                            """
                            INSERT INTO custom_betting.custom_bets (
                                id, creator_user_id, title, description, slug, event_criteria,
                                verification_source, verification_deadline, bet_type, minimum_stake,
                                maximum_stake, deadline, settlement_deadline, category, tags,
                                outcome_definitions, created_by
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $2)
                            """,
                            bet['id'], bet['creator_user_id'], bet['title'], bet['description'],
                            bet['slug'], bet['event_criteria'], bet['verification_source'],
                            bet['verification_deadline'], bet['bet_type'], bet['minimum_stake'],
                            bet['maximum_stake'], bet['deadline'], bet['settlement_deadline'],
                            bet['category'], bet['tags'], bet['outcome_definitions']
                        )
                        
                        # Create outcomes for this bet
                        for outcome in bet['outcome_definitions']['outcomes']:
                            outcome_id = str(uuid4())
                            await conn.execute(
                                """
                                INSERT INTO custom_betting.bet_outcomes (
                                    id, custom_bet_id, outcome_text, outcome_key, is_active, created_by
                                ) VALUES ($1, $2, $3, $4, true, $5)
                                """,
                                outcome_id, bet['id'], outcome['text'], outcome['key'], bet['creator_user_id']
                            )
                        
                        logger.info(f"✅ Created sample bet: {bet['title']}")
                    else:
                        logger.info(f"⏭️ Bet already exists: {bet['title']}")
            
            return True
            
        except Exception as e:
            logger.error("Failed to create sample custom bets", error=str(e))
            return False
    
    async def create_sample_participants(self):
        """Create sample bet participants"""
        try:
            db_manager = get_db_manager()
            
            async with db_manager.get_raw_connection() as conn:
                # Get available bets and outcomes
                bets = await conn.fetch(
                    """
                    SELECT cb.id as bet_id, cb.slug, bo.id as outcome_id, bo.outcome_key 
                    FROM custom_betting.custom_bets cb
                    JOIN custom_betting.bet_outcomes bo ON cb.id = bo.custom_bet_id
                    WHERE cb.slug IN ('bitcoin-100k-2025', 'chess-championship-2025')
                    """
                )
                
                # Sample participants
                sample_participants = [
                    {
                        'user_id': '550e8400-e29b-41d4-a716-************',
                        'bet_slug': 'bitcoin-100k-2025',
                        'outcome_key': 'yes',
                        'stake_amount': Decimal('100.00'),
                        'position_type': 'backing'
                    },
                    {
                        'user_id': '550e8400-e29b-41d4-a716-************', 
                        'bet_slug': 'bitcoin-100k-2025',
                        'outcome_key': 'no',
                        'stake_amount': Decimal('50.00'),
                        'position_type': 'backing'
                    },
                    {
                        'user_id': '550e8400-e29b-41d4-a716-************',
                        'bet_slug': 'chess-championship-2025',
                        'outcome_key': 'carlsen',
                        'stake_amount': Decimal('75.00'),
                        'position_type': 'backing'
                    }
                ]
                
                for participant in sample_participants:
                    # Find the bet and outcome IDs
                    bet_outcome = next((b for b in bets if b['slug'] == participant['bet_slug'] and b['outcome_key'] == participant['outcome_key']), None)
                    
                    if bet_outcome:
                        # Check if participation already exists
                        exists = await conn.fetchval(
                            """
                            SELECT EXISTS(SELECT 1 FROM custom_betting.bet_participants 
                                         WHERE custom_bet_id = $1 AND user_id = $2 AND outcome_id = $3)
                            """,
                            bet_outcome['bet_id'], participant['user_id'], bet_outcome['outcome_id']
                        )
                        
                        if not exists:
                            # Calculate potential payout (simplified)
                            potential_payout = participant['stake_amount'] * Decimal('1.8')  # 1.8x return
                            
                            # Insert participant
                            participant_id = str(uuid4())
                            await conn.execute(
                                """
                                INSERT INTO custom_betting.bet_participants (
                                    id, custom_bet_id, user_id, outcome_id, stake_amount, 
                                    potential_payout, position_type, created_by
                                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $3)
                                """,
                                participant_id, bet_outcome['bet_id'], participant['user_id'],
                                bet_outcome['outcome_id'], participant['stake_amount'],
                                potential_payout, participant['position_type']
                            )
                            
                            logger.info(f"✅ Created sample participant: {participant['bet_slug']} - {participant['outcome_key']}")
                        else:
                            logger.info(f"⏭️ Participant already exists: {participant['bet_slug']} - {participant['outcome_key']}")
            
            return True
            
        except Exception as e:
            logger.error("Failed to create sample participants", error=str(e))
            return False
    
    async def seed_all_data(self):
        """Seed all sample data"""
        logger.info("🌱 Starting Custom Betting Platform sample data seeding...")
        
        success = True
        success &= await self.create_sample_users()
        success &= await self.create_sample_custom_bets()
        success &= await self.create_sample_participants()
        
        if success:
            logger.info("🎉 Sample data seeding completed successfully!")
        else:
            logger.error("❌ Sample data seeding failed")
        
        return success

async def main():
    """Main seeding function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Seed Custom Betting Platform with sample data")
    parser.add_argument("--force", action="store_true", help="Force recreate sample data")
    
    args = parser.parse_args()
    
    seeder = CustomBettingSampleDataSeeder()
    
    # Initialize database
    if not await seeder.initialize():
        logger.error("Failed to initialize database connection")
        sys.exit(1)
    
    try:
        success = await seeder.seed_all_data()
        if not success:
            logger.error("Sample data seeding failed")
            sys.exit(1)
    
    finally:
        await close_database()

if __name__ == "__main__":
    asyncio.run(main())