 import os
import sys
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime, timezone

from fastapi import FastAPI, HTTPException, Depends, Query, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from typing import List

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Import wallet client for financial operations
from shared.core.financial.wallet_client import WalletClient, InsufficientFundsError, FeeCategory, FeeType

# Define comprehensive models for custom betting
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.sql import func
import structlog

Base = declarative_base()
logger = structlog.get_logger()

class MarketCategory(Base):
    __tablename__ = "market_categories"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    slug = Column(String(120), unique=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class CustomBetMarket(Base):
    __tablename__ = "custom_bet_markets"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    creator_user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    category_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.market_categories.id"))
    
    # Market details
    title = Column(String(300), nullable=False)
    description = Column(Text)
    slug = Column(String(400), unique=True)
    
    # Market configuration
    market_type = Column(String(50), default='binary')  # binary, multiple_choice, scalar
    creator_type = Column(String(20), default='user')  # user, ai, admin
    
    # Financial settings
    minimum_stake = Column(Numeric(15, 2), default=1.00)
    maximum_stake = Column(Numeric(15, 2))
    total_volume = Column(Numeric(15, 2), default=0)
    total_participants = Column(Integer, default=0)
    
    # Market timing
    closes_at = Column(DateTime(timezone=True))
    resolves_at = Column(DateTime(timezone=True))
    
    # Market status and resolution
    status = Column(String(20), default='open')  # open, closed, settled, disputed, cancelled
    resolution_source = Column(String(200))  # URL or description of data source
    resolution_criteria = Column(Text)
    winning_outcome_id = Column(PostgresUUID(as_uuid=True))
    
    # Moderation and disputes
    is_featured = Column(Boolean, default=False)
    dispute_count = Column(Integer, default=0)
    last_disputed_at = Column(DateTime(timezone=True))
    
    # Metadata
    tags = Column(ARRAY(String(50)))
    market_metadata = Column(JSONB)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(PostgresUUID(as_uuid=True))
    updated_by = Column(PostgresUUID(as_uuid=True))

    # Relationships
    outcomes = relationship("MarketOutcome", back_populates="market", cascade="all, delete-orphan")

class MarketOutcome(Base):
    __tablename__ = "market_outcomes"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.custom_bet_markets.id"), nullable=False)
    
    title = Column(String(200), nullable=False)
    description = Column(Text)
    outcome_value = Column(String(100))  # For scalar markets
    
    # Betting statistics
    total_stakes = Column(Numeric(15, 2), default=0)
    total_bets = Column(Integer, default=0)
    implied_probability = Column(Numeric(5, 4), default=0.5)  # 0.0 to 1.0
    
    # Status
    is_active = Column(Boolean, default=True)
    is_winning = Column(Boolean, default=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    market = relationship("CustomBetMarket", back_populates="outcomes")

class UserPosition(Base):
    __tablename__ = "user_positions"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.custom_bet_markets.id"), nullable=False)
    outcome_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.market_outcomes.id"), nullable=False)
    
    # Position details
    position_type = Column(String(10), default='yes')  # yes, no
    stake_amount = Column(Numeric(15, 2), nullable=False)
    entry_price = Column(Numeric(10, 8), nullable=False)  # Price paid per share (0.0 to 1.0)
    shares = Column(Numeric(15, 8), nullable=False)  # Number of shares owned
    
    # P2P matching
    counterparty_user_id = Column(PostgresUUID(as_uuid=True))  # Who took the opposite side
    
    # Status and settlement
    status = Column(String(20), default='open')  # open, matched, settled, cancelled
    payout_amount = Column(Numeric(15, 2), default=0)
    
    # Metadata
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class EscrowTransaction(Base):
    __tablename__ = "escrow_transactions"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.custom_bet_markets.id"), nullable=False)
    position_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.user_positions.id"))
    
    # Transaction details
    transaction_type = Column(String(20), nullable=False)  # lock, unlock, settle, refund
    amount = Column(Numeric(15, 2), nullable=False)
    reference_id = Column(String(100))  # External transaction reference
    
    # Status
    status = Column(String(20), default='pending')  # pending, completed, failed, cancelled
    error_message = Column(Text)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class MarketDispute(Base):
    __tablename__ = "market_disputes"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("custom_betting.custom_bet_markets.id"), nullable=False)
    disputer_user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    
    # Dispute details
    dispute_reason = Column(String(50), nullable=False)  # incorrect_resolution, market_manipulation, etc.
    description = Column(Text)
    evidence_urls = Column(ARRAY(String(500)))
    
    # Resolution
    status = Column(String(20), default='open')  # open, investigating, resolved, rejected
    admin_user_id = Column(PostgresUUID(as_uuid=True))  # Admin handling the dispute
    admin_notes = Column(Text)
    resolution = Column(Text)
    resolved_at = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

# Legacy table for backwards compatibility
class CustomBet(Base):
    __tablename__ = "custom_bets"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    creator_user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    title = Column(String(300), nullable=False)
    description = Column(Text)
    slug = Column(String(400), unique=True)
    status = Column(String(20), default='open')
    bet_type = Column(String(50), default='binary')
    minimum_stake = Column(Numeric(15, 2), default=1.00)
    maximum_stake = Column(Numeric(15, 2))
    total_stakes = Column(Numeric(15, 2), default=0)
    total_participants = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class BetParticipant(Base):
    __tablename__ = "bet_participants"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    custom_bet_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    stake_amount = Column(Numeric(15, 2), nullable=False)
    status = Column(String(20), default='active')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

# Pydantic models for API requests and responses
class MarketOutcomeCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    outcome_value: Optional[str] = None

class MarketOutcomeResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    outcome_value: Optional[str]
    total_stakes: Decimal
    total_bets: int
    implied_probability: Decimal
    is_active: bool
    is_winning: bool
    created_at: datetime

class MarketCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=300)
    description: str = Field(..., min_length=1)
    category_id: Optional[str] = None
    market_type: str = Field(default='binary', pattern='^(binary|multiple_choice|scalar)$')
    minimum_stake: Decimal = Field(default=Decimal('1.00'), ge=0.01)
    maximum_stake: Optional[Decimal] = Field(None, ge=1.00)
    closes_at: Optional[datetime] = None
    resolves_at: Optional[datetime] = None
    resolution_source: Optional[str] = None
    resolution_criteria: str = Field(..., min_length=1)
    outcomes: List[MarketOutcomeCreate] = Field(..., min_items=2)
    tags: Optional[List[str]] = None

class MarketResponse(BaseModel):
    id: str
    title: str
    description: str
    slug: str
    market_type: str
    creator_type: str
    creator_user_id: str
    minimum_stake: Decimal
    maximum_stake: Optional[Decimal]
    total_volume: Decimal
    total_participants: int
    status: str
    closes_at: Optional[datetime]
    resolves_at: Optional[datetime]
    resolution_criteria: str
    is_featured: bool
    tags: Optional[List[str]]
    outcomes: List[MarketOutcomeResponse]
    created_at: datetime
    updated_at: datetime

class PositionCreate(BaseModel):
    market_id: str
    outcome_id: str
    position_type: str = Field(..., pattern='^(yes|no)$')
    stake_amount: Decimal = Field(..., gt=0)
    desired_price: Decimal = Field(..., ge=0.01, le=0.99)  # Price per share

class PositionResponse(BaseModel):
    id: str
    user_id: str
    market_id: str
    outcome_id: str
    position_type: str
    stake_amount: Decimal
    entry_price: Decimal
    shares: Decimal
    status: str
    payout_amount: Decimal
    counterparty_user_id: Optional[str]
    created_at: datetime

class DisputeCreate(BaseModel):
    market_id: str
    dispute_reason: str = Field(..., min_length=1, max_length=50)
    description: str = Field(..., min_length=10)
    evidence_urls: Optional[List[str]] = None

class DisputeResponse(BaseModel):
    id: str
    market_id: str
    disputer_user_id: str
    dispute_reason: str
    description: str
    evidence_urls: Optional[List[str]]
    status: str
    admin_user_id: Optional[str]
    admin_notes: Optional[str]
    resolution: Optional[str]
    created_at: datetime
    resolved_at: Optional[datetime]

class MarketSettlement(BaseModel):
    winning_outcome_id: str
    resolution_notes: Optional[str] = None

class CategoryResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    slug: str
    is_active: bool

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db"
    
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://localhost:8000",
        "https://betbet.com",
        "https://admin.betbet.com"
    ]
    
    SERVICE_NAME: str = "custom-betting"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8002
    
    class Config:
        # Environment variables take precedence over defaults
        extra = "ignore"

settings = Settings()

# Debug: Print the actual DATABASE_URL being used
print(f"[CUSTOM BETTING] DATABASE_URL from settings: {settings.DATABASE_URL}")
print(f"[CUSTOM BETTING] DATABASE_URL from env: {os.getenv('DATABASE_URL', 'NOT_SET')}")

app = FastAPI(
    title="BetBet Custom Betting",
    description="P2P custom betting marketplace",
    version=settings.SERVICE_VERSION
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Custom Betting Service connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

# Metrics tracking
import time
from collections import defaultdict

metrics_data = {
    "http_requests_total": 0,
    "http_request_duration_seconds": [],
    "start_time": time.time()
}

@app.middleware("http")
async def track_metrics(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time

    metrics_data["http_requests_total"] += 1
    metrics_data["http_request_duration_seconds"].append(duration)

    # Keep only last 1000 requests for memory efficiency
    if len(metrics_data["http_request_duration_seconds"]) > 1000:
        metrics_data["http_request_duration_seconds"] = metrics_data["http_request_duration_seconds"][-1000:]

    return response

@app.get("/metrics")
async def get_metrics(db: AsyncSession = Depends(get_database_write)):
    """Prometheus-compatible metrics endpoint"""
    try:
        # Get business metrics from database
        active_markets_result = await db.execute(
            select(func.count(CustomBetMarket.id)).where(
                CustomBetMarket.status.in_(['open', 'active'])
            )
        )
        active_markets = active_markets_result.scalar() or 0

        total_volume_result = await db.execute(
            select(func.coalesce(func.sum(CustomBetMarket.total_volume), 0))
        )
        total_volume = float(total_volume_result.scalar() or 0)

        total_markets_result = await db.execute(
            select(func.count(CustomBetMarket.id))
        )
        total_markets = total_markets_result.scalar() or 0

        # Calculate response time metrics
        durations = metrics_data["http_request_duration_seconds"]
        avg_duration = sum(durations) / len(durations) if durations else 0
        p95_duration = sorted(durations)[int(len(durations) * 0.95)] if durations else 0

        uptime = time.time() - metrics_data["start_time"]

        prometheus_metrics = f"""# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total{{service="custom-betting"}} {metrics_data["http_requests_total"]}

# HELP http_request_duration_seconds HTTP request duration in seconds
# TYPE http_request_duration_seconds histogram
http_request_duration_seconds_sum{{service="custom-betting"}} {sum(durations)}
http_request_duration_seconds_count{{service="custom-betting"}} {len(durations)}
http_request_duration_seconds_bucket{{service="custom-betting",le="0.1"}} {len([d for d in durations if d <= 0.1])}
http_request_duration_seconds_bucket{{service="custom-betting",le="0.5"}} {len([d for d in durations if d <= 0.5])}
http_request_duration_seconds_bucket{{service="custom-betting",le="1.0"}} {len([d for d in durations if d <= 1.0])}
http_request_duration_seconds_bucket{{service="custom-betting",le="5.0"}} {len([d for d in durations if d <= 5.0])}
http_request_duration_seconds_bucket{{service="custom-betting",le="+Inf"}} {len(durations)}

# HELP betbet_active_markets_total Number of active betting markets
# TYPE betbet_active_markets_total gauge
betbet_active_markets_total{{service="custom-betting"}} {active_markets}

# HELP betbet_total_markets_total Total number of betting markets
# TYPE betbet_total_markets_total gauge
betbet_total_markets_total{{service="custom-betting"}} {total_markets}

# HELP betbet_total_volume_usd Total betting volume in USD
# TYPE betbet_total_volume_usd gauge
betbet_total_volume_usd{{service="custom-betting"}} {total_volume}

# HELP betbet_service_uptime_seconds Service uptime in seconds
# TYPE betbet_service_uptime_seconds gauge
betbet_service_uptime_seconds{{service="custom-betting"}} {uptime}
"""

        return Response(content=prometheus_metrics, media_type="text/plain")

    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        return Response(content="# Error generating metrics\n", media_type="text/plain", status_code=500)

@app.get("/")
def read_root():
    return {
        "service": "BetBet Custom Betting Service",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "features": [
            "P2P prediction markets",
            "User/AI/Admin market creation",
            "Wallet integration with escrow",
            "Real-time price discovery",
            "Dispute resolution system",
            "Category-based markets"
        ],
        "endpoints": {
            "health": "/health",
            "docs": "/docs", 
            "markets": "/api/v1/custom-betting/markets",
            "positions": "/api/v1/custom-betting/positions",
            "categories": "/api/v1/custom-betting/categories",
            "disputes": "/api/v1/custom-betting/disputes",
            "admin": "/api/v1/custom-betting/admin"
        }
    }

@app.get("/health")
async def health_check():
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/custom-betting/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get custom betting analytics overview"""

    # Get count of active markets (using custom_bet_markets table)
    active_markets_query = select(func.count(CustomBetMarket.id)).where(
        CustomBetMarket.status == 'open'
    )
    active_markets_result = await db.execute(active_markets_query)
    active_markets = active_markets_result.scalar() or 0

    # Get total betting volume from bet_participants table
    total_volume_query = select(func.coalesce(func.sum(BetParticipant.stake_amount), 0))
    total_volume_result = await db.execute(total_volume_query)
    total_volume = total_volume_result.scalar() or 0

    # Get count of unique participants from bet_participants table
    total_participants_query = select(func.count(func.distinct(BetParticipant.user_id)))
    total_participants_result = await db.execute(total_participants_query)
    total_participants = total_participants_result.scalar() or 0

    # Get markets settled today
    today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    markets_settled_today_query = select(func.count(CustomBetMarket.id)).where(
        CustomBetMarket.status == 'settled',
        CustomBetMarket.updated_at >= today
    )
    markets_settled_today_result = await db.execute(markets_settled_today_query)
    markets_settled_today = markets_settled_today_result.scalar() or 0

    return {
        "active_markets": active_markets,
        "total_volume": float(total_volume),
        "total_participants": total_participants,
        "markets_settled_today": markets_settled_today
    }

# =============================================
# MARKET MANAGEMENT ENDPOINTS
# =============================================

@app.get("/api/v1/custom-betting/markets", response_model=List[MarketResponse])
async def get_markets(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by market status"),
    category_id: Optional[str] = Query(None, description="Filter by category"),
    creator_type: Optional[str] = Query(None, description="Filter by creator type"),
    featured_only: bool = Query(False, description="Show only featured markets"),
    db: AsyncSession = Depends(get_database_write)
):
    """Get prediction markets with filtering and pagination"""
    
    try:
        query = select(CustomBetMarket).options(
            selectinload(CustomBetMarket.outcomes)
        )
        
        # Apply filters
        if status:
            query = query.where(CustomBetMarket.status == status)
        if category_id:
            query = query.where(CustomBetMarket.category_id == UUID(category_id))
        if creator_type:
            query = query.where(CustomBetMarket.creator_type == creator_type)
        if featured_only:
            query = query.where(CustomBetMarket.is_featured == True)
        
        # Order by volume descending for discovery
        query = query.order_by(CustomBetMarket.total_volume.desc())
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        result = await db.execute(query)
        markets = result.scalars().all()
        
        # Convert to response format
        market_responses = []
        for market in markets:
            # Get outcomes for this market
            outcomes_query = select(MarketOutcome).where(MarketOutcome.market_id == market.id)
            outcomes_result = await db.execute(outcomes_query)
            outcomes = outcomes_result.scalars().all()
            
            market_responses.append(MarketResponse(
                id=str(market.id),
                title=market.title,
                description=market.description,
                slug=market.slug,
                market_type=market.market_type,
                creator_type=market.creator_type,
                creator_user_id=str(market.creator_user_id),
                minimum_stake=market.minimum_stake,
                maximum_stake=market.maximum_stake,
                total_volume=market.total_volume,
                total_participants=market.total_participants,
                status=market.status,
                closes_at=market.closes_at,
                resolves_at=market.resolves_at,
                resolution_criteria=market.resolution_criteria,
                is_featured=market.is_featured,
                tags=market.tags,
                outcomes=[MarketOutcomeResponse(
                    id=str(outcome.id),
                    title=outcome.title,
                    description=outcome.description,
                    outcome_value=outcome.outcome_value,
                    total_stakes=outcome.total_stakes,
                    total_bets=outcome.total_bets,
                    implied_probability=outcome.implied_probability,
                    is_active=outcome.is_active,
                    is_winning=outcome.is_winning,
                    created_at=outcome.created_at
                ) for outcome in outcomes],
                created_at=market.created_at,
                updated_at=market.updated_at
            ))
        
        logger.info(f"Retrieved {len(market_responses)} prediction markets", 
                   page=page, limit=limit, status=status)
        
        return market_responses
        
    except Exception as e:
        logger.error(f"Failed to get markets: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve markets")

@app.post("/api/v1/custom-betting/markets", response_model=MarketResponse, status_code=201)
async def create_market(
    market_data: MarketCreate,
    creator_user_id: str = Query(..., description="User ID creating the market"),
    creator_type: str = Query('user', description="Creator type: user, ai, admin"),
    db: AsyncSession = Depends(get_database_write)
):
    """Create a new prediction market"""
    
    try:
        # Generate slug from title
        import re
        slug = re.sub(r'[^a-zA-Z0-9\s-]', '', market_data.title.lower())
        slug = re.sub(r'\s+', '-', slug.strip())
        slug = f"{slug}-{str(uuid4())[:8]}"
        
        # Check if user has sufficient balance for market creation fee (if required)
        async with WalletClient(service_name="custom-betting") as wallet:
            balance = await wallet.get_balance(creator_user_id)
            
            # For admin and AI, no fee required
            if creator_type == 'user':
                market_creation_fee = Decimal('10.00')  # $10 fee to create market
                if balance.available_balance < market_creation_fee:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Insufficient balance for market creation. Required: ${market_creation_fee}, Available: ${balance.available_balance}"
                    )
        
        # Create market
        market = CustomBetMarket(
            creator_user_id=UUID(creator_user_id),
            category_id=UUID(market_data.category_id) if market_data.category_id else None,
            title=market_data.title,
            description=market_data.description,
            slug=slug,
            market_type=market_data.market_type,
            creator_type=creator_type,
            minimum_stake=market_data.minimum_stake,
            maximum_stake=market_data.maximum_stake,
            closes_at=market_data.closes_at,
            resolves_at=market_data.resolves_at,
            resolution_source=market_data.resolution_source,
            resolution_criteria=market_data.resolution_criteria,
            tags=market_data.tags,
            created_by=UUID(creator_user_id),
            updated_by=UUID(creator_user_id)
        )
        
        db.add(market)
        await db.flush()  # Get market ID
        
        # Create outcomes
        outcomes = []
        for outcome_data in market_data.outcomes:
            outcome = MarketOutcome(
                market_id=market.id,
                title=outcome_data.title,
                description=outcome_data.description,
                outcome_value=outcome_data.outcome_value
            )
            db.add(outcome)
            outcomes.append(outcome)
        
        await db.commit()
        
        logger.info(f"Created prediction market", 
                   market_id=str(market.id), creator_type=creator_type, creator_user_id=creator_user_id)
        
        # Return complete market with outcomes
        return MarketResponse(
            id=str(market.id),
            title=market.title,
            description=market.description,
            slug=market.slug,
            market_type=market.market_type,
            creator_type=market.creator_type,
            creator_user_id=str(market.creator_user_id),
            minimum_stake=market.minimum_stake,
            maximum_stake=market.maximum_stake,
            total_volume=market.total_volume,
            total_participants=market.total_participants,
            status=market.status,
            closes_at=market.closes_at,
            resolves_at=market.resolves_at,
            resolution_criteria=market.resolution_criteria,
            is_featured=market.is_featured,
            tags=market.tags,
            outcomes=[MarketOutcomeResponse(
                id=str(outcome.id),
                title=outcome.title,
                description=outcome.description,
                outcome_value=outcome.outcome_value,
                total_stakes=outcome.total_stakes,
                total_bets=outcome.total_bets,
                implied_probability=outcome.implied_probability,
                is_active=outcome.is_active,
                is_winning=outcome.is_winning,
                created_at=outcome.created_at
            ) for outcome in outcomes],
            created_at=market.created_at,
            updated_at=market.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create market: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create market: {str(e)}")

@app.get("/api/v1/custom-betting/markets/{market_id}", response_model=MarketResponse)
async def get_market_by_id(
    market_id: str,
    db: AsyncSession = Depends(get_database_write)
):
    """Get a specific prediction market by ID"""
    
    try:
        query = select(CustomBetMarket).options(
            selectinload(CustomBetMarket.outcomes)
        ).where(CustomBetMarket.id == UUID(market_id))
        
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        # Convert to response format
        outcomes_query = select(MarketOutcome).where(MarketOutcome.market_id == market.id)
        outcomes_result = await db.execute(outcomes_query)
        outcomes = outcomes_result.scalars().all()
        
        return MarketResponse(
            id=str(market.id),
            title=market.title,
            description=market.description,
            slug=market.slug,
            market_type=market.market_type,
            creator_type=market.creator_type,
            creator_user_id=str(market.creator_user_id),
            minimum_stake=market.minimum_stake,
            maximum_stake=market.maximum_stake,
            total_volume=market.total_volume,
            total_participants=market.total_participants,
            status=market.status,
            closes_at=market.closes_at,
            resolves_at=market.resolves_at,
            resolution_criteria=market.resolution_criteria,
            is_featured=market.is_featured,
            tags=market.tags,
            outcomes=[MarketOutcomeResponse(
                id=str(outcome.id),
                title=outcome.title,
                description=outcome.description,
                outcome_value=outcome.outcome_value,
                total_stakes=outcome.total_stakes,
                total_bets=outcome.total_bets,
                implied_probability=outcome.implied_probability,
                is_active=outcome.is_active,
                is_winning=outcome.is_winning,
                created_at=outcome.created_at
            ) for outcome in outcomes],
            created_at=market.created_at,
            updated_at=market.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get market: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve market: {str(e)}")

@app.patch("/api/v1/custom-betting/markets/{market_id}", response_model=MarketResponse)
async def update_market(
    market_id: str,
    market_data: dict,
    updated_by: str = Query(..., description="User ID updating the market"),
    db: AsyncSession = Depends(get_database_write)
):
    """Update a prediction market"""
    
    try:
        # Get market
        query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(market_id))
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        # Update market fields
        updatable_fields = [
            'title', 'description', 'category_id', 'minimum_stake',
            'maximum_stake', 'closes_at', 'resolves_at', 'resolution_source',
            'resolution_criteria', 'tags', 'status', 'is_featured'
        ]
        
        for field in updatable_fields:
            if field in market_data:
                if field == 'category_id' and market_data[field]:
                    setattr(market, field, UUID(market_data[field]))
                else:
                    setattr(market, field, market_data[field])
        
        market.updated_by = UUID(updated_by)
        market.updated_at = datetime.now(timezone.utc)
        
        await db.commit()
        await db.refresh(market)
        
        # Get outcomes for response
        outcomes_query = select(MarketOutcome).where(MarketOutcome.market_id == market.id)
        outcomes_result = await db.execute(outcomes_query)
        outcomes = outcomes_result.scalars().all()
        
        logger.info(f"Updated prediction market", market_id=str(market.id), updated_by=updated_by)
        
        return MarketResponse(
            id=str(market.id),
            title=market.title,
            description=market.description,
            slug=market.slug,
            market_type=market.market_type,
            creator_type=market.creator_type,
            creator_user_id=str(market.creator_user_id),
            minimum_stake=market.minimum_stake,
            maximum_stake=market.maximum_stake,
            total_volume=market.total_volume,
            total_participants=market.total_participants,
            status=market.status,
            closes_at=market.closes_at,
            resolves_at=market.resolves_at,
            resolution_criteria=market.resolution_criteria,
            is_featured=market.is_featured,
            tags=market.tags,
            outcomes=[MarketOutcomeResponse(
                id=str(outcome.id),
                title=outcome.title,
                description=outcome.description,
                outcome_value=outcome.outcome_value,
                total_stakes=outcome.total_stakes,
                total_bets=outcome.total_bets,
                implied_probability=outcome.implied_probability,
                is_active=outcome.is_active,
                is_winning=outcome.is_winning,
                created_at=outcome.created_at
            ) for outcome in outcomes],
            created_at=market.created_at,
            updated_at=market.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to update market: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update market: {str(e)}")

@app.delete("/api/v1/custom-betting/markets/{market_id}", status_code=200)
async def delete_market(
    market_id: str,
    deleted_by: str = Query(..., description="User ID deleting the market"),
    db: AsyncSession = Depends(get_database_write)
):
    """Delete a prediction market (soft delete)"""
    
    try:
        # Get market
        query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(market_id))
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        # Only allow deletion of markets that haven't started or have no participants
        if market.status != 'open' or market.total_participants > 0:
            raise HTTPException(
                status_code=400,
                detail="Can only delete open markets with no participants"
            )
        
        # Soft delete - mark as cancelled
        market.status = 'cancelled'
        market.deleted_at = datetime.now(timezone.utc)
        market.deleted_by = UUID(deleted_by)
        market.updated_by = UUID(deleted_by)
        market.updated_at = datetime.now(timezone.utc)
        
        await db.commit()
        
        logger.info(f"Deleted prediction market", market_id=str(market.id), deleted_by=deleted_by)
        
        return {
            "message": "Market deleted successfully",
            "market_id": str(market.id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to delete market: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete market: {str(e)}")

@app.get("/api/v1/custom-betting/categories", response_model=List[CategoryResponse])
async def get_categories(
    active_only: bool = Query(True, description="Show only active categories"),
    db: AsyncSession = Depends(get_database_write)
):
    """Get market categories"""
    
    try:
        query = select(MarketCategory)
        
        if active_only:
            query = query.where(MarketCategory.is_active == True)
        
        query = query.order_by(MarketCategory.name)
        
        result = await db.execute(query)
        categories = result.scalars().all()
        
        return [CategoryResponse(
            id=str(cat.id),
            name=cat.name,
            description=cat.description,
            slug=cat.slug,
            is_active=cat.is_active
        ) for cat in categories]
        
    except Exception as e:
        logger.error(f"Failed to get categories: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve categories")

# =============================================
# P2P POSITION MANAGEMENT ENDPOINTS
# =============================================

@app.post("/api/v1/custom-betting/positions", response_model=PositionResponse, status_code=201)
async def create_position(
    position_data: PositionCreate,
    user_id: str = Query(..., description="User ID creating the position"),
    db: AsyncSession = Depends(get_database_write)
):
    """Create a new position (bet) on a market outcome"""
    
    try:
        # Validate market exists and is open
        market_query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(position_data.market_id))
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        if market.status != 'open':
            raise HTTPException(status_code=400, detail="Market is not open for betting")
        
        # Validate outcome exists
        outcome_query = select(MarketOutcome).where(MarketOutcome.id == UUID(position_data.outcome_id))
        outcome_result = await db.execute(outcome_query)
        outcome = outcome_result.scalar_one_or_none()
        
        if not outcome or not outcome.is_active:
            raise HTTPException(status_code=404, detail="Outcome not found or inactive")
        
        # Calculate shares based on desired price
        # In prediction markets: shares = stake_amount / price
        shares = position_data.stake_amount / position_data.desired_price
        
        # Check user balance and lock funds
        async with WalletClient(service_name="custom-betting") as wallet:
            balance = await wallet.get_balance(user_id)
            
            if balance.available_balance < position_data.stake_amount:
                raise HTTPException(
                    status_code=400,
                    detail=f"Insufficient balance. Available: ${balance.available_balance}, Required: ${position_data.stake_amount}"
                )
            
            # Lock funds for the position
            lock_result = await wallet.lock_funds(
                user_id=user_id,
                amount=position_data.stake_amount,
                reference_type="prediction_market_position",
                reference_id=str(uuid4()),
                description=f"Position on {market.title}"
            )
            
            if not lock_result.success:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to lock funds: {lock_result.error_message}"
                )
        
        # Try to find matching position for P2P trading
        counterparty_position = await find_matching_position(
            db, market.id, outcome.id, position_data.position_type, 
            position_data.desired_price, position_data.stake_amount
        )
        
        # Create the position
        position = UserPosition(
            user_id=UUID(user_id),
            market_id=market.id,
            outcome_id=outcome.id,
            position_type=position_data.position_type,
            stake_amount=position_data.stake_amount,
            entry_price=position_data.desired_price,
            shares=shares,
            status='matched' if counterparty_position else 'open',
            counterparty_user_id=counterparty_position.user_id if counterparty_position else None
        )
        
        db.add(position)
        await db.flush()
        
        # If matched, update counterparty and create escrow transactions
        if counterparty_position:
            counterparty_position.status = 'matched'
            counterparty_position.counterparty_user_id = UUID(user_id)
            
            # Create escrow records for both positions
            escrow1 = EscrowTransaction(
                user_id=UUID(user_id),
                market_id=market.id,
                position_id=position.id,
                transaction_type='lock',
                amount=position_data.stake_amount,
                status='completed'
            )
            
            escrow2 = EscrowTransaction(
                user_id=counterparty_position.user_id,
                market_id=market.id,
                position_id=counterparty_position.id,
                transaction_type='lock',
                amount=counterparty_position.stake_amount,
                status='completed'
            )
            
            db.add_all([escrow1, escrow2])
            
            logger.info(f"Matched P2P position", 
                       position_id=str(position.id), 
                       counterparty_id=str(counterparty_position.id))
        
        # Update market and outcome statistics
        market.total_volume += position_data.stake_amount
        market.total_participants += 1
        outcome.total_stakes += position_data.stake_amount
        outcome.total_bets += 1
        
        # Update implied probability based on betting activity
        await update_outcome_probability(db, outcome.id)
        
        await db.commit()
        
        logger.info(f"Created position", 
                   position_id=str(position.id), user_id=user_id, 
                   market_id=position_data.market_id, matched=bool(counterparty_position))
        
        return PositionResponse(
            id=str(position.id),
            user_id=user_id,
            market_id=str(position.market_id),
            outcome_id=str(position.outcome_id),
            position_type=position.position_type,
            stake_amount=position.stake_amount,
            entry_price=position.entry_price,
            shares=position.shares,
            status=position.status,
            payout_amount=position.payout_amount,
            counterparty_user_id=str(position.counterparty_user_id) if position.counterparty_user_id else None,
            created_at=position.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create position: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create position: {str(e)}")

@app.get("/api/v1/custom-betting/positions", response_model=List[PositionResponse])
async def get_user_positions(
    user_id: str = Query(..., description="User ID to get positions for"),
    market_id: Optional[str] = Query(None, description="Filter by market ID"),
    status: Optional[str] = Query(None, description="Filter by position status"),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get user's positions with filtering"""
    
    try:
        query = select(UserPosition).where(UserPosition.user_id == UUID(user_id))
        
        if market_id:
            query = query.where(UserPosition.market_id == UUID(market_id))
        if status:
            query = query.where(UserPosition.status == status)
        
        query = query.order_by(UserPosition.created_at.desc())
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        result = await db.execute(query)
        positions = result.scalars().all()
        
        return [PositionResponse(
            id=str(pos.id),
            user_id=str(pos.user_id),
            market_id=str(pos.market_id),
            outcome_id=str(pos.outcome_id),
            position_type=pos.position_type,
            stake_amount=pos.stake_amount,
            entry_price=pos.entry_price,
            shares=pos.shares,
            status=pos.status,
            payout_amount=pos.payout_amount,
            counterparty_user_id=str(pos.counterparty_user_id) if pos.counterparty_user_id else None,
            created_at=pos.created_at
        ) for pos in positions]
        
    except Exception as e:
        logger.error(f"Failed to get user positions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve positions")

# =============================================
# MARKET SETTLEMENT ENDPOINTS
# =============================================

@app.post("/api/v1/custom-betting/markets/{market_id}/settle", status_code=200)
async def settle_market(
    market_id: str,
    settlement_data: MarketSettlement,
    admin_user_id: str = Query(..., description="Admin ID settling the market"),
    db: AsyncSession = Depends(get_database_write)
):
    """Settle a market by declaring the winning outcome"""
    
    try:
        # Get market
        market_query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(market_id))
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        if market.status not in ['open', 'closed']:
            raise HTTPException(status_code=400, detail="Market cannot be settled")
        
        # Verify winning outcome
        outcome_query = select(MarketOutcome).where(MarketOutcome.id == UUID(settlement_data.winning_outcome_id))
        outcome_result = await db.execute(outcome_query)
        winning_outcome = outcome_result.scalar_one_or_none()
        
        if not winning_outcome or winning_outcome.market_id != market.id:
            raise HTTPException(status_code=404, detail="Invalid winning outcome")
        
        # Get all positions for this market
        positions_query = select(UserPosition).where(
            UserPosition.market_id == market.id,
            UserPosition.status.in_(['open', 'matched'])
        )
        positions_result = await db.execute(positions_query)
        positions = positions_result.scalars().all()
        
        total_payouts = Decimal('0')
        settlement_results = []
        
        # Process each position
        async with WalletClient(service_name="custom-betting") as wallet:
            for position in positions:
                try:
                    # Determine if position won
                    position_won = (
                        position.outcome_id == winning_outcome.id and position.position_type == 'yes'
                    ) or (
                        position.outcome_id != winning_outcome.id and position.position_type == 'no'
                    )
                    
                    if position_won:
                        # Calculate payout: shares * $1.00 (full value)
                        payout_amount = position.shares
                        
                        # Calculate platform fee on winnings
                        net_winnings = payout_amount - position.stake_amount
                        if net_winnings > 0:
                            fee_calc = await wallet.calculate_platform_fee(
                                user_id=str(position.user_id),
                                transaction_amount=net_winnings,
                                fee_category=FeeCategory.GAMING,
                                fee_type=FeeType.STANDARD
                            )
                            payout_amount = payout_amount - fee_calc.fee_amount
                        
                        position.payout_amount = payout_amount
                        total_payouts += payout_amount
                    else:
                        # Position lost
                        position.payout_amount = Decimal('0')
                    
                    # Settle position in wallet
                    settlement_result = await wallet.unlock_funds(
                        user_id=str(position.user_id),
                        locked_amount=position.stake_amount,
                        win_amount=position.payout_amount,
                        reference_type="prediction_market_settlement",
                        reference_id=str(position.id),
                        description=f"Settlement for {market.title}"
                    )
                    
                    position.status = 'settled'
                    
                    settlement_results.append({
                        "position_id": str(position.id),
                        "user_id": str(position.user_id),
                        "won": position_won,
                        "payout": float(position.payout_amount),
                        "wallet_success": settlement_result.success
                    })
                    
                except Exception as pos_error:
                    logger.error(f"Failed to settle position {position.id}: {str(pos_error)}")
                    position.status = 'error'
                    settlement_results.append({
                        "position_id": str(position.id),
                        "user_id": str(position.user_id),
                        "won": False,
                        "payout": 0.0,
                        "wallet_success": False,
                        "error": str(pos_error)
                    })
        
        # Update market status
        market.status = 'settled'
        market.winning_outcome_id = winning_outcome.id
        market.updated_by = UUID(admin_user_id)
        
        # Mark winning outcome
        winning_outcome.is_winning = True
        
        await db.commit()
        
        logger.info(f"Settled prediction market",
                   market_id=market_id, winning_outcome_id=settlement_data.winning_outcome_id,
                   total_positions=len(positions), total_payouts=float(total_payouts))
        
        return {
            "message": "Market settled successfully",
            "market_id": market_id,
            "winning_outcome_id": settlement_data.winning_outcome_id,
            "total_positions": len(positions),
            "total_payouts": float(total_payouts),
            "settlement_results": settlement_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to settle market: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to settle market: {str(e)}")

# =============================================
# HELPER FUNCTIONS
# =============================================

async def find_matching_position(
    db: AsyncSession, market_id: UUID, outcome_id: UUID, 
    position_type: str, desired_price: Decimal, stake_amount: Decimal
) -> Optional[UserPosition]:
    """Find a matching position for P2P trading"""
    
    # Look for opposite position type at compatible price
    opposite_type = 'no' if position_type == 'yes' else 'yes'
    
    # For binary markets: yes at 0.60 matches no at 0.40
    matching_price = Decimal('1.0') - desired_price
    price_tolerance = Decimal('0.01')  # 1% tolerance
    
    query = select(UserPosition).where(
        UserPosition.market_id == market_id,
        UserPosition.outcome_id == outcome_id,
        UserPosition.position_type == opposite_type,
        UserPosition.status == 'open',
        UserPosition.entry_price >= matching_price - price_tolerance,
        UserPosition.entry_price <= matching_price + price_tolerance,
        UserPosition.stake_amount == stake_amount  # Exact amount match for simplicity
    ).limit(1)
    
    result = await db.execute(query)
    return result.scalar_one_or_none()

async def update_outcome_probability(db: AsyncSession, outcome_id: UUID):
    """Update implied probability based on betting activity"""
    
    try:
        # Get all positions for this outcome
        positions_query = select(UserPosition).where(UserPosition.outcome_id == outcome_id)
        positions_result = await db.execute(positions_query)
        positions = positions_result.scalars().all()
        
        if not positions:
            return
        
        # Calculate volume-weighted average price
        total_volume = sum(pos.stake_amount for pos in positions)
        if total_volume > 0:
            weighted_price = sum(pos.entry_price * pos.stake_amount for pos in positions) / total_volume
            
            # Update outcome probability
            outcome_query = select(MarketOutcome).where(MarketOutcome.id == outcome_id)
            outcome_result = await db.execute(outcome_query)
            outcome = outcome_result.scalar_one()
            
            outcome.implied_probability = weighted_price
            
            await db.commit()
            
    except Exception as e:
        logger.error(f"Failed to update outcome probability: {str(e)}")

# =============================================
# DISPUTE RESOLUTION SYSTEM
# =============================================

@app.post("/api/v1/custom-betting/disputes", response_model=DisputeResponse, status_code=201)
async def create_dispute(
    dispute_data: DisputeCreate,
    user_id: str = Query(..., description="User ID filing the dispute"),
    db: AsyncSession = Depends(get_database_write)
):
    """File a dispute against a market resolution"""
    
    try:
        # Validate market exists
        market_query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(dispute_data.market_id))
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        if market.status != 'settled':
            raise HTTPException(status_code=400, detail="Can only dispute settled markets")
        
        # Check if user has positions in this market
        position_query = select(UserPosition).where(
            UserPosition.market_id == market.id,
            UserPosition.user_id == UUID(user_id)
        )
        position_result = await db.execute(position_query)
        user_positions = position_result.scalars().all()
        
        if not user_positions:
            raise HTTPException(status_code=400, detail="Can only dispute markets you participated in")
        
        # Create dispute
        dispute = MarketDispute(
            market_id=market.id,
            disputer_user_id=UUID(user_id),
            dispute_reason=dispute_data.dispute_reason,
            description=dispute_data.description,
            evidence_urls=dispute_data.evidence_urls or []
        )
        
        db.add(dispute)
        
        # Update market dispute count
        market.dispute_count += 1
        market.last_disputed_at = datetime.now(timezone.utc)
        
        # If this is the first dispute, change market status
        if market.dispute_count == 1:
            market.status = 'disputed'
        
        await db.commit()
        
        logger.info(f"Created market dispute", 
                   dispute_id=str(dispute.id), market_id=dispute_data.market_id, 
                   user_id=user_id, reason=dispute_data.dispute_reason)
        
        return DisputeResponse(
            id=str(dispute.id),
            market_id=str(dispute.market_id),
            disputer_user_id=str(dispute.disputer_user_id),
            dispute_reason=dispute.dispute_reason,
            description=dispute.description,
            evidence_urls=dispute.evidence_urls,
            status=dispute.status,
            admin_user_id=str(dispute.admin_user_id) if dispute.admin_user_id else None,
            admin_notes=dispute.admin_notes,
            resolution=dispute.resolution,
            created_at=dispute.created_at,
            resolved_at=dispute.resolved_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create dispute: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create dispute: {str(e)}")

@app.get("/api/v1/custom-betting/disputes", response_model=List[DisputeResponse])
async def get_disputes(
    status: Optional[str] = Query(None, description="Filter by dispute status"),
    market_id: Optional[str] = Query(None, description="Filter by market ID"),
    admin_user_id: str = Query(..., description="Admin user ID"),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get disputes for admin review"""
    
    try:
        query = select(MarketDispute)
        
        if status:
            query = query.where(MarketDispute.status == status)
        if market_id:
            query = query.where(MarketDispute.market_id == UUID(market_id))
        
        query = query.order_by(MarketDispute.created_at.desc())
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        result = await db.execute(query)
        disputes = result.scalars().all()
        
        return [DisputeResponse(
            id=str(dispute.id),
            market_id=str(dispute.market_id),
            disputer_user_id=str(dispute.disputer_user_id),
            dispute_reason=dispute.dispute_reason,
            description=dispute.description,
            evidence_urls=dispute.evidence_urls,
            status=dispute.status,
            admin_user_id=str(dispute.admin_user_id) if dispute.admin_user_id else None,
            admin_notes=dispute.admin_notes,
            resolution=dispute.resolution,
            created_at=dispute.created_at,
            resolved_at=dispute.resolved_at
        ) for dispute in disputes]
        
    except Exception as e:
        logger.error(f"Failed to get disputes: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve disputes")

@app.put("/api/v1/custom-betting/disputes/{dispute_id}/resolve", status_code=200)
async def resolve_dispute(
    dispute_id: str,
    resolution: str = Query(..., description="Admin resolution decision"),
    admin_notes: Optional[str] = Query(None, description="Admin notes on resolution"),
    admin_user_id: str = Query(..., description="Admin user ID resolving dispute"),
    new_winning_outcome_id: Optional[str] = Query(None, description="New winning outcome if resolution changed"),
    db: AsyncSession = Depends(get_database_write)
):
    """Resolve a dispute (admin only)"""
    
    try:
        # Get dispute
        dispute_query = select(MarketDispute).where(MarketDispute.id == UUID(dispute_id))
        dispute_result = await db.execute(dispute_query)
        dispute = dispute_result.scalar_one_or_none()
        
        if not dispute:
            raise HTTPException(status_code=404, detail="Dispute not found")
        
        if dispute.status != 'open':
            raise HTTPException(status_code=400, detail="Dispute already resolved")
        
        # Get market
        market_query = select(CustomBetMarket).where(CustomBetMarket.id == dispute.market_id)
        market_result = await db.execute(market_query)
        market = market_result.scalar_one()
        
        # Update dispute
        dispute.status = 'resolved'
        dispute.admin_user_id = UUID(admin_user_id)
        dispute.admin_notes = admin_notes
        dispute.resolution = resolution
        dispute.resolved_at = datetime.now(timezone.utc)
        
        # If resolution changed the winning outcome, re-settle the market
        if new_winning_outcome_id and new_winning_outcome_id != str(market.winning_outcome_id):
            logger.info(f"Re-settling market due to dispute resolution", 
                       market_id=str(market.id), 
                       old_winner=str(market.winning_outcome_id),
                       new_winner=new_winning_outcome_id)
            
            # This would trigger a complex re-settlement process
            # For now, just update the winning outcome
            market.winning_outcome_id = UUID(new_winning_outcome_id)
            market.status = 'settled'  # Keep as settled but with new winner
        
        # Check if all disputes are resolved
        pending_disputes_query = select(func.count(MarketDispute.id)).where(
            MarketDispute.market_id == market.id,
            MarketDispute.status == 'open'
        )
        pending_count_result = await db.execute(pending_disputes_query)
        pending_count = pending_count_result.scalar()
        
        if pending_count == 0:
            # All disputes resolved, market back to settled
            market.status = 'settled'
        
        await db.commit()
        
        logger.info(f"Resolved dispute", 
                   dispute_id=dispute_id, admin_user_id=admin_user_id, 
                   resolution=resolution, market_changed=bool(new_winning_outcome_id))
        
        return {
            "message": "Dispute resolved successfully",
            "dispute_id": dispute_id,
            "resolution": resolution,
            "market_re_settled": bool(new_winning_outcome_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to resolve dispute: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to resolve dispute: {str(e)}")

# =============================================
# ADMIN ENDPOINTS
# =============================================

@app.get("/api/v1/custom-betting/admin/overview")
async def get_admin_overview(
    admin_user_id: str = Query(..., description="Admin user ID"),
    db: AsyncSession = Depends(get_database_write)
):
    """Get admin dashboard overview"""
    
    try:
        # Market statistics
        total_markets_query = select(func.count(CustomBetMarket.id))
        total_markets_result = await db.execute(total_markets_query)
        total_markets = total_markets_result.scalar()
        
        active_markets_query = select(func.count(CustomBetMarket.id)).where(CustomBetMarket.status == 'open')
        active_markets_result = await db.execute(active_markets_query)
        active_markets = active_markets_result.scalar()
        
        disputed_markets_query = select(func.count(CustomBetMarket.id)).where(CustomBetMarket.status == 'disputed')
        disputed_markets_result = await db.execute(disputed_markets_query)
        disputed_markets = disputed_markets_result.scalar()
        
        # Position statistics
        total_positions_query = select(func.count(UserPosition.id))
        total_positions_result = await db.execute(total_positions_query)
        total_positions = total_positions_result.scalar()
        
        total_volume_query = select(func.coalesce(func.sum(CustomBetMarket.total_volume), 0))
        total_volume_result = await db.execute(total_volume_query)
        total_volume = total_volume_result.scalar()
        
        # Dispute statistics
        open_disputes_query = select(func.count(MarketDispute.id)).where(MarketDispute.status == 'open')
        open_disputes_result = await db.execute(open_disputes_query)
        open_disputes = open_disputes_result.scalar()
        
        return {
            "market_stats": {
                "total_markets": total_markets,
                "active_markets": active_markets,
                "disputed_markets": disputed_markets,
                "total_volume": float(total_volume)
            },
            "position_stats": {
                "total_positions": total_positions,
                "average_position_size": float(total_volume / total_positions) if total_positions > 0 else 0
            },
            "dispute_stats": {
                "open_disputes": open_disputes,
                "dispute_rate": float(disputed_markets / total_markets * 100) if total_markets > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get admin overview: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve admin overview")

@app.post("/api/v1/custom-betting/admin/markets/{market_id}/feature", status_code=200)
async def toggle_market_featured(
    market_id: str,
    featured: bool = Query(..., description="Set featured status"),
    admin_user_id: str = Query(..., description="Admin user ID"),
    db: AsyncSession = Depends(get_database_write)
):
    """Toggle market featured status"""
    
    try:
        market_query = select(CustomBetMarket).where(CustomBetMarket.id == UUID(market_id))
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        market.is_featured = featured
        market.updated_by = UUID(admin_user_id)
        
        await db.commit()
        
        logger.info(f"Updated market featured status", 
                   market_id=market_id, featured=featured, admin_user_id=admin_user_id)
        
        return {
            "message": f"Market {'featured' if featured else 'unfeatured'} successfully",
            "market_id": market_id,
            "is_featured": featured
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to toggle market featured status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update market")

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)