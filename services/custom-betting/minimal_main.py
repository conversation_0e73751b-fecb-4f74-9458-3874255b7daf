#!/usr/bin/env python3
"""
Minimal Custom Betting Service for Testing
"""

import os
import sys
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime, timezone
from pathlib import Path

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

# Add the services directory to the Python path
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Simple models for testing
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func
import structlog

Base = declarative_base()
logger = structlog.get_logger()

class CustomBetMarket(Base):
    __tablename__ = "custom_bet_markets"
    __table_args__ = {"schema": "custom_betting"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    creator_user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    title = Column(String(300), nullable=False)
    description = Column(Text)
    slug = Column(String(400), unique=True)
    status = Column(String(20), default='open')
    market_type = Column(String(50), default='binary')
    creator_type = Column(String(20), default='user')
    minimum_stake = Column(Numeric(15, 2), default=1.00)
    total_volume = Column(Numeric(15, 2), default=0)
    total_participants = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

# Simple response models
class MarketResponse(BaseModel):
    id: str
    title: str
    description: str
    status: str
    market_type: str
    creator_type: str
    total_volume: Decimal
    total_participants: int
    created_at: datetime

class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://localhost:8000"
    ]
    
    SERVICE_NAME: str = "custom-betting"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8002
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"

settings = Settings()

app = FastAPI(
    title="BetBet Custom Betting - Minimal",
    description="P2P prediction markets",
    version=settings.SERVICE_VERSION
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Custom Betting Service connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

@app.get("/")
def read_root():
    return {
        "service": "BetBet Custom Betting Service",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "features": [
            "P2P prediction markets",
            "User/AI/Admin market creation",
            "Wallet integration with escrow",
            "Real-time price discovery"
        ]
    }

@app.get("/health")
async def health_check():
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False
        
        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/custom-betting/markets", response_model=List[MarketResponse])
async def get_markets(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get prediction markets"""
    
    try:
        query = select(CustomBetMarket).order_by(CustomBetMarket.total_volume.desc())
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        result = await db.execute(query)
        markets = result.scalars().all()
        
        return [MarketResponse(
            id=str(market.id),
            title=market.title,
            description=market.description,
            status=market.status,
            market_type=market.market_type,
            creator_type=market.creator_type,
            total_volume=market.total_volume,
            total_participants=market.total_participants,
            created_at=market.created_at
        ) for market in markets]
        
    except Exception as e:
        logger.error(f"Failed to get markets: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve markets")

@app.get("/api/v1/custom-betting/categories")
async def get_categories():
    """Get market categories"""
    return [
        {"id": "1", "name": "Sports", "slug": "sports"},
        {"id": "2", "name": "Politics", "slug": "politics"},
        {"id": "3", "name": "Technology", "slug": "technology"},
        {"id": "4", "name": "Entertainment", "slug": "entertainment"},
        {"id": "5", "name": "Crypto", "slug": "crypto"}
    ]

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)