# Custom Betting Platform Dependencies
# ====================================

# Core FastAPI Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database and ORM
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Data Validation and Serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Async Support
asyncio-mqtt==0.16.1
aioredis==2.0.1
httpx==0.25.2
aiohttp==3.9.1

# Logging and Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Utilities
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# Background Tasks
celery==5.3.4
redis==5.0.1

# Date and Time
python-dateutil==2.8.2

# Decimal handling for financial calculations
# decimal is a built-in Python module

# UUID generation
# uuid is a built-in Python module

# JSON handling
orjson==3.9.10

# WebSocket support
websockets==12.0

# Development dependencies
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Shared services (assuming these exist in the monorepo)
# NOTE: These would be installed as local packages in actual deployment
# -e ../shared/core
# -e ../shared/database
# -e ../shared/messaging