#!/usr/bin/env python3
"""
Test script for custom betting service
"""

import asyncio
import aiohttp
import json

async def test_service():
    """Test the custom betting service endpoints"""
    base_url = "http://localhost:8002"
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Health check
        print("🔍 Testing health endpoint...")
        try:
            async with session.get(f"{base_url}/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ Health check passed: {data['status']}")
                else:
                    print(f"❌ Health check failed: {resp.status}")
                    return
        except Exception as e:
            print(f"❌ Service appears to be offline: {e}")
            return
        
        # Test 2: Root endpoint
        print("\n🔍 Testing root endpoint...")
        async with session.get(f"{base_url}/") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ Root endpoint: {data['service']}")
                print(f"   Features: {', '.join(data['features'])}")
            else:
                print(f"❌ Root endpoint failed: {resp.status}")
        
        # Test 3: Categories endpoint
        print("\n🔍 Testing categories endpoint...")
        async with session.get(f"{base_url}/api/v1/custom-betting/categories") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ Categories endpoint: {len(data)} categories found")
            else:
                print(f"❌ Categories failed: {resp.status}")
        
        # Test 4: Markets endpoint
        print("\n🔍 Testing markets endpoint...")
        async with session.get(f"{base_url}/api/v1/custom-betting/markets") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ Markets endpoint: {len(data)} markets found")
            else:
                error = await resp.text()
                print(f"❌ Markets failed: {resp.status} - {error}")

if __name__ == "__main__":
    asyncio.run(test_service())