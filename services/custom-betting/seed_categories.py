#!/usr/bin/env python3
"""
Seed script for market categories in custom betting service
"""

import asyncio
import sys
from pathlib import Path
from uuid import uuid4

# Add the services directory to path
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings
from main import MarketCategory

async def seed_categories():
    """Seed the database with default market categories"""
    
    # Initialize database
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    
    categories = [
        {
            "name": "Sports",
            "description": "Sports events, matches, tournaments, and player performance",
            "slug": "sports"
        },
        {
            "name": "Politics",
            "description": "Elections, policy outcomes, political events",
            "slug": "politics"
        },
        {
            "name": "Crypto",
            "description": "Cryptocurrency prices, DeFi events, blockchain outcomes",
            "slug": "crypto"
        },
        {
            "name": "Technology",
            "description": "Tech product launches, company performance, innovation milestones",
            "slug": "technology"
        },
        {
            "name": "Entertainment",
            "description": "Movies, TV shows, awards, celebrity events",
            "slug": "entertainment"
        },
        {
            "name": "Economics",
            "description": "Market indicators, inflation rates, economic events",
            "slug": "economics"
        },
        {
            "name": "Weather",
            "description": "Climate events, seasonal predictions, natural phenomena",
            "slug": "weather"
        },
        {
            "name": "Science",
            "description": "Research outcomes, space events, scientific discoveries",
            "slug": "science"
        },
        {
            "name": "Culture",
            "description": "Social trends, cultural events, viral phenomena",
            "slug": "culture"
        },
        {
            "name": "Other",
            "description": "Miscellaneous markets that don't fit other categories",
            "slug": "other"
        }
    ]
    
    async for db in get_database_write():
        try:
            print("Seeding market categories...")
            
            for cat_data in categories:
                # Check if category already exists
                from sqlalchemy import select
                existing_query = select(MarketCategory).where(MarketCategory.slug == cat_data["slug"])
                result = await db.execute(existing_query)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    category = MarketCategory(
                        id=uuid4(),
                        name=cat_data["name"],
                        description=cat_data["description"],
                        slug=cat_data["slug"],
                        is_active=True
                    )
                    db.add(category)
                    print(f"  ✓ Added category: {cat_data['name']}")
                else:
                    print(f"  - Category already exists: {cat_data['name']}")
            
            await db.commit()
            print("✅ Market categories seeded successfully")
            
        except Exception as e:
            await db.rollback()
            print(f"❌ Failed to seed categories: {str(e)}")
            raise
        finally:
            break  # Exit the async generator
    
    # Close database connections
    from shared.core.database.connection import close_database
    await close_database()

if __name__ == "__main__":
    asyncio.run(seed_categories())