/**
 * Metrics Routes for Prometheus Integration
 * ========================================
 * 
 * Provides Prometheus-compatible metrics for monitoring and alerting.
 */

const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Simple in-memory metrics store
let metrics = {
  http_requests_total: 0,
  http_request_duration_seconds: [],
  active_markets_total: 0,
  total_volume_usd: 0,
  active_users_total: 0,
  database_connections_active: 0,
  last_updated: Date.now()
};

// Middleware to track HTTP requests
const trackMetrics = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    metrics.http_requests_total++;
    metrics.http_request_duration_seconds.push(duration);
    
    // Keep only last 1000 requests for memory efficiency
    if (metrics.http_request_duration_seconds.length > 1000) {
      metrics.http_request_duration_seconds = metrics.http_request_duration_seconds.slice(-1000);
    }
  });
  
  next();
};

// Update business metrics from database
const updateBusinessMetrics = async () => {
  try {
    // Get active markets count
    const marketsResult = await pool.query(
      'SELECT COUNT(*) as count FROM custom_betting.custom_bet_markets WHERE status IN ($1, $2)',
      ['open', 'active']
    );
    metrics.active_markets_total = parseInt(marketsResult.rows[0].count) || 0;

    // Get total volume
    const volumeResult = await pool.query(
      'SELECT COALESCE(SUM(total_volume), 0) as total FROM custom_betting.custom_bet_markets'
    );
    metrics.total_volume_usd = parseFloat(volumeResult.rows[0].total) || 0;

    // Get database connection count
    const connectionsResult = await pool.query(
      'SELECT count(*) as active FROM pg_stat_activity WHERE state = $1',
      ['active']
    );
    metrics.database_connections_active = parseInt(connectionsResult.rows[0].active) || 0;

    metrics.last_updated = Date.now();
  } catch (error) {
    console.error('Failed to update business metrics:', error);
  }
};

// Update metrics every 30 seconds
setInterval(updateBusinessMetrics, 30000);
updateBusinessMetrics(); // Initial update

// Prometheus metrics endpoint
router.get('/prometheus', async (req, res) => {
  try {
    await updateBusinessMetrics();
    
    const avgResponseTime = metrics.http_request_duration_seconds.length > 0
      ? metrics.http_request_duration_seconds.reduce((a, b) => a + b, 0) / metrics.http_request_duration_seconds.length
      : 0;

    const p95ResponseTime = metrics.http_request_duration_seconds.length > 0
      ? metrics.http_request_duration_seconds.sort((a, b) => a - b)[Math.floor(metrics.http_request_duration_seconds.length * 0.95)]
      : 0;

    const prometheusMetrics = `
# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total{service="custom-betting"} ${metrics.http_requests_total}

# HELP http_request_duration_seconds HTTP request duration in seconds
# TYPE http_request_duration_seconds histogram
http_request_duration_seconds_sum{service="custom-betting"} ${metrics.http_request_duration_seconds.reduce((a, b) => a + b, 0)}
http_request_duration_seconds_count{service="custom-betting"} ${metrics.http_request_duration_seconds.length}
http_request_duration_seconds_bucket{service="custom-betting",le="0.1"} ${metrics.http_request_duration_seconds.filter(d => d <= 0.1).length}
http_request_duration_seconds_bucket{service="custom-betting",le="0.5"} ${metrics.http_request_duration_seconds.filter(d => d <= 0.5).length}
http_request_duration_seconds_bucket{service="custom-betting",le="1.0"} ${metrics.http_request_duration_seconds.filter(d => d <= 1.0).length}
http_request_duration_seconds_bucket{service="custom-betting",le="5.0"} ${metrics.http_request_duration_seconds.filter(d => d <= 5.0).length}
http_request_duration_seconds_bucket{service="custom-betting",le="+Inf"} ${metrics.http_request_duration_seconds.length}

# HELP betbet_active_markets_total Number of active betting markets
# TYPE betbet_active_markets_total gauge
betbet_active_markets_total{service="custom-betting"} ${metrics.active_markets_total}

# HELP betbet_total_volume_usd Total betting volume in USD
# TYPE betbet_total_volume_usd gauge
betbet_total_volume_usd{service="custom-betting"} ${metrics.total_volume_usd}

# HELP betbet_database_connections_active Active database connections
# TYPE betbet_database_connections_active gauge
betbet_database_connections_active{service="custom-betting"} ${metrics.database_connections_active}

# HELP betbet_metrics_last_updated_timestamp Last time metrics were updated
# TYPE betbet_metrics_last_updated_timestamp gauge
betbet_metrics_last_updated_timestamp{service="custom-betting"} ${metrics.last_updated}
`.trim();

    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    console.error('Failed to generate Prometheus metrics:', error);
    res.status(500).send('# Error generating metrics\n');
  }
});

// JSON metrics endpoint for debugging
router.get('/json', async (req, res) => {
  try {
    await updateBusinessMetrics();
    res.json({
      ...metrics,
      avg_response_time: metrics.http_request_duration_seconds.length > 0
        ? metrics.http_request_duration_seconds.reduce((a, b) => a + b, 0) / metrics.http_request_duration_seconds.length
        : 0
    });
  } catch (error) {
    console.error('Failed to get JSON metrics:', error);
    res.status(500).json({ error: 'Failed to get metrics' });
  }
});

module.exports = { router, trackMetrics };
