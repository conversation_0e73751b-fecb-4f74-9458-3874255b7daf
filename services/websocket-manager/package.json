{"name": "betbet-websocket-manager", "version": "1.0.0", "description": "WebSocket Manager for real-time communication across BetBet Platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "health": "curl -f http://localhost:8007/health || exit 1"}, "dependencies": {"ws": "^8.13.0", "express": "^4.18.2", "redis": "^4.6.5", "jsonwebtoken": "^9.0.0", "uuid": "^9.0.0", "dotenv": "^16.0.3", "winston": "^3.8.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^2.0.21", "jest": "^29.5.0"}, "engines": {"node": ">=18.0.0"}, "license": "MIT"}