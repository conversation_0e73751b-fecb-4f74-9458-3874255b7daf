version: '3.8'

services:
  # WebSocket Manager Service
  websocket-manager:
    build: .
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=http://localhost:3000,https://betbet-platform.com
    depends_on:
      - redis
    networks:
      - betbet-network
    volumes:
      - ./app:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for pub/sub messaging
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis-websocket-data:/data
    command: redis-server --appendonly yes --port 6379
    networks:
      - betbet-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Commander for debugging (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8082:8081"
    depends_on:
      - redis
    networks:
      - betbet-network

volumes:
  redis-websocket-data:

networks:
  betbet-network:
    external: true
    name: betbet_betbet-network