# Development files
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test files
test/
tests/
*.test.js
*.spec.js
__tests__/
coverage/
.nyc_output/

# Development tools
.git/
.gitignore
.env.local
.env.development
.env.test
.eslintrc*
.prettierrc*
.stylelintrc*
jest.config.js
webpack.config.js
rollup.config.js

# Documentation
README.md
CHANGELOG.md
*.md
docs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/
*.log.*
websocket-manager.log

# Temporary files
tmp/
temp/
.tmp/

# Development configs
docker-compose.yml
docker-compose.dev.yml
Dockerfile.dev

# Package manager files
yarn.lock