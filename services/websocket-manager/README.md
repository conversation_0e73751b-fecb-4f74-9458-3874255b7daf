# BetBet WebSocket Manager

**Unified Real-Time Communication Hub for BetBet Platform**

The WebSocket Manager provides centralized WebSocket connection management, message routing, and cross-module event propagation for the entire BetBet platform.

## 🎯 Purpose

The WebSocket Manager serves as the unified real-time communication layer, providing:

- **Connection Management**: Centralized WebSocket connection handling
- **Message Routing**: Intelligent message distribution across modules
- **Cross-Module Events**: Event propagation between different services
- **Real-Time Synchronization**: Live updates across all platform features
- **Scalable Architecture**: Redis pub/sub for horizontal scaling

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Apps   │────│  WebSocket Mgr   │────│  Microservices  │
│  - Web App      │    │  - Connection    │    │  - Gaming       │
│  - Mobile App   │    │    Management    │    │  - Betting      │
│  - Admin Panel  │    │  - Event Routing │    │  - Trading      │
└─────────────────┘    │  - Redis Pub/Sub │    │  - Leaderboards │
                       └──────────────────┘    │  - Achievements │
                                               └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Redis (included in docker-compose)
- Access to BetBet network

### 1. Start WebSocket Manager

```bash
# Navigate to WebSocket Manager directory
cd services/websocket-manager

# Start all services
docker-compose up -d

# Check service health
curl http://localhost:8080/health
```

### 2. Test WebSocket Connection

```javascript
// Test connection from browser console
const ws = new WebSocket('ws://localhost:8080/ws/user123');

ws.onopen = () => {
    console.log('Connected to WebSocket Manager');
    
    // Subscribe to gaming events
    ws.send(JSON.stringify({
        type: 'subscribe_module',
        module: 'gaming'
    }));
};

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('Received:', message);
};
```

### 3. Access Points

- **WebSocket Endpoint**: `ws://localhost:8080/ws/{user_id}`
- **HTTP API**: `http://localhost:8080`
- **Health Check**: `http://localhost:8080/health`
- **Stats**: `http://localhost:8080/stats`
- **Redis Commander**: `http://localhost:8082`

## 📡 WebSocket API

### Connection

```javascript
// Connect to WebSocket Manager
const ws = new WebSocket('ws://localhost:8080/ws/your-user-id');
```

### Message Format

All messages follow this standard format:

```typescript
interface WebSocketMessage {
    type: string;           // Message type (e.g., 'gaming_update', 'achievement_unlocked')
    module: string;         // Source module (e.g., 'gaming', 'betting', 'leaderboards')
    data: object;          // Message payload
    timestamp: string;     // ISO timestamp
    user_id?: string;      // Target user ID (optional)
    session_id?: string;   // Session identifier (optional)
}
```

### Subscription Commands

```javascript
// Subscribe to specific event types
ws.send(JSON.stringify({
    type: 'subscribe_events',
    event_types: ['achievement_unlocked', 'leaderboard_update', 'bet_matched']
}));

// Subscribe to all events from a module
ws.send(JSON.stringify({
    type: 'subscribe_module',
    module: 'gaming'  // or 'betting', 'trading', etc.
}));

// Publish custom event
ws.send(JSON.stringify({
    type: 'publish_event',
    event_type: 'custom_event',
    module: 'client',
    data: { message: 'Hello from client!' }
}));
```

## 🔄 Cross-Module Integration

### From Backend Services

Services can publish events to the WebSocket Manager via Redis:

```python
# In your service (e.g., gaming engine)
import redis
import json

redis_client = redis.Redis(host='redis', port=6379)

# Publish achievement event
event_data = {
    'type': 'achievement_unlocked',
    'user_id': 'user123',
    'data': {
        'achievement_id': 'first_win',
        'title': 'First Victory',
        'points': 100
    }
}

redis_client.publish('gaming:events', json.dumps(event_data))
```

### Event Types by Module

| Module | Event Types | Description |
|--------|-------------|-------------|
| **Gaming** | `game_started`, `game_ended`, `player_joined` | Game session events |
| **Betting** | `bet_placed`, `bet_matched`, `bet_settled` | Betting lifecycle events |
| **Trading** | `order_filled`, `price_update`, `portfolio_change` | Trading activity |
| **Leaderboards** | `rank_changed`, `achievement_unlocked`, `tier_promoted` | Ranking updates |
| **Achievements** | `achievement_unlocked`, `milestone_reached`, `badge_earned` | Achievement system |
| **Notifications** | `notification_sent`, `alert_triggered` | Global notifications |

## 🔗 Frontend Integration

### React Hook for WebSocket

```typescript
// hooks/useUnifiedWebSocket.ts
import { useEffect, useState, useRef } from 'react';

interface WebSocketMessage {
    type: string;
    module: string;
    data: any;
    timestamp: string;
    user_id?: string;
}

export const useUnifiedWebSocket = (userId: string) => {
    const [isConnected, setIsConnected] = useState(false);
    const [messages, setMessages] = useState<WebSocketMessage[]>([]);
    const ws = useRef<WebSocket | null>(null);

    useEffect(() => {
        // Connect to WebSocket Manager
        ws.current = new WebSocket(`ws://localhost:8080/ws/${userId}`);
        
        ws.current.onopen = () => {
            setIsConnected(true);
            console.log('Connected to WebSocket Manager');
        };
        
        ws.current.onmessage = (event) => {
            const message: WebSocketMessage = JSON.parse(event.data);
            setMessages(prev => [...prev, message]);
        };
        
        ws.current.onclose = () => {
            setIsConnected(false);
            console.log('Disconnected from WebSocket Manager');
        };
        
        return () => {
            ws.current?.close();
        };
    }, [userId]);

    const sendMessage = (message: any) => {
        if (ws.current && isConnected) {
            ws.current.send(JSON.stringify(message));
        }
    };

    const subscribeToModule = (module: string) => {
        sendMessage({
            type: 'subscribe_module',
            module: module
        });
    };

    const subscribeToEvents = (eventTypes: string[]) => {
        sendMessage({
            type: 'subscribe_events',
            event_types: eventTypes
        });
    };

    return {
        isConnected,
        messages,
        sendMessage,
        subscribeToModule,
        subscribeToEvents
    };
};
```

### Usage Example

```typescript
// In your React component
import { useUnifiedWebSocket } from '@/hooks/useUnifiedWebSocket';

export const GameDashboard = () => {
    const { isConnected, messages, subscribeToModule } = useUnifiedWebSocket('user123');
    
    useEffect(() => {
        if (isConnected) {
            // Subscribe to gaming and leaderboard events
            subscribeToModule('gaming');
            subscribeToModule('leaderboards');
        }
    }, [isConnected]);
    
    // Filter messages by type
    const achievementMessages = messages.filter(msg => msg.type === 'achievement_unlocked');
    const gameUpdates = messages.filter(msg => msg.module === 'gaming');
    
    return (
        <div>
            <div>Status: {isConnected ? 'Connected' : 'Disconnected'}</div>
            
            {/* Show live achievements */}
            {achievementMessages.map(msg => (
                <AchievementNotification key={msg.timestamp} achievement={msg.data} />
            ))}
            
            {/* Show game updates */}
            {gameUpdates.map(msg => (
                <GameUpdate key={msg.timestamp} update={msg.data} />
            ))}
        </div>
    );
};
```

## 📊 HTTP API

### Statistics Endpoint

```bash
curl http://localhost:8080/stats
```

```json
{
    "total_connections": 45,
    "total_users": 38,
    "subscriptions_by_event": {
        "achievement_unlocked": 15,
        "bet_matched": 8,
        "leaderboard_update": 22
    },
    "subscriptions_by_module": {
        "gaming": 12,
        "betting": 8,
        "leaderboards": 25
    }
}
```

### Broadcast Event

```bash
curl -X POST http://localhost:8080/broadcast/achievement_unlocked \
  -H "Content-Type: application/json" \
  -d '{
    "type": "achievement_unlocked",
    "module": "achievements",
    "data": {
        "achievement": "platform_expert",
        "user_id": "user123"
    }
  }'
```

### Send to Specific User

```bash
curl -X POST http://localhost:8080/send/user123 \
  -H "Content-Type: application/json" \
  -d '{
    "type": "private_notification",
    "module": "notifications",
    "data": {
        "message": "You have a new message!"
    }
  }'
```

## 🔧 Configuration

### Environment Variables

```bash
REDIS_URL=redis://redis:6379
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3000,https://betbet-platform.com
```

### Redis Channels

The WebSocket Manager subscribes to these Redis channels:

- `gaming:events` - Gaming engine events
- `betting:events` - Custom betting events  
- `experts:events` - Expert analyst events
- `sports:events` - Sports analysis events
- `trading:events` - Trading engine events
- `leaderboards:events` - Leaderboard updates
- `achievements:events` - Achievement unlocks
- `notifications:global` - Global notifications

## 📈 Performance & Monitoring

### Health Check

```bash
curl http://localhost:8080/health
```

### Redis Monitoring

```bash
# Connect to Redis CLI
docker-compose exec redis redis-cli

# Monitor pub/sub activity
MONITOR

# List active channels
PUBSUB CHANNELS *
```

### Connection Monitoring

The WebSocket Manager provides real-time statistics about:

- Total active connections
- Unique users connected
- Event subscription counts
- Module subscription distribution

## 🚀 Production Deployment

### Environment Configuration

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  websocket-manager:
    environment:
      - REDIS_URL=redis://redis-cluster:6379
      - LOG_LEVEL=WARNING
      - CORS_ORIGINS=https://app.betbet.com,https://mobile.betbet.com
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

### Scaling Considerations

1. **Horizontal Scaling**: Multiple WebSocket Manager instances
2. **Redis Cluster**: Use Redis cluster for high availability
3. **Load Balancing**: Sticky sessions for WebSocket connections
4. **Connection Limits**: Configure per-instance connection limits

## 🔍 Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure Redis is running and accessible
2. **CORS Errors**: Update CORS_ORIGINS environment variable
3. **Message Delivery**: Check Redis pub/sub channels are working
4. **Performance**: Monitor connection counts and Redis memory usage

### Debug Commands

```bash
# Check WebSocket Manager logs
docker-compose logs -f websocket-manager

# Test Redis connectivity
docker-compose exec websocket-manager python -c "import redis; r=redis.Redis(host='redis'); print(r.ping())"

# Monitor Redis pub/sub
docker-compose exec redis redis-cli monitor
```

## 📚 Next Steps

1. **Frontend Integration**: Update all modules to use unified WebSocket
2. **Mobile Support**: Configure WebSocket for React Native
3. **Advanced Features**: Implement message queuing and offline support
4. **Analytics**: Add detailed WebSocket usage analytics
5. **Security**: Implement WebSocket authentication and rate limiting

---

**Status**: ✅ WebSocket Manager Infrastructure Complete  
**Next**: Authentication Unification