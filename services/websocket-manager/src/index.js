const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const redis = require('redis');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const winston = require('winston');
const cors = require('cors');
require('dotenv').config();

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'websocket-manager.log' })
  ]
});

// Express app for health checks and REST endpoints
const app = express();
app.use(cors());
app.use(express.json());

const server = http.createServer(app);
const PORT = process.env.PORT || 8080;

// Redis clients for pub/sub
const redisPublisher = redis.createClient({
  host: process.env.REDIS_HOST || 'redis',
  port: process.env.REDIS_PORT || 6379
});

const redisSubscriber = redis.createClient({
  host: process.env.REDIS_HOST || 'redis',
  port: process.env.REDIS_PORT || 6379
});

// Connection management
const connections = new Map();
const userConnections = new Map();
const roomConnections = new Map();

// WebSocket Server
const wss = new WebSocket.Server({ 
  noServer: true
});

server.on('upgrade', (request, socket, head) => {
  const url = new URL(request.url, `http://${request.headers.host}`);
  
  if (url.pathname.startsWith('/ws/')) {
    wss.handleUpgrade(request, socket, head, (ws) => {
      wss.emit('connection', ws, request);
    });
  } else {
    socket.destroy();
  }
});

// Authentication function
const authenticateUser = (token) => {
  try {
    if (!token) return null;
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    return decoded;
  } catch (error) {
    logger.error('Token verification failed:', error.message);
    return null;
  }
};

// Connection handler
wss.on('connection', (ws, req) => {
  const connectionId = uuidv4();
  const clientIP = req.socket.remoteAddress;
  
  logger.info(`New WebSocket connection: ${connectionId} from ${clientIP}`);
  
  // Store connection
  connections.set(connectionId, {
    ws,
    id: connectionId,
    user: null,
    rooms: new Set(),
    authenticated: false,
    connectedAt: new Date()
  });

  // Send connection acknowledgment
  ws.send(JSON.stringify({
    type: 'connection',
    connectionId,
    message: 'Connected successfully'
  }));

  // Message handler
  ws.on('message', async (data) => {
    try {
      const message = JSON.parse(data);
      await handleMessage(connectionId, message);
    } catch (error) {
      logger.error(`Message parsing error for ${connectionId}:`, error.message);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }));
    }
  });

  // Disconnect handler
  ws.on('close', () => {
    handleDisconnection(connectionId);
  });

  // Error handler
  ws.on('error', (error) => {
    logger.error(`WebSocket error for ${connectionId}:`, error.message);
    handleDisconnection(connectionId);
  });
});

// Message handling
async function handleMessage(connectionId, message) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  const { type, ...payload } = message;

  switch (type) {
    case 'authenticate':
      await handleAuthentication(connectionId, payload);
      break;
    
    case 'join_room':
      await handleJoinRoom(connectionId, payload);
      break;
    
    case 'leave_room':
      await handleLeaveRoom(connectionId, payload);
      break;
    
    case 'send_message':
      await handleSendMessage(connectionId, payload);
      break;
    
    case 'subscribe':
      await handleSubscribe(connectionId, payload);
      break;
    
    case 'unsubscribe':
      await handleUnsubscribe(connectionId, payload);
      break;
    
    case 'ping':
      connection.ws.send(JSON.stringify({ type: 'pong' }));
      break;
    
    default:
      connection.ws.send(JSON.stringify({
        type: 'error',
        message: 'Unknown message type'
      }));
  }
}

// Authentication handler
async function handleAuthentication(connectionId, { token }) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  const user = authenticateUser(token);
  if (!user) {
    connection.ws.send(JSON.stringify({
      type: 'auth_error',
      message: 'Invalid token'
    }));
    return;
  }

  connection.user = user;
  connection.authenticated = true;

  // Store user connection mapping
  if (!userConnections.has(user.id)) {
    userConnections.set(user.id, new Set());
  }
  userConnections.get(user.id).add(connectionId);

  connection.ws.send(JSON.stringify({
    type: 'authenticated',
    user: {
      id: user.id,
      email: user.email,
      role: user.role
    }
  }));

  logger.info(`User ${user.id} authenticated on connection ${connectionId}`);
}

// Room management
async function handleJoinRoom(connectionId, { room }) {
  const connection = connections.get(connectionId);
  if (!connection || !connection.authenticated) {
    connection.ws.send(JSON.stringify({
      type: 'error',
      message: 'Authentication required'
    }));
    return;
  }

  connection.rooms.add(room);
  
  if (!roomConnections.has(room)) {
    roomConnections.set(room, new Set());
  }
  roomConnections.get(room).add(connectionId);

  connection.ws.send(JSON.stringify({
    type: 'room_joined',
    room
  }));

  // Notify others in the room
  broadcastToRoom(room, {
    type: 'user_joined',
    room,
    user: connection.user,
    timestamp: new Date().toISOString()
  }, connectionId);

  logger.info(`User ${connection.user.id} joined room: ${room}`);
}

async function handleLeaveRoom(connectionId, { room }) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  connection.rooms.delete(room);
  
  if (roomConnections.has(room)) {
    roomConnections.get(room).delete(connectionId);
    if (roomConnections.get(room).size === 0) {
      roomConnections.delete(room);
    }
  }

  connection.ws.send(JSON.stringify({
    type: 'room_left',
    room
  }));

  // Notify others in the room
  if (connection.authenticated) {
    broadcastToRoom(room, {
      type: 'user_left',
      room,
      user: connection.user,
      timestamp: new Date().toISOString()
    });
  }

  logger.info(`Connection ${connectionId} left room: ${room}`);
}

// Message sending
async function handleSendMessage(connectionId, { room, message, target }) {
  const connection = connections.get(connectionId);
  if (!connection || !connection.authenticated) return;

  const messageData = {
    type: 'message',
    from: connection.user,
    message,
    timestamp: new Date().toISOString()
  };

  if (target === 'user') {
    // Direct message to specific user
    sendToUser(message.to, messageData);
  } else if (room) {
    // Message to room
    messageData.room = room;
    broadcastToRoom(room, messageData, connectionId);
  }

  // Publish to Redis for other service instances
  await redisPublisher.publish('websocket_messages', JSON.stringify({
    type: 'broadcast',
    room,
    target,
    data: messageData
  }));
}

// Subscription management
async function handleSubscribe(connectionId, { channels }) {
  const connection = connections.get(connectionId);
  if (!connection || !connection.authenticated) return;

  for (const channel of channels) {
    await redisSubscriber.subscribe(channel);
  }

  connection.ws.send(JSON.stringify({
    type: 'subscribed',
    channels
  }));
}

async function handleUnsubscribe(connectionId, { channels }) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  for (const channel of channels) {
    await redisSubscriber.unsubscribe(channel);
  }

  connection.ws.send(JSON.stringify({
    type: 'unsubscribed',
    channels
  }));
}

// Broadcasting functions
function broadcastToRoom(room, message, excludeConnectionId = null) {
  const roomConns = roomConnections.get(room);
  if (!roomConns) return;

  const messageStr = JSON.stringify(message);
  
  roomConns.forEach(connId => {
    if (connId === excludeConnectionId) return;
    
    const connection = connections.get(connId);
    if (connection && connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(messageStr);
    }
  });
}

function sendToUser(userId, message) {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  const messageStr = JSON.stringify(message);
  
  userConns.forEach(connId => {
    const connection = connections.get(connId);
    if (connection && connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(messageStr);
    }
  });
}

function broadcastToAll(message, excludeConnectionId = null) {
  const messageStr = JSON.stringify(message);
  
  connections.forEach((connection, connId) => {
    if (connId === excludeConnectionId) return;
    
    if (connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(messageStr);
    }
  });
}

// Disconnect handler
function handleDisconnection(connectionId) {
  const connection = connections.get(connectionId);
  if (!connection) return;

  // Remove from user connections
  if (connection.user) {
    const userConns = userConnections.get(connection.user.id);
    if (userConns) {
      userConns.delete(connectionId);
      if (userConns.size === 0) {
        userConnections.delete(connection.user.id);
      }
    }
  }

  // Remove from all rooms
  connection.rooms.forEach(room => {
    if (roomConnections.has(room)) {
      roomConnections.get(room).delete(connectionId);
      if (roomConnections.get(room).size === 0) {
        roomConnections.delete(room);
      }
    }
    
    // Notify room members
    if (connection.authenticated) {
      broadcastToRoom(room, {
        type: 'user_disconnected',
        room,
        user: connection.user,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Remove connection
  connections.delete(connectionId);
  
  logger.info(`Connection ${connectionId} disconnected`);
}

// Redis subscriber for inter-service communication
redisSubscriber.on('message', (channel, message) => {
  try {
    const data = JSON.parse(message);
    
    switch (data.type) {
      case 'broadcast':
        if (data.room) {
          broadcastToRoom(data.room, data.data);
        } else if (data.target === 'all') {
          broadcastToAll(data.data);
        }
        break;
      
      case 'user_message':
        sendToUser(data.userId, data.data);
        break;
    }
  } catch (error) {
    logger.error('Redis message handling error:', error.message);
  }
});

// REST endpoints
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    connections: connections.size,
    rooms: roomConnections.size,
    users: userConnections.size,
    timestamp: new Date().toISOString()
  });
});

app.get('/stats', (req, res) => {
  res.json({
    connections: connections.size,
    authenticatedConnections: Array.from(connections.values()).filter(c => c.authenticated).length,
    rooms: roomConnections.size,
    users: userConnections.size,
    roomDetails: Array.from(roomConnections.entries()).map(([room, conns]) => ({
      room,
      connections: conns.size
    }))
  });
});

// Broadcast endpoint for other services
app.post('/broadcast', (req, res) => {
  const { room, message, target, userId } = req.body;
  
  if (target === 'user' && userId) {
    sendToUser(userId, message);
  } else if (room) {
    broadcastToRoom(room, message);
  } else if (target === 'all') {
    broadcastToAll(message);
  }
  
  res.json({ success: true });
});

// Health check for Docker
app.get('/ping', (req, res) => {
  res.send('pong');
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
  logger.info(`WebSocket Manager running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  wss.close(() => {
    redisPublisher.quit();
    redisSubscriber.quit();
    server.close(() => {
      process.exit(0);
    });
  });
});

module.exports = { app, wss };