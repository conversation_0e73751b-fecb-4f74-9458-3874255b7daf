"""
BetBet WebSocket Manager
========================

Unified WebSocket management system for real-time communication across all BetBet modules.
Handles connection management, message routing, and cross-module event propagation.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Set, Optional, List, Any
from uuid import UUID, uuid4

import redis.asyncio as redis
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="BetBet WebSocket Manager",
    description="Unified real-time communication hub for BetBet platform",
    version="1.0.0"
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://betbet-platform.com", "https://*.betbet-platform.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class WebSocketMessage(BaseModel):
    """Standard WebSocket message format"""
    type: str
    module: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_id: Optional[str] = None
    session_id: Optional[str] = None

class ConnectionInfo(BaseModel):
    """WebSocket connection information"""
    user_id: str
    session_id: str
    connected_at: datetime
    modules: Set[str] = Field(default_factory=set)

class EventSubscription(BaseModel):
    """Event subscription model"""
    user_id: str
    event_types: List[str]
    modules: List[str]

# WebSocket Manager Class
class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Set[str]] = {}  # user_id -> session_ids
        self.session_info: Dict[str, ConnectionInfo] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # event_type -> user_ids
        self.module_subscriptions: Dict[str, Set[str]] = {}  # module -> user_ids
        self.redis: Optional[redis.Redis] = None
        
    async def init_redis(self):
        """Initialize Redis connection for pub/sub"""
        try:
            self.redis = redis.from_url("redis://redis:6379", decode_responses=True)
            await self.redis.ping()
            logger.info("Redis connection established")
            
            # Start Redis subscriber
            asyncio.create_task(self._redis_subscriber())
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            
    async def _redis_subscriber(self):
        """Subscribe to Redis channels for cross-module events"""
        if not self.redis:
            return
            
        pubsub = self.redis.pubsub()
        
        # Subscribe to all module channels
        channels = [
            "gaming:events",
            "betting:events", 
            "experts:events",
            "sports:events",
            "trading:events",
            "leaderboards:events",
            "achievements:events",
            "notifications:global"
        ]
        
        for channel in channels:
            await pubsub.subscribe(channel)
            
        logger.info(f"Subscribed to Redis channels: {channels}")
        
        async for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    data = json.loads(message['data'])
                    await self._handle_redis_message(message['channel'], data)
                except Exception as e:
                    logger.error(f"Error handling Redis message: {e}")
                    
    async def _handle_redis_message(self, channel: str, data: Dict[str, Any]):
        """Handle messages from Redis pub/sub"""
        module = channel.split(':')[0]
        
        # Create WebSocket message
        ws_message = WebSocketMessage(
            type=data.get('type', 'module_event'),
            module=module,
            data=data,
            user_id=data.get('user_id')
        )
        
        # Route to appropriate subscribers
        if data.get('user_id'):
            await self.send_to_user(data['user_id'], ws_message)
        elif data.get('broadcast', False):
            await self.broadcast_to_module_subscribers(module, ws_message)
        else:
            await self.broadcast_to_subscribers(ws_message.type, ws_message)
            
    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        session_id = str(uuid4())
        
        # Store connection
        self.connections[session_id] = websocket
        
        # Update user sessions
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = set()
        self.user_sessions[user_id].add(session_id)
        
        # Store session info
        self.session_info[session_id] = ConnectionInfo(
            user_id=user_id,
            session_id=session_id,
            connected_at=datetime.utcnow()
        )
        
        logger.info(f"WebSocket connected: user={user_id}, session={session_id}")
        
        # Send connection confirmation
        await self.send_to_session(session_id, WebSocketMessage(
            type="connection_established",
            module="websocket_manager",
            data={
                "session_id": session_id,
                "user_id": user_id,
                "status": "connected"
            }
        ))
        
        return session_id
        
    async def disconnect(self, session_id: str):
        """Handle WebSocket disconnection"""
        if session_id not in self.connections:
            return
            
        session_info = self.session_info.get(session_id)
        if session_info:
            user_id = session_info.user_id
            
            # Remove from user sessions
            if user_id in self.user_sessions:
                self.user_sessions[user_id].discard(session_id)
                if not self.user_sessions[user_id]:
                    del self.user_sessions[user_id]
                    
            # Remove subscriptions for this session
            for event_type, subscribers in self.subscriptions.items():
                subscribers.discard(user_id)
                
            for module, subscribers in self.module_subscriptions.items():
                subscribers.discard(user_id)
                
        # Clean up connection
        del self.connections[session_id]
        if session_id in self.session_info:
            del self.session_info[session_id]
            
        logger.info(f"WebSocket disconnected: session={session_id}")
        
    async def send_to_session(self, session_id: str, message: WebSocketMessage):
        """Send message to specific session"""
        if session_id in self.connections:
            try:
                await self.connections[session_id].send_text(message.model_dump_json())
            except Exception as e:
                logger.error(f"Failed to send message to session {session_id}: {e}")
                await self.disconnect(session_id)
                
    async def send_to_user(self, user_id: str, message: WebSocketMessage):
        """Send message to all sessions for a user"""
        if user_id in self.user_sessions:
            tasks = []
            for session_id in self.user_sessions[user_id].copy():
                tasks.append(self.send_to_session(session_id, message))
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def broadcast_to_subscribers(self, event_type: str, message: WebSocketMessage):
        """Broadcast message to all subscribers of an event type"""
        if event_type in self.subscriptions:
            tasks = []
            for user_id in self.subscriptions[event_type].copy():
                tasks.append(self.send_to_user(user_id, message))
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def broadcast_to_module_subscribers(self, module: str, message: WebSocketMessage):
        """Broadcast message to all subscribers of a module"""
        if module in self.module_subscriptions:
            tasks = []
            for user_id in self.module_subscriptions[module].copy():
                tasks.append(self.send_to_user(user_id, message))
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def subscribe_to_events(self, user_id: str, event_types: List[str]):
        """Subscribe user to specific event types"""
        for event_type in event_types:
            if event_type not in self.subscriptions:
                self.subscriptions[event_type] = set()
            self.subscriptions[event_type].add(user_id)
            
        logger.info(f"User {user_id} subscribed to events: {event_types}")
        
    async def subscribe_to_module(self, user_id: str, module: str):
        """Subscribe user to all events from a module"""
        if module not in self.module_subscriptions:
            self.module_subscriptions[module] = set()
        self.module_subscriptions[module].add(user_id)
        
        # Update session info
        for session_id in self.user_sessions.get(user_id, []):
            if session_id in self.session_info:
                self.session_info[session_id].modules.add(module)
                
        logger.info(f"User {user_id} subscribed to module: {module}")
        
    async def publish_event(self, message: WebSocketMessage):
        """Publish event to Redis for cross-service communication"""
        if self.redis:
            channel = f"{message.module}:events"
            await self.redis.publish(channel, message.model_dump_json())
            
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get current connection statistics"""
        return {
            "total_connections": len(self.connections),
            "total_users": len(self.user_sessions),
            "subscriptions_by_event": {
                event_type: len(users) for event_type, users in self.subscriptions.items()
            },
            "subscriptions_by_module": {
                module: len(users) for module, users in self.module_subscriptions.items()
            }
        }

# Global manager instance
manager = WebSocketManager()

# Startup event
@app.on_event("startup")
async def startup():
    await manager.init_redis()

# WebSocket endpoint
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    session_id = await manager.connect(websocket, user_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message_data = json.loads(data)
                
                # Handle different message types
                message_type = message_data.get('type')
                
                if message_type == 'subscribe_events':
                    event_types = message_data.get('event_types', [])
                    await manager.subscribe_to_events(user_id, event_types)
                    
                elif message_type == 'subscribe_module':
                    module = message_data.get('module')
                    if module:
                        await manager.subscribe_to_module(user_id, module)
                        
                elif message_type == 'publish_event':
                    # Client wants to publish an event
                    ws_message = WebSocketMessage(
                        type=message_data.get('event_type', 'custom'),
                        module=message_data.get('module', 'client'),
                        data=message_data.get('data', {}),
                        user_id=user_id
                    )
                    await manager.publish_event(ws_message)
                    
                else:
                    # Echo back unknown message types for debugging
                    response = WebSocketMessage(
                        type="echo",
                        module="websocket_manager",
                        data=message_data,
                        user_id=user_id
                    )
                    await manager.send_to_session(session_id, response)
                    
            except json.JSONDecodeError:
                # Send error message for invalid JSON
                error_message = WebSocketMessage(
                    type="error",
                    module="websocket_manager",
                    data={"error": "Invalid JSON format"},
                    user_id=user_id
                )
                await manager.send_to_session(session_id, error_message)
                
    except WebSocketDisconnect:
        await manager.disconnect(session_id)
        
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
        await manager.disconnect(session_id)

# REST API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "websocket-manager"}

@app.get("/stats")
async def get_stats():
    """Get connection statistics"""
    return manager.get_connection_stats()

@app.post("/broadcast/{event_type}")
async def broadcast_event(event_type: str, message: WebSocketMessage):
    """Broadcast event to all subscribers via REST API"""
    message.type = event_type
    await manager.broadcast_to_subscribers(event_type, message)
    return {"status": "broadcasted", "event_type": event_type}

@app.post("/send/{user_id}")
async def send_to_user_endpoint(user_id: str, message: WebSocketMessage):
    """Send message to specific user via REST API"""
    await manager.send_to_user(user_id, message)
    return {"status": "sent", "user_id": user_id}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)