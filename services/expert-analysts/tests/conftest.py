"""
Test Configuration and Fixtures
================================

Pytest configuration and shared fixtures for Expert Analyst Marketplace tests.

Author: Claude-API
Date: 2025-07-21
"""

import asyncio
import pytest
from typing import AsyncGenerator, Generator
from uuid import uuid4

from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from main import app
from app.api.dependencies import get_database_session_dep, get_settings
from app.database.models import Base, Expert, SubscriptionTier, Subscription

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def db_engine():
    """Create test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()

@pytest.fixture
async def db_session(db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async with AsyncSession(db_engine, expire_on_commit=False) as session:
        yield session

@pytest.fixture
def client() -> TestClient:
    """Create test client."""
    return TestClient(app)

@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def test_user_id():
    """Test user ID."""
    return uuid4()

@pytest.fixture
def test_expert_id():
    """Test expert ID."""
    return uuid4()

@pytest.fixture
async def sample_expert(db_session: AsyncSession, test_user_id, test_expert_id) -> Expert:
    """Create sample expert for testing."""
    expert = Expert(
        id=test_expert_id,
        user_id=test_user_id,
        expert_name="Test Expert",
        slug="test-expert",
        specialty="NBA",
        bio="Test expert bio",
        verification_status="verified",
        is_active=True,
        total_picks=50,
        winning_picks=30,
        win_rate=0.60,
        total_subscribers=100,
        average_rating=4.5,
        total_reviews=20
    )
    
    db_session.add(expert)
    await db_session.commit()
    await db_session.refresh(expert)
    
    return expert

@pytest.fixture
async def sample_subscription_tier(db_session: AsyncSession, sample_expert: Expert) -> SubscriptionTier:
    """Create sample subscription tier for testing."""
    tier = SubscriptionTier(
        expert_id=sample_expert.id,
        tier_name="Premium",
        tier_slug="premium",
        price_monthly=29.99,
        access_level="premium",
        features_included=["Premium picks", "Analysis"],
        created_by=sample_expert.user_id,
        updated_by=sample_expert.user_id
    )
    
    db_session.add(tier)
    await db_session.commit()
    await db_session.refresh(tier)
    
    return tier

@pytest.fixture
async def sample_subscription(
    db_session: AsyncSession, 
    sample_expert: Expert, 
    sample_subscription_tier: SubscriptionTier,
    test_user_id
) -> Subscription:
    """Create sample subscription for testing."""
    from datetime import datetime, timedelta
    
    subscription = Subscription(
        user_id=test_user_id,
        expert_id=sample_expert.id,
        tier_id=sample_subscription_tier.id,
        subscription_type="monthly",
        status="active",
        amount_paid=29.99,
        billing_cycle="monthly",
        starts_at=datetime.now(),
        ends_at=datetime.now() + timedelta(days=30),
        created_by=test_user_id,
        updated_by=test_user_id
    )
    
    db_session.add(subscription)
    await db_session.commit()
    await db_session.refresh(subscription)
    
    return subscription

# Mock authentication
@pytest.fixture
def mock_jwt_token(test_user_id):
    """Create mock JWT token for testing."""
    from jose import jwt
    from app.api.dependencies import get_settings
    
    settings = get_settings()
    payload = {"sub": str(test_user_id)}
    return jwt.encode(payload, settings.jwt_secret_key, algorithm=settings.jwt_algorithm)

@pytest.fixture
def auth_headers(mock_jwt_token):
    """Create authorization headers for testing."""
    return {"Authorization": f"Bearer {mock_jwt_token}"}

# Override dependencies for testing
def override_get_database_session_dep(db_session):
    """Override database session dependency for testing."""
    def _override():
        return db_session
    return _override