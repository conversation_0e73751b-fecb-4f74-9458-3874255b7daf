#!/usr/bin/env python3
import sys
import urllib.request
import urllib.error

try:
    with urllib.request.urlopen('http://127.0.0.1:8004/health', timeout=3) as response:
        if response.status == 200:
            print('Health check passed')
            sys.exit(0)
        else:
            print(f'Health check failed with status: {response.status}')
            sys.exit(1)
except Exception as e:
    print(f'Health check failed: {str(e)}')
    sys.exit(1)