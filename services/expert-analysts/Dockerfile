FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY expert-analysts/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules and source code
COPY shared/ ./shared/
COPY expert-analysts/ .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# Expose port
EXPOSE 8004

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python healthcheck.py

# Start the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8004"]