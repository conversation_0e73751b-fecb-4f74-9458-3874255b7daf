import os
import sys
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Define basic models for expert analysis
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class Expert(Base):
    __tablename__ = "experts"
    __table_args__ = {"schema": "expert_analysis"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    display_name = Column(String(100), nullable=False)
    bio = Column(Text)
    expertise_areas = Column(ARRAY(String))
    rating = Column(Numeric(3, 2), default=0)
    total_picks = Column(Integer, default=0)
    successful_picks = Column(Integer, default=0)
    roi_percentage = Column(Numeric(6, 2), default=0)
    subscription_price = Column(Numeric(10, 2))
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Pick(Base):
    __tablename__ = "picks"
    __table_args__ = {"schema": "expert_analysis"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    expert_id = Column(PostgresUUID(as_uuid=True), ForeignKey("expert_analysis.experts.id"), nullable=False)
    title = Column(String(300), nullable=False)
    description = Column(Text)
    sport = Column(String(50))
    confidence_level = Column(Integer)  # 1-5
    odds = Column(Numeric(6, 3))
    stake_recommendation = Column(Numeric(5, 2))
    status = Column(String(20), default='pending')
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000",
        "https://betbet.com",
        "https://admin.betbet.com"
    ]
    
    SERVICE_NAME: str = "expert-analysis"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8004
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"

settings = Settings()

app = FastAPI(
    title="BetBet Expert Analysis",
    description="Expert predictions and insights platform",
    version=settings.SERVICE_VERSION
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Expert Analysis Service connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

@app.get("/")
def read_root():
    return {
        "service": "BetBet Expert Analysis",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "experts": "/api/v1/experts/list",
            "picks": "/api/v1/experts/picks",
            "analytics": "/api/v1/experts/analytics/overview"
        }
    }

@app.get("/health")
async def health_check():
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/experts/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get experts analytics overview"""

    # Get total experts count
    total_experts_query = select(func.count(Expert.id)).where(
        Expert.is_verified == True
    )
    total_experts_result = await db.execute(total_experts_query)
    total_experts = total_experts_result.scalar() or 0

    # Get total predictions count (using Pick model)
    total_predictions_query = select(func.count(Pick.id))
    total_predictions_result = await db.execute(total_predictions_query)
    total_predictions = total_predictions_result.scalar() or 0

    # Get average accuracy (simplified calculation)
    avg_accuracy = 78.5  # This would be calculated from actual prediction results

    return {
        "total_experts": total_experts,
        "total_predictions": total_predictions,
        "average_accuracy": avg_accuracy,
        "active_followers": 2840  # This would come from follower relationships
    }

@app.get("/api/v1/experts/list")
async def get_experts(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    sport: Optional[str] = None,
    verified_only: bool = False,
    db: AsyncSession = Depends(get_database_write)
):
    """Get list of experts with filtering"""
    
    query = select(Expert)
    
    if verified_only:
        query = query.where(Expert.is_verified == True)
    if sport:
        query = query.where(Expert.expertise_areas.contains([sport]))
    
    query = query.order_by(Expert.roi_percentage.desc())
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    experts = result.scalars().all()
    
    count_query = select(func.count(Expert.id))
    if verified_only:
        count_query = count_query.where(Expert.is_verified == True)
    if sport:
        count_query = count_query.where(Expert.expertise_areas.contains([sport]))
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    return {
        "experts": [{
            "id": str(expert.id),
            "display_name": expert.display_name,
            "bio": expert.bio,
            "expertise_areas": expert.expertise_areas,
            "rating": float(expert.rating),
            "total_picks": expert.total_picks,
            "successful_picks": expert.successful_picks,
            "roi_percentage": float(expert.roi_percentage),
            "subscription_price": float(expert.subscription_price) if expert.subscription_price else None,
            "is_verified": expert.is_verified
        } for expert in experts],
        "total_count": total_count,
        "page": page,
        "limit": limit
    }

@app.get("/api/v1/experts/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get platform analytics for expert analysis"""
    
    # Total experts
    total_experts_query = select(func.count(Expert.id))
    total_experts_result = await db.execute(total_experts_query)
    total_experts = total_experts_result.scalar() or 0
    
    # Verified experts
    verified_experts_query = select(func.count(Expert.id)).where(Expert.is_verified == True)
    verified_experts_result = await db.execute(verified_experts_query)
    verified_experts = verified_experts_result.scalar() or 0
    
    # Total picks today
    today = datetime.utcnow().date()
    picks_today_query = select(func.count(Pick.id)).where(
        Pick.created_at >= today
    )
    picks_today_result = await db.execute(picks_today_query)
    picks_today = picks_today_result.scalar() or 0
    
    # Average ROI
    avg_roi_query = select(func.avg(Expert.roi_percentage)).where(Expert.total_picks > 10)
    avg_roi_result = await db.execute(avg_roi_query)
    avg_roi = avg_roi_result.scalar() or 0
    
    return {
        "total_experts": total_experts,
        "verified_experts": verified_experts,
        "picks_today": picks_today,
        "average_roi": float(avg_roi),
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/api/v1/experts/picks")
async def get_expert_picks(
    expert_id: Optional[str] = Query(None, description="Filter by expert ID"),
    sport: Optional[str] = Query(None, description="Filter by sport"),
    status: Optional[str] = Query(None, description="Filter by pick status"),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get expert picks with optional filtering"""
    
    query = select(Pick).join(Expert, Pick.expert_id == Expert.id)
    
    # Apply filters
    if expert_id:
        query = query.where(Pick.expert_id == UUID(expert_id))
    if sport:
        query = query.where(Pick.sport == sport)
    if status:
        query = query.where(Pick.status == status)
    
    # Get total count
    count_query = select(func.count(Pick.id)).select_from(
        Pick.__table__.join(Expert.__table__, Pick.expert_id == Expert.id)
    )
    if expert_id:
        count_query = count_query.where(Pick.expert_id == UUID(expert_id))
    if sport:
        count_query = count_query.where(Pick.sport == sport)
    if status:
        count_query = count_query.where(Pick.status == status)
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    # Apply pagination and ordering
    offset = (page - 1) * limit
    query = query.order_by(Pick.created_at.desc()).offset(offset).limit(limit)
    
    result = await db.execute(query)
    picks = result.scalars().all()
    
    return {
        "picks": [{
            "id": str(pick.id),
            "expert_id": str(pick.expert_id),
            "title": pick.title,
            "description": pick.description,
            "sport": pick.sport,
            "confidence_level": pick.confidence_level,
            "odds": float(pick.odds) if pick.odds else None,
            "stake_recommendation": float(pick.stake_recommendation) if pick.stake_recommendation else None,
            "status": pick.status,
            "created_at": pick.created_at.isoformat()
        } for pick in picks],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)