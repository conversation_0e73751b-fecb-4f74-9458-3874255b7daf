# BetBet Expert Analyst Marketplace - Dependencies
# ================================================

# FastAPI and async web framework
fastapi[all]==0.104.1
uvicorn[standard]==0.24.0
pydantic[email]==2.4.2
pydantic-settings==2.0.3

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
sqlalchemy-utils==0.41.1

# Authentication and Security  
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
passlib[bcrypt]==1.7.4
cryptography==41.0.7

# Structured logging
structlog==23.2.0
python-json-logger==2.0.7

# HTTP client and utilities
httpx==0.25.2
aiofiles==23.2.1
python-dateutil==2.8.2

# WebSocket support
websockets==12.0

# Data validation and serialization
pydantic==2.4.2
email-validator==2.1.0

# Financial calculations
# decimal is a built-in Python module - no installation needed

# Redis for caching (optional)
redis[hiredis]==5.0.1
aioredis==2.0.1

# Monitoring and observability
prometheus-client==0.19.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2  # for testing

# Production WSGI server
gunicorn==21.2.0