"""
Revenue Management API Endpoints
=================================

FastAPI endpoints for revenue sharing, payouts, and financial management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date, timedelta
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_expert, get_expert_by_id,
    get_read_database_session, get_database_session_dep,
    require_admin_permission, get_platform_settings
)
from app.database.models import Expert, RevenueSharing, Subscription, ExpertAnalystTransaction
from app.schemas.revenue import (
    RevenueShareResponse, PayoutRequest, PayoutResponse,
    EarningsResponse, RevenueReportResponse
)
from app.schemas.common import BaseResponse, StatusResponse

logger = structlog.get_logger()

router = APIRouter()

@router.get("/experts/{expert_id}/earnings", response_model=EarningsResponse)
async def get_expert_earnings(
    expert_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert's earnings summary (expert owner only)
    """
    try:
        # Verify expert ownership
        expert_query = select(Expert).where(
            Expert.id == expert_id,
            Expert.user_id == user_id,
            Expert.deleted_at.is_(None)
        )
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one_or_none()
        
        if not expert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert profile not found or access denied"
            )
        
        # Get current period earnings (this month)
        current_month_start = date.today().replace(day=1)
        next_month = current_month_start.replace(month=current_month_start.month + 1) if current_month_start.month < 12 else current_month_start.replace(year=current_month_start.year + 1, month=1)
        current_month_end = next_month - timedelta(days=1)
        
        current_earnings_query = select(func.sum(RevenueSharing.final_payout_amount)).where(
            RevenueSharing.expert_id == expert_id,
            RevenueSharing.revenue_period_start >= current_month_start,
            RevenueSharing.revenue_period_end <= current_month_end,
            RevenueSharing.payout_status.in_(['pending', 'processing', 'paid'])
        )
        current_earnings_result = await session.execute(current_earnings_query)
        current_period_earnings = current_earnings_result.scalar() or Decimal('0')
        
        # Get pending payouts
        pending_query = select(func.sum(RevenueSharing.final_payout_amount)).where(
            RevenueSharing.expert_id == expert_id,
            RevenueSharing.payout_status == 'pending'
        )
        pending_result = await session.execute(pending_query)
        pending_payouts = pending_result.scalar() or Decimal('0')
        
        # Get total earnings and payouts
        total_earnings = expert.total_revenue
        
        total_payouts_query = select(func.sum(RevenueSharing.final_payout_amount)).where(
            RevenueSharing.expert_id == expert_id,
            RevenueSharing.payout_status == 'paid'
        )
        total_payouts_result = await session.execute(total_payouts_query)
        total_payouts = total_payouts_result.scalar() or Decimal('0')
        
        # Calculate available for payout
        available_for_payout = current_period_earnings - pending_payouts
        
        # Get YTD earnings for tax reporting
        year_start = date(date.today().year, 1, 1)
        ytd_query = select(func.sum(RevenueSharing.final_payout_amount)).where(
            RevenueSharing.expert_id == expert_id,
            RevenueSharing.revenue_period_start >= year_start,
            RevenueSharing.payout_status.in_(['pending', 'processing', 'paid'])
        )
        ytd_result = await session.execute(ytd_query)
        ytd_earnings = ytd_result.scalar() or Decimal('0')
        
        # Get tax withholding YTD
        ytd_tax_query = select(func.sum(RevenueSharing.tax_withholding_amount)).where(
            RevenueSharing.expert_id == expert_id,
            RevenueSharing.revenue_period_start >= year_start
        )
        ytd_tax_result = await session.execute(ytd_tax_query)
        tax_withholding_ytd = ytd_tax_result.scalar() or Decimal('0')
        
        # Next payout date (assuming monthly payouts on 15th)
        today = date.today()
        if today.day <= 15:
            next_payout_date = today.replace(day=15)
        else:
            if today.month == 12:
                next_payout_date = date(today.year + 1, 1, 15)
            else:
                next_payout_date = today.replace(month=today.month + 1, day=15)
        
        return EarningsResponse(
            expert_id=expert_id,
            current_period_earnings=current_period_earnings,
            pending_payouts=pending_payouts,
            available_for_payout=max(Decimal('0'), available_for_payout),
            total_earnings=total_earnings,
            total_payouts=total_payouts,
            subscription_revenue=current_period_earnings,  # Most revenue is subscription-based
            bonus_earnings=Decimal('0'),  # Placeholder for bonus programs
            referral_earnings=Decimal('0'),  # Placeholder for referral programs
            next_payout_date=next_payout_date,
            next_payout_amount=pending_payouts,
            ytd_earnings=ytd_earnings,
            tax_withholding_ytd=tax_withholding_ytd
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get expert earnings", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve earnings information"
        )

@router.get("/experts/{expert_id}/revenue-sharing", response_model=List[RevenueShareResponse])
async def get_expert_revenue_sharing(
    expert_id: UUID,
    limit: int = Query(12, ge=1, le=100),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert's revenue sharing records (expert owner only)
    """
    try:
        # Verify expert ownership
        expert_query = select(Expert).where(
            Expert.id == expert_id,
            Expert.user_id == user_id,
            Expert.deleted_at.is_(None)
        )
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one_or_none()
        
        if not expert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert profile not found or access denied"
            )
        
        # Get revenue sharing records
        query = select(RevenueSharing).where(
            RevenueSharing.expert_id == expert_id
        ).order_by(RevenueSharing.revenue_period_start.desc()).limit(limit)
        
        result = await session.execute(query)
        revenue_records = result.scalars().all()
        
        return [
            RevenueShareResponse(
                id=record.id,
                expert_id=record.expert_id,
                subscription_id=record.subscription_id,
                revenue_period_start=record.revenue_period_start,
                revenue_period_end=record.revenue_period_end,
                gross_revenue=record.gross_revenue,
                platform_fee_percentage=record.platform_fee_percentage,
                platform_fee_amount=record.gross_revenue * record.platform_fee_percentage,
                payment_processor_fee=record.payment_processor_fee,
                net_revenue=record.gross_revenue - (record.gross_revenue * record.platform_fee_percentage),
                final_payout_amount=record.final_payout_amount,
                payout_status=record.payout_status,
                payout_method=record.payout_method,
                payout_reference=record.payout_reference,
                tax_withholding_amount=record.tax_withholding_amount,
                tax_form_required=record.tax_form_required,
                calculated_at=record.calculated_at,
                scheduled_payout_date=record.scheduled_payout_date,
                actual_payout_date=record.actual_payout_date,
                subscriber_count=record.subscriber_count,
                transaction_count=record.transaction_count
            )
            for record in revenue_records
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get revenue sharing records", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve revenue sharing records"
        )

@router.post("/payouts", response_model=PayoutResponse)
async def request_payout(
    payout_request: PayoutRequest,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Request payout for expert (expert owner only)
    """
    try:
        # Verify expert ownership
        expert_query = select(Expert).where(
            Expert.id == payout_request.expert_id,
            Expert.user_id == user_id,
            Expert.deleted_at.is_(None)
        )
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one_or_none()
        
        if not expert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert profile not found or access denied"
            )
        
        # Check available balance
        available_query = select(func.sum(RevenueSharing.final_payout_amount)).where(
            RevenueSharing.expert_id == payout_request.expert_id,
            RevenueSharing.payout_status == 'pending'
        )
        available_result = await session.execute(available_query)
        available_amount = available_result.scalar() or Decimal('0')
        
        if payout_request.amount > available_amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Requested amount exceeds available balance of ${available_amount}"
            )
        
        # Check minimum payout amount (business rule)
        min_payout = Decimal('25.00')  # $25 minimum
        if payout_request.amount < min_payout:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Minimum payout amount is ${min_payout}"
            )
        
        # Create payout transaction
        processing_fee = payout_request.amount * Decimal('0.025')  # 2.5% processing fee
        net_amount = payout_request.amount - processing_fee
        
        transaction = ExpertAnalystTransaction(
            transaction_type='payout',
            user_id=user_id,
            expert_id=payout_request.expert_id,
            amount=payout_request.amount,
            description=f"Payout request via {payout_request.payout_method}",
            debit_account=f"expert_{payout_request.expert_id}_earnings",
            credit_account=f"payout_{payout_request.payout_method}",
            status='pending',
            metadata={
                "payout_method": payout_request.payout_method,
                "payout_details": payout_request.payout_details,
                "processing_fee": str(processing_fee),
                "net_amount": str(net_amount),
                "notes": payout_request.notes
            },
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(transaction)
        await session.commit()
        await session.refresh(transaction)
        
        # TODO: Integrate with actual payout processor (Stripe, PayPal, etc.)
        
        logger.info("Payout requested", 
                   transaction_id=transaction.id, 
                   expert_id=payout_request.expert_id, 
                   amount=payout_request.amount)
        
        return PayoutResponse(
            id=transaction.id,
            expert_id=payout_request.expert_id,
            amount=payout_request.amount,
            payout_method=payout_request.payout_method,
            status='pending',
            reference_id=str(transaction.id),
            processing_fee=processing_fee,
            net_amount=net_amount,
            requested_at=transaction.created_at,
            processed_at=None,
            expected_arrival=date.today() + timedelta(days=3)  # 3 business days
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to process payout request", error=str(e))
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process payout request"
        )

@router.get("/payouts/{expert_id}", response_model=List[PayoutResponse])
async def get_expert_payouts(
    expert_id: UUID,
    limit: int = Query(20, ge=1, le=100),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert's payout history (expert owner only)
    """
    try:
        # Verify expert ownership
        expert_query = select(Expert).where(
            Expert.id == expert_id,
            Expert.user_id == user_id,
            Expert.deleted_at.is_(None)
        )
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one_or_none()
        
        if not expert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert profile not found or access denied"
            )
        
        # Get payout transactions
        query = select(ExpertAnalystTransaction).where(
            ExpertAnalystTransaction.expert_id == expert_id,
            ExpertAnalystTransaction.transaction_type == 'payout',
            ExpertAnalystTransaction.deleted_at.is_(None)
        ).order_by(ExpertAnalystTransaction.created_at.desc()).limit(limit)
        
        result = await session.execute(query)
        transactions = result.scalars().all()
        
        payouts = []
        for tx in transactions:
            metadata = tx.metadata or {}
            payouts.append(PayoutResponse(
                id=tx.id,
                expert_id=tx.expert_id,
                amount=tx.amount,
                payout_method=metadata.get('payout_method', 'unknown'),
                status=tx.status,
                reference_id=tx.external_transaction_id or str(tx.id),
                processing_fee=Decimal(metadata.get('processing_fee', '0')),
                net_amount=Decimal(metadata.get('net_amount', str(tx.amount))),
                requested_at=tx.created_at,
                processed_at=tx.updated_at if tx.status == 'completed' else None,
                expected_arrival=None  # Would be calculated based on payout method
            ))
        
        return payouts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get expert payouts", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payout history"
        )

@router.get("/reports/{report_type}", response_model=RevenueReportResponse)
async def generate_revenue_report(
    report_type: str = Query(..., regex="^(expert_summary|platform_summary|monthly_breakdown)$"),
    start_date: date = Query(...),
    end_date: date = Query(...),
    expert_id: Optional[UUID] = Query(None),
    admin_user_id: UUID = Depends(require_admin_permission),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Generate revenue reports (admin only)
    """
    try:
        # Validate date range
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if report_type == "expert_summary" and expert_id:
            # Single expert revenue summary
            query = select(RevenueSharing).where(
                RevenueSharing.expert_id == expert_id,
                RevenueSharing.revenue_period_start >= start_date,
                RevenueSharing.revenue_period_end <= end_date
            ).order_by(RevenueSharing.revenue_period_start)
            
            result = await session.execute(query)
            revenue_records = result.scalars().all()
            
            total_revenue = sum(record.gross_revenue for record in revenue_records)
            total_payouts = sum(record.final_payout_amount for record in revenue_records)
            platform_fees = sum(record.gross_revenue * record.platform_fee_percentage for record in revenue_records)
            
            expert_breakdown = [{
                "expert_id": str(expert_id),
                "total_revenue": float(total_revenue),
                "total_payouts": float(total_payouts),
                "platform_fees": float(platform_fees)
            }]
            
        elif report_type == "platform_summary":
            # Platform-wide revenue summary
            query = select(RevenueSharing).where(
                RevenueSharing.revenue_period_start >= start_date,
                RevenueSharing.revenue_period_end <= end_date
            )
            
            result = await session.execute(query)
            revenue_records = result.scalars().all()
            
            total_revenue = sum(record.gross_revenue for record in revenue_records)
            total_payouts = sum(record.final_payout_amount for record in revenue_records)
            platform_fees = sum(record.gross_revenue * record.platform_fee_percentage for record in revenue_records)
            
            # Group by expert for breakdown
            expert_totals = {}
            for record in revenue_records:
                expert_id_str = str(record.expert_id)
                if expert_id_str not in expert_totals:
                    expert_totals[expert_id_str] = {
                        "expert_id": expert_id_str,
                        "total_revenue": 0,
                        "total_payouts": 0,
                        "platform_fees": 0
                    }
                expert_totals[expert_id_str]["total_revenue"] += float(record.gross_revenue)
                expert_totals[expert_id_str]["total_payouts"] += float(record.final_payout_amount)
                expert_totals[expert_id_str]["platform_fees"] += float(record.gross_revenue * record.platform_fee_percentage)
            
            expert_breakdown = list(expert_totals.values())
            
        else:
            # Default to platform summary
            total_revenue = Decimal('0')
            total_payouts = Decimal('0')
            platform_fees = Decimal('0')
            expert_breakdown = []
        
        return RevenueReportResponse(
            report_id=UUID('00000000-0000-0000-0000-000000000001'),  # Placeholder
            report_type=report_type,
            period_start=start_date,
            period_end=end_date,
            total_revenue=total_revenue,
            total_payouts=total_payouts,
            platform_fees=platform_fees,
            net_profit=total_revenue - total_payouts,
            expert_revenue_breakdown=expert_breakdown,
            revenue_trend=[],  # Would be calculated from historical data
            generated_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to generate revenue report", error=str(e), report_type=report_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate revenue report"
        )