"""
Community Features API Endpoints
=================================

FastAPI endpoints for reviews, ratings, notifications, and social features.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_user_optional, get_current_expert,
    get_expert_by_id, get_read_database_session, get_database_session_dep,
    get_pagination_params, validate_user_subscription
)
from app.database.models import Expert, ExpertReview, ExpertNotification, Subscription
from app.schemas.community import (
    ExpertReviewCreate, ExpertReviewUpdate, ExpertReviewResponse,
    NotificationResponse, FollowResponse
)
from app.schemas.common import BaseResponse, StatusResponse, PaginatedResponse

logger = structlog.get_logger()

router = APIRouter()

# Expert Reviews
@router.post("/reviews", response_model=BaseResponse, status_code=status.HTTP_201_CREATED)
async def create_expert_review(
    review_data: ExpertReviewCreate,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Create review for expert (requires active subscription)
    """
    try:
        # Check if user has active subscription to this expert
        subscription = await validate_user_subscription(review_data.expert_id, user_id, session)
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Active subscription required to review expert"
            )
        
        # Check if user has already reviewed this expert
        existing_query = select(ExpertReview).where(
            ExpertReview.user_id == user_id,
            ExpertReview.expert_id == review_data.expert_id,
            ExpertReview.deleted_at.is_(None)
        )
        existing_result = await session.execute(existing_query)
        existing_review = existing_result.scalar_one_or_none()
        
        if existing_review:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="You have already reviewed this expert"
            )
        
        # Create review
        review = ExpertReview(
            user_id=user_id,
            expert_id=review_data.expert_id,
            subscription_id=subscription.id,
            rating=review_data.rating,
            review_title=review_data.review_title,
            review_text=review_data.review_text,
            analysis_quality_rating=review_data.analysis_quality_rating,
            communication_rating=review_data.communication_rating,
            value_for_money_rating=review_data.value_for_money_rating,
            timeliness_rating=review_data.timeliness_rating,
            is_verified=True,  # Verified because requires subscription
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(review)
        
        # Update expert's review stats
        expert_query = select(Expert).where(Expert.id == review_data.expert_id)
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one()
        
        expert.total_reviews += 1
        
        # Recalculate average rating
        avg_rating_query = select(func.avg(ExpertReview.rating)).where(
            ExpertReview.expert_id == review_data.expert_id,
            ExpertReview.status == 'active',
            ExpertReview.deleted_at.is_(None)
        )
        avg_rating_result = await session.execute(avg_rating_query)
        new_avg_rating = avg_rating_result.scalar()
        expert.average_rating = new_avg_rating
        
        await session.commit()
        
        logger.info("Expert review created", review_id=review.id, expert_id=review_data.expert_id, user_id=user_id)
        
        return BaseResponse(message="Review submitted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create expert review", error=str(e), expert_id=review_data.expert_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit review"
        )

@router.get("/experts/{expert_id}/reviews", response_model=PaginatedResponse[ExpertReviewResponse])
async def get_expert_reviews(
    expert_id: UUID,
    pagination=Depends(get_pagination_params),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get reviews for expert with pagination
    """
    try:
        # Base query for active reviews
        base_query = select(ExpertReview).where(
            ExpertReview.expert_id == expert_id,
            ExpertReview.status == 'active',
            ExpertReview.deleted_at.is_(None)
        )
        
        # Get total count
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering (featured first, then by date)
        query = base_query.order_by(
            ExpertReview.is_featured.desc(),
            ExpertReview.created_at.desc()
        ).offset(pagination.offset).limit(pagination.limit)
        
        result = await session.execute(query)
        reviews = result.scalars().all()
        
        # Convert to response format
        review_responses = []
        for review in reviews:
            review_responses.append(ExpertReviewResponse(
                id=review.id,
                user_id=review.user_id,
                expert_id=review.expert_id,
                subscription_id=review.subscription_id,
                rating=review.rating,
                review_title=review.review_title,
                review_text=review.review_text,
                analysis_quality_rating=review.analysis_quality_rating,
                communication_rating=review.communication_rating,
                value_for_money_rating=review.value_for_money_rating,
                timeliness_rating=review.timeliness_rating,
                is_verified=review.is_verified,
                is_featured=review.is_featured,
                status=review.status,
                helpful_votes=review.helpful_votes,
                total_votes=review.total_votes,
                user_name="Anonymous User",  # TODO: Get user name from users table
                user_avatar_url=None,  # TODO: Get user avatar from users table
                created_at=review.created_at,
                updated_at=review.updated_at
            ))
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return PaginatedResponse(
            data=review_responses,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get expert reviews", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve reviews"
        )

@router.put("/reviews/{review_id}", response_model=StatusResponse)
async def update_expert_review(
    review_id: UUID,
    updates: ExpertReviewUpdate,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update user's expert review
    """
    try:
        # Get review and verify ownership
        review_query = select(ExpertReview).where(
            ExpertReview.id == review_id,
            ExpertReview.user_id == user_id,
            ExpertReview.deleted_at.is_(None)
        )
        review_result = await session.execute(review_query)
        review = review_result.scalar_one_or_none()
        
        if not review:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Review not found or access denied"
            )
        
        # Apply updates
        update_data = updates.dict(exclude_unset=True)
        old_rating = review.rating
        
        for field, value in update_data.items():
            setattr(review, field, value)
        
        review.updated_by = user_id
        review.updated_at = datetime.now()
        
        # If rating changed, update expert's average rating
        if 'rating' in update_data and update_data['rating'] != old_rating:
            avg_rating_query = select(func.avg(ExpertReview.rating)).where(
                ExpertReview.expert_id == review.expert_id,
                ExpertReview.status == 'active',
                ExpertReview.deleted_at.is_(None)
            )
            avg_rating_result = await session.execute(avg_rating_query)
            new_avg_rating = avg_rating_result.scalar()
            
            expert_query = select(Expert).where(Expert.id == review.expert_id)
            expert_result = await session.execute(expert_query)
            expert = expert_result.scalar_one()
            expert.average_rating = new_avg_rating
        
        await session.commit()
        
        logger.info("Expert review updated", review_id=review_id, user_id=user_id)
        
        return StatusResponse(status="updated", message="Review updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update expert review", error=str(e), review_id=review_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update review"
        )

# Notifications
@router.get("/notifications", response_model=PaginatedResponse[NotificationResponse])
async def get_user_notifications(
    pagination=Depends(get_pagination_params),
    unread_only: bool = Query(False, description="Show only unread notifications"),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get user's notifications with pagination
    """
    try:
        # Base query - get notifications where user is the recipient
        base_query = select(ExpertNotification).where(
            ExpertNotification.user_id == user_id
        )
        
        # Apply unread filter
        if unread_only:
            base_query = base_query.where(ExpertNotification.opened_at.is_(None))
        
        # Get total count
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = base_query.order_by(ExpertNotification.created_at.desc()).offset(pagination.offset).limit(pagination.limit)
        
        result = await session.execute(query)
        notifications = result.scalars().all()
        
        # Convert to response format
        notification_responses = []
        for notification in notifications:
            notification_responses.append(NotificationResponse(
                id=notification.id,
                expert_id=notification.expert_id,
                user_id=notification.user_id,
                notification_type=notification.notification_type,
                title=notification.title,
                message=notification.message,
                send_email=notification.send_email,
                send_push=notification.send_push,
                send_sms=notification.send_sms,
                send_in_app=notification.send_in_app,
                status=notification.status,
                sent_at=notification.sent_at,
                delivered_at=notification.delivered_at,
                opened_at=notification.opened_at,
                clicked_at=notification.clicked_at,
                related_pick_id=notification.related_pick_id,
                related_subscription_id=notification.related_subscription_id,
                metadata=notification.metadata or {},
                created_at=notification.created_at
            ))
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return PaginatedResponse(
            data=notification_responses,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get user notifications", error=str(e), user_id=user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications"
        )

@router.put("/notifications/{notification_id}/read", response_model=StatusResponse)
async def mark_notification_read(
    notification_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Mark notification as read
    """
    try:
        # Get notification and verify ownership
        notification_query = select(ExpertNotification).where(
            ExpertNotification.id == notification_id,
            ExpertNotification.user_id == user_id
        )
        notification_result = await session.execute(notification_query)
        notification = notification_result.scalar_one_or_none()
        
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        # Mark as read
        if not notification.opened_at:
            notification.opened_at = datetime.now()
            await session.commit()
        
        return StatusResponse(status="read", message="Notification marked as read")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to mark notification as read", error=str(e), notification_id=notification_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification"
        )

# Expert Following/Social Features (placeholder implementation)
@router.post("/experts/{expert_id}/follow", response_model=FollowResponse)
async def follow_expert(
    expert_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Follow expert (placeholder - would need follows table)
    """
    try:
        # Verify expert exists
        expert = await get_expert_by_id(expert_id, session)
        
        # TODO: Implement follows table and relationship
        # For now, return a success response
        
        logger.info("Expert followed", expert_id=expert_id, user_id=user_id)
        
        return FollowResponse(
            expert_id=expert_id,
            user_id=user_id,
            followed_at=datetime.now(),
            expert_name=expert.expert_name,
            expert_slug=expert.slug,
            expert_avatar_url=expert.avatar_url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to follow expert", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to follow expert"
        )

@router.delete("/experts/{expert_id}/follow", response_model=StatusResponse)
async def unfollow_expert(
    expert_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Unfollow expert (placeholder - would need follows table)
    """
    try:
        # TODO: Implement unfollow logic
        
        logger.info("Expert unfollowed", expert_id=expert_id, user_id=user_id)
        
        return StatusResponse(status="unfollowed", message="Expert unfollowed successfully")
        
    except Exception as e:
        logger.error("Failed to unfollow expert", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unfollow expert"
        )