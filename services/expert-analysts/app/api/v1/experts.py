"""
Expert Management API Endpoints
===============================

FastAPI endpoints for expert registration, profile management, and discovery.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_expert, get_expert_by_id, 
    get_expert_by_slug, get_read_database_session, get_database_session_dep,
    get_pagination_params, get_expert_filters, require_admin_permission
)
from app.database.models import Expert, SubscriptionTier, Subscription, ExpertReview
from app.schemas.experts import (
    ExpertCreate, ExpertUpdate, ExpertVerificationUpdate, ExpertStatusUpdate,
    ExpertResponse, ExpertPublicResponse, ExpertListResponse, ExpertListItem,
    ExpertProfileResponse, ExpertDashboardResponse, ExpertDashboardStats,
    ExpertSearchParams, ExpertDiscoveryResponse, ExpertOnboardingData,
    ExpertOnboardingResponse
)
from app.schemas.common import BaseResponse, StatusResponse

logger = structlog.get_logger()

router = APIRouter()

# Expert Discovery and Search
@router.get("/", response_model=ExpertListResponse)
async def get_experts(
    pagination=Depends(get_pagination_params),
    filters=Depends(get_expert_filters),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get list of experts with filtering and pagination
    """
    try:
        # Build base query
        query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True
        )
        
        # Apply filters
        if filters.specialty:
            query = query.where(Expert.specialty == filters.specialty)
        if filters.verification_status:
            query = query.where(Expert.verification_status == filters.verification_status)
        if filters.min_rating:
            query = query.where(Expert.average_rating >= filters.min_rating)
        if filters.is_featured is not None:
            query = query.where(Expert.is_featured == filters.is_featured)
        
        # Get total count for pagination
        count_query = select(func.count(Expert.id)).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = query.order_by(Expert.average_rating.desc()).offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await session.execute(query)
        experts = result.scalars().all()
        
        # Convert to list items
        expert_items = [
            ExpertListItem(
                id=expert.id,
                expert_name=expert.expert_name,
                slug=expert.slug,
                specialty=expert.specialty,
                tagline=expert.tagline,
                avatar_url=expert.avatar_url,
                verification_status=expert.verification_status,
                is_featured=expert.is_featured,
                win_rate=expert.win_rate,
                average_rating=expert.average_rating,
                total_subscribers=expert.total_subscribers,
                total_reviews=expert.total_reviews
            )
            for expert in experts
        ]
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return ExpertListResponse(
            data=expert_items,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get experts list", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve experts"
        )

@router.get("/discovery", response_model=ExpertDiscoveryResponse)
async def get_expert_discovery(
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get curated expert discovery data with featured, top performers, and trending experts
    """
    try:
        # Featured experts
        featured_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.is_featured == True,
            Expert.verification_status == 'verified'
        ).order_by(Expert.average_rating.desc()).limit(6)
        
        featured_result = await session.execute(featured_query)
        featured_experts = featured_result.scalars().all()
        
        # Top performers (by win rate and subscriber count)
        top_performers_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified',
            Expert.total_picks >= 10  # Minimum picks for reliable stats
        ).order_by(
            Expert.win_rate.desc(),
            Expert.total_subscribers.desc()
        ).limit(6)
        
        top_performers_result = await session.execute(top_performers_query)
        top_performers = top_performers_result.scalars().all()
        
        # Trending experts (recent activity and growth)
        # For now, use recent creation and subscriber growth as proxy
        trending_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified'
        ).order_by(Expert.created_at.desc()).limit(6)
        
        trending_result = await session.execute(trending_query)
        trending_experts = trending_result.scalars().all()
        
        # All experts for main feed
        all_experts_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified'
        ).order_by(Expert.average_rating.desc()).limit(12)
        
        all_experts_result = await session.execute(all_experts_query)
        all_experts = all_experts_result.scalars().all()
        
        # Get categories (unique specialties)
        categories_query = select(Expert.specialty, func.count(Expert.id).label('count')).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified'
        ).group_by(Expert.specialty)
        
        categories_result = await session.execute(categories_query)
        categories = [
            {"name": row.specialty, "count": row.count}
            for row in categories_result.fetchall()
        ]
        
        # Convert to public response format
        def to_public_response(expert: Expert) -> ExpertPublicResponse:
            return ExpertPublicResponse(
                id=expert.id,
                expert_name=expert.expert_name,
                slug=expert.slug,
                bio=expert.bio,
                specialty=expert.specialty,
                tagline=expert.tagline,
                years_experience=expert.years_experience,
                certifications=expert.certifications or [],
                achievements=expert.achievements or [],
                verification_status=expert.verification_status,
                verification_date=expert.verification_date,
                is_featured=expert.is_featured,
                is_premium=expert.is_premium,
                avatar_url=expert.avatar_url,
                banner_url=expert.banner_url,
                social_links=expert.social_links or {},
                total_picks=expert.total_picks,
                winning_picks=expert.winning_picks,
                win_rate=expert.win_rate,
                average_roi=expert.average_roi,
                total_subscribers=expert.total_subscribers,
                average_rating=expert.average_rating,
                total_reviews=expert.total_reviews,
                analysis_focus=expert.analysis_focus or [],
                betting_style=expert.betting_style,
                risk_tolerance=expert.risk_tolerance,
                created_at=expert.created_at,
                updated_at=expert.updated_at
            )
        
        return ExpertDiscoveryResponse(
            experts=[to_public_response(expert) for expert in all_experts],
            featured_experts=[to_public_response(expert) for expert in featured_experts],
            top_performers=[to_public_response(expert) for expert in top_performers],
            trending_experts=[to_public_response(expert) for expert in trending_experts],
            categories=categories
        )
        
    except Exception as e:
        logger.error("Failed to get expert discovery data", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve expert discovery data"
        )

@router.get("/search", response_model=ExpertListResponse)
async def search_experts(
    query: Optional[str] = Query(None, description="Search query"),
    specialty: Optional[str] = Query(None, description="Expert specialty"),
    min_rating: Optional[float] = Query(None, ge=0.0, le=5.0),
    min_win_rate: Optional[float] = Query(None, ge=0.0, le=1.0),
    is_featured: Optional[bool] = Query(None),
    sort_by: str = Query("average_rating", regex="^(average_rating|win_rate|total_subscribers|created_at)$"),
    sort_direction: str = Query("desc", regex="^(asc|desc)$"),
    pagination=Depends(get_pagination_params),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Advanced expert search with full-text search and filtering
    """
    try:
        # Build base query
        base_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified'
        )
        
        # Apply text search
        if query:
            search_filter = or_(
                Expert.expert_name.ilike(f"%{query}%"),
                Expert.bio.ilike(f"%{query}%"),
                Expert.specialty.ilike(f"%{query}%"),
                Expert.tagline.ilike(f"%{query}%")
            )
            base_query = base_query.where(search_filter)
        
        # Apply filters
        if specialty:
            base_query = base_query.where(Expert.specialty == specialty)
        if min_rating is not None:
            base_query = base_query.where(Expert.average_rating >= min_rating)
        if min_win_rate is not None:
            base_query = base_query.where(Expert.win_rate >= min_win_rate)
        if is_featured is not None:
            base_query = base_query.where(Expert.is_featured == is_featured)
        
        # Get total count
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply sorting
        if sort_by == "average_rating":
            sort_column = Expert.average_rating
        elif sort_by == "win_rate":
            sort_column = Expert.win_rate
        elif sort_by == "total_subscribers":
            sort_column = Expert.total_subscribers
        elif sort_by == "created_at":
            sort_column = Expert.created_at
        else:
            sort_column = Expert.average_rating
        
        if sort_direction == "desc":
            sort_column = sort_column.desc()
        
        # Apply pagination and execute
        query = base_query.order_by(sort_column).offset(pagination.offset).limit(pagination.limit)
        result = await session.execute(query)
        experts = result.scalars().all()
        
        # Convert to list items
        expert_items = [
            ExpertListItem(
                id=expert.id,
                expert_name=expert.expert_name,
                slug=expert.slug,
                specialty=expert.specialty,
                tagline=expert.tagline,
                avatar_url=expert.avatar_url,
                verification_status=expert.verification_status,
                is_featured=expert.is_featured,
                win_rate=expert.win_rate,
                average_rating=expert.average_rating,
                total_subscribers=expert.total_subscribers,
                total_reviews=expert.total_reviews
            )
            for expert in experts
        ]
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return ExpertListResponse(
            data=expert_items,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to search experts", error=str(e), query=query)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search experts"
        )

# Expert Profile Management
@router.post("/", response_model=ExpertProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_expert_profile(
    expert_data: ExpertCreate,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Create new expert profile for authenticated user
    """
    try:
        # Check if user already has expert profile
        existing_query = select(Expert).where(
            Expert.user_id == user_id,
            Expert.deleted_at.is_(None)
        )
        existing_result = await session.execute(existing_query)
        existing_expert = existing_result.scalar_one_or_none()
        
        if existing_expert:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User already has an expert profile"
            )
        
        # Check slug uniqueness
        slug_query = select(Expert).where(Expert.slug == expert_data.slug)
        slug_result = await session.execute(slug_query)
        if slug_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Expert slug already exists"
            )
        
        # Create expert profile
        expert = Expert(
            user_id=user_id,
            expert_name=expert_data.expert_name,
            slug=expert_data.slug,
            bio=expert_data.bio,
            specialty=expert_data.specialty,
            tagline=expert_data.tagline,
            professional_background=expert_data.professional_background,
            years_experience=expert_data.years_experience,
            certifications=expert_data.certifications,
            achievements=expert_data.achievements,
            avatar_url=expert_data.avatar_url,
            banner_url=expert_data.banner_url,
            social_links=expert_data.social_links or {},
            analysis_focus=expert_data.analysis_focus,
            betting_style=expert_data.betting_style,
            risk_tolerance=expert_data.risk_tolerance,
            verification_status='pending',
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(expert)
        await session.commit()
        await session.refresh(expert)
        
        logger.info("Expert profile created", expert_id=expert.id, user_id=user_id)
        
        # Convert to response
        expert_response = ExpertResponse(
            id=expert.id,
            user_id=expert.user_id,
            expert_name=expert.expert_name,
            slug=expert.slug,
            bio=expert.bio,
            specialty=expert.specialty,
            tagline=expert.tagline,
            professional_background=expert.professional_background,
            years_experience=expert.years_experience,
            certifications=expert.certifications or [],
            achievements=expert.achievements or [],
            verification_status=expert.verification_status,
            verification_date=expert.verification_date,
            verification_notes=expert.verification_notes,
            is_active=expert.is_active,
            is_featured=expert.is_featured,
            is_premium=expert.is_premium,
            status=expert.status,
            avatar_url=expert.avatar_url,
            banner_url=expert.banner_url,
            social_links=expert.social_links or {},
            total_picks=expert.total_picks,
            winning_picks=expert.winning_picks,
            win_rate=expert.win_rate,
            average_roi=expert.average_roi,
            total_subscribers=expert.total_subscribers,
            active_subscribers=expert.active_subscribers,
            total_revenue=expert.total_revenue,
            monthly_revenue=expert.monthly_revenue,
            average_rating=expert.average_rating,
            total_reviews=expert.total_reviews,
            analysis_focus=expert.analysis_focus or [],
            betting_style=expert.betting_style,
            risk_tolerance=expert.risk_tolerance,
            created_at=expert.created_at,
            updated_at=expert.updated_at
        )
        
        return ExpertProfileResponse(expert=expert_response, message="Expert profile created successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create expert profile", error=str(e), user_id=user_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create expert profile"
        )

@router.get("/me", response_model=ExpertProfileResponse)
async def get_my_expert_profile(
    expert: Expert = Depends(get_current_expert)
):
    """
    Get current user's expert profile
    """
    expert_response = ExpertResponse(
        id=expert.id,
        user_id=expert.user_id,
        expert_name=expert.expert_name,
        slug=expert.slug,
        bio=expert.bio,
        specialty=expert.specialty,
        tagline=expert.tagline,
        professional_background=expert.professional_background,
        years_experience=expert.years_experience,
        certifications=expert.certifications or [],
        achievements=expert.achievements or [],
        verification_status=expert.verification_status,
        verification_date=expert.verification_date,
        verification_notes=expert.verification_notes,
        is_active=expert.is_active,
        is_featured=expert.is_featured,
        is_premium=expert.is_premium,
        status=expert.status,
        avatar_url=expert.avatar_url,
        banner_url=expert.banner_url,
        social_links=expert.social_links or {},
        total_picks=expert.total_picks,
        winning_picks=expert.winning_picks,
        win_rate=expert.win_rate,
        average_roi=expert.average_roi,
        total_subscribers=expert.total_subscribers,
        active_subscribers=expert.active_subscribers,
        total_revenue=expert.total_revenue,
        monthly_revenue=expert.monthly_revenue,
        average_rating=expert.average_rating,
        total_reviews=expert.total_reviews,
        analysis_focus=expert.analysis_focus or [],
        betting_style=expert.betting_style,
        risk_tolerance=expert.risk_tolerance,
        created_at=expert.created_at,
        updated_at=expert.updated_at
    )
    
    return ExpertProfileResponse(expert=expert_response)

@router.put("/me", response_model=ExpertProfileResponse)
async def update_my_expert_profile(
    updates: ExpertUpdate,
    expert: Expert = Depends(get_current_expert),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update current user's expert profile
    """
    try:
        # Apply updates
        update_data = updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(expert, field, value)
        
        expert.updated_by = user_id
        expert.updated_at = datetime.now()
        
        await session.commit()
        await session.refresh(expert)
        
        logger.info("Expert profile updated", expert_id=expert.id, user_id=user_id)
        
        expert_response = ExpertResponse(
            id=expert.id,
            user_id=expert.user_id,
            expert_name=expert.expert_name,
            slug=expert.slug,
            bio=expert.bio,
            specialty=expert.specialty,
            tagline=expert.tagline,
            professional_background=expert.professional_background,
            years_experience=expert.years_experience,
            certifications=expert.certifications or [],
            achievements=expert.achievements or [],
            verification_status=expert.verification_status,
            verification_date=expert.verification_date,
            verification_notes=expert.verification_notes,
            is_active=expert.is_active,
            is_featured=expert.is_featured,
            is_premium=expert.is_premium,
            status=expert.status,
            avatar_url=expert.avatar_url,
            banner_url=expert.banner_url,
            social_links=expert.social_links or {},
            total_picks=expert.total_picks,
            winning_picks=expert.winning_picks,
            win_rate=expert.win_rate,
            average_roi=expert.average_roi,
            total_subscribers=expert.total_subscribers,
            active_subscribers=expert.active_subscribers,
            total_revenue=expert.total_revenue,
            monthly_revenue=expert.monthly_revenue,
            average_rating=expert.average_rating,
            total_reviews=expert.total_reviews,
            analysis_focus=expert.analysis_focus or [],
            betting_style=expert.betting_style,
            risk_tolerance=expert.risk_tolerance,
            created_at=expert.created_at,
            updated_at=expert.updated_at
        )
        
        return ExpertProfileResponse(expert=expert_response, message="Expert profile updated successfully")
        
    except Exception as e:
        logger.error("Failed to update expert profile", error=str(e), expert_id=expert.id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update expert profile"
        )

@router.get("/{expert_id}", response_model=ExpertPublicResponse)
async def get_expert_by_id_endpoint(
    expert: Expert = Depends(get_expert_by_id)
):
    """
    Get expert profile by ID (public view)
    """
    return ExpertPublicResponse(
        id=expert.id,
        expert_name=expert.expert_name,
        slug=expert.slug,
        bio=expert.bio,
        specialty=expert.specialty,
        tagline=expert.tagline,
        years_experience=expert.years_experience,
        certifications=expert.certifications or [],
        achievements=expert.achievements or [],
        verification_status=expert.verification_status,
        verification_date=expert.verification_date,
        is_featured=expert.is_featured,
        is_premium=expert.is_premium,
        avatar_url=expert.avatar_url,
        banner_url=expert.banner_url,
        social_links=expert.social_links or {},
        total_picks=expert.total_picks,
        winning_picks=expert.winning_picks,
        win_rate=expert.win_rate,
        average_roi=expert.average_roi,
        total_subscribers=expert.total_subscribers,
        average_rating=expert.average_rating,
        total_reviews=expert.total_reviews,
        analysis_focus=expert.analysis_focus or [],
        betting_style=expert.betting_style,
        risk_tolerance=expert.risk_tolerance,
        created_at=expert.created_at,
        updated_at=expert.updated_at
    )

@router.get("/slug/{slug}", response_model=ExpertPublicResponse)
async def get_expert_by_slug_endpoint(
    expert: Expert = Depends(get_expert_by_slug)
):
    """
    Get expert profile by slug (public view)
    """
    return ExpertPublicResponse(
        id=expert.id,
        expert_name=expert.expert_name,
        slug=expert.slug,
        bio=expert.bio,
        specialty=expert.specialty,
        tagline=expert.tagline,
        years_experience=expert.years_experience,
        certifications=expert.certifications or [],
        achievements=expert.achievements or [],
        verification_status=expert.verification_status,
        verification_date=expert.verification_date,
        is_featured=expert.is_featured,
        is_premium=expert.is_premium,
        avatar_url=expert.avatar_url,
        banner_url=expert.banner_url,
        social_links=expert.social_links or {},
        total_picks=expert.total_picks,
        winning_picks=expert.winning_picks,
        win_rate=expert.win_rate,
        average_roi=expert.average_roi,
        total_subscribers=expert.total_subscribers,
        average_rating=expert.average_rating,
        total_reviews=expert.total_reviews,
        analysis_focus=expert.analysis_focus or [],
        betting_style=expert.betting_style,
        risk_tolerance=expert.risk_tolerance,
        created_at=expert.created_at,
        updated_at=expert.updated_at
    )

# Admin endpoints
@router.put("/{expert_id}/verification", response_model=StatusResponse)
async def update_expert_verification(
    expert_id: UUID,
    verification_data: ExpertVerificationUpdate,
    admin_user_id: UUID = Depends(require_admin_permission),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update expert verification status (admin only)
    """
    try:
        expert = await get_expert_by_id(expert_id, session)
        
        # Update verification fields
        expert.verification_status = verification_data.verification_status
        expert.verification_notes = verification_data.verification_notes
        if verification_data.verification_status == 'verified':
            expert.verification_date = datetime.now()
        expert.verified_by = admin_user_id
        
        if verification_data.is_featured is not None:
            expert.is_featured = verification_data.is_featured
        if verification_data.is_premium is not None:
            expert.is_premium = verification_data.is_premium
        
        expert.updated_by = admin_user_id
        expert.updated_at = datetime.now()
        
        await session.commit()
        
        logger.info("Expert verification updated", 
                   expert_id=expert_id, 
                   status=verification_data.verification_status, 
                   admin_id=admin_user_id)
        
        return StatusResponse(status="verified", message="Expert verification updated successfully")
        
    except Exception as e:
        logger.error("Failed to update expert verification", error=str(e), expert_id=expert_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update expert verification"
        )