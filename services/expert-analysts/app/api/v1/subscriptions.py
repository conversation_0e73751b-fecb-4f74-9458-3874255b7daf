"""
Subscription Management API Endpoints
====================================

FastAPI endpoints for subscription tiers, user subscriptions, and billing management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date, timedelta
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_expert, get_expert_by_id,
    get_read_database_session, get_database_session_dep,
    get_pagination_params, validate_user_subscription, get_platform_settings
)
from app.database.models import (
    Expert, SubscriptionTier, Subscription, ExpertAnalystTransaction
)
from app.schemas.subscriptions import (
    SubscriptionTierCreate, SubscriptionTierUpdate, SubscriptionTierResponse,
    SubscriptionCreate, SubscriptionUpdate, SubscriptionResponse,
    SubscriptionListResponse, SubscriptionSummary, SubscriptionUpgrade,
    SubscriptionCancellation, SubscriptionUsageResponse, SubscriptionBillingResponse
)
from app.schemas.common import BaseResponse, StatusResponse

logger = structlog.get_logger()

router = APIRouter()

# Subscription Tiers Management
@router.post("/tiers", response_model=BaseResponse, status_code=status.HTTP_201_CREATED)
async def create_subscription_tier(
    tier_data: SubscriptionTierCreate,
    expert: Expert = Depends(get_current_expert),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Create new subscription tier for expert
    """
    try:
        # Check tier limit per expert
        existing_tiers_query = select(func.count(SubscriptionTier.id)).where(
            SubscriptionTier.expert_id == expert.id,
            SubscriptionTier.deleted_at.is_(None)
        )
        tier_count_result = await session.execute(existing_tiers_query)
        tier_count = tier_count_result.scalar()
        
        if tier_count >= 5:  # Business rule: max 5 tiers per expert
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum number of subscription tiers reached"
            )
        
        # Check slug uniqueness for this expert
        slug_query = select(SubscriptionTier).where(
            SubscriptionTier.expert_id == expert.id,
            SubscriptionTier.tier_slug == tier_data.tier_slug,
            SubscriptionTier.deleted_at.is_(None)
        )
        slug_result = await session.execute(slug_query)
        if slug_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Tier slug already exists for this expert"
            )
        
        # Create subscription tier
        tier = SubscriptionTier(
            expert_id=expert.id,
            tier_name=tier_data.tier_name,
            tier_slug=tier_data.tier_slug,
            description=tier_data.description,
            price_monthly=tier_data.price_monthly,
            price_yearly=tier_data.price_yearly,
            discount_yearly_percentage=tier_data.discount_yearly_percentage,
            free_trial_days=tier_data.free_trial_days,
            max_picks_per_month=tier_data.max_picks_per_month,
            access_level=tier_data.access_level,
            features_included=tier_data.features_included,
            picks_access_level=tier_data.picks_access_level,
            analysis_depth=tier_data.analysis_depth,
            allows_direct_messages=tier_data.allows_direct_messages,
            allows_live_chat=tier_data.allows_live_chat,
            allows_priority_support=tier_data.allows_priority_support,
            subscriber_limit=tier_data.subscriber_limit,
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(tier)
        await session.commit()
        await session.refresh(tier)
        
        logger.info("Subscription tier created", tier_id=tier.id, expert_id=expert.id)
        
        return BaseResponse(message="Subscription tier created successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create subscription tier", error=str(e), expert_id=expert.id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create subscription tier"
        )

@router.get("/experts/{expert_id}/tiers", response_model=List[SubscriptionTierResponse])
async def get_expert_subscription_tiers(
    expert_id: UUID,
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get all subscription tiers for an expert
    """
    try:
        query = select(SubscriptionTier).where(
            SubscriptionTier.expert_id == expert_id,
            SubscriptionTier.deleted_at.is_(None),
            SubscriptionTier.is_active == True
        ).order_by(SubscriptionTier.display_order, SubscriptionTier.price_monthly)
        
        result = await session.execute(query)
        tiers = result.scalars().all()
        
        return [
            SubscriptionTierResponse(
                id=tier.id,
                expert_id=tier.expert_id,
                tier_name=tier.tier_name,
                tier_slug=tier.tier_slug,
                description=tier.description,
                price_monthly=tier.price_monthly,
                price_yearly=tier.price_yearly,
                discount_yearly_percentage=tier.discount_yearly_percentage,
                free_trial_days=tier.free_trial_days,
                max_picks_per_month=tier.max_picks_per_month,
                access_level=tier.access_level,
                features_included=tier.features_included or [],
                picks_access_level=tier.picks_access_level,
                analysis_depth=tier.analysis_depth,
                allows_direct_messages=tier.allows_direct_messages,
                allows_live_chat=tier.allows_live_chat,
                allows_priority_support=tier.allows_priority_support,
                is_active=tier.is_active,
                is_featured=tier.is_featured,
                display_order=tier.display_order,
                subscriber_limit=tier.subscriber_limit,
                current_subscribers=tier.current_subscribers,
                created_at=tier.created_at,
                updated_at=tier.updated_at
            )
            for tier in tiers
        ]
        
    except Exception as e:
        logger.error("Failed to get expert subscription tiers", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subscription tiers"
        )

@router.put("/tiers/{tier_id}", response_model=StatusResponse)
async def update_subscription_tier(
    tier_id: UUID,
    updates: SubscriptionTierUpdate,
    expert: Expert = Depends(get_current_expert),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update subscription tier (expert only)
    """
    try:
        # Get tier and verify ownership
        tier_query = select(SubscriptionTier).where(
            SubscriptionTier.id == tier_id,
            SubscriptionTier.expert_id == expert.id,
            SubscriptionTier.deleted_at.is_(None)
        )
        tier_result = await session.execute(tier_query)
        tier = tier_result.scalar_one_or_none()
        
        if not tier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription tier not found"
            )
        
        # Apply updates
        update_data = updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tier, field, value)
        
        tier.updated_by = user_id
        tier.updated_at = datetime.now()
        
        await session.commit()
        
        logger.info("Subscription tier updated", tier_id=tier_id, expert_id=expert.id)
        
        return StatusResponse(status="updated", message="Subscription tier updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update subscription tier", error=str(e), tier_id=tier_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update subscription tier"
        )

# User Subscriptions Management
@router.post("/", response_model=BaseResponse, status_code=status.HTTP_201_CREATED)
async def create_subscription(
    subscription_data: SubscriptionCreate,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Create new subscription for user
    """
    try:
        # Check if user already has subscription to this expert
        existing_query = select(Subscription).where(
            Subscription.user_id == user_id,
            Subscription.expert_id == subscription_data.expert_id,
            Subscription.status.in_(['active', 'pending']),
            Subscription.deleted_at.is_(None)
        )
        existing_result = await session.execute(existing_query)
        existing_sub = existing_result.scalar_one_or_none()
        
        if existing_sub:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User already has active subscription to this expert"
            )
        
        # Get subscription tier and validate
        tier_query = select(SubscriptionTier).where(
            SubscriptionTier.id == subscription_data.tier_id,
            SubscriptionTier.expert_id == subscription_data.expert_id,
            SubscriptionTier.is_active == True,
            SubscriptionTier.deleted_at.is_(None)
        )
        tier_result = await session.execute(tier_query)
        tier = tier_result.scalar_one_or_none()
        
        if not tier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription tier not found or inactive"
            )
        
        # Check subscriber limit
        if tier.subscriber_limit and tier.current_subscribers >= tier.subscriber_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Subscription tier is at capacity"
            )
        
        # Calculate subscription period and amount
        if subscription_data.subscription_type == "monthly":
            amount = tier.price_monthly
            ends_at = datetime.now() + timedelta(days=30)
        elif subscription_data.subscription_type == "yearly":
            amount = tier.price_yearly or (tier.price_monthly * 12)
            ends_at = datetime.now() + timedelta(days=365)
        else:  # lifetime
            amount = tier.price_yearly or (tier.price_monthly * 12 * 10)  # 10 years as proxy for lifetime
            ends_at = datetime.now() + timedelta(days=365 * 10)
        
        # Handle free trial
        is_trial = tier.free_trial_days > 0
        trial_ends_at = None
        if is_trial:
            trial_ends_at = datetime.now() + timedelta(days=tier.free_trial_days)
            amount = Decimal('0')  # Free trial, no charge initially
        
        # Create subscription
        subscription = Subscription(
            user_id=user_id,
            expert_id=subscription_data.expert_id,
            tier_id=subscription_data.tier_id,
            subscription_type=subscription_data.subscription_type,
            status='pending',  # Will be updated after payment
            amount_paid=amount,
            billing_cycle=subscription_data.billing_cycle,
            starts_at=datetime.now(),
            ends_at=ends_at,
            auto_renew=subscription_data.auto_renew,
            is_trial=is_trial,
            trial_ends_at=trial_ends_at,
            external_subscription_id=subscription_data.payment_method_id,  # Placeholder for Stripe integration
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(subscription)
        
        # Update tier subscriber count
        tier.current_subscribers += 1
        
        await session.commit()
        await session.refresh(subscription)
        
        logger.info("Subscription created", 
                   subscription_id=subscription.id, 
                   user_id=user_id, 
                   expert_id=subscription_data.expert_id)
        
        # TODO: Integrate with payment processor (Stripe) here
        # For now, mark as active if free trial
        if is_trial:
            subscription.status = 'active'
            await session.commit()
        
        return BaseResponse(message="Subscription created successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create subscription", error=str(e), user_id=user_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create subscription"
        )

@router.get("/", response_model=SubscriptionListResponse)
async def get_user_subscriptions(
    user_id: UUID = Depends(get_current_user_id),
    pagination=Depends(get_pagination_params),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get user's subscriptions with pagination
    """
    try:
        # Base query
        base_query = select(Subscription).options(
            selectinload(Subscription.expert),
            selectinload(Subscription.tier)
        ).where(
            Subscription.user_id == user_id,
            Subscription.deleted_at.is_(None)
        )
        
        # Get total count
        count_query = select(func.count()).select_from(
            select(Subscription.id).where(
                Subscription.user_id == user_id,
                Subscription.deleted_at.is_(None)
            )
        )
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = base_query.order_by(Subscription.created_at.desc()).offset(pagination.offset).limit(pagination.limit)
        result = await session.execute(query)
        subscriptions = result.scalars().all()
        
        # Convert to response format
        subscription_responses = []
        for sub in subscriptions:
            # Calculate computed properties
            days_remaining = None
            is_active = False
            if sub.ends_at > datetime.now() and sub.status == 'active':
                days_remaining = (sub.ends_at.date() - date.today()).days
                is_active = True
            
            subscription_responses.append(SubscriptionResponse(
                id=sub.id,
                user_id=sub.user_id,
                expert_id=sub.expert_id,
                tier_id=sub.tier_id,
                subscription_type=sub.subscription_type,
                status=sub.status,
                amount_paid=sub.amount_paid,
                currency=sub.currency,
                billing_cycle=sub.billing_cycle,
                external_subscription_id=sub.external_subscription_id,
                starts_at=sub.starts_at,
                ends_at=sub.ends_at,
                auto_renew=sub.auto_renew,
                is_trial=sub.is_trial,
                trial_ends_at=sub.trial_ends_at,
                cancelled_at=sub.cancelled_at,
                cancellation_reason=sub.cancellation_reason,
                picks_accessed=sub.picks_accessed,
                last_access_at=sub.last_access_at,
                expert_name=sub.expert.expert_name if sub.expert else None,
                tier_name=sub.tier.tier_name if sub.tier else None,
                days_remaining=days_remaining,
                is_active=is_active,
                created_at=sub.created_at,
                updated_at=sub.updated_at
            ))
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return SubscriptionListResponse(
            data=subscription_responses,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get user subscriptions", error=str(e), user_id=user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subscriptions"
        )

@router.get("/{subscription_id}", response_model=SubscriptionResponse)
async def get_subscription(
    subscription_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get specific subscription details
    """
    try:
        query = select(Subscription).options(
            selectinload(Subscription.expert),
            selectinload(Subscription.tier)
        ).where(
            Subscription.id == subscription_id,
            Subscription.user_id == user_id,
            Subscription.deleted_at.is_(None)
        )
        result = await session.execute(query)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        # Calculate computed properties
        days_remaining = None
        is_active = False
        if subscription.ends_at > datetime.now() and subscription.status == 'active':
            days_remaining = (subscription.ends_at.date() - date.today()).days
            is_active = True
        
        return SubscriptionResponse(
            id=subscription.id,
            user_id=subscription.user_id,
            expert_id=subscription.expert_id,
            tier_id=subscription.tier_id,
            subscription_type=subscription.subscription_type,
            status=subscription.status,
            amount_paid=subscription.amount_paid,
            currency=subscription.currency,
            billing_cycle=subscription.billing_cycle,
            external_subscription_id=subscription.external_subscription_id,
            starts_at=subscription.starts_at,
            ends_at=subscription.ends_at,
            auto_renew=subscription.auto_renew,
            is_trial=subscription.is_trial,
            trial_ends_at=subscription.trial_ends_at,
            cancelled_at=subscription.cancelled_at,
            cancellation_reason=subscription.cancellation_reason,
            picks_accessed=subscription.picks_accessed,
            last_access_at=subscription.last_access_at,
            expert_name=subscription.expert.expert_name if subscription.expert else None,
            tier_name=subscription.tier.tier_name if subscription.tier else None,
            days_remaining=days_remaining,
            is_active=is_active,
            created_at=subscription.created_at,
            updated_at=subscription.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get subscription", error=str(e), subscription_id=subscription_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subscription"
        )

@router.put("/{subscription_id}/cancel", response_model=StatusResponse)
async def cancel_subscription(
    subscription_id: UUID,
    cancellation_data: SubscriptionCancellation,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Cancel user subscription
    """
    try:
        # Get subscription and verify ownership
        query = select(Subscription).where(
            Subscription.id == subscription_id,
            Subscription.user_id == user_id,
            Subscription.deleted_at.is_(None)
        )
        result = await session.execute(query)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        if subscription.status in ['cancelled', 'expired']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Subscription is already cancelled or expired"
            )
        
        # Cancel subscription
        subscription.status = 'cancelled'
        subscription.cancelled_at = datetime.now()
        subscription.cancellation_reason = cancellation_data.cancellation_reason
        subscription.cancelled_by = user_id
        subscription.updated_by = user_id
        subscription.updated_at = datetime.now()
        subscription.auto_renew = False
        
        # TODO: Cancel with payment processor (Stripe)
        
        await session.commit()
        
        logger.info("Subscription cancelled", subscription_id=subscription_id, user_id=user_id)
        
        return StatusResponse(status="cancelled", message="Subscription cancelled successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel subscription", error=str(e), subscription_id=subscription_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel subscription"
        )

@router.get("/{subscription_id}/usage", response_model=SubscriptionUsageResponse)
async def get_subscription_usage(
    subscription_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get subscription usage tracking
    """
    try:
        # Get subscription and verify ownership
        query = select(Subscription).options(
            selectinload(Subscription.tier)
        ).where(
            Subscription.id == subscription_id,
            Subscription.user_id == user_id,
            Subscription.deleted_at.is_(None)
        )
        result = await session.execute(query)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        # Calculate billing period (current month)
        now = datetime.now()
        if subscription.billing_cycle == 'monthly':
            period_start = date(now.year, now.month, 1)
            if now.month == 12:
                period_end = date(now.year + 1, 1, 1) - timedelta(days=1)
            else:
                period_end = date(now.year, now.month + 1, 1) - timedelta(days=1)
        else:  # yearly
            period_start = date(now.year, 1, 1)
            period_end = date(now.year, 12, 31)
        
        # Calculate usage
        picks_remaining = None
        usage_percentage = None
        if subscription.tier and subscription.tier.max_picks_per_month:
            picks_remaining = max(0, subscription.tier.max_picks_per_month - subscription.picks_accessed)
            usage_percentage = (subscription.picks_accessed / subscription.tier.max_picks_per_month) * 100
        
        # TODO: Get detailed access history from pick_access_log table
        access_history = []
        
        return SubscriptionUsageResponse(
            subscription_id=subscription.id,
            billing_period_start=period_start,
            billing_period_end=period_end,
            picks_accessed=subscription.picks_accessed,
            picks_remaining=picks_remaining,
            usage_percentage=usage_percentage,
            last_access_date=subscription.last_access_at.date() if subscription.last_access_at else None,
            access_history=access_history
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get subscription usage", error=str(e), subscription_id=subscription_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subscription usage"
        )