"""
Expert Picks API Endpoints
===========================

FastAPI endpoints for expert picks, predictions, and content management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_user_optional, get_current_expert,
    get_expert_by_id, get_read_database_session, get_database_session_dep,
    get_pagination_params, validate_user_subscription, require_admin_permission
)
from app.database.models import Expert, ExpertPick, Subscription, PickAccessLog
from app.schemas.picks import (
    ExpertPickCreate, ExpertPickUpdate, PickOutcomeUpdate,
    ExpertPickResponse, ExpertPickPublicResponse, ExpertPickSummary,
    ExpertPickListResponse, PickAccessRequest, PickAccessResponse,
    PickSearchParams, PickFeedResponse
)
from app.schemas.common import BaseResponse, StatusResponse

logger = structlog.get_logger()

router = APIRouter()

# Pick Discovery and Feeds
@router.get("/", response_model=ExpertPickListResponse)
async def get_picks(
    expert_id: Optional[UUID] = Query(None, description="Filter by expert ID"),
    sport: Optional[str] = Query(None, description="Filter by sport"),
    pick_type: Optional[str] = Query(None, description="Filter by pick type"),
    access_tier: Optional[str] = Query(None, description="Filter by access tier"),
    status: str = Query("published", description="Filter by status"),
    pagination=Depends(get_pagination_params),
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get list of expert picks with filtering and pagination
    """
    try:
        # Build base query
        query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            ExpertPick.deleted_at.is_(None)
        )
        
        # Apply filters
        if expert_id:
            query = query.where(ExpertPick.expert_id == expert_id)
        if sport:
            query = query.where(ExpertPick.sport == sport)
        if pick_type:
            query = query.where(ExpertPick.pick_type == pick_type)
        if access_tier:
            query = query.where(ExpertPick.access_tier == access_tier)
        if status:
            query = query.where(ExpertPick.status == status)
        
        # Apply access control - show public picks or user's subscribed content
        if user_id:
            # If user is authenticated, show picks they have access to
            query = query.where(
                or_(
                    ExpertPick.is_public == True,
                    ExpertPick.access_tier == 'free'
                    # TODO: Add subscription-based access check
                )
            )
        else:
            # Anonymous users only see public/free picks
            query = query.where(
                or_(
                    ExpertPick.is_public == True,
                    ExpertPick.access_tier == 'free'
                )
            )
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = query.order_by(ExpertPick.publish_at.desc()).offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await session.execute(query)
        picks = result.scalars().all()
        
        # Convert to summary format
        pick_summaries = []
        for pick in picks:
            pick_summaries.append(ExpertPickSummary(
                id=pick.id,
                expert_id=pick.expert_id,
                title=pick.title,
                slug=pick.slug,
                sport=pick.sport,
                pick_type=pick.pick_type,
                confidence_level=pick.confidence_level,
                access_tier=pick.access_tier,
                outcome=pick.outcome,
                publish_at=pick.publish_at,
                event_date=pick.event_date,
                expert_name=pick.expert.expert_name if pick.expert else "",
                expert_slug=pick.expert.slug if pick.expert else "",
                expert_avatar_url=pick.expert.avatar_url if pick.expert else None
            ))
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return ExpertPickListResponse(
            data=pick_summaries,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get picks list", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve picks"
        )

@router.get("/feed", response_model=PickFeedResponse)
async def get_pick_feed(
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get curated pick feed with featured, recent, and trending picks
    """
    try:
        # Base filters for published, active picks
        base_filters = and_(
            ExpertPick.deleted_at.is_(None),
            ExpertPick.status == 'published',
            ExpertPick.publish_at <= datetime.now()
        )
        
        # Featured picks
        featured_query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            base_filters,
            ExpertPick.is_featured == True,
            or_(ExpertPick.is_public == True, ExpertPick.access_tier == 'free')
        ).order_by(ExpertPick.publish_at.desc()).limit(6)
        
        # Recent picks
        recent_query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            base_filters,
            or_(ExpertPick.is_public == True, ExpertPick.access_tier == 'free')
        ).order_by(ExpertPick.publish_at.desc()).limit(8)
        
        # Trending picks (by views/engagement)
        trending_query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            base_filters,
            or_(ExpertPick.is_public == True, ExpertPick.access_tier == 'free')
        ).order_by(ExpertPick.views_count.desc()).limit(6)
        
        # Upcoming events
        upcoming_query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            base_filters,
            ExpertPick.event_date > datetime.now(),
            or_(ExpertPick.is_public == True, ExpertPick.access_tier == 'free')
        ).order_by(ExpertPick.event_date.asc()).limit(6)
        
        # Execute queries
        featured_result = await session.execute(featured_query)
        featured_picks = featured_result.scalars().all()
        
        recent_result = await session.execute(recent_query)
        recent_picks = recent_result.scalars().all()
        
        trending_result = await session.execute(trending_query)
        trending_picks = trending_result.scalars().all()
        
        upcoming_result = await session.execute(upcoming_query)
        upcoming_picks = upcoming_result.scalars().all()
        
        # Convert to summary format
        def to_summary(pick):
            return ExpertPickSummary(
                id=pick.id,
                expert_id=pick.expert_id,
                title=pick.title,
                slug=pick.slug,
                sport=pick.sport,
                pick_type=pick.pick_type,
                confidence_level=pick.confidence_level,
                access_tier=pick.access_tier,
                outcome=pick.outcome,
                publish_at=pick.publish_at,
                event_date=pick.event_date,
                expert_name=pick.expert.expert_name if pick.expert else "",
                expert_slug=pick.expert.slug if pick.expert else "",
                expert_avatar_url=pick.expert.avatar_url if pick.expert else None
            )
        
        # Get user's expert picks if authenticated
        your_experts_picks = None
        if user_id:
            # TODO: Get picks from experts user is subscribed to
            your_experts_picks = []
        
        return PickFeedResponse(
            featured_picks=[to_summary(pick) for pick in featured_picks],
            recent_picks=[to_summary(pick) for pick in recent_picks],
            trending_picks=[to_summary(pick) for pick in trending_picks],
            upcoming_events=[to_summary(pick) for pick in upcoming_picks],
            your_experts_picks=your_experts_picks
        )
        
    except Exception as e:
        logger.error("Failed to get pick feed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve pick feed"
        )

# Pick Content Management (Expert Only)
@router.post("/", response_model=BaseResponse, status_code=status.HTTP_201_CREATED)
async def create_expert_pick(
    pick_data: ExpertPickCreate,
    expert: Expert = Depends(get_current_expert),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Create new expert pick
    """
    try:
        # Check slug uniqueness
        slug_query = select(ExpertPick).where(ExpertPick.slug == pick_data.slug)
        slug_result = await session.execute(slug_query)
        if slug_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Pick slug already exists"
            )
        
        # Create expert pick
        pick = ExpertPick(
            expert_id=expert.id,
            title=pick_data.title,
            slug=pick_data.slug,
            sport=pick_data.sport,
            league=pick_data.league,
            event_description=pick_data.event_description,
            event_date=pick_data.event_date,
            pick_type=pick_data.pick_type,
            pick_value=pick_data.pick_value,
            recommended_stake=pick_data.recommended_stake,
            odds_when_posted=pick_data.odds_when_posted,
            odds_format=pick_data.odds_format,
            implied_probability=pick_data.implied_probability,
            confidence_level=pick_data.confidence_level,
            analysis_text=pick_data.analysis_text,
            reasoning=pick_data.reasoning,
            key_factors=pick_data.key_factors,
            access_tier=pick_data.access_tier,
            required_tier_id=pick_data.required_tier_id,
            is_public=pick_data.is_public,
            is_featured=pick_data.is_featured,
            is_premium=pick_data.is_premium,
            publish_at=pick_data.publish_at or datetime.now(),
            expires_at=pick_data.expires_at,
            tags=pick_data.tags,
            categories=pick_data.categories,
            status='draft',  # Default to draft
            created_by=user_id,
            updated_by=user_id
        )
        
        session.add(pick)
        await session.commit()
        await session.refresh(pick)
        
        # Update expert's pick count
        expert.total_picks += 1
        await session.commit()
        
        logger.info("Expert pick created", pick_id=pick.id, expert_id=expert.id)
        
        return BaseResponse(message="Expert pick created successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create expert pick", error=str(e), expert_id=expert.id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create expert pick"
        )

@router.get("/my-picks", response_model=ExpertPickListResponse)
async def get_my_expert_picks(
    status: Optional[str] = Query(None, description="Filter by status"),
    pagination=Depends(get_pagination_params),
    expert: Expert = Depends(get_current_expert),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get current expert's picks
    """
    try:
        # Build query
        query = select(ExpertPick).where(
            ExpertPick.expert_id == expert.id,
            ExpertPick.deleted_at.is_(None)
        )
        
        # Apply status filter
        if status:
            query = query.where(ExpertPick.status == status)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = query.order_by(ExpertPick.created_at.desc()).offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await session.execute(query)
        picks = result.scalars().all()
        
        # Convert to summary format
        pick_summaries = []
        for pick in picks:
            pick_summaries.append(ExpertPickSummary(
                id=pick.id,
                expert_id=pick.expert_id,
                title=pick.title,
                slug=pick.slug,
                sport=pick.sport,
                pick_type=pick.pick_type,
                confidence_level=pick.confidence_level,
                access_tier=pick.access_tier,
                outcome=pick.outcome,
                publish_at=pick.publish_at,
                event_date=pick.event_date,
                expert_name=expert.expert_name,
                expert_slug=expert.slug,
                expert_avatar_url=expert.avatar_url
            ))
        
        # Calculate pagination info
        total_pages = (total + pagination.limit - 1) // pagination.limit
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return ExpertPickListResponse(
            data=pick_summaries,
            pagination={
                "page": pagination.page,
                "limit": pagination.limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        )
        
    except Exception as e:
        logger.error("Failed to get expert's picks", error=str(e), expert_id=expert.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve expert picks"
        )

@router.get("/{pick_id}", response_model=PickAccessResponse)
async def get_expert_pick(
    pick_id: UUID,
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert pick with access control
    """
    try:
        # Get pick with expert info
        query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            ExpertPick.id == pick_id,
            ExpertPick.deleted_at.is_(None)
        )
        result = await session.execute(query)
        pick = result.scalar_one_or_none()
        
        if not pick:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pick not found"
            )
        
        # Check access permissions
        access_granted = False
        access_reason = None
        subscription_required = None
        
        if pick.is_public or pick.access_tier == 'free':
            access_granted = True
            access_reason = "Public content"
        elif user_id:
            # Check if user has subscription to expert
            subscription = await validate_user_subscription(pick.expert_id, user_id, session)
            if subscription:
                access_granted = True
                access_reason = "Active subscription"
            else:
                subscription_required = {
                    "expert_id": pick.expert_id,
                    "expert_name": pick.expert.expert_name,
                    "required_tier": pick.access_tier
                }
        else:
            subscription_required = {
                "expert_id": pick.expert_id,
                "expert_name": pick.expert.expert_name,
                "required_tier": pick.access_tier
            }
        
        # Create appropriate response based on access
        if access_granted:
            # Increment view count
            pick.views_count += 1
            await session.commit()
            
            # Log access if user is authenticated
            if user_id:
                access_log = PickAccessLog(
                    user_id=user_id,
                    expert_id=pick.expert_id,
                    pick_id=pick.id,
                    access_type='view',
                    access_method='web'
                )
                session.add(access_log)
                await session.commit()
            
            # Return full pick data
            pick_response = ExpertPickResponse(
                id=pick.id,
                expert_id=pick.expert_id,
                title=pick.title,
                slug=pick.slug,
                sport=pick.sport,
                league=pick.league,
                event_description=pick.event_description,
                event_date=pick.event_date,
                pick_type=pick.pick_type,
                pick_value=pick.pick_value,
                recommended_stake=pick.recommended_stake,
                odds_when_posted=pick.odds_when_posted,
                odds_format=pick.odds_format,
                implied_probability=pick.implied_probability,
                confidence_level=pick.confidence_level,
                analysis_text=pick.analysis_text,
                reasoning=pick.reasoning,
                key_factors=pick.key_factors or [],
                access_tier=pick.access_tier,
                required_tier_id=pick.required_tier_id,
                is_public=pick.is_public,
                is_featured=pick.is_featured,
                is_premium=pick.is_premium,
                status=pick.status,
                publish_at=pick.publish_at,
                expires_at=pick.expires_at,
                views_count=pick.views_count,
                likes_count=pick.likes_count,
                shares_count=pick.shares_count,
                comments_count=pick.comments_count,
                outcome=pick.outcome,
                outcome_notes=pick.outcome_notes,
                settled_at=pick.settled_at,
                actual_odds=pick.actual_odds,
                profit_loss=pick.profit_loss,
                roi_percentage=pick.roi_percentage,
                parent_pick_id=pick.parent_pick_id,
                related_picks=pick.related_picks or [],
                tags=pick.tags or [],
                categories=pick.categories or [],
                expert_name=pick.expert.expert_name if pick.expert else None,
                expert_slug=pick.expert.slug if pick.expert else None,
                expert_avatar_url=pick.expert.avatar_url if pick.expert else None,
                created_at=pick.created_at,
                updated_at=pick.updated_at
            )
        else:
            # Return limited public data
            pick_response = ExpertPickPublicResponse(
                id=pick.id,
                expert_id=pick.expert_id,
                title=pick.title,
                slug=pick.slug,
                sport=pick.sport,
                league=pick.league,
                event_description=pick.event_description,
                event_date=pick.event_date,
                pick_type=pick.pick_type,
                confidence_level=pick.confidence_level,
                access_tier=pick.access_tier,
                is_public=pick.is_public,
                is_featured=pick.is_featured,
                is_premium=pick.is_premium,
                views_count=pick.views_count,
                likes_count=pick.likes_count,
                outcome=pick.outcome,
                settled_at=pick.settled_at,
                expert_name=pick.expert.expert_name if pick.expert else "",
                expert_slug=pick.expert.slug if pick.expert else "",
                expert_avatar_url=pick.expert.avatar_url if pick.expert else None,
                published_at=pick.publish_at
            )
        
        return PickAccessResponse(
            pick=pick_response,
            access_granted=access_granted,
            access_reason=access_reason,
            subscription_required=subscription_required
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get expert pick", error=str(e), pick_id=pick_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve expert pick"
        )

@router.put("/{pick_id}", response_model=StatusResponse)
async def update_expert_pick(
    pick_id: UUID,
    updates: ExpertPickUpdate,
    expert: Expert = Depends(get_current_expert),
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update expert pick (expert only)
    """
    try:
        # Get pick and verify ownership
        pick_query = select(ExpertPick).where(
            ExpertPick.id == pick_id,
            ExpertPick.expert_id == expert.id,
            ExpertPick.deleted_at.is_(None)
        )
        pick_result = await session.execute(pick_query)
        pick = pick_result.scalar_one_or_none()
        
        if not pick:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pick not found"
            )
        
        # Check if pick can still be updated (not after event start)
        if pick.event_date <= datetime.now():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot update pick after event has started"
            )
        
        # Apply updates
        update_data = updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(pick, field, value)
        
        pick.updated_by = user_id
        pick.updated_at = datetime.now()
        
        await session.commit()
        
        logger.info("Expert pick updated", pick_id=pick_id, expert_id=expert.id)
        
        return StatusResponse(status="updated", message="Expert pick updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update expert pick", error=str(e), pick_id=pick_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update expert pick"
        )

@router.put("/{pick_id}/outcome", response_model=StatusResponse)
async def update_pick_outcome(
    pick_id: UUID,
    outcome_data: PickOutcomeUpdate,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_database_session_dep)
):
    """
    Update pick outcome (expert or admin only)
    """
    try:
        # Get pick
        pick_query = select(ExpertPick).options(
            selectinload(ExpertPick.expert)
        ).where(
            ExpertPick.id == pick_id,
            ExpertPick.deleted_at.is_(None)
        )
        pick_result = await session.execute(pick_query)
        pick = pick_result.scalar_one_or_none()
        
        if not pick:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pick not found"
            )
        
        # Check permissions (expert owner or admin)
        if pick.expert.user_id != user_id:
            # TODO: Check admin permissions
            pass
        
        # Update outcome
        pick.outcome = outcome_data.outcome
        pick.outcome_notes = outcome_data.outcome_notes
        pick.actual_odds = outcome_data.actual_odds
        pick.profit_loss = outcome_data.profit_loss
        pick.roi_percentage = outcome_data.roi_percentage
        pick.settled_at = datetime.now()
        pick.updated_by = user_id
        pick.updated_at = datetime.now()
        
        # Update expert's performance stats
        expert = pick.expert
        if outcome_data.outcome == 'win':
            expert.winning_picks += 1
        # Recalculate win rate
        if expert.total_picks > 0:
            expert.win_rate = expert.winning_picks / expert.total_picks
        
        await session.commit()
        
        logger.info("Pick outcome updated", pick_id=pick_id, outcome=outcome_data.outcome)
        
        return StatusResponse(status="settled", message="Pick outcome updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update pick outcome", error=str(e), pick_id=pick_id)
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update pick outcome"
        )