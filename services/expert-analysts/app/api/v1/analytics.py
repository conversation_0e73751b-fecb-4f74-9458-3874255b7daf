"""
Analytics API Endpoints
========================

FastAPI endpoints for performance analytics, statistics, and reporting.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload
import structlog

from app.api.dependencies import (
    get_current_user_id, get_current_expert, get_expert_by_id,
    get_read_database_session, require_admin_permission
)
from app.database.models import (
    Expert, ExpertPerformanceStats, ExpertPick, Subscription, ExpertReview
)
from app.schemas.analytics import (
    PerformanceStatsResponse, LeaderboardResponse, LeaderboardEntry,
    ExpertAnalyticsResponse, PlatformAnalyticsResponse
)
from app.schemas.common import BaseResponse

logger = structlog.get_logger()

router = APIRouter()

@router.get("/experts/{expert_id}/performance", response_model=List[PerformanceStatsResponse])
async def get_expert_performance_stats(
    expert_id: UUID,
    period_type: str = Query("monthly", regex="^(daily|weekly|monthly|yearly|all_time)$"),
    limit: int = Query(12, ge=1, le=100),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert performance statistics for specified period
    """
    try:
        query = select(ExpertPerformanceStats).where(
            ExpertPerformanceStats.expert_id == expert_id,
            ExpertPerformanceStats.period_type == period_type
        ).order_by(ExpertPerformanceStats.period_start.desc()).limit(limit)
        
        result = await session.execute(query)
        stats = result.scalars().all()
        
        return [
            PerformanceStatsResponse(
                id=stat.id,
                expert_id=stat.expert_id,
                period_type=stat.period_type,
                period_start=stat.period_start,
                period_end=stat.period_end,
                total_picks=stat.total_picks,
                winning_picks=stat.winning_picks,
                losing_picks=stat.losing_picks,
                push_picks=stat.push_picks,
                pending_picks=stat.pending_picks,
                win_rate=float(stat.win_rate) if stat.win_rate else None,
                total_profit_loss=stat.total_profit_loss,
                average_roi=stat.average_roi,
                best_win=stat.best_win,
                worst_loss=stat.worst_loss,
                pick_type_breakdown=stat.pick_type_breakdown or {},
                sport_breakdown=stat.sport_breakdown or {},
                high_confidence_wins=stat.high_confidence_wins,
                high_confidence_total=stat.high_confidence_total,
                medium_confidence_wins=stat.medium_confidence_wins,
                medium_confidence_total=stat.medium_confidence_total,
                low_confidence_wins=stat.low_confidence_wins,
                low_confidence_total=stat.low_confidence_total,
                current_win_streak=stat.current_win_streak,
                current_loss_streak=stat.current_loss_streak,
                longest_win_streak=stat.longest_win_streak,
                longest_loss_streak=stat.longest_loss_streak,
                avg_views_per_pick=stat.avg_views_per_pick,
                avg_likes_per_pick=stat.avg_likes_per_pick,
                calculated_at=stat.calculated_at
            )
            for stat in stats
        ]
        
    except Exception as e:
        logger.error("Failed to get expert performance stats", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance statistics"
        )

@router.get("/leaderboards/{leaderboard_type}", response_model=LeaderboardResponse)
async def get_leaderboard(
    leaderboard_type: str = Query(..., regex="^(win_rate|total_subscribers|average_rating|total_picks|roi)$"),
    period: str = Query("monthly", regex="^(daily|weekly|monthly|yearly|all_time)$"),
    limit: int = Query(50, ge=1, le=100),
    specialty: Optional[str] = Query(None, description="Filter by specialty"),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get expert leaderboards by various metrics
    """
    try:
        # Build base query for verified, active experts
        base_query = select(Expert).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True,
            Expert.verification_status == 'verified'
        )
        
        # Apply specialty filter
        if specialty:
            base_query = base_query.where(Expert.specialty == specialty)
        
        # Apply ordering based on leaderboard type
        if leaderboard_type == "win_rate":
            # Minimum picks requirement for win rate leaderboard
            base_query = base_query.where(Expert.total_picks >= 10)
            query = base_query.order_by(desc(Expert.win_rate)).limit(limit)
        elif leaderboard_type == "total_subscribers":
            query = base_query.order_by(desc(Expert.total_subscribers)).limit(limit)
        elif leaderboard_type == "average_rating":
            # Minimum reviews requirement
            base_query = base_query.where(Expert.total_reviews >= 5)
            query = base_query.order_by(desc(Expert.average_rating)).limit(limit)
        elif leaderboard_type == "total_picks":
            query = base_query.order_by(desc(Expert.total_picks)).limit(limit)
        elif leaderboard_type == "roi":
            # Minimum picks requirement for ROI leaderboard
            base_query = base_query.where(Expert.total_picks >= 10)
            query = base_query.order_by(desc(Expert.average_roi)).limit(limit)
        else:
            query = base_query.order_by(desc(Expert.average_rating)).limit(limit)
        
        result = await session.execute(query)
        experts = result.scalars().all()
        
        # Build leaderboard entries
        entries = []
        for rank, expert in enumerate(experts, 1):
            # Get metric value based on leaderboard type
            if leaderboard_type == "win_rate":
                metric_value = float(expert.win_rate) if expert.win_rate else 0.0
            elif leaderboard_type == "total_subscribers":
                metric_value = float(expert.total_subscribers)
            elif leaderboard_type == "average_rating":
                metric_value = float(expert.average_rating) if expert.average_rating else 0.0
            elif leaderboard_type == "total_picks":
                metric_value = float(expert.total_picks)
            elif leaderboard_type == "roi":
                metric_value = float(expert.average_roi) if expert.average_roi else 0.0
            else:
                metric_value = 0.0
            
            # Secondary metrics for context
            secondary_metrics = {
                "total_picks": expert.total_picks,
                "win_rate": float(expert.win_rate) if expert.win_rate else 0.0,
                "total_subscribers": expert.total_subscribers,
                "average_rating": float(expert.average_rating) if expert.average_rating else 0.0,
                "specialty": expert.specialty
            }
            
            entries.append(LeaderboardEntry(
                rank=rank,
                expert_id=expert.id,
                expert_name=expert.expert_name,
                expert_slug=expert.slug,
                expert_avatar_url=expert.avatar_url,
                metric_value=metric_value,
                secondary_metrics=secondary_metrics
            ))
        
        return LeaderboardResponse(
            leaderboard_type=leaderboard_type,
            period=period,
            entries=entries,
            total_experts=len(entries),
            last_updated=datetime.now()
        )
        
    except Exception as e:
        logger.error("Failed to get leaderboard", error=str(e), leaderboard_type=leaderboard_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve leaderboard"
        )

@router.get("/experts/{expert_id}/analytics", response_model=ExpertAnalyticsResponse)
async def get_expert_analytics(
    expert_id: UUID,
    period_days: int = Query(30, ge=1, le=365),
    user_id: Optional[UUID] = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get comprehensive expert analytics (expert owner or admin only)
    """
    try:
        # Verify access permissions
        expert_query = select(Expert).where(Expert.id == expert_id, Expert.deleted_at.is_(None))
        expert_result = await session.execute(expert_query)
        expert = expert_result.scalar_one_or_none()
        
        if not expert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert not found"
            )
        
        # Check if user owns this expert profile or is admin
        if expert.user_id != user_id:
            # TODO: Check admin permissions
            pass
        
        # Get performance stats
        end_date = date.today()
        start_date = end_date - timedelta(days=period_days)
        
        # Overall performance (all time)
        overall_stats_query = select(ExpertPerformanceStats).where(
            ExpertPerformanceStats.expert_id == expert_id,
            ExpertPerformanceStats.period_type == 'all_time'
        ).order_by(ExpertPerformanceStats.calculated_at.desc()).limit(1)
        
        overall_result = await session.execute(overall_stats_query)
        overall_stats = overall_result.scalar_one_or_none()
        
        # Recent performance (last period)
        recent_stats_query = select(ExpertPerformanceStats).where(
            ExpertPerformanceStats.expert_id == expert_id,
            ExpertPerformanceStats.period_type == 'monthly'
        ).order_by(ExpertPerformanceStats.period_start.desc()).limit(1)
        
        recent_result = await session.execute(recent_stats_query)
        recent_stats = recent_result.scalar_one_or_none()
        
        # Subscriber metrics
        active_subs_query = select(func.count(Subscription.id)).where(
            Subscription.expert_id == expert_id,
            Subscription.status == 'active',
            Subscription.deleted_at.is_(None)
        )
        active_subs_result = await session.execute(active_subs_query)
        active_subscribers = active_subs_result.scalar() or 0
        
        # Revenue metrics (placeholder - would integrate with revenue sharing table)
        revenue_metrics = {
            "total_revenue": float(expert.total_revenue),
            "monthly_revenue": float(expert.monthly_revenue),
            "average_revenue_per_subscriber": float(expert.monthly_revenue / max(active_subscribers, 1))
        }
        
        # Content metrics
        total_picks_query = select(func.count(ExpertPick.id)).where(
            ExpertPick.expert_id == expert_id,
            ExpertPick.deleted_at.is_(None)
        )
        total_picks_result = await session.execute(total_picks_query)
        total_picks = total_picks_result.scalar() or 0
        
        content_metrics = {
            "total_picks": total_picks,
            "published_picks": expert.total_picks,
            "avg_confidence_level": 7.5  # Placeholder calculation
        }
        
        # Engagement metrics
        total_views_query = select(func.sum(ExpertPick.views_count)).where(
            ExpertPick.expert_id == expert_id,
            ExpertPick.deleted_at.is_(None)
        )
        total_views_result = await session.execute(total_views_query)
        total_views = total_views_result.scalar() or 0
        
        engagement_metrics = {
            "total_views": total_views,
            "average_views_per_pick": float(total_views / max(total_picks, 1)),
            "engagement_rate": 0.15  # Placeholder calculation
        }
        
        return ExpertAnalyticsResponse(
            expert_id=expert_id,
            overall_performance=overall_stats,
            recent_performance=recent_stats,
            performance_trend=[],  # Would be calculated from historical data
            subscriber_metrics={"active_subscribers": active_subscribers},
            subscription_growth=[],  # Would be calculated from historical data
            churn_analysis={},  # Would be calculated from subscription data
            revenue_metrics=revenue_metrics,
            revenue_trend=[],  # Would be calculated from historical data
            content_metrics=content_metrics,
            engagement_metrics=engagement_metrics,
            peer_comparison={},  # Would compare to similar experts
            industry_benchmarks={}  # Would compare to industry averages
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get expert analytics", error=str(e), expert_id=expert_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve expert analytics"
        )

@router.get("/platform", response_model=PlatformAnalyticsResponse)
async def get_platform_analytics(
    period_days: int = Query(30, ge=1, le=365),
    admin_user_id: UUID = Depends(require_admin_permission),
    session: AsyncSession = Depends(get_read_database_session)
):
    """
    Get platform-wide analytics (admin only)
    """
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=period_days)
        
        # Expert metrics
        total_experts_query = select(func.count(Expert.id)).where(Expert.deleted_at.is_(None))
        total_experts_result = await session.execute(total_experts_query)
        total_experts = total_experts_result.scalar() or 0
        
        verified_experts_query = select(func.count(Expert.id)).where(
            Expert.deleted_at.is_(None),
            Expert.verification_status == 'verified'
        )
        verified_experts_result = await session.execute(verified_experts_query)
        verified_experts = verified_experts_result.scalar() or 0
        
        active_experts_query = select(func.count(Expert.id)).where(
            Expert.deleted_at.is_(None),
            Expert.is_active == True
        )
        active_experts_result = await session.execute(active_experts_query)
        active_experts = active_experts_result.scalar() or 0
        
        # Subscription metrics
        total_subscriptions_query = select(func.count(Subscription.id)).where(
            Subscription.deleted_at.is_(None)
        )
        total_subs_result = await session.execute(total_subscriptions_query)
        total_subscriptions = total_subs_result.scalar() or 0
        
        active_subscriptions_query = select(func.count(Subscription.id)).where(
            Subscription.deleted_at.is_(None),
            Subscription.status == 'active'
        )
        active_subs_result = await session.execute(active_subscriptions_query)
        active_subscriptions = active_subs_result.scalar() or 0
        
        # Revenue metrics (would be calculated from revenue sharing table)
        total_revenue_query = select(func.sum(Expert.total_revenue)).where(Expert.deleted_at.is_(None))
        total_revenue_result = await session.execute(total_revenue_query)
        total_revenue = total_revenue_result.scalar() or 0
        
        # Performance metrics
        avg_rating_query = select(func.avg(Expert.average_rating)).where(
            Expert.deleted_at.is_(None),
            Expert.total_reviews > 0
        )
        avg_rating_result = await session.execute(avg_rating_query)
        platform_avg_rating = avg_rating_result.scalar() or 0
        
        total_picks_query = select(func.sum(Expert.total_picks)).where(Expert.deleted_at.is_(None))
        total_picks_result = await session.execute(total_picks_query)
        total_picks_made = total_picks_result.scalar() or 0
        
        # Calculate growth rates (placeholder - would use historical data)
        expert_growth_rate = 0.15
        subscription_growth_rate = 0.25
        revenue_growth_rate = 0.30
        
        return PlatformAnalyticsResponse(
            total_experts=total_experts,
            verified_experts=verified_experts,
            active_experts=active_experts,
            expert_growth_rate=expert_growth_rate,
            total_subscriptions=total_subscriptions,
            active_subscriptions=active_subscriptions,
            subscription_growth_rate=subscription_growth_rate,
            total_mrr=total_revenue * 0.1,  # Placeholder calculation
            total_arr=total_revenue,
            average_revenue_per_expert=float(total_revenue / max(active_experts, 1)),
            revenue_growth_rate=revenue_growth_rate,
            platform_win_rate=0.58,  # Would be calculated from all picks
            average_expert_rating=float(platform_avg_rating),
            total_picks_made=total_picks_made,
            total_pick_views=0,  # Would be calculated from pick view counts
            average_engagement_rate=0.12,  # Would be calculated from engagement data
            period_start=start_date,
            period_end=end_date,
            generated_at=datetime.now()
        )
        
    except Exception as e:
        logger.error("Failed to get platform analytics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve platform analytics"
        )