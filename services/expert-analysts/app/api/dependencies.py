"""
Expert Analyst Marketplace - API Dependencies
=============================================

FastAPI dependency injection for authentication, database sessions,
and common business logic following BetBet template patterns.

Author: Claude-API
Date: 2025-07-21
"""

import os
from typing import Optional, AsyncGenerator, Dict, Any
from functools import lru_cache
from uuid import UUID

from fastapi import Depends, HTTPException, status, Security
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic_settings import BaseSettings
import structlog
from jose import jwt, JWTError

# Import shared database infrastructure
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from services.shared.core.database.connection import get_database_session
from app.database.models import Expert, Subscription, SubscriptionTier

logger = structlog.get_logger()

# Security
security = HTTPBearer()

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Database
    database_url: str = "postgresql://localhost/betbet"
    database_pool_size: int = 20
    database_max_overflow: int = 30
    
    # JWT Authentication
    jwt_secret_key: str = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    
    # External services
    stripe_secret_key: Optional[str] = None
    stripe_webhook_secret: Optional[str] = None
    
    # Redis caching
    redis_url: Optional[str] = None
    
    # Email/notifications
    email_service_url: Optional[str] = None
    email_api_key: Optional[str] = None
    
    # Business rules
    platform_fee_percentage: float = 0.10  # 10% platform fee
    free_trial_days: int = 7
    max_pick_access_per_month: int = 100
    
    class Config:
        env_file = ".env"

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

# Database session dependencies
async def get_read_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get read-only database session"""
    async with get_database_session() as session:
        yield session

async def get_database_session_dep() -> AsyncGenerator[AsyncSession, None]:
    """Get read-write database session"""
    async with get_database_session() as session:
        yield session

# Authentication dependencies
async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Security(security),
    settings: Settings = Depends(get_settings)
) -> UUID:
    """
    Extract and validate JWT token to get current user ID
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        return UUID(user_id)
    except (JWTError, ValueError) as e:
        logger.warning("JWT validation failed", error=str(e))
        raise credentials_exception

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Security(security, auto_error=False),
    settings: Settings = Depends(get_settings)
) -> Optional[UUID]:
    """
    Optional authentication - returns None if no token provided
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm])
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
        return UUID(user_id)
    except (JWTError, ValueError):
        return None

# Expert-specific dependencies
async def get_current_expert(
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
) -> Expert:
    """
    Get current user's expert profile, raise 404 if not an expert
    """
    query = select(Expert).where(
        Expert.user_id == user_id,
        Expert.deleted_at.is_(None)
    )
    result = await session.execute(query)
    expert = result.scalar_one_or_none()
    
    if not expert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expert profile not found"
        )
    
    return expert

async def get_expert_by_id(
    expert_id: UUID,
    session: AsyncSession = Depends(get_read_database_session)
) -> Expert:
    """
    Get expert by ID with validation
    """
    query = select(Expert).where(
        Expert.id == expert_id,
        Expert.deleted_at.is_(None)
    )
    result = await session.execute(query)
    expert = result.scalar_one_or_none()
    
    if not expert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expert not found"
        )
    
    return expert

async def get_expert_by_slug(
    slug: str,
    session: AsyncSession = Depends(get_read_database_session)
) -> Expert:
    """
    Get expert by slug with validation
    """
    query = select(Expert).where(
        Expert.slug == slug,
        Expert.deleted_at.is_(None)
    )
    result = await session.execute(query)
    expert = result.scalar_one_or_none()
    
    if not expert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expert not found"
        )
    
    return expert

# Subscription validation dependencies
async def validate_user_subscription(
    expert_id: UUID,
    user_id: UUID = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_read_database_session)
) -> Optional[Subscription]:
    """
    Validate user has active subscription to expert
    """
    from datetime import datetime
    
    query = select(Subscription).where(
        Subscription.user_id == user_id,
        Subscription.expert_id == expert_id,
        Subscription.status == 'active',
        Subscription.ends_at > datetime.now(),
        Subscription.deleted_at.is_(None)
    )
    result = await session.execute(query)
    subscription = result.scalar_one_or_none()
    
    return subscription

async def require_subscription_access(
    expert_id: UUID,
    required_tier: str = "basic",
    user_id: UUID = Depends(get_current_user_id),
    subscription: Optional[Subscription] = Depends(validate_user_subscription)
) -> Subscription:
    """
    Require user to have active subscription with specific tier access
    """
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Active subscription required to access this content"
        )
    
    # Check subscription tier access level
    # This would need to be implemented based on business logic
    # For now, just return the subscription if it exists
    return subscription

# Admin/Permission dependencies
async def require_admin_permission(
    user_id: UUID = Depends(get_current_user_id)
) -> UUID:
    """
    Require user to have admin permissions
    TODO: Implement role-based permission checking
    """
    # This would typically check user roles/permissions in the database
    # For now, placeholder implementation
    return user_id

# Business logic dependencies
def get_platform_settings(
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Get platform business configuration
    """
    return {
        "platform_fee_percentage": settings.platform_fee_percentage,
        "free_trial_days": settings.free_trial_days,
        "max_pick_access_per_month": settings.max_pick_access_per_month
    }

# Pagination dependency
class PaginationParams:
    """Pagination parameters for list endpoints"""
    
    def __init__(self, page: int = 1, limit: int = 20):
        self.page = max(1, page)
        self.limit = min(100, max(1, limit))  # Max 100 items per page
        
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.limit

def get_pagination_params(page: int = 1, limit: int = 20) -> PaginationParams:
    """Get pagination parameters with validation"""
    return PaginationParams(page=page, limit=limit)

# Sorting and filtering dependencies
class ExpertFilterParams:
    """Expert filtering parameters"""
    
    def __init__(
        self,
        specialty: Optional[str] = None,
        verification_status: Optional[str] = None,
        min_rating: Optional[float] = None,
        is_featured: Optional[bool] = None
    ):
        self.specialty = specialty
        self.verification_status = verification_status
        self.min_rating = min_rating
        self.is_featured = is_featured

def get_expert_filters(
    specialty: Optional[str] = None,
    verification_status: Optional[str] = None,
    min_rating: Optional[float] = None,
    is_featured: Optional[bool] = None
) -> ExpertFilterParams:
    """Get expert filtering parameters"""
    return ExpertFilterParams(
        specialty=specialty,
        verification_status=verification_status,
        min_rating=min_rating,
        is_featured=is_featured
    )

class PickFilterParams:
    """Pick filtering parameters"""
    
    def __init__(
        self,
        sport: Optional[str] = None,
        pick_type: Optional[str] = None,
        access_tier: Optional[str] = None,
        status: Optional[str] = None,
        outcome: Optional[str] = None
    ):
        self.sport = sport
        self.pick_type = pick_type
        self.access_tier = access_tier
        self.status = status
        self.outcome = outcome

def get_pick_filters(
    sport: Optional[str] = None,
    pick_type: Optional[str] = None,
    access_tier: Optional[str] = None,
    status: Optional[str] = None,
    outcome: Optional[str] = None
) -> PickFilterParams:
    """Get pick filtering parameters"""
    return PickFilterParams(
        sport=sport,
        pick_type=pick_type,
        access_tier=access_tier,
        status=status,
        outcome=outcome
    )

# Cache dependency (placeholder for Redis integration)
class CacheService:
    """Cache service for frequently accessed data"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url
        # TODO: Initialize Redis connection
    
    async def get(self, key: str) -> Optional[str]:
        """Get cached value"""
        # TODO: Implement Redis get
        return None
    
    async def set(self, key: str, value: str, expire: int = 3600) -> bool:
        """Set cached value"""
        # TODO: Implement Redis set
        return True
    
    async def delete(self, key: str) -> bool:
        """Delete cached value"""
        # TODO: Implement Redis delete
        return True

@lru_cache()
def get_cache_service(settings: Settings = Depends(get_settings)) -> CacheService:
    """Get cache service instance"""
    return CacheService(redis_url=settings.redis_url)