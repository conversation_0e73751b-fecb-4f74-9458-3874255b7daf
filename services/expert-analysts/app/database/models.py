"""
Expert Analyst Marketplace Database Models
==========================================

SQLAlchemy models for Expert Analyst Marketplace following BetBet template patterns.
Enterprise-grade models with proper relationships, constraints, and audit support.

Author: Claude-DB
Date: 2025-07-21
Template Base: Gaming Engine models
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any
from sqlalchemy import (
    Column, String, Integer, Boolean, DateTime, Date, Numeric, 
    Text, JSON, ARRAY, ForeignKey, CheckConstraint, UniqueConstraint,
    Index, event, text
)
from sqlalchemy.dialects.postgresql import UUID, INET
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from sqlalchemy_utils import EmailType, URLType

# Base model with standard audit fields
Base = declarative_base()

class AuditMixin:
    """Standard audit fields for all tables following BetBet template."""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    version = Column(Integer, default=1, nullable=False)
    
    # Soft delete support
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    deleted_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)

class Expert(Base, AuditMixin):
    """Expert profiles with verification, performance tracking, and subscription management."""
    __tablename__ = 'experts'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('years_experience >= 0', name='ck_experts_years_experience'),
        CheckConstraint('total_picks >= 0', name='ck_experts_total_picks'),
        CheckConstraint('winning_picks >= 0 AND winning_picks <= total_picks', name='ck_experts_winning_picks'),
        CheckConstraint('total_subscribers >= 0', name='ck_experts_total_subscribers'),
        CheckConstraint('active_subscribers >= 0 AND active_subscribers <= total_subscribers', name='ck_experts_active_subscribers'),
        CheckConstraint('total_revenue >= 0', name='ck_experts_total_revenue'),
        CheckConstraint('monthly_revenue >= 0', name='ck_experts_monthly_revenue'),
        CheckConstraint('total_reviews >= 0', name='ck_experts_total_reviews'),
        CheckConstraint('average_rating IS NULL OR average_rating BETWEEN 1 AND 5', name='ck_experts_average_rating'),
        CheckConstraint("verification_status IN ('pending', 'verified', 'rejected', 'suspended')", name='ck_experts_verification_status'),
        CheckConstraint("status IN ('active', 'inactive', 'suspended', 'banned')", name='ck_experts_status'),
        CheckConstraint("risk_tolerance IN ('conservative', 'moderate', 'aggressive', 'high_risk')", name='ck_experts_risk_tolerance'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Expert identification and branding
    expert_name = Column(String(200), nullable=False)
    slug = Column(String(300), unique=True, nullable=False)
    bio = Column(Text)
    specialty = Column(String(100), nullable=False)
    tagline = Column(String(300))
    
    # Professional credentials
    professional_background = Column(Text)
    years_experience = Column(Integer)
    certifications = Column(ARRAY(Text))
    achievements = Column(ARRAY(Text))
    
    # Verification and status
    verification_status = Column(String(20), default='pending', nullable=False)
    verification_date = Column(DateTime(timezone=True))
    verification_notes = Column(Text)
    verified_by = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    
    # Expert status
    is_active = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)
    status = Column(String(20), default='active', nullable=False)
    
    # Profile media
    avatar_url = Column(URLType)
    banner_url = Column(URLType)
    social_links = Column(JSON, default={})
    
    # Performance metrics (calculated fields)
    total_picks = Column(Integer, default=0, nullable=False)
    winning_picks = Column(Integer, default=0, nullable=False)
    average_roi = Column(Numeric(8, 4), default=0)
    
    # Subscriber metrics
    total_subscribers = Column(Integer, default=0, nullable=False)
    active_subscribers = Column(Integer, default=0, nullable=False)
    
    # Financial metrics
    total_revenue = Column(Numeric(15, 2), default=0, nullable=False)
    monthly_revenue = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Rating system
    average_rating = Column(Numeric(3, 2))
    total_reviews = Column(Integer, default=0, nullable=False)
    
    # Configuration
    subscription_config = Column(JSON, default={})
    analysis_focus = Column(ARRAY(Text))
    betting_style = Column(String(50))
    risk_tolerance = Column(String(20))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="expert_profile")
    subscription_tiers = relationship("SubscriptionTier", back_populates="expert", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="expert")
    expert_picks = relationship("ExpertPick", back_populates="expert", cascade="all, delete-orphan")
    performance_stats = relationship("ExpertPerformanceStats", back_populates="expert", cascade="all, delete-orphan")
    revenue_sharing = relationship("RevenueSharing", back_populates="expert", cascade="all, delete-orphan")
    reviews = relationship("ExpertReview", back_populates="expert", cascade="all, delete-orphan")
    
    @hybrid_property
    def win_rate(self) -> Optional[Decimal]:
        """Calculate win rate from total and winning picks."""
        if self.total_picks == 0:
            return Decimal('0')
        return round(Decimal(self.winning_picks) / Decimal(self.total_picks), 4)
    
    @validates('slug')
    def validate_slug(self, key, value):
        """Ensure slug is URL-friendly."""
        if value and not value.replace('-', '').replace('_', '').isalnum():
            raise ValueError("Slug must contain only alphanumeric characters, hyphens, and underscores")
        return value

class SubscriptionTier(Base, AuditMixin):
    """Flexible subscription plans for experts with feature access control."""
    __tablename__ = 'subscription_tiers'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('price_monthly >= 0', name='ck_subscription_tiers_price_monthly'),
        CheckConstraint('price_yearly IS NULL OR price_yearly >= 0', name='ck_subscription_tiers_price_yearly'),
        CheckConstraint('discount_yearly_percentage BETWEEN 0 AND 100', name='ck_subscription_tiers_discount_yearly'),
        CheckConstraint('free_trial_days >= 0', name='ck_subscription_tiers_free_trial_days'),
        CheckConstraint('max_picks_per_month IS NULL OR max_picks_per_month > 0', name='ck_subscription_tiers_max_picks'),
        CheckConstraint('subscriber_limit IS NULL OR subscriber_limit > 0', name='ck_subscription_tiers_subscriber_limit'),
        CheckConstraint('current_subscribers >= 0', name='ck_subscription_tiers_current_subscribers'),
        CheckConstraint("access_level IN ('free', 'basic', 'premium', 'vip')", name='ck_subscription_tiers_access_level'),
        CheckConstraint("picks_access_level IN ('basic', 'premium', 'exclusive')", name='ck_subscription_tiers_picks_access'),
        CheckConstraint("analysis_depth IN ('basic', 'standard', 'detailed', 'comprehensive')", name='ck_subscription_tiers_analysis_depth'),
        UniqueConstraint('expert_id', 'tier_slug', name='uq_subscription_tiers_expert_slug'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    
    # Tier identification
    tier_name = Column(String(100), nullable=False)
    tier_slug = Column(String(150), nullable=False)
    description = Column(Text)
    
    # Pricing
    price_monthly = Column(Numeric(10, 2), nullable=False)
    price_yearly = Column(Numeric(10, 2))
    discount_yearly_percentage = Column(Numeric(5, 2), default=0)
    free_trial_days = Column(Integer, default=0)
    
    # Access permissions
    max_picks_per_month = Column(Integer)
    access_level = Column(String(20), nullable=False)
    features_included = Column(ARRAY(Text), default=[], nullable=False)
    picks_access_level = Column(String(20), default='basic')
    analysis_depth = Column(String(20), default='standard')
    
    # Communication access
    allows_direct_messages = Column(Boolean, default=False)
    allows_live_chat = Column(Boolean, default=False)
    allows_priority_support = Column(Boolean, default=False)
    
    # Status and ordering
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    display_order = Column(Integer, default=0)
    
    # Limits
    subscriber_limit = Column(Integer)
    current_subscribers = Column(Integer, default=0)
    
    # Relationships
    expert = relationship("Expert", back_populates="subscription_tiers")
    subscriptions = relationship("Subscription", back_populates="tier")

class Subscription(Base, AuditMixin):
    """User subscriptions to expert analysts with billing and access tracking."""
    __tablename__ = 'subscriptions'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('amount_paid >= 0', name='ck_subscriptions_amount_paid'),
        CheckConstraint('picks_accessed >= 0', name='ck_subscriptions_picks_accessed'),
        CheckConstraint('ends_at > starts_at', name='ck_subscriptions_end_after_start'),
        CheckConstraint('trial_ends_at IS NULL OR trial_ends_at <= ends_at', name='ck_subscriptions_trial_before_end'),
        CheckConstraint("subscription_type IN ('monthly', 'yearly', 'lifetime')", name='ck_subscriptions_type'),
        CheckConstraint("status IN ('pending', 'active', 'cancelled', 'expired', 'suspended', 'refunded')", name='ck_subscriptions_status'),
        CheckConstraint("billing_cycle IN ('monthly', 'yearly', 'one_time')", name='ck_subscriptions_billing_cycle'),
        UniqueConstraint('user_id', 'expert_id', name='uq_subscriptions_user_expert'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    tier_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscription_tiers.id'), nullable=False)
    
    # Subscription details
    subscription_type = Column(String(20), nullable=False)
    status = Column(String(20), default='active')
    
    # Billing information
    amount_paid = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), default='USD')
    billing_cycle = Column(String(20), nullable=False)
    payment_transaction_id = Column(UUID(as_uuid=True), ForeignKey('gaming_engine.transactions.id'))
    external_subscription_id = Column(String(255))
    
    # Subscription period
    starts_at = Column(DateTime(timezone=True), server_default=func.now())
    ends_at = Column(DateTime(timezone=True), nullable=False)
    auto_renew = Column(Boolean, default=True)
    
    # Trial period
    is_trial = Column(Boolean, default=False)
    trial_ends_at = Column(DateTime(timezone=True))
    
    # Cancellation
    cancelled_at = Column(DateTime(timezone=True))
    cancellation_reason = Column(String(100))
    cancelled_by = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    
    # Usage tracking
    picks_accessed = Column(Integer, default=0)
    last_access_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    expert = relationship("Expert", back_populates="subscriptions")
    tier = relationship("SubscriptionTier", back_populates="subscriptions")
    access_logs = relationship("PickAccessLog", back_populates="subscription")
    
    @property
    def is_active(self) -> bool:
        """Check if subscription is currently active."""
        return (
            self.status == 'active' and 
            self.ends_at > datetime.now() and 
            self.deleted_at is None
        )
    
    @property
    def days_remaining(self) -> int:
        """Calculate days remaining in subscription."""
        if not self.is_active:
            return 0
        return (self.ends_at.date() - date.today()).days

class ExpertPick(Base, AuditMixin):
    """Expert predictions and analysis with access control and outcome tracking."""
    __tablename__ = 'expert_picks'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('recommended_stake > 0', name='ck_expert_picks_recommended_stake'),
        CheckConstraint('implied_probability BETWEEN 0 AND 100', name='ck_expert_picks_implied_probability'),
        CheckConstraint('confidence_level BETWEEN 1 AND 10', name='ck_expert_picks_confidence_level'),
        CheckConstraint('views_count >= 0', name='ck_expert_picks_views_count'),
        CheckConstraint('likes_count >= 0', name='ck_expert_picks_likes_count'),
        CheckConstraint('shares_count >= 0', name='ck_expert_picks_shares_count'),
        CheckConstraint('comments_count >= 0', name='ck_expert_picks_comments_count'),
        CheckConstraint('event_date > created_at', name='ck_expert_picks_event_future'),
        CheckConstraint('expires_at IS NULL OR expires_at > publish_at', name='ck_expert_picks_expires_after_publish'),
        CheckConstraint("pick_type IN ('spread', 'moneyline', 'total', 'prop_bet', 'futures', 'parlay', 'custom')", name='ck_expert_picks_pick_type'),
        CheckConstraint("odds_format IN ('american', 'decimal', 'fractional')", name='ck_expert_picks_odds_format'),
        CheckConstraint("access_tier IN ('free', 'basic', 'premium', 'vip')", name='ck_expert_picks_access_tier'),
        CheckConstraint("status IN ('draft', 'scheduled', 'published', 'archived', 'cancelled')", name='ck_expert_picks_status'),
        CheckConstraint("outcome IN ('win', 'loss', 'push', 'cancelled', 'pending')", name='ck_expert_picks_outcome'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    
    # Pick identification
    title = Column(String(300), nullable=False)
    slug = Column(String(400), unique=True, nullable=False)
    
    # Event information
    sport = Column(String(50), nullable=False)
    league = Column(String(100))
    event_description = Column(Text, nullable=False)
    event_date = Column(DateTime(timezone=True), nullable=False)
    
    # Pick details
    pick_type = Column(String(50), nullable=False)
    pick_value = Column(Text, nullable=False)
    recommended_stake = Column(Numeric(8, 2))
    
    # Odds and analysis
    odds_when_posted = Column(Numeric(8, 3))
    odds_format = Column(String(10), default='american')
    implied_probability = Column(Numeric(5, 2))
    
    # Confidence and analysis
    confidence_level = Column(Integer)
    analysis_text = Column(Text, nullable=False)
    reasoning = Column(Text)
    key_factors = Column(ARRAY(Text))
    
    # Access control
    access_tier = Column(String(20), nullable=False)
    required_tier_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscription_tiers.id'))
    
    # Content visibility
    is_public = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)
    
    # Scheduling and publication
    status = Column(String(20), default='draft')
    publish_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))
    
    # Engagement metrics
    views_count = Column(Integer, default=0)
    likes_count = Column(Integer, default=0)
    shares_count = Column(Integer, default=0)
    comments_count = Column(Integer, default=0)
    
    # Pick outcome
    outcome = Column(String(20))
    outcome_notes = Column(Text)
    settled_at = Column(DateTime(timezone=True))
    actual_odds = Column(Numeric(8, 3))
    profit_loss = Column(Numeric(10, 2))
    roi_percentage = Column(Numeric(8, 4))
    
    # Related picks
    parent_pick_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.expert_picks.id'))
    related_picks = Column(ARRAY(UUID))
    
    # Categorization
    tags = Column(ARRAY(Text), default=[])
    categories = Column(ARRAY(Text), default=[])
    
    # Relationships
    expert = relationship("Expert", back_populates="expert_picks")
    required_tier = relationship("SubscriptionTier", foreign_keys=[required_tier_id])
    access_logs = relationship("PickAccessLog", back_populates="pick", cascade="all, delete-orphan")
    
    @property
    def is_published(self) -> bool:
        """Check if pick is published and visible."""
        return (
            self.status == 'published' and 
            self.publish_at <= datetime.now() and
            self.deleted_at is None
        )

class PickAccessLog(Base):
    """Track pick access for billing and analytics."""
    __tablename__ = 'pick_access_log'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint("access_type IN ('view', 'download', 'share')", name='ck_pick_access_log_access_type'),
        CheckConstraint("access_method IN ('web', 'mobile', 'api', 'email')", name='ck_pick_access_log_access_method'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id'), nullable=False)
    pick_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.expert_picks.id'), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscriptions.id'))
    
    # Access details
    access_type = Column(String(20), nullable=False)
    access_method = Column(String(20))
    session_id = Column(String(255))
    ip_address = Column(INET)
    user_agent = Column(Text)
    counted_against_limit = Column(Boolean, default=True)
    accessed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    expert = relationship("Expert", foreign_keys=[expert_id])
    pick = relationship("ExpertPick", back_populates="access_logs")
    subscription = relationship("Subscription", back_populates="access_logs")

class ExpertPerformanceStats(Base):
    """Aggregated performance statistics for experts by time period."""
    __tablename__ = 'expert_performance_stats'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('total_picks >= 0', name='ck_expert_performance_stats_total_picks'),
        CheckConstraint('winning_picks >= 0', name='ck_expert_performance_stats_winning_picks'),
        CheckConstraint('losing_picks >= 0', name='ck_expert_performance_stats_losing_picks'),
        CheckConstraint('push_picks >= 0', name='ck_expert_performance_stats_push_picks'),
        CheckConstraint('pending_picks >= 0', name='ck_expert_performance_stats_pending_picks'),
        CheckConstraint('current_win_streak >= 0', name='ck_expert_performance_stats_current_win_streak'),
        CheckConstraint('current_loss_streak >= 0', name='ck_expert_performance_stats_current_loss_streak'),
        CheckConstraint('longest_win_streak >= 0', name='ck_expert_performance_stats_longest_win_streak'),
        CheckConstraint('longest_loss_streak >= 0', name='ck_expert_performance_stats_longest_loss_streak'),
        CheckConstraint('period_end >= period_start', name='ck_expert_performance_stats_period_valid'),
        CheckConstraint('winning_picks + losing_picks + push_picks + pending_picks = total_picks', name='ck_expert_performance_stats_pick_sum'),
        CheckConstraint("period_type IN ('daily', 'weekly', 'monthly', 'yearly', 'all_time')", name='ck_expert_performance_stats_period_type'),
        UniqueConstraint('expert_id', 'period_type', 'period_start', 'period_end', name='uq_expert_performance_stats_period'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    
    # Time period
    period_type = Column(String(20), nullable=False)
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    
    # Pick statistics
    total_picks = Column(Integer, default=0)
    winning_picks = Column(Integer, default=0)
    losing_picks = Column(Integer, default=0)
    push_picks = Column(Integer, default=0)
    pending_picks = Column(Integer, default=0)
    
    # Financial performance
    total_profit_loss = Column(Numeric(12, 2), default=0)
    average_roi = Column(Numeric(8, 4), default=0)
    best_win = Column(Numeric(10, 2), default=0)
    worst_loss = Column(Numeric(10, 2), default=0)
    
    # Breakdown analysis
    pick_type_breakdown = Column(JSON, default={})
    sport_breakdown = Column(JSON, default={})
    
    # Confidence analysis
    high_confidence_wins = Column(Integer, default=0)
    high_confidence_total = Column(Integer, default=0)
    medium_confidence_wins = Column(Integer, default=0)
    medium_confidence_total = Column(Integer, default=0)
    low_confidence_wins = Column(Integer, default=0)
    low_confidence_total = Column(Integer, default=0)
    
    # Streak tracking
    current_win_streak = Column(Integer, default=0)
    current_loss_streak = Column(Integer, default=0)
    longest_win_streak = Column(Integer, default=0)
    longest_loss_streak = Column(Integer, default=0)
    
    # Engagement metrics
    avg_views_per_pick = Column(Numeric(8, 2), default=0)
    avg_likes_per_pick = Column(Numeric(8, 2), default=0)
    
    # Calculation timestamp
    calculated_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    expert = relationship("Expert", back_populates="performance_stats")
    
    @hybrid_property
    def win_rate(self) -> Optional[Decimal]:
        """Calculate win rate from winning and total picks."""
        decided_picks = self.winning_picks + self.losing_picks
        if decided_picks == 0:
            return Decimal('0')
        return round(Decimal(self.winning_picks) / Decimal(decided_picks), 4)

class RevenueSharing(Base, AuditMixin):
    """Revenue sharing and payout tracking for experts."""
    __tablename__ = 'revenue_sharing'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('gross_revenue >= 0', name='ck_revenue_sharing_gross_revenue'),
        CheckConstraint('platform_fee_percentage BETWEEN 0 AND 1', name='ck_revenue_sharing_platform_fee_percentage'),
        CheckConstraint('payment_processor_fee >= 0', name='ck_revenue_sharing_payment_processor_fee'),
        CheckConstraint('tax_withholding_amount >= 0', name='ck_revenue_sharing_tax_withholding_amount'),
        CheckConstraint('subscriber_count >= 0', name='ck_revenue_sharing_subscriber_count'),
        CheckConstraint('transaction_count >= 0', name='ck_revenue_sharing_transaction_count'),
        CheckConstraint('revenue_period_end >= revenue_period_start', name='ck_revenue_sharing_period_valid'),
        CheckConstraint("payout_status IN ('pending', 'processing', 'paid', 'failed', 'cancelled')", name='ck_revenue_sharing_payout_status'),
        UniqueConstraint('expert_id', 'revenue_period_start', 'revenue_period_end', name='uq_revenue_sharing_expert_period'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscriptions.id'))
    
    # Revenue period
    revenue_period_start = Column(Date, nullable=False)
    revenue_period_end = Column(Date, nullable=False)
    
    # Revenue breakdown
    gross_revenue = Column(Numeric(15, 2), nullable=False)
    platform_fee_percentage = Column(Numeric(5, 4), nullable=False)
    payment_processor_fee = Column(Numeric(15, 2), default=0)
    
    # Payout status
    payout_status = Column(String(20), default='pending')
    payout_method = Column(String(50))
    payout_reference = Column(String(255))
    
    # Tax and compliance
    tax_withholding_amount = Column(Numeric(15, 2), default=0)
    tax_form_required = Column(Boolean, default=False)
    
    # Timing
    calculated_at = Column(DateTime(timezone=True), server_default=func.now())
    scheduled_payout_date = Column(Date)
    actual_payout_date = Column(Date)
    
    # Metadata
    subscriber_count = Column(Integer, default=0)
    transaction_count = Column(Integer, default=0)
    
    # Relationships
    expert = relationship("Expert", back_populates="revenue_sharing")
    
    @hybrid_property
    def platform_fee_amount(self) -> Decimal:
        """Calculate platform fee amount."""
        return self.gross_revenue * self.platform_fee_percentage
    
    @hybrid_property
    def net_revenue(self) -> Decimal:
        """Calculate net revenue after platform fees."""
        return self.gross_revenue - (self.gross_revenue * self.platform_fee_percentage)
    
    @hybrid_property
    def final_payout_amount(self) -> Decimal:
        """Calculate final payout amount after all deductions."""
        return (self.gross_revenue - 
                (self.gross_revenue * self.platform_fee_percentage) - 
                self.payment_processor_fee)

class ExpertReview(Base, AuditMixin):
    """User reviews and ratings for expert analysts."""
    __tablename__ = 'expert_reviews'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('rating BETWEEN 1 AND 5', name='ck_expert_reviews_rating'),
        CheckConstraint('analysis_quality_rating BETWEEN 1 AND 5', name='ck_expert_reviews_analysis_quality_rating'),
        CheckConstraint('communication_rating BETWEEN 1 AND 5', name='ck_expert_reviews_communication_rating'),
        CheckConstraint('value_for_money_rating BETWEEN 1 AND 5', name='ck_expert_reviews_value_for_money_rating'),
        CheckConstraint('timeliness_rating BETWEEN 1 AND 5', name='ck_expert_reviews_timeliness_rating'),
        CheckConstraint('helpful_votes >= 0', name='ck_expert_reviews_helpful_votes'),
        CheckConstraint('total_votes >= helpful_votes', name='ck_expert_reviews_total_votes'),
        CheckConstraint("status IN ('active', 'hidden', 'flagged', 'removed')", name='ck_expert_reviews_status'),
        UniqueConstraint('user_id', 'expert_id', name='uq_expert_reviews_user_expert'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscriptions.id'))
    
    # Review content
    rating = Column(Integer, nullable=False)
    review_title = Column(String(200))
    review_text = Column(Text)
    
    # Review categories
    analysis_quality_rating = Column(Integer)
    communication_rating = Column(Integer)
    value_for_money_rating = Column(Integer)
    timeliness_rating = Column(Integer)
    
    # Review status
    is_verified = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    status = Column(String(20), default='active')
    
    # Engagement
    helpful_votes = Column(Integer, default=0)
    total_votes = Column(Integer, default=0)
    
    # Moderation
    flagged_by = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    flagged_reason = Column(Text)
    moderated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    moderated_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    expert = relationship("Expert", back_populates="reviews")
    subscription = relationship("Subscription", foreign_keys=[subscription_id])

class ExpertNotification(Base):
    """Notification system for expert communications."""
    __tablename__ = 'expert_notifications'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint("notification_type IN ('new_pick', 'pick_update', 'pick_result', 'subscription_expiry', 'expert_message', 'system_alert')", name='ck_expert_notifications_notification_type'),
        CheckConstraint("status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled')", name='ck_expert_notifications_status'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id', ondelete='CASCADE'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'))  # NULL for broadcast
    
    # Notification details
    notification_type = Column(String(50), nullable=False)
    title = Column(String(300), nullable=False)
    message = Column(Text, nullable=False)
    
    # Delivery channels
    send_email = Column(Boolean, default=True)
    send_push = Column(Boolean, default=True)
    send_sms = Column(Boolean, default=False)
    send_in_app = Column(Boolean, default=True)
    
    # Status tracking
    status = Column(String(20), default='pending')
    sent_at = Column(DateTime(timezone=True))
    delivered_at = Column(DateTime(timezone=True))
    opened_at = Column(DateTime(timezone=True))
    clicked_at = Column(DateTime(timezone=True))
    
    # Related content
    related_pick_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.expert_picks.id'))
    related_subscription_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscriptions.id'))
    
    # Metadata
    metadata = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'))

class ExpertAnalystAuditLog(Base):
    """Audit log table for expert analyst operations."""
    __tablename__ = 'audit_logs'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint("action IN ('INSERT', 'UPDATE', 'DELETE')", name='ck_expert_analyst_audit_logs_action'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    table_name = Column(String(100), nullable=False)
    record_id = Column(UUID(as_uuid=True), nullable=False)
    action = Column(String(20), nullable=False)
    old_values = Column(JSON)
    new_values = Column(JSON)
    changed_fields = Column(ARRAY(Text))
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    ip_address = Column(INET)
    user_agent = Column(Text)
    session_id = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class ExpertAnalystTransaction(Base, AuditMixin):
    """Financial transactions for expert analyst marketplace."""
    __tablename__ = 'transactions'
    __table_args__ = (
        {'schema': 'expert_analysts'},
        CheckConstraint('amount >= 0', name='ck_expert_analyst_transactions_amount'),
        CheckConstraint("status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed')", name='ck_expert_analyst_transactions_status'),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text('uuid_generate_v4()'))
    transaction_type = Column(String(50), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    expert_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.experts.id'))
    subscription_id = Column(UUID(as_uuid=True), ForeignKey('expert_analysts.subscriptions.id'))
    amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), default='USD')
    description = Column(Text, nullable=False)
    reference_id = Column(UUID(as_uuid=True))
    
    # Double-entry bookkeeping
    debit_account = Column(String(100), nullable=False)
    credit_account = Column(String(100), nullable=False)
    
    # Status tracking
    status = Column(String(20), default='pending')
    external_transaction_id = Column(String(255))
    payment_method = Column(String(50))
    metadata = Column(JSON)

# Create indexes for performance optimization
def create_indexes(engine):
    """Create performance-optimized indexes."""
    from sqlalchemy import text
    
    with engine.connect() as conn:
        # Expert table indexes
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experts_user_id 
            ON expert_analysts.experts(user_id) WHERE deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experts_verification_status 
            ON expert_analysts.experts(verification_status, is_active) WHERE deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experts_featured 
            ON expert_analysts.experts(is_featured, average_rating DESC) 
            WHERE is_featured = true AND deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experts_specialty 
            ON expert_analysts.experts(specialty, is_active) WHERE deleted_at IS NULL;
        """))
        
        # Expert picks indexes
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_expert_picks_expert_status 
            ON expert_analysts.expert_picks(expert_id, status, publish_at DESC) WHERE deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_expert_picks_sport_date 
            ON expert_analysts.expert_picks(sport, event_date) 
            WHERE status = 'published' AND deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_expert_picks_access_tier 
            ON expert_analysts.expert_picks(access_tier, is_public) WHERE deleted_at IS NULL;
        """))
        
        # Subscription indexes
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_user_status 
            ON expert_analysts.subscriptions(user_id, status, ends_at) WHERE deleted_at IS NULL;
        """))
        
        conn.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_expert_active 
            ON expert_analysts.subscriptions(expert_id, status) 
            WHERE status = 'active' AND deleted_at IS NULL;
        """))
        
        conn.commit()

# Event listeners for automatic updates
@event.listens_for(Expert, 'after_insert')
@event.listens_for(Expert, 'after_update')
def update_expert_win_rate(mapper, connection, target):
    """Automatically calculate win rate when picks are updated."""
    if target.total_picks > 0:
        target.win_rate = round(Decimal(target.winning_picks) / Decimal(target.total_picks), 4)
    else:
        target.win_rate = Decimal('0')