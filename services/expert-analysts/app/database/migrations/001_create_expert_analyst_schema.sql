-- Expert Analyst Marketplace - Core Schema Migration
-- ====================================================
-- 
-- Migration: 001_create_expert_analyst_schema
-- Description: Expert marketplace with subscriptions, picks, performance tracking, and revenue distribution
-- Author: Claude-DB
-- Date: 2025-07-21
-- Template Base: Gaming Engine enterprise patterns
-- Integration: Gaming Engine and Custom Betting modules

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- CREATE EXPERT ANALYST SCHEMA
-- =====================================================
CREATE SCHEMA IF NOT EXISTS expert_analysts;

-- =====================================================
-- EXPERTS TABLE (Core Expert Profiles)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.experts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Expert identification and branding
    expert_name VARCHAR(200) NOT NULL,
    slug VARCHAR(300) UNIQUE NOT NULL, -- URL-friendly identifier
    bio TEXT,
    specialty VARCHAR(100) NOT NULL, -- e.g., 'NBA', 'NFL', 'Soccer', 'General Sports'
    tagline VARCHAR(300),
    
    -- Professional credentials
    professional_background TEXT,
    years_experience INTEGER CHECK (years_experience >= 0),
    certifications TEXT[],
    achievements TEXT[],
    
    -- Verification and status
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'suspended')),
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    verified_by UUID REFERENCES public.users(id),
    
    -- Expert status
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    is_premium BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned')),
    
    -- Profile media
    avatar_url TEXT,
    banner_url TEXT,
    social_links JSONB DEFAULT '{}',
    
    -- Performance metrics (calculated fields)
    total_picks INTEGER DEFAULT 0 CHECK (total_picks >= 0),
    winning_picks INTEGER DEFAULT 0 CHECK (winning_picks >= 0 AND winning_picks <= total_picks),
    win_rate DECIMAL(5,4) GENERATED ALWAYS AS (
        CASE WHEN total_picks = 0 THEN 0 
        ELSE ROUND(winning_picks::DECIMAL / total_picks, 4) 
        END
    ) STORED,
    average_roi DECIMAL(8,4) DEFAULT 0,
    
    -- Subscriber metrics
    total_subscribers INTEGER DEFAULT 0 CHECK (total_subscribers >= 0),
    active_subscribers INTEGER DEFAULT 0 CHECK (active_subscribers >= 0 AND active_subscribers <= total_subscribers),
    
    -- Financial metrics
    total_revenue DECIMAL(15,2) DEFAULT 0 CHECK (total_revenue >= 0),
    monthly_revenue DECIMAL(15,2) DEFAULT 0 CHECK (monthly_revenue >= 0),
    
    -- Rating system
    average_rating DECIMAL(3,2) CHECK (average_rating IS NULL OR average_rating BETWEEN 1 AND 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    
    -- Subscription tiers configuration
    subscription_config JSONB DEFAULT '{}',
    
    -- Analysis settings
    analysis_focus TEXT[],
    betting_style VARCHAR(50),
    risk_tolerance VARCHAR(20) CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive', 'high_risk')),
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- =====================================================
-- SUBSCRIPTION_TIERS TABLE (Flexible Subscription Plans)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.subscription_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    
    -- Tier identification
    tier_name VARCHAR(100) NOT NULL,
    tier_slug VARCHAR(150) NOT NULL,
    description TEXT,
    
    -- Pricing
    price_monthly DECIMAL(10,2) NOT NULL CHECK (price_monthly >= 0),
    price_yearly DECIMAL(10,2) CHECK (price_yearly IS NULL OR price_yearly >= 0),
    discount_yearly_percentage DECIMAL(5,2) DEFAULT 0 CHECK (discount_yearly_percentage BETWEEN 0 AND 100),
    
    -- Free trial
    free_trial_days INTEGER DEFAULT 0 CHECK (free_trial_days >= 0),
    
    -- Access permissions
    max_picks_per_month INTEGER CHECK (max_picks_per_month IS NULL OR max_picks_per_month > 0),
    access_level VARCHAR(20) NOT NULL CHECK (access_level IN ('free', 'basic', 'premium', 'vip')),
    
    -- Feature access
    features_included TEXT[] NOT NULL DEFAULT '{}',
    picks_access_level VARCHAR(20) DEFAULT 'basic' CHECK (picks_access_level IN ('basic', 'premium', 'exclusive')),
    analysis_depth VARCHAR(20) DEFAULT 'standard' CHECK (analysis_depth IN ('basic', 'standard', 'detailed', 'comprehensive')),
    
    -- Communication access
    allows_direct_messages BOOLEAN DEFAULT false,
    allows_live_chat BOOLEAN DEFAULT false,
    allows_priority_support BOOLEAN DEFAULT false,
    
    -- Status and ordering
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    
    -- Limits and restrictions
    subscriber_limit INTEGER CHECK (subscriber_limit IS NULL OR subscriber_limit > 0),
    current_subscribers INTEGER DEFAULT 0 CHECK (current_subscribers >= 0),
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    UNIQUE(expert_id, tier_slug),
    CHECK (current_subscribers <= COALESCE(subscriber_limit, current_subscribers))
);

-- =====================================================
-- SUBSCRIPTIONS TABLE (User Subscriptions)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    tier_id UUID NOT NULL REFERENCES expert_analysts.subscription_tiers(id),
    
    -- Subscription details
    subscription_type VARCHAR(20) NOT NULL CHECK (subscription_type IN ('monthly', 'yearly', 'lifetime')),
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('pending', 'active', 'cancelled', 'expired', 'suspended', 'refunded')),
    
    -- Billing information
    amount_paid DECIMAL(10,2) NOT NULL CHECK (amount_paid >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    billing_cycle VARCHAR(20) NOT NULL CHECK (billing_cycle IN ('monthly', 'yearly', 'one_time')),
    
    -- Payment reference
    payment_transaction_id UUID REFERENCES gaming_engine.transactions(id),
    external_subscription_id VARCHAR(255), -- Stripe/payment processor ID
    
    -- Subscription period
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ends_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renew BOOLEAN DEFAULT true,
    
    -- Trial period
    is_trial BOOLEAN DEFAULT false,
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    
    -- Cancellation
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason VARCHAR(100),
    cancelled_by UUID REFERENCES public.users(id),
    
    -- Usage tracking
    picks_accessed INTEGER DEFAULT 0 CHECK (picks_accessed >= 0),
    last_access_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    UNIQUE(user_id, expert_id), -- One subscription per user per expert
    CHECK (ends_at > starts_at),
    CHECK (trial_ends_at IS NULL OR trial_ends_at <= ends_at)
);

-- =====================================================
-- EXPERT_PICKS TABLE (Expert Predictions/Analysis)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.expert_picks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    
    -- Pick identification
    title VARCHAR(300) NOT NULL,
    slug VARCHAR(400) UNIQUE NOT NULL,
    
    -- Event information
    sport VARCHAR(50) NOT NULL,
    league VARCHAR(100),
    event_description TEXT NOT NULL,
    event_date TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Pick details
    pick_type VARCHAR(50) NOT NULL CHECK (pick_type IN ('spread', 'moneyline', 'total', 'prop_bet', 'futures', 'parlay', 'custom')),
    pick_value TEXT NOT NULL,
    recommended_stake DECIMAL(8,2) CHECK (recommended_stake > 0),
    
    -- Odds and analysis
    odds_when_posted DECIMAL(8,3),
    odds_format VARCHAR(10) DEFAULT 'american' CHECK (odds_format IN ('american', 'decimal', 'fractional')),
    implied_probability DECIMAL(5,2) CHECK (implied_probability BETWEEN 0 AND 100),
    
    -- Confidence and analysis
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 10),
    analysis_text TEXT NOT NULL,
    reasoning TEXT,
    key_factors TEXT[],
    
    -- Access control
    access_tier VARCHAR(20) NOT NULL CHECK (access_tier IN ('free', 'basic', 'premium', 'vip')),
    required_tier_id UUID REFERENCES expert_analysts.subscription_tiers(id),
    
    -- Content visibility
    is_public BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    is_premium BOOLEAN DEFAULT false,
    
    -- Scheduling and publication
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'archived', 'cancelled')),
    publish_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Engagement metrics
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    shares_count INTEGER DEFAULT 0 CHECK (shares_count >= 0),
    comments_count INTEGER DEFAULT 0 CHECK (comments_count >= 0),
    
    -- Pick outcome (set after event)
    outcome VARCHAR(20) CHECK (outcome IN ('win', 'loss', 'push', 'cancelled', 'pending')),
    outcome_notes TEXT,
    settled_at TIMESTAMP WITH TIME ZONE,
    actual_odds DECIMAL(8,3),
    
    -- Performance calculation
    profit_loss DECIMAL(10,2),
    roi_percentage DECIMAL(8,4),
    
    -- Related picks (for parlays/combinations)
    parent_pick_id UUID REFERENCES expert_analysts.expert_picks(id),
    related_picks UUID[],
    
    -- Tags and categorization
    tags TEXT[] DEFAULT '{}',
    categories TEXT[] DEFAULT '{}',
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    CHECK (event_date > created_at),
    CHECK (expires_at IS NULL OR expires_at > publish_at)
);

-- =====================================================
-- PICK_ACCESS_LOG TABLE (Track pick access for billing)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.pick_access_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    user_id UUID NOT NULL REFERENCES public.users(id),
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id),
    pick_id UUID NOT NULL REFERENCES expert_analysts.expert_picks(id),
    subscription_id UUID REFERENCES expert_analysts.subscriptions(id),
    
    -- Access details
    access_type VARCHAR(20) NOT NULL CHECK (access_type IN ('view', 'download', 'share')),
    access_method VARCHAR(20) CHECK (access_method IN ('web', 'mobile', 'api', 'email')),
    
    -- Session information
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    
    -- Billing impact
    counted_against_limit BOOLEAN DEFAULT true,
    
    -- Audit fields (simplified for high volume)
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- EXPERT_PERFORMANCE_STATS TABLE (Aggregated Performance)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.expert_performance_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    
    -- Time period
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly', 'all_time')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Pick statistics
    total_picks INTEGER DEFAULT 0 CHECK (total_picks >= 0),
    winning_picks INTEGER DEFAULT 0 CHECK (winning_picks >= 0),
    losing_picks INTEGER DEFAULT 0 CHECK (losing_picks >= 0),
    push_picks INTEGER DEFAULT 0 CHECK (push_picks >= 0),
    pending_picks INTEGER DEFAULT 0 CHECK (pending_picks >= 0),
    
    -- Performance metrics
    win_rate DECIMAL(5,4) GENERATED ALWAYS AS (
        CASE WHEN (winning_picks + losing_picks) = 0 THEN 0 
        ELSE ROUND(winning_picks::DECIMAL / (winning_picks + losing_picks), 4) 
        END
    ) STORED,
    
    -- Financial performance
    total_profit_loss DECIMAL(12,2) DEFAULT 0,
    average_roi DECIMAL(8,4) DEFAULT 0,
    best_win DECIMAL(10,2) DEFAULT 0,
    worst_loss DECIMAL(10,2) DEFAULT 0,
    
    -- Pick type breakdown
    pick_type_breakdown JSONB DEFAULT '{}',
    sport_breakdown JSONB DEFAULT '{}',
    
    -- Confidence analysis
    high_confidence_wins INTEGER DEFAULT 0 CHECK (high_confidence_wins >= 0),
    high_confidence_total INTEGER DEFAULT 0 CHECK (high_confidence_total >= 0),
    medium_confidence_wins INTEGER DEFAULT 0 CHECK (medium_confidence_wins >= 0),
    medium_confidence_total INTEGER DEFAULT 0 CHECK (medium_confidence_total >= 0),
    low_confidence_wins INTEGER DEFAULT 0 CHECK (low_confidence_wins >= 0),
    low_confidence_total INTEGER DEFAULT 0 CHECK (low_confidence_total >= 0),
    
    -- Streak tracking
    current_win_streak INTEGER DEFAULT 0 CHECK (current_win_streak >= 0),
    current_loss_streak INTEGER DEFAULT 0 CHECK (current_loss_streak >= 0),
    longest_win_streak INTEGER DEFAULT 0 CHECK (longest_win_streak >= 0),
    longest_loss_streak INTEGER DEFAULT 0 CHECK (longest_loss_streak >= 0),
    
    -- Subscriber engagement
    avg_views_per_pick DECIMAL(8,2) DEFAULT 0,
    avg_likes_per_pick DECIMAL(8,2) DEFAULT 0,
    
    -- Calculated at timestamp
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for period
    UNIQUE(expert_id, period_type, period_start, period_end),
    
    -- Constraints
    CHECK (period_end >= period_start),
    CHECK (winning_picks + losing_picks + push_picks + pending_picks = total_picks)
);

-- =====================================================
-- REVENUE_SHARING TABLE (Expert Payouts)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.revenue_sharing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES expert_analysts.subscriptions(id),
    
    -- Revenue period
    revenue_period_start DATE NOT NULL,
    revenue_period_end DATE NOT NULL,
    
    -- Revenue breakdown
    gross_revenue DECIMAL(15,2) NOT NULL CHECK (gross_revenue >= 0),
    platform_fee_percentage DECIMAL(5,4) NOT NULL CHECK (platform_fee_percentage BETWEEN 0 AND 1),
    platform_fee_amount DECIMAL(15,2) GENERATED ALWAYS AS (gross_revenue * platform_fee_percentage) STORED,
    net_revenue DECIMAL(15,2) GENERATED ALWAYS AS (gross_revenue - (gross_revenue * platform_fee_percentage)) STORED,
    
    -- Payment processing
    payment_processor_fee DECIMAL(15,2) DEFAULT 0 CHECK (payment_processor_fee >= 0),
    final_payout_amount DECIMAL(15,2) GENERATED ALWAYS AS (gross_revenue - (gross_revenue * platform_fee_percentage) - payment_processor_fee) STORED,
    
    -- Payout status
    payout_status VARCHAR(20) DEFAULT 'pending' CHECK (payout_status IN ('pending', 'processing', 'paid', 'failed', 'cancelled')),
    payout_method VARCHAR(50),
    payout_reference VARCHAR(255),
    
    -- Tax and compliance
    tax_withholding_amount DECIMAL(15,2) DEFAULT 0 CHECK (tax_withholding_amount >= 0),
    tax_form_required BOOLEAN DEFAULT false,
    
    -- Timing
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_payout_date DATE,
    actual_payout_date DATE,
    
    -- Metadata
    subscriber_count INTEGER DEFAULT 0 CHECK (subscriber_count >= 0),
    transaction_count INTEGER DEFAULT 0 CHECK (transaction_count >= 0),
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (revenue_period_end >= revenue_period_start),
    UNIQUE(expert_id, revenue_period_start, revenue_period_end)
);

-- =====================================================
-- EXPERT_REVIEWS TABLE (User Reviews and Ratings)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.expert_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    user_id UUID NOT NULL REFERENCES public.users(id),
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES expert_analysts.subscriptions(id),
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_title VARCHAR(200),
    review_text TEXT,
    
    -- Review categories
    analysis_quality_rating INTEGER CHECK (analysis_quality_rating BETWEEN 1 AND 5),
    communication_rating INTEGER CHECK (communication_rating BETWEEN 1 AND 5),
    value_for_money_rating INTEGER CHECK (value_for_money_rating BETWEEN 1 AND 5),
    timeliness_rating INTEGER CHECK (timeliness_rating BETWEEN 1 AND 5),
    
    -- Review status
    is_verified BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'flagged', 'removed')),
    
    -- Engagement
    helpful_votes INTEGER DEFAULT 0 CHECK (helpful_votes >= 0),
    total_votes INTEGER DEFAULT 0 CHECK (total_votes >= helpful_votes),
    
    -- Moderation
    flagged_by UUID REFERENCES public.users(id),
    flagged_reason TEXT,
    moderated_by UUID REFERENCES public.users(id),
    moderated_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    UNIQUE(user_id, expert_id) -- One review per user per expert
);

-- =====================================================
-- EXPERT_NOTIFICATIONS TABLE (Communication System)
-- =====================================================
CREATE TABLE IF NOT EXISTS expert_analysts.expert_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    expert_id UUID NOT NULL REFERENCES expert_analysts.experts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id), -- NULL for broadcast notifications
    
    -- Notification details
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('new_pick', 'pick_update', 'pick_result', 'subscription_expiry', 'expert_message', 'system_alert')),
    title VARCHAR(300) NOT NULL,
    message TEXT NOT NULL,
    
    -- Delivery channels
    send_email BOOLEAN DEFAULT true,
    send_push BOOLEAN DEFAULT true,
    send_sms BOOLEAN DEFAULT false,
    send_in_app BOOLEAN DEFAULT true,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled')),
    
    -- Delivery tracking
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    
    -- Related content
    related_pick_id UUID REFERENCES expert_analysts.expert_picks(id),
    related_subscription_id UUID REFERENCES expert_analysts.subscriptions(id),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id)
);

-- =====================================================
-- STANDARD AUDIT LOG TABLE (REQUIRED for all modules)
-- =====================================================
CREATE TABLE expert_analysts.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID REFERENCES public.users(id),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STANDARD FINANCIAL TRANSACTION TABLE (For revenue tracking)
-- =====================================================
CREATE TABLE expert_analysts.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_type VARCHAR(50) NOT NULL,
    user_id UUID NOT NULL REFERENCES public.users(id),
    expert_id UUID REFERENCES expert_analysts.experts(id),
    subscription_id UUID REFERENCES expert_analysts.subscriptions(id),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT NOT NULL,
    reference_id UUID,
    
    -- Double-entry bookkeeping fields
    debit_account VARCHAR(100) NOT NULL,
    credit_account VARCHAR(100) NOT NULL,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed')),
    
    -- External references
    external_transaction_id VARCHAR(255),
    payment_method VARCHAR(50),
    
    -- Metadata
    metadata JSONB,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Migration completed successfully
-- Schema: expert_analysts with 10 primary tables
-- Features: Expert profiles, subscriptions, picks, performance tracking, revenue sharing
-- Template compliance: VERIFIED
-- Integration ready: Gaming Engine and Custom Betting modules