"""
Expert schemas for Expert Analyst Marketplace
=============================================

Pydantic schemas for expert profiles, verification, and management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field, validator

from .common import (
    BaseResponse, PerformanceMetrics, SocialLinks, ContactInfo,
    AuditInfo, MetadataInfo, PaginatedResponse
)

# Expert profile schemas
class ExpertBase(BaseModel):
    """Base expert schema with common fields"""
    expert_name: str = Field(min_length=2, max_length=200)
    bio: Optional[str] = Field(None, max_length=2000)
    specialty: str = Field(min_length=2, max_length=100)
    tagline: Optional[str] = Field(None, max_length=300)
    professional_background: Optional[str] = None
    years_experience: Optional[int] = Field(None, ge=0, le=50)
    certifications: List[str] = []
    achievements: List[str] = []
    
    # Profile media
    avatar_url: Optional[str] = None
    banner_url: Optional[str] = None
    social_links: Optional[Dict[str, str]] = {}
    
    # Configuration
    analysis_focus: List[str] = []
    betting_style: Optional[str] = None
    risk_tolerance: Optional[str] = Field(None, regex="^(conservative|moderate|aggressive|high_risk)$")

class ExpertCreate(ExpertBase):
    """Schema for creating new expert profile"""
    slug: str = Field(min_length=3, max_length=300, regex="^[a-z0-9-_]+$")
    
    @validator('slug')
    def validate_slug(cls, v):
        if not v.replace('-', '').replace('_', '').replace('.', '').isalnum():
            raise ValueError('Slug must contain only lowercase letters, numbers, hyphens, underscores, and dots')
        return v

class ExpertUpdate(BaseModel):
    """Schema for updating expert profile"""
    expert_name: Optional[str] = Field(None, min_length=2, max_length=200)
    bio: Optional[str] = Field(None, max_length=2000)
    tagline: Optional[str] = Field(None, max_length=300)
    professional_background: Optional[str] = None
    years_experience: Optional[int] = Field(None, ge=0, le=50)
    certifications: Optional[List[str]] = None
    achievements: Optional[List[str]] = None
    
    # Profile media
    avatar_url: Optional[str] = None
    banner_url: Optional[str] = None
    social_links: Optional[Dict[str, str]] = None
    
    # Configuration
    analysis_focus: Optional[List[str]] = None
    betting_style: Optional[str] = None
    risk_tolerance: Optional[str] = Field(None, regex="^(conservative|moderate|aggressive|high_risk)$")

class ExpertVerificationUpdate(BaseModel):
    """Schema for expert verification status updates (admin only)"""
    verification_status: str = Field(regex="^(pending|verified|rejected|suspended)$")
    verification_notes: Optional[str] = None
    is_featured: Optional[bool] = None
    is_premium: Optional[bool] = None

class ExpertStatusUpdate(BaseModel):
    """Schema for expert status updates"""
    is_active: Optional[bool] = None
    status: Optional[str] = Field(None, regex="^(active|inactive|suspended|banned)$")

# Expert response schemas
class ExpertResponse(BaseModel):
    """Full expert profile response"""
    id: UUID
    user_id: UUID
    expert_name: str
    slug: str
    bio: Optional[str]
    specialty: str
    tagline: Optional[str]
    professional_background: Optional[str]
    years_experience: Optional[int]
    certifications: List[str]
    achievements: List[str]
    
    # Verification and status
    verification_status: str
    verification_date: Optional[datetime]
    verification_notes: Optional[str]
    is_active: bool
    is_featured: bool
    is_premium: bool
    status: str
    
    # Profile media
    avatar_url: Optional[str]
    banner_url: Optional[str]
    social_links: Dict[str, str]
    
    # Performance metrics
    total_picks: int
    winning_picks: int
    win_rate: Optional[Decimal]
    average_roi: Optional[Decimal]
    
    # Subscriber metrics
    total_subscribers: int
    active_subscribers: int
    
    # Financial metrics  
    total_revenue: Decimal
    monthly_revenue: Decimal
    
    # Rating system
    average_rating: Optional[Decimal]
    total_reviews: int
    
    # Configuration
    analysis_focus: List[str]
    betting_style: Optional[str]
    risk_tolerance: Optional[str]
    
    # Audit info
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ExpertPublicResponse(BaseModel):
    """Public expert profile response (no sensitive data)"""
    id: UUID
    expert_name: str
    slug: str
    bio: Optional[str]
    specialty: str
    tagline: Optional[str]
    years_experience: Optional[int]
    certifications: List[str]
    achievements: List[str]
    
    # Verification status (public)
    verification_status: str
    verification_date: Optional[datetime]
    is_featured: bool
    is_premium: bool
    
    # Profile media
    avatar_url: Optional[str]
    banner_url: Optional[str]
    social_links: Dict[str, str]
    
    # Performance metrics (public)
    total_picks: int
    winning_picks: int
    win_rate: Optional[Decimal]
    average_roi: Optional[Decimal]
    
    # Public subscriber metrics
    total_subscribers: int
    
    # Rating system
    average_rating: Optional[Decimal]
    total_reviews: int
    
    # Configuration
    analysis_focus: List[str]
    betting_style: Optional[str]
    risk_tolerance: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ExpertListItem(BaseModel):
    """Compact expert info for list responses"""
    id: UUID
    expert_name: str
    slug: str
    specialty: str
    tagline: Optional[str]
    avatar_url: Optional[str]
    
    # Key metrics
    verification_status: str
    is_featured: bool
    win_rate: Optional[Decimal]
    average_rating: Optional[Decimal]
    total_subscribers: int
    total_reviews: int
    
    class Config:
        from_attributes = True

class ExpertListResponse(PaginatedResponse[ExpertListItem]):
    """Paginated expert list response"""
    pass

class ExpertProfileResponse(BaseResponse):
    """Expert profile response wrapper"""
    expert: ExpertResponse

class ExpertStatsResponse(BaseModel):
    """Expert statistics response"""
    expert_id: UUID
    performance_metrics: PerformanceMetrics
    engagement_metrics: Dict[str, Any]
    financial_metrics: Dict[str, Any]
    period_stats: Dict[str, Any]
    
    class Config:
        from_attributes = True

# Expert search and filtering schemas
class ExpertSearchParams(BaseModel):
    """Expert search parameters"""
    query: Optional[str] = None
    specialty: Optional[str] = None
    verification_status: Optional[str] = None
    min_rating: Optional[float] = Field(None, ge=0.0, le=5.0)
    max_rating: Optional[float] = Field(None, ge=0.0, le=5.0)
    min_win_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    max_win_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_featured: Optional[bool] = None
    is_premium: Optional[bool] = None
    has_free_content: Optional[bool] = None
    min_experience: Optional[int] = Field(None, ge=0)
    risk_tolerance: Optional[str] = None
    
    # Sorting
    sort_by: str = Field(default="average_rating", regex="^(average_rating|win_rate|total_subscribers|created_at|total_picks)$")
    sort_direction: str = Field(default="desc", regex="^(asc|desc)$")

class ExpertDiscoveryResponse(BaseModel):
    """Expert discovery with enhanced data"""
    experts: List[ExpertPublicResponse]
    featured_experts: List[ExpertPublicResponse]
    top_performers: List[ExpertPublicResponse]
    trending_experts: List[ExpertPublicResponse]
    categories: List[Dict[str, Any]]

# Expert onboarding schemas
class ExpertOnboardingData(BaseModel):
    """Complete expert onboarding data"""
    profile: ExpertCreate
    contact_info: Optional[ContactInfo] = None
    initial_subscription_tiers: Optional[List[Dict[str, Any]]] = None
    verification_documents: Optional[List[str]] = None  # Document URLs
    terms_accepted: bool = True
    privacy_policy_accepted: bool = True

class ExpertOnboardingResponse(BaseResponse):
    """Expert onboarding response"""
    expert: ExpertResponse
    onboarding_status: str
    next_steps: List[str]
    required_documents: List[str]

# Expert dashboard schemas
class ExpertDashboardStats(BaseModel):
    """Expert dashboard statistics"""
    total_picks: int
    picks_this_month: int
    active_subscribers: int
    new_subscribers_this_month: int
    monthly_revenue: Decimal
    pending_payouts: Decimal
    average_rating: Optional[Decimal]
    recent_performance: PerformanceMetrics

class ExpertDashboardResponse(BaseResponse):
    """Expert dashboard response"""
    stats: ExpertDashboardStats
    recent_picks: List[Dict[str, Any]]  # Recent pick summaries
    recent_reviews: List[Dict[str, Any]]  # Recent review summaries
    notifications: List[Dict[str, Any]]  # Unread notifications
    quick_actions: List[str]

# Expert verification schemas  
class VerificationDocument(BaseModel):
    """Verification document schema"""
    document_type: str = Field(regex="^(id|proof_of_expertise|bank_statement|tax_form|other)$")
    document_url: str
    description: Optional[str] = None
    uploaded_at: datetime = Field(default_factory=datetime.now)

class ExpertVerificationSubmission(BaseModel):
    """Expert verification submission"""
    documents: List[VerificationDocument]
    additional_info: Optional[str] = None
    contact_preferences: Optional[Dict[str, Any]] = None

class ExpertVerificationResponse(BaseResponse):
    """Expert verification response"""
    verification_id: UUID
    status: str
    submitted_at: datetime
    expected_review_time: str
    required_documents: List[str]
    submitted_documents: List[VerificationDocument]

# Bulk operations schemas
class BulkExpertUpdate(BaseModel):
    """Bulk expert update schema"""
    expert_ids: List[UUID]
    updates: Dict[str, Any]
    reason: Optional[str] = None

class BulkExpertResponse(BaseResponse):
    """Bulk expert operation response"""
    updated_count: int
    failed_updates: List[Dict[str, Any]]
    success_ids: List[UUID]