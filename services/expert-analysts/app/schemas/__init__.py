"""
Expert Analyst Marketplace - Pydantic Schemas
==============================================

Pydantic schemas for API request/response validation and serialization.
Following BetBet template patterns for consistent API design.

Author: Claude-API
Date: 2025-07-21
"""

from .experts import *
from .subscriptions import *
from .picks import *
from .analytics import *
from .revenue import *
from .community import *
from .common import *

__all__ = [
    # Common schemas
    "BaseResponse",
    "ErrorResponse", 
    "PaginatedResponse",
    "StatusResponse",
    
    # Expert schemas
    "ExpertCreate",
    "ExpertUpdate",
    "ExpertResponse",
    "ExpertPublicResponse",
    "ExpertListResponse",
    "ExpertProfileResponse",
    "ExpertVerificationUpdate",
    
    # Subscription schemas
    "SubscriptionTierCreate",
    "SubscriptionTierUpdate", 
    "SubscriptionTierResponse",
    "SubscriptionCreate",
    "SubscriptionUpdate",
    "SubscriptionResponse",
    "SubscriptionListResponse",
    
    # Pick schemas
    "ExpertPickCreate",
    "ExpertPickUpdate",
    "ExpertPickResponse",
    "ExpertPickListResponse",
    "ExpertPickPublicResponse",
    "PickOutcomeUpdate",
    
    # Analytics schemas
    "PerformanceStatsResponse",
    "LeaderboardResponse",
    "ExpertAnalyticsResponse",
    "PlatformAnalyticsResponse",
    
    # Revenue schemas
    "RevenueShareResponse",
    "PayoutRequest",
    "PayoutResponse",
    "EarningsResponse",
    "RevenueReportResponse",
    
    # Community schemas
    "ExpertReviewCreate",
    "ExpertReviewUpdate",
    "ExpertReviewResponse",
    "NotificationResponse",
    "FollowResponse"
]