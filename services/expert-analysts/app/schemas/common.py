"""
Common Pydantic schemas for Expert Analyst Marketplace
======================================================

Shared schemas for consistent API responses and data structures.

Author: Claude-API  
Date: 2025-07-21
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Generic, TypeVar
from uuid import UUID
from pydantic import BaseModel, Field

T = TypeVar('T')

class BaseResponse(BaseModel):
    """Base response schema with standard metadata"""
    success: bool = True
    timestamp: datetime = Field(default_factory=datetime.now)
    message: Optional[str] = None

class ErrorResponse(BaseModel):
    """Standard error response schema"""
    success: bool = False
    error: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)

class StatusResponse(BaseResponse):
    """Simple status response"""
    status: str

class PaginatedResponse(BaseResponse, Generic[T]):
    """Generic paginated response schema"""
    data: List[T]
    pagination: Dict[str, Any] = Field(
        default_factory=lambda: {
            "page": 1,
            "limit": 20,
            "total": 0,
            "total_pages": 0,
            "has_next": False,
            "has_previous": False
        }
    )

class PaginationInfo(BaseModel):
    """Pagination metadata"""
    page: int = Field(ge=1)
    limit: int = Field(ge=1, le=100)
    total: int = Field(ge=0)
    total_pages: int = Field(ge=0)
    has_next: bool
    has_previous: bool

class SortParams(BaseModel):
    """Sorting parameters"""
    field: str
    direction: str = Field(default="asc", regex="^(asc|desc)$")

class FilterParams(BaseModel):
    """Base filtering parameters"""
    search: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    updated_after: Optional[datetime] = None
    updated_before: Optional[datetime] = None

class PerformanceMetrics(BaseModel):
    """Performance metrics schema"""
    total_picks: int = Field(ge=0)
    winning_picks: int = Field(ge=0)
    losing_picks: int = Field(ge=0)
    push_picks: int = Field(ge=0)
    win_rate: float = Field(ge=0.0, le=1.0)
    average_roi: Optional[float] = None
    total_profit_loss: Optional[float] = None

class SocialLinks(BaseModel):
    """Social media links schema"""
    twitter: Optional[str] = None
    instagram: Optional[str] = None
    youtube: Optional[str] = None
    tiktok: Optional[str] = None
    website: Optional[str] = None
    linkedin: Optional[str] = None

class ContactInfo(BaseModel):
    """Contact information schema"""
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    social_links: Optional[SocialLinks] = None

class Address(BaseModel):
    """Address information schema"""
    street: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class BankingInfo(BaseModel):
    """Banking information for payouts"""
    bank_name: Optional[str] = None
    account_number: Optional[str] = None  # Should be encrypted in storage
    routing_number: Optional[str] = None
    account_type: Optional[str] = None
    swift_code: Optional[str] = None

class TaxInfo(BaseModel):
    """Tax information for compliance"""
    tax_id: Optional[str] = None  # SSN/EIN - encrypted in storage
    tax_classification: Optional[str] = None
    is_1099_required: bool = False
    backup_withholding: bool = False

class SubscriptionConfig(BaseModel):
    """Subscription configuration schema"""
    auto_renew_default: bool = True
    trial_enabled: bool = True
    trial_days: int = Field(ge=0, le=30)
    cancellation_policy: Optional[str] = None
    refund_policy: Optional[str] = None

class ContentSettings(BaseModel):
    """Content creation and publishing settings"""
    default_access_tier: str = Field(default="basic")
    auto_publish_enabled: bool = False
    requires_approval: bool = True
    content_guidelines: Optional[str] = None

class NotificationPreferences(BaseModel):
    """User notification preferences"""
    email_enabled: bool = True
    push_enabled: bool = True
    sms_enabled: bool = False
    in_app_enabled: bool = True
    
    # Specific notification types
    new_picks: bool = True
    pick_outcomes: bool = True
    subscription_updates: bool = True
    payment_notifications: bool = True
    expert_messages: bool = True
    marketing_emails: bool = False

class PrivacySettings(BaseModel):
    """Privacy and data sharing settings"""
    profile_public: bool = True
    show_performance_publicly: bool = True
    show_subscriber_count: bool = True
    allow_direct_messages: bool = True
    show_in_leaderboards: bool = True

class GameInfo(BaseModel):
    """Game/event information for picks"""
    sport: str
    league: Optional[str] = None
    home_team: Optional[str] = None
    away_team: Optional[str] = None
    event_name: Optional[str] = None
    event_date: Optional[datetime] = None
    venue: Optional[str] = None

class OddsInfo(BaseModel):
    """Odds information schema"""
    value: float
    format: str = Field(default="american", regex="^(american|decimal|fractional)$")
    implied_probability: Optional[float] = Field(None, ge=0.0, le=100.0)
    bookmaker: Optional[str] = None
    timestamp: Optional[datetime] = None

class PickAnalysis(BaseModel):
    """Pick analysis and reasoning schema"""
    confidence_level: int = Field(ge=1, le=10)
    analysis_text: str = Field(min_length=50)
    reasoning: Optional[str] = None
    key_factors: List[str] = []
    risk_assessment: Optional[str] = None
    recommended_stake: Optional[float] = Field(None, gt=0)

class OutcomeResult(BaseModel):
    """Pick outcome result schema"""
    outcome: str = Field(regex="^(win|loss|push|cancelled|pending)$")
    outcome_notes: Optional[str] = None
    actual_odds: Optional[float] = None
    profit_loss: Optional[float] = None
    roi_percentage: Optional[float] = None
    settled_at: Optional[datetime] = None

class MarketData(BaseModel):
    """Market and betting data schema"""
    market_type: str
    total_volume: Optional[float] = Field(None, ge=0)
    participant_count: Optional[int] = Field(None, ge=0)
    closing_odds: Optional[float] = None
    market_movement: Optional[str] = None

class EngagementMetrics(BaseModel):
    """Content engagement metrics"""
    views_count: int = Field(ge=0)
    likes_count: int = Field(ge=0) 
    shares_count: int = Field(ge=0)
    comments_count: int = Field(ge=0)
    engagement_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    avg_time_viewed: Optional[float] = None

class AuditInfo(BaseModel):
    """Audit trail information"""
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    version: int = Field(ge=1)

class MetadataInfo(BaseModel):
    """Generic metadata container"""
    tags: List[str] = []
    categories: List[str] = []
    custom_fields: Dict[str, Any] = {}
    external_ids: Dict[str, str] = {}

# Configuration and settings schemas
class PlatformConfig(BaseModel):
    """Platform-wide configuration"""
    platform_fee_percentage: float = Field(ge=0.0, le=1.0)
    max_subscription_tiers_per_expert: int = Field(ge=1, le=10)
    min_pick_confidence_level: int = Field(ge=1, le=10)
    max_picks_per_day_per_expert: int = Field(ge=1, le=100)
    free_trial_max_days: int = Field(ge=0, le=30)

class BusinessRules(BaseModel):
    """Business logic rules and constraints"""
    min_expert_verification_requirements: List[str] = []
    payout_schedule: str = Field(default="monthly")
    minimum_payout_amount: float = Field(ge=0)
    subscription_cancellation_policy: str = ""
    content_moderation_enabled: bool = True