"""
Community schemas for Expert Analyst Marketplace
=================================================

Pydantic schemas for reviews, ratings, notifications, and social features.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field

from .common import BaseResponse, PaginatedResponse

class ExpertReviewCreate(BaseModel):
    """Schema for creating expert review"""
    expert_id: UUID
    rating: int = Field(ge=1, le=5)
    review_title: Optional[str] = Field(None, max_length=200)
    review_text: Optional[str] = None
    
    # Category ratings
    analysis_quality_rating: Optional[int] = Field(None, ge=1, le=5)
    communication_rating: Optional[int] = Field(None, ge=1, le=5)
    value_for_money_rating: Optional[int] = Field(None, ge=1, le=5)
    timeliness_rating: Optional[int] = Field(None, ge=1, le=5)

class ExpertReviewUpdate(BaseModel):
    """Schema for updating expert review"""
    rating: Optional[int] = Field(None, ge=1, le=5)
    review_title: Optional[str] = Field(None, max_length=200)
    review_text: Optional[str] = None
    
    # Category ratings
    analysis_quality_rating: Optional[int] = Field(None, ge=1, le=5)
    communication_rating: Optional[int] = Field(None, ge=1, le=5)
    value_for_money_rating: Optional[int] = Field(None, ge=1, le=5)
    timeliness_rating: Optional[int] = Field(None, ge=1, le=5)

class ExpertReviewResponse(BaseModel):
    """Expert review response"""
    id: UUID
    user_id: UUID
    expert_id: UUID
    subscription_id: Optional[UUID]
    
    # Review content
    rating: int
    review_title: Optional[str]
    review_text: Optional[str]
    
    # Category ratings
    analysis_quality_rating: Optional[int]
    communication_rating: Optional[int]
    value_for_money_rating: Optional[int]
    timeliness_rating: Optional[int]
    
    # Review status
    is_verified: bool
    is_featured: bool
    status: str
    
    # Engagement
    helpful_votes: int
    total_votes: int
    
    # User info (populated separately)
    user_name: Optional[str] = None
    user_avatar_url: Optional[str] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class NotificationResponse(BaseModel):
    """Notification response"""
    id: UUID
    expert_id: UUID
    user_id: Optional[UUID]
    
    # Notification details
    notification_type: str
    title: str
    message: str
    
    # Delivery channels
    send_email: bool
    send_push: bool
    send_sms: bool
    send_in_app: bool
    
    # Status tracking
    status: str
    sent_at: Optional[datetime]
    delivered_at: Optional[datetime]
    opened_at: Optional[datetime]
    clicked_at: Optional[datetime]
    
    # Related content
    related_pick_id: Optional[UUID]
    related_subscription_id: Optional[UUID]
    
    # Metadata
    metadata: Dict[str, Any]
    created_at: datetime
    
    class Config:
        from_attributes = True

class FollowResponse(BaseModel):
    """Expert follow response"""
    expert_id: UUID
    user_id: UUID
    followed_at: datetime
    
    # Expert info
    expert_name: str
    expert_slug: str
    expert_avatar_url: Optional[str]
    
    class Config:
        from_attributes = True