"""
Expert Picks schemas for Expert Analyst Marketplace
====================================================

Pydantic schemas for expert picks, predictions, and content management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field, validator

from .common import (
    BaseResponse, PaginatedResponse, GameInfo, OddsInfo, 
    PickAnalysis, OutcomeResult, EngagementMetrics
)

# Expert pick base schemas
class ExpertPickBase(BaseModel):
    """Base expert pick schema"""
    title: str = Field(min_length=10, max_length=300)
    
    # Event information
    sport: str = Field(min_length=2, max_length=50)
    league: Optional[str] = Field(None, max_length=100)
    event_description: str = Field(min_length=10)
    event_date: datetime
    
    # Pick details
    pick_type: str = Field(regex="^(spread|moneyline|total|prop_bet|futures|parlay|custom)$")
    pick_value: str = Field(min_length=1)
    recommended_stake: Optional[Decimal] = Field(None, gt=0)
    
    # Odds and analysis
    odds_when_posted: Optional[Decimal] = None
    odds_format: str = Field(default="american", regex="^(american|decimal|fractional)$")
    implied_probability: Optional[Decimal] = Field(None, ge=0, le=100)
    
    # Confidence and analysis
    confidence_level: int = Field(ge=1, le=10)
    analysis_text: str = Field(min_length=50)
    reasoning: Optional[str] = None
    key_factors: List[str] = []
    
    # Access control
    access_tier: str = Field(regex="^(free|basic|premium|vip)$")
    
    # Content visibility
    is_public: bool = False
    is_featured: bool = False
    is_premium: bool = False
    
    # Scheduling
    publish_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Categorization
    tags: List[str] = []
    categories: List[str] = []

class ExpertPickCreate(ExpertPickBase):
    """Schema for creating expert pick"""
    slug: str = Field(min_length=10, max_length=400, regex="^[a-z0-9-_]+$")
    required_tier_id: Optional[UUID] = None
    
    @validator('slug')
    def validate_slug(cls, v):
        if not v.replace('-', '').replace('_', '').replace('.', '').isalnum():
            raise ValueError('Slug must contain only lowercase letters, numbers, hyphens, underscores, and dots')
        return v
    
    @validator('expires_at')
    def validate_expiration(cls, v, values):
        if v and 'publish_at' in values and values['publish_at']:
            if v <= values['publish_at']:
                raise ValueError('Expiration must be after publish time')
        if v and 'event_date' in values and v >= values['event_date']:
            raise ValueError('Pick must expire before event starts')
        return v
    
    @validator('event_date')
    def validate_event_date(cls, v):
        if v <= datetime.now():
            raise ValueError('Event date must be in the future')
        return v

class ExpertPickUpdate(BaseModel):
    """Schema for updating expert pick (before event)"""
    title: Optional[str] = Field(None, min_length=10, max_length=300)
    event_description: Optional[str] = Field(None, min_length=10)
    pick_value: Optional[str] = Field(None, min_length=1)
    recommended_stake: Optional[Decimal] = Field(None, gt=0)
    
    # Odds updates
    odds_when_posted: Optional[Decimal] = None
    implied_probability: Optional[Decimal] = Field(None, ge=0, le=100)
    
    # Analysis updates
    confidence_level: Optional[int] = Field(None, ge=1, le=10)
    analysis_text: Optional[str] = Field(None, min_length=50)
    reasoning: Optional[str] = None
    key_factors: Optional[List[str]] = None
    
    # Access and visibility
    access_tier: Optional[str] = Field(None, regex="^(free|basic|premium|vip)$")
    is_public: Optional[bool] = None
    is_featured: Optional[bool] = None
    is_premium: Optional[bool] = None
    required_tier_id: Optional[UUID] = None
    
    # Scheduling
    publish_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Status
    status: Optional[str] = Field(None, regex="^(draft|scheduled|published|archived|cancelled)$")
    
    # Categorization
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None

class PickOutcomeUpdate(BaseModel):
    """Schema for updating pick outcome (admin/expert only)"""
    outcome: str = Field(regex="^(win|loss|push|cancelled|pending)$")
    outcome_notes: Optional[str] = None
    actual_odds: Optional[Decimal] = None
    profit_loss: Optional[Decimal] = None
    roi_percentage: Optional[Decimal] = None
    
    @validator('outcome')
    def validate_outcome_change(cls, v):
        # Add business rules for outcome changes
        return v

# Pick response schemas
class ExpertPickResponse(BaseModel):
    """Full expert pick response"""
    id: UUID
    expert_id: UUID
    title: str
    slug: str
    
    # Event information
    sport: str
    league: Optional[str]
    event_description: str
    event_date: datetime
    
    # Pick details
    pick_type: str
    pick_value: str
    recommended_stake: Optional[Decimal]
    
    # Odds and analysis
    odds_when_posted: Optional[Decimal]
    odds_format: str
    implied_probability: Optional[Decimal]
    
    # Confidence and analysis
    confidence_level: int
    analysis_text: str
    reasoning: Optional[str]
    key_factors: List[str]
    
    # Access control
    access_tier: str
    required_tier_id: Optional[UUID]
    
    # Content visibility
    is_public: bool
    is_featured: bool
    is_premium: bool
    
    # Scheduling and publication
    status: str
    publish_at: datetime
    expires_at: Optional[datetime]
    
    # Engagement metrics
    views_count: int
    likes_count: int
    shares_count: int
    comments_count: int
    
    # Pick outcome
    outcome: Optional[str]
    outcome_notes: Optional[str]
    settled_at: Optional[datetime]
    actual_odds: Optional[Decimal]
    profit_loss: Optional[Decimal]
    roi_percentage: Optional[Decimal]
    
    # Related picks
    parent_pick_id: Optional[UUID]
    related_picks: List[UUID]
    
    # Categorization
    tags: List[str]
    categories: List[str]
    
    # Expert info (populated separately)
    expert_name: Optional[str] = None
    expert_slug: Optional[str] = None
    expert_avatar_url: Optional[str] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ExpertPickPublicResponse(BaseModel):
    """Public expert pick response (limited access)"""
    id: UUID
    expert_id: UUID
    title: str
    slug: str
    
    # Event information
    sport: str
    league: Optional[str]
    event_description: str
    event_date: datetime
    
    # Pick type and basic info
    pick_type: str
    confidence_level: int
    
    # Access info
    access_tier: str
    is_public: bool
    is_featured: bool
    is_premium: bool
    
    # Basic engagement
    views_count: int
    likes_count: int
    
    # Outcome (if settled)
    outcome: Optional[str]
    settled_at: Optional[datetime]
    
    # Expert info
    expert_name: str
    expert_slug: str
    expert_avatar_url: Optional[str]
    
    # Timestamps
    published_at: datetime
    
    class Config:
        from_attributes = True

class ExpertPickSummary(BaseModel):
    """Pick summary for lists and feeds"""
    id: UUID
    expert_id: UUID
    title: str
    slug: str
    sport: str
    pick_type: str
    confidence_level: int
    access_tier: str
    outcome: Optional[str]
    publish_at: datetime
    event_date: datetime
    
    # Expert info
    expert_name: str
    expert_slug: str
    expert_avatar_url: Optional[str]
    
    class Config:
        from_attributes = True

class ExpertPickListResponse(PaginatedResponse[ExpertPickSummary]):
    """Paginated expert pick list response"""
    pass

# Pick access and security schemas
class PickAccessRequest(BaseModel):
    """Request to access premium pick content"""
    pick_id: UUID
    access_type: str = Field(default="view", regex="^(view|download|share)$")

class PickAccessResponse(BaseModel):
    """Pick access response with content"""
    pick: ExpertPickResponse
    access_granted: bool
    access_reason: Optional[str] = None
    subscription_required: Optional[Dict[str, Any]] = None
    remaining_access_count: Optional[int] = None

class PickAccessLog(BaseModel):
    """Pick access log entry"""
    id: UUID
    user_id: UUID
    pick_id: UUID
    access_type: str
    access_method: str
    counted_against_limit: bool
    accessed_at: datetime
    
    class Config:
        from_attributes = True

# Pick search and filtering schemas
class PickSearchParams(BaseModel):
    """Pick search parameters"""
    query: Optional[str] = None
    expert_id: Optional[UUID] = None
    sport: Optional[str] = None
    league: Optional[str] = None
    pick_type: Optional[str] = None
    access_tier: Optional[str] = None
    status: Optional[str] = None
    outcome: Optional[str] = None
    confidence_min: Optional[int] = Field(None, ge=1, le=10)
    confidence_max: Optional[int] = Field(None, ge=1, le=10)
    is_featured: Optional[bool] = None
    is_premium: Optional[bool] = None
    published_after: Optional[datetime] = None
    published_before: Optional[datetime] = None
    event_date_after: Optional[datetime] = None
    event_date_before: Optional[datetime] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    
    # Sorting
    sort_by: str = Field(default="publish_at", regex="^(publish_at|event_date|confidence_level|views_count|created_at)$")
    sort_direction: str = Field(default="desc", regex="^(asc|desc)$")

class PickFeedResponse(BaseModel):
    """Pick feed with various categories"""
    featured_picks: List[ExpertPickSummary]
    recent_picks: List[ExpertPickSummary]
    trending_picks: List[ExpertPickSummary]
    upcoming_events: List[ExpertPickSummary]
    your_experts_picks: Optional[List[ExpertPickSummary]] = None  # If authenticated

# Pick analytics schemas
class PickPerformanceAnalytics(BaseModel):
    """Pick performance analytics"""
    pick_id: UUID
    expert_id: UUID
    
    # Engagement metrics
    total_views: int
    unique_views: int
    view_sources: Dict[str, int]
    engagement_rate: float
    
    # Access patterns
    free_accesses: int
    premium_accesses: int
    subscription_accesses: int
    
    # Performance impact
    revenue_generated: Optional[Decimal]
    subscriptions_attributed: int
    
    # Time-based metrics
    views_by_hour: List[Dict[str, Any]]
    peak_engagement_time: Optional[datetime]

class ExpertPickAnalytics(BaseModel):
    """Expert's pick analytics summary"""
    expert_id: UUID
    total_picks: int
    published_picks: int
    draft_picks: int
    
    # Performance breakdown
    wins: int
    losses: int
    pushes: int
    pending: int
    win_rate: float
    
    # Engagement totals
    total_views: int
    total_likes: int
    total_shares: int
    average_engagement_rate: float
    
    # Revenue metrics
    total_revenue_from_picks: Decimal
    average_revenue_per_pick: Decimal
    
    # Content analysis
    picks_by_sport: Dict[str, int]
    picks_by_type: Dict[str, int]
    picks_by_confidence: Dict[str, int]
    average_confidence_level: float

# Pick management schemas
class BulkPickUpdate(BaseModel):
    """Bulk pick update schema"""
    pick_ids: List[UUID]
    updates: Dict[str, Any]
    reason: Optional[str] = None

class BulkPickResponse(BaseResponse):
    """Bulk pick operation response"""
    updated_count: int
    failed_updates: List[Dict[str, Any]]
    success_ids: List[UUID]

class PickModerationAction(BaseModel):
    """Pick moderation action"""
    action: str = Field(regex="^(approve|reject|flag|unflag|archive)$")
    reason: Optional[str] = None
    notes: Optional[str] = None

class PickModerationResponse(BaseResponse):
    """Pick moderation response"""
    pick_id: UUID
    action_taken: str
    moderator_id: UUID
    timestamp: datetime

# Pick recommendations and suggestions
class PickRecommendation(BaseModel):
    """Pick recommendation for user"""
    pick: ExpertPickSummary
    recommendation_score: float
    recommendation_reasons: List[str]
    expert_match_score: Optional[float] = None

class PickRecommendationResponse(BaseModel):
    """Pick recommendations response"""
    recommendations: List[PickRecommendation]
    recommendation_basis: str
    updated_at: datetime

# Pick templates and automation
class PickTemplate(BaseModel):
    """Pick template for recurring picks"""
    name: str
    sport: str
    pick_type: str
    default_access_tier: str
    analysis_template: str
    key_factors_template: List[str]
    
class AutoPickSettings(BaseModel):
    """Automated pick creation settings"""
    enabled: bool = False
    sports: List[str] = []
    pick_types: List[str] = []
    min_confidence_level: int = Field(ge=1, le=10)
    auto_publish: bool = False
    default_access_tier: str = "basic"