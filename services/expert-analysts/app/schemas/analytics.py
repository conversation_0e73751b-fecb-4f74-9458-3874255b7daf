"""
Analytics schemas for Expert Analyst Marketplace
================================================

Pydantic schemas for performance analytics, statistics, and reporting.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field

from .common import BaseResponse, PerformanceMetrics

class PerformanceStatsResponse(BaseModel):
    """Expert performance statistics response"""
    id: UUID
    expert_id: UUID
    
    # Time period
    period_type: str
    period_start: date
    period_end: date
    
    # Pick statistics
    total_picks: int
    winning_picks: int
    losing_picks: int
    push_picks: int
    pending_picks: int
    win_rate: Optional[float]
    
    # Financial performance
    total_profit_loss: Decimal
    average_roi: Decimal
    best_win: Decimal
    worst_loss: Decimal
    
    # Breakdown analysis
    pick_type_breakdown: Dict[str, Any]
    sport_breakdown: Dict[str, Any]
    
    # Confidence analysis
    high_confidence_wins: int
    high_confidence_total: int
    medium_confidence_wins: int
    medium_confidence_total: int
    low_confidence_wins: int
    low_confidence_total: int
    
    # Streak tracking
    current_win_streak: int
    current_loss_streak: int
    longest_win_streak: int
    longest_loss_streak: int
    
    # Engagement metrics
    avg_views_per_pick: Decimal
    avg_likes_per_pick: Decimal
    
    # Calculation timestamp
    calculated_at: datetime
    
    class Config:
        from_attributes = True

class LeaderboardEntry(BaseModel):
    """Leaderboard entry schema"""
    rank: int
    expert_id: UUID
    expert_name: str
    expert_slug: str
    expert_avatar_url: Optional[str]
    metric_value: float
    secondary_metrics: Dict[str, Any]
    
    class Config:
        from_attributes = True

class LeaderboardResponse(BaseResponse):
    """Leaderboard response"""
    leaderboard_type: str
    period: str
    entries: List[LeaderboardEntry]
    total_experts: int
    last_updated: datetime

class ExpertAnalyticsResponse(BaseModel):
    """Comprehensive expert analytics"""
    expert_id: UUID
    
    # Performance overview
    overall_performance: PerformanceStatsResponse
    recent_performance: PerformanceStatsResponse
    performance_trend: List[Dict[str, Any]]
    
    # Subscriber analytics
    subscriber_metrics: Dict[str, Any]
    subscription_growth: List[Dict[str, Any]]
    churn_analysis: Dict[str, Any]
    
    # Revenue analytics
    revenue_metrics: Dict[str, Any]
    revenue_trend: List[Dict[str, Any]]
    
    # Content analytics
    content_metrics: Dict[str, Any]
    engagement_metrics: Dict[str, Any]
    
    # Comparative analysis
    peer_comparison: Dict[str, Any]
    industry_benchmarks: Dict[str, Any]

class PlatformAnalyticsResponse(BaseModel):
    """Platform-wide analytics"""
    
    # Expert metrics
    total_experts: int
    verified_experts: int
    active_experts: int
    expert_growth_rate: float
    
    # Subscription metrics
    total_subscriptions: int
    active_subscriptions: int
    subscription_growth_rate: float
    
    # Revenue metrics
    total_mrr: Decimal
    total_arr: Decimal
    average_revenue_per_expert: Decimal
    revenue_growth_rate: float
    
    # Performance metrics
    platform_win_rate: float
    average_expert_rating: float
    total_picks_made: int
    
    # Engagement metrics
    total_pick_views: int
    average_engagement_rate: float
    
    # Time period
    period_start: date
    period_end: date
    generated_at: datetime