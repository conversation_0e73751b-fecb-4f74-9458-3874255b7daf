"""
Revenue schemas for Expert Analyst Marketplace
===============================================

Pydantic schemas for revenue sharing, payouts, and financial management.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field

from .common import BaseResponse, PaginatedResponse

class RevenueShareResponse(BaseModel):
    """Revenue sharing record response"""
    id: UUID
    expert_id: UUID
    subscription_id: Optional[UUID]
    
    # Revenue period
    revenue_period_start: date
    revenue_period_end: date
    
    # Revenue breakdown
    gross_revenue: Decimal
    platform_fee_percentage: Decimal
    platform_fee_amount: Decimal
    payment_processor_fee: Decimal
    net_revenue: Decimal
    final_payout_amount: Decimal
    
    # Payout status
    payout_status: str
    payout_method: Optional[str]
    payout_reference: Optional[str]
    
    # Tax and compliance
    tax_withholding_amount: Decimal
    tax_form_required: bool
    
    # Timing
    calculated_at: datetime
    scheduled_payout_date: Optional[date]
    actual_payout_date: Optional[date]
    
    # Metadata
    subscriber_count: int
    transaction_count: int
    
    class Config:
        from_attributes = True

class PayoutRequest(BaseModel):
    """Payout request schema"""
    expert_id: UUID
    amount: Decimal = Field(gt=0)
    payout_method: str = Field(regex="^(bank_transfer|paypal|stripe|check)$")
    payout_details: Dict[str, Any]
    notes: Optional[str] = None

class PayoutResponse(BaseModel):
    """Payout response"""
    id: UUID
    expert_id: UUID
    amount: Decimal
    payout_method: str
    status: str
    reference_id: Optional[str]
    processing_fee: Optional[Decimal]
    net_amount: Decimal
    
    requested_at: datetime
    processed_at: Optional[datetime]
    expected_arrival: Optional[date]
    
    class Config:
        from_attributes = True

class EarningsResponse(BaseModel):
    """Expert earnings summary"""
    expert_id: UUID
    
    # Current period
    current_period_earnings: Decimal
    pending_payouts: Decimal
    available_for_payout: Decimal
    
    # Historical
    total_earnings: Decimal
    total_payouts: Decimal
    
    # Breakdown
    subscription_revenue: Decimal
    bonus_earnings: Decimal
    referral_earnings: Decimal
    
    # Next payout
    next_payout_date: Optional[date]
    next_payout_amount: Optional[Decimal]
    
    # Tax info
    ytd_earnings: Decimal
    tax_withholding_ytd: Decimal
    
    class Config:
        from_attributes = True

class RevenueReportResponse(BaseModel):
    """Revenue report response"""
    report_id: UUID
    report_type: str
    period_start: date
    period_end: date
    
    # Summary metrics
    total_revenue: Decimal
    total_payouts: Decimal
    platform_fees: Decimal
    net_profit: Decimal
    
    # Expert breakdown
    expert_revenue_breakdown: List[Dict[str, Any]]
    
    # Trend data
    revenue_trend: List[Dict[str, Any]]
    
    generated_at: datetime
    
    class Config:
        from_attributes = True