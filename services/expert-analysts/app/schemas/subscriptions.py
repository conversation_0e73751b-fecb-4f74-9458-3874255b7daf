"""
Subscription schemas for Expert Analyst Marketplace
===================================================

Pydantic schemas for subscription tiers, user subscriptions, and billing.

Author: Claude-API
Date: 2025-07-21
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field, validator

from .common import BaseResponse, PaginatedResponse

# Subscription tier schemas
class SubscriptionTierBase(BaseModel):
    """Base subscription tier schema"""
    tier_name: str = Field(min_length=2, max_length=100)
    description: Optional[str] = None
    price_monthly: Decimal = Field(ge=0)
    price_yearly: Optional[Decimal] = Field(None, ge=0)
    discount_yearly_percentage: Decimal = Field(default=0, ge=0, le=100)
    free_trial_days: int = Field(default=0, ge=0, le=30)
    
    # Access permissions
    max_picks_per_month: Optional[int] = Field(None, gt=0)
    access_level: str = Field(regex="^(free|basic|premium|vip)$")
    features_included: List[str] = []
    picks_access_level: str = Field(default="basic", regex="^(basic|premium|exclusive)$")
    analysis_depth: str = Field(default="standard", regex="^(basic|standard|detailed|comprehensive)$")
    
    # Communication access
    allows_direct_messages: bool = False
    allows_live_chat: bool = False
    allows_priority_support: bool = False
    
    # Limits
    subscriber_limit: Optional[int] = Field(None, gt=0)

class SubscriptionTierCreate(SubscriptionTierBase):
    """Schema for creating subscription tier"""
    tier_slug: str = Field(min_length=3, max_length=150, regex="^[a-z0-9-_]+$")
    
    @validator('price_yearly')
    def validate_yearly_price(cls, v, values):
        if v is not None and 'price_monthly' in values:
            monthly_annual = values['price_monthly'] * 12
            if v >= monthly_annual:
                raise ValueError('Yearly price must be less than 12x monthly price to provide discount')
        return v

class SubscriptionTierUpdate(BaseModel):
    """Schema for updating subscription tier"""
    tier_name: Optional[str] = Field(None, min_length=2, max_length=100)
    description: Optional[str] = None
    price_monthly: Optional[Decimal] = Field(None, ge=0)
    price_yearly: Optional[Decimal] = Field(None, ge=0)
    discount_yearly_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    free_trial_days: Optional[int] = Field(None, ge=0, le=30)
    
    # Access permissions
    max_picks_per_month: Optional[int] = Field(None, gt=0)
    access_level: Optional[str] = Field(None, regex="^(free|basic|premium|vip)$")
    features_included: Optional[List[str]] = None
    picks_access_level: Optional[str] = Field(None, regex="^(basic|premium|exclusive)$")
    analysis_depth: Optional[str] = Field(None, regex="^(basic|standard|detailed|comprehensive)$")
    
    # Communication access
    allows_direct_messages: Optional[bool] = None
    allows_live_chat: Optional[bool] = None
    allows_priority_support: Optional[bool] = None
    
    # Status
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    
    # Limits
    subscriber_limit: Optional[int] = Field(None, gt=0)

class SubscriptionTierResponse(BaseModel):
    """Subscription tier response"""
    id: UUID
    expert_id: UUID
    tier_name: str
    tier_slug: str
    description: Optional[str]
    
    # Pricing
    price_monthly: Decimal
    price_yearly: Optional[Decimal]
    discount_yearly_percentage: Decimal
    free_trial_days: int
    
    # Access permissions
    max_picks_per_month: Optional[int]
    access_level: str
    features_included: List[str]
    picks_access_level: str
    analysis_depth: str
    
    # Communication access
    allows_direct_messages: bool
    allows_live_chat: bool
    allows_priority_support: bool
    
    # Status and ordering
    is_active: bool
    is_featured: bool
    display_order: int
    
    # Limits and current usage
    subscriber_limit: Optional[int]
    current_subscribers: int
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# User subscription schemas
class SubscriptionBase(BaseModel):
    """Base subscription schema"""
    subscription_type: str = Field(regex="^(monthly|yearly|lifetime)$")
    billing_cycle: str = Field(regex="^(monthly|yearly|one_time)$")
    auto_renew: bool = True

class SubscriptionCreate(SubscriptionBase):
    """Schema for creating new subscription"""
    expert_id: UUID
    tier_id: UUID
    payment_method_id: Optional[str] = None  # Stripe payment method ID
    promo_code: Optional[str] = None
    
    @validator('billing_cycle')
    def validate_billing_cycle(cls, v, values):
        if 'subscription_type' in values:
            subscription_type = values['subscription_type']
            if subscription_type == 'monthly' and v != 'monthly':
                raise ValueError('Monthly subscription must have monthly billing cycle')
            elif subscription_type == 'yearly' and v != 'yearly':
                raise ValueError('Yearly subscription must have yearly billing cycle')
            elif subscription_type == 'lifetime' and v != 'one_time':
                raise ValueError('Lifetime subscription must have one_time billing cycle')
        return v

class SubscriptionUpdate(BaseModel):
    """Schema for updating subscription"""
    auto_renew: Optional[bool] = None
    payment_method_id: Optional[str] = None

class SubscriptionUpgrade(BaseModel):
    """Schema for subscription tier upgrade/downgrade"""
    new_tier_id: UUID
    effective_date: Optional[date] = None  # If None, effective immediately
    prorate_charges: bool = True

class SubscriptionCancellation(BaseModel):
    """Schema for subscription cancellation"""
    cancellation_reason: Optional[str] = Field(None, max_length=100)
    effective_date: Optional[date] = None  # If None, cancel at end of current period
    feedback: Optional[str] = None

class SubscriptionResponse(BaseModel):
    """User subscription response"""
    id: UUID
    user_id: UUID
    expert_id: UUID
    tier_id: UUID
    
    # Subscription details
    subscription_type: str
    status: str
    
    # Billing information
    amount_paid: Decimal
    currency: str
    billing_cycle: str
    external_subscription_id: Optional[str]
    
    # Subscription period
    starts_at: datetime
    ends_at: datetime
    auto_renew: bool
    
    # Trial period
    is_trial: bool
    trial_ends_at: Optional[datetime]
    
    # Cancellation
    cancelled_at: Optional[datetime]
    cancellation_reason: Optional[str]
    
    # Usage tracking
    picks_accessed: int
    last_access_at: Optional[datetime]
    
    # Related data (populated separately)
    expert_name: Optional[str] = None
    tier_name: Optional[str] = None
    
    # Computed properties
    days_remaining: Optional[int] = None
    is_active: Optional[bool] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class SubscriptionListResponse(PaginatedResponse[SubscriptionResponse]):
    """Paginated subscription list response"""
    pass

class SubscriptionSummary(BaseModel):
    """Subscription summary for dashboard"""
    id: UUID
    expert_name: str
    expert_slug: str
    tier_name: str
    status: str
    ends_at: datetime
    days_remaining: int
    picks_accessed: int
    max_picks_per_month: Optional[int]
    
    class Config:
        from_attributes = True

# Subscription management schemas
class SubscriptionUsageResponse(BaseModel):
    """Subscription usage tracking"""
    subscription_id: UUID
    billing_period_start: date
    billing_period_end: date
    picks_accessed: int
    picks_remaining: Optional[int]
    usage_percentage: Optional[float]
    last_access_date: Optional[date]
    access_history: List[Dict[str, Any]]  # Recent access log

class SubscriptionBillingResponse(BaseModel):
    """Subscription billing information"""
    subscription_id: UUID
    current_period_start: date
    current_period_end: date
    amount_due: Decimal
    next_billing_date: date
    payment_method: Dict[str, Any]
    billing_history: List[Dict[str, Any]]
    upcoming_charges: List[Dict[str, Any]]

# Payment and billing schemas
class PaymentMethodInfo(BaseModel):
    """Payment method information"""
    id: str
    type: str  # card, bank_account, etc.
    last_four: Optional[str]
    brand: Optional[str]
    expires: Optional[str]
    is_default: bool

class InvoiceLineItem(BaseModel):
    """Invoice line item"""
    description: str
    amount: Decimal
    quantity: int = 1
    unit_price: Decimal
    tax_amount: Optional[Decimal] = None

class InvoiceResponse(BaseModel):
    """Invoice response"""
    id: str
    subscription_id: UUID
    invoice_number: str
    status: str
    amount_due: Decimal
    amount_paid: Decimal
    currency: str
    
    # Dates
    invoice_date: date
    due_date: date
    paid_date: Optional[date]
    
    # Line items
    line_items: List[InvoiceLineItem]
    subtotal: Decimal
    tax_total: Optional[Decimal]
    total: Decimal
    
    # Payment info
    payment_method: Optional[PaymentMethodInfo]
    
    class Config:
        from_attributes = True

# Promo codes and discounts
class PromoCodeInfo(BaseModel):
    """Promo code information"""
    code: str
    discount_type: str = Field(regex="^(percentage|fixed_amount|free_trial)$")
    discount_value: Decimal
    max_uses: Optional[int]
    current_uses: int
    expires_at: Optional[datetime]
    applicable_tiers: List[UUID] = []
    min_subscription_months: Optional[int] = None

class PromoCodeValidation(BaseModel):
    """Promo code validation result"""
    valid: bool
    discount_amount: Optional[Decimal]
    discount_description: Optional[str]
    error_message: Optional[str]

# Subscription analytics schemas
class SubscriptionAnalytics(BaseModel):
    """Subscription analytics for expert"""
    expert_id: UUID
    total_subscribers: int
    active_subscribers: int
    trial_subscribers: int
    cancelled_subscribers: int
    
    # Revenue metrics
    monthly_recurring_revenue: Decimal
    annual_recurring_revenue: Decimal
    average_revenue_per_user: Decimal
    
    # Growth metrics
    new_subscribers_this_month: int
    churn_rate: Optional[float]
    growth_rate: Optional[float]
    
    # Tier breakdown
    tier_distribution: Dict[str, int]
    tier_revenue_distribution: Dict[str, Decimal]

class PlatformSubscriptionAnalytics(BaseModel):
    """Platform-wide subscription analytics"""
    total_active_subscriptions: int
    total_mrr: Decimal
    total_arr: Decimal
    average_subscription_value: Decimal
    
    # Growth metrics
    new_subscriptions_this_month: int
    cancelled_subscriptions_this_month: int
    net_subscriber_growth: int
    overall_churn_rate: float
    
    # Expert performance
    top_experts_by_subscribers: List[Dict[str, Any]]
    top_experts_by_revenue: List[Dict[str, Any]]