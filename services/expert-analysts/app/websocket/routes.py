"""
WebSocket Routes for Expert Analyst Marketplace
================================================

WebSocket route registration and connection handling.

Author: Claude-API
Date: 2025-07-21
"""

from typing import Optional
from uuid import UUID

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from jose import jwt, JWTError
import structlog

from app.api.dependencies import get_settings
from .handlers import websocket_handler

logger = structlog.get_logger()

def register_websocket_routes(app: FastAPI):
    """Register WebSocket routes with the FastAPI application"""
    
    @app.websocket("/ws/experts/{expert_id}")
    async def expert_websocket_endpoint(websocket: WebSocket, expert_id: UUID):
        """
        WebSocket endpoint for expert-specific updates
        Provides real-time notifications for:
        - New subscriber activities
        - Pick performance updates
        - Revenue/earnings updates
        - Performance statistics changes
        """
        try:
            await websocket_handler.connect_expert(websocket, expert_id)
            
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                
                # Handle ping/pong for connection health
                if data == "ping":
                    await websocket.send_text("pong")
                
                # TODO: Handle other incoming message types if needed
                
        except WebSocketDisconnect:
            await websocket_handler.disconnect(websocket)
            logger.info("Expert WebSocket disconnected", expert_id=expert_id)
        except Exception as e:
            logger.error("Expert WebSocket error", error=str(e), expert_id=expert_id)
            await websocket_handler.disconnect(websocket)
    
    @app.websocket("/ws/subscriptions/{user_id}")
    async def subscription_websocket_endpoint(websocket: WebSocket, user_id: UUID):
        """
        WebSocket endpoint for user subscription updates
        Provides real-time notifications for:
        - Subscription status changes
        - New picks from subscribed experts
        - Subscription renewal reminders
        - Payment status updates
        """
        try:
            await websocket_handler.connect_user_subscriptions(websocket, user_id)
            
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                
                # Handle ping/pong for connection health
                if data == "ping":
                    await websocket.send_text("pong")
                
        except WebSocketDisconnect:
            await websocket_handler.disconnect(websocket)
            logger.info("Subscription WebSocket disconnected", user_id=user_id)
        except Exception as e:
            logger.error("Subscription WebSocket error", error=str(e), user_id=user_id)
            await websocket_handler.disconnect(websocket)
    
    @app.websocket("/ws/picks/notifications")
    async def pick_notifications_websocket_endpoint(
        websocket: WebSocket,
        token: Optional[str] = None
    ):
        """
        WebSocket endpoint for general pick notifications
        Provides real-time updates for:
        - New public picks
        - Featured pick highlights
        - Trending picks
        - Pick outcome updates
        """
        user_id = None
        
        # Optional authentication for personalized content
        if token:
            try:
                settings = get_settings()
                payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm])
                user_id_str = payload.get("sub")
                if user_id_str:
                    user_id = UUID(user_id_str)
            except (JWTError, ValueError):
                # Continue without authentication for public content
                pass
        
        try:
            await websocket_handler.connect_pick_notifications(websocket, user_id)
            
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                
                # Handle ping/pong for connection health
                if data == "ping":
                    await websocket.send_text("pong")
                
        except WebSocketDisconnect:
            await websocket_handler.disconnect(websocket)
            logger.info("Pick notifications WebSocket disconnected", user_id=user_id)
        except Exception as e:
            logger.error("Pick notifications WebSocket error", error=str(e), user_id=user_id)
            await websocket_handler.disconnect(websocket)
    
    @app.websocket("/ws/picks/{pick_id}")
    async def pick_specific_websocket_endpoint(websocket: WebSocket, pick_id: UUID):
        """
        WebSocket endpoint for specific pick updates
        Provides real-time updates for:
        - Pick status changes
        - Odds movements
        - Analysis updates
        - Outcome/settlement notifications
        """
        try:
            await websocket.accept()
            
            # Add to pick-specific connections
            if pick_id not in websocket_handler.pick_connections:
                websocket_handler.pick_connections[pick_id] = set()
            websocket_handler.pick_connections[pick_id].add(websocket)
            
            logger.info("Pick WebSocket connected", pick_id=pick_id)
            
            # Send welcome message
            await websocket.send_text(f'{{"type": "connection_established", "pick_id": "{pick_id}", "message": "Connected to pick updates"}}')
            
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                
                # Handle ping/pong for connection health
                if data == "ping":
                    await websocket.send_text("pong")
                
        except WebSocketDisconnect:
            await websocket_handler.disconnect(websocket)
            logger.info("Pick WebSocket disconnected", pick_id=pick_id)
        except Exception as e:
            logger.error("Pick WebSocket error", error=str(e), pick_id=pick_id)
            await websocket_handler.disconnect(websocket)
    
    @app.get("/ws/stats")
    async def websocket_stats():
        """
        Get WebSocket connection statistics for monitoring
        """
        return websocket_handler.get_connection_stats()
    
    # Health check for WebSocket service
    @app.get("/ws/health")
    async def websocket_health():
        """WebSocket service health check"""
        stats = websocket_handler.get_connection_stats()
        return {
            "status": "healthy",
            "service": "websocket",
            "connections": stats,
            "message": "WebSocket service is operational"
        }

# Utility functions for broadcasting from API endpoints
async def broadcast_new_pick(expert_id: UUID, pick_data: dict):
    """Broadcast new pick creation"""
    await websocket_handler.broadcast_new_pick(expert_id, pick_data)

async def broadcast_pick_update(expert_id: UUID, pick_id: UUID, update_data: dict):
    """Broadcast pick updates"""
    await websocket_handler.broadcast_pick_update(expert_id, pick_id, update_data)

async def broadcast_pick_outcome(expert_id: UUID, pick_id: UUID, outcome_data: dict):
    """Broadcast pick outcome"""
    await websocket_handler.broadcast_pick_outcome(expert_id, pick_id, outcome_data)

async def broadcast_subscription_update(user_id: UUID, expert_id: UUID, subscription_data: dict):
    """Broadcast subscription changes"""
    await websocket_handler.broadcast_subscription_update(user_id, expert_id, subscription_data)

async def broadcast_expert_notification(expert_id: UUID, notification_data: dict):
    """Broadcast expert notifications"""
    await websocket_handler.broadcast_expert_notification(expert_id, notification_data)

async def broadcast_performance_update(expert_id: UUID, performance_data: dict):
    """Broadcast performance updates"""
    await websocket_handler.broadcast_performance_update(expert_id, performance_data)

async def broadcast_revenue_update(expert_id: UUID, revenue_data: dict):
    """Broadcast revenue updates"""
    await websocket_handler.broadcast_revenue_update(expert_id, revenue_data)