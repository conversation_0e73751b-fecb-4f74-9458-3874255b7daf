"""
WebSocket Handlers for Expert Analyst Marketplace
==================================================

Real-time communication handlers for expert notifications and updates.

Author: Claude-API
Date: 2025-07-21
"""

import json
from typing import Dict, Set, Optional
from uuid import UUID
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import structlog

from app.database.models import Expert, ExpertPick, Subscription

logger = structlog.get_logger()

class ExpertWebSocketHandler:
    """WebSocket handler for expert analyst marketplace real-time features"""
    
    def __init__(self):
        # Connection pools
        self.expert_connections: Dict[UUID, Set[WebSocket]] = {}
        self.user_connections: Dict[UUID, Set[WebSocket]] = {}
        self.pick_connections: Dict[UUID, Set[WebSocket]] = {}
        self.general_connections: Set[WebSocket] = set()
    
    async def connect_expert(self, websocket: WebSocket, expert_id: UUID):
        """Connect expert to their personal channel"""
        await websocket.accept()
        
        if expert_id not in self.expert_connections:
            self.expert_connections[expert_id] = set()
        
        self.expert_connections[expert_id].add(websocket)
        
        logger.info("Expert WebSocket connected", expert_id=expert_id)
        
        # Send welcome message
        await self._send_message(websocket, {
            "type": "connection_established",
            "expert_id": str(expert_id),
            "timestamp": datetime.now().isoformat(),
            "message": "Connected to expert channel"
        })
    
    async def connect_user_subscriptions(self, websocket: WebSocket, user_id: UUID):
        """Connect user to subscription updates channel"""
        await websocket.accept()
        
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        
        self.user_connections[user_id].add(websocket)
        
        logger.info("User WebSocket connected", user_id=user_id)
        
        # Send welcome message
        await self._send_message(websocket, {
            "type": "connection_established",
            "user_id": str(user_id),
            "timestamp": datetime.now().isoformat(),
            "message": "Connected to subscription updates"
        })
    
    async def connect_pick_notifications(self, websocket: WebSocket, user_id: Optional[UUID] = None):
        """Connect to general pick notifications channel"""
        await websocket.accept()
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(websocket)
        else:
            self.general_connections.add(websocket)
        
        logger.info("Pick notifications WebSocket connected", user_id=user_id)
        
        # Send welcome message
        await self._send_message(websocket, {
            "type": "connection_established",
            "timestamp": datetime.now().isoformat(),
            "message": "Connected to pick notifications"
        })
    
    async def disconnect(self, websocket: WebSocket):
        """Disconnect WebSocket from all channels"""
        # Remove from expert connections
        for expert_id, connections in self.expert_connections.items():
            connections.discard(websocket)
            if not connections:
                del self.expert_connections[expert_id]
        
        # Remove from user connections
        for user_id, connections in self.user_connections.items():
            connections.discard(websocket)
            if not connections:
                del self.user_connections[user_id]
        
        # Remove from pick connections
        for pick_id, connections in self.pick_connections.items():
            connections.discard(websocket)
            if not connections:
                del self.pick_connections[pick_id]
        
        # Remove from general connections
        self.general_connections.discard(websocket)
        
        logger.info("WebSocket disconnected")
    
    async def broadcast_new_pick(self, expert_id: UUID, pick_data: dict):
        """Broadcast new pick to subscribers"""
        message = {
            "type": "new_pick",
            "expert_id": str(expert_id),
            "pick": pick_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to expert's channel
        await self._broadcast_to_expert(expert_id, message)
        
        # Send to general pick feed
        await self._broadcast_to_general(message)
        
        logger.info("New pick broadcasted", expert_id=expert_id, pick_id=pick_data.get("id"))
    
    async def broadcast_pick_update(self, expert_id: UUID, pick_id: UUID, update_data: dict):
        """Broadcast pick updates"""
        message = {
            "type": "pick_update",
            "expert_id": str(expert_id),
            "pick_id": str(pick_id),
            "update": update_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to expert's channel
        await self._broadcast_to_expert(expert_id, message)
        
        # Send to pick-specific channel
        if pick_id in self.pick_connections:
            await self._broadcast_to_pick(pick_id, message)
        
        logger.info("Pick update broadcasted", expert_id=expert_id, pick_id=pick_id)
    
    async def broadcast_pick_outcome(self, expert_id: UUID, pick_id: UUID, outcome_data: dict):
        """Broadcast pick outcome/settlement"""
        message = {
            "type": "pick_outcome",
            "expert_id": str(expert_id),
            "pick_id": str(pick_id),
            "outcome": outcome_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to expert's channel
        await self._broadcast_to_expert(expert_id, message)
        
        # Send to general feed for trending outcomes
        await self._broadcast_to_general(message)
        
        logger.info("Pick outcome broadcasted", expert_id=expert_id, pick_id=pick_id, outcome=outcome_data.get("outcome"))
    
    async def broadcast_subscription_update(self, user_id: UUID, expert_id: UUID, subscription_data: dict):
        """Broadcast subscription status changes"""
        message = {
            "type": "subscription_update",
            "user_id": str(user_id),
            "expert_id": str(expert_id),
            "subscription": subscription_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to user's channel
        await self._broadcast_to_user(user_id, message)
        
        # Send to expert's channel
        await self._broadcast_to_expert(expert_id, message)
        
        logger.info("Subscription update broadcasted", user_id=user_id, expert_id=expert_id)
    
    async def broadcast_expert_notification(self, expert_id: UUID, notification_data: dict):
        """Broadcast notifications to expert"""
        message = {
            "type": "expert_notification",
            "expert_id": str(expert_id),
            "notification": notification_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_expert(expert_id, message)
        
        logger.info("Expert notification broadcasted", expert_id=expert_id)
    
    async def broadcast_performance_update(self, expert_id: UUID, performance_data: dict):
        """Broadcast performance statistics updates"""
        message = {
            "type": "performance_update",
            "expert_id": str(expert_id),
            "performance": performance_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_expert(expert_id, message)
        
        logger.info("Performance update broadcasted", expert_id=expert_id)
    
    async def broadcast_revenue_update(self, expert_id: UUID, revenue_data: dict):
        """Broadcast revenue and earnings updates"""
        message = {
            "type": "revenue_update",
            "expert_id": str(expert_id),
            "revenue": revenue_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_expert(expert_id, message)
        
        logger.info("Revenue update broadcasted", expert_id=expert_id)
    
    # Private helper methods
    async def _broadcast_to_expert(self, expert_id: UUID, message: dict):
        """Send message to all connections for specific expert"""
        if expert_id in self.expert_connections:
            connections_to_remove = set()
            for websocket in self.expert_connections[expert_id]:
                try:
                    await self._send_message(websocket, message)
                except WebSocketDisconnect:
                    connections_to_remove.add(websocket)
            
            # Clean up disconnected websockets
            for websocket in connections_to_remove:
                self.expert_connections[expert_id].discard(websocket)
            
            if not self.expert_connections[expert_id]:
                del self.expert_connections[expert_id]
    
    async def _broadcast_to_user(self, user_id: UUID, message: dict):
        """Send message to all connections for specific user"""
        if user_id in self.user_connections:
            connections_to_remove = set()
            for websocket in self.user_connections[user_id]:
                try:
                    await self._send_message(websocket, message)
                except WebSocketDisconnect:
                    connections_to_remove.add(websocket)
            
            # Clean up disconnected websockets
            for websocket in connections_to_remove:
                self.user_connections[user_id].discard(websocket)
            
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
    
    async def _broadcast_to_pick(self, pick_id: UUID, message: dict):
        """Send message to all connections for specific pick"""
        if pick_id in self.pick_connections:
            connections_to_remove = set()
            for websocket in self.pick_connections[pick_id]:
                try:
                    await self._send_message(websocket, message)
                except WebSocketDisconnect:
                    connections_to_remove.add(websocket)
            
            # Clean up disconnected websockets
            for websocket in connections_to_remove:
                self.pick_connections[pick_id].discard(websocket)
            
            if not self.pick_connections[pick_id]:
                del self.pick_connections[pick_id]
    
    async def _broadcast_to_general(self, message: dict):
        """Send message to all general connections"""
        connections_to_remove = set()
        for websocket in self.general_connections:
            try:
                await self._send_message(websocket, message)
            except WebSocketDisconnect:
                connections_to_remove.add(websocket)
        
        # Clean up disconnected websockets
        for websocket in connections_to_remove:
            self.general_connections.discard(websocket)
    
    async def _send_message(self, websocket: WebSocket, message: dict):
        """Send JSON message to websocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error("Failed to send WebSocket message", error=str(e))
            raise WebSocketDisconnect()
    
    # Statistics and monitoring
    def get_connection_stats(self) -> dict:
        """Get connection statistics for monitoring"""
        return {
            "expert_connections": sum(len(connections) for connections in self.expert_connections.values()),
            "user_connections": sum(len(connections) for connections in self.user_connections.values()),
            "pick_connections": sum(len(connections) for connections in self.pick_connections.values()),
            "general_connections": len(self.general_connections),
            "total_connections": (
                sum(len(connections) for connections in self.expert_connections.values()) +
                sum(len(connections) for connections in self.user_connections.values()) +
                sum(len(connections) for connections in self.pick_connections.values()) +
                len(self.general_connections)
            )
        }

# Global WebSocket handler instance
websocket_handler = ExpertWebSocketHandler()