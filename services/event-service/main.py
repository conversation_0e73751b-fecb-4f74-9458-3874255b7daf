"""
BetBet Event Service
===================

Centralized event service for notifications, platform activities, and real-time updates.
Handles user notifications, system events, and cross-platform activity tracking.
"""

import os
import sys
import logging
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from enum import Enum

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import asyncpg
import redis.asyncio as redis
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add shared directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings

# Event Types
class EventType(str, Enum):
    USER_REGISTRATION = "user_registration"
    USER_LOGIN = "user_login"
    BET_PLACED = "bet_placed"
    BET_WON = "bet_won"
    BET_LOST = "bet_lost"
    GAME_STARTED = "game_started"
    GAME_COMPLETED = "game_completed"
    ACHIEVEMENT_UNLOCKED = "achievement_unlocked"
    TIER_UPGRADED = "tier_upgraded"
    DEPOSIT_COMPLETED = "deposit_completed"
    WITHDRAWAL_COMPLETED = "withdrawal_completed"
    MARKET_CREATED = "market_created"
    MARKET_RESOLVED = "market_resolved"
    EXPERT_PICK_PUBLISHED = "expert_pick_published"
    LEADERBOARD_POSITION_CHANGED = "leaderboard_position_changed"
    SYSTEM_MAINTENANCE = "system_maintenance"
    SYSTEM_ALERT = "system_alert"

class NotificationPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class NotificationChannel(str, Enum):
    IN_APP = "in_app"
    EMAIL = "email"
    PUSH = "push"
    SMS = "sms"
    WEBSOCKET = "websocket"

# Pydantic Models
class EventCreate(BaseModel):
    event_type: EventType
    user_id: Optional[str] = None
    title: str
    description: str
    data: Dict[str, Any] = Field(default_factory=dict)
    priority: NotificationPriority = NotificationPriority.MEDIUM
    channels: List[NotificationChannel] = Field(default_factory=lambda: [NotificationChannel.IN_APP])
    expires_at: Optional[datetime] = None

class Event(BaseModel):
    id: str
    event_type: EventType
    user_id: Optional[str]
    title: str
    description: str
    data: Dict[str, Any]
    priority: NotificationPriority
    channels: List[NotificationChannel]
    is_read: bool = False
    created_at: datetime
    expires_at: Optional[datetime]

class NotificationPreferences(BaseModel):
    user_id: str
    email_enabled: bool = True
    push_enabled: bool = True
    sms_enabled: bool = False
    in_app_enabled: bool = True
    event_types: List[EventType] = Field(default_factory=list)

# FastAPI App
app = FastAPI(
    title="BetBet Event Service",
    description="Centralized event service for notifications and platform activities",
    version="1.0.0"
)

# CORS Configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
db_pool = None
redis_client = None
websocket_connections: Dict[str, List[WebSocket]] = {}

# Database initialization
async def init_database():
    global db_pool
    try:
        db_settings = DatabaseSettings()
        db_pool = await initialize_database(db_settings)
        logger.info("Event service database initialized successfully")
        
        # Create tables if they don't exist
        await create_tables()
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

async def create_tables():
    """Create event service tables"""
    async with db_pool.acquire() as conn:
        # Events table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS events.platform_events (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                event_type VARCHAR(50) NOT NULL,
                user_id VARCHAR(255),
                title VARCHAR(255) NOT NULL,
                description TEXT,
                data JSONB DEFAULT '{}',
                priority VARCHAR(20) DEFAULT 'medium',
                channels JSONB DEFAULT '["in_app"]',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                expires_at TIMESTAMP WITH TIME ZONE,
                INDEX (user_id),
                INDEX (event_type),
                INDEX (created_at),
                INDEX (is_read)
            )
        """)
        
        # Notification preferences table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS events.notification_preferences (
                user_id VARCHAR(255) PRIMARY KEY,
                email_enabled BOOLEAN DEFAULT TRUE,
                push_enabled BOOLEAN DEFAULT TRUE,
                sms_enabled BOOLEAN DEFAULT FALSE,
                in_app_enabled BOOLEAN DEFAULT TRUE,
                event_types JSONB DEFAULT '[]',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        logger.info("Event service tables created successfully")

# Redis initialization
async def init_redis():
    global redis_client
    try:
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Redis connection established")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None

# WebSocket Manager
class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.connections:
            self.connections[user_id] = []
        self.connections[user_id].append(websocket)
        logger.info(f"WebSocket connected for user {user_id}")
    
    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.connections:
            self.connections[user_id].remove(websocket)
            if not self.connections[user_id]:
                del self.connections[user_id]
        logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def send_to_user(self, user_id: str, message: dict):
        if user_id in self.connections:
            for websocket in self.connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except:
                    # Remove dead connections
                    self.connections[user_id].remove(websocket)

websocket_manager = WebSocketManager()

# Health Check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "event-service",
        "version": "1.0.0",
        "database": "connected" if db_pool else "disconnected",
        "redis": "connected" if redis_client else "disconnected",
        "timestamp": datetime.utcnow().isoformat()
    }

# Event Endpoints
@app.post("/api/v1/events", response_model=Event)
async def create_event(event_data: EventCreate):
    """Create a new platform event"""
    try:
        event_id = str(uuid4())
        
        async with db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO events.platform_events 
                (id, event_type, user_id, title, description, data, priority, channels, expires_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, event_id, event_data.event_type, event_data.user_id, event_data.title,
                event_data.description, json.dumps(event_data.data), event_data.priority,
                json.dumps(event_data.channels), event_data.expires_at)
        
        # Send real-time notification if WebSocket channel is enabled
        if NotificationChannel.WEBSOCKET in event_data.channels and event_data.user_id:
            await websocket_manager.send_to_user(event_data.user_id, {
                "type": "notification",
                "event_type": event_data.event_type,
                "title": event_data.title,
                "description": event_data.description,
                "priority": event_data.priority,
                "data": event_data.data,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # Cache in Redis for quick access
        if redis_client and event_data.user_id:
            await redis_client.lpush(f"user_events:{event_data.user_id}", event_id)
            await redis_client.expire(f"user_events:{event_data.user_id}", 86400)  # 24 hours
        
        logger.info(f"Event created: {event_id} for user {event_data.user_id}")
        
        return Event(
            id=event_id,
            event_type=event_data.event_type,
            user_id=event_data.user_id,
            title=event_data.title,
            description=event_data.description,
            data=event_data.data,
            priority=event_data.priority,
            channels=event_data.channels,
            created_at=datetime.utcnow(),
            expires_at=event_data.expires_at
        )
        
    except Exception as e:
        logger.error(f"Failed to create event: {e}")
        raise HTTPException(status_code=500, detail="Failed to create event")

# Startup event
@app.on_event("startup")
async def startup_event():
    await init_database()
    await init_redis()
    logger.info("Event service started successfully")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    logger.info("Event service shutdown complete")

@app.get("/api/v1/events/user/{user_id}")
async def get_user_events(user_id: str, limit: int = 20, offset: int = 0, unread_only: bool = False):
    """Get events for a specific user"""
    try:
        where_clause = "WHERE user_id = $1"
        params = [user_id]

        if unread_only:
            where_clause += " AND is_read = FALSE"

        where_clause += " AND (expires_at IS NULL OR expires_at > NOW())"

        async with db_pool.acquire() as conn:
            events = await conn.fetch(f"""
                SELECT * FROM events.platform_events
                {where_clause}
                ORDER BY created_at DESC
                LIMIT $2 OFFSET $3
            """, *params, limit, offset)

            return {
                "events": [dict(event) for event in events],
                "total": len(events),
                "unread_count": await get_unread_count(user_id)
            }

    except Exception as e:
        logger.error(f"Failed to get user events: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve events")

@app.put("/api/v1/events/{event_id}/read")
async def mark_event_read(event_id: str, user_id: str):
    """Mark an event as read"""
    try:
        async with db_pool.acquire() as conn:
            result = await conn.execute("""
                UPDATE events.platform_events
                SET is_read = TRUE
                WHERE id = $1 AND user_id = $2
            """, event_id, user_id)

            if result == "UPDATE 0":
                raise HTTPException(status_code=404, detail="Event not found")

            return {"success": True}

    except Exception as e:
        logger.error(f"Failed to mark event as read: {e}")
        raise HTTPException(status_code=500, detail="Failed to update event")

@app.post("/api/v1/events/broadcast")
async def broadcast_event(event_data: EventCreate):
    """Broadcast an event to all users or specific user groups"""
    try:
        # Create system-wide event
        event_id = str(uuid4())

        async with db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO events.platform_events
                (id, event_type, title, description, data, priority, channels, expires_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """, event_id, event_data.event_type, event_data.title,
                event_data.description, json.dumps(event_data.data), event_data.priority,
                json.dumps(event_data.channels), event_data.expires_at)

        # Send to all connected WebSocket users
        if NotificationChannel.WEBSOCKET in event_data.channels:
            broadcast_message = {
                "type": "broadcast",
                "event_type": event_data.event_type,
                "title": event_data.title,
                "description": event_data.description,
                "priority": event_data.priority,
                "data": event_data.data,
                "timestamp": datetime.utcnow().isoformat()
            }

            for user_id, connections in websocket_manager.connections.items():
                for websocket in connections:
                    try:
                        await websocket.send_text(json.dumps(broadcast_message))
                    except:
                        pass

        logger.info(f"Broadcast event created: {event_id}")
        return {"success": True, "event_id": event_id}

    except Exception as e:
        logger.error(f"Failed to broadcast event: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast event")

@app.websocket("/ws/events/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time event notifications"""
    await websocket_manager.connect(websocket, user_id)
    try:
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": "Event notifications connected",
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat()
        }))

        # Keep connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()
            # Echo back for heartbeat
            await websocket.send_text(json.dumps({
                "type": "heartbeat",
                "timestamp": datetime.utcnow().isoformat()
            }))

    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, user_id)

async def get_unread_count(user_id: str) -> int:
    """Get count of unread events for a user"""
    try:
        async with db_pool.acquire() as conn:
            result = await conn.fetchval("""
                SELECT COUNT(*) FROM events.platform_events
                WHERE user_id = $1 AND is_read = FALSE
                AND (expires_at IS NULL OR expires_at > NOW())
            """, user_id)
            return result or 0
    except:
        return 0

# Helper function to create common platform events
async def create_platform_event(event_type: EventType, user_id: str, title: str,
                               description: str, data: Dict = None,
                               priority: NotificationPriority = NotificationPriority.MEDIUM):
    """Helper function to create platform events from other services"""
    event_data = EventCreate(
        event_type=event_type,
        user_id=user_id,
        title=title,
        description=description,
        data=data or {},
        priority=priority,
        channels=[NotificationChannel.IN_APP, NotificationChannel.WEBSOCKET]
    )
    return await create_event(event_data)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
