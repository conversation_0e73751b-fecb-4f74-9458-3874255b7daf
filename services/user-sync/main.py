"""
BetBet User Sync Service
========================

Synchronizes Clerk users with platform database and manages user profiles.
Ensures all Clerk users have corresponding platform profiles for leaderboards.
"""

import os
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

import asyncpg
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
from clerk_backend_api import Clerk
from clerk_backend_api.models import User as ClerkUser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/betbet_db")
CLERK_SECRET_KEY = os.getenv("CLERK_SECRET_KEY")
SERVICE_PORT = int(os.getenv("SERVICE_PORT", "8005"))

# Initialize Clerk client
clerk = Clerk(bearer_auth=CLERK_SECRET_KEY) if CLERK_SECRET_KEY else None

app = FastAPI(
    title="BetBet User Sync Service",
    description="Synchronizes Clerk users with platform database",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class PlatformUser(BaseModel):
    id: str
    clerk_id: str
    username: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    avatar_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    is_verified: bool = True

class UserSyncResult(BaseModel):
    synced_users: int
    created_users: int
    updated_users: int
    errors: List[str] = []

class LeaderboardProfile(BaseModel):
    user_id: str
    gaming_stats: Dict[str, Any] = {}
    betting_stats: Dict[str, Any] = {}
    trading_performance: Dict[str, Any] = {}
    analyst_metrics: Dict[str, Any] = {}
    unified_platform_score: float = 0.0
    platform_tier: str = "Bronze"
    cross_platform_consistency_score: float = 0.0

# Database connection
async def get_db_connection():
    """Get database connection"""
    return await asyncpg.connect(DATABASE_URL)

class UserSyncService:
    """Service for syncing Clerk users with platform database"""
    
    def __init__(self):
        self.db_pool = None
    
    async def init_db_pool(self):
        """Initialize database connection pool"""
        try:
            self.db_pool = await asyncpg.create_pool(
                DATABASE_URL,
                min_size=2,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    async def get_clerk_users(self) -> List[ClerkUser]:
        """Fetch all users from Clerk"""
        if not clerk:
            raise HTTPException(status_code=500, detail="Clerk not configured")
        
        try:
            users = []
            offset = 0
            limit = 100
            
            while True:
                response = clerk.users.list(limit=limit, offset=offset)
                batch_users = response.data if hasattr(response, 'data') else []
                
                if not batch_users:
                    break
                    
                users.extend(batch_users)
                offset += limit
                
                # Safety break
                if len(users) > 10000:
                    logger.warning("Retrieved over 10,000 users, stopping")
                    break
            
            logger.info(f"Retrieved {len(users)} users from Clerk")
            return users
            
        except Exception as e:
            logger.error(f"Failed to fetch Clerk users: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch users: {str(e)}")
    
    async def sync_user_to_platform(self, clerk_user: ClerkUser) -> bool:
        """Sync a single Clerk user to platform database"""
        try:
            async with self.db_pool.acquire() as conn:
                # Check if user already exists
                existing_user = await conn.fetchrow(
                    "SELECT id, clerk_id FROM public.users WHERE clerk_id = $1",
                    clerk_user.id
                )
                
                # Prepare user data
                email = clerk_user.email_addresses[0].email_address if clerk_user.email_addresses else ""
                username = clerk_user.username or f"user_{clerk_user.id[:8]}"
                first_name = clerk_user.first_name or ""
                last_name = clerk_user.last_name or ""
                avatar_url = clerk_user.image_url or ""
                
                if existing_user:
                    # Update existing user
                    await conn.execute("""
                        UPDATE public.users 
                        SET username = $2, email = $3, first_name = $4, last_name = $5, 
                            avatar_url = $6, updated_at = NOW()
                        WHERE clerk_id = $1
                    """, clerk_user.id, username, email, first_name, last_name, avatar_url)
                    
                    user_id = existing_user['id']
                    logger.debug(f"Updated existing user: {user_id}")
                    
                else:
                    # Create new user
                    user_id = str(uuid4())
                    await conn.execute("""
                        INSERT INTO public.users 
                        (id, clerk_id, username, email, first_name, last_name, avatar_url, 
                         is_active, is_verified, created_at, updated_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, true, true, NOW(), NOW())
                    """, user_id, clerk_user.id, username, email, first_name, last_name, avatar_url)
                    
                    logger.info(f"Created new user: {user_id} for Clerk ID: {clerk_user.id}")
                
                # Ensure leaderboard profile exists
                await self.ensure_leaderboard_profile(conn, user_id)
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to sync user {clerk_user.id}: {e}")
            return False
    
    async def ensure_leaderboard_profile(self, conn, user_id: str):
        """Ensure user has a leaderboard profile"""
        try:
            # Check if leaderboard profile exists
            existing_profile = await conn.fetchrow(
                "SELECT user_id FROM leaderboards.unified_user_performance WHERE user_id = $1",
                user_id
            )
            
            if not existing_profile:
                # Create initial leaderboard profile
                await conn.execute("""
                    INSERT INTO leaderboards.unified_user_performance 
                    (user_id, gaming_stats, betting_stats, trading_performance, analyst_metrics,
                     unified_platform_score, platform_tier, cross_platform_consistency_score,
                     created_at, updated_at)
                    VALUES ($1, '{}', '{}', '{}', '{}', 0.0, 'Bronze', 0.0, NOW(), NOW())
                """, user_id)
                
                logger.debug(f"Created leaderboard profile for user: {user_id}")
                
        except Exception as e:
            logger.error(f"Failed to create leaderboard profile for {user_id}: {e}")
    
    async def sync_all_users(self) -> UserSyncResult:
        """Sync all Clerk users to platform database"""
        result = UserSyncResult(synced_users=0, created_users=0, updated_users=0)
        
        try:
            # Get all Clerk users
            clerk_users = await self.get_clerk_users()
            
            # Get existing platform users
            async with self.db_pool.acquire() as conn:
                existing_users = await conn.fetch(
                    "SELECT clerk_id FROM public.users WHERE clerk_id IS NOT NULL"
                )
                existing_clerk_ids = {row['clerk_id'] for row in existing_users}
            
            # Sync each user
            for clerk_user in clerk_users:
                try:
                    is_new = clerk_user.id not in existing_clerk_ids
                    success = await self.sync_user_to_platform(clerk_user)
                    
                    if success:
                        result.synced_users += 1
                        if is_new:
                            result.created_users += 1
                        else:
                            result.updated_users += 1
                    else:
                        result.errors.append(f"Failed to sync user {clerk_user.id}")
                        
                except Exception as e:
                    error_msg = f"Error syncing user {clerk_user.id}: {str(e)}"
                    result.errors.append(error_msg)
                    logger.error(error_msg)
            
            logger.info(f"User sync completed: {result.synced_users} synced, {result.created_users} created, {result.updated_users} updated")
            return result
            
        except Exception as e:
            logger.error(f"Failed to sync users: {e}")
            result.errors.append(f"Sync failed: {str(e)}")
            return result

# Initialize service
user_sync_service = UserSyncService()

@app.on_event("startup")
async def startup_event():
    """Initialize service on startup"""
    await user_sync_service.init_db_pool()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    if user_sync_service.db_pool:
        await user_sync_service.db_pool.close()

# API endpoints
@app.get("/")
def read_root():
    return {
        "service": "BetBet User Sync Service",
        "status": "running",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "sync": "/api/v1/sync/users",
            "status": "/api/v1/sync/status"
        }
    }

@app.get("/health")
def health_check():
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

@app.post("/api/v1/sync/users", response_model=UserSyncResult)
async def sync_users(background_tasks: BackgroundTasks):
    """Sync all Clerk users to platform database"""
    try:
        result = await user_sync_service.sync_all_users()
        return result
    except Exception as e:
        logger.error(f"User sync failed: {e}")
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")

@app.get("/api/v1/sync/status")
async def get_sync_status():
    """Get current sync status"""
    try:
        async with user_sync_service.db_pool.acquire() as conn:
            # Count platform users
            platform_users = await conn.fetchval("SELECT COUNT(*) FROM public.users")
            clerk_users_count = await conn.fetchval("SELECT COUNT(*) FROM public.users WHERE clerk_id IS NOT NULL")
            leaderboard_profiles = await conn.fetchval("SELECT COUNT(*) FROM leaderboards.unified_user_performance")
            
        return {
            "platform_users": platform_users,
            "clerk_synced_users": clerk_users_count,
            "leaderboard_profiles": leaderboard_profiles,
            "sync_coverage": f"{(clerk_users_count/platform_users*100):.1f}%" if platform_users > 0 else "0%"
        }
    except Exception as e:
        logger.error(f"Failed to get sync status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get status")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=SERVICE_PORT)
