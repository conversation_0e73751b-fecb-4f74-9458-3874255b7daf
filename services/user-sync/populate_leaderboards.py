"""
Leaderboard Data Population Script
==================================

Populates leaderboard data with realistic scores and achievements for testing.
Creates diverse user profiles across gaming, betting, trading, and analysis modules.
"""

import asyncio
import asyncpg
import random
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
import os

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/betbet_db")

class LeaderboardPopulator:
    def __init__(self):
        self.conn = None
    
    async def connect(self):
        """Connect to database"""
        self.conn = await asyncpg.connect(DATABASE_URL)
        print("Connected to database")
    
    async def close(self):
        """Close database connection"""
        if self.conn:
            await self.conn.close()
    
    async def get_users(self) -> List[Dict[str, Any]]:
        """Get all platform users"""
        users = await self.conn.fetch("""
            SELECT id, username, email, first_name, last_name, created_at
            FROM public.users 
            WHERE is_active = true
            ORDER BY created_at DESC
        """)
        return [dict(user) for user in users]
    
    def generate_gaming_stats(self) -> Dict[str, Any]:
        """Generate realistic gaming statistics"""
        chess_rating = random.randint(800, 2400)
        poker_rating = random.randint(1000, 2000)
        
        return {
            "chess": {
                "rating": chess_rating,
                "games_played": random.randint(10, 500),
                "wins": random.randint(5, 250),
                "losses": random.randint(5, 200),
                "draws": random.randint(0, 50),
                "best_rating": chess_rating + random.randint(0, 200),
                "tournaments_won": random.randint(0, 15),
                "total_prize_money": round(random.uniform(0, 5000), 2)
            },
            "poker": {
                "rating": poker_rating,
                "hands_played": random.randint(100, 10000),
                "tournaments_entered": random.randint(5, 100),
                "tournaments_won": random.randint(0, 20),
                "biggest_win": round(random.uniform(50, 2000), 2),
                "total_winnings": round(random.uniform(-500, 3000), 2)
            },
            "overall": {
                "total_games": random.randint(50, 1000),
                "win_rate": round(random.uniform(0.35, 0.75), 3),
                "favorite_game": random.choice(["chess", "poker", "backgammon"])
            }
        }
    
    def generate_betting_stats(self) -> Dict[str, Any]:
        """Generate realistic betting statistics"""
        total_bets = random.randint(20, 500)
        wins = random.randint(int(total_bets * 0.3), int(total_bets * 0.7))
        
        return {
            "total_bets": total_bets,
            "wins": wins,
            "losses": total_bets - wins,
            "win_rate": round(wins / total_bets, 3),
            "total_wagered": round(random.uniform(500, 10000), 2),
            "total_winnings": round(random.uniform(-1000, 5000), 2),
            "roi": round(random.uniform(-0.3, 0.4), 3),
            "biggest_win": round(random.uniform(100, 1500), 2),
            "favorite_sport": random.choice(["football", "basketball", "tennis", "soccer"]),
            "bet_types": {
                "moneyline": random.randint(5, 100),
                "spread": random.randint(5, 100),
                "over_under": random.randint(5, 100),
                "prop_bets": random.randint(0, 50)
            }
        }
    
    def generate_trading_performance(self) -> Dict[str, Any]:
        """Generate realistic trading performance"""
        return {
            "total_trades": random.randint(10, 300),
            "profitable_trades": random.randint(5, 200),
            "win_rate": round(random.uniform(0.4, 0.8), 3),
            "total_pnl": round(random.uniform(-2000, 8000), 2),
            "roi": round(random.uniform(-0.2, 0.6), 3),
            "sharpe_ratio": round(random.uniform(0.5, 2.5), 2),
            "max_drawdown": round(random.uniform(0.05, 0.3), 3),
            "avg_trade_size": round(random.uniform(100, 1000), 2),
            "best_trade": round(random.uniform(200, 2000), 2),
            "worst_trade": round(random.uniform(-500, -50), 2),
            "favorite_asset": random.choice(["BTC", "ETH", "SPY", "TSLA", "AAPL"])
        }
    
    def generate_analyst_metrics(self) -> Dict[str, Any]:
        """Generate realistic analyst metrics"""
        predictions_made = random.randint(10, 200)
        correct_predictions = random.randint(int(predictions_made * 0.4), int(predictions_made * 0.8))
        
        return {
            "predictions_made": predictions_made,
            "correct_predictions": correct_predictions,
            "accuracy": round(correct_predictions / predictions_made, 3),
            "insights_shared": random.randint(5, 100),
            "upvotes_received": random.randint(10, 500),
            "followers": random.randint(0, 200),
            "reputation_score": round(random.uniform(1.0, 5.0), 2),
            "specialties": random.sample(["NFL", "NBA", "MLB", "Soccer", "Tennis", "Crypto", "Stocks"], k=random.randint(1, 3)),
            "model_performance": {
                "accuracy": round(random.uniform(0.5, 0.85), 3),
                "precision": round(random.uniform(0.4, 0.8), 3),
                "recall": round(random.uniform(0.4, 0.8), 3)
            }
        }
    
    def calculate_unified_score(self, gaming: Dict, betting: Dict, trading: Dict, analyst: Dict) -> float:
        """Calculate unified platform score"""
        # Weighted scoring system
        gaming_score = (gaming.get("chess", {}).get("rating", 1200) / 2400) * 2500
        betting_score = max(0, (betting.get("roi", 0) + 0.3) / 0.7) * 2500
        trading_score = max(0, (trading.get("roi", 0) + 0.2) / 0.8) * 2500
        analyst_score = (analyst.get("accuracy", 0.5) - 0.5) / 0.35 * 2500
        
        # Combine with weights
        unified = (gaming_score * 0.3 + betting_score * 0.3 + trading_score * 0.25 + analyst_score * 0.15)
        return round(max(0, min(10000, unified)), 2)
    
    def determine_tier(self, unified_score: float) -> str:
        """Determine platform tier based on unified score"""
        if unified_score >= 8000:
            return "Master"
        elif unified_score >= 6500:
            return "Diamond"
        elif unified_score >= 5000:
            return "Platinum"
        elif unified_score >= 3500:
            return "Gold"
        elif unified_score >= 2000:
            return "Silver"
        else:
            return "Bronze"
    
    def calculate_consistency(self, gaming: Dict, betting: Dict, trading: Dict, analyst: Dict) -> float:
        """Calculate cross-platform consistency score"""
        scores = []
        
        # Normalize each module score to 0-1
        if gaming.get("chess", {}).get("rating"):
            scores.append(gaming["chess"]["rating"] / 2400)
        if betting.get("roi") is not None:
            scores.append(max(0, (betting["roi"] + 0.3) / 0.7))
        if trading.get("roi") is not None:
            scores.append(max(0, (trading["roi"] + 0.2) / 0.8))
        if analyst.get("accuracy"):
            scores.append((analyst["accuracy"] - 0.5) / 0.35)
        
        if len(scores) < 2:
            return 0.0
        
        # Calculate coefficient of variation (lower = more consistent)
        mean_score = sum(scores) / len(scores)
        variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
        std_dev = variance ** 0.5
        
        if mean_score == 0:
            return 0.0
        
        cv = std_dev / mean_score
        consistency = max(0, 1 - cv)  # Invert so higher = more consistent
        
        return round(consistency, 4)
    
    async def populate_user_performance(self, user: Dict[str, Any]):
        """Populate performance data for a single user"""
        user_id = user['id']
        
        # Generate module statistics
        gaming_stats = self.generate_gaming_stats()
        betting_stats = self.generate_betting_stats()
        trading_performance = self.generate_trading_performance()
        analyst_metrics = self.generate_analyst_metrics()
        
        # Calculate derived metrics
        unified_score = self.calculate_unified_score(gaming_stats, betting_stats, trading_performance, analyst_metrics)
        platform_tier = self.determine_tier(unified_score)
        consistency_score = self.calculate_consistency(gaming_stats, betting_stats, trading_performance, analyst_metrics)
        
        # Update or insert performance data
        await self.conn.execute("""
            INSERT INTO leaderboards.unified_user_performance 
            (user_id, gaming_stats, betting_stats, trading_performance, analyst_metrics,
             unified_platform_score, platform_tier, cross_platform_consistency_score,
             created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
            ON CONFLICT (user_id) 
            DO UPDATE SET
                gaming_stats = EXCLUDED.gaming_stats,
                betting_stats = EXCLUDED.betting_stats,
                trading_performance = EXCLUDED.trading_performance,
                analyst_metrics = EXCLUDED.analyst_metrics,
                unified_platform_score = EXCLUDED.unified_platform_score,
                platform_tier = EXCLUDED.platform_tier,
                cross_platform_consistency_score = EXCLUDED.cross_platform_consistency_score,
                updated_at = NOW()
        """, user_id, json.dumps(gaming_stats), json.dumps(betting_stats), 
             json.dumps(trading_performance), json.dumps(analyst_metrics),
             unified_score, platform_tier, consistency_score)
        
        print(f"Updated performance for user {user['username']} (Score: {unified_score}, Tier: {platform_tier})")
    
    async def populate_all_users(self):
        """Populate performance data for all users"""
        users = await self.get_users()
        print(f"Found {len(users)} users to populate")
        
        for user in users:
            await self.populate_user_performance(user)
        
        print(f"Successfully populated performance data for {len(users)} users")
    
    async def create_sample_achievements(self):
        """Create sample achievements"""
        achievements = [
            {
                "achievement_code": "CHESS_MASTER",
                "name": "Chess Master",
                "description": "Reach a chess rating of 2000+",
                "category": "gaming",
                "tier": "Gold",
                "rarity": "Rare",
                "points": 500
            },
            {
                "achievement_code": "BETTING_STREAK",
                "name": "Hot Streak",
                "description": "Win 10 bets in a row",
                "category": "betting",
                "tier": "Silver",
                "rarity": "Common",
                "points": 250
            },
            {
                "achievement_code": "TRADING_PROFIT",
                "name": "Profitable Trader",
                "description": "Achieve 20%+ ROI in trading",
                "category": "trading",
                "tier": "Platinum",
                "rarity": "Epic",
                "points": 750
            },
            {
                "achievement_code": "ANALYST_EXPERT",
                "name": "Expert Analyst",
                "description": "Maintain 80%+ prediction accuracy",
                "category": "analyst",
                "tier": "Diamond",
                "rarity": "Legendary",
                "points": 1000
            }
        ]
        
        for achievement in achievements:
            await self.conn.execute("""
                INSERT INTO leaderboards.cross_platform_achievements 
                (achievement_id, achievement_code, name, description, category, tier, rarity, points, created_at)
                VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, NOW())
                ON CONFLICT (achievement_code) DO NOTHING
            """, achievement["achievement_code"], achievement["name"], achievement["description"],
                achievement["category"], achievement["tier"], achievement["rarity"], achievement["points"])
        
        print(f"Created {len(achievements)} sample achievements")

async def main():
    """Main function to populate leaderboard data"""
    populator = LeaderboardPopulator()
    
    try:
        await populator.connect()
        
        print("Starting leaderboard data population...")
        
        # Create sample achievements
        await populator.create_sample_achievements()
        
        # Populate user performance data
        await populator.populate_all_users()
        
        print("Leaderboard data population completed successfully!")
        
    except Exception as e:
        print(f"Error during population: {e}")
        raise
    finally:
        await populator.close()

if __name__ == "__main__":
    asyncio.run(main())
