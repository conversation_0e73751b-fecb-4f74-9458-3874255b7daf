import os
import sys
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    # Database Configuration
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",   # Web frontend
        "http://localhost:3001",   # Admin dashboard  
        "http://localhost:8000",   # API Gateway
        "https://betbet.com",      # Production web
        "https://admin.betbet.com" # Production admin
    ]
    
    SERVICE_NAME: str = "sports-analysis"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8005
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"  # Allow extra environment variables

# Load settings
settings = Settings()

# Initialize FastAPI app
app = FastAPI(
    title="BetBet Sports Analysis",
    description="Sports analysis and AI chat service",
    version=settings.SERVICE_VERSION
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    # Use DATABASE_URL from environment variables
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Sports Analysis connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup database connections on shutdown"""
    await close_database()

# Basic response models
class AnalysisResponse(BaseModel):
    id: str
    type: str
    data: dict
    timestamp: datetime

@app.get("/")
def read_root():
    return {
        "service": "BetBet Sports Analysis",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "analysis": "/api/v1/analysis/",
            "fixtures": "/api/v1/analysis/fixtures",
            "leagues": "/api/v1/analysis/leagues",
            "teams": "/api/v1/analysis/teams",
            "analytics": "/api/v1/analysis/analytics/overview"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/analysis/analytics/overview")
async def get_analytics_overview():
    """Get sports analytics overview"""

    return {
        "predictions_today": 28,
        "model_accuracy": 78.6,
        "active_events": 15,
        "total_predictions": 0  # Real prediction count needed
    }

@app.get("/ready")
async def ready_check():
    """Readiness check endpoint"""
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False
            
        return {
            "status": "ready" if db_status else "not_ready",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "timestamp": datetime.utcnow().timestamp(),
            "checks": {
                "database": "healthy" if db_status else "error",
                "api_football": "healthy",
                "rag_engine": "healthy",
                "websocket": "healthy", 
                "cache": "healthy"
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "service": settings.SERVICE_NAME, 
            "error": str(e)
        }

# Basic API endpoints following gaming-engine pattern
@app.get("/api/v1/analysis/fixtures")
async def get_fixtures(db: AsyncSession = Depends(get_database_write)):
    """Get sports fixtures"""
    return {
        "fixtures": [
            {
                "id": "1",
                "home_team": "Arsenal",
                "away_team": "Chelsea", 
                "league": "Premier League",
                "date": "2024-01-20",
                "status": "upcoming"
            }
        ],
        "total": 1
    }

@app.get("/api/v1/analysis/leagues")
async def get_leagues(db: AsyncSession = Depends(get_database_write)):
    """Get sports leagues"""
    return {
        "leagues": [
            {
                "id": "39",
                "name": "Premier League",
                "country": "England",
                "season": "2023-2024"
            }
        ],
        "total": 1
    }

@app.get("/api/v1/analysis/teams")
async def get_teams(db: AsyncSession = Depends(get_database_write)):
    """Get sports teams"""
    return {
        "teams": [
            {
                "id": "42",
                "name": "Arsenal",
                "league": "Premier League",
                "country": "England"
            }
        ],
        "total": 1
    }

@app.get("/api/v1/analysis/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get sports analytics overview"""
    return {
        "total_fixtures": 150,
        "active_leagues": 15,
        "tracked_teams": 500,
        "predictions_made": 1200,
        "timestamp": datetime.utcnow().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)