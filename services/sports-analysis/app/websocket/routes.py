"""
BetBet Sports Analysis - WebSocket Routes
========================================

WebSocket route registration and management.
"""

from fastapi import FastAPI
import structlog

from .handlers import (
    websocket_live_matches,
    websocket_chat_session,
    websocket_document_processing,
    websocket_premium_alerts
)

# Configure logging
logger = structlog.get_logger()


def register_websocket_routes(app: FastAPI):
    """Register all WebSocket routes with the FastAPI app."""
    try:
        logger.info("Registering WebSocket routes")
        
        # Live matches WebSocket
        app.websocket("/ws/live-matches")(websocket_live_matches)
        
        # Chat session WebSocket
        app.websocket("/ws/chat/{session_id}")(websocket_chat_session)
        
        # Document processing WebSocket
        app.websocket("/ws/documents/{document_id}")(websocket_document_processing)
        
        # Premium alerts WebSocket
        app.websocket("/ws/premium/{user_id}")(websocket_premium_alerts)
        
        logger.info("✅ WebSocket routes registered successfully")
        
    except Exception as e:
        logger.error("❌ Failed to register WebSocket routes", error=str(e))
        raise