"""
BetBet Sports Analysis - API Dependencies
========================================

Common dependencies and utilities for FastAPI endpoints.
"""

import os
from functools import lru_cache
from typing import AsyncGenerator, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic_settings import BaseSettings
import structlog

# Import shared libraries
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from shared.core.database.connection import get_database_write, get_database_read, _db_manager

# Configure logging
logger = structlog.get_logger()
try:
    from services.shared.core.auth.jwt_handler import get_auth_manager
    from services.shared.core.auth.models import TokenData
except ImportError:
    # Fallback TokenData class
    class TokenData:
        def __init__(self, user_id: str, username: str = "", email: str = "", roles: list = None):
            self.user_id = user_id
            self.username = username  
            self.email = email
            self.roles = roles or []
    
    def get_auth_manager():
        """Placeholder - should be replaced with proper Clerk integration"""
        class MockAuthManager:
            def verify_token(self, token: str) -> TokenData:
                return TokenData(
                    user_id="550e8400-e29b-41d4-a716-************",  # Valid UUID format
                    username="testuser", 
                    email="<EMAIL>",
                    roles=["user", "admin"]  # Add admin role for testing
                )
        return MockAuthManager()


class Settings(BaseSettings):
    """Application settings"""
    
    # Service configuration
    service_name: str = "sports-analysis"
    service_version: str = "1.0.0"
    environment: str = "development"
    debug: bool = True
    
    # Database configuration  
    database_url: str = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db")
    
    def model_post_init(self, __context) -> None:
        # Convert Railway's postgresql:// to asyncpg format
        if self.database_url.startswith("postgresql://"):
            self.database_url = self.database_url.replace("postgresql://", "postgresql+asyncpg://", 1)
    database_echo: bool = False
    database_pool_size: int = 10
    database_max_overflow: int = 20
    database_pool_timeout: int = 30
    database_pool_recycle: int = 3600
    
    # Redis configuration
    redis_url: str = "redis://localhost:6379/0"
    redis_max_connections: int = 20
    
    # Clerk Authentication
    clerk_publishable_key: str = os.getenv("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "")
    clerk_secret_key: str = os.getenv("CLERK_SECRET_KEY", "")
    
    # Legacy JWT Authentication (for internal services)
    jwt_secret_key: str = "your-super-secret-jwt-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    
    # API configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8004  # Sports Analysis port
    api_workers: int = 1
    api_reload: bool = True
    
    # CORS configuration
    cors_origins: list = ["http://localhost:3000", "http://localhost:8080"]
    cors_allow_credentials: bool = True
    cors_allow_methods: list = ["*"]
    cors_allow_headers: list = ["*"]
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    # Sports Analysis specific settings
    api_football_key: str = os.getenv("API_FOOTBALL_KEY", "")
    api_football_base_url: str = "https://v3.football.api-sports.io"
    cache_ttl_seconds: int = 30  # 30 second cache for live data
    
    # RAG Engine settings
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    embedding_model: str = "text-embedding-ada-002"
    chat_model: str = "gpt-4-turbo-preview"
    max_context_length: int = 8000
    
    # Document processing
    max_file_size_mb: int = 50
    allowed_file_types: list = ["pdf", "txt", "docx"]
    
    # Premium features
    premium_rate_limit: int = 100  # requests per hour
    free_rate_limit: int = 20     # requests per hour
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


# Security
security = HTTPBearer(auto_error=False)


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session dependency"""
    async for session in get_database_write():
        yield session


async def get_read_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get read-only database session dependency"""
    async for session in get_database_read():
        yield session


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    settings: Settings = Depends(get_settings)
) -> TokenData:
    """Get current authenticated user"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Use Clerk authentication if configured
        if settings.clerk_secret_key:
            # TODO: Implement Clerk authentication for sports-analysis
            # For now, use mock authentication
            logger.info("Using mock authentication for testing")
            return TokenData(
                user_id="550e8400-e29b-41d4-a716-************",  # Valid UUID format
                username="testuser", 
                email="<EMAIL>",
                roles=["user", "admin"]
            )
        else:
            # Fallback to shared auth manager or mock
            logger.info("Using mock authentication for testing")
            return TokenData(
                user_id="550e8400-e29b-41d4-a716-************",  # Valid UUID format
                username="testuser", 
                email="<EMAIL>",
                roles=["user", "admin"]
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions (already properly formatted)
        raise
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_optional_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    settings: Settings = Depends(get_settings)
) -> Optional[TokenData]:
    """Get current user if authenticated, None otherwise"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, settings)
    except Exception:
        return None


def get_admin_user(
    current_user: TokenData = Depends(get_current_user)
) -> TokenData:
    """Require admin user"""
    if "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


def get_premium_user(
    current_user: TokenData = Depends(get_current_user)
) -> TokenData:
    """Require premium user"""
    if not any(role in current_user.roles for role in ["admin", "premium", "subscriber"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Premium subscription required"
        )
    return current_user


# Pagination dependencies
class PaginationParams:
    """Pagination parameters"""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="Page number"),
        limit: int = Query(20, ge=1, le=100, description="Items per page")
    ):
        self.page = page
        self.limit = limit
        self.offset = (page - 1) * limit


def get_pagination(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page")
) -> PaginationParams:
    """Get pagination parameters"""
    return PaginationParams(page, limit)


# Sports Analysis specific dependencies
class SportsQueryParams:
    """Sports data query parameters"""
    
    def __init__(
        self,
        league_id: Optional[int] = Query(None, description="Filter by league ID"),
        season: Optional[int] = Query(None, description="Filter by season year"),
        team_id: Optional[int] = Query(None, description="Filter by team ID"),
        date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
        date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)"),
        live: Optional[bool] = Query(None, description="Filter live matches only")
    ):
        self.league_id = league_id
        self.season = season
        self.team_id = team_id
        self.date_from = date_from
        self.date_to = date_to
        self.live = live


def get_sports_query_params(
    league_id: Optional[int] = Query(None, description="Filter by league ID"),
    season: Optional[int] = Query(None, description="Filter by season year"),
    team_id: Optional[int] = Query(None, description="Filter by team ID"),
    date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)"),
    live: Optional[bool] = Query(None, description="Filter live matches only")
) -> SportsQueryParams:
    """Get sports query parameters"""
    return SportsQueryParams(league_id, season, team_id, date_from, date_to, live)


# Validation helpers
def validate_uuid(uuid_str: str, field_name: str = "ID") -> UUID:
    """Validate UUID string"""
    try:
        return UUID(uuid_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name} format"
        )


def validate_positive_int(value: int, field_name: str = "value") -> int:
    """Validate positive integer value"""
    if value <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be positive"
        )
    return value


# Error handling
class ServiceError(Exception):
    """Base service error"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class NotFoundError(ServiceError):
    """Resource not found error"""
    def __init__(self, resource: str, resource_id: str = None):
        message = f"{resource} not found"
        if resource_id:
            message += f" (ID: {resource_id})"
        super().__init__(message, 404)


class ValidationError(ServiceError):
    """Validation error"""
    def __init__(self, message: str):
        super().__init__(message, 400)


class PermissionError(ServiceError):
    """Permission denied error"""
    def __init__(self, message: str = "Permission denied"):
        super().__init__(message, 403)


class RateLimitError(ServiceError):
    """Rate limit exceeded error"""
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, 429)


# Rate limiting dependencies
class RateLimitParams:
    """Rate limiting parameters"""
    
    def __init__(self, requests_per_hour: int = 60):
        self.requests_per_hour = requests_per_hour


def get_rate_limit() -> RateLimitParams:
    """Get rate limiting parameters"""
    return RateLimitParams()


# Cache dependencies
class CacheParams:
    """Cache parameters"""
    
    def __init__(self, ttl_seconds: int = 30):
        self.ttl_seconds = ttl_seconds


def get_cache_params() -> CacheParams:
    """Get cache parameters"""
    return CacheParams()


# WebSocket dependencies
async def get_websocket_manager():
    """Get WebSocket manager dependency"""
    from services.shared.core.messaging.websocket_manager import get_websocket_manager
    return get_websocket_manager()