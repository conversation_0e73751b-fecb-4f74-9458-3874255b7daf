# BetBet Platform - Sports Analysis Service

## Overview

The Sports Analysis Service provides comprehensive sports data analysis, real-time statistics, and intelligent insights to power betting decisions on the BetBet platform. It combines advanced analytics, machine learning, and real-time data processing to deliver accurate sports predictions and market intelligence.

## Features

- **Real-time Sports Data**: Live scores, statistics, and game events
- **Advanced Analytics**: Statistical modeling and trend analysis
- **AI-Powered Predictions**: Machine learning-based outcome predictions
- **Market Intelligence**: Betting market analysis and value identification
- **Performance Tracking**: Team and player performance metrics
- **Historical Analysis**: Comprehensive historical data analysis
- **Custom Reports**: Tailored analysis for specific sports and markets

## Technology Stack

- **Framework**: FastAPI (Python 3.11+)
- **Analytics**: pandas, numpy, scikit-learn
- **Database**: PostgreSQL with time-series optimization
- **Cache**: Redis for real-time data and calculations
- **External APIs**: Multiple sports data providers
- **Authentication**: JWT-based authentication
- **Containerization**: Docker with optimized data processing

## Sports Coverage

### Current Sports
- **Football (Soccer)**: Premier League, Champions League, World Cup
- **American Football**: NFL, College Football
- **Basketball**: NBA, WNBA, EuroLeague
- **Baseball**: MLB, World Series
- **Tennis**: Grand Slams, ATP/WTA Tours
- **Hockey**: NHL, IIHF Championships

### Planned Expansions
- **Cricket**: IPL, World Cup
- **Rugby**: Six Nations, World Cup
- **Golf**: Major Championships, PGA Tour
- **Boxing/MMA**: Major fights and events

## API Endpoints

### Sports Data
- `GET /api/v1/sports` - List supported sports
- `GET /api/v1/sports/{sport_id}/leagues` - Get leagues for sport
- `GET /api/v1/sports/{sport_id}/teams` - Get teams information
- `GET /api/v1/sports/{sport_id}/players` - Get player statistics

### Game Analysis
- `GET /api/v1/games/{game_id}/analysis` - Comprehensive game analysis
- `GET /api/v1/games/{game_id}/predictions` - AI predictions for game
- `GET /api/v1/games/live` - Live games with real-time data
- `GET /api/v1/games/upcoming` - Upcoming games and previews

### Statistics
- `GET /api/v1/stats/team/{team_id}` - Team performance statistics
- `GET /api/v1/stats/player/{player_id}` - Player performance data
- `GET /api/v1/stats/league/{league_id}` - League-wide statistics
- `GET /api/v1/stats/trends` - Performance trends and patterns

### Predictions
- `POST /api/v1/predictions/generate` - Generate custom predictions
- `GET /api/v1/predictions/game/{game_id}` - Get game predictions
- `GET /api/v1/predictions/accuracy` - Prediction accuracy metrics
- `GET /api/v1/predictions/models` - Available prediction models

### Market Analysis
- `GET /api/v1/markets/{game_id}/analysis` - Betting market analysis
- `GET /api/v1/markets/value` - Value betting opportunities
- `GET /api/v1/markets/trends` - Market movement analysis
- `GET /api/v1/markets/comparison` - Cross-market odds comparison

## Environment Variables

```bash
# Database Configuration
DATABASE_URL=****************************************/betbet_sports
REDIS_URL=redis://:password@redis:6379

# Authentication
JWT_SECRET=your-jwt-secret-key

# External Services
API_GATEWAY_URL=http://api-gateway:8000
WEBSOCKET_URL=ws://websocket-manager:8080

# Sports Data APIs
SPORTS_API_KEY=your-sports-api-key
FOOTBALL_API_KEY=your-football-api-key
BASKETBALL_API_KEY=your-basketball-api-key

# Analysis Configuration
PREDICTION_UPDATE_INTERVAL=300  # seconds
MODEL_RETRAIN_INTERVAL=86400    # daily
CACHE_TTL=1800                  # 30 minutes

# Application Settings
LOG_LEVEL=INFO
```

## Database Schema

The service uses the `betbet_sports` database with tables for:
- `sports` - Sport definitions and configurations
- `leagues` - League information and seasons
- `teams` - Team profiles and statistics
- `players` - Player profiles and performance data
- `games` - Game schedules and results
- `game_events` - Real-time game events and statistics
- `predictions` - AI-generated predictions and accuracy
- `market_data` - Betting market information and trends

## Analytics Engine

### Statistical Models
```python
class SportsAnalytics:
    def team_strength_rating(self, team_stats):
        # Calculates team strength based on:
        # - Recent performance
        # - Head-to-head records
        # - Home/away performance
        # - Player quality metrics
        pass
    
    def game_outcome_probability(self, team1, team2, venue):
        # Machine learning model considering:
        # - Team strength ratings
        # - Historical matchups
        # - Current form
        # - Venue factors
        pass
```

### Real-time Processing
- **Live Data Ingestion**: Real-time sports data feeds
- **Event Processing**: Process game events as they occur
- **Prediction Updates**: Update predictions based on live events
- **Market Monitoring**: Track betting market movements

## Prediction Models

### Machine Learning Models
- **Team Performance Model**: Predicts team performance metrics
- **Player Impact Model**: Measures individual player contributions
- **Game Outcome Model**: Predicts match results and scores
- **Market Value Model**: Identifies value in betting markets

### Model Features
- **Team Statistics**: Goals, shots, possession, etc.
- **Player Metrics**: Individual player performance data
- **Historical Data**: Past performance and trends
- **Contextual Factors**: Weather, venue, injuries, etc.

## Development

### Local Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize sports data:**
   ```bash
   python scripts/init_sports_data.py
   ```

4. **Train initial models:**
   ```bash
   python scripts/train_models.py
   ```

5. **Run the service:**
   ```bash
   python app/main.py
   ```

### Docker Development

```bash
# Build and run with Docker Compose
docker-compose up sports-analysis

# View logs
docker-compose logs -f sports-analysis
```

## Data Sources

### Primary Data Providers
- **API-Football**: Comprehensive football data
- **SportsRadar**: Multi-sport real-time data
- **ESPN API**: Sports scores and statistics
- **The Sports DB**: Historical sports data

### Data Quality
- **Validation**: Multi-source data validation
- **Cleaning**: Automated data cleaning processes
- **Normalization**: Standardized data formats
- **Completeness**: Data quality monitoring

## Health Checks

The service exposes a health check endpoint at `/health` that returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "database": "connected",
  "redis": "connected",
  "sports_apis": "connected",
  "models_loaded": 8,
  "prediction_accuracy": 0.78
}
```

## Performance Optimization

### Caching Strategy
- **Game Data**: Cache game information and statistics
- **Predictions**: Cache prediction results
- **Market Data**: Cache betting market information
- **Historical Data**: Cache frequently accessed historical data

### Data Processing
- **Batch Processing**: Efficient bulk data processing
- **Streaming**: Real-time data stream processing
- **Parallel Processing**: Multi-threaded data analysis
- **Memory Optimization**: Efficient memory usage for large datasets

## Real-time Features

### Live Data Streaming
- **Game Events**: Real-time goal, score, and event updates
- **Statistics**: Live game statistics and player metrics
- **Predictions**: Dynamic prediction updates during games
- **Market Changes**: Real-time betting market movements

### WebSocket Events
- `game_started` - Game begins with initial data
- `game_event` - Goal, card, substitution, etc.
- `prediction_updated` - Updated prediction based on events
- `market_movement` - Significant betting market changes
- `game_finished` - Final result and statistics

## Security Features

- **API Security**: Secure API key management for data providers
- **Data Validation**: Comprehensive input validation
- **Rate Limiting**: Prevent API abuse and ensure fair usage
- **Model Protection**: Secure machine learning model storage
- **Audit Logging**: Complete audit trail for predictions and analysis

## Integration

### External APIs
- **Rate Limiting**: Respect API provider rate limits
- **Failover**: Multiple data sources for redundancy
- **Error Handling**: Graceful handling of API failures
- **Cost Optimization**: Efficient API usage to minimize costs

### Platform Integration
- **Betting Service**: Provide analysis for betting markets
- **Gaming Engine**: Sports data for gaming sessions
- **Expert Analysis**: Data for expert predictions
- **User Interface**: Real-time data for frontend displays

## Monitoring and Analytics

### Performance Metrics
- **Prediction Accuracy**: Track model performance over time
- **Data Latency**: Monitor real-time data processing delays
- **API Response Times**: Track external API performance
- **Error Rates**: Monitor data processing and model errors

### Business Intelligence
- **User Engagement**: Track analysis feature usage
- **Prediction Performance**: Measure betting success rates
- **Market Impact**: Analyze effect on betting patterns
- **Revenue Attribution**: Track revenue from analysis features

## Testing

```bash
# Run unit tests
pytest tests/unit/

# Run model tests
pytest tests/models/

# Run integration tests
pytest tests/integration/

# Test prediction accuracy
python scripts/test_prediction_accuracy.py

# Load testing
python scripts/load_test_analysis.py
```

## Advanced Features

### Custom Analysis
- **Team Comparisons**: Head-to-head team analysis
- **Player Impact**: Analyze individual player contributions
- **Trend Analysis**: Identify performance trends and patterns
- **Scenario Analysis**: What-if analysis for different outcomes

### Market Intelligence
- **Value Detection**: Identify mispriced betting markets
- **Market Efficiency**: Analyze betting market accuracy
- **Arbitrage Opportunities**: Detect arbitrage possibilities
- **Line Movement**: Track and analyze betting line changes

## Deployment

### Production Deployment

```bash
# Build production image
docker build -f Dockerfile.prod -t betbet/sports-analysis:latest .

# Deploy with data processing optimization
docker-compose -f docker-compose.prod.yml up -d sports-analysis
```

### Scaling Considerations
- **CPU Intensive**: Scale for data processing workloads
- **Memory Requirements**: 8GB+ RAM for ML models and data
- **Storage**: Fast SSD for historical data and model storage
- **Network**: High bandwidth for real-time data feeds

## Troubleshooting

### Common Issues

**Prediction accuracy declining:**
- Check data quality and completeness
- Verify model training data freshness
- Review feature engineering pipeline

**Slow analysis responses:**
- Monitor database query performance
- Check Redis cache hit rates
- Review data processing efficiency

**External API failures:**
- Verify API key validity and limits
- Check network connectivity
- Review failover mechanisms

### Debug Mode

Enable detailed analysis logging:
```bash
export LOG_LEVEL=DEBUG
export ANALYSIS_DEBUG=true
python app/main.py
```

## Contributing

1. Follow sports analytics best practices
2. Add comprehensive tests for new models
3. Document analysis methodologies
4. Ensure prediction accuracy and reliability
5. Test performance with large datasets

## License

MIT License - see LICENSE file for details.

---

**Part of the BetBet Platform** - Turning sports data into intelligent insights.