FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

FROM python:3.11-slim AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r betbet && useradd -r -g betbet betbet

WORKDIR /app

# Copy Python dependencies from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=betbet:betbet app/ ./app/
COPY --chown=betbet:betbet requirements.txt .

# Remove development files
RUN rm -f .env* docker-compose* Dockerfile.dev

# Make sure scripts are executable
RUN chmod +x /home/<USER>/.local/bin/*

# Switch to non-root user
USER betbet

# Add local bin to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Expose port
EXPOSE 8005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8005/health || exit 1

# Start the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8005", "--workers", "2"]