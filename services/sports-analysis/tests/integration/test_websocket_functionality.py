"""
Integration tests for WebSocket functionality.
"""

import pytest
import asyncio
import json
from datetime import datetime
from uuid import uuid4

from fastapi.testclient import TestClient
from fastapi import WebSocket

from main import app
from app.websocket.routes import connection_managers


class TestWebSocketConnections:
    """Test WebSocket connection management."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_websocket_route_registration(self):
        """Test that WebSocket routes are registered."""
        # Check that connection managers are initialized
        assert 'live_matches' in connection_managers
        assert 'chat_sessions' in connection_managers
        assert 'document_processing' in connection_managers
        assert 'premium_alerts' in connection_managers
        
        # Check manager types
        from app.websocket.routes import (
            LiveMatchConnectionManager,
            ChatSessionConnectionManager, 
            DocumentProcessingConnectionManager,
            PremiumAlertsConnectionManager
        )
        
        assert isinstance(connection_managers['live_matches'], LiveMatchConnectionManager)
        assert isinstance(connection_managers['chat_sessions'], ChatSessionConnectionManager)
        assert isinstance(connection_managers['document_processing'], DocumentProcessingConnectionManager)
        assert isinstance(connection_managers['premium_alerts'], PremiumAlertsConnectionManager)


class TestLiveMatchWebSocket:
    """Test live match WebSocket functionality."""
    
    @pytest.fixture
    def manager(self):
        """Get live match connection manager."""
        return connection_managers['live_matches']
    
    def test_manager_initialization(self, manager):
        """Test manager is properly initialized."""
        assert hasattr(manager, 'active_connections')
        assert hasattr(manager, 'connection_metadata')
        assert hasattr(manager, 'subscribed_matches')
        
        # Should start empty
        assert len(manager.active_connections) == 0
        assert len(manager.subscribed_matches) == 0
    
    @pytest.mark.asyncio
    async def test_match_subscription_logic(self, manager):
        """Test match subscription logic."""
        connection_id = "test_connection_1"
        match_id = "12345"
        
        # Test subscription
        await manager.subscribe_to_match(connection_id, match_id)
        
        assert connection_id in manager.subscribed_matches
        assert match_id in manager.subscribed_matches[connection_id]
        
        # Test unsubscription
        await manager.unsubscribe_from_match(connection_id, match_id)
        
        if connection_id in manager.subscribed_matches:
            assert match_id not in manager.subscribed_matches[connection_id]
    
    @pytest.mark.asyncio
    async def test_match_update_broadcasting(self, manager):
        """Test match update broadcasting."""
        match_id = "12345"
        update_data = {
            'type': 'goal',
            'player': 'Smith',
            'minute': 23,
            'team': 'home'
        }
        
        # Mock connection for testing
        class MockWebSocket:
            def __init__(self):
                self.sent_messages = []
            
            async def send_text(self, message):
                self.sent_messages.append(message)
        
        # Add mock connection subscribed to match
        connection_id = "test_connection_1"
        mock_ws = MockWebSocket()
        
        manager.active_connections[connection_id] = mock_ws
        manager.subscribed_matches[connection_id] = [match_id]
        
        # Broadcast update
        await manager.broadcast_match_update(match_id, update_data)
        
        # Check message was sent
        assert len(mock_ws.sent_messages) == 1
        
        # Parse sent message
        sent_data = json.loads(mock_ws.sent_messages[0])
        assert sent_data['type'] == 'match_update'
        assert sent_data['match_id'] == match_id
        assert sent_data['update'] == update_data


class TestChatSessionWebSocket:
    """Test chat session WebSocket functionality."""
    
    @pytest.fixture
    def manager(self):
        """Get chat session connection manager."""
        return connection_managers['chat_sessions']
    
    def test_manager_initialization(self, manager):
        """Test manager is properly initialized."""
        assert hasattr(manager, 'session_connections')
        assert len(manager.session_connections) == 0
    
    @pytest.mark.asyncio
    async def test_session_message_handling(self, manager):
        """Test session message handling."""
        connection_id = "chat_session_123_conn1"
        session_id = "session_123"
        
        # Add connection to metadata
        manager.connection_metadata[connection_id] = {
            'session_id': session_id,
            'connected_at': datetime.utcnow().timestamp(),
            'message_count': 0
        }
        
        # Test typing start message
        typing_start_message = {
            'type': 'typing_start',
            'user_id': str(uuid4())
        }
        
        await manager._handle_message(typing_start_message, connection_id)
        
        # Should handle without errors
        assert True  # If we get here, message was handled
        
        # Test typing stop message
        typing_stop_message = {
            'type': 'typing_stop',
            'user_id': str(uuid4())
        }
        
        await manager._handle_message(typing_stop_message, connection_id)
        
        # Should handle without errors
        assert True
    
    @pytest.mark.asyncio
    async def test_session_broadcasting(self, manager):
        """Test broadcasting to session connections."""
        session_id = "session_123"
        
        # Mock connections for session
        class MockWebSocket:
            def __init__(self, conn_id):
                self.conn_id = conn_id
                self.sent_messages = []
            
            async def send_text(self, message):
                self.sent_messages.append(message)
        
        # Add mock connections to session
        conn1 = MockWebSocket("conn1")
        conn2 = MockWebSocket("conn2")
        
        manager.active_connections["conn1"] = conn1
        manager.active_connections["conn2"] = conn2
        manager.session_connections[session_id] = ["conn1", "conn2"]
        
        # Broadcast message to session
        broadcast_message = {
            'type': 'user_typing',
            'session_id': session_id,
            'typing': True
        }
        
        await manager.broadcast_to_session(
            broadcast_message,
            session_id,
            exclude_connection="conn1"
        )
        
        # Check that only conn2 received the message (conn1 was excluded)
        assert len(conn1.sent_messages) == 0
        assert len(conn2.sent_messages) == 1
        
        # Parse message sent to conn2
        sent_data = json.loads(conn2.sent_messages[0])
        assert sent_data['type'] == 'user_typing'
        assert sent_data['session_id'] == session_id


class TestDocumentProcessingWebSocket:
    """Test document processing WebSocket functionality."""
    
    @pytest.fixture
    def manager(self):
        """Get document processing connection manager."""
        return connection_managers['document_processing']
    
    def test_manager_initialization(self, manager):
        """Test manager is properly initialized."""
        assert hasattr(manager, 'document_connections')
        assert len(manager.document_connections) == 0
    
    @pytest.mark.asyncio
    async def test_processing_status_updates(self, manager):
        """Test processing status update handling."""
        # Mock handler
        class MockHandler:
            async def get_processing_status(self, document_id):
                return {
                    'document_id': document_id,
                    'status': 'processing',
                    'progress_percentage': 50,
                    'message': 'Extracting text from PDF'
                }
        
        manager.handler = MockHandler()
        
        document_id = "doc_123"
        status = await manager.handler.get_processing_status(document_id)
        
        assert status is not None
        assert status['document_id'] == document_id
        assert status['status'] == 'processing'
        assert status['progress_percentage'] == 50
    
    @pytest.mark.asyncio
    async def test_processing_update_broadcasting(self, manager):
        """Test broadcasting processing updates."""
        document_id = "doc_123"
        update_data = {
            'status': 'completed',
            'progress_percentage': 100,
            'fixtures_found': 5,
            'teams_identified': 10
        }
        
        # Mock connection for document
        class MockWebSocket:
            def __init__(self):
                self.sent_messages = []
            
            async def send_text(self, message):
                self.sent_messages.append(message)
        
        # Add mock connection for document
        connection_id = f"doc_{document_id}_conn1"
        mock_ws = MockWebSocket()
        
        manager.active_connections[connection_id] = mock_ws
        manager.document_connections[document_id] = [connection_id]
        
        # Broadcast update
        await manager.broadcast_processing_update(document_id, update_data)
        
        # Check message was sent
        assert len(mock_ws.sent_messages) == 1
        
        # Parse sent message
        sent_data = json.loads(mock_ws.sent_messages[0])
        assert sent_data['type'] == 'processing_update'
        assert sent_data['document_id'] == document_id
        assert sent_data['update'] == update_data


class TestPremiumAlertsWebSocket:
    """Test premium alerts WebSocket functionality."""
    
    @pytest.fixture
    def manager(self):
        """Get premium alerts connection manager."""
        return connection_managers['premium_alerts']
    
    def test_manager_initialization(self, manager):
        """Test manager is properly initialized."""
        assert hasattr(manager, 'user_connections')
        assert len(manager.user_connections) == 0
    
    @pytest.mark.asyncio
    async def test_premium_alert_sending(self, manager):
        """Test sending premium alerts."""
        user_id = "user_123"
        alert_data = {
            'type': 'match_event',
            'title': 'Goal Alert!',
            'message': 'Arsenal scored! 2-1 vs Chelsea',
            'priority': 'high'
        }
        
        # Mock connection for user
        class MockWebSocket:
            def __init__(self):
                self.sent_messages = []
            
            async def send_text(self, message):
                self.sent_messages.append(message)
        
        # Add mock connection for user
        connection_id = f"premium_{user_id}_conn1"
        mock_ws = MockWebSocket()
        
        manager.active_connections[connection_id] = mock_ws
        manager.user_connections[user_id] = [connection_id]
        
        # Send alert
        await manager.send_premium_alert(user_id, alert_data)
        
        # Check message was sent
        assert len(mock_ws.sent_messages) == 1
        
        # Parse sent message
        sent_data = json.loads(mock_ws.sent_messages[0])
        assert sent_data['type'] == 'premium_alert'
        assert sent_data['user_id'] == user_id
        assert sent_data['alert'] == alert_data


class TestWebSocketHandlers:
    """Test WebSocket event handlers."""
    
    def test_live_match_handler_initialization(self):
        """Test live match handler initialization."""
        from app.websocket.handlers import LiveMatchHandler
        
        handler = LiveMatchHandler()
        assert handler.cache == {}
        assert handler.cache_ttl == 30
    
    def test_chat_session_handler_initialization(self):
        """Test chat session handler initialization."""
        from app.websocket.handlers import ChatSessionHandler
        
        handler = ChatSessionHandler()
        assert handler.active_sessions == {}
        assert handler.typing_users == {}
    
    def test_document_processing_handler_initialization(self):
        """Test document processing handler initialization."""
        from app.websocket.handlers import DocumentProcessingHandler
        
        handler = DocumentProcessingHandler()
        assert handler.processing_status == {}
    
    def test_premium_alerts_handler_initialization(self):
        """Test premium alerts handler initialization."""
        from app.websocket.handlers import PremiumAlertsHandler
        
        handler = PremiumAlertsHandler()
        assert handler.alert_history == {}
        assert handler.alert_preferences == {}