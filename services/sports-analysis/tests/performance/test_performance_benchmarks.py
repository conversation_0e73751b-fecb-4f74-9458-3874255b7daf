"""
Performance tests to ensure response time requirements are met.
"""

import pytest
import asyncio
import time
from datetime import datetime
from uuid import uuid4
from statistics import mean, stdev
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock, Mock
from httpx import AsyncClient

from main import app


class TestPerformanceBenchmarks:
    """Performance tests for Sports Analysis & AI Chat service."""
    
    @pytest.fixture
    async def client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer mock-jwt-token"}
    
    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return Mock(id=uuid4(), username='perftest', email='<EMAIL>')
    
    async def measure_response_time(self, coro):
        """Measure response time of an async operation."""
        start = time.perf_counter()
        result = await coro
        end = time.perf_counter()
        return (end - start) * 1000, result  # Return time in milliseconds
    
    def assert_performance(self, times: List[float], target_ms: float, percentile: int = 95):
        """Assert performance meets target."""
        times.sort()
        p95_index = int(len(times) * (percentile / 100))
        p95_time = times[p95_index] if p95_index < len(times) else times[-1]
        avg_time = mean(times)
        
        print(f"\nPerformance Stats:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  P95: {p95_time:.2f}ms")
        print(f"  Min: {min(times):.2f}ms")
        print(f"  Max: {max(times):.2f}ms")
        if len(times) > 1:
            print(f"  StdDev: {stdev(times):.2f}ms")
        
        assert p95_time < target_ms, f"P{percentile} response time {p95_time:.2f}ms exceeds target {target_ms}ms"
    
    @pytest.mark.asyncio
    async def test_sports_data_query_performance(self, client, auth_headers, mock_user):
        """Test: Sports data queries should respond in <50ms average."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.core.api_football.get_api_football_service') as mock_api:
            
            # Mock API-Football service with realistic data
            mock_service = AsyncMock()
            mock_service.get_fixtures.return_value = {
                'response': [
                    {
                        'fixture': {'id': i, 'date': '2025-01-25T15:00:00Z'},
                        'teams': {'home': {'name': f'Team{i}'}, 'away': {'name': f'Team{i+1}'}}
                    }
                    for i in range(10)
                ]
            }
            mock_api.return_value = mock_service
            
            # Warm up
            await client.get("/analysis/fixtures?date=2025-01-25", headers=auth_headers)
            
            # Run performance tests
            times = []
            for _ in range(100):
                time_ms, response = await self.measure_response_time(
                    client.get("/analysis/fixtures?date=2025-01-25", headers=auth_headers)
                )
                assert response.status_code == 200
                times.append(time_ms)
            
            # Assert <50ms average for sports data
            self.assert_performance(times, target_ms=50)
    
    @pytest.mark.asyncio
    async def test_rag_query_performance(self, client, auth_headers, mock_user):
        """Test: RAG query processing should complete in <2s end-to-end."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Setup mocks
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # Mock RAG with realistic delay
            async def mock_semantic_search(*args, **kwargs):
                await asyncio.sleep(0.1)  # Simulate 100ms vector search
                return [
                    {'content': f'Result {i}', 'similarity': 0.9 - i*0.1}
                    for i in range(5)
                ]
            
            mock_rag_instance = AsyncMock()
            mock_rag_instance.semantic_search = mock_semantic_search
            mock_rag.return_value = mock_rag_instance
            
            # Mock chat processor with realistic delay
            async def mock_process_message(*args, **kwargs):
                await asyncio.sleep(0.3)  # Simulate 300ms AI processing
                return {
                    'response': 'AI generated response based on RAG results',
                    'response_type': 'informative',
                    'rag_used': True,
                    'retrieval_results': [],
                    'similarity_scores': [0.9, 0.8, 0.7],
                    'avg_similarity_score': 0.8,
                    'tokens_used': 150,
                    'cost': 0.003,
                    'confidence': 0.9,
                    'entities': [],
                    'topics': [],
                    'follow_ups': []
                }
            
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message = mock_process_message
            mock_processor.return_value = mock_processor_instance
            
            # Run performance tests
            times = []
            for _ in range(20):  # Fewer iterations for slower operations
                time_ms, response = await self.measure_response_time(
                    client.post(
                        f"/chat/sessions/{session_id}/message",
                        json={"message": "Which teams play today?", "context": {}},
                        headers=auth_headers
                    )
                )
                assert response.status_code == 200
                times.append(time_ms)
            
            # Assert <2000ms (2s) for RAG queries
            self.assert_performance(times, target_ms=2000)
    
    @pytest.mark.asyncio
    async def test_vector_search_performance(self, client, auth_headers, mock_user):
        """Test: Vector similarity search should complete in <100ms."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.core.vector_store.get_vector_store') as mock_vector:
            
            # Mock vector store with realistic performance
            async def mock_search(query_vector, top_k=10, filters=None):
                await asyncio.sleep(0.02)  # Simulate 20ms vector search
                return [
                    {
                        'id': f'doc{i}',
                        'content': f'Document content {i}',
                        'similarity': 0.95 - i*0.05,
                        'metadata': {'type': 'fixture'}
                    }
                    for i in range(min(top_k, 5))
                ]
            
            mock_vector_instance = AsyncMock()
            mock_vector_instance.search = mock_search
            mock_vector.return_value = mock_vector_instance
            
            # Run performance tests
            times = []
            for _ in range(50):
                time_ms, response = await self.measure_response_time(
                    client.post(
                        "/analysis/search",
                        json={"query": "Arsenal matches", "top_k": 10},
                        headers=auth_headers
                    )
                )
                assert response.status_code == 200
                times.append(time_ms)
            
            # Assert <100ms for vector search
            self.assert_performance(times, target_ms=100)
    
    @pytest.mark.asyncio
    async def test_concurrent_request_performance(self, client, auth_headers, mock_user):
        """Test: System should handle concurrent requests efficiently."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.core.api_football.get_api_football_service') as mock_api:
            
            # Mock fast API responses
            mock_service = AsyncMock()
            mock_service.get_fixtures.return_value = {'response': []}
            mock_service.get_teams.return_value = {'response': []}
            mock_service.get_standings.return_value = {'response': []}
            mock_api.return_value = mock_service
            
            # Define concurrent requests
            async def make_requests():
                tasks = []
                # Mix of different endpoint types
                for i in range(10):
                    if i % 3 == 0:
                        task = client.get("/analysis/fixtures", headers=auth_headers)
                    elif i % 3 == 1:
                        task = client.get("/analysis/teams", headers=auth_headers)
                    else:
                        task = client.get("/analysis/standings?league_id=39", headers=auth_headers)
                    tasks.append(task)
                
                start = time.perf_counter()
                responses = await asyncio.gather(*tasks)
                end = time.perf_counter()
                
                # All should succeed
                for response in responses:
                    assert response.status_code == 200
                
                return (end - start) * 1000
            
            # Run concurrent request tests
            times = []
            for _ in range(10):
                time_ms = await make_requests()
                times.append(time_ms)
            
            # Concurrent requests should complete efficiently
            # 10 requests should complete in <500ms total
            self.assert_performance(times, target_ms=500)
    
    @pytest.mark.asyncio
    async def test_websocket_latency(self, client, mock_user):
        """Test: WebSocket message delivery latency should be <50ms."""
        with patch('app.api.dependencies.get_current_user_ws', return_value=mock_user.id):
            
            # Connect to WebSocket
            async with client.websocket_connect("/ws/live-matches") as websocket:
                # Measure round-trip latency
                times = []
                
                for _ in range(50):
                    start = time.perf_counter()
                    
                    # Send message
                    await websocket.send_json({
                        "type": "get_live_matches"
                    })
                    
                    # Receive response
                    response = await websocket.receive_json()
                    
                    end = time.perf_counter()
                    times.append((end - start) * 1000)
                
                # Assert <50ms latency for WebSocket messages
                self.assert_performance(times, target_ms=50)
    
    @pytest.mark.asyncio
    async def test_cache_performance(self, client, auth_headers, mock_user):
        """Test: Cached responses should be significantly faster."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.core.api_football.get_api_football_service') as mock_api:
            
            # Mock API with caching behavior
            call_count = 0
            
            async def mock_get_fixtures(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    # First call - simulate API delay
                    await asyncio.sleep(0.1)
                # Subsequent calls should hit cache (no delay)
                return {'response': [{'fixture': {'id': 1}}]}
            
            mock_service = AsyncMock()
            mock_service.get_fixtures = mock_get_fixtures
            mock_api.return_value = mock_service
            
            # First call (cache miss)
            time_uncached, response1 = await self.measure_response_time(
                client.get("/analysis/fixtures?date=2025-01-25", headers=auth_headers)
            )
            assert response1.status_code == 200
            
            # Subsequent calls (cache hits)
            cached_times = []
            for _ in range(20):
                time_ms, response = await self.measure_response_time(
                    client.get("/analysis/fixtures?date=2025-01-25", headers=auth_headers)
                )
                assert response.status_code == 200
                cached_times.append(time_ms)
            
            # Cached responses should be at least 5x faster
            avg_cached = mean(cached_times)
            assert avg_cached < time_uncached / 5, f"Cache not effective: uncached={time_uncached:.2f}ms, cached={avg_cached:.2f}ms"
            
            # Cached responses should be <20ms
            self.assert_performance(cached_times, target_ms=20)
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, client, auth_headers, mock_user):
        """Test: Database queries should be optimized and fast."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db:
            
            # Mock database with realistic query times
            async def mock_query_execute(*args, **kwargs):
                await asyncio.sleep(0.005)  # 5ms query time
                return Mock(
                    scalar_one_or_none=lambda: Mock(id=uuid4()),
                    all=lambda: [Mock(id=uuid4()) for _ in range(10)],
                    count=lambda: 100
                )
            
            mock_session = AsyncMock()
            mock_session.execute = mock_query_execute
            mock_db.return_value.__aenter__.return_value = mock_session
            
            # Test various database operations
            times = []
            
            # Get user sessions (pagination query)
            for _ in range(30):
                time_ms, response = await self.measure_response_time(
                    client.get("/chat/sessions?page=1&limit=20", headers=auth_headers)
                )
                assert response.status_code == 200
                times.append(time_ms)
            
            # Database queries should be <30ms
            self.assert_performance(times, target_ms=30)


class TestLoadCapacity:
    """Test system capacity under load."""
    
    @pytest.mark.asyncio
    async def test_sustained_load(self, client, auth_headers, mock_user):
        """Test: System should handle sustained load without degradation."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.core.api_football.get_api_football_service') as mock_api:
            
            mock_service = AsyncMock()
            mock_service.get_fixtures.return_value = {'response': []}
            mock_api.return_value = mock_service
            
            # Simulate sustained load for 10 seconds
            start_time = time.time()
            request_times = []
            error_count = 0
            
            while time.time() - start_time < 10:
                # 100 requests per second
                batch_tasks = []
                for _ in range(10):
                    task = client.get("/analysis/fixtures", headers=auth_headers)
                    batch_tasks.append(task)
                
                batch_start = time.perf_counter()
                responses = await asyncio.gather(*batch_tasks, return_exceptions=True)
                batch_end = time.perf_counter()
                
                for response in responses:
                    if isinstance(response, Exception):
                        error_count += 1
                    elif response.status_code != 200:
                        error_count += 1
                
                request_times.append((batch_end - batch_start) * 1000)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
            
            # System should maintain performance under load
            assert error_count == 0, f"Errors under load: {error_count}"
            assert mean(request_times) < 200, f"Performance degraded under load: {mean(request_times):.2f}ms"