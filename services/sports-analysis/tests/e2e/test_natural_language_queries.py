"""
End-to-end tests for natural language query processing.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from uuid import uuid4
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock, Mock

from main import app


class TestNaturalLanguageQueries:
    """E2E tests for natural language sports queries."""
    
    @pytest.fixture
    async def client(self):
        """Create async test client."""
        async with Async<PERSON>lient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer mock-jwt-token"}
    
    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return Mock(
            id=uuid4(),
            username='testuser',
            email='<EMAIL>',
            roles=['user', 'premium']
        )
    
    @pytest.fixture
    def mock_rag_results(self):
        """Mock RAG search results for different queries."""
        return {
            'london_teams': [
                {
                    'content': 'Arsenal will host Newcastle United at Emirates Stadium on Saturday, January 25th at 3:00 PM',
                    'metadata': {'type': 'fixture', 'teams': ['Arsenal', 'Newcastle'], 'venue': 'Emirates Stadium'},
                    'similarity': 0.92
                },
                {
                    'content': 'Chelsea faces Brighton at Stamford Bridge on Saturday, January 25th at 5:30 PM',
                    'metadata': {'type': 'fixture', 'teams': ['Chelsea', 'Brighton'], 'venue': 'Stamford Bridge'},
                    'similarity': 0.89
                },
                {
                    'content': 'Tottenham travels to Manchester for away fixture on Saturday',
                    'metadata': {'type': 'fixture', 'teams': ['Tottenham', 'Manchester United']},
                    'similarity': 0.75
                }
            ],
            'team_form': [
                {
                    'content': 'Liverpool has won 5 of their last 6 away matches with impressive scoring record',
                    'metadata': {'type': 'stats', 'team': 'Liverpool', 'form': 'WWWLWW'},
                    'similarity': 0.88
                },
                {
                    'content': 'Manchester City showing strong away form with 4 wins in last 5 road games',
                    'metadata': {'type': 'stats', 'team': 'Manchester City', 'form': 'WWDWW'},
                    'similarity': 0.85
                }
            ],
            'odds_value': [
                {
                    'content': 'Arsenal vs Newcastle: Home win @ 1.85 shows value given Arsenal\'s strong home record',
                    'metadata': {'type': 'odds', 'fixture': 'Arsenal vs Newcastle', 'recommendation': 'value'},
                    'similarity': 0.90
                },
                {
                    'content': 'Liverpool @ 2.20 away to Wolves presents good value based on recent performances',
                    'metadata': {'type': 'odds', 'fixture': 'Wolves vs Liverpool', 'recommendation': 'value'},
                    'similarity': 0.87
                }
            ],
            'team_comparison': [
                {
                    'content': 'Liverpool attack: 45 goals in 20 matches (2.25 per game), top scorers: Salah (15), Nunez (8)',
                    'metadata': {'type': 'analysis', 'team': 'Liverpool', 'category': 'attack'},
                    'similarity': 0.93
                },
                {
                    'content': 'Manchester City defense: 12 goals conceded in 20 matches (0.6 per game), 12 clean sheets',
                    'metadata': {'type': 'analysis', 'team': 'Manchester City', 'category': 'defense'},
                    'similarity': 0.91
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_london_teams_query(self, client, auth_headers, mock_user, mock_rag_results):
        """Test query: 'Which London teams play at home this Saturday?'"""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Setup mocks
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True,
                ai_model='gpt-4'
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # Mock RAG engine
            mock_rag_instance = AsyncMock()
            mock_rag_instance.semantic_search.return_value = mock_rag_results['london_teams']
            mock_rag.return_value = mock_rag_instance
            
            # Mock chat processor
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message.return_value = {
                'response': 'Based on the fixtures, two London teams are playing at home this Saturday:\n\n1. **Arsenal** hosts Newcastle United at Emirates Stadium at 3:00 PM\n2. **Chelsea** hosts Brighton at Stamford Bridge at 5:30 PM\n\nTottenham is playing away at Manchester United.',
                'response_type': 'informative',
                'rag_used': True,
                'retrieval_results': mock_rag_results['london_teams'],
                'similarity_scores': [0.92, 0.89, 0.75],
                'avg_similarity_score': 0.85,
                'tokens_used': 200,
                'cost': 0.004,
                'confidence': 0.95,
                'entities': ['Arsenal', 'Chelsea', 'Tottenham', 'Newcastle United', 'Brighton'],
                'topics': ['Premier League', 'London teams', 'fixtures'],
                'follow_ups': [
                    'Would you like to see the odds for these matches?',
                    'Do you want more details about any specific match?'
                ]
            }
            mock_processor.return_value = mock_processor_instance
            
            # Create session first
            create_response = await client.post(
                "/chat/sessions",
                json={
                    "title": "Premier League Queries",
                    "session_type": "sports_analysis",
                    "rag_enabled": True
                },
                headers=auth_headers
            )
            assert create_response.status_code == 200
            
            # Send the query
            message_data = {
                "message": "Which London teams play at home this Saturday?",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/message",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response quality
            assert data['rag_used'] is True
            assert data['confidence_score'] > 0.9
            assert 'Arsenal' in data['ai_response']
            assert 'Chelsea' in data['ai_response']
            assert 'Emirates Stadium' in data['ai_response']
            assert 'Stamford Bridge' in data['ai_response']
            assert len(data['retrieval_sources']) >= 2
            assert len(data['suggested_follow_ups']) > 0
    
    @pytest.mark.asyncio
    async def test_team_form_query(self, client, auth_headers, mock_user, mock_rag_results):
        """Test query: 'Show me teams with the best recent away form'"""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Setup mocks
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # Mock RAG engine
            mock_rag_instance = AsyncMock()
            mock_rag_instance.semantic_search.return_value = mock_rag_results['team_form']
            mock_rag.return_value = mock_rag_instance
            
            # Mock chat processor
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message.return_value = {
                'response': 'The teams with the best recent away form are:\n\n1. **Liverpool** - Won 5 of their last 6 away matches (WWWLWW)\n   - Impressive scoring record on the road\n   - Only 1 loss in last 6 away games\n\n2. **Manchester City** - Won 4 of their last 5 away matches (WWDWW)\n   - Strong defensive record away from home\n   - Unbeaten in last 5 (4W, 1D)',
                'response_type': 'analytical',
                'rag_used': True,
                'retrieval_results': mock_rag_results['team_form'],
                'similarity_scores': [0.88, 0.85],
                'avg_similarity_score': 0.865,
                'tokens_used': 180,
                'cost': 0.0036,
                'confidence': 0.92,
                'entities': ['Liverpool', 'Manchester City'],
                'topics': ['team form', 'away performance', 'statistics'],
                'follow_ups': [
                    'Would you like to see their upcoming away fixtures?',
                    'Want to compare their away stats in more detail?'
                ]
            }
            mock_processor.return_value = mock_processor_instance
            
            # Send the query
            message_data = {
                "message": "Show me teams with the best recent away form",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/message",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response
            assert data['response_type'] == 'analytical'
            assert 'Liverpool' in data['ai_response']
            assert 'Manchester City' in data['ai_response']
            assert 'WWWLWW' in data['ai_response'] or '5 of their last 6' in data['ai_response']
    
    @pytest.mark.asyncio
    async def test_value_odds_query(self, client, auth_headers, mock_user, mock_rag_results):
        """Test query: 'Which games have value odds based on recent form?'"""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Setup mocks similar to above
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # Mock RAG engine
            mock_rag_instance = AsyncMock()
            mock_rag_instance.semantic_search.return_value = mock_rag_results['odds_value']
            mock_rag.return_value = mock_rag_instance
            
            # Mock chat processor
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message.return_value = {
                'response': 'Based on recent form analysis, here are matches with value odds:\n\n1. **Arsenal vs Newcastle** - Home win @ 1.85\n   - Arsenal\'s strong home record makes this good value\n   - Newcastle struggling away from home\n\n2. **Wolves vs Liverpool** - Away win @ 2.20\n   - Liverpool\'s excellent away form (5W in last 6)\n   - Odds seem generous given Liverpool\'s current performance\n\n⚠️ Remember to gamble responsibly and within your means.',
                'response_type': 'recommendation',
                'rag_used': True,
                'retrieval_results': mock_rag_results['odds_value'],
                'similarity_scores': [0.90, 0.87],
                'avg_similarity_score': 0.885,
                'tokens_used': 220,
                'cost': 0.0044,
                'confidence': 0.88,
                'entities': ['Arsenal', 'Newcastle', 'Liverpool', 'Wolves'],
                'topics': ['betting odds', 'value bets', 'form analysis'],
                'follow_ups': [
                    'Would you like detailed stats for these teams?',
                    'Want to see head-to-head records?'
                ]
            }
            mock_processor.return_value = mock_processor_instance
            
            # Send the query
            message_data = {
                "message": "Which games have value odds based on recent form?",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/message",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response includes responsible gambling message
            assert 'responsibly' in data['ai_response'].lower()
            assert data['response_type'] == 'recommendation'
            assert '@' in data['ai_response']  # Contains odds
    
    @pytest.mark.asyncio
    async def test_team_comparison_query(self, client, auth_headers, mock_user, mock_rag_results):
        """Test query: 'Compare Liverpool's attack to Manchester City's defense'"""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor, \
             patch('app.core.comparison_engine.get_comparison_engine') as mock_comparison:
            
            # Setup mocks
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # Mock RAG engine
            mock_rag_instance = AsyncMock()
            mock_rag_instance.semantic_search.return_value = mock_rag_results['team_comparison']
            mock_rag.return_value = mock_rag_instance
            
            # Mock comparison engine
            mock_comparison_instance = AsyncMock()
            mock_comparison_instance.compare_teams.return_value = {
                'team1_strengths': ['High scoring rate', 'Multiple goal threats'],
                'team2_strengths': ['Best defensive record', 'Most clean sheets'],
                'analysis': 'Classic clash of styles'
            }
            mock_comparison.return_value = mock_comparison_instance
            
            # Mock chat processor
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message.return_value = {
                'response': '## Liverpool Attack vs Manchester City Defense\n\n### Liverpool Attack (League Leaders)\n- **45 goals** in 20 matches (2.25 per game)\n- Top scorers: Salah (15), Nunez (8)\n- Multiple goal threats across the front line\n\n### Manchester City Defense (Best in League)\n- Only **12 goals conceded** in 20 matches (0.6 per game)\n- **12 clean sheets** - highest in the league\n- Exceptional defensive organization\n\n### Analysis\nThis represents a classic clash of styles - Liverpool\'s prolific attack against City\'s impenetrable defense. Historical data shows these matchups often produce tight, low-scoring games.',
                'response_type': 'comparison',
                'rag_used': True,
                'retrieval_results': mock_rag_results['team_comparison'],
                'similarity_scores': [0.93, 0.91],
                'avg_similarity_score': 0.92,
                'tokens_used': 250,
                'cost': 0.005,
                'confidence': 0.94,
                'entities': ['Liverpool', 'Manchester City', 'Salah', 'Nunez'],
                'topics': ['team comparison', 'attack vs defense', 'statistics'],
                'follow_ups': [
                    'Would you like to see their head-to-head record?',
                    'Want to analyze specific player matchups?'
                ]
            }
            mock_processor.return_value = mock_processor_instance
            
            # Send the query
            message_data = {
                "message": "Compare Liverpool's attack to Manchester City's defense",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/message",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify comparison response
            assert data['response_type'] == 'comparison'
            assert '45 goals' in data['ai_response'] or '2.25' in data['ai_response']
            assert '12 goals conceded' in data['ai_response'] or '0.6' in data['ai_response']
            assert 'Salah' in data['ai_response']
            assert data['confidence_score'] > 0.9
    
    @pytest.mark.asyncio
    async def test_conversation_context(self, client, auth_headers, mock_user):
        """Test maintaining context across multiple messages."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user.id), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            session_id = uuid4()
            mock_session = Mock(
                id=session_id,
                user_id=mock_user.id,
                status='active',
                rag_enabled=True,
                conversation_context={}
            )
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            
            # First message
            mock_processor_instance = AsyncMock()
            mock_processor_instance.process_message.return_value = {
                'response': 'Arsenal is playing Newcastle at home this Saturday at 3 PM.',
                'response_type': 'informative',
                'rag_used': True,
                'retrieval_results': [],
                'similarity_scores': [],
                'avg_similarity_score': 0,
                'tokens_used': 50,
                'cost': 0.001,
                'confidence': 0.95,
                'entities': ['Arsenal', 'Newcastle'],
                'topics': ['fixtures'],
                'follow_ups': []
            }
            mock_processor.return_value = mock_processor_instance
            
            # First query
            response1 = await client.post(
                f"/chat/sessions/{session_id}/message",
                json={"message": "When does Arsenal play next?", "context": {}},
                headers=auth_headers
            )
            assert response1.status_code == 200
            
            # Update processor mock for second message
            mock_processor_instance.process_message.return_value = {
                'response': 'Based on our previous discussion about Arsenal, their recent home form shows 4 wins in last 5 matches at Emirates Stadium.',
                'response_type': 'informative',
                'rag_used': True,
                'retrieval_results': [],
                'similarity_scores': [],
                'avg_similarity_score': 0,
                'tokens_used': 60,
                'cost': 0.0012,
                'confidence': 0.93,
                'entities': ['Arsenal'],
                'topics': ['team form'],
                'follow_ups': []
            }
            
            # Second query referencing previous context
            response2 = await client.post(
                f"/chat/sessions/{session_id}/message",
                json={"message": "What about their recent form?", "context": {"refers_to": "Arsenal"}},
                headers=auth_headers
            )
            assert response2.status_code == 200
            data2 = response2.json()
            
            # Should reference Arsenal from context
            assert 'Arsenal' in data2['ai_response']
            assert 'previous discussion' in data2['ai_response'] or 'Emirates' in data2['ai_response']