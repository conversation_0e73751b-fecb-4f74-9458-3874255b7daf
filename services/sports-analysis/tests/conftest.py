"""
Test configuration and fixtures for Sports Analysis & AI Chat.
"""

import pytest
import asyncio
from datetime import datetime
from uuid import uuid4
from typing import Dict, Any

# Test fixtures

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_user():
    """Mock user data for testing."""
    return {
        'id': uuid4(),
        'username': 'testuser',
        'email': '<EMAIL>',
        'roles': ['user'],
        'subscription_tier': 'basic',
        'created_at': datetime.utcnow()
    }

@pytest.fixture
def mock_premium_user():
    """Mock premium user data for testing."""
    return {
        'id': uuid4(),
        'username': 'premiumuser',
        'email': '<EMAIL>',
        'roles': ['user', 'premium'],
        'subscription_tier': 'pro',
        'created_at': datetime.utcnow()
    }

@pytest.fixture
def mock_fixture_data():
    """Mock fixture data from API-Football."""
    return {
        'fixture': {
            'id': 12345,
            'date': '2025-01-25T15:00:00Z',
            'status': {'short': 'FT'}
        },
        'league': {
            'id': 39,
            'name': 'Premier League',
            'country': 'England'
        },
        'teams': {
            'home': {
                'id': 1,
                'name': 'Arsenal',
                'logo': 'https://example.com/arsenal.png'
            },
            'away': {
                'id': 2,
                'name': 'Chelsea',
                'logo': 'https://example.com/chelsea.png'
            }
        },
        'goals': {
            'home': 2,
            'away': 1
        }
    }

@pytest.fixture
def mock_chat_session():
    """Mock chat session data."""
    return {
        'id': uuid4(),
        'user_id': uuid4(),
        'session_title': 'Test Chat Session',
        'session_type': 'sports_analysis',
        'status': 'active',
        'created_at': datetime.utcnow(),
        'rag_enabled': True
    }

@pytest.fixture
def mock_ppv_event():
    """Mock PPV event data."""
    return {
        'id': uuid4(),
        'event_title': 'Arsenal vs Chelsea - Premier League',
        'event_description': 'London Derby match',
        'event_type': 'football_match',
        'price': 9.99,
        'currency': 'USD',
        'starts_at': datetime.utcnow(),
        'status': 'scheduled',
        'stream_url': 'https://stream.betbet.com/event/123',
        'created_at': datetime.utcnow()
    }

@pytest.fixture
def mock_prediction_data():
    """Mock prediction data."""
    return {
        'fixture_id': 12345,
        'home_team_name': 'Arsenal',
        'away_team_name': 'Chelsea',
        'predicted_outcome': 'home_win',
        'home_win_probability': 0.65,
        'draw_probability': 0.20,
        'away_win_probability': 0.15,
        'confidence_score': 0.78,
        'key_factors': ['Home advantage', 'Recent form']
    }

@pytest.fixture
def mock_document_content():
    """Mock document content for testing."""
    return """
    Premier League Fixtures - January 2025
    
    Saturday 25th January:
    Arsenal vs Chelsea - 15:00 - Emirates Stadium
    Manchester United vs Liverpool - 17:30 - Old Trafford
    
    Sunday 26th January:
    Manchester City vs Tottenham - 16:00 - Etihad Stadium
    """

# Test configuration
pytest_plugins = ['pytest_asyncio']