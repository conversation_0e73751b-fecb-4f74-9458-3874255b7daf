#!/usr/bin/env python3
"""
Comprehensive test script for natural language query processing.

This script tests the key natural language queries that make BetBet's
Sports Analysis & AI Chat a competitive differentiator.

Run: python test_natural_language.py
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any, List
import httpx

# Test configuration
BASE_URL = "http://localhost:8004"
TEST_USER_TOKEN = "mock-jwt-token"  # Would be real JWT in production

# Test queries that must work perfectly
TEST_QUERIES = [
    {
        "name": "London Teams Home Games",
        "query": "Which London teams play at home this Saturday?",
        "expected_entities": ["Arsenal", "Chelsea", "Tottenham"],
        "expected_topics": ["Premier League", "London teams", "fixtures"],
        "min_confidence": 0.8,
        "max_response_time": 2000  # 2 seconds
    },
    {
        "name": "Best Away Form",
        "query": "Show me teams with the best recent away form",
        "expected_entities": ["Liverpool", "Manchester City"],
        "expected_topics": ["team form", "away performance", "statistics"],
        "min_confidence": 0.85,
        "max_response_time": 2000
    },
    {
        "name": "Value Odds Analysis",
        "query": "Based on my uploaded fixtures, which games have value odds?",
        "expected_entities": ["Arsenal", "Liverpool"],
        "expected_topics": ["betting odds", "value bets", "analysis"],
        "min_confidence": 0.8,
        "max_response_time": 2500  # Slightly higher for document analysis
    },
    {
        "name": "Team Comparison",
        "query": "Compare Liverpool's attack to Manchester City's defense",
        "expected_entities": ["Liverpool", "Manchester City"],
        "expected_topics": ["team comparison", "statistics"],
        "min_confidence": 0.9,
        "max_response_time": 2000
    },
    {
        "name": "Complex Multi-Team Query",
        "query": "What are the head-to-head records between Liverpool, Arsenal, and Manchester City this season?",
        "expected_entities": ["Liverpool", "Arsenal", "Manchester City"],
        "expected_topics": ["head-to-head", "records", "season statistics"],
        "min_confidence": 0.75,
        "max_response_time": 3000
    },
    {
        "name": "Player Performance Query",
        "query": "Show me the top Premier League goal scorers who are likely to score this weekend",
        "expected_entities": ["Premier League"],
        "expected_topics": ["goal scorers", "player performance", "predictions"],
        "min_confidence": 0.8,
        "max_response_time": 2500
    }
]

class NaturalLanguageValidator:
    """Validates natural language query processing."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=10.0)
        self.session_id = None
        
    async def setup(self) -> bool:
        """Set up test environment."""
        try:
            # Check service health
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code != 200:
                print("❌ Service health check failed")
                return False
            
            # Create chat session
            session_data = {
                "title": "Natural Language Test Session",
                "session_type": "sports_analysis",
                "rag_enabled": True,
                "ai_model": "gpt-4",
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{self.base_url}/chat/sessions",
                json=session_data,
                headers={"Authorization": f"Bearer {TEST_USER_TOKEN}"}
            )
            
            if response.status_code != 200:
                print(f"❌ Failed to create chat session: {response.status_code}")
                return False
            
            self.session_id = response.json().get('id')
            print(f"✅ Test session created: {self.session_id}")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    async def test_query(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single natural language query."""
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"Query: \"{test_case['query']}\"")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/sessions/{self.session_id}/message",
                json={
                    "message": test_case['query'],
                    "context": {}
                },
                headers={"Authorization": f"Bearer {TEST_USER_TOKEN}"}
            )
            
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "response_time": response_time
                }
            
            data = response.json()
            
            # Validate response structure
            required_fields = ['ai_response', 'rag_used', 'confidence_score']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return {
                    "success": False,
                    "error": f"Missing fields: {missing_fields}",
                    "response_time": response_time
                }
            
            # Validate RAG was used
            if not data.get('rag_used', False):
                print("⚠️  RAG was not used for this query")
            
            # Validate confidence score
            confidence = data.get('confidence_score', 0)
            if confidence < test_case['min_confidence']:
                print(f"⚠️  Low confidence: {confidence:.2f} < {test_case['min_confidence']}")
            
            # Validate response time
            if response_time > test_case['max_response_time']:
                print(f"⚠️  Slow response: {response_time:.0f}ms > {test_case['max_response_time']}ms")
            
            # Validate entities (if response contains expected entities)
            entities_found = data.get('entities_found', [])
            expected_entities = test_case.get('expected_entities', [])
            entities_match = any(entity.lower() in data['ai_response'].lower() 
                               for entity in expected_entities) if expected_entities else True
            
            # Validate topics
            topics_found = data.get('topics', [])
            
            result = {
                "success": True,
                "response_time": response_time,
                "confidence": confidence,
                "rag_used": data.get('rag_used', False),
                "entities_found": entities_found,
                "entities_match": entities_match,
                "topics_found": topics_found,
                "response_length": len(data['ai_response']),
                "retrieval_sources": len(data.get('retrieval_sources', [])),
                "ai_response": data['ai_response'][:200] + "..." if len(data['ai_response']) > 200 else data['ai_response']
            }
            
            # Print results
            print(f"✅ Response time: {response_time:.0f}ms")
            print(f"✅ Confidence: {confidence:.2f}")
            print(f"✅ RAG used: {data.get('rag_used', False)}")
            print(f"✅ Sources: {len(data.get('retrieval_sources', []))}")
            print(f"📝 Response preview: {result['ai_response']}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "response_time": (time.time() - start_time) * 1000
            }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all natural language tests."""
        print("🚀 Starting Natural Language Query Validation")
        print("=" * 60)
        
        if not await self.setup():
            return {"success": False, "error": "Setup failed"}
        
        results = []
        total_tests = len(TEST_QUERIES)
        passed_tests = 0
        total_response_time = 0
        
        for i, test_case in enumerate(TEST_QUERIES, 1):
            print(f"\n[{i}/{total_tests}] " + "=" * 50)
            
            result = await self.test_query(test_case)
            result['test_name'] = test_case['name']
            result['query'] = test_case['query']
            results.append(result)
            
            if result['success']:
                passed_tests += 1
                total_response_time += result['response_time']
                
                # Check performance benchmarks
                if result['response_time'] <= test_case['max_response_time']:
                    print("✅ Performance: PASS")
                else:
                    print(f"⚠️  Performance: SLOW ({result['response_time']:.0f}ms)")
                
                if result.get('confidence', 0) >= test_case['min_confidence']:
                    print("✅ Confidence: PASS")
                else:
                    print(f"⚠️  Confidence: LOW ({result.get('confidence', 0):.2f})")
            else:
                print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 NATURAL LANGUAGE VALIDATION SUMMARY")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        avg_response_time = total_response_time / passed_tests if passed_tests > 0 else 0
        
        print(f"Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"Average response time: {avg_response_time:.0f}ms")
        
        # Performance targets
        performance_target = 2000  # 2 seconds
        confidence_target = 0.8
        
        avg_confidence = sum(r.get('confidence', 0) for r in results if r['success']) / passed_tests if passed_tests > 0 else 0
        
        print(f"Average confidence: {avg_confidence:.2f}")
        print(f"Performance target (<{performance_target}ms): {'✅ PASS' if avg_response_time <= performance_target else '❌ FAIL'}")
        print(f"Confidence target (>{confidence_target}): {'✅ PASS' if avg_confidence >= confidence_target else '❌ FAIL'}")
        
        # Key differentiator queries
        key_queries = ["London Teams Home Games", "Best Away Form", "Team Comparison"]
        key_query_results = [r for r in results if r['test_name'] in key_queries and r['success']]
        
        print(f"\n🎯 Key Differentiator Queries: {len(key_query_results)}/{len(key_queries)} working")
        
        # Overall assessment
        production_ready = (
            success_rate >= 85 and  # At least 85% success rate
            avg_response_time <= performance_target and  # Meet performance target
            avg_confidence >= confidence_target and  # Meet confidence target
            len(key_query_results) == len(key_queries)  # All key queries work
        )
        
        print(f"\n🏆 PRODUCTION READY: {'✅ YES' if production_ready else '❌ NO'}")
        
        if production_ready:
            print("\n🎉 Sports Analysis & AI Chat is ready for production!")
            print("The natural language queries that make BetBet competitive are working correctly.")
        else:
            print("\n⚠️  Additional work needed before production deployment.")
        
        return {
            "success": production_ready,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "avg_confidence": avg_confidence,
            "key_queries_working": len(key_query_results),
            "results": results
        }
    
    async def cleanup(self):
        """Clean up resources."""
        await self.client.aclose()

async def main():
    """Main test runner."""
    validator = NaturalLanguageValidator(BASE_URL)
    
    try:
        results = await validator.run_all_tests()
        
        # Write results to file
        import json
        with open('natural_language_test_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: natural_language_test_results.json")
        
        # Exit with appropriate code
        exit_code = 0 if results['success'] else 1
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        return 1
    finally:
        await validator.cleanup()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)