#!/usr/bin/env python3
"""
Test runner for Sports Analysis & AI Chat service.
Executes comprehensive test suite and generates coverage reports.
"""

import sys
import subprocess
import os
from pathlib import Path

def run_tests():
    """Run the complete test suite."""
    
    print("🧪 Running Sports Analysis & AI Chat Test Suite")
    print("=" * 60)
    
    # Change to service directory
    service_dir = Path(__file__).parent
    os.chdir(service_dir)
    
    # Install test dependencies if needed
    print("📦 Ensuring test dependencies are installed...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Warning: Could not install dependencies: {e}")
    
    # Run unit tests
    print("\n🔍 Running Unit Tests...")
    unit_result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/unit/", 
        "-v", 
        "--cov=app/core",
        "--cov-report=term-missing"
    ], capture_output=True, text=True)
    
    print(unit_result.stdout)
    if unit_result.stderr:
        print("STDERR:", unit_result.stderr)
    
    # Run integration tests
    print("\n🔗 Running Integration Tests...")
    integration_result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/integration/", 
        "-v",
        "--cov=app/api",
        "--cov=app/websocket",
        "--cov-append"
    ], capture_output=True, text=True)
    
    print(integration_result.stdout)
    if integration_result.stderr:
        print("STDERR:", integration_result.stderr)
    
    # Generate comprehensive coverage report
    print("\n📊 Generating Coverage Report...")
    coverage_result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/", 
        "--cov=app",
        "--cov-report=html:coverage_html",
        "--cov-report=term-missing",
        "--cov-fail-under=85"
    ], capture_output=True, text=True)
    
    print(coverage_result.stdout)
    if coverage_result.stderr:
        print("STDERR:", coverage_result.stderr)
    
    # Test results summary
    print("\n📋 Test Results Summary")
    print("=" * 40)
    
    unit_passed = unit_result.returncode == 0
    integration_passed = integration_result.returncode == 0
    coverage_passed = coverage_result.returncode == 0
    
    print(f"Unit Tests: {'✅ PASSED' if unit_passed else '❌ FAILED'}")
    print(f"Integration Tests: {'✅ PASSED' if integration_passed else '❌ FAILED'}")
    print(f"Coverage (85%+): {'✅ PASSED' if coverage_passed else '❌ FAILED'}")
    
    # Overall result
    all_passed = unit_passed and integration_passed and coverage_passed
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if coverage_passed:
        print("📊 Coverage report generated in: coverage_html/index.html")
    
    return all_passed

def run_specific_tests(test_pattern=None):
    """Run specific tests matching pattern."""
    
    print(f"🧪 Running Specific Tests: {test_pattern}")
    print("=" * 50)
    
    cmd = [sys.executable, "-m", "pytest"]
    
    if test_pattern:
        cmd.extend(["-k", test_pattern])
    
    cmd.extend([
        "-v",
        "--cov=app",
        "--cov-report=term-missing"
    ])
    
    result = subprocess.run(cmd)
    return result.returncode == 0

def run_performance_tests():
    """Run performance-specific tests."""
    
    print("⚡ Running Performance Tests...")
    print("=" * 40)
    
    # Performance test markers
    result = subprocess.run([
        sys.executable, "-m", "pytest",
        "-m", "performance",
        "-v",
        "--durations=10"  # Show 10 slowest tests
    ], capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    return result.returncode == 0

def main():
    """Main test runner."""
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "unit":
            # Run only unit tests
            result = subprocess.run([
                sys.executable, "-m", "pytest", "tests/unit/", "-v"
            ])
            sys.exit(result.returncode)
            
        elif command == "integration":
            # Run only integration tests
            result = subprocess.run([
                sys.executable, "-m", "pytest", "tests/integration/", "-v"
            ])
            sys.exit(result.returncode)
            
        elif command == "performance":
            # Run performance tests
            success = run_performance_tests()
            sys.exit(0 if success else 1)
            
        elif command.startswith("--pattern="):
            # Run tests matching pattern
            pattern = command.split("=", 1)[1]
            success = run_specific_tests(pattern)
            sys.exit(0 if success else 1)
            
        else:
            print(f"Unknown command: {command}")
            print("Available commands:")
            print("  unit          - Run unit tests only")
            print("  integration   - Run integration tests only")
            print("  performance   - Run performance tests")
            print("  --pattern=X   - Run tests matching pattern X")
            sys.exit(1)
    
    else:
        # Run all tests
        success = run_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()