#!/usr/bin/env python3
import sys
import requests

try:
    response = requests.get('http://localhost:8003/health', timeout=3)
    if response.status_code == 200:
        print('Health check passed')
        sys.exit(0)
    else:
        print(f'Health check failed with status: {response.status_code}')
        sys.exit(1)
except Exception as e:
    print(f'Health check failed: {str(e)}')
    sys.exit(1)