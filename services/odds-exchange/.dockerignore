# Python cache and bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Test files and coverage
test/
tests/
pytest.ini
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Development tools
.git/
.gitignore
.env.local
.env.development
.env.test
.flake8
.mypy_cache/
.dmypy.json
dmypy.json
pyproject.toml
setup.cfg
tox.ini

# Documentation
README.md
CHANGELOG.md
*.md
docs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/
*.log.*

# Temporary files
tmp/
temp/
.tmp/

# Development configs
docker-compose.yml
docker-compose.dev.yml
Dockerfile.dev

# Database files
*.db
*.sqlite
*.sqlite3