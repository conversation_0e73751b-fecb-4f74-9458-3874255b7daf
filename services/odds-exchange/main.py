import os
import sys
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Import models from the db-migrations schema
# Since we don't have separate models.py, we'll define basic models here
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class Market(Base):
    __tablename__ = "markets"
    __table_args__ = {"schema": "odds_exchange"}

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    event_id = Column(String(100), nullable=True)  # Made nullable to match DB
    market_type = Column(String(50), nullable=False)
    title = Column(String(500), nullable=True)  # Made nullable to match DB
    status = Column(String(20), nullable=False, default='active')
    total_matched = Column(Numeric(15, 2), default=0)
    total_unmatched = Column(Numeric(15, 2), default=0)  # Use existing column
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    closes_at = Column(DateTime(timezone=True))

class Order(Base):
    __tablename__ = "orders"
    __table_args__ = {"schema": "odds_exchange"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("odds_exchange.markets.id"), nullable=False)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    side = Column(String(10), nullable=False)  # back or lay
    odds = Column(Numeric(6, 3), nullable=False)
    stake = Column(Numeric(15, 2), nullable=False)
    status = Column(String(20), nullable=False, default='pending')
    matched_amount = Column(Numeric(15, 2), default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Pydantic models for API requests/responses
class MarketCreate(BaseModel):
    event_id: str
    market_type: str
    title: str
    closes_at: Optional[datetime] = None

class OrderCreate(BaseModel):
    market_id: str
    side: str  # 'back' or 'lay'
    odds: float
    stake: float
    
    @validator('side')
    def validate_side(cls, v):
        if v not in ['back', 'lay']:
            raise ValueError('side must be either "back" or "lay"')
        return v
    
    @validator('odds')
    def validate_odds(cls, v):
        if v <= 1.0:
            raise ValueError('odds must be greater than 1.0')
        return v
    
    @validator('stake')
    def validate_stake(cls, v):
        if v <= 0:
            raise ValueError('stake must be positive')
        return v

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    # Database Configuration
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",   # Web frontend
        "http://localhost:3001",   # Admin dashboard  
        "http://localhost:8000",   # API Gateway
        "https://betbet.com",      # Production web
        "https://admin.betbet.com" # Production admin
    ]
    
    SERVICE_NAME: str = "odds-exchange"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8003
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"

# Load settings
settings = Settings()

# Initialize FastAPI app
app = FastAPI(
    title="BetBet Odds Exchange",
    description="Trading platform for sports betting odds",
    version=settings.SERVICE_VERSION
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Odds Exchange connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup database connections on shutdown"""
    await close_database()

@app.get("/")
def read_root():
    return {
        "service": "BetBet Odds Exchange",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "markets": "/api/v1/trading/markets",
            "orders": "/api/v1/trading/orders",
            "orderbook": "/api/v1/trading/orderbook"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/trading/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get trading analytics overview"""

    # Get active markets count
    active_markets_query = select(func.count(Market.id)).where(
        Market.status == "active"
    )
    active_markets_result = await db.execute(active_markets_query)
    active_markets = active_markets_result.scalar() or 0

    # Get total trading volume
    total_volume_query = select(func.coalesce(func.sum(Order.stake), 0))
    total_volume_result = await db.execute(total_volume_query)
    total_volume = total_volume_result.scalar() or 0

    # Get today's volume
    from datetime import date
    today = date.today()
    daily_volume_query = select(func.coalesce(func.sum(Order.stake), 0)).where(
        func.date(Order.created_at) == today
    )
    daily_volume_result = await db.execute(daily_volume_query)
    daily_volume = daily_volume_result.scalar() or 0

    return {
        "active_markets": active_markets,
        "total_volume": float(total_volume),
        "daily_volume": float(daily_volume),
        "active_traders": 156  # This would come from user activity analysis
    }

@app.get("/api/v1/trading/markets")
async def get_markets(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_database_write)
):
    """Get available betting markets"""
    
    query = select(Market)
    
    if status:
        query = query.where(Market.status == status)
    
    query = query.order_by(Market.total_matched.desc())
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    markets = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(Market.id))
    if status:
        count_query = count_query.where(Market.status == status)
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    return {
        "markets": [{
            "id": str(market.id),
            "event_id": market.event_id,
            "market_type": market.market_type,
            "title": market.title,
            "status": market.status,
            "total_matched": float(market.total_matched),
            "total_volume": float(market.total_volume),
            "closes_at": market.closes_at.isoformat() if market.closes_at else None
        } for market in markets],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }

@app.get("/api/v1/trading/markets/popular")
async def get_popular_markets(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_database_write)
):
    """Get most popular markets by volume"""
    
    query = select(Market).where(
        Market.status == 'active'
    ).order_by(
        Market.total_matched.desc()
    ).limit(limit)
    
    result = await db.execute(query)
    markets = result.scalars().all()
    
    return [{
        "id": str(market.id),
        "title": market.title or "Untitled Market",
        "total_volume": float(market.total_matched + market.total_unmatched),
        "total_matched": float(market.total_matched)
    } for market in markets]

@app.get("/api/v1/trading/orderbook/{market_id}")
async def get_orderbook(
    market_id: str,
    db: AsyncSession = Depends(get_database_write)
):
    """Get orderbook for a specific market"""
    
    # Get back orders
    back_query = select(Order).where(
        Order.market_id == UUID(market_id),
        Order.side == 'back',
        Order.status == 'pending'
    ).order_by(Order.odds.desc()).limit(10)
    
    back_result = await db.execute(back_query)
    back_orders = back_result.scalars().all()
    
    # Get lay orders
    lay_query = select(Order).where(
        Order.market_id == UUID(market_id),
        Order.side == 'lay',
        Order.status == 'pending'
    ).order_by(Order.odds.asc()).limit(10)
    
    lay_result = await db.execute(lay_query)
    lay_orders = lay_result.scalars().all()
    
    return {
        "market_id": market_id,
        "backs": [{
            "odds": float(order.odds),
            "available": float(order.stake - order.matched_amount)
        } for order in back_orders],
        "lays": [{
            "odds": float(order.odds),
            "available": float(order.stake - order.matched_amount)
        } for order in lay_orders]
    }

@app.get("/api/v1/trading/analytics/overview")
async def get_trading_overview(db: AsyncSession = Depends(get_database_write)):
    """Get trading platform overview statistics"""
    
    # Active markets
    active_markets_query = select(func.count(Market.id)).where(
        Market.status == 'active'
    )
    active_markets_result = await db.execute(active_markets_query)
    active_markets = active_markets_result.scalar() or 0
    
    # Total volume today
    today = datetime.utcnow().date()
    volume_query = select(func.coalesce(func.sum(Market.total_volume), 0)).where(
        Market.created_at >= today
    )
    volume_result = await db.execute(volume_query)
    daily_volume = volume_result.scalar() or 0
    
    # Pending orders
    pending_orders_query = select(func.count(Order.id)).where(
        Order.status == 'pending'
    )
    pending_orders_result = await db.execute(pending_orders_query)
    pending_orders = pending_orders_result.scalar() or 0
    
    return {
        "active_markets": active_markets,
        "daily_volume": float(daily_volume),
        "pending_orders": pending_orders,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/api/v1/trading/markets")
async def create_market(
    market_data: MarketCreate,
    db: AsyncSession = Depends(get_database_write)
):
    """Create a new betting market"""
    
    try:
        # Create new market
        market = Market(
            event_id=market_data.event_id,
            market_type=market_data.market_type,
            title=market_data.title,
            closes_at=market_data.closes_at,
            status='active'
        )
        
        db.add(market)
        await db.commit()
        await db.refresh(market)
        
        return {
            "id": str(market.id),
            "event_id": market.event_id,
            "market_type": market.market_type,
            "title": market.title,
            "status": market.status,
            "total_matched": float(market.total_matched),
            "total_volume": float(market.total_volume),
            "closes_at": market.closes_at.isoformat() if market.closes_at else None,
            "created_at": market.created_at.isoformat()
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create market: {str(e)}")

@app.post("/api/v1/trading/orders")
async def place_order(
    order_data: OrderCreate,
    user_id: str = Query(..., description="User ID placing the order"),
    db: AsyncSession = Depends(get_database_write)
):
    """Place a new trading order"""
    
    try:
        # Verify market exists
        market_result = await db.execute(
            select(Market).where(Market.id == UUID(order_data.market_id))
        )
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise HTTPException(status_code=404, detail="Market not found")
        
        if market.status != 'active':
            raise HTTPException(status_code=400, detail="Market is not active")
        
        # Create new order
        order = Order(
            market_id=UUID(order_data.market_id),
            user_id=UUID(user_id),
            side=order_data.side,
            odds=Decimal(str(order_data.odds)),
            stake=Decimal(str(order_data.stake)),
            status='pending'
        )
        
        db.add(order)
        
        # Update market volume
        market.total_volume += Decimal(str(order_data.stake))
        
        await db.commit()
        await db.refresh(order)
        
        return {
            "id": str(order.id),
            "market_id": str(order.market_id),
            "user_id": str(order.user_id),
            "side": order.side,
            "odds": float(order.odds),
            "stake": float(order.stake),
            "status": order.status,
            "matched_amount": float(order.matched_amount),
            "created_at": order.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to place order: {str(e)}")

@app.get("/api/v1/trading/orders")
async def get_orders(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    market_id: Optional[str] = Query(None, description="Filter by market ID"),
    status: Optional[str] = Query(None, description="Filter by order status"),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get trading orders with filters"""
    
    query = select(Order)
    
    # Apply filters
    if user_id:
        query = query.where(Order.user_id == UUID(user_id))
    if market_id:
        query = query.where(Order.market_id == UUID(market_id))
    if status:
        query = query.where(Order.status == status)
    
    # Get total count
    count_query = select(func.count(Order.id))
    if user_id:
        count_query = count_query.where(Order.user_id == UUID(user_id))
    if market_id:
        count_query = count_query.where(Order.market_id == UUID(market_id))
    if status:
        count_query = count_query.where(Order.status == status)
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    # Apply pagination
    offset = (page - 1) * limit
    query = query.order_by(Order.created_at.desc()).offset(offset).limit(limit)
    
    result = await db.execute(query)
    orders = result.scalars().all()
    
    return {
        "orders": [{
            "id": str(order.id),
            "market_id": str(order.market_id),
            "user_id": str(order.user_id),
            "side": order.side,
            "odds": float(order.odds),
            "stake": float(order.stake),
            "status": order.status,
            "matched_amount": float(order.matched_amount),
            "created_at": order.created_at.isoformat()
        } for order in orders],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)