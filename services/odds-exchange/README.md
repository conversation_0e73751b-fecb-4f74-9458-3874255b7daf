# 📈 BetBet Odds Exchange & Trading Service

**Module 5: Financial-Grade Sports Trading Platform**

Ultra-low latency trading engine for professional sports betting and trading with real-time order matching, risk management, and market data streaming.

---

## 🎯 **Service Overview**

The Odds Exchange & Trading service provides a professional-grade trading platform for sports betting markets, enabling users to trade odds like financial instruments with advanced order types, real-time charts, and sophisticated risk management.

### **Key Features**
- ⚡ **Ultra-Low Latency**: <1ms order acceptance, <5ms execution
- 📊 **Real-Time Order Books**: Live bid/ask with market depth visualization
- 💹 **Advanced Trading**: Market, limit, stop-loss, and take-profit orders
- 🛡️ **Risk Management**: Real-time position limits and margin monitoring
- 📈 **Technical Analysis**: OHLCV data, indicators, and charting support
- 🔌 **WebSocket Streaming**: Live market data and order updates

### **Revenue Model**
- **Trading Fees**: 0.1-0.5% commission on trades
- **Market Making**: Rebates for liquidity providers
- **Premium Features**: Advanced analytics and API access
- **Target**: $50K+ monthly trading fee revenue

---

## 🚀 **Quick Start**

### **Prerequisites**
- Python 3.11+
- PostgreSQL with odds_exchange schema deployed
- Redis for caching (optional but recommended)

### **Installation**
```bash
# Navigate to service directory
cd services/odds-exchange

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="***************************************"
export JWT_SECRET_KEY="your-secret-key"
```

### **Running the Service**
```bash
# Development mode
uvicorn app.main:app --reload --port 8005

# Production mode
uvicorn app.main:app --host 0.0.0.0 --port 8005 --workers 4
```

### **Health Check**
```bash
curl http://localhost:8005/health
```

---

## 🏗️ **Architecture**

### **Core Components**

#### **1. Matching Engine** (`app/core/matching_engine.py`)
- High-performance order matching with price-time priority
- In-memory order book management
- Atomic trade execution
- Performance: <5ms trade execution

#### **2. Risk Manager** (`app/core/risk_manager.py`)
- Real-time position monitoring
- Pre-trade risk validation
- Margin requirement calculations
- Daily loss limits enforcement

#### **3. Market Data Manager** (`app/core/market_data_manager.py`)
- Price tick aggregation
- OHLCV data generation
- Technical indicator calculations
- Market summary statistics

### **API Structure**
```
/api/v1/
├── /orders          # Order management (submit, cancel, status)
├── /market-data     # Public market data endpoints
│   ├── /orderbook   # Real-time order book snapshots
│   ├── /ticker      # Market tickers with 24h stats
│   ├── /trades      # Recent trade history
│   └── /depth       # Market depth visualization
└── /ws/trading      # WebSocket for real-time updates
```

---

## 📡 **API Documentation**

### **Order Management**

#### **Submit Order**
```http
POST /api/v1/orders
Authorization: Bearer <token>

{
  "market_id": "uuid",
  "side": "buy",
  "order_type": "limit",
  "quantity": "100.00",
  "price": "1.85",
  "time_in_force": "GTC"
}
```

#### **Cancel Order**
```http
DELETE /api/v1/orders/{order_id}
Authorization: Bearer <token>
```

### **Market Data (Public)**

#### **Get Order Book**
```http
GET /api/v1/market-data/orderbook/{market_id}?levels=10
```

#### **Get Market Ticker**
```http
GET /api/v1/market-data/ticker/{market_id}
```

### **WebSocket Connection**
```javascript
const ws = new WebSocket('ws://localhost:8005/ws/trading/{user_id}');

// Subscribe to market
ws.send(JSON.stringify({
  type: 'subscribe_market',
  market_id: 'uuid'
}));

// Receive real-time updates
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // Handle orderbook updates, trades, etc.
};
```

---

## 🔧 **Development**

### **Project Structure**
```
odds-exchange/
├── app/
│   ├── api/
│   │   ├── dependencies.py    # Common dependencies
│   │   └── v1/
│   │       ├── orders.py       # Order endpoints
│   │       └── market_data.py  # Market data endpoints
│   ├── core/
│   │   ├── matching_engine.py # Order matching logic
│   │   ├── risk_manager.py    # Risk management
│   │   └── market_data_manager.py # Market data processing
│   ├── websocket/
│   │   └── routes.py          # WebSocket handlers
│   └── main.py               # FastAPI application
├── tests/
├── requirements.txt
└── README.md
```

### **Testing**
```bash
# Run unit tests
pytest tests/unit/

# Run integration tests
pytest tests/integration/

# Run with coverage
pytest --cov=app tests/
```

### **Performance Testing**
```bash
# Load test with locust
locust -f tests/performance/locustfile.py
```

---

## 🛡️ **Risk Management**

### **Position Limits**
- Maximum position size per market
- Concentration limits across markets
- Daily loss limits per user

### **Margin Requirements**
- 5% default margin requirement
- Dynamic margin based on volatility
- Real-time margin calls

### **Pre-Trade Validation**
- <2ms risk check before order acceptance
- Credit limit verification
- Position limit enforcement

---

## 📊 **Performance Standards**

### **Latency Targets**
- **Order Acceptance**: <1ms
- **Trade Execution**: <5ms
- **Market Data Updates**: <10ms
- **WebSocket Messages**: <10ms

### **Throughput Capacity**
- **Orders**: 10,000+ per second
- **Trades**: 5,000+ per second
- **WebSocket Connections**: 10,000+ concurrent
- **Market Data Updates**: 100,000+ per second

### **Monitoring**
- Prometheus metrics exposed on `/metrics`
- Performance tracking for all operations
- Real-time latency monitoring
- Capacity utilization dashboards

---

## 🔒 **Security**

### **Authentication**
- JWT-based authentication required for trading
- Role-based access control (trader, market_maker)
- API rate limiting per user

### **Financial Security**
- ACID compliance for all trades
- Immutable audit trails
- Position reconciliation
- Double-entry bookkeeping

---

## 🚦 **Production Deployment**

### **Environment Variables**
```bash
# Required
DATABASE_URL=**********************************
JWT_SECRET_KEY=your-secret-key
REDIS_URL=redis://localhost:6379

# Optional
MATCHING_ENGINE_ENABLED=true
RISK_MANAGEMENT_ENABLED=true
MAX_POSITION_SIZE=1000000.00
MARGIN_REQUIREMENT=0.05
```

### **Docker Deployment**
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8005"]
```

### **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: odds-exchange
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: odds-exchange
        image: betbet/odds-exchange:latest
        ports:
        - containerPort: 8005
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

---

## 📞 **Support & Contact**

### **Technical Support**
- **Service Owner**: BetBet Platform Team
- **Documentation**: [API Docs](http://localhost:8005/docs)
- **Health Status**: [Health Check](http://localhost:8005/health)

### **Monitoring**
- **Grafana Dashboard**: http://localhost:3001/d/odds-exchange
- **Prometheus Metrics**: http://localhost:8005/metrics
- **Log Aggregation**: Available in ELK stack

---

**Built with ⚡ for ultra-low latency trading on the BetBet platform**