#!/usr/bin/env python3
"""
Direct deployment script for betting tables
==========================================

Deploy betting tables directly using the Gaming Engine's database connection.
"""

import asyncio
import sys
from pathlib import Path

# Add the project path for imports
sys.path.append(str(Path(__file__).parent))

async def deploy_betting_tables():
    """Deploy betting tables directly"""
    try:
        print("🎲 Claude-DB: Deploying Betting Tables...")
        
        # Import after path is set
        from shared.core.database.connection import DatabaseSettings, initialize_database, get_db_manager
        
        # Initialize database connection (will use existing connection if available)
        try:
            await initialize_database()
            print("✅ Database connection established")
        except:
            print("⚠️  Using existing database connection")
        
        db_manager = get_db_manager()
        if not db_manager:
            print("❌ No database manager available")
            return False
        
        # Read the migration SQL
        migration_file = Path(__file__).parent / "app" / "database" / "migrations" / "001_add_betting_tables.sql"
        
        if not migration_file.exists():
            print(f"❌ Migration file not found: {migration_file}")
            return False
        
        migration_sql = migration_file.read_text()
        print(f"📄 Loaded migration: {migration_file.name} ({len(migration_sql)} chars)")
        
        # Execute the migration
        print("🚀 Executing betting tables migration...")
        
        async with db_manager.get_raw_connection() as conn:
            await conn.execute(migration_sql)
        
        print("✅ Migration executed successfully")
        
        # Verify tables were created
        print("🔍 Verifying table creation...")
        
        tables = await db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine' AND table_name LIKE 'betting_%' ORDER BY table_name",
            fetch_all=True
        )
        
        if tables:
            table_names = [dict(row)['table_name'] for row in tables]
            print(f"✅ Betting tables created: {table_names}")
            print(f"📊 Total tables: {len(table_names)}")
            
            # Verify specific expected tables
            expected_tables = ['betting_markets', 'betting_outcomes', 'user_bets', 'betting_market_odds_history']
            missing_tables = [t for t in expected_tables if t not in table_names]
            
            if missing_tables:
                print(f"⚠️  Missing expected tables: {missing_tables}")
            else:
                print("✅ All expected betting tables created successfully")
            
            return True
        else:
            print("❌ No betting tables found after migration")
            return False
            
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(deploy_betting_tables())
    if success:
        print("\n🎉 Claude-DB: Betting tables deployment SUCCESSFUL!")
        print("🔗 Betting APIs are now ready for use")
    else:
        print("\n💥 Claude-DB: Betting tables deployment FAILED!")
        sys.exit(1)