import os
import sys
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime, timedelta

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

# Add the current directory to the Python path to import shared modules
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database
from app.database.models import (
    Game, GameSession, SessionParticipant, Tournament, 
    BettingMarket, BettingOutcome, UserBet, SpectatorBet
)
from app.api.v1.games import router as games_router
from app.api.v1.sessions import router as sessions_router
from app.api.v1.tournaments import router as tournaments_router
from app.api.v1.chess import router as chess_router
from app.api.v1.chess_tournaments import router as chess_tournaments_router
from app.api.v1.analytics import router as analytics_router
from app.websocket.routes import register_websocket_routes
from app.services.chess_service import chess_service

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    # Database Configuration
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",   # Web frontend
        "http://localhost:3001",   # Admin dashboard
        "http://localhost:3002",   # Alternative port
        "http://localhost:3003",   # Current Next.js port
        "http://localhost:8000",   # API Gateway
        "https://betbet.com",      # Production web
        "https://admin.betbet.com" # Production admin
    ]
    
    SERVICE_NAME: str = "gaming-engine"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8001
    
    # Clerk Authentication Configuration
    clerk_publishable_key: str = os.getenv("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "")
    clerk_secret_key: str = os.getenv("CLERK_SECRET_KEY", "")
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"  # Allow extra environment variables

# Load settings
settings = Settings()

# Log Clerk configuration status
print(f"✅ Gaming Engine Settings Loaded:")
print(f"   - Clerk Publishable Key: {'Configured' if settings.clerk_publishable_key else 'Missing'}")
print(f"   - Clerk Secret Key: {'Configured' if settings.clerk_secret_key else 'Missing'}")
print(f"   - Database URL: {settings.DATABASE_URL[:50]}...")
print(f"   - Service Port: {settings.SERVICE_PORT}")

# Initialize FastAPI app
app = FastAPI(
    title="BetBet Gaming Engine",
    description="Core gaming functionality with database integration",
    version=settings.SERVICE_VERSION
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include routers
app.include_router(games_router, prefix="/api/v1/gaming", tags=["games"])
app.include_router(sessions_router, prefix="/api/v1/gaming", tags=["sessions"])
app.include_router(tournaments_router, prefix="/api/v1/gaming", tags=["tournaments"])
app.include_router(chess_router, prefix="/api/v1/gaming", tags=["chess"])
app.include_router(chess_tournaments_router, prefix="/api/v1/gaming", tags=["chess-tournaments"])
app.include_router(analytics_router, prefix="/api/v1/gaming", tags=["analytics"])

# Register WebSocket routes
try:
    register_websocket_routes(app)
    print("✅ WebSocket routes registered successfully")
except Exception as e:
    print(f"❌ Failed to register WebSocket routes: {e}")
    # Add a simple test WebSocket route directly
    @app.websocket("/ws/test")
    async def test_websocket(websocket):
        await websocket.accept()
        await websocket.send_text("Hello WebSocket!")
        await websocket.close()

    # Add chess WebSocket route directly
    @app.websocket("/ws/chess/{game_id}")
    async def chess_websocket(websocket, game_id: str):
        await websocket.accept()
        await websocket.send_json({
            "type": "connection_established",
            "game_id": game_id,
            "message": "Connected to chess game"
        })

        try:
            while True:
                data = await websocket.receive_text()
                # Echo back for now
                await websocket.send_json({
                    "type": "echo",
                    "data": data,
                    "game_id": game_id
                })
        except Exception:
            pass
        finally:
            await websocket.close()

    print("✅ Added fallback WebSocket routes")

@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    # Use DATABASE_URL from environment variables
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Gaming Engine connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup database connections on shutdown"""
    await close_database()

# Pydantic models for API responses
class GameResponse(BaseModel):
    id: str
    slug: str
    name: str
    category: str
    description: Optional[str]
    is_active: bool
    is_featured: bool
    min_players: int
    max_players: int
    estimated_duration_minutes: int
    scoring_system: str
    has_spectator_betting: bool

@app.get("/")
def read_root():
    return {
        "service": "BetBet Gaming Engine",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "games": "/api/v1/gaming/games",
            "sessions": "/api/v1/gaming/sessions",
            "chess": "/api/v1/chess",
            "chess_tournaments": "/api/v1/chess/tournaments",
            "chess_invitations": "/api/v1/chess/invitations",
            "analytics": "/api/v1/gaming/analytics/overview"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection by making a simple query
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False
        
        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

# Removed duplicate games endpoint - using database-integrated version below

# Helper functions to convert models to responses
def game_to_response(game) -> dict:
    return {
        "id": str(game.id),
        "slug": game.slug,
        "name": game.name,
        "category": game.category,
        "description": game.description,
        "is_active": game.is_active,
        "is_featured": game.is_featured if hasattr(game, 'is_featured') and game.is_featured is not None else False,
        "is_beta": game.is_beta if hasattr(game, 'is_beta') and game.is_beta is not None else False,
        "min_players": game.min_players,
        "max_players": game.max_players,
        "estimated_duration_minutes": game.estimated_duration_minutes,
        "scoring_system": game.scoring_system if hasattr(game, 'scoring_system') and game.scoring_system else "points",
        "has_spectator_betting": game.has_spectator_betting if hasattr(game, 'has_spectator_betting') and game.has_spectator_betting is not None else False,
        "allows_practice_mode": game.allows_practice_mode if hasattr(game, 'allows_practice_mode') and game.allows_practice_mode is not None else True,
        "popularity_score": str(game.popularity_score) if hasattr(game, 'popularity_score') and game.popularity_score is not None else "0.0",
        "total_sessions_played": game.total_sessions_played if hasattr(game, 'total_sessions_played') and game.total_sessions_played is not None else 0,
        "created_at": game.created_at.isoformat() if hasattr(game, 'created_at') and game.created_at else "2024-01-01T00:00:00Z",
        "updated_at": game.updated_at.isoformat() if hasattr(game, 'updated_at') and game.updated_at else "2024-01-01T00:00:00Z"
    }

# API endpoints with database integration and fallback
@app.get("/api/v1/gaming/games")
async def get_games(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    category: Optional[str] = None,
    is_featured: Optional[bool] = None,
    db: AsyncSession = Depends(get_database_write)
):
    """Get all games with pagination and filtering"""
    
    # Build query
    query = select(Game).where(Game.deleted_at.is_(None))
    
    if category:
        query = query.where(Game.category == category)
    if is_featured is not None:
        query = query.where(Game.is_featured == is_featured)
    
    query = query.order_by(Game.is_featured.desc(), Game.name)
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    games = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(Game.id)).where(Game.deleted_at.is_(None))
    if category:
        count_query = count_query.where(Game.category == category)
    if is_featured is not None:
        count_query = count_query.where(Game.is_featured == is_featured)
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    return {
        "games": [game_to_response(game) for game in games],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }

@app.get("/api/v1/gaming/games/featured")
async def get_featured_games(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_database_write)
):
    """Get featured games"""
    
    query = select(Game).where(
        Game.is_featured == True,
        Game.is_active == True,
        Game.deleted_at.is_(None)
    ).order_by(Game.popularity_score.desc()).limit(limit)
    
    result = await db.execute(query)
    games = result.scalars().all()
    
    return [game_to_response(game) for game in games]

@app.get("/api/v1/gaming/sessions")
async def get_sessions(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = None,
    game_id: Optional[str] = None,
    db: AsyncSession = Depends(get_database_write)
):
    """Get game sessions with pagination and filtering"""
    
    query = select(GameSession).options(
        selectinload(GameSession.game)
    ).where(GameSession.deleted_at.is_(None))
    
    if status:
        query = query.where(GameSession.state == status)
    if game_id:
        query = query.where(GameSession.game_id == UUID(game_id))
    
    query = query.order_by(GameSession.scheduled_start_at.desc())
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    sessions = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(GameSession.id)).where(GameSession.deleted_at.is_(None))
    if status:
        count_query = count_query.where(GameSession.state == status)
    if game_id:
        count_query = count_query.where(GameSession.game_id == UUID(game_id))
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    # Convert to response format
    sessions_response = []
    for session in sessions:
        sessions_response.append({
            "id": str(session.id),
            "slug": session.slug,
            "session_name": session.session_name,
            "game_id": str(session.game_id),
            "status": session.state,
            "entry_fee": float(session.entry_fee),
            "min_participants": session.min_participants_to_start,
            "max_participants": session.max_participants,
            "current_participants": session.current_participants,
            "allow_spectators": session.allows_spectators,
            "scheduled_start_time": session.scheduled_start_at.isoformat() if session.scheduled_start_at else None,
            "estimated_duration_minutes": session.game.estimated_duration_minutes if session.game else None
        })
    
    return {
        "sessions": sessions_response,
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }

# REMOVED: Duplicate session creation endpoint
# The proper endpoint is in app/api/v1/sessions.py with authentication and proper slug generation

# Analytics endpoint is now handled by the analytics router at /api/v1/gaming/analytics/overview


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)