# BetBet Platform - Gaming Engine

## Overview

The Gaming Engine is the core service powering interactive gaming sessions on the BetBet platform. It manages game logic, player sessions, tournaments, and spectator betting functionality. Built for high performance and real-time interaction, it supports multiple game types with robust session management.

## Features

- **Multi-Game Support**: Extensible framework supporting various game types
- **Real-time Sessions**: Live gaming sessions with WebSocket communication
- **Tournament Management**: Create and manage competitive tournaments
- **Spectator Betting**: Allow viewers to bet on live gaming sessions
- **Session Analytics**: Comprehensive gaming statistics and performance tracking
- **Player Management**: User profiles, rankings, and achievement systems
- **Anti-Cheat Protection**: Built-in safeguards and fair play monitoring

## Technology Stack

- **Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL with async support
- **Cache**: Redis for session state and real-time data
- **Authentication**: Clerk integration with JWT fallback
- **Real-time**: WebSocket for live game communication
- **Containerization**: Docker with optimized game engine dependencies

## Game Types Supported

### Current Games
- **Classic Games**: Chess, Checkers, Tic-tac-toe
- **Strategy Games**: Custom board games and puzzles
- **Skill Games**: Reaction-based and skill-testing games
- **Trivia Games**: Knowledge-based competitions

### Planned Expansions
- **Card Games**: Poker, Blackjack variations
- **Sports Simulations**: Virtual sports betting
- **E-sports Integration**: Live tournament integration

## API Endpoints

### Game Management
- `GET /api/v1/games` - List available games
- `GET /api/v1/games/{game_id}` - Get game details and rules
- `POST /api/v1/games/{game_id}/validate` - Validate game moves

### Session Operations
- `POST /api/v1/sessions/create` - Create new gaming session
- `GET /api/v1/sessions/{session_id}` - Get session details
- `POST /api/v1/sessions/{session_id}/join` - Join existing session
- `POST /api/v1/sessions/{session_id}/move` - Submit game move
- `GET /api/v1/sessions/active` - List active sessions

### Tournament Management
- `GET /api/v1/tournaments` - List tournaments
- `POST /api/v1/tournaments` - Create tournament (admin)
- `POST /api/v1/tournaments/{tournament_id}/register` - Register for tournament
- `GET /api/v1/tournaments/{tournament_id}/bracket` - Get tournament bracket

### Spectator Betting
- `GET /api/v1/spectator/sessions` - List sessions available for betting
- `POST /api/v1/spectator/bet` - Place spectator bet
- `GET /api/v1/spectator/bets/{user_id}` - Get user's spectator bets

### Analytics
- `GET /api/v1/analytics/session/{session_id}` - Session performance data
- `GET /api/v1/analytics/player/{player_id}` - Player statistics
- `GET /api/v1/analytics/games/popular` - Most popular games

## Environment Variables

```bash
# Database Configuration
DATABASE_URL=****************************************/betbet_gaming
REDIS_URL=redis://:password@redis:6379

# Authentication
JWT_SECRET=your-jwt-secret-key
CLERK_SECRET_KEY=your-clerk-secret-key

# External Services
API_GATEWAY_URL=http://api-gateway:8000
WEBSOCKET_URL=ws://websocket-manager:8080

# Gaming Configuration
MAX_PARTICIPANTS_PER_SESSION=10
GAME_SESSION_TIMEOUT_MINUTES=30
SPECTATOR_BETTING_ENABLED=true

# Application Settings
LOG_LEVEL=INFO
```

## Database Schema

The service uses the `betbet_gaming` database with tables for:
- `games` - Game definitions and rules
- `game_sessions` - Active and completed gaming sessions
- `session_participants` - Player participation in sessions
- `game_moves` - Individual moves and game state
- `tournaments` - Tournament configurations and brackets
- `spectator_bets` - Bets placed on gaming sessions
- `player_stats` - Player performance and rankings

## Game Engine Architecture

### Session Management
```python
class GameSession:
    - session_id: UUID
    - game_type: GameType
    - participants: List[Player]
    - current_state: GameState
    - move_history: List[Move]
    - spectators: List[Spectator]
```

### Move Validation
- **Rule Engine**: Validates moves against game rules
- **State Management**: Maintains consistent game state
- **Conflict Resolution**: Handles simultaneous moves
- **Cheat Detection**: Monitors for suspicious patterns

### Real-time Communication
- **Move Broadcasting**: Instant move updates to all participants
- **Spectator Updates**: Live game state for viewers
- **Session Events**: Join/leave notifications
- **Betting Updates**: Real-time odds and betting opportunities

## Development

### Local Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize game rules:**
   ```bash
   python scripts/init_games.py
   ```

4. **Run the service:**
   ```bash
   python app/main.py
   ```

### Docker Development

```bash
# Build and run with Docker Compose
docker-compose up gaming-engine

# View logs
docker-compose logs -f gaming-engine
```

## Game Development

### Adding New Games

1. **Define Game Rules:**
   ```python
   class NewGameRules(BaseGameRules):
       def validate_move(self, move: Move, state: GameState) -> bool:
           # Implementation
           pass
   ```

2. **Register Game Type:**
   ```python
   GAME_REGISTRY.register("new_game", NewGameRules)
   ```

3. **Add Database Schema:**
   ```sql
   INSERT INTO games (game_type, rules_config, max_players)
   VALUES ('new_game', '{"rule": "value"}', 2);
   ```

### Game State Management

The engine maintains game state using a hybrid approach:
- **In-Memory**: Active session states in Redis
- **Persistent**: Move history and final states in PostgreSQL
- **Replication**: Session state replicated across instances

## Health Checks

The service exposes a health check endpoint at `/health` that returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "database": "connected",
  "redis": "connected",
  "active_sessions": 15,
  "game_types_loaded": 8
}
```

## Performance Optimization

### Session Scaling
- **Session Partitioning**: Distribute sessions across instances
- **Redis Clustering**: Scale session storage horizontally
- **Connection Pooling**: Optimize database connections
- **Caching Strategy**: Cache frequently accessed game rules

### Real-time Performance
- **WebSocket Optimization**: Efficient message broadcasting
- **State Compression**: Minimize data transfer
- **Batch Updates**: Group related updates
- **Lazy Loading**: Load game data on demand

## Security Features

- **Input Validation**: All moves validated before processing
- **Session Isolation**: Prevent cross-session data leaks
- **Anti-Cheat System**: Pattern detection for suspicious behavior
- **Rate Limiting**: Prevent rapid-fire move spam
- **Audit Trail**: Complete game history for dispute resolution

## Monitoring and Analytics

### Game Metrics
- **Session Duration**: Average game completion times
- **Player Engagement**: Active player counts and retention
- **Game Popularity**: Most played game types
- **Error Rates**: Move validation failures and system errors

### Performance Metrics
- **Response Times**: Move processing and validation speeds
- **Throughput**: Sessions and moves processed per second
- **Resource Usage**: CPU, memory, and Redis utilization
- **WebSocket Health**: Connection stability and message delivery

## Integration

### WebSocket Events

The service publishes real-time game events:
- `session_created` - New gaming session started
- `player_joined` - Player joined session
- `move_made` - Game move submitted
- `session_ended` - Game completed
- `spectator_bet_placed` - Bet on gaming session

### Spectator Betting Integration

- **Odds Calculation**: Dynamic odds based on game state
- **Live Betting**: Accept bets during active sessions
- **Settlement**: Automatic bet settlement on game completion
- **Market Creation**: Generate betting markets for popular sessions

## Testing

```bash
# Run unit tests
pytest tests/unit/

# Run game logic tests
pytest tests/games/

# Run integration tests
pytest tests/integration/

# Test WebSocket functionality
pytest tests/websocket/

# Load testing
python scripts/load_test_sessions.py
```

## Deployment

### Production Deployment

```bash
# Build production image
docker build -f Dockerfile.prod -t betbet/gaming-engine:latest .

# Deploy with scaling
docker-compose -f docker-compose.prod.yml up -d gaming-engine
```

### Scaling Configuration

```yaml
# Production scaling
gaming-engine:
  deploy:
    replicas: 3
    resources:
      limits:
        memory: 1G
        cpus: '0.5'
```

## Troubleshooting

### Common Issues

**Session state inconsistency:**
- Check Redis connectivity and cluster health
- Verify session state synchronization
- Review move validation logic

**High response times:**
- Monitor database query performance
- Check Redis cache hit rates
- Review WebSocket connection pools

**Game rule violations:**
- Verify game rule implementations
- Check move validation logs
- Review anti-cheat detection accuracy

### Debug Mode

Enable detailed game logging:
```bash
export LOG_LEVEL=DEBUG
export GAME_DEBUG=true
python app/main.py
```

## Tournament Features

### Tournament Types
- **Single Elimination**: Classic bracket tournament
- **Round Robin**: All players play each other
- **Swiss System**: Skill-based pairing system
- **Custom Formats**: Configurable tournament rules

### Prize Distribution
- **Entry Fees**: Player-funded prize pools
- **House Contribution**: Platform-sponsored prizes
- **Spectator Revenue**: Share of betting revenue
- **Achievement Rewards**: Performance-based bonuses

## Contributing

1. Follow gaming industry best practices
2. Add comprehensive tests for new games
3. Document game rules and mechanics
4. Ensure fair play and security
5. Test performance under load

## License

MIT License - see LICENSE file for details.

---

**Part of the BetBet Platform** - Where skill meets opportunity in competitive gaming.