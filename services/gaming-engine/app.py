from fastapi import FastAPI
import os

app = FastAPI(title="BetBet Gaming Engine")

@app.get("/")
def root():
    return {"message": "BetBet Gaming Engine is live on Railway!", "status": "healthy"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "gaming-engine"}

@app.get("/api/v1/gaming/games")
def get_games():
    """Get all available games"""
    # Return chess game as the main game for now
    return {
        "games": [
            {
                "id": "chess-uuid-1234",
                "slug": "chess", 
                "name": "Chess",
                "category": "strategy",
                "description": "The classic strategy game with multiple variants and time controls",
                "is_active": True,
                "is_featured": True,
                "min_players": 2,
                "max_players": 2,
                "estimated_duration_minutes": 30,
                "scoring_system": "win/loss",
                "has_spectator_betting": True,
                "allows_practice_mode": False,
                "is_beta": False,
                "total_sessions_played": 150,
                "popularity_score": 95.5,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        ],
        "total_count": 1,
        "page": 1,
        "limit": 50,
        "has_next": False,
        "has_prev": False
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8001))
    uvicorn.run(app, host="0.0.0.0", port=port)