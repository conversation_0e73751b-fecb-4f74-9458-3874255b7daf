"""
BetBet Gaming Engine - Clerk Authentication Integration
=====================================================

Clerk authentication integration for secure user management.
"""

import json
import httpx
import os
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status, Header
import structlog

logger = structlog.get_logger()


class ClerkAuthManager:
    """Clerk authentication manager for token verification"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.base_url = "https://api.clerk.com/v1"
        
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify Clerk JWT token and return user data
        
        Args:
            token: Clerk JWT token from Authorization header
            
        Returns:
            Dict containing user information
            
        Raises:
            HTTPException: If token is invalid or verification fails
        """
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No authentication token provided"
            )
            
        # Remove 'Bearer ' prefix if present
        if token.startswith('Bearer '):
            token = token[7:]
            
        try:
            # For Clerk JWT tokens, we need to decode and verify using proper JWT verification
            # Clerk JWTs contain user information directly in the payload
            from jose import jwt, JWTError
            import requests
            
            # Get Clerk's JWKS (JSON Web Key Set) to verify the token
            # First, extract the key ID from the token header
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')
            
            if not kid:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing key ID"
                )
            
            # Get Clerk's public keys
            jwks_url = f"https://clerk.{self._get_clerk_domain()}.com/v1/jwks"
            try:
                jwks_response = requests.get(jwks_url, timeout=10)
                jwks_response.raise_for_status()
                jwks = jwks_response.json()
            except Exception as e:
                logger.error("Failed to fetch Clerk JWKS", error=str(e))
                # Fallback to session verification API
                return await self._verify_token_via_api(token)
            
            # Find the matching key
            key = None
            for jwk in jwks.get('keys', []):
                if jwk.get('kid') == kid:
                    key = jwk
                    break
            
            if not key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: key not found"
                )
            
            # Convert JWK to PEM format for verification
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.asymmetric import rsa
            import base64
            
            # Extract RSA components
            n = base64.urlsafe_b64decode(key['n'] + '==')
            e = base64.urlsafe_b64decode(key['e'] + '==')
            
            # Convert to integers
            n_int = int.from_bytes(n, 'big')
            e_int = int.from_bytes(e, 'big')
            
            # Create RSA public key
            public_key = rsa.RSAPublicNumbers(e_int, n_int).public_key()
            pem_key = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            # Verify and decode the JWT
            payload = jwt.decode(
                token,
                pem_key,
                algorithms=['RS256'],
                audience=None,  # Clerk doesn't always use audience
                options={"verify_aud": False}
            )
            
            # Extract user information from the payload
            user_id = payload.get('sub')
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: no user ID found"
                )
            
            # Return user data from JWT payload
            user_data = {
                "user_id": user_id,
                "email": payload.get('email', ''),
                "username": payload.get('username', '') or payload.get('email', '').split('@')[0],
                "first_name": payload.get('given_name', ''),
                "last_name": payload.get('family_name', ''),
                "image_url": payload.get('picture', ''),
                "created_at": payload.get('iat'),
                "last_sign_in_at": payload.get('iat'),
                "roles": self._extract_roles_from_payload(payload),
                "metadata": payload.get('public_metadata', {})
            }
            
            logger.info("Successfully verified Clerk JWT token", user_id=user_id)
            return user_data
            
        except JWTError as e:
            logger.error("JWT verification failed", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        except Exception as e:
            logger.error("Unexpected error during JWT verification", error=str(e))
            # Fallback to API verification
            return await self._verify_token_via_api(token)
    
    async def _verify_token_via_api(self, token: str) -> Dict[str, Any]:
        """Fallback method to verify token via Clerk API"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.secret_key}",
                    "Content-Type": "application/json"
                }
                
                response = await client.post(
                    f"{self.base_url}/sessions/verify",
                    headers=headers,
                    json={"token": token}
                )
                
                if response.status_code == 200:
                    session_data = response.json()
                    user_id = session_data.get("user_id")
                    if not user_id:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Invalid token: no user ID found"
                        )
                    
                    user_data = await self._get_user_details(user_id, headers)
                    return user_data
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Token verification failed"
                    )
        except Exception as e:
            logger.error("API token verification failed", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )
    
    def _get_clerk_domain(self) -> str:
        """Extract domain from Clerk secret key"""
        # Clerk secret keys contain domain info, but for simplicity use the default
        return "accounts.dev"  # Default for test keys
    
    def _extract_roles_from_payload(self, payload: Dict[str, Any]) -> List[str]:
        """Extract user roles from JWT payload"""
        metadata = payload.get('public_metadata', {})
        roles = metadata.get('roles', [])
        
        if not roles:
            roles = ['user']
        
        return roles
    
    async def _get_user_details(self, user_id: str, headers: Dict[str, str]) -> Dict[str, Any]:
        """Get detailed user information from Clerk"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/users/{user_id}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    user_data = response.json()
                    
                    # Extract relevant user information
                    return {
                        "user_id": user_data.get("id"),
                        "email": user_data.get("email_addresses", [{}])[0].get("email_address", ""),
                        "username": user_data.get("username", ""),
                        "first_name": user_data.get("first_name", ""),
                        "last_name": user_data.get("last_name", ""),
                        "image_url": user_data.get("image_url", ""),
                        "created_at": user_data.get("created_at"),
                        "last_sign_in_at": user_data.get("last_sign_in_at"),
                        "roles": self._extract_user_roles(user_data),
                        "metadata": user_data.get("public_metadata", {})
                    }
                else:
                    logger.warning(
                        "Failed to get user details from Clerk",
                        user_id=user_id,
                        status_code=response.status_code
                    )
                    # Return minimal user data if details fetch fails
                    return {
                        "user_id": user_id,
                        "email": "",
                        "username": "",
                        "first_name": "",
                        "last_name": "",
                        "roles": ["user"],
                        "metadata": {}
                    }
                    
        except Exception as e:
            logger.error("Error fetching user details", user_id=user_id, error=str(e))
            # Return minimal user data on error
            return {
                "user_id": user_id,
                "email": "",
                "username": "",
                "roles": ["user"],
                "metadata": {}
            }
    
    def _extract_user_roles(self, user_data: Dict[str, Any]) -> list:
        """Extract user roles from Clerk user data"""
        # Check public metadata for roles
        metadata = user_data.get("public_metadata", {})
        roles = metadata.get("roles", [])
        
        # Default role
        if not roles:
            roles = ["user"]
            
        # Add admin role if user has admin privileges
        if metadata.get("is_admin", False):
            roles.append("admin")
            
        # Add moderator role if applicable
        if metadata.get("is_moderator", False):
            roles.append("moderator")
            
        return roles


class ClerkTokenData:
    """Token data structure compatible with existing codebase"""
    
    def __init__(self, user_data: Dict[str, Any]):
        self.user_id = user_data.get("user_id", "")
        self.email = user_data.get("email", "")
        self.username = user_data.get("username", "") or self.email.split("@")[0]
        self.first_name = user_data.get("first_name", "")
        self.last_name = user_data.get("last_name", "")
        self.image_url = user_data.get("image_url", "")
        self.roles = user_data.get("roles", ["user"])
        self.metadata = user_data.get("metadata", {})
        self.created_at = user_data.get("created_at")
        self.last_sign_in_at = user_data.get("last_sign_in_at")
    
    @property
    def display_name(self) -> str:
        """Get user's display name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return self.username
        else:
            return self.email.split("@")[0] if self.email else self.user_id[:8]


async def get_clerk_auth_manager(settings) -> ClerkAuthManager:
    """Get configured Clerk authentication manager"""
    if not settings.clerk_secret_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Clerk authentication not configured"
        )

    return ClerkAuthManager(settings.clerk_secret_key)


async def get_current_user(authorization: str = Header(None)) -> ClerkTokenData:
    """FastAPI dependency to get current authenticated user"""
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header required"
        )

    # Extract token from Bearer header
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format"
        )

    token = authorization.split(" ")[1]

    # For now, create a simple auth manager instance
    # In production, this should be properly configured
    auth_manager = ClerkAuthManager(
        secret_key=os.getenv("CLERK_SECRET_KEY", "")
    )

    try:
        user = await auth_manager.verify_token(token)
        return user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}"
        )