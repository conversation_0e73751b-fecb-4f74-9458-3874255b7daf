"""
BetBet Gaming Engine - SQLAlchemy Database Models
================================================

SQLAlchemy models for the gaming engine database tables.
These models correspond to the tables defined in database/schemas/gaming_engine.sql
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB, INET
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.sql import func

# Use the shared base that works with the existing database connection
Base = declarative_base()


class User(Base):
    """User model (public.users table)"""
    __tablename__ = "users"
    __table_args__ = {"schema": "public"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    avatar_url = Column(Text)
    balance = Column(Numeric(12, 2), default=0.00)
    
    # Status and permissions
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True))
    updated_by = Column(PostgresUUID(as_uuid=True))
    version = Column(Integer, default=1)
    
    # Soft delete
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(PostgresUUID(as_uuid=True))


class Game(Base):
    """Game model (gaming_engine.games table)"""
    __tablename__ = "games"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Game identification
    name = Column(String(200), nullable=False)
    slug = Column(String(100), unique=True, nullable=False)
    category = Column(String(50), nullable=False)
    description = Column(Text)
    cover_image = Column(Text)  # URL to game cover image for UI enhancement
    
    # Game configuration and rules
    game_config = Column(JSONB, default={})
    rules_text = Column(Text)
    instructions = Column(Text)
    
    # Player limits and requirements
    min_players = Column(Integer, nullable=False, default=2)
    max_players = Column(Integer, nullable=False, default=10)
    min_skill_level = Column(Integer, default=1)
    estimated_duration_minutes = Column(Integer, default=10)
    
    # Game mechanics
    scoring_system = Column(String(20), default="points")
    has_spectator_betting = Column(Boolean, default=True)
    allows_practice_mode = Column(Boolean, default=True)
    
    # Status and visibility
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    is_beta = Column(Boolean, default=False)
    
    # Plugin system support
    plugin_name = Column(String(100))
    plugin_version = Column(String(20))
    plugin_config = Column(JSONB, default={})
    
    # Analytics and performance
    total_sessions_played = Column(Integer, default=0)
    average_session_duration = Column(Numeric(8, 2))
    average_players_per_session = Column(Numeric(4, 2))
    popularity_score = Column(Numeric(6, 2), default=0)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Soft delete
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Relationships
    sessions = relationship("GameSession", back_populates="game")
    tournaments = relationship("Tournament", back_populates="game")


class GameSession(Base):
    """Game session model (gaming_engine.game_sessions table)"""
    __tablename__ = "game_sessions"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Game reference
    game_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.games.id"), nullable=False)
    
    # Session identification
    session_name = Column(String(200))
    slug = Column(String(300), unique=True)  # Descriptive slug for sharing/archiving
    session_code = Column(String(20), unique=True)
    
    # Session type and configuration
    session_type = Column(String(20), nullable=False, default="casual")
    game_mode = Column(String(20), default="standard")
    
    # Session state management
    state = Column(String(20), nullable=False, default="waiting")
    
    # Player and capacity management
    max_participants = Column(Integer, nullable=False)
    current_participants = Column(Integer, nullable=False, default=0)
    min_participants_to_start = Column(Integer, default=2)
    
    # Financial configuration
    entry_fee = Column(Numeric(10, 2), nullable=False, default=0)
    prize_pool = Column(Numeric(10, 2), nullable=False, default=0)
    platform_fee_percentage = Column(Numeric(5, 4), default=0.05)
    
    # Timing and scheduling
    scheduled_start_at = Column(DateTime(timezone=True))
    actual_started_at = Column(DateTime(timezone=True))
    estimated_end_at = Column(DateTime(timezone=True))
    actual_ended_at = Column(DateTime(timezone=True))
    auto_start_when_full = Column(Boolean, default=True)
    
    # Session configuration
    session_config = Column(JSONB, default={})
    custom_rules = Column(JSONB, default={})
    
    # Real-time features
    allows_spectators = Column(Boolean, default=True)
    allows_spectator_betting = Column(Boolean, default=True)
    allows_late_joining = Column(Boolean, default=False)
    
    # Privacy and access
    is_private = Column(Boolean, default=False)
    password_hash = Column(String(255))
    invite_only = Column(Boolean, default=False)
    
    # Results and analytics
    winner_id = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    final_results = Column(JSONB)
    session_statistics = Column(JSONB)
    
    # Host information
    host_id = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Tournament reference (if applicable)
    tournament_id = Column(PostgresUUID(as_uuid=True))
    tournament_round = Column(Integer)
    tournament_match_number = Column(Integer)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(String(255))  # Support Clerk user IDs
    updated_by = Column(String(255))  # Support Clerk user IDs
    version = Column(Integer, default=1)
    
    # Soft delete
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(String(255))  # Support Clerk user IDs
    
    # Relationships
    game = relationship("Game", back_populates="sessions")
    participants = relationship("SessionParticipant", back_populates="session")
    spectator_bets = relationship("SpectatorBet", back_populates="session")
    game_states = relationship("GameState", back_populates="session")
    betting_markets = relationship("BettingMarket", back_populates="session")


class SessionParticipant(Base):
    """Session participant model (gaming_engine.session_participants table)"""
    __tablename__ = "session_participants"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    session_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.game_sessions.id"), nullable=False)
    user_id = Column(String(255), nullable=False)  # Support Clerk user IDs
    
    # Participation details
    stake_amount = Column(Numeric(10, 2), nullable=False)
    join_order = Column(Integer, nullable=False)
    
    # Performance tracking
    current_score = Column(Numeric(10, 4), default=0)
    final_score = Column(Numeric(10, 4))
    final_position = Column(Integer)
    performance_data = Column(JSONB, default={})
    
    # Timing
    joined_at = Column(DateTime(timezone=True), default=func.now())
    left_at = Column(DateTime(timezone=True))
    total_play_time = Column(Integer)  # in seconds
    
    # Results and payouts
    payout_amount = Column(Numeric(10, 2), default=0)
    bonus_amount = Column(Numeric(10, 2), default=0)
    payout_processed = Column(Boolean, default=False)
    payout_processed_at = Column(DateTime(timezone=True))
    
    # Player state
    is_active = Column(Boolean, default=True)
    is_ready = Column(Boolean, default=False)
    connection_status = Column(String(20), default="connected")
    last_activity_at = Column(DateTime(timezone=True), default=func.now())
    
    # Anti-cheat and validation
    validation_status = Column(String(20), default="pending")
    validation_data = Column(JSONB, default={})
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Relationships
    session = relationship("GameSession", back_populates="participants")


class GameState(Base):
    """Game state model (gaming_engine.game_states table)"""
    __tablename__ = "game_states"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    session_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.game_sessions.id"), nullable=False)
    
    # State information
    state_type = Column(String(30), nullable=False)
    state_data = Column(JSONB, nullable=False)
    
    # Sequence and ordering
    sequence_number = Column(Integer, nullable=False)
    round_number = Column(Integer)
    
    # Player context
    affected_player_id = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Validation and integrity
    state_hash = Column(String(64))
    previous_state_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.game_states.id"))
    
    # Performance tracking
    processing_time_ms = Column(Numeric(8, 3))
    
    # Audit fields (simplified for performance)
    created_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Relationships
    session = relationship("GameSession", back_populates="game_states")


class SpectatorBet(Base):
    """Spectator bet model (gaming_engine.spectator_bets table)"""
    __tablename__ = "spectator_bets"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    session_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.game_sessions.id"), nullable=False)
    user_id = Column(String(255), nullable=False)  # Support Clerk user IDs
    
    # Bet details
    bet_type = Column(String(50), nullable=False)
    bet_data = Column(JSONB, nullable=False)
    
    # Financial details
    stake_amount = Column(Numeric(10, 2), nullable=False)
    potential_payout = Column(Numeric(10, 2), nullable=False)
    actual_payout = Column(Numeric(10, 2), default=0)
    
    # Odds and market data
    odds_at_placement = Column(Numeric(8, 4))
    market_data = Column(JSONB)
    
    # Bet lifecycle
    status = Column(String(20), default="active")
    placed_at = Column(DateTime(timezone=True), default=func.now())
    settled_at = Column(DateTime(timezone=True))
    settlement_data = Column(JSONB)
    
    # Market making and liquidity
    is_market_maker_bet = Column(Boolean, default=False)
    matched_bet_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.spectator_bets.id"))
    
    # Risk management
    risk_category = Column(String(20), default="standard")
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Soft delete
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Relationships
    session = relationship("GameSession", back_populates="spectator_bets")


class Tournament(Base):
    """Tournament model (gaming_engine.tournaments table)"""
    __tablename__ = "tournaments"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Tournament identification
    name = Column(String(200), nullable=False)
    slug = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    
    # Tournament configuration
    game_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.games.id"), nullable=False)
    tournament_type = Column(String(30), nullable=False)
    
    # Entry and participation
    entry_fee = Column(Numeric(10, 2), nullable=False)
    max_participants = Column(Integer, nullable=False)
    current_participants = Column(Integer, default=0)
    min_participants = Column(Integer, default=4)
    
    # Prize structure
    prize_pool = Column(Numeric(10, 2), nullable=False, default=0)
    prize_distribution = Column(JSONB)
    guaranteed_prize_pool = Column(Numeric(10, 2), default=0)
    
    # Scheduling
    registration_opens_at = Column(DateTime(timezone=True))
    registration_closes_at = Column(DateTime(timezone=True))
    starts_at = Column(DateTime(timezone=True), nullable=False)
    estimated_ends_at = Column(DateTime(timezone=True))
    actual_ended_at = Column(DateTime(timezone=True))
    
    # Tournament state
    status = Column(String(20), default="scheduled")
    current_round = Column(Integer, default=0)
    total_rounds = Column(Integer)
    
    # Tournament settings
    tournament_config = Column(JSONB, default={})
    bracket_data = Column(JSONB)
    
    # Visibility and access
    is_public = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    skill_level_restriction = Column(String(20))
    
    # Organizer information
    organizer_id = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    sponsor_data = Column(JSONB)
    
    # Results
    champion_id = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    final_standings = Column(JSONB)
    tournament_statistics = Column(JSONB)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Soft delete
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Relationships
    game = relationship("Game", back_populates="tournaments")
    participants = relationship("TournamentParticipant", back_populates="tournament")
    betting_markets = relationship("BettingMarket", back_populates="tournament")


class TournamentParticipant(Base):
    """Tournament participant model (gaming_engine.tournament_participants table)"""
    __tablename__ = "tournament_participants"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    tournament_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.tournaments.id"), nullable=False)
    user_id = Column(String(255), nullable=False)  # Support Clerk user IDs
    
    # Participation details
    entry_fee_paid = Column(Numeric(10, 2), nullable=False)
    seed_number = Column(Integer)
    bracket_position = Column(Integer)
    
    # Performance tracking
    current_round = Column(Integer, default=0)
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    total_score = Column(Numeric(10, 4), default=0)
    
    # Results
    final_position = Column(Integer)
    prize_amount = Column(Numeric(10, 2), default=0)
    prize_paid = Column(Boolean, default=False)
    
    # Status
    status = Column(String(20), default="active")
    eliminated_in_round = Column(Integer)
    
    # Timestamps
    registered_at = Column(DateTime(timezone=True), default=func.now())
    eliminated_at = Column(DateTime(timezone=True))
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Relationships
    tournament = relationship("Tournament", back_populates="participants")


class BettingMarket(Base):
    """Betting market model (gaming_engine.betting_markets table)"""
    __tablename__ = "betting_markets"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    session_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.game_sessions.id"))
    tournament_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.tournaments.id"))
    
    # Market details
    name = Column(String(200), nullable=False)
    description = Column(Text)
    market_type = Column(String(50), nullable=False)  # match_winner, tournament_winner, player_performance, special_events
    
    # Market status and configuration
    status = Column(String(20), default="active")  # active, suspended, closed, settled
    total_pool = Column(Numeric(15, 2), default=0)
    total_bets = Column(Integer, default=0)
    
    # Timing
    opens_at = Column(DateTime(timezone=True), default=func.now())
    closes_at = Column(DateTime(timezone=True))
    settlement_time = Column(DateTime(timezone=True))
    
    # Market configuration
    market_config = Column(JSONB, default={})
    settlement_criteria = Column(JSONB)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Relationships
    session = relationship("GameSession", back_populates="betting_markets")
    tournament = relationship("Tournament", back_populates="betting_markets")
    outcomes = relationship("BettingOutcome", back_populates="market", cascade="all, delete-orphan")
    bets = relationship("UserBet", back_populates="market")


class BettingOutcome(Base):
    """Betting outcome model (gaming_engine.betting_outcomes table)"""
    __tablename__ = "betting_outcomes"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.betting_markets.id"), nullable=False)
    
    # Outcome details
    name = Column(String(200), nullable=False)
    description = Column(Text)
    outcome_type = Column(String(50))  # player, team, event, etc.
    
    # Odds and probability
    odds = Column(Numeric(8, 3), nullable=False)  # American odds format
    implied_probability = Column(Numeric(5, 2))  # Percentage
    
    # Betting statistics
    total_bet_amount = Column(Numeric(15, 2), default=0)
    bet_count = Column(Integer, default=0)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_winning_outcome = Column(Boolean)
    
    # External references (for player/team outcomes)
    external_id = Column(String(100))  # Can reference user_id, team_id, etc.
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Relationships
    market = relationship("BettingMarket", back_populates="outcomes")
    bets = relationship("UserBet", back_populates="outcome")
    odds_history = relationship("BettingMarketOddsHistory", back_populates="outcome")


class UserBet(Base):
    """User bet model (gaming_engine.user_bets table)"""
    __tablename__ = "user_bets"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    user_id = Column(String(255), nullable=False)  # Support Clerk user IDs
    market_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.betting_markets.id"), nullable=False)
    outcome_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.betting_outcomes.id"), nullable=False)
    
    # Bet details
    bet_amount = Column(Numeric(15, 2), nullable=False)
    odds = Column(Numeric(8, 3), nullable=False)  # Odds at time of bet placement
    potential_return = Column(Numeric(15, 2), nullable=False)
    actual_return = Column(Numeric(15, 2))
    
    # Status and settlement
    status = Column(String(20), default="pending")  # pending, won, lost, void, cancelled
    placed_at = Column(DateTime(timezone=True), default=func.now())
    settled_at = Column(DateTime(timezone=True))
    
    # Risk management
    ip_address = Column(INET)
    user_agent = Column(Text)
    
    # Settlement details
    settlement_reason = Column(Text)
    settlement_data = Column(JSONB)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    updated_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    version = Column(Integer, default=1)
    
    # Relationships
    market = relationship("BettingMarket", back_populates="bets")
    outcome = relationship("BettingOutcome", back_populates="bets")


class BettingMarketOddsHistory(Base):
    """Betting market odds history model (gaming_engine.betting_market_odds_history table)"""
    __tablename__ = "betting_market_odds_history"
    __table_args__ = {"schema": "gaming_engine"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # References
    outcome_id = Column(PostgresUUID(as_uuid=True), ForeignKey("gaming_engine.betting_outcomes.id"), nullable=False)
    
    # Odds change tracking
    old_odds = Column(Numeric(8, 3))
    new_odds = Column(Numeric(8, 3), nullable=False)
    old_implied_probability = Column(Numeric(5, 2))
    new_implied_probability = Column(Numeric(5, 2))
    
    # Change metadata
    change_reason = Column(String(100))  # bet_placement, manual_adjustment, system_rebalance
    change_amount = Column(Numeric(15, 2))  # bet amount that triggered change
    
    # Timestamps
    timestamp = Column(DateTime(timezone=True), default=func.now())
    created_by = Column(PostgresUUID(as_uuid=True), ForeignKey("public.users.id"))
    
    # Relationships
    outcome = relationship("BettingOutcome", back_populates="odds_history")