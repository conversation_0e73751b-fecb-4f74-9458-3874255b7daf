"""
BetBet Gaming Engine - Slug Generation Utilities
===============================================

Utilities for generating descriptive, shareable slugs for games and sessions.
"""

import re
from datetime import datetime
from typing import List, Optional


def slugify(text: str) -> str:
    """Convert text to URL-friendly slug format"""
    # Convert to lowercase and replace spaces/special chars with hyphens
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', text.lower())
    slug = re.sub(r'[\s_-]+', '-', slug)
    return slug.strip('-')


def generate_game_slug(name: str, category: Optional[str] = None) -> str:
    """
    Generate a slug for a game
    
    Examples:
    - "Trivia Challenge" -> "trivia-challenge"
    - "Chess Blitz" -> "chess-blitz"
    - "Reaction Time Master" -> "reaction-time-master"
    """
    base_slug = slugify(name)
    
    # For some games, prepend category for clarity
    if category and category not in base_slug:
        category_slug = slugify(category)
        # Only prepend if it adds meaningful context
        if len(category_slug) <= 8 and category_slug not in base_slug:
            base_slug = f"{category_slug}-{base_slug}"
    
    return base_slug


def generate_session_slug(
    game_slug: str,
    participants: List[str],
    session_type: str = "casual",
    start_time: Optional[datetime] = None,
    session_name: Optional[str] = None
) -> str:
    """
    Generate a descriptive slug for a game session
    
    Format patterns:
    - 2 players: "{game-slug}-{player1}-vs-{player2}-{date-time}"
    - 3+ players: "{game-slug}-{session-type}-{count}p-{date-time}"
    - Named session: "{game-slug}-{session-name}-{date-time}"
    - Tournament: "{game-slug}-tournament-r{round}-{date-time}"
    
    Examples:
    - "chess-blitz-alice-vs-bob-2025-01-19-15-30"
    - "trivia-challenge-tournament-4p-2025-01-19-20-00" 
    - "reaction-time-casual-8p-2025-01-19-18-45"
    """
    if start_time is None:
        start_time = datetime.utcnow()
    
    # Format timestamp for URL (YYYY-MM-DD-HH-MM)
    time_str = start_time.strftime("%Y-%m-%d-%H-%M")
    
    # Clean participant names for URLs
    clean_participants = [slugify(name) for name in participants if name]
    
    # Handle different session patterns
    if session_name and session_name.strip():
        # Named session (e.g., custom tournament names)
        session_part = slugify(session_name.strip())
        return f"{game_slug}-{session_part}-{time_str}"
    
    elif len(clean_participants) == 2:
        # Head-to-head match
        player1, player2 = clean_participants[:2]
        return f"{game_slug}-{player1}-vs-{player2}-{time_str}"
    
    elif len(clean_participants) >= 3:
        # Multi-player session
        session_type_slug = slugify(session_type)
        participant_count = len(clean_participants)
        return f"{game_slug}-{session_type_slug}-{participant_count}p-{time_str}"
    
    else:
        # Default format (waiting for players or single player)
        session_type_slug = slugify(session_type)
        return f"{game_slug}-{session_type_slug}-{time_str}"


def generate_tournament_session_slug(
    game_slug: str,
    tournament_name: str,
    round_number: int,
    match_number: int,
    participants: List[str],
    start_time: Optional[datetime] = None
) -> str:
    """
    Generate slug for tournament session
    
    Format: "{game-slug}-{tournament-name}-r{round}-m{match}-{date}"
    Example: "chess-blitz-winter-cup-r2-m3-2025-01-19"
    """
    if start_time is None:
        start_time = datetime.utcnow()
    
    tournament_slug = slugify(tournament_name)
    date_str = start_time.strftime("%Y-%m-%d")
    
    # For head-to-head tournament matches, include player names
    if len(participants) == 2:
        clean_participants = [slugify(name) for name in participants]
        player1, player2 = clean_participants[:2]
        return f"{game_slug}-{tournament_slug}-r{round_number}-{player1}-vs-{player2}-{date_str}"
    else:
        return f"{game_slug}-{tournament_slug}-r{round_number}-m{match_number}-{date_str}"


def extract_info_from_session_slug(slug: str) -> dict:
    """
    Extract information from a session slug for display purposes
    
    Returns dict with keys: game_type, session_info, participants, timestamp
    """
    parts = slug.split('-')
    
    if len(parts) < 4:
        return {"game_type": slug, "session_info": "", "participants": [], "timestamp": ""}
    
    # Try to extract timestamp (last 5 parts: YYYY-MM-DD-HH-MM)
    if len(parts) >= 5:
        try:
            timestamp_parts = parts[-5:]
            timestamp = "-".join(timestamp_parts)
            datetime.strptime(timestamp, "%Y-%m-%d-%H-%M")  # Validate format
            content_parts = parts[:-5]
        except ValueError:
            # Not a valid timestamp, treat as part of content
            content_parts = parts
            timestamp = ""
    else:
        content_parts = parts
        timestamp = ""
    
    # Extract game type (first part)
    game_type = content_parts[0] if content_parts else ""
    
    # Look for "vs" pattern to identify head-to-head matches
    if "vs" in content_parts:
        vs_index = content_parts.index("vs")
        if vs_index > 1 and vs_index < len(content_parts) - 1:
            player1 = content_parts[vs_index - 1]
            player2 = content_parts[vs_index + 1]
            return {
                "game_type": game_type,
                "session_info": "Head-to-head match",
                "participants": [player1, player2],
                "timestamp": timestamp
            }
    
    # Extract other session info
    session_info = "-".join(content_parts[1:]) if len(content_parts) > 1 else ""
    
    return {
        "game_type": game_type,
        "session_info": session_info,
        "participants": [],
        "timestamp": timestamp
    }


def validate_slug(slug: str, max_length: int = 300) -> bool:
    """
    Validate slug format and length
    
    Rules:
    - Only lowercase letters, numbers, and hyphens
    - No consecutive hyphens
    - No leading/trailing hyphens
    - Within max_length limit
    """
    if not slug or len(slug) > max_length:
        return False
    
    # Check format
    if not re.match(r'^[a-z0-9]+(-[a-z0-9]+)*$', slug):
        return False
    
    return True


# Example usage and test cases
if __name__ == "__main__":
    # Test game slug generation
    print("Game slugs:")
    print(generate_game_slug("Trivia Challenge"))
    print(generate_game_slug("Chess Blitz"))
    print(generate_game_slug("Reaction Time Master!"))
    
    print("\nSession slugs:")
    # Test session slug generation
    test_time = datetime(2025, 1, 19, 15, 30)
    
    # Head-to-head
    print(generate_session_slug("chess-blitz", ["Alice", "Bob"], "ranked", test_time))
    
    # Multi-player
    print(generate_session_slug("trivia-challenge", ["Alice", "Bob", "Charlie", "Diana"], "tournament", test_time))
    
    # Named session
    print(generate_session_slug("reaction-time", [], "custom", test_time, "Friday Night Showdown"))
    
    # Tournament
    print(generate_tournament_session_slug("chess-blitz", "Winter Cup 2025", 2, 3, ["Alice", "Bob"], test_time))
    
    print("\nSlug info extraction:")
    print(extract_info_from_session_slug("chess-blitz-alice-vs-bob-2025-01-19-15-30"))