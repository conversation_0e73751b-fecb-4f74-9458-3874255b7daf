"""
BetBet Gaming Engine - Bets API
===============================

API endpoints for bet placement, management, and history.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, update, desc
from sqlalchemy.orm import selectinload, joinedload
import structlog

# Import SQLAlchemy models
from app.database.models import (
    UserBet, BettingMarket, BettingOutcome, BettingMarketOddsHistory,
    User, GameSession, Tournament
)

# Import Pydantic models for validation
from app.models.betting import (
    UserBetCreate, UserBetResponse, UserBetListResponse,
    BettingHistoryResponse, BettingStatsResponse,
    BetCancellationRequest
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_optional_user,
    get_pagination, PaginationParams,
    NotFoundError, ValidationError, PermissionError, ConflictError
)
from app.websocket.handlers import betting_handler
from shared.core.auth.models import TokenData

# Import wallet client
import sys
from pathlib import Path
services_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(services_dir))
from shared.core.financial.wallet_client import WalletClient, InsufficientFundsError, FeeCategory, FeeType

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.post("/bets", response_model=UserBetResponse, status_code=status.HTTP_201_CREATED)
async def place_bet(
    bet_data: UserBetCreate,
    request: Request,
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Place a new bet
    
    Places a bet on a specific outcome in a betting market.
    Validates user balance, market status, and outcome availability.
    """
    try:
        # Get the betting market and outcome
        market_query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(BettingMarket.id == bet_data.market_id)
        
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(bet_data.market_id))
        
        # Check market status
        if market.status != "active":
            raise ValidationError(f"Market is {market.status}, betting not allowed")
        
        # Check market timing
        if market.closes_at and datetime.utcnow() >= market.closes_at:
            raise ValidationError("Market has closed for betting")
        
        # Find the specific outcome
        outcome = next((o for o in market.outcomes if o.id == bet_data.outcome_id), None)
        if not outcome:
            raise NotFoundError("BettingOutcome", str(bet_data.outcome_id))
        
        if not outcome.is_active:
            raise ValidationError("Outcome is not active for betting")
        
        # Validate bet amount
        if bet_data.bet_amount <= 0:
            raise ValidationError("Bet amount must be positive")
        
        if bet_data.bet_amount < Decimal("1.00"):
            raise ValidationError("Minimum bet amount is $1.00")
        
        if bet_data.bet_amount > Decimal("1000.00"):
            raise ValidationError("Maximum bet amount is $1000.00")
        
        # Initialize wallet client for balance check and fund locking
        async with WalletClient(service_name="gaming-engine") as wallet:
            # Check user balance
            balance = await wallet.get_balance(str(user.user_id))
            
            if bet_data.bet_amount > balance.available_balance:
                raise ValidationError(
                    f"Insufficient balance. Available: ${balance.available_balance}, Required: ${bet_data.bet_amount}"
                )
            
            # Calculate potential return using decimal odds
            # Our odds are stored as decimal odds (e.g., 2.1 means 2.1x payout)
            potential_return = bet_data.bet_amount * outcome.odds
        
        logger.info(
            "Bet calculation details",
            bet_amount=float(bet_data.bet_amount),
            outcome_odds=float(outcome.odds),
            potential_return=float(potential_return),
            user_id=str(user.user_id)
        )
        
        # Get client IP and user agent for risk management
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # Convert IP to proper format for inet column type
        if client_ip == "127.0.0.1":
            client_ip = "127.0.0.1"  # Ensure proper format
        elif client_ip:
            # For other IPs, ensure they're valid
            import ipaddress
            try:
                ipaddress.ip_address(client_ip)
            except ValueError:
                client_ip = None  # Invalid IP, set to None
        
        # Create the bet and lock funds
        logger.info("Creating UserBet object", user_id=str(user.user_id), market_id=str(bet_data.market_id))
        
        # Re-acquire wallet client for fund locking (inside the transaction context)
        async with WalletClient(service_name="gaming-engine") as wallet:
            try:
                bet = UserBet(
                    user_id=user.user_id,
                    market_id=bet_data.market_id,
                    outcome_id=bet_data.outcome_id,
                    bet_amount=bet_data.bet_amount,
                    odds=outcome.odds,
                    potential_return=potential_return,
                    status="pending",  # Correct status for new bets (pending, won, lost, void, cancelled)
                    ip_address=client_ip,
                    user_agent=user_agent,
                    created_by=user.user_id,  # Add created_by to satisfy FK constraint
                    updated_by=user.user_id   # Add updated_by to satisfy FK constraint
                )
                logger.info("UserBet object created successfully")
                
                db.add(bet)
                await db.flush()  # Get the bet ID for wallet reference
                
                # Lock funds for the bet
                lock_result = await wallet.create_spectator_bet(
                    user_id=str(user.user_id),
                    bet_amount=bet_data.bet_amount,
                    bet_id=str(bet.id),
                    market_type=market.market_type
                )
                
                if not lock_result.success:
                    # If wallet operation fails, rollback the bet creation
                    await db.rollback()
                    raise ValidationError(f"Failed to lock funds: {lock_result.error_message}")
                
                logger.info(
                    "Funds locked for bet",
                    bet_id=str(bet.id),
                    user_id=str(user.user_id),
                    amount=float(bet_data.bet_amount),
                    new_balance=float(lock_result.new_balance)
                )
                
            except Exception as bet_creation_error:
                logger.error("Failed to create UserBet object or lock funds", error=str(bet_creation_error))
                await db.rollback()
                raise
        
        # Update outcome statistics
        outcome.total_bet_amount += bet_data.bet_amount
        outcome.bet_count += 1
        outcome.updated_at = datetime.utcnow()
        
        # Update market statistics
        market.total_pool += bet_data.bet_amount
        market.total_bets += 1
        market.updated_at = datetime.utcnow()
        
        # Record odds history
        odds_history = BettingMarketOddsHistory(
            outcome_id=outcome.id,
            old_odds=outcome.odds,
            new_odds=outcome.odds,  # For now, keep same odds
            old_implied_probability=outcome.implied_probability,
            new_implied_probability=outcome.implied_probability,
            change_reason="bet_placement",
            change_amount=bet_data.bet_amount,
            created_by=user.user_id
        )
        db.add(odds_history)
        
        await db.commit()
        
        # Fetch the complete bet with relationships
        bet_query = select(UserBet).options(
            joinedload(UserBet.market),
            joinedload(UserBet.outcome)
        ).where(UserBet.id == bet.id)
        
        bet_result = await db.execute(bet_query)
        complete_bet = bet_result.scalar_one()
        
        logger.info(
            "Bet placed successfully",
            bet_id=str(bet.id),
            user_id=str(user.user_id),
            market_id=str(bet_data.market_id),
            outcome_id=str(bet_data.outcome_id),
            bet_amount=float(bet_data.bet_amount),
            potential_return=float(potential_return)
        )
        
        # Send WebSocket notification for new bet
        try:
            await betting_handler.broadcast_new_bet(
                market_id=str(bet_data.market_id),
                outcome_id=str(bet_data.outcome_id),
                bet_amount=float(bet_data.bet_amount),
                total_pool=float(market.total_pool),
                db=db
            )
        except Exception as ws_error:
            logger.warning("WebSocket broadcast failed", error=str(ws_error))
            # Continue without failing the bet placement
        
        # TODO: Trigger odds recalculation if needed
        # For now, we keep odds static but in production this would trigger
        # dynamic odds adjustment based on betting volume
        
        return UserBetResponse.model_validate(complete_bet)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to place bet", error=str(e), error_type=type(e).__name__)
        import traceback
        logger.error("Full traceback", traceback=traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to place bet: {str(e)}"
        )


@router.get("/bets/user/{user_id}", response_model=List[UserBetResponse])
async def get_user_bets(
    user_id: UUID,
    status_filter: Optional[str] = Query(None, description="Filter by bet status"),
    market_type: Optional[str] = Query(None, description="Filter by market type"),
    limit: int = Query(50, ge=1, le=100, description="Number of bets to return"),
    offset: int = Query(0, ge=0, description="Number of bets to skip"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Get user's bets
    
    Returns a list of bets placed by the user with optional filtering.
    Users can only access their own bets unless they are admins.
    """
    try:
        # Check permission
        if current_user.user_id != user_id and "admin" not in current_user.roles:
            raise PermissionError("You can only access your own bets")
        
        # Build query
        query = select(UserBet).options(
            joinedload(UserBet.market),
            joinedload(UserBet.outcome)
        ).where(UserBet.user_id == user_id)
        
        # Apply filters
        if status_filter:
            query = query.where(UserBet.status == status_filter)
        
        if market_type:
            query = query.join(BettingMarket).where(BettingMarket.market_type == market_type)
        
        # Apply ordering and pagination
        query = query.order_by(desc(UserBet.placed_at)).offset(offset).limit(limit)
        
        result = await db.execute(query)
        bets = result.scalars().all()
        
        logger.info(
            "Retrieved user bets",
            user_id=str(user_id),
            bet_count=len(bets),
            status_filter=status_filter,
            market_type=market_type,
            requested_by=str(current_user.user_id)
        )
        
        return [UserBetResponse.model_validate(bet) for bet in bets]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get user bets", user_id=str(user_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user bets"
        )


@router.get("/bets/{bet_id}", response_model=UserBetResponse)
async def get_bet(
    bet_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Get a specific bet
    
    Returns detailed information about a specific bet.
    Users can only access their own bets unless they are admins.
    """
    try:
        query = select(UserBet).options(
            joinedload(UserBet.market),
            joinedload(UserBet.outcome)
        ).where(UserBet.id == bet_id)
        
        result = await db.execute(query)
        bet = result.scalar_one_or_none()
        
        if not bet:
            raise NotFoundError("Bet", str(bet_id))
        
        # Check permission
        if bet.user_id != user.user_id and "admin" not in user.roles:
            raise PermissionError("You can only access your own bets")
        
        logger.info(
            "Retrieved bet",
            bet_id=str(bet_id),
            bet_user_id=str(bet.user_id),
            requested_by=str(user.user_id)
        )
        
        return UserBetResponse.model_validate(bet)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get bet", bet_id=str(bet_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve bet"
        )


@router.post("/bets/{bet_id}/cancel", status_code=status.HTTP_200_OK)
async def cancel_bet(
    bet_id: UUID,
    cancellation_data: BetCancellationRequest,
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Cancel a bet
    
    Cancels a pending bet and refunds the user.
    Only pending bets can be cancelled.
    """
    try:
        # Get the bet
        query = select(UserBet).options(
            joinedload(UserBet.market),
            joinedload(UserBet.outcome)
        ).where(UserBet.id == bet_id)
        
        result = await db.execute(query)
        bet = result.scalar_one_or_none()
        
        if not bet:
            raise NotFoundError("Bet", str(bet_id))
        
        # Check permission
        if bet.user_id != user.user_id and "admin" not in user.roles:
            raise PermissionError("You can only cancel your own bets")
        
        # Check if bet can be cancelled
        if bet.status != "pending":
            raise ConflictError(f"Cannot cancel bet with status: {bet.status}")
        
        # Check market status (some markets may not allow cancellations)
        if bet.market.status not in ["active", "suspended"]:
            raise ConflictError("Cannot cancel bet in this market state")
        
        # Unlock funds before updating bet status
        async with WalletClient(service_name="gaming-engine") as wallet:
            try:
                # Unlock the locked bet amount (return original stake, no winnings)
                unlock_result = await wallet.settle_spectator_bet(
                    user_id=str(bet.user_id),
                    original_stake=bet.bet_amount,
                    payout_amount=bet.bet_amount,  # Return original stake only
                    bet_id=str(bet.id),
                    market_type=bet.market.market_type
                )
                
                if not unlock_result.success:
                    logger.error(
                        "Failed to unlock funds for bet cancellation",
                        bet_id=str(bet.id),
                        error=unlock_result.error_message
                    )
                    # Continue with cancellation even if unlock fails (admin can resolve manually)
                
                logger.info(
                    "Funds unlocked for bet cancellation",
                    bet_id=str(bet.id),
                    user_id=str(bet.user_id),
                    refunded_amount=float(bet.bet_amount),
                    new_balance=float(unlock_result.new_balance) if unlock_result.success else None
                )
                
            except Exception as wallet_error:
                logger.error(
                    "Wallet error during bet cancellation",
                    bet_id=str(bet.id),
                    error=str(wallet_error)
                )
                # Continue with cancellation - admin can resolve wallet issues manually
        
        # Update bet status
        bet.status = "cancelled"
        bet.settlement_reason = cancellation_data.reason
        bet.settled_at = datetime.utcnow()
        bet.updated_by = user.user_id
        bet.updated_at = datetime.utcnow()
        
        # Update outcome statistics
        bet.outcome.total_bet_amount -= bet.bet_amount
        bet.outcome.bet_count -= 1
        bet.outcome.updated_at = datetime.utcnow()
        
        # Update market statistics
        bet.market.total_pool -= bet.bet_amount
        bet.market.total_bets -= 1
        bet.market.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(
            "Bet cancelled",
            bet_id=str(bet_id),
            user_id=str(bet.user_id),
            bet_amount=float(bet.bet_amount),
            reason=cancellation_data.reason,
            cancelled_by=str(user.user_id)
        )
        
        # Send WebSocket notification for bet cancellation (impacts pool/odds)
        await betting_handler.broadcast_new_bet(
            market_id=str(bet.market_id),
            outcome_id=str(bet.outcome_id),
            bet_amount=-float(bet.bet_amount),  # Negative amount indicates cancellation
            total_pool=float(bet.market.total_pool),
            db=db
        )
        
        return {"message": "Bet cancelled successfully", "bet_id": str(bet_id)}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to cancel bet", bet_id=str(bet_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel bet"
        )


@router.get("/users/{user_id}/betting-history", response_model=BettingHistoryResponse)
async def get_user_betting_history(
    user_id: UUID,
    status_filter: Optional[str] = Query(None, description="Filter by bet status"),
    market_type: Optional[str] = Query(None, description="Filter by market type"),
    date_from: Optional[datetime] = Query(None, description="Start date filter"),
    date_to: Optional[datetime] = Query(None, description="End date filter"),
    pagination: PaginationParams = Depends(get_pagination),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Get comprehensive betting history for a user
    
    Returns detailed betting history with filtering and pagination.
    """
    try:
        # Check permission
        if current_user.user_id != user_id and "admin" not in current_user.roles:
            raise PermissionError("You can only access your own betting history")
        
        # Build base query
        query = select(UserBet).options(
            joinedload(UserBet.market),
            joinedload(UserBet.outcome)
        ).where(UserBet.user_id == user_id)
        
        # Apply filters
        if status_filter:
            query = query.where(UserBet.status == status_filter)
        
        if market_type:
            query = query.join(BettingMarket).where(BettingMarket.market_type == market_type)
        
        if date_from:
            query = query.where(UserBet.placed_at >= date_from)
        
        if date_to:
            query = query.where(UserBet.placed_at <= date_to)
        
        # Get total count
        count_query = select(func.count(UserBet.id)).select_from(query.subquery())
        count_result = await db.execute(count_query)
        total_count = count_result.scalar()
        
        # Apply ordering and pagination
        query = query.order_by(desc(UserBet.placed_at)).offset(pagination.offset).limit(pagination.limit)
        
        result = await db.execute(query)
        bets = result.scalars().all()
        
        logger.info(
            "Retrieved user betting history",
            user_id=str(user_id),
            total_count=total_count,
            returned_count=len(bets),
            requested_by=str(current_user.user_id)
        )
        
        return BettingHistoryResponse(
            bets=[UserBetResponse.model_validate(bet) for bet in bets],
            total_count=total_count,
            page=pagination.page,
            limit=pagination.limit,
            has_next=pagination.offset + len(bets) < total_count,
            has_prev=pagination.offset > 0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get user betting history", user_id=str(user_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting history"
        )


@router.get("/users/{user_id}/betting-stats", response_model=BettingStatsResponse)
async def get_user_betting_stats(
    user_id: UUID,
    date_from: Optional[datetime] = Query(None, description="Start date for stats calculation"),
    date_to: Optional[datetime] = Query(None, description="End date for stats calculation"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Get comprehensive betting statistics for a user
    
    Returns detailed betting analytics including win rate,
    profit/loss, favorite markets, etc.
    """
    try:
        # Check permission
        if current_user.user_id != user_id and "admin" not in current_user.roles:
            raise PermissionError("You can only access your own betting statistics")
        
        # Build base query with date filters
        base_query = select(UserBet).where(UserBet.user_id == user_id)
        
        if date_from:
            base_query = base_query.where(UserBet.placed_at >= date_from)
        if date_to:
            base_query = base_query.where(UserBet.placed_at <= date_to)
        
        # Get basic statistics
        stats_query = select(
            func.count(UserBet.id).label("total_bets"),
            func.sum(UserBet.bet_amount).label("total_wagered"),
            func.sum(UserBet.actual_return).label("total_returned"),
            func.avg(UserBet.bet_amount).label("average_bet"),
            func.max(UserBet.bet_amount).label("largest_bet"),
            func.count(UserBet.id).filter(UserBet.status == "won").label("wins"),
            func.count(UserBet.id).filter(UserBet.status == "lost").label("losses"),
            func.count(UserBet.id).filter(UserBet.status == "pending").label("pending")
        ).select_from(base_query.subquery())
        
        stats_result = await db.execute(stats_query)
        stats = stats_result.first()
        
        # Calculate derived statistics
        total_bets = stats.total_bets or 0
        total_wagered = float(stats.total_wagered or 0)
        total_returned = float(stats.total_returned or 0)
        net_profit = total_returned - total_wagered
        win_rate = (stats.wins / total_bets * 100) if total_bets > 0 else 0
        
        # Get favorite market type
        market_type_query = select(
            BettingMarket.market_type,
            func.count(UserBet.id).label("bet_count")
        ).select_from(
            UserBet.__table__.join(BettingMarket.__table__)
        ).where(UserBet.user_id == user_id).group_by(
            BettingMarket.market_type
        ).order_by(desc("bet_count")).limit(1)
        
        if date_from:
            market_type_query = market_type_query.where(UserBet.placed_at >= date_from)
        if date_to:
            market_type_query = market_type_query.where(UserBet.placed_at <= date_to)
        
        market_type_result = await db.execute(market_type_query)
        favorite_market = market_type_result.first()
        
        # Get biggest win
        biggest_win_query = select(
            func.max(UserBet.actual_return - UserBet.bet_amount)
        ).select_from(base_query.subquery()).where(UserBet.status == "won")
        
        biggest_win_result = await db.execute(biggest_win_query)
        biggest_win = float(biggest_win_result.scalar() or 0)
        
        betting_stats = BettingStatsResponse(
            total_bets=total_bets,
            total_wagered=total_wagered,
            total_returned=total_returned,
            net_profit=net_profit,
            win_rate=win_rate,
            average_odds=float(stats.average_bet or 0),
            biggest_win=biggest_win,
            longest_streak=0,  # TODO: Calculate longest winning streak
            favorite_market_type=favorite_market.market_type if favorite_market else "",
            total_pending=stats.pending or 0
        )
        
        logger.info(
            "Retrieved user betting stats",
            user_id=str(user_id),
            total_bets=total_bets,
            net_profit=net_profit,
            requested_by=str(current_user.user_id)
        )
        
        return betting_stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get user betting stats", user_id=str(user_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting statistics"
        )


@router.post("/betting-markets/{market_id}/settle", status_code=status.HTTP_200_OK)
async def settle_betting_market(
    market_id: UUID,
    winning_outcome_id: UUID,
    settlement_reason: str = "market_resolution",
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Settle a betting market by declaring the winning outcome
    
    Settles all bets in the market, distributing winnings and collecting platform fees.
    Requires admin privileges.
    """
    try:
        # Check if user has permission
        if "admin" not in user.roles:
            raise PermissionError("Only administrators can settle betting markets")
        
        # Get the market and all its bets
        market_query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes),
            selectinload(BettingMarket.bets)
        ).where(BettingMarket.id == market_id)
        
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(market_id))
        
        # Check if market can be settled
        if market.status not in ["active", "suspended"]:
            raise ConflictError(f"Cannot settle market with status: {market.status}")
        
        # Verify winning outcome exists
        winning_outcome = next((o for o in market.outcomes if o.id == winning_outcome_id), None)
        if not winning_outcome:
            raise NotFoundError("BettingOutcome", str(winning_outcome_id))
        
        # Get all pending bets for this market
        bets_query = select(UserBet).where(
            and_(
                UserBet.market_id == market_id,
                UserBet.status == "pending"
            )
        )
        bets_result = await db.execute(bets_query)
        pending_bets = bets_result.scalars().all()
        
        if not pending_bets:
            logger.info(f"No pending bets to settle for market {market_id}")
            return {"message": "No pending bets to settle", "settled_bets": 0}
        
        # Settle all bets using wallet service
        settlement_results = []
        total_platform_fees = Decimal('0')
        
        async with WalletClient(service_name="gaming-engine") as wallet:
            for bet in pending_bets:
                try:
                    # Determine if bet won
                    bet_won = bet.outcome_id == winning_outcome_id
                    
                    if bet_won:
                        # Calculate platform fee on winnings (net profit)
                        net_profit = bet.potential_return - bet.bet_amount
                        fee_calc = await wallet.calculate_platform_fee(
                            user_id=str(bet.user_id),
                            transaction_amount=net_profit,
                            fee_category=FeeCategory.GAMING,
                            fee_type=FeeType.STANDARD
                        )
                        
                        # Payout is original bet plus net profit minus platform fee
                        payout_amount = bet.bet_amount + net_profit - fee_calc.fee_amount
                        total_platform_fees += fee_calc.fee_amount
                        
                        # Update bet status to won
                        bet.status = "won"
                        bet.actual_return = payout_amount
                        
                    else:
                        # Bet lost - no payout
                        payout_amount = Decimal('0')
                        
                        # Update bet status to lost
                        bet.status = "lost"
                        bet.actual_return = Decimal('0')
                    
                    # Settle the bet in wallet service
                    settlement_result = await wallet.settle_spectator_bet(
                        user_id=str(bet.user_id),
                        original_stake=bet.bet_amount,
                        payout_amount=payout_amount,
                        bet_id=str(bet.id),
                        market_type=market.market_type
                    )
                    
                    # Update bet settlement fields
                    bet.settlement_reason = settlement_reason
                    bet.settled_at = datetime.utcnow()
                    bet.updated_by = user.user_id
                    bet.updated_at = datetime.utcnow()
                    
                    settlement_results.append({
                        "bet_id": str(bet.id),
                        "user_id": str(bet.user_id),
                        "won": bet_won,
                        "original_amount": float(bet.bet_amount),
                        "payout": float(payout_amount),
                        "wallet_success": settlement_result.success,
                        "new_balance": float(settlement_result.new_balance) if settlement_result.success else None
                    })
                    
                    logger.info(
                        "Bet settled",
                        bet_id=str(bet.id),
                        user_id=str(bet.user_id),
                        market_id=str(market_id),
                        won=bet_won,
                        payout=float(payout_amount),
                        wallet_success=settlement_result.success
                    )
                    
                except Exception as bet_settlement_error:
                    logger.error(
                        "Failed to settle individual bet",
                        bet_id=str(bet.id),
                        error=str(bet_settlement_error)
                    )
                    # Mark bet as error status for manual resolution
                    bet.status = "error"
                    bet.settlement_reason = f"settlement_error: {str(bet_settlement_error)}"
                    bet.settled_at = datetime.utcnow()
                    
                    settlement_results.append({
                        "bet_id": str(bet.id),
                        "user_id": str(bet.user_id),
                        "won": False,
                        "original_amount": float(bet.bet_amount),
                        "payout": 0.0,
                        "wallet_success": False,
                        "error": str(bet_settlement_error)
                    })
        
        # Update market status
        market.status = "settled"
        market.winning_outcome_id = winning_outcome_id
        market.settled_at = datetime.utcnow()
        market.updated_by = user.user_id
        market.updated_at = datetime.utcnow()
        
        # Update winning outcome status
        winning_outcome.is_winning = True
        winning_outcome.updated_at = datetime.utcnow()
        
        await db.commit()
        
        successful_settlements = sum(1 for r in settlement_results if r.get("wallet_success", False))
        total_payouts = sum(r["payout"] for r in settlement_results)
        
        logger.info(
            "Betting market settled",
            market_id=str(market_id),
            winning_outcome_id=str(winning_outcome_id),
            total_bets=len(pending_bets),
            successful_settlements=successful_settlements,
            total_payouts=total_payouts,
            total_platform_fees=float(total_platform_fees),
            settled_by=str(user.user_id)
        )
        
        return {
            "message": "Betting market settled successfully",
            "market_id": str(market_id),
            "winning_outcome_id": str(winning_outcome_id),
            "total_bets_settled": len(pending_bets),
            "successful_settlements": successful_settlements,
            "total_payouts": total_payouts,
            "total_platform_fees": float(total_platform_fees),
            "settlement_details": settlement_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to settle betting market", market_id=str(market_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to settle betting market: {str(e)}"
        )