"""
BetBet Gaming Engine - Tournaments API
======================================

API endpoints for tournament management and bracket systems.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

# Import SQLAlchemy models
from app.database.models import Tournament, TournamentParticipant

# Import Pydantic models for validation
from app.models.tournaments import (
    TournamentCreate, TournamentUpdate, TournamentResponse,
    TournamentParticipantResponse, TournamentBracket, TournamentStats
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_admin_user, get_game_moderator, get_optional_user,
    get_pagination, PaginationParams,
    NotFoundError, ValidationError, PermissionError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/tournaments", response_model=List[TournamentResponse])
async def list_tournaments(
    pagination: PaginationParams = Depends(get_pagination),
    game_id: Optional[UUID] = Query(None, description="Filter by game ID"),
    status: Optional[str] = Query(None, description="Filter by tournament status"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    registration_open: Optional[bool] = Query(None, description="Filter by registration open"),
    sort_by: str = Query("tournament_start", description="Sort field"),
    sort_order: str = Query("asc", description="Sort order (asc/desc)"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    List tournaments with filtering and pagination
    
    Returns a list of tournaments matching the specified filters.
    Public endpoint - authentication optional.
    """
    try:
        from sqlalchemy import select, func, and_, or_
        
        # Build base query
        query = select(Tournament).where(Tournament.deleted_at.is_(None))
        
        # Apply filters
        if game_id:
            query = query.where(Tournament.game_id == game_id)
            
        if status:
            query = query.where(Tournament.status == status)
            
        if is_featured is not None:
            query = query.where(Tournament.is_featured == is_featured)
            
        if registration_open is not None:
            from datetime import datetime
            now = datetime.utcnow()
            if registration_open:
                query = query.where(
                    and_(
                        Tournament.registration_opens_at <= now,
                        Tournament.registration_closes_at > now,
                        Tournament.status == "registration_open"
                    )
                )
            else:
                query = query.where(
                    or_(
                        Tournament.registration_closes_at <= now,
                        Tournament.status != "registration_open"
                    )
                )
        
        # Apply sorting
        if sort_order.lower() == "desc":
            sort_column = getattr(Tournament, sort_by).desc()
        else:
            sort_column = getattr(Tournament, sort_by).asc()
        query = query.order_by(sort_column)
        
        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await db.execute(query)
        tournaments = result.scalars().all()
        
        # Convert to response models
        tournament_responses = [TournamentResponse.model_validate(tournament) for tournament in tournaments]
        
        logger.info(
            "Listed tournaments",
            count=len(tournament_responses),
            page=pagination.page,
            user_id=current_user.user_id if current_user else None
        )
        
        return tournament_responses
        
    except Exception as e:
        logger.error("Failed to list tournaments", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournaments"
        )


@router.get("/tournaments/slug/{slug}", response_model=TournamentResponse)
async def get_tournament_by_slug(
    slug: str,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a tournament by its slug
    
    Returns detailed information about a tournament using its URL-friendly slug
    instead of the UUID. This provides user-friendly URLs for sharing.
    """
    try:
        from sqlalchemy import select, and_
        
        query = select(Tournament).where(
            and_(Tournament.slug == slug, Tournament.deleted_at.is_(None))
        )
        result = await db.execute(query)
        tournament = result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", slug)
        
        logger.info(
            "Retrieved tournament by slug",
            slug=slug,
            tournament_id=tournament.id,
            tournament_name=tournament.name,
            user_id=current_user.user_id if current_user else None
        )
        
        return TournamentResponse.model_validate(tournament)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tournament not found (slug: {slug})"
        )
    except Exception as e:
        logger.error("Failed to get tournament by slug", slug=slug, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournament"
        )


@router.get("/tournaments/{tournament_id}", response_model=TournamentResponse)
async def get_tournament(
    tournament_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific tournament by ID
    
    Returns detailed information about a tournament including current status,
    participant counts, and prize pool information.
    """
    try:
        from sqlalchemy import select, and_
        
        query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        result = await db.execute(query)
        tournament = result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        logger.info(
            "Retrieved tournament",
            tournament_id=tournament_id,
            tournament_name=tournament.name,
            user_id=current_user.user_id if current_user else None
        )
        
        return TournamentResponse.model_validate(tournament)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tournament not found (ID: {tournament_id})"
        )
    except Exception as e:
        logger.error("Failed to get tournament", tournament_id=tournament_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournament"
        )


@router.post("/tournaments", response_model=TournamentResponse, status_code=status.HTTP_201_CREATED)
async def create_tournament(
    tournament_data: TournamentCreate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_game_moderator)
):
    """
    Create a new tournament
    
    Creates a new tournament with the specified configuration. Requires
    moderator or admin privileges.
    """
    try:
        from sqlalchemy import select
        from app.database.models import Game
        from app.utils.slugs import generate_game_slug
        
        # Verify game exists and is active
        game_query = select(Game).where(
            Game.id == tournament_data.game_id
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise ValidationError(f"Game not found (ID: {tournament_data.game_id})")
            
        if not game.is_active:
            raise ValidationError("Cannot create tournament for inactive game")
        
        # Check for slug uniqueness
        existing_query = select(Tournament).where(Tournament.slug == tournament_data.slug)
        existing_tournament = await db.scalar(existing_query)
        
        if existing_tournament:
            raise ValidationError(f"Tournament with slug '{tournament_data.slug}' already exists")
        
        # Create tournament record
        tournament = Tournament(
            **tournament_data.model_dump(exclude={'created_by'}),
            status="draft",
            current_participants=0,
            current_round=0,
            total_entry_fees=0,
            platform_fee=0,
            remaining_prize_pool=tournament_data.prize_pool,
            bracket_structure={},
            current_matches=[],
            final_standings=[],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            created_by=current_user.user_id,
            updated_by=current_user.user_id,
            version=1
        )
        
        db.add(tournament)
        await db.commit()
        await db.refresh(tournament)
        
        logger.info(
            "Created tournament",
            tournament_id=tournament.id,
            tournament_name=tournament.name,
            game_id=tournament_data.game_id,
            created_by=current_user.user_id
        )
        
        return TournamentResponse.model_validate(tournament)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create tournament", error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create tournament"
        )


@router.put("/tournaments/{tournament_id}")
async def update_tournament(tournament_id: UUID):
    """Update an existing tournament"""
    # TODO: Implement tournament updates
    return {"message": f"Update tournament {tournament_id} - TODO"}


@router.post("/tournaments/{tournament_id}/register", response_model=TournamentParticipantResponse)
async def register_for_tournament(
    tournament_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Register for a tournament
    
    Registers the current user as a participant in the specified tournament.
    Validates registration requirements and processes entry fee.
    """
    try:
        from sqlalchemy import select, and_
        from datetime import datetime
        
        # Get tournament
        tournament_query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        tournament_result = await db.execute(tournament_query)
        tournament = tournament_result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        # Check if tournament is open for registration
        now = datetime.utcnow()
        if tournament.status != "registration_open":
            raise ValidationError("Tournament registration is not currently open")
            
        if tournament.registration_closes_at and now > tournament.registration_closes_at:
            raise ValidationError("Tournament registration has closed")
            
        if tournament.registration_opens_at and now < tournament.registration_opens_at:
            raise ValidationError("Tournament registration has not yet opened")
        
        # Check if tournament is full
        if tournament.current_participants >= tournament.max_participants:
            raise ValidationError("Tournament is full")
        
        # Check if user is already registered
        existing_query = select(TournamentParticipant).where(
            and_(
                TournamentParticipant.tournament_id == tournament_id,
                TournamentParticipant.user_id == current_user.user_id
            )
        )
        existing_participant = await db.scalar(existing_query)
        
        if existing_participant:
            raise ValidationError("User is already registered for this tournament")
        
        # Create participant record
        participant = TournamentParticipant(
            tournament_id=tournament_id,
            user_id=current_user.user_id,
            entry_fee_paid=tournament.entry_fee,
            seed_number=tournament.current_participants + 1,  # Temporary seed
            status="registered",
            current_round=0,
            wins=0,
            losses=0,
            total_score=0,
            prize_amount=0,
            joined_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(participant)
        
        # Update tournament participant count and totals
        tournament.current_participants += 1
        tournament.total_entry_fees += tournament.entry_fee
        tournament.remaining_prize_pool = tournament.prize_pool + (tournament.total_entry_fees * 0.95)  # 5% platform fee
        tournament.platform_fee = tournament.total_entry_fees * 0.05
        tournament.updated_at = datetime.utcnow()
        tournament.updated_by = current_user.user_id
        tournament.version += 1
        
        await db.commit()
        await db.refresh(participant)
        
        logger.info(
            "User registered for tournament",
            tournament_id=tournament_id,
            user_id=current_user.user_id,
            entry_fee=tournament.entry_fee,
            participant_count=tournament.current_participants
        )
        
        return TournamentParticipantResponse.model_validate(participant)
        
    except (NotFoundError, ValidationError) as e:
        raise HTTPException(
            status_code=e.status_code if hasattr(e, 'status_code') else status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to register for tournament", tournament_id=tournament_id, user_id=current_user.user_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register for tournament"
        )


@router.get("/tournaments/{tournament_id}/bracket", response_model=TournamentBracket)
async def get_tournament_bracket(
    tournament_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get tournament bracket
    
    Returns the current tournament bracket structure including rounds,
    matches, and participant progression.
    """
    try:
        from sqlalchemy import select, and_
        
        # Get tournament
        tournament_query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        tournament_result = await db.execute(tournament_query)
        tournament = tournament_result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        # Get participants
        participants_query = select(TournamentParticipant).where(
            TournamentParticipant.tournament_id == tournament_id
        ).order_by(TournamentParticipant.seed_number)
        
        participants_result = await db.execute(participants_query)
        participants = participants_result.scalars().all()
        
        # Separate active and eliminated participants
        active_participants = [p for p in participants if p.status not in ["eliminated", "withdrawn", "disqualified"]]
        eliminated_participants = [p for p in participants if p.status in ["eliminated", "withdrawn", "disqualified"]]
        
        # Create bracket structure
        bracket = TournamentBracket(
            tournament_id=tournament_id,
            format=tournament.tournament_type,
            total_rounds=tournament.bracket_structure.get("total_rounds", 0),
            current_round=tournament.current_round,
            rounds=tournament.bracket_structure.get("rounds", []),
            matches=tournament.bracket_structure.get("matches", []),
            participants=[TournamentParticipantResponse.model_validate(p) for p in active_participants],
            eliminated_participants=[TournamentParticipantResponse.model_validate(p) for p in eliminated_participants],
            active_matches=tournament.current_matches,
            next_matches=tournament.bracket_structure.get("next_matches", [])
        )
        
        logger.info(
            "Retrieved tournament bracket",
            tournament_id=tournament_id,
            current_round=tournament.current_round,
            active_participants=len(active_participants),
            user_id=current_user.user_id if current_user else None
        )
        
        return bracket
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tournament not found (ID: {tournament_id})"
        )
    except Exception as e:
        logger.error("Failed to get tournament bracket", tournament_id=tournament_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournament bracket"
        )


@router.get("/tournaments/{tournament_id}/participants", response_model=List[TournamentParticipantResponse])
async def get_tournament_participants(
    tournament_id: UUID,
    include_eliminated: bool = Query(True, description="Include eliminated participants"),
    sort_by: str = Query("seed_number", description="Sort field"),
    sort_order: str = Query("asc", description="Sort order (asc/desc)"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get tournament participants
    
    Returns a list of participants in the tournament, optionally filtered
    to exclude eliminated participants.
    """
    try:
        from sqlalchemy import select, and_
        
        # Verify tournament exists
        tournament_query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        tournament_result = await db.execute(tournament_query)
        tournament = tournament_result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        # Build participants query
        query = select(TournamentParticipant).where(
            TournamentParticipant.tournament_id == tournament_id
        )
        
        # Filter by status if requested
        if not include_eliminated:
            query = query.where(
                TournamentParticipant.status.notin_(["eliminated", "withdrawn", "disqualified"])
            )
        
        # Apply sorting
        if sort_order.lower() == "desc":
            sort_column = getattr(TournamentParticipant, sort_by).desc()
        else:
            sort_column = getattr(TournamentParticipant, sort_by).asc()
        query = query.order_by(sort_column)
        
        # Execute query
        result = await db.execute(query)
        participants = result.scalars().all()
        
        # Convert to response models
        participant_responses = [TournamentParticipantResponse.model_validate(participant) for participant in participants]
        
        logger.info(
            "Retrieved tournament participants",
            tournament_id=tournament_id,
            participant_count=len(participant_responses),
            include_eliminated=include_eliminated,
            user_id=current_user.user_id if current_user else None
        )
        
        return participant_responses
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tournament not found (ID: {tournament_id})"
        )
    except Exception as e:
        logger.error("Failed to get tournament participants", tournament_id=tournament_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournament participants"
        )