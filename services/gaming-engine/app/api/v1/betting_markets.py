"""
BetBet Gaming Engine - Betting Markets API
==========================================

API endpoints for betting market management and operations.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, update
from sqlalchemy.orm import selectinload, joinedload
import structlog

# Import SQLAlchemy models
from app.database.models import (
    BettingMarket, BettingOutcome, UserBet, BettingMarketOddsHistory,
    GameSession, Tournament, User
)

# Import Pydantic models for validation
from app.models.betting import (
    BettingMarketCreate, BettingMarketUpdate, BettingMarketResponse,
    BettingOutcomeCreate, BettingOutcomeUpdate, BettingOutcomeResponse,
    BettingMarketListResponse, BettingMarketStats
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_optional_user, get_admin_user,
    get_pagination, PaginationParams,
    NotFoundError, ValidationError, PermissionError, ConflictError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/sessions/{session_id}/betting-markets", response_model=List[BettingMarketResponse])
async def get_session_betting_markets(
    session_id: UUID,
    include_inactive: bool = Query(False, description="Include inactive outcomes"),
    db: AsyncSession = Depends(get_read_database_session),
    user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get betting markets for a session
    
    Returns all betting markets associated with a specific session,
    including their outcomes and current odds.
    """
    try:
        # Check if session exists
        session_query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        # Build query for betting markets
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(
            and_(
                BettingMarket.session_id == session_id,
                BettingMarket.status.in_(["active", "suspended", "closed"])
            )
        ).order_by(BettingMarket.created_at)
        
        result = await db.execute(query)
        markets = result.scalars().all()
        
        # Filter outcomes if needed
        for market in markets:
            if not include_inactive:
                market.outcomes = [outcome for outcome in market.outcomes if outcome.is_active]
        
        logger.info(
            "Retrieved session betting markets",
            session_id=str(session_id),
            market_count=len(markets),
            user_id=str(user.user_id) if user else None
        )
        
        return [BettingMarketResponse.model_validate(market) for market in markets]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get session betting markets", session_id=str(session_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting markets"
        )


@router.get("/tournaments/{tournament_id}/betting-markets", response_model=List[BettingMarketResponse])
async def get_tournament_betting_markets(
    tournament_id: UUID,
    include_inactive: bool = Query(False, description="Include inactive outcomes"),
    db: AsyncSession = Depends(get_read_database_session),
    user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get betting markets for a tournament
    
    Returns all betting markets associated with a specific tournament,
    including their outcomes and current odds.
    """
    try:
        # Check if tournament exists
        tournament_query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        tournament_result = await db.execute(tournament_query)
        tournament = tournament_result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        # Build query for betting markets
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(
            and_(
                BettingMarket.tournament_id == tournament_id,
                BettingMarket.status.in_(["active", "suspended", "closed"])
            )
        ).order_by(BettingMarket.created_at)
        
        result = await db.execute(query)
        markets = result.scalars().all()
        
        # Filter outcomes if needed
        for market in markets:
            if not include_inactive:
                market.outcomes = [outcome for outcome in market.outcomes if outcome.is_active]
        
        logger.info(
            "Retrieved tournament betting markets",
            tournament_id=str(tournament_id),
            market_count=len(markets),
            user_id=str(user.user_id) if user else None
        )
        
        return [BettingMarketResponse.model_validate(market) for market in markets]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get tournament betting markets", tournament_id=str(tournament_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting markets"
        )


@router.post("/betting-markets", response_model=BettingMarketResponse, status_code=status.HTTP_201_CREATED)
async def create_betting_market(
    market_data: BettingMarketCreate,
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Create a new betting market
    
    Creates a new betting market with outcomes. Requires admin privileges.
    """
    try:
        # Check if user has permission (admin or game moderator)
        if "admin" not in user.roles:
            raise PermissionError("Only administrators can create betting markets")
        
        # TODO: Add back session/tournament validation once schema is aligned
        # For now, allow betting markets to be created without strict session/tournament validation
        
        # Create betting market
        market = BettingMarket(
            session_id=market_data.session_id,
            tournament_id=market_data.tournament_id,
            name=market_data.title,  # Map title to name column  
            description=market_data.description,
            market_type=market_data.market_type,
            status=market_data.status,
            closes_at=market_data.auto_close_at,  # Map auto_close_at to closes_at column
            market_config=market_data.market_config or {},
            settlement_criteria=market_data.settlement_criteria or {},
            created_by=user.user_id,
            updated_by=user.user_id
        )
        
        db.add(market)
        await db.flush()  # Get the market ID
        
        # Create outcomes
        for outcome_data in market_data.outcomes:
            outcome = BettingOutcome(
                market_id=market.id,
                name=outcome_data.title,  # Map title to name column
                description=outcome_data.description,
                outcome_type=outcome_data.outcome_type,
                odds=outcome_data.initial_odds,  # Map initial_odds to odds column
                implied_probability=outcome_data.implied_probability,
                external_id=outcome_data.external_id,
                created_by=user.user_id,
                updated_by=user.user_id
            )
            db.add(outcome)
        
        await db.commit()
        
        # Fetch the complete market with outcomes
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(BettingMarket.id == market.id)
        
        result = await db.execute(query)
        created_market = result.scalar_one()
        
        logger.info(
            "Created betting market",
            market_id=str(market.id),
            market_type=market.market_type,
            outcome_count=len(created_market.outcomes),
            created_by=str(user.user_id)
        )
        
        return BettingMarketResponse.model_validate(created_market)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to create betting market", error=str(e), error_type=type(e).__name__)
        import traceback
        logger.error("Traceback", traceback=traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create betting market: {str(e)}"
        )


@router.put("/betting-markets/{market_id}", response_model=BettingMarketResponse)
async def update_betting_market(
    market_id: UUID,
    market_data: BettingMarketUpdate,
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Update a betting market
    
    Updates market details and status. Requires admin privileges.
    """
    try:
        # Check if user has permission
        if "admin" not in user.roles:
            raise PermissionError("Only administrators can update betting markets")
        
        # Get existing market
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(BettingMarket.id == market_id)
        
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(market_id))
        
        # Update fields
        update_data = market_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field != "outcomes" and hasattr(market, field):
                setattr(market, field, value)
        
        market.updated_by = user.user_id
        market.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(
            "Updated betting market",
            market_id=str(market_id),
            updated_by=str(user.user_id)
        )
        
        return BettingMarketResponse.model_validate(market)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to update betting market", market_id=str(market_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update betting market"
        )


@router.delete("/betting-markets/{market_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_betting_market(
    market_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    user: TokenData = Depends(get_current_user)
):
    """
    Delete a betting market
    
    Soft deletes a betting market and all associated outcomes.
    Requires admin privileges.
    """
    try:
        # Check if user has permission
        if "admin" not in user.roles:
            raise PermissionError("Only administrators can delete betting markets")
        
        # Get market
        query = select(BettingMarket).where(BettingMarket.id == market_id)
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(market_id))
        
        # Check if market has active bets
        bets_query = select(func.count(UserBet.id)).where(
            and_(
                UserBet.market_id == market_id,
                UserBet.status == "pending"
            )
        )
        bets_result = await db.execute(bets_query)
        active_bets = bets_result.scalar()
        
        if active_bets > 0:
            raise ConflictError(f"Cannot delete market with {active_bets} active bets")
        
        # Soft delete (or hard delete if no bets exist)
        market.status = "deleted"
        market.updated_by = user.user_id
        market.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(
            "Deleted betting market",
            market_id=str(market_id),
            deleted_by=str(user.user_id)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Failed to delete betting market", market_id=str(market_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete betting market"
        )


@router.get("/betting-markets/{market_id}", response_model=BettingMarketResponse)
async def get_betting_market(
    market_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific betting market
    
    Returns detailed information about a betting market including all outcomes.
    """
    try:
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(BettingMarket.id == market_id)
        
        result = await db.execute(query)
        market = result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(market_id))
        
        logger.info(
            "Retrieved betting market",
            market_id=str(market_id),
            user_id=str(user.user_id) if user else None
        )
        
        return BettingMarketResponse.model_validate(market)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get betting market", market_id=str(market_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting market"
        )


@router.get("/betting-markets/{market_id}/stats", response_model=BettingMarketStats)
async def get_betting_market_stats(
    market_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get betting market statistics
    
    Returns detailed statistics about a betting market including
    betting volume, participant counts, and outcome distribution.
    """
    try:
        # Get market
        market_query = select(BettingMarket).where(BettingMarket.id == market_id)
        market_result = await db.execute(market_query)
        market = market_result.scalar_one_or_none()
        
        if not market:
            raise NotFoundError("BettingMarket", str(market_id))
        
        # Get betting statistics
        stats_query = select(
            func.count(UserBet.id).label("total_bets"),
            func.sum(UserBet.bet_amount).label("total_volume"),
            func.count(func.distinct(UserBet.user_id)).label("unique_bettors"),
            func.avg(UserBet.bet_amount).label("average_bet_size")
        ).where(UserBet.market_id == market_id)
        
        stats_result = await db.execute(stats_query)
        stats = stats_result.first()
        
        # Get outcome statistics
        outcome_stats_query = select(
            BettingOutcome.id,
            BettingOutcome.name,
            BettingOutcome.total_bet_amount,
            BettingOutcome.bet_count,
            BettingOutcome.odds
        ).where(BettingOutcome.market_id == market_id)
        
        outcome_stats_result = await db.execute(outcome_stats_query)
        outcome_stats = outcome_stats_result.all()
        
        market_stats = BettingMarketStats(
            market_id=market_id,
            total_bets=stats.total_bets or 0,
            total_volume=float(stats.total_volume or 0),
            unique_bettors=stats.unique_bettors or 0,
            average_bet_size=float(stats.average_bet_size or 0),
            outcome_stats=[
                {
                    "outcome_id": str(outcome.id),
                    "outcome_name": outcome.name,
                    "total_bet_amount": float(outcome.total_bet_amount),
                    "bet_count": outcome.bet_count,
                    "current_odds": float(outcome.odds)
                }
                for outcome in outcome_stats
            ]
        )
        
        logger.info(
            "Retrieved betting market stats",
            market_id=str(market_id),
            total_bets=market_stats.total_bets,
            user_id=str(user.user_id) if user else None
        )
        
        return market_stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get betting market stats", market_id=str(market_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting market statistics"
        )


@router.get("/betting-markets", response_model=BettingMarketListResponse)
async def get_all_betting_markets(
    status_filter: Optional[str] = Query(None, description="Filter by market status"),
    market_type: Optional[str] = Query(None, description="Filter by market type"),
    session_id: Optional[UUID] = Query(None, description="Filter by session ID"),
    tournament_id: Optional[UUID] = Query(None, description="Filter by tournament ID"),
    pagination: PaginationParams = Depends(get_pagination),
    db: AsyncSession = Depends(get_read_database_session),
    user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get all betting markets
    
    Returns a paginated list of betting markets with optional filtering.
    """
    try:
        # Build base query
        query = select(BettingMarket).options(
            selectinload(BettingMarket.outcomes)
        ).where(
            BettingMarket.status.in_(["active", "suspended", "closed"])
        )
        
        # Apply filters
        if status_filter:
            query = query.where(BettingMarket.status == status_filter)
        
        if market_type:
            query = query.where(BettingMarket.market_type == market_type)
        
        if session_id:
            query = query.where(BettingMarket.session_id == session_id)
        
        if tournament_id:
            query = query.where(BettingMarket.tournament_id == tournament_id)
        
        # Get total count
        count_query = select(func.count(BettingMarket.id)).select_from(query.subquery())
        count_result = await db.execute(count_query)
        total_count = count_result.scalar()
        
        # Apply ordering and pagination
        query = query.order_by(BettingMarket.created_at.desc()).offset(pagination.offset).limit(pagination.limit)
        
        result = await db.execute(query)
        markets = result.scalars().all()
        
        logger.info(
            "Retrieved betting markets",
            total_count=total_count,
            returned_count=len(markets),
            status_filter=status_filter,
            market_type=market_type,
            user_id=str(user.user_id) if user else None
        )
        
        return BettingMarketListResponse(
            markets=[BettingMarketResponse.model_validate(market) for market in markets],
            total_count=total_count,
            page=pagination.page,
            limit=pagination.limit,
            has_next=pagination.offset + len(markets) < total_count,
            has_prev=pagination.offset > 0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get betting markets", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting markets"
        )