"""
BetBet Gaming Engine - Spectator Betting API
============================================

API endpoints for spectator betting functionality and odds management.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

# Import SQLAlchemy models
from app.database.models import SpectatorBet

# Import Pydantic models for validation
from app.models.spectator_bets import (
    SpectatorBetCreate, SpectatorBetResponse,
    BettingMarket, OddsCalculation, LiveOdds, BettingStats
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_admin_user, get_optional_user,
    get_pagination, PaginationParams,
    NotFoundError, ValidationError, PermissionError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/spectator-bets", response_model=List[SpectatorBetResponse])
async def list_bets(
    pagination: PaginationParams = Depends(get_pagination),
    session_id: Optional[UUID] = None,
    user_id: Optional[UUID] = None,
    bet_type: Optional[str] = None,
    status: Optional[str] = None,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    List spectator bets with filtering and pagination
    
    Returns a list of spectator bets matching the specified filters.
    """
    try:
        from sqlalchemy import select, and_
        
        # Build base query
        query = select(SpectatorBet)
        
        # Apply filters
        filters = []
        
        if session_id:
            filters.append(SpectatorBet.session_id == session_id)
            
        if user_id:
            filters.append(SpectatorBet.user_id == user_id)
            
        if bet_type:
            filters.append(SpectatorBet.bet_type == bet_type)
            
        if status:
            filters.append(SpectatorBet.status == status)
            
        if filters:
            query = query.where(and_(*filters))
        
        # Apply sorting
        if sort_order.lower() == "desc":
            sort_column = getattr(SpectatorBet, sort_by).desc()
        else:
            sort_column = getattr(SpectatorBet, sort_by).asc()
        query = query.order_by(sort_column)
        
        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await db.execute(query)
        bets = result.scalars().all()
        
        # Convert to response models
        bet_responses = [SpectatorBetResponse.model_validate(bet) for bet in bets]
        
        logger.info(
            "Listed spectator bets",
            count=len(bet_responses),
            session_id=session_id,
            user_id=user_id,
            user_id_current=current_user.user_id if current_user else None
        )
        
        return bet_responses
        
    except Exception as e:
        logger.error("Failed to list spectator bets", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve spectator bets"
        )


@router.get("/spectator-bets/{bet_id}")
async def get_bet(bet_id: UUID):
    """Get a specific bet by ID"""
    # TODO: Implement bet retrieval
    return {"message": f"Get bet {bet_id} - TODO"}


@router.post("/spectator-bets", response_model=SpectatorBetResponse, status_code=status.HTTP_201_CREATED)
async def place_bet(
    bet_data: SpectatorBetCreate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Place a new spectator bet
    
    Creates a new spectator bet for the authenticated user on the specified
    session. Validates betting conditions and calculates potential payouts.
    """
    try:
        from sqlalchemy import select, and_
        from app.database.models import GameSession
        from decimal import Decimal
        
        # Verify session exists and allows spectator betting
        session_query = select(GameSession).where(
            and_(GameSession.id == bet_data.session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(bet_data.session_id))
            
        if not session.allows_spectator_betting:
            raise ValidationError("Spectator betting is not enabled for this session")
            
        if session.state not in ["waiting", "starting", "active"]:
            raise ValidationError("Cannot place bets on completed or cancelled sessions")
        
        # Validate bet amount
        if bet_data.stake_amount <= 0:
            raise ValidationError("Bet amount must be positive")
            
        if bet_data.stake_amount < Decimal('1.00'):
            raise ValidationError("Minimum bet amount is $1.00")
            
        if bet_data.stake_amount > Decimal('1000.00'):
            raise ValidationError("Maximum bet amount is $1000.00")
        
        # Calculate odds and potential payout
        # For now, use simple 2:1 odds calculation
        # TODO: Implement proper odds calculation based on market data
        odds = Decimal('2.0')  # Simple 2:1 odds
        potential_payout = bet_data.stake_amount * odds
        
        # Create bet record
        bet = SpectatorBet(
            session_id=bet_data.session_id,
            user_id=current_user.user_id,
            bet_type=bet_data.bet_type,
            bet_data=bet_data.bet_data,
            stake_amount=bet_data.stake_amount,
            potential_payout=potential_payout,
            actual_payout=0,
            odds_at_placement=odds,
            market_data={
                "session_state": session.state,
                "participants": session.current_participants,
                "created_at": datetime.utcnow().isoformat()
            },
            status="active",
            placed_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(bet)
        await db.commit()
        await db.refresh(bet)
        
        logger.info(
            "Spectator bet placed",
            bet_id=bet.id,
            session_id=bet_data.session_id,
            user_id=current_user.user_id,
            bet_type=bet_data.bet_type,
            stake_amount=float(bet_data.stake_amount),
            potential_payout=float(potential_payout)
        )
        
        return SpectatorBetResponse.model_validate(bet)
        
    except (NotFoundError, ValidationError) as e:
        raise HTTPException(
            status_code=e.status_code if hasattr(e, 'status_code') else status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to place spectator bet", error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to place bet"
        )


@router.get("/sessions/{session_id}/betting-markets", response_model=List[BettingMarket])
async def get_session_betting_markets(
    session_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get available betting markets for a session
    
    Returns all available betting markets and options for the specified session.
    Markets include winner bets, over/under, and performance propositions.
    """
    try:
        from sqlalchemy import select, and_
        from app.database.models import GameSession, SessionParticipant
        from decimal import Decimal
        
        # Verify session exists and allows betting
        session_query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
            
        if not session.allows_spectator_betting:
            return []  # No betting markets available
            
        # Get session participants
        participants_query = select(SessionParticipant).where(
            and_(
                SessionParticipant.session_id == session_id,
                SessionParticipant.is_spectator == False
            )
        ).order_by(SessionParticipant.join_order)
        
        participants_result = await db.execute(participants_query)
        participants = participants_result.scalars().all()
        
        markets = []
        
        # Winner Market
        if len(participants) >= 2:
            winner_options = []
            for participant in participants:
                winner_options.append({
                    "id": f"winner_{participant.user_id}",
                    "name": f"User {str(participant.user_id)[:8]} Wins",
                    "odds": 2.0 + (0.5 * participant.join_order),  # Simple odds based on join order
                    "available": True
                })
            
            markets.append(BettingMarket(
                market_id=f"session_{session_id}_winner",
                market_type="session_winner",
                market_name="Session Winner",
                description="Bet on who will win this session",
                is_active=session.state in ["waiting", "starting", "active"],
                options=winner_options,
                min_stake=Decimal('1.00'),
                max_stake=Decimal('1000.00'),
                market_data={
                    "participants_count": len(participants),
                    "session_state": session.state
                }
            ))
        
        # Score Over/Under Market (if applicable)
        if session.state in ["waiting", "starting"] and len(participants) >= 2:
            markets.append(BettingMarket(
                market_id=f"session_{session_id}_score_over_under",
                market_type="score_over_under",
                market_name="Total Score Over/Under",
                description="Bet on whether total session score will be over or under the line",
                is_active=True,
                options=[
                    {
                        "id": "over_100",
                        "name": "Over 100 Points",
                        "odds": 1.9,
                        "available": True
                    },
                    {
                        "id": "under_100", 
                        "name": "Under 100 Points",
                        "odds": 1.9,
                        "available": True
                    }
                ],
                min_stake=Decimal('1.00'),
                max_stake=Decimal('500.00'),
                market_data={
                    "line": 100,
                    "estimated_avg_score": 85
                }
            ))
        
        # First to Score Market (if session hasn't started)
        if session.state in ["waiting", "starting"] and len(participants) >= 2:
            first_score_options = []
            for participant in participants:
                first_score_options.append({
                    "id": f"first_score_{participant.user_id}",
                    "name": f"User {str(participant.user_id)[:8]} Scores First",
                    "odds": 2.0 + (len(participants) * 0.1),
                    "available": True
                })
            
            markets.append(BettingMarket(
                market_id=f"session_{session_id}_first_score",
                market_type="first_to_score",
                market_name="First to Score",
                description="Bet on who will score first in this session",
                is_active=session.state in ["waiting", "starting"],
                options=first_score_options,
                min_stake=Decimal('1.00'),
                max_stake=Decimal('250.00'),
                market_data={
                    "participants_count": len(participants)
                }
            ))
        
        logger.info(
            "Retrieved betting markets for session",
            session_id=session_id,
            markets_count=len(markets),
            session_state=session.state,
            user_id=current_user.user_id if current_user else None
        )
        
        return markets
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session not found (ID: {session_id})"
        )
    except Exception as e:
        logger.error("Failed to get betting markets", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve betting markets"
        )


@router.get("/sessions/{session_id}/live-odds", response_model=LiveOdds)
async def get_live_odds(
    session_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get live odds for a session
    
    Returns real-time odds updates for all available betting markets in the session.
    Updates dynamically based on current game state and betting volume.
    """
    try:
        from sqlalchemy import select, and_, func
        from app.database.models import GameSession, SessionParticipant
        from decimal import Decimal
        
        # Verify session exists and is active
        session_query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
            
        if not session.allows_spectator_betting:
            raise ValidationError("Spectator betting is not enabled for this session")
        
        # Get current betting volume
        bet_volume_query = select(
            func.coalesce(func.sum(SpectatorBet.stake_amount), 0).label("total_volume"),
            func.count(SpectatorBet.id).label("bet_count")
        ).where(
            and_(
                SpectatorBet.session_id == session_id,
                SpectatorBet.status == "active"
            )
        )
        volume_result = await db.execute(bet_volume_query)
        volume_data = volume_result.first()
        
        # Get participants for odds calculation
        participants_query = select(SessionParticipant).where(
            and_(
                SessionParticipant.session_id == session_id,
                SessionParticipant.is_spectator == False
            )
        ).order_by(SessionParticipant.join_order)
        
        participants_result = await db.execute(participants_query)
        participants = participants_result.scalars().all()
        
        # Calculate dynamic odds based on session state and betting volume
        base_odds = {}
        volume_impact = min(float(volume_data.total_volume or 0) / 1000, 0.5)  # Max 50% impact
        
        # Winner odds - adjust based on current scores and betting volume
        for i, participant in enumerate(participants):
            participant_id = str(participant.user_id)
            
            # Base odds favor participants who joined earlier (lower join_order)
            base_odd = 2.0 + (0.3 * participant.join_order)
            
            # Adjust for current performance (mock data for now)
            performance_modifier = 1.0
            if session.state == "active":
                # In real implementation, would use actual game scores
                mock_score_modifier = 0.9 if participant.join_order == 0 else 1.1
                performance_modifier = mock_score_modifier
            
            # Apply volume impact
            final_odd = base_odd * performance_modifier * (1 + volume_impact)
            base_odds[f"winner_{participant_id}"] = round(final_odd, 2)
        
        # Over/under odds
        over_under_base = 1.9
        over_under_modifier = 1 + (volume_impact * 0.1)
        base_odds["over_100"] = round(over_under_base * over_under_modifier, 2)
        base_odds["under_100"] = round(over_under_base / over_under_modifier, 2)
        
        # Calculate odds movement (simulate for demo)
        previous_odds = {k: v * 0.95 for k, v in base_odds.items()}  # Simulate 5% increase
        odds_movement = {k: round(base_odds[k] - previous_odds[k], 2) for k in base_odds}
        
        # Determine if there's significant change
        significant_change = any(abs(movement) > 0.1 for movement in odds_movement.values())
        
        # Calculate time to deadline
        time_to_deadline = None
        if session.state in ["waiting", "starting"]:
            # Mock deadline - 5 minutes before session starts
            time_to_deadline = 300  # 5 minutes in seconds
        
        # Determine volume trend
        volume_trend = "stable"
        if volume_data.total_volume > 500:
            volume_trend = "increasing"
        elif volume_data.total_volume < 100:
            volume_trend = "decreasing"
        
        live_odds = LiveOdds(
            market_id=f"session_{session_id}_live",
            event_id=session_id,
            current_odds=base_odds,
            previous_odds=previous_odds,
            odds_movement=odds_movement,
            significant_change=significant_change,
            market_open=session.state in ["waiting", "starting", "active"],
            time_to_deadline=time_to_deadline,
            recent_bet_volume=volume_data.total_volume or Decimal('0'),
            volume_trend=volume_trend,
            update_timestamp=datetime.utcnow(),
            update_sequence=1,
            update_reason="live_calculation"
        )
        
        logger.info(
            "Generated live odds for session",
            session_id=session_id,
            odds_count=len(base_odds),
            total_volume=float(volume_data.total_volume or 0),
            significant_change=significant_change,
            user_id=current_user.user_id if current_user else None
        )
        
        return live_odds
        
    except (NotFoundError, ValidationError) as e:
        raise HTTPException(
            status_code=e.status_code if hasattr(e, 'status_code') else status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to get live odds", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve live odds"
        )


@router.get("/tournaments/{tournament_id}/betting-markets", response_model=List[BettingMarket])
async def get_tournament_betting_markets(
    tournament_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get available betting markets for a tournament
    
    Returns all available betting markets for the tournament including
    winner markets, bracket predictions, and performance propositions.
    """
    try:
        from sqlalchemy import select, and_
        from app.database.models import Tournament, TournamentParticipant
        from decimal import Decimal
        
        # Verify tournament exists
        tournament_query = select(Tournament).where(
            and_(Tournament.id == tournament_id, Tournament.deleted_at.is_(None))
        )
        tournament_result = await db.execute(tournament_query)
        tournament = tournament_result.scalar_one_or_none()
        
        if not tournament:
            raise NotFoundError("Tournament", str(tournament_id))
        
        # Get tournament participants
        participants_query = select(TournamentParticipant).where(
            TournamentParticipant.tournament_id == tournament_id
        ).order_by(TournamentParticipant.seed_number)
        
        participants_result = await db.execute(participants_query)
        participants = participants_result.scalars().all()
        
        markets = []
        
        # Tournament Winner Market
        if len(participants) >= 4 and tournament.status in ["registration_open", "registration_closed", "starting", "in_progress"]:
            winner_options = []
            for participant in participants[:16]:  # Limit to top 16 for display
                # Odds based on seed number (lower seed = better odds)
                base_odds = 2.0 + (participant.seed_number or 1) * 0.5
                
                # Adjust for tournament format
                if tournament.format == "single_elimination":
                    format_modifier = 1.2
                elif tournament.format == "double_elimination":
                    format_modifier = 1.0
                else:
                    format_modifier = 1.1
                
                final_odds = base_odds * format_modifier
                
                winner_options.append({
                    "id": f"tournament_winner_{participant.user_id}",
                    "name": f"User {str(participant.user_id)[:8]} Wins Tournament",
                    "odds": round(final_odds, 2),
                    "available": participant.status in ["registered", "confirmed", "active"]
                })
            
            markets.append(BettingMarket(
                market_id=f"tournament_{tournament_id}_winner",
                market_type="tournament_winner",
                market_name="Tournament Winner",
                description="Bet on who will win the entire tournament",
                is_active=tournament.status in ["registration_open", "registration_closed", "starting", "in_progress"],
                options=winner_options,
                min_stake=Decimal('5.00'),  # Higher minimum for tournament bets
                max_stake=Decimal('2000.00'),
                market_data={
                    "tournament_format": tournament.format,
                    "participants_count": len(participants),
                    "current_round": tournament.current_round,
                    "prize_pool": float(tournament.prize_pool)
                }
            ))
        
        # Top 4 Finish Market
        if len(participants) >= 8 and tournament.status in ["registration_open", "registration_closed", "starting"]:
            top4_options = []
            for participant in participants[:8]:  # Top seeded players
                odds = 1.5 + (participant.seed_number or 1) * 0.2
                top4_options.append({
                    "id": f"top4_{participant.user_id}",
                    "name": f"User {str(participant.user_id)[:8]} Reaches Top 4",
                    "odds": round(odds, 2),
                    "available": True
                })
            
            markets.append(BettingMarket(
                market_id=f"tournament_{tournament_id}_top4",
                market_type="placement_bet",
                market_name="Top 4 Finish",
                description="Bet on players to reach the top 4",
                is_active=tournament.status in ["registration_open", "registration_closed", "starting"],
                options=top4_options,
                min_stake=Decimal('2.00'),
                max_stake=Decimal('500.00'),
                market_data={
                    "placement_threshold": 4,
                    "participants_count": len(participants)
                }
            ))
        
        # Total Rounds Market (for elimination tournaments)
        if tournament.format in ["single_elimination", "double_elimination"] and tournament.status in ["registration_open", "registration_closed", "starting"]:
            import math
            expected_rounds = math.ceil(math.log2(len(participants))) if len(participants) > 1 else 1
            
            markets.append(BettingMarket(
                market_id=f"tournament_{tournament_id}_total_rounds",
                market_type="tournament_duration",
                market_name="Total Tournament Rounds",
                description="Bet on how many rounds the tournament will have",
                is_active=tournament.status in ["registration_open", "registration_closed", "starting"],
                options=[
                    {
                        "id": f"rounds_under_{expected_rounds}",
                        "name": f"Under {expected_rounds} Rounds",
                        "odds": 2.1,
                        "available": True
                    },
                    {
                        "id": f"rounds_exact_{expected_rounds}",
                        "name": f"Exactly {expected_rounds} Rounds",
                        "odds": 1.8,
                        "available": True
                    },
                    {
                        "id": f"rounds_over_{expected_rounds}",
                        "name": f"Over {expected_rounds} Rounds",
                        "odds": 2.5,
                        "available": True
                    }
                ],
                min_stake=Decimal('1.00'),
                max_stake=Decimal('250.00'),
                market_data={
                    "expected_rounds": expected_rounds,
                    "tournament_format": tournament.format
                }
            ))
        
        # Upset Special Market (lower seeds beating higher seeds)
        if tournament.status in ["starting", "in_progress"] and tournament.current_round > 0:
            markets.append(BettingMarket(
                market_id=f"tournament_{tournament_id}_upset_special",
                market_type="special_proposition",
                market_name="Major Upset Occurs",
                description="Bet on whether a player seeded 8+ will beat a player seeded 1-4",
                is_active=tournament.current_round <= 2,  # Only for early rounds
                options=[
                    {
                        "id": "upset_yes",
                        "name": "Major Upset Will Occur",
                        "odds": 3.5,
                        "available": True
                    },
                    {
                        "id": "upset_no",
                        "name": "No Major Upset",
                        "odds": 1.3,
                        "available": True
                    }
                ],
                min_stake=Decimal('1.00'),
                max_stake=Decimal('100.00'),
                market_data={
                    "current_round": tournament.current_round,
                    "definition": "Seed 8+ beats Seed 1-4"
                }
            ))
        
        logger.info(
            "Retrieved tournament betting markets",
            tournament_id=tournament_id,
            markets_count=len(markets),
            tournament_status=tournament.status,
            participants_count=len(participants),
            user_id=current_user.user_id if current_user else None
        )
        
        return markets
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tournament not found (ID: {tournament_id})"
        )
    except Exception as e:
        logger.error("Failed to get tournament betting markets", tournament_id=tournament_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tournament betting markets"
        )