"""
BetBet Gaming Engine - Chess API Endpoints
==========================================

FastAPI endpoints for chess game management and gameplay.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from uuid import UUID
import structlog

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse

from ...models.chess import (
    ChessGameCreate, ChessGameJoin, ChessMoveRequest, ChessGameAction,
    ChessGameResponse, ChessGameState, ChessSpectatorBet, ChessGameStats,
    ChessRatingHistory, ChessLeaderboard, ChessSearchFilters
)
from ...services.chess_service import chess_service
from ...auth.clerk_auth import get_current_user
from ..dependencies import get_database_session

# Import wallet client
import sys
from pathlib import Path
services_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(services_dir))
from shared.core.financial.wallet_client import WalletClient, InsufficientFunds<PERSON>rror, FeeCategory, FeeType

router = APIRouter(prefix="/chess", tags=["chess"])
logger = structlog.get_logger()


@router.get("/test")
async def test_chess_endpoint():
    """Test endpoint to verify chess router is working"""
    return {
        "status": "ok",
        "message": "Chess router is working",
        "games": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None,
                "status": "waiting",
                "variant": "standard",
                "time_control": "blitz",
                "initial_time_seconds": 300,
                "increment_seconds": 3,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "created_at": "2024-01-15T10:30:00Z",
                "description": "Test chess game for WebSocket testing"
            }
        ],
        "total": 1
    }


@router.get("/testgame")
async def get_test_chess_game():
    """Get the test chess game - public endpoint"""
    return {
        "success": True,
        "game": {
            "id": "550e8400-e29b-41d4-a716-************",
            "variant": "standard",
            "time_control": "blitz",
            "initial_time_seconds": 300,
            "increment_seconds": 3,
            "is_rated": True,
            "is_private": False,
            "allow_spectators": True,
            "wager_amount": 0,
            "status": "waiting",
            "created_at": "2024-01-15T10:30:00Z",
            "description": "Test chess game for WebSocket testing",
            "current_fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "move_count": 0,
            "white_time_remaining": 300,
            "black_time_remaining": 300,
            "white_player": {
                "id": "550e8400-e29b-41d4-a716-************",
                "username": "ChessMaster2024",
                "rating": 1650
            },
            "black_player": None
        }
    }


@router.get("/public-games")
async def get_public_chess_games():
    """Public chess games list - no authentication required"""
    return {
        "games": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None,
                "status": "waiting",
                "variant": "standard",
                "time_control": "blitz",
                "initial_time_seconds": 300,
                "increment_seconds": 3,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "created_at": "2024-01-15T10:30:00Z",
                "description": "Test chess game for WebSocket testing"
            }
        ],
        "total": 1,
        "limit": 20,
        "offset": 0
    }


@router.get("/public-game/{game_id}")
async def get_public_chess_game(game_id: str):
    """Get a specific chess game by ID - no authentication required"""
    if game_id == "550e8400-e29b-41d4-a716-************":
        return {
            "success": True,
            "game": {
                "id": "550e8400-e29b-41d4-a716-************",
                "variant": "standard",
                "time_control": "blitz",
                "initial_time_seconds": 300,
                "increment_seconds": 3,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "status": "waiting",
                "created_at": "2024-01-15T10:30:00Z",
                "description": "Test chess game for WebSocket testing",
                "current_fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
                "move_count": 0,
                "white_time_remaining": 300,
                "black_time_remaining": 300,
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None
            }
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chess game not found"
        )


@router.get("/games-test")
async def test_chess_games_endpoint():
    """Test endpoint to verify chess games endpoint is working without auth"""
    return {
        "status": "ok",
        "message": "Chess games endpoint is working",
        "games": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "status": "waiting",
                "variant": "standard",
                "time_control": "blitz",
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "description": "Test chess game for WebSocket testing",
                "initial_time_seconds": 300,
                "increment_seconds": 3
            }
        ],
        "total": 1,
        "limit": 20,
        "offset": 0
    }


# Remove this endpoint - use the main /games endpoint instead


@router.post("/games", response_model=Dict[str, Any])
async def create_chess_game(
    game_data: Dict[str, Any],  # Accept flexible input format
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Create a new chess game with wallet integration"""
    try:
        user_id = current_user["id"]

        # Convert frontend format to backend format
        if "config" not in game_data:
            # Frontend sends flat structure, convert to nested
            time_control_map = {
                "bullet": {"time": 60, "increment": 0},
                "blitz": {"time": 300, "increment": 3},
                "rapid": {"time": 900, "increment": 10},
                "classical": {"time": 1800, "increment": 30}
            }

            time_control = game_data.get("time_control", "blitz")
            time_config = time_control_map.get(time_control, time_control_map["blitz"])

            # Create proper ChessGameCreate structure
            from ...models.chess import ChessGameCreate, ChessGameConfig
            from decimal import Decimal

            config = ChessGameConfig(
                variant=game_data.get("variant", "standard"),
                time_control=time_control,
                initial_time_seconds=time_config["time"],
                increment_seconds=time_config["increment"],
                rated=game_data.get("is_rated", True),
                allow_spectators=game_data.get("allow_spectators", True),
                allow_spectator_betting=game_data.get("allow_spectator_betting", True)
            )

            chess_game_data = ChessGameCreate(
                opponent_id=game_data.get("opponent_id"),
                config=config,
                stake_amount=Decimal(str(game_data.get("wager_amount", 0))),
                is_private=game_data.get("is_private", False),
                password=game_data.get("password")
            )
        else:
            # Backend format already
            chess_game_data = ChessGameCreate(**game_data)

        # Initialize wallet client
        async with WalletClient(service_name="gaming-engine") as wallet:

            # If there's a stake amount, handle wallet operations
            if chess_game_data.stake_amount and chess_game_data.stake_amount > 0:
                
                # Check if user has sufficient balance
                balance = await wallet.get_balance(user_id)
                if balance.available_balance < chess_game_data.stake_amount:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Insufficient funds. Available: ${balance.available_balance}, Required: ${chess_game_data.stake_amount}"
                    )

                # Create the chess game first to get game_id
                game_id = await chess_service.create_game(user_id, chess_game_data)

                # Lock funds for the wager
                lock_result = await wallet.create_game_wager(
                    user_id=user_id,
                    wager_amount=chess_game_data.stake_amount,
                    game_id=str(game_id),
                    game_type="chess"
                )

                if not lock_result.success:
                    # If wallet operation fails, clean up the game
                    await chess_service.cleanup_game(game_id)
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to lock funds: {lock_result.error_message}"
                    )

                return {
                    "success": True,
                    "id": str(game_id),  # Frontend expects 'id' field
                    "game_id": str(game_id),
                    "stake_amount": float(chess_game_data.stake_amount),
                    "funds_locked": True,
                    "new_balance": float(lock_result.new_balance),
                    "message": f"Chess game created with ${chess_game_data.stake_amount} wager"
                }

            else:
                # No stake - just create the game
                game_id = await chess_service.create_game(user_id, chess_game_data)

                return {
                    "success": True,
                    "id": str(game_id),  # Frontend expects 'id' field
                    "game_id": str(game_id),
                    "stake_amount": 0,
                    "funds_locked": False,
                    "message": "Chess game created successfully"
                }
                
    except InsufficientFundsError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create chess game: {str(e)}"
        )


@router.post("/games/{game_id}/join", response_model=Dict[str, Any])
async def join_chess_game(
    game_id: UUID,
    join_data: ChessGameJoin,
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Join an existing chess game with wallet integration"""
    try:
        user_id = current_user["id"]
        
        # TODO: Get game details from database to check stake amount
        # For now, we'll assume this is retrieved from the game data
        game_stake_amount = Decimal('50.00')  # This should come from game data
        
        async with WalletClient(service_name="gaming-engine") as wallet:
            
            # If game has a stake, require matching funds
            if game_stake_amount > 0:
                
                # Check if user has sufficient balance
                balance = await wallet.get_balance(user_id)
                if balance.available_balance < game_stake_amount:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Insufficient funds to join. Available: ${balance.available_balance}, Required: ${game_stake_amount}"
                    )
                
                # Lock funds for the wager
                lock_result = await wallet.create_game_wager(
                    user_id=user_id,
                    wager_amount=game_stake_amount,
                    game_id=str(game_id),
                    game_type="chess"
                )
                
                if not lock_result.success:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to lock funds: {lock_result.error_message}"
                    )
                
                # TODO: Add player to game session in database
                assigned_color = "black"  # This would be determined by game logic
                
                return {
                    "success": True,
                    "message": f"Successfully joined chess game with ${game_stake_amount} wager",
                    "color": assigned_color,
                    "stake_amount": float(game_stake_amount),
                    "funds_locked": True,
                    "new_balance": float(lock_result.new_balance)
                }
            
            else:
                # No stake required - just join
                assigned_color = "black"  # This would be determined by game logic
                
                return {
                    "success": True,
                    "message": "Successfully joined chess game",
                    "color": assigned_color,
                    "stake_amount": 0,
                    "funds_locked": False
                }
                
    except InsufficientFundsError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to join chess game: {str(e)}"
        )


@router.post("/games/{game_id}/moves", response_model=Dict[str, Any])
async def make_move(
    game_id: UUID,
    move_request: ChessMoveRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Make a move in a chess game"""
    try:
        # Validate it's the player's turn
        success, message, chess_move = await chess_service.make_move(
            game_id, current_user["id"], move_request
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # Store move in database
        # Update game state
        # Notify spectators via WebSocket
        background_tasks.add_task(notify_game_update, game_id, chess_move)
        
        # Check for time forfeit
        is_timeout, timeout_result = await chess_service.check_time_forfeit(game_id)
        if is_timeout:
            background_tasks.add_task(handle_game_end, game_id, timeout_result)
        
        return {
            "success": True,
            "move": chess_move.dict() if chess_move else None,
            "message": message
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to make move: {str(e)}"
        )


@router.get("/games/{game_id}/state", response_model=ChessGameState)
async def get_game_state(
    game_id: UUID,
    current_user=Depends(get_current_user)
):
    """Get current chess game state"""
    try:
        game_state = await chess_service.get_game_state(game_id)
        if not game_state:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chess game not found"
            )
        
        return game_state
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get game state: {str(e)}"
        )


@router.get("/games/{game_id}/legal-moves", response_model=List[str])
async def get_legal_moves(
    game_id: UUID,
    current_user=Depends(get_current_user)
):
    """Get all legal moves for current position"""
    try:
        legal_moves = await chess_service.get_legal_moves(game_id)
        return legal_moves
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get legal moves: {str(e)}"
        )


@router.post("/games/{game_id}/actions", response_model=Dict[str, Any])
async def game_action(
    game_id: UUID,
    action: ChessGameAction,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Perform a game action (resign, offer draw, etc.)"""
    try:
        success = False
        message = ""
        
        if action.action == "resign":
            success, message = await chess_service.resign_game(game_id, current_user["id"])
            if success:
                background_tasks.add_task(handle_game_end, game_id, "resignation")
        
        elif action.action == "offer_draw":
            success, message = await chess_service.offer_draw(game_id, current_user["id"])
            if success:
                background_tasks.add_task(notify_draw_offer, game_id, current_user["id"])
        
        elif action.action == "accept_draw":
            success, message = await chess_service.accept_draw(game_id, current_user["id"])
            if success:
                background_tasks.add_task(handle_game_end, game_id, "draw_agreement")
        
        elif action.action == "decline_draw":
            # Handle draw decline
            success, message = True, "Draw offer declined"
            background_tasks.add_task(notify_draw_decline, game_id, current_user["id"])
        
        elif action.action == "claim_draw":
            claim_type = action.reason or "threefold_repetition"
            success, message = await chess_service.claim_draw(game_id, current_user["id"], claim_type)
            if success:
                background_tasks.add_task(handle_game_end, game_id, claim_type)
        
        elif action.action == "abort":
            # Handle game abort (only in first few moves)
            success, message = True, "Game aborted"
            background_tasks.add_task(handle_game_end, game_id, "aborted")
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        return {
            "success": True,
            "action": action.action,
            "message": message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform action: {str(e)}"
        )


@router.get("/games/{game_id}/analysis", response_model=Dict[str, Any])
async def analyze_position(
    game_id: UUID,
    depth: int = 15,
    current_user=Depends(get_current_user)
):
    """Get engine analysis of current position"""
    try:
        analysis = await chess_service.analyze_position(game_id, depth)
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Game not found or analysis unavailable"
            )
        
        return analysis
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze position: {str(e)}"
        )


@router.get("/games/{game_id}/pgn", response_model=Dict[str, str])
async def get_game_pgn(
    game_id: UUID,
    current_user=Depends(get_current_user)
):
    """Get PGN representation of the game"""
    try:
        pgn = await chess_service.get_pgn(game_id)
        if not pgn:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Game not found"
            )
        
        return {"pgn": pgn}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get PGN: {str(e)}"
        )


@router.post("/games/{game_id}/spectate", response_model=Dict[str, Any])
async def join_as_spectator(
    game_id: UUID,
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Join a chess game as spectator"""
    try:
        # Add user to spectators list
        # Return current game state for spectator
        
        game_state = await chess_service.get_game_state(game_id)
        if not game_state:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Game not found"
            )
        
        return {
            "success": True,
            "message": "Joined as spectator",
            "game_state": game_state.dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to join as spectator: {str(e)}"
        )


@router.post("/games/{game_id}/bets", response_model=Dict[str, Any])
async def place_spectator_bet(
    game_id: UUID,
    bet_data: ChessSpectatorBet,
    current_user=Depends(get_current_user),
    db=Depends(get_database_session)
):
    """Place a spectator bet on a chess game"""
    try:
        # Validate game is active and accepting bets
        # Create bet record
        # Update user balance
        
        return {
            "success": True,
            "bet_id": "uuid_here",
            "message": "Bet placed successfully"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to place bet: {str(e)}"
        )


@router.get("/games/public", response_model=Dict[str, Any])
async def list_chess_games_public(
    limit: int = 20,
    offset: int = 0
):
    """Public chess games list - no authentication required"""
    # Return the real game from database as mock data for now
    return {
        "games": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None,
                "status": "waiting",
                "variant": "standard",
                "time_control": "blitz",
                "initial_time_seconds": 300,
                "increment_seconds": 3,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "created_at": "2024-01-15T10:30:00Z",
                "description": "Test chess game for WebSocket testing"
            }
        ],
        "total": 1,
        "limit": limit,
        "offset": offset
    }


@router.get("/games", response_model=Dict[str, Any])
async def list_chess_games(
    filters: ChessSearchFilters = Depends(),
    limit: int = 20,
    offset: int = 0,
    # current_user=Depends(get_current_user),  # Temporarily disable auth for testing
    db=Depends(get_database_session)
):
    """List available chess games with filters"""
    try:
        # Query actual chess games from database
        from sqlalchemy import text

        # Get chess games from database
        query = text("""
            SELECT
                cg.id,
                cg.variant,
                cg.time_control,
                cg.initial_time_seconds,
                cg.increment_seconds,
                cg.is_rated,
                cg.is_private,
                cg.allow_spectators,
                cg.wager_amount,
                cg.status,
                cg.created_at,
                cg.description,
                wp.id as white_player_id,
                wp.username as white_username,
                wp.rating as white_rating,
                bp.id as black_player_id,
                bp.username as black_username,
                bp.rating as black_rating
            FROM chess_engine.chess_games cg
            LEFT JOIN chess_engine.users wp ON cg.white_player_id = wp.id
            LEFT JOIN chess_engine.users bp ON cg.black_player_id = bp.id
            WHERE cg.status IN ('waiting', 'active')
            ORDER BY cg.created_at DESC
            LIMIT :limit OFFSET :offset
        """)

        result = await db.execute(query, {"limit": limit, "offset": offset})
        rows = result.fetchall()

        games = []
        for row in rows:
            game_data = {
                "id": str(row.id),
                "variant": row.variant,
                "time_control": row.time_control,
                "initial_time_seconds": row.initial_time_seconds,
                "increment_seconds": row.increment_seconds,
                "is_rated": row.is_rated,
                "is_private": row.is_private,
                "allow_spectators": row.allow_spectators,
                "wager_amount": float(row.wager_amount) if row.wager_amount else 0,
                "status": row.status,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "description": row.description,
                "white_player": {
                    "id": str(row.white_player_id),
                    "username": row.white_username,
                    "rating": row.white_rating
                } if row.white_player_id else None,
                "black_player": {
                    "id": str(row.black_player_id),
                    "username": row.black_username,
                    "rating": row.black_rating
                } if row.black_player_id else None
            }
            games.append(game_data)

        # Get total count
        count_query = text("""
            SELECT COUNT(*) as total
            FROM chess_engine.chess_games
            WHERE status IN ('waiting', 'active')
        """)
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        return {
            "games": games,
            "total": total,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list games: {str(e)}"
        )


@router.get("/games/{game_id}", response_model=Dict[str, Any])
async def get_chess_game_by_id(
    game_id: str,
    # current_user=Depends(get_current_user),  # Temporarily disable auth for testing
    db=Depends(get_database_session)
):
    """Get a specific chess game by ID"""
    try:
        from sqlalchemy import text

        # Query specific chess game from database
        query = text("""
            SELECT
                cg.id,
                cg.variant,
                cg.time_control,
                cg.initial_time_seconds,
                cg.increment_seconds,
                cg.is_rated,
                cg.is_private,
                cg.allow_spectators,
                cg.wager_amount,
                cg.status,
                cg.created_at,
                cg.description,
                cg.current_fen,
                cg.move_count,
                cg.white_time_remaining,
                cg.black_time_remaining,
                wp.id as white_player_id,
                wp.username as white_username,
                wp.rating as white_rating,
                bp.id as black_player_id,
                bp.username as black_username,
                bp.rating as black_rating
            FROM chess_engine.chess_games cg
            LEFT JOIN chess_engine.users wp ON cg.white_player_id = wp.id
            LEFT JOIN chess_engine.users bp ON cg.black_player_id = bp.id
            WHERE cg.id = :game_id
        """)

        result = await db.execute(query, {"game_id": game_id})
        row = result.fetchone()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chess game not found"
            )

        game_data = {
            "id": str(row.id),
            "variant": row.variant,
            "time_control": row.time_control,
            "initial_time_seconds": row.initial_time_seconds,
            "increment_seconds": row.increment_seconds,
            "is_rated": row.is_rated,
            "is_private": row.is_private,
            "allow_spectators": row.allow_spectators,
            "wager_amount": float(row.wager_amount) if row.wager_amount else 0,
            "status": row.status,
            "created_at": row.created_at.isoformat() if row.created_at else None,
            "description": row.description,
            "current_fen": row.current_fen,
            "move_count": row.move_count,
            "white_time_remaining": row.white_time_remaining,
            "black_time_remaining": row.black_time_remaining,
            "white_player": {
                "id": str(row.white_player_id),
                "username": row.white_username,
                "rating": row.white_rating
            } if row.white_player_id else None,
            "black_player": {
                "id": str(row.black_player_id),
                "username": row.black_username,
                "rating": row.black_rating
            } if row.black_player_id else None
        }

        return {
            "success": True,
            "game": game_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get chess game {game_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve chess game: {str(e)}"
        )


@router.get("/games/{game_id}/public", response_model=Dict[str, Any])
async def get_chess_game_by_id_public(game_id: str):
    """Get a specific chess game by ID - no authentication required"""
    if game_id == "550e8400-e29b-41d4-a716-************":
        return {
            "success": True,
            "game": {
                "id": "550e8400-e29b-41d4-a716-************",
                "variant": "standard",
                "time_control": "blitz",
                "initial_time_seconds": 300,
                "increment_seconds": 3,
                "is_rated": True,
                "is_private": False,
                "allow_spectators": True,
                "wager_amount": 0,
                "status": "waiting",
                "created_at": "2024-01-15T10:30:00Z",
                "description": "Test chess game for WebSocket testing",
                "current_fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
                "move_count": 0,
                "white_time_remaining": 300,
                "black_time_remaining": 300,
                "white_player": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "username": "ChessMaster2024",
                    "rating": 1650
                },
                "black_player": None
            }
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chess game not found"
        )


@router.get("/leaderboard", response_model=List[ChessLeaderboard])
async def get_chess_leaderboard(
    time_control: str = "blitz",
    limit: int = 100,
    db=Depends(get_database_session)
):
    """Get chess leaderboard for a time control"""
    try:
        # Query database for top rated players
        return []  # Placeholder
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get leaderboard: {str(e)}"
        )


@router.get("/users/{user_id}/stats", response_model=ChessGameStats)
async def get_user_chess_stats(
    user_id: UUID,
    time_control: Optional[str] = None,
    db=Depends(get_database_session)
):
    """Get chess statistics for a user"""
    try:
        # Query database for user's chess statistics
        return ChessGameStats()  # Placeholder
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user stats: {str(e)}"
        )


@router.get("/users/{user_id}/rating-history", response_model=List[ChessRatingHistory])
async def get_rating_history(
    user_id: UUID,
    time_control: str = "blitz",
    limit: int = 50,
    db=Depends(get_database_session)
):
    """Get rating history for a user"""
    try:
        # Query database for rating history
        return []  # Placeholder
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get rating history: {str(e)}"
        )


# Background task functions
async def notify_game_update(game_id: UUID, move_data: Any):
    """Notify spectators and players of game update via WebSocket"""
    from ...websocket.chess_handler import chess_game_handler
    # WebSocket notifications are handled automatically by the chess_game_handler
    # when moves are processed through the service
    pass


async def handle_game_end(game_id: UUID, result: str):
    """Handle game end logic - update ratings, settle bets, etc."""
    from ...websocket.chess_handler import chess_game_handler
    
    # The chess WebSocket handler will handle:
    # 1. Broadcasting game end to all connected clients
    # 2. Updating game result in database  
    # 3. Calculating rating changes
    # 4. Settling spectator bets
    # 5. Updating player statistics
    # 6. Cleaning up game resources
    
    # Handle wallet settlement for wagered games
    await settle_game_wagers(game_id, result)
    
    # Cleanup is handled by the WebSocket handler
    pass


async def notify_draw_offer(game_id: UUID, player_id: UUID):
    """Notify opponent of draw offer"""
    from ...websocket.chess_handler import chess_game_handler
    # Draw offers are broadcast automatically through WebSocket handler
    pass


async def notify_draw_decline(game_id: UUID, player_id: UUID):
    """Notify opponent that draw offer was declined"""
    from ...websocket.chess_handler import chess_game_handler
    # Draw declines are broadcast automatically through WebSocket handler
    pass


async def settle_game_wagers(game_id: UUID, game_result: str):
    """
    Settle game wagers by unlocking funds and distributing winnings.
    
    Args:
        game_id: The chess game ID
        game_result: Game outcome ('white_wins', 'black_wins', 'draw', 'aborted', etc.)
    """
    try:
        # TODO: Get game details from database to identify players and stake amount
        # For now, we'll use placeholder data structure
        game_data = {
            "stake_amount": Decimal('50.00'),  # This should come from game record
            "white_player_id": "user1",        # These should come from game record
            "black_player_id": "user2"
        }
        
        # Skip settlement for games without stakes or aborted games
        if game_data["stake_amount"] <= 0 or game_result == "aborted":
            logger.info(f"Skipping settlement for game {game_id}: no stakes or aborted")
            return
        
        async with WalletClient(service_name="gaming-engine") as wallet:
            
            # Calculate platform fee on the total pot (both player contributions)
            total_pot = game_data["stake_amount"] * 2
            fee_calc = await wallet.calculate_platform_fee(
                user_id=game_data["white_player_id"],  # Use white player for fee calculation
                transaction_amount=total_pot,
                fee_category=FeeCategory.GAMING,
                fee_type=FeeType.STANDARD
            )
            
            # Determine winnings distribution
            if game_result == "white_wins":
                winner_id = game_data["white_player_id"]
                loser_id = game_data["black_player_id"]
                winner_payout = total_pot - fee_calc.fee_amount
                loser_payout = Decimal('0')
                
            elif game_result == "black_wins":
                winner_id = game_data["black_player_id"] 
                loser_id = game_data["white_player_id"]
                winner_payout = total_pot - fee_calc.fee_amount
                loser_payout = Decimal('0')
                
            elif game_result in ["draw", "draw_agreement", "threefold_repetition", "insufficient_material"]:
                # In a draw, return original stakes minus half the platform fee each
                winner_id = None
                loser_id = None
                half_fee = fee_calc.fee_amount / 2
                winner_payout = game_data["stake_amount"] - half_fee
                loser_payout = game_data["stake_amount"] - half_fee
                
            else:
                # Timeout, resignation, or other result - handle as win/loss
                # TODO: Determine winner based on game_result details
                logger.warning(f"Unhandled game result: {game_result}, treating as no contest")
                winner_payout = game_data["stake_amount"]
                loser_payout = game_data["stake_amount"]
            
            # Settle both players' wagers
            settlement_results = []
            
            # White player settlement
            white_settlement = await wallet.settle_game_wager(
                user_id=game_data["white_player_id"],
                original_wager=game_data["stake_amount"],
                payout_amount=winner_payout if winner_id == game_data["white_player_id"] else loser_payout,
                game_id=str(game_id),
                game_type="chess"
            )
            settlement_results.append(("white", white_settlement))
            
            # Black player settlement  
            black_settlement = await wallet.settle_game_wager(
                user_id=game_data["black_player_id"],
                original_wager=game_data["stake_amount"],
                payout_amount=winner_payout if winner_id == game_data["black_player_id"] else loser_payout,
                game_id=str(game_id),
                game_type="chess"  
            )
            settlement_results.append(("black", black_settlement))
            
            # Log settlement results
            for player_color, settlement in settlement_results:
                if settlement.success:
                    logger.info(
                        f"Chess game settlement successful",
                        game_id=str(game_id),
                        player_color=player_color,
                        result=game_result,
                        payout=float(settlement.metadata.get("win_amount", 0)),
                        new_balance=float(settlement.new_balance)
                    )
                else:
                    logger.error(
                        f"Chess game settlement failed",
                        game_id=str(game_id),
                        player_color=player_color,
                        error=settlement.error_message
                    )
            
            # Record platform fee collection
            logger.info(
                f"Platform fee collected from chess game",
                game_id=str(game_id),
                total_pot=float(total_pot),
                fee_amount=float(fee_calc.fee_amount),
                fee_percentage=float(fee_calc.applied_percentage),
                promotional_discount=float(fee_calc.promotional_discount) if fee_calc.promotional_discount else None
            )
            
    except Exception as e:
        logger.error(
            f"Failed to settle game wagers",
            game_id=str(game_id),
            game_result=game_result,
            error=str(e)
        )
        # Don't re-raise - game should still end even if settlement fails
        # TODO: Add settlement retry mechanism or manual intervention queue