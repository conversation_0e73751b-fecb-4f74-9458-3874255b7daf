"""
BetBet Gaming Engine - Analytics API
====================================

API endpoints for performance analytics and statistics.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

# Import models and dependencies
from app.models.games import GameStats
from app.models.sessions import SessionStats
from app.models.tournaments import TournamentStats
from app.models.spectator_bets import BettingStats
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_admin_user,
    NotFoundError, ValidationError, PermissionError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/analytics/games/{game_id}/stats")
async def get_game_analytics(game_id: UUID):
    """Get comprehensive analytics for a specific game"""
    # TODO: Implement game analytics
    return {"message": f"Game analytics for {game_id} - TODO"}


@router.get("/analytics/sessions/{session_id}/stats")
async def get_session_analytics(session_id: UUID):
    """Get comprehensive analytics for a specific session"""
    # TODO: Implement session analytics
    return {"message": f"Session analytics for {session_id} - TODO"}


@router.get("/analytics/tournaments/{tournament_id}/stats")
async def get_tournament_analytics(tournament_id: UUID):
    """Get comprehensive analytics for a specific tournament"""
    # TODO: Implement tournament analytics
    return {"message": f"Tournament analytics for {tournament_id} - TODO"}


@router.get("/analytics/overview")
async def get_gaming_analytics_overview(
    db: AsyncSession = Depends(get_database_session)
):
    """Get gaming engine analytics overview with real database data"""
    try:
        from sqlalchemy import select, func, distinct
        from app.database.models import Game, GameSession, SessionParticipant, Tournament, TournamentParticipant
        
        # Get total unique users across all gaming activities
        total_users_query = select(func.count(distinct(SessionParticipant.user_id)))
        total_users_result = await db.execute(total_users_query)
        total_users = total_users_result.scalar() or 0
        
        # Get active sessions (waiting, starting, active states)
        active_sessions_query = select(func.count(GameSession.id)).where(
            GameSession.state.in_(["waiting", "starting", "active"])
        )
        active_sessions_result = await db.execute(active_sessions_query)
        active_sessions = active_sessions_result.scalar() or 0
        
        # Get active tournaments (scheduled, registration_open, active states)
        active_tournaments_query = select(func.count(Tournament.id)).where(
            Tournament.status.in_(["scheduled", "registration_open", "active"])
        )
        active_tournaments_result = await db.execute(active_tournaments_query)
        active_tournaments = active_tournaments_result.scalar() or 0
        
        # Calculate total betting volume from session entry fees
        betting_volume_query = select(func.coalesce(func.sum(SessionParticipant.stake_amount), 0))
        betting_volume_result = await db.execute(betting_volume_query)
        total_betting_volume = float(betting_volume_result.scalar() or 0)
        
        # Get total sessions count
        total_sessions_query = select(func.count(GameSession.id))
        total_sessions_result = await db.execute(total_sessions_query)
        total_sessions = total_sessions_result.scalar() or 0
        
        # Get total games available
        total_games_query = select(func.count(Game.id)).where(Game.is_active == True)
        total_games_result = await db.execute(total_games_query)
        total_games = total_games_result.scalar() or 0
        
        # Get completed sessions today
        from datetime import datetime, timedelta
        today = datetime.utcnow().date()
        completed_today_query = select(func.count(GameSession.id)).where(
            GameSession.state == "completed",
            func.date(GameSession.actual_ended_at) == today
        )
        completed_today_result = await db.execute(completed_today_query)
        sessions_completed_today = completed_today_result.scalar() or 0
        
        analytics_data = {
            "total_users": total_users,
            "active_sessions": active_sessions,
            "active_tournaments": active_tournaments,
            "total_betting_volume": total_betting_volume,
            "total_sessions": total_sessions,
            "total_games": total_games,
            "sessions_completed_today": sessions_completed_today,
            "platform_revenue": total_betting_volume * 0.05,  # 5% platform fee
            "average_session_value": total_betting_volume / max(total_sessions, 1)
        }
        
        return analytics_data
        
    except Exception as e:
        logger.error("Failed to get gaming analytics overview", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve gaming analytics overview"
        )


@router.get("/analytics/leaderboard")
async def get_leaderboard(
    game_id: Optional[UUID] = Query(None, description="Filter by game"),
    period: str = Query("all_time", description="Time period (daily/weekly/monthly/all_time)")
):
    """Get player leaderboard"""
    # TODO: Implement leaderboard
    return {"message": f"Leaderboard for period {period} - TODO"}


@router.get("/analytics/revenue")
async def get_revenue_analytics(
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date")
):
    """Get revenue analytics"""
    # TODO: Implement revenue analytics
    return {"message": "Revenue analytics - TODO"}


@router.get("/analytics/users/{user_id}/stats")
async def get_user_stats(
    user_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """Get comprehensive user statistics"""
    try:
        from sqlalchemy import select, func, case
        from app.database.models import GameSession, SessionParticipant, Game
        
        # Basic session stats
        session_stats_query = select(
            func.count(SessionParticipant.id).label('total_sessions'),
            func.count(case((SessionParticipant.final_position == 1, 1))).label('sessions_won'),
            func.count(case((SessionParticipant.final_position > 1, 1))).label('sessions_lost'),
            func.coalesce(func.sum(SessionParticipant.payout_amount), 0).label('total_earnings'),
            func.coalesce(func.sum(SessionParticipant.stake_amount), 0).label('total_spent'),
        ).where(SessionParticipant.user_id == user_id)
        
        session_stats_result = await db.execute(session_stats_query)
        session_stats = session_stats_result.fetchone()
        
        # Get favorite game
        favorite_game_query = select(Game.name, func.count(SessionParticipant.id).label('count')).join(
            GameSession, SessionParticipant.session_id == GameSession.id
        ).join(
            Game, GameSession.game_id == Game.id
        ).where(SessionParticipant.user_id == user_id).group_by(Game.name).order_by(func.count(SessionParticipant.id).desc()).limit(1)
        
        favorite_result = await db.execute(favorite_game_query)
        favorite_game = favorite_result.fetchone()
        
        # Calculate derived stats
        total_sessions = session_stats.total_sessions or 0
        sessions_won = session_stats.sessions_won or 0
        sessions_lost = session_stats.sessions_lost or 0
        total_earnings = float(session_stats.total_earnings or 0)
        total_spent = float(session_stats.total_spent or 0)
        
        win_rate = (sessions_won / total_sessions) if total_sessions > 0 else 0
        net_profit = total_earnings - total_spent
        
        # Mock some additional stats for now (TODO: implement proper calculations)
        current_streak = 2  # TODO: Calculate actual streak
        best_streak = 5    # TODO: Calculate actual best streak
        tournaments_joined = 3  # TODO: Get from tournaments table
        tournaments_won = 1     # TODO: Get from tournaments table
        current_rank = 247      # TODO: Calculate actual ranking
        skill_points = 1850     # TODO: Implement skill point system
        
        user_stats = {
            "total_sessions": total_sessions,
            "sessions_won": sessions_won,
            "sessions_lost": sessions_lost,
            "win_rate": win_rate,
            "total_earnings": total_earnings,
            "total_spent": total_spent,
            "net_profit": net_profit,
            "favorite_game": favorite_game.name if favorite_game else "No games played",
            "current_streak": current_streak,
            "best_streak": best_streak,
            "tournaments_joined": tournaments_joined,
            "tournaments_won": tournaments_won,
            "current_rank": current_rank,
            "skill_points": skill_points
        }
        
        return user_stats
        
    except Exception as e:
        logger.error("Failed to get user stats", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user statistics"
        )


@router.get("/analytics/users/{user_id}/activity")
async def get_user_activity(
    user_id: str,
    limit: int = Query(10, ge=1, le=50, description="Number of activities to return"),
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """Get user's recent activity"""
    try:
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload
        from app.database.models import GameSession, SessionParticipant, Game
        
        # Get recent session participations
        recent_sessions_query = select(SessionParticipant).options(
            selectinload(SessionParticipant.session).selectinload(GameSession.game)
        ).where(
            SessionParticipant.user_id == user_id
        ).order_by(SessionParticipant.joined_at.desc()).limit(limit)
        
        recent_sessions_result = await db.execute(recent_sessions_query)
        recent_sessions = recent_sessions_result.scalars().all()
        
        activities = []
        for participation in recent_sessions:
            session = participation.session
            game = session.game if session else None
            
            # Determine result
            result = None
            if participation.final_position:
                if participation.final_position == 1:
                    result = "won"
                elif participation.final_position > 1:
                    result = "lost"
                else:
                    result = "pending"
            
            activity = {
                "id": str(participation.id),
                "type": "session",
                "title": f"{game.name if game else 'Game'} Session",
                "game_name": game.name if game else None,
                "result": result,
                "amount": float(participation.payout_amount) if participation.payout_amount else 0,
                "created_at": participation.joined_at.isoformat(),
                "session_id": str(session.id) if session else None
            }
            activities.append(activity)
        
        return activities
        
    except Exception as e:
        logger.error("Failed to get user activity", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user activity"
        )


@router.get("/analytics/users/{user_id}/active-sessions")
async def get_user_active_sessions(
    user_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """Get user's active sessions"""
    try:
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload
        from app.database.models import GameSession, SessionParticipant, Game
        
        # Get active sessions user is participating in
        active_sessions_query = select(SessionParticipant).options(
            selectinload(SessionParticipant.session).selectinload(GameSession.game)
        ).where(
            SessionParticipant.user_id == user_id,
            SessionParticipant.session.has(GameSession.state.in_(["waiting", "starting", "active"]))
        ).order_by(SessionParticipant.joined_at.desc())
        
        active_sessions_result = await db.execute(active_sessions_query)
        active_participations = active_sessions_result.scalars().all()
        
        sessions = []
        for participation in active_participations:
            session = participation.session
            game = session.game if session else None
            
            session_data = {
                "id": str(session.id),
                "slug": session.slug or f"session-{session.id}",
                "session_name": session.session_name or "Unnamed Session",
                "game_name": game.name if game else "Unknown Game",
                "state": session.state,
                "current_participants": session.current_participants,
                "max_participants": session.max_participants,
                "entry_fee": float(session.entry_fee),
                "user_role": "player",  # TODO: Determine if spectator or player
                "scheduled_start_time": session.scheduled_start_at.isoformat() if session.scheduled_start_at else None
            }
            sessions.append(session_data)
        
        return sessions
        
    except Exception as e:
        logger.error("Failed to get user active sessions", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user active sessions"
        )