"""
BetBet Gaming Engine - Games API
===============================

API endpoints for game management and configuration.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
import structlog

# Import SQLAlchemy models
from app.database.models import Game

# Import Pydantic models for validation
from app.models.games import (
    GameCreate, GameUpdate, GameResponse, GameStats,
    GameSearchFilter, GameListResponse
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_optional_user, get_admin_user, get_game_moderator,
    get_pagination, get_game_query_params,
    PaginationParams, GameQueryParams,
    NotFoundError, ValidationError, PermissionError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/games", response_model=GameListResponse)
async def list_games(
    pagination: PaginationParams = Depends(get_pagination),
    query_params: GameQueryParams = Depends(get_game_query_params),
    sort_by: str = Query("popularity_score", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    List games with filtering and pagination
    
    Returns a paginated list of games with optional filtering by category,
    status, features, and search terms.
    """
    try:
        # Build query
        query = select(Game).where(Game.deleted_at.is_(None))
        
        # Apply filters
        if query_params.category:
            query = query.where(Game.category == query_params.category)
            
        if query_params.is_active is not None:
            query = query.where(Game.is_active == query_params.is_active)
            
        if query_params.is_featured is not None:
            query = query.where(Game.is_featured == query_params.is_featured)
            
        if query_params.has_spectator_betting is not None:
            query = query.where(Game.has_spectator_betting == query_params.has_spectator_betting)
            
        if query_params.search:
            search_pattern = f"%{query_params.search}%"
            query = query.where(
                or_(
                    Game.name.ilike(search_pattern),
                    Game.description.ilike(search_pattern)
                )
            )
        
        # Apply sorting
        if sort_order.lower() == "desc":
            sort_column = getattr(Game, sort_by).desc()
        else:
            sort_column = getattr(Game, sort_by).asc()
        query = query.order_by(sort_column)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await db.scalar(count_query)
        
        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await db.execute(query)
        games = result.scalars().all()
        
        # Convert to response models
        game_responses = [GameResponse.model_validate(game) for game in games]
        
        logger.info(
            "Listed games",
            count=len(game_responses),
            total_count=total_count,
            page=pagination.page,
            user_id=current_user.user_id if current_user else None
        )
        
        return GameListResponse(
            games=game_responses,
            total_count=total_count,
            page=pagination.page,
            limit=pagination.limit,
            has_next=(pagination.offset + pagination.limit) < total_count,
            has_prev=pagination.page > 1
        )
        
    except Exception as e:
        logger.error("Failed to list games", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve games"
        )


@router.get("/games/featured", response_model=List[GameResponse])
async def get_featured_games(
    limit: int = Query(10, ge=1, le=50, description="Number of featured games"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get featured games
    
    Returns a list of featured games sorted by popularity.
    """
    try:
        query = (
            select(Game)
            .where(
                and_(
                    Game.is_featured == True,
                    Game.is_active == True,
                    Game.deleted_at.is_(None)
                )
            )
            .order_by(Game.popularity_score.desc())
            .limit(limit)
        )
        
        result = await db.execute(query)
        games = result.scalars().all()
        
        game_responses = [GameResponse.model_validate(game) for game in games]
        
        logger.info(
            "Retrieved featured games",
            count=len(game_responses),
            user_id=current_user.user_id if current_user else None
        )
        
        return game_responses
        
    except Exception as e:
        logger.error("Failed to get featured games", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve featured games"
        )


@router.get("/games/slug/{slug}", response_model=GameResponse)
async def get_game_by_slug(
    slug: str,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific game by slug
    
    Returns detailed information about a game using its human-readable slug.
    This is the preferred method for frontend applications.
    """
    try:
        query = select(Game).where(
            and_(Game.slug == slug, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", f"slug '{slug}'")
        
        logger.info(
            "Retrieved game by slug",
            slug=slug,
            game_id=game.id,
            game_name=game.name,
            user_id=current_user.user_id if current_user else None
        )
        
        return GameResponse.model_validate(game)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (slug: {slug})"
        )
    except Exception as e:
        logger.error("Failed to get game by slug", slug=slug, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve game"
        )


@router.get("/games/{game_id}", response_model=GameResponse)
async def get_game(
    game_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific game by ID
    
    Returns detailed information about a game including configuration,
    statistics, and current status.
    """
    try:
        query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        logger.info(
            "Retrieved game",
            game_id=game_id,
            game_name=game.name,
            user_id=current_user.user_id if current_user else None
        )
        
        return GameResponse.model_validate(game)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to get game", game_id=game_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve game"
        )


@router.post("/games", response_model=GameResponse, status_code=status.HTTP_201_CREATED)
async def create_game(
    game_data: GameCreate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_game_moderator)
):
    """
    Create a new game
    
    Creates a new game with the specified configuration. Requires
    moderator or admin privileges.
    """
    try:
        # Check for slug uniqueness
        existing_query = select(Game).where(Game.slug == game_data.slug)
        existing_game = await db.scalar(existing_query)
        
        if existing_game:
            raise ValidationError(f"Game with slug '{game_data.slug}' already exists")
        
        # Create game record
        game = Game(
            **game_data.model_dump(),
            created_by=current_user.user_id,
            updated_by=current_user.user_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(game)
        await db.commit()
        await db.refresh(game)
        
        logger.info(
            "Created game",
            game_id=game.id,
            game_name=game.name,
            created_by=current_user.user_id
        )
        
        return GameResponse.model_validate(game)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create game", error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create game"
        )


@router.put("/games/{game_id}", response_model=GameResponse)
async def update_game(
    game_id: UUID,
    update_data: GameUpdate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_game_moderator)
):
    """
    Update an existing game
    
    Updates game configuration and settings. Requires moderator
    or admin privileges.
    """
    try:
        # Get existing game
        query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(game, field, value)
        
        game.updated_by = current_user.user_id
        game.updated_at = datetime.utcnow()
        game.version += 1
        
        await db.commit()
        await db.refresh(game)
        
        logger.info(
            "Updated game",
            game_id=game_id,
            updated_by=current_user.user_id,
            updated_fields=list(update_dict.keys())
        )
        
        return GameResponse.model_validate(game)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to update game", game_id=game_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update game"
        )


@router.delete("/games/{game_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_game(
    game_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_admin_user)
):
    """
    Delete a game (soft delete)
    
    Soft deletes a game by setting the deleted_at timestamp.
    Requires admin privileges.
    """
    try:
        # Get existing game
        query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        # Soft delete
        game.deleted_at = datetime.utcnow()
        game.deleted_by = current_user.user_id
        game.updated_at = datetime.utcnow()
        game.updated_by = current_user.user_id
        game.version += 1
        
        await db.commit()
        
        logger.info(
            "Deleted game",
            game_id=game_id,
            deleted_by=current_user.user_id
        )
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to delete game", game_id=game_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete game"
        )


@router.get("/games/{game_id}/stats", response_model=GameStats)
async def get_game_stats(
    game_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get game statistics
    
    Returns comprehensive statistics for a game including session counts,
    player metrics, and performance data.
    """
    try:
        # Verify game exists
        game_query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        # TODO: Calculate actual statistics from sessions and participants
        # For now, return basic stats from the game record
        stats = GameStats(
            game_id=game_id,
            total_sessions=game.total_sessions_played,
            active_sessions=0,  # TODO: Count active sessions
            total_players=0,    # TODO: Count unique players
            average_duration=float(game.average_session_duration) if game.average_session_duration else None,
            popularity_score=float(game.popularity_score),
            average_players_per_session=float(game.average_players_per_session) if game.average_players_per_session else 0.0,
            completion_rate=0.0,  # TODO: Calculate completion rate
            sessions_last_24h=0,  # TODO: Count recent sessions
            sessions_last_week=0  # TODO: Count weekly sessions
        )
        
        logger.info(
            "Retrieved game stats",
            game_id=game_id,
            user_id=current_user.user_id if current_user else None
        )
        
        return stats
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to get game stats", game_id=game_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve game statistics"
        )


@router.post("/games/{game_id}/activate", response_model=GameResponse)
async def activate_game(
    game_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_game_moderator)
):
    """
    Activate a game
    
    Sets a game as active and available for sessions.
    Requires moderator or admin privileges.
    """
    try:
        # Get existing game
        query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        # Activate game
        game.is_active = True
        game.updated_by = current_user.user_id
        game.updated_at = datetime.utcnow()
        game.version += 1
        
        await db.commit()
        await db.refresh(game)
        
        logger.info(
            "Activated game",
            game_id=game_id,
            activated_by=current_user.user_id
        )
        
        return GameResponse.model_validate(game)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to activate game", game_id=game_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate game"
        )


@router.post("/games/{game_id}/deactivate", response_model=GameResponse)
async def deactivate_game(
    game_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_game_moderator)
):
    """
    Deactivate a game
    
    Sets a game as inactive and unavailable for new sessions.
    Requires moderator or admin privileges.
    """
    try:
        # Get existing game
        query = select(Game).where(
            and_(Game.id == game_id, Game.deleted_at.is_(None))
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise NotFoundError("Game", str(game_id))
        
        # Deactivate game
        game.is_active = False
        game.updated_by = current_user.user_id
        game.updated_at = datetime.utcnow()
        game.version += 1
        
        await db.commit()
        await db.refresh(game)
        
        logger.info(
            "Deactivated game",
            game_id=game_id,
            deactivated_by=current_user.user_id
        )
        
        return GameResponse.model_validate(game)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Game not found (ID: {game_id})"
        )
    except Exception as e:
        logger.error("Failed to deactivate game", game_id=game_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate game"
        )