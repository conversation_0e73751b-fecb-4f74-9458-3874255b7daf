"""
BetBet Gaming Engine - Sessions API
===================================

API endpoints for game session management and real-time state tracking.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
import structlog

# Import SQLAlchemy models
from app.database.models import GameSession, SessionParticipant, GameState, Game

# Import utilities
from app.utils.slugs import generate_session_slug

# Import Pydantic models for validation
from app.models.sessions import (
    GameSessionCreate, GameSessionUpdate, GameSessionResponse,
    SessionParticipantCreate, SessionParticipantResponse,
    GameStateCreate, SessionStatus, ParticipantStatus,
    SessionSearchFilter, SessionListResponse, SessionStats
)
from app.api.dependencies import (
    get_database_session, get_read_database_session,
    get_current_user, get_optional_user, get_admin_user, get_game_moderator,
    get_pagination, get_session_query_params,
    PaginationParams, SessionQueryParams,
    NotFoundError, ValidationError, PermissionError, ConflictError
)
from shared.core.auth.models import TokenData

# Configure logging
logger = structlog.get_logger()

# Create router
router = APIRouter()


@router.get("/sessions", response_model=SessionListResponse)
async def list_sessions(
    pagination: PaginationParams = Depends(get_pagination),
    query_params: SessionQueryParams = Depends(get_session_query_params),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    List game sessions with filtering and pagination
    
    Returns a paginated list of game sessions with optional filtering by game,
    status, entry fee, and availability.
    """
    try:
        # Build query
        query = select(GameSession).where(GameSession.deleted_at.is_(None))
        
        # Apply filters
        if query_params.game_id:
            query = query.where(GameSession.game_id == query_params.game_id)
            
        if query_params.status:
            query = query.where(GameSession.status == query_params.status)
            
        if query_params.min_entry_fee is not None:
            query = query.where(GameSession.entry_fee >= query_params.min_entry_fee)
            
        if query_params.max_entry_fee is not None:
            query = query.where(GameSession.entry_fee <= query_params.max_entry_fee)
            
        if query_params.has_available_slots:
            query = query.where(GameSession.current_participants < GameSession.max_participants)
            
        if query_params.starting_soon:
            cutoff_time = datetime.utcnow() + timedelta(hours=1)
            query = query.where(
                and_(
                    GameSession.scheduled_start_time.is_not(None),
                    GameSession.scheduled_start_time <= cutoff_time,
                    GameSession.status.in_([SessionStatus.WAITING, SessionStatus.STARTING])
                )
            )
        
        # Apply sorting
        if sort_order.lower() == "desc":
            sort_column = getattr(GameSession, sort_by).desc()
        else:
            sort_column = getattr(GameSession, sort_by).asc()
        query = query.order_by(sort_column)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await db.scalar(count_query)
        
        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)
        
        # Execute query
        result = await db.execute(query)
        sessions = result.scalars().all()
        
        # Convert to response models
        session_responses = [GameSessionResponse.model_validate(session) for session in sessions]
        
        logger.info(
            "Listed sessions",
            count=len(session_responses),
            total_count=total_count,
            page=pagination.page,
            user_id=current_user.user_id if current_user else None
        )
        
        return SessionListResponse(
            sessions=session_responses,
            total_count=total_count,
            page=pagination.page,
            limit=pagination.limit,
            has_next=(pagination.offset + pagination.limit) < total_count,
            has_prev=pagination.page > 1
        )
        
    except Exception as e:
        logger.error("Failed to list sessions", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.get("/sessions/slug/{slug}", response_model=GameSessionResponse)
async def get_session_by_slug(
    slug: str,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific session by slug
    
    Returns detailed information about a session using its descriptive slug.
    This is the preferred method for sharing session links and archival access.
    
    Examples:
    - chess-blitz-alice-vs-bob-2025-01-19-15-30
    - trivia-challenge-tournament-4p-2025-01-19-20-00
    """
    try:
        query = select(GameSession).where(
            and_(GameSession.slug == slug, GameSession.deleted_at.is_(None))
        )
        result = await db.execute(query)
        session = result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", f"slug '{slug}'")
        
        logger.info(
            "Retrieved session by slug",
            slug=slug,
            session_id=session.id,
            session_name=session.session_name,
            user_id=current_user.user_id if current_user else None
        )
        
        return GameSessionResponse.model_validate(session)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session not found (slug: {slug})"
        )
    except Exception as e:
        logger.error("Failed to get session by slug", slug=slug, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session"
        )


@router.get("/sessions/{session_id}", response_model=GameSessionResponse)
async def get_session(
    session_id: UUID,
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get a specific game session by ID
    
    Returns detailed information about a game session including participants,
    status, and current state.
    """
    try:
        query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        result = await db.execute(query)
        session = result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        logger.info(
            "Retrieved session",
            session_id=session_id,
            session_name=session.session_name,
            user_id=current_user.user_id if current_user else None
        )
        
        return GameSessionResponse.model_validate(session)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session not found (ID: {session_id})"
        )
    except Exception as e:
        logger.error("Failed to get session", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session"
        )


@router.post("/sessions", response_model=GameSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
    session_data: GameSessionCreate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Create a new game session
    
    Creates a new game session for the specified game. The creator
    automatically becomes the first participant.
    """
    try:
        logger.info(f"Creating session with data: {session_data.model_dump()}")
        # Verify game exists and is active
        game_query = select(Game).where(
            and_(Game.id == session_data.game_id, Game.deleted_at.is_(None))
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise ValidationError(f"Game not found (ID: {session_data.game_id})")
            
        if not game.is_active:
            raise ValidationError("Cannot create session for inactive game")
        
        # Validate participant limits against game constraints
        if session_data.min_participants_to_start < game.min_players:
            raise ValidationError(f"Minimum participants cannot be less than game minimum ({game.min_players})")
            
        if session_data.max_participants > game.max_players:
            raise ValidationError(f"Maximum participants cannot exceed game maximum ({game.max_players})")
        
        # Create session record - manually specify all fields to avoid field name issues
        session = GameSession(
            game_id=session_data.game_id,
            session_name=session_data.session_name,
            entry_fee=session_data.entry_fee,
            min_participants_to_start=session_data.min_participants_to_start,
            max_participants=session_data.max_participants,
            game_config=session_data.game_config,
            session_rules=session_data.session_rules,
            scheduled_start_time=session_data.scheduled_start_time,
            estimated_duration_minutes=session_data.estimated_duration_minutes,
            allow_spectators=session_data.allow_spectators,
            allow_practice_mode=session_data.allow_practice_mode,
            is_tournament_session=session_data.is_tournament_session,
            tournament_id=session_data.tournament_id,
            current_participants=0,  # Will be incremented when creator joins
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            created_by=current_user.user_id,  # Use authenticated user, not from request data
            updated_by=current_user.user_id
        )
        
        db.add(session)
        await db.flush()  # Get the session ID
        
        # Generate session slug based on game and initial participant
        # For now, use a placeholder format - will be updated when more players join
        session_slug = generate_session_slug(
            game_slug=game.slug,
            participants=[current_user.username] if hasattr(current_user, 'username') and current_user.username else [current_user.user_id],
            session_type=session.session_type,
            start_time=session.scheduled_start_at or datetime.utcnow(),
            session_name=session.session_name
        )
        session.slug = session_slug
        
        # Add creator as first participant
        participant = SessionParticipant(
            session_id=session.id,
            user_id=current_user.user_id,
            entry_fee_paid=session_data.entry_fee,
            join_order=1,
            joined_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(participant)
        
        # Update session participant count
        session.current_participants = 1
        session.total_prize_pool = session_data.entry_fee
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(
            "Created session",
            session_id=session.id,
            game_id=session_data.game_id,
            created_by=current_user.user_id
        )
        
        return GameSessionResponse.model_validate(session)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create session", error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create session: {str(e)}"
        )


@router.put("/sessions/{session_id}", response_model=GameSessionResponse)
async def update_session(
    session_id: UUID,
    update_data: GameSessionUpdate,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Update an existing game session
    
    Updates session configuration. Only the session creator or moderators
    can update sessions, and only before the session starts.
    """
    try:
        # Get existing session
        query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        result = await db.execute(query)
        session = result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        # Check permissions
        is_creator = session.created_by == current_user.user_id
        is_moderator = any(role in current_user.roles for role in ["admin", "moderator", "game_admin"])
        
        if not (is_creator or is_moderator):
            raise PermissionError("Only session creator or moderators can update sessions")
        
        # Check if session can be updated
        if session.status not in [SessionStatus.WAITING, SessionStatus.STARTING]:
            raise ConflictError("Cannot update session after it has started")
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(session, field, value)
        
        session.updated_by = current_user.user_id
        session.updated_at = datetime.utcnow()
        session.version += 1
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(
            "Updated session",
            session_id=session_id,
            updated_by=current_user.user_id,
            updated_fields=list(update_dict.keys())
        )
        
        return GameSessionResponse.model_validate(session)
        
    except (NotFoundError, PermissionError, ConflictError) as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to update session", session_id=session_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update session"
        )


@router.post("/sessions/{session_id}/join", response_model=SessionParticipantResponse)
async def join_session(
    session_id: UUID,
    as_spectator: bool = Query(False, description="Join as spectator"),
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Join a game session
    
    Adds the current user as a participant or spectator in the specified session.
    """
    try:
        # Get session
        session_query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        # Check if session is joinable
        if session.status not in [SessionStatus.WAITING, SessionStatus.STARTING]:
            raise ConflictError("Cannot join session that has already started")
        
        # Check if user is already a participant
        existing_query = select(SessionParticipant).where(
            and_(
                SessionParticipant.session_id == session_id,
                SessionParticipant.user_id == current_user.user_id
            )
        )
        existing_participant = await db.scalar(existing_query)
        
        if existing_participant:
            raise ConflictError("User is already a participant in this session")
        
        # Check capacity
        if not as_spectator and session.current_participants >= session.max_participants:
            raise ConflictError("Session is full")
        
        if as_spectator and not session.allow_spectators:
            raise ConflictError("Spectators are not allowed in this session")
        
        # Create participant record
        participant = SessionParticipant(
            session_id=session_id,
            user_id=current_user.user_id,
            entry_fee_paid=session.entry_fee if not as_spectator else 0,
            is_spectator=as_spectator,
            join_order=session.current_participants + 1 if not as_spectator else 0,
            joined_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(participant)
        
        # Update session counts
        if as_spectator:
            session.current_spectators += 1
        else:
            session.current_participants += 1
            session.total_prize_pool += session.entry_fee
        
        session.updated_at = datetime.utcnow()
        session.updated_by = current_user.user_id
        
        # Regenerate session slug with updated participant list (for non-spectators)
        if not as_spectator and session.current_participants <= 4:  # Only for small sessions
            # Get all current participants including the one we just added
            participants_query = select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_id,
                    SessionParticipant.is_spectator == False
                )
            ).order_by(SessionParticipant.join_order)
            
            participants_result = await db.execute(participants_query)
            all_participants = participants_result.scalars().all()
            
            # Get game for slug
            game_query = select(Game).where(Game.id == session.game_id)
            game = await db.scalar(game_query)
            
            if game and len(all_participants) >= 2:
                # Generate usernames list (fallback to user_id if username not available)
                participant_names = []
                for p in all_participants:
                    # For now, use user_id since we don't have username access here
                    # TODO: Join with users table to get actual usernames
                    participant_names.append(str(p.user_id)[:8])  # Use first 8 chars of UUID
                
                # Regenerate slug with all participants
                new_slug = generate_session_slug(
                    game_slug=game.slug,
                    participants=participant_names,
                    session_type=session.session_type,
                    start_time=session.scheduled_start_at or session.created_at,
                    session_name=session.session_name
                )
                session.slug = new_slug
        
        await db.commit()
        await db.refresh(participant)
        
        logger.info(
            "User joined session",
            session_id=session_id,
            user_id=current_user.user_id,
            as_spectator=as_spectator
        )
        
        return SessionParticipantResponse.model_validate(participant)
        
    except (NotFoundError, ConflictError) as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to join session", session_id=session_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to join session"
        )


@router.post("/sessions/{session_id}/leave", status_code=status.HTTP_204_NO_CONTENT)
async def leave_session(
    session_id: UUID,
    db: AsyncSession = Depends(get_database_session),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Leave a game session
    
    Removes the current user from the specified session.
    """
    try:
        # Get participant record
        participant_query = select(SessionParticipant).where(
            and_(
                SessionParticipant.session_id == session_id,
                SessionParticipant.user_id == current_user.user_id
            )
        )
        participant_result = await db.execute(participant_query)
        participant = participant_result.scalar_one_or_none()
        
        if not participant:
            raise NotFoundError("Participant not found in session")
        
        # Get session
        session_query = select(GameSession).where(GameSession.id == session_id)
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        # Check if user can leave
        if session.status in [SessionStatus.ACTIVE, SessionStatus.COMPLETED]:
            raise ConflictError("Cannot leave session that is active or completed")
        
        # Update participant record
        participant.status = ParticipantStatus.DISCONNECTED
        participant.left_at = datetime.utcnow()
        participant.updated_at = datetime.utcnow()
        
        # Update session counts
        if participant.is_spectator:
            session.current_spectators = max(0, session.current_spectators - 1)
        else:
            session.current_participants = max(0, session.current_participants - 1)
            # Refund entry fee if session hasn't started
            if session.status == SessionStatus.WAITING:
                session.total_prize_pool = max(0, session.total_prize_pool - participant.entry_fee_paid)
        
        session.updated_at = datetime.utcnow()
        session.updated_by = current_user.user_id
        
        await db.commit()
        
        logger.info(
            "User left session",
            session_id=session_id,
            user_id=current_user.user_id,
            was_spectator=participant.is_spectator
        )
        
    except (NotFoundError, ConflictError) as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to leave session", session_id=session_id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to leave session"
        )


@router.get("/sessions/{session_id}/participants", response_model=List[SessionParticipantResponse])
async def get_session_participants(
    session_id: UUID,
    include_spectators: bool = Query(True, description="Include spectators in results"),
    db: AsyncSession = Depends(get_read_database_session),
    current_user: Optional[TokenData] = Depends(get_optional_user)
):
    """
    Get session participants
    
    Returns a list of all participants in the specified session.
    """
    try:
        # Verify session exists
        session_query = select(GameSession).where(
            and_(GameSession.id == session_id, GameSession.deleted_at.is_(None))
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise NotFoundError("Session", str(session_id))
        
        # Build participants query
        query = select(SessionParticipant).where(SessionParticipant.session_id == session_id)
        
        if not include_spectators:
            query = query.where(SessionParticipant.is_spectator == False)
        
        query = query.order_by(SessionParticipant.join_order.asc())
        
        # Execute query
        result = await db.execute(query)
        participants = result.scalars().all()
        
        # Convert to response models
        participant_responses = [SessionParticipantResponse.model_validate(p) for p in participants]
        
        logger.info(
            "Retrieved session participants",
            session_id=session_id,
            count=len(participant_responses),
            user_id=current_user.user_id if current_user else None
        )
        
        return participant_responses
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session not found (ID: {session_id})"
        )
    except Exception as e:
        logger.error("Failed to get session participants", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session participants"
        )