"""
BetBet Gaming Engine - API Dependencies
======================================

Common dependencies and utilities for FastAPI endpoints.
"""

import os
from functools import lru_cache
from typing import AsyncGenerator, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic_settings import BaseSettings
import structlog

# Import shared libraries
import sys
from pathlib import Path

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import get_database_write, get_database_read, _db_manager

# Configure logging
logger = structlog.get_logger()
try:
    from shared.core.auth.jwt_handler import get_auth_manager
    from shared.core.auth.models import TokenData
except ImportError:
    # Fallback TokenData class
    class TokenData:
        def __init__(self, user_id: str, username: str = "", email: str = "", roles: list = None):
            self.user_id = user_id
            self.username = username  
            self.email = email
            self.roles = roles or []
    
    def get_auth_manager():
        """Placeholder - should be replaced with proper Clerk integration"""
        class MockAuthManager:
            def verify_token(self, token: str) -> TokenData:
                return TokenData(
                    user_id="550e8400-e29b-41d4-a716-************",  # Valid UUID format
                    username="testuser", 
                    email="<EMAIL>",
                    roles=["user", "admin"]  # Add admin role for testing
                )
        return MockAuthManager()


class Settings(BaseSettings):
    """Application settings"""
    
    # Service configuration
    service_name: str = "gaming-engine"
    service_version: str = "1.0.0"
    environment: str = "development"
    debug: bool = True
    
    # Database configuration  
    database_url: str = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db")
    
    def model_post_init(self, __context) -> None:
        # Convert Railway's postgresql:// to asyncpg format
        if self.database_url.startswith("postgresql://"):
            self.database_url = self.database_url.replace("postgresql://", "postgresql+asyncpg://", 1)
    database_echo: bool = False
    database_pool_size: int = 10
    database_max_overflow: int = 20
    database_pool_timeout: int = 30
    database_pool_recycle: int = 3600
    
    # Redis configuration
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    redis_max_connections: int = 20
    
    # Clerk Authentication
    clerk_publishable_key: str = os.getenv("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "")
    clerk_secret_key: str = os.getenv("CLERK_SECRET_KEY", "")
    
    # Legacy JWT Authentication (for internal services)
    jwt_secret_key: str = "your-super-secret-jwt-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    
    # API configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    api_workers: int = 1
    api_reload: bool = True
    
    # CORS configuration
    cors_origins: list = ["http://localhost:3000", "http://localhost:8080"]
    cors_allow_credentials: bool = True
    cors_allow_methods: list = ["*"]
    cors_allow_headers: list = ["*"]
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    # Business rules
    min_entry_fee: float = 0.01
    max_entry_fee: float = 1000.0
    platform_fee_percentage: float = 0.05
    max_tournament_participants: int = 1024
    session_timeout_minutes: int = 60
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


# Security
security = HTTPBearer(auto_error=False)


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session dependency"""
    async for session in get_database_write():
        yield session


async def get_read_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get read-only database session dependency"""
    async for session in get_database_read():
        yield session


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    settings: Settings = Depends(get_settings)
) -> TokenData:
    """Get current authenticated user"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Use Clerk authentication if configured
        if settings.clerk_secret_key:
            from app.auth.clerk_auth import ClerkAuthManager, ClerkTokenData
            
            clerk_manager = ClerkAuthManager(settings.clerk_secret_key)
            user_data = await clerk_manager.verify_token(credentials.credentials)
            clerk_token_data = ClerkTokenData(user_data)
            
            # Convert to compatible TokenData format
            return TokenData(
                user_id=clerk_token_data.user_id,
                username=clerk_token_data.username,
                email=clerk_token_data.email,
                roles=clerk_token_data.roles
            )
        else:
            # Fallback to shared auth manager or mock
            # Simple mock for testing - just return valid user with proper UUID
            logger.info("Using mock authentication for testing")
            return TokenData(
                user_id="550e8400-e29b-41d4-a716-************",  # Valid UUID format
                username="testuser", 
                email="<EMAIL>",
                roles=["user", "admin"]
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions (already properly formatted)
        raise
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_optional_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    settings: Settings = Depends(get_settings)
) -> Optional[TokenData]:
    """Get current user if authenticated, None otherwise"""
    if not credentials:
        return None
    
    try:
        # Use Clerk authentication if configured
        if settings.clerk_secret_key:
            from app.auth.clerk_auth import ClerkAuthManager, ClerkTokenData
            
            clerk_manager = ClerkAuthManager(settings.clerk_secret_key)
            user_data = await clerk_manager.verify_token(credentials.credentials)
            clerk_token_data = ClerkTokenData(user_data)
            
            # Convert to compatible TokenData format
            return TokenData(
                user_id=clerk_token_data.user_id,
                username=clerk_token_data.username,
                email=clerk_token_data.email,
                roles=clerk_token_data.roles
            )
        else:
            # Fallback to shared auth manager or mock
            auth_manager = get_auth_manager()
            token_data = auth_manager.verify_token(credentials.credentials)
            return token_data
    except Exception:
        return None


def get_admin_user(
    current_user: TokenData = Depends(get_current_user)
) -> TokenData:
    """Require admin user"""
    if "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


def get_game_moderator(
    current_user: TokenData = Depends(get_current_user)
) -> TokenData:
    """Require game moderator or admin"""
    if not any(role in current_user.roles for role in ["admin", "moderator", "game_admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Moderator access required"
        )
    return current_user


# Pagination dependencies
class PaginationParams:
    """Pagination parameters"""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="Page number"),
        limit: int = Query(20, ge=1, le=100, description="Items per page")
    ):
        self.page = page
        self.limit = limit
        self.offset = (page - 1) * limit


def get_pagination(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page")
) -> PaginationParams:
    """Get pagination parameters"""
    return PaginationParams(page, limit)


# Validation helpers
def validate_uuid(uuid_str: str, field_name: str = "ID") -> UUID:
    """Validate UUID string"""
    try:
        return UUID(uuid_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name} format"
        )


def validate_positive_decimal(value: float, field_name: str = "amount") -> float:
    """Validate positive decimal value"""
    if value <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be positive"
        )
    return value


def validate_entry_fee(amount: float, settings: Settings = Depends(get_settings)) -> float:
    """Validate entry fee amount"""
    if amount < settings.min_entry_fee:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Entry fee must be at least ${settings.min_entry_fee}"
        )
    if amount > settings.max_entry_fee:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Entry fee cannot exceed ${settings.max_entry_fee}"
        )
    return amount


# Error handling
class ServiceError(Exception):
    """Base service error"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class NotFoundError(ServiceError):
    """Resource not found error"""
    def __init__(self, resource: str, resource_id: str = None):
        message = f"{resource} not found"
        if resource_id:
            message += f" (ID: {resource_id})"
        super().__init__(message, 404)


class ValidationError(ServiceError):
    """Validation error"""
    def __init__(self, message: str):
        super().__init__(message, 400)


class PermissionError(ServiceError):
    """Permission denied error"""
    def __init__(self, message: str = "Permission denied"):
        super().__init__(message, 403)


class ConflictError(ServiceError):
    """Resource conflict error"""
    def __init__(self, message: str):
        super().__init__(message, 409)


# Rate limiting dependencies
class RateLimitParams:
    """Rate limiting parameters"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute


def get_rate_limit() -> RateLimitParams:
    """Get rate limiting parameters"""
    return RateLimitParams()


# Cache dependencies
class CacheParams:
    """Cache parameters"""
    
    def __init__(self, ttl_seconds: int = 300):
        self.ttl_seconds = ttl_seconds


def get_cache_params() -> CacheParams:
    """Get cache parameters"""
    return CacheParams()


# WebSocket dependencies
async def get_websocket_manager():
    """Get WebSocket manager dependency"""
    from shared.core.messaging.websocket_manager import get_websocket_manager
    return get_websocket_manager()


# Financial dependencies
async def get_financial_manager():
    """Get financial manager dependency"""
    from shared.core.financial.transaction_manager import get_transaction_manager
    return get_transaction_manager()


# Common query parameters
class GameQueryParams:
    """Common game query parameters"""
    
    def __init__(
        self,
        category: Optional[str] = Query(None, description="Filter by game category"),
        is_active: Optional[bool] = Query(None, description="Filter by active status"),
        is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
        has_spectator_betting: Optional[bool] = Query(None, description="Filter by spectator betting"),
        search: Optional[str] = Query(None, min_length=3, description="Search in name and description")
    ):
        self.category = category
        self.is_active = is_active
        self.is_featured = is_featured
        self.has_spectator_betting = has_spectator_betting
        self.search = search


def get_game_query_params(
    category: Optional[str] = Query(None, description="Filter by game category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    has_spectator_betting: Optional[bool] = Query(None, description="Filter by spectator betting"),
    search: Optional[str] = Query(None, min_length=3, description="Search in name and description")
) -> GameQueryParams:
    """Get game query parameters"""
    return GameQueryParams(category, is_active, is_featured, has_spectator_betting, search)


class SessionQueryParams:
    """Common session query parameters"""
    
    def __init__(
        self,
        game_id: Optional[str] = Query(None, description="Filter by game ID"),
        status: Optional[str] = Query(None, description="Filter by session status"),
        min_entry_fee: Optional[float] = Query(None, description="Minimum entry fee"),
        max_entry_fee: Optional[float] = Query(None, description="Maximum entry fee"),
        has_available_slots: Optional[bool] = Query(None, description="Has available slots"),
        starting_soon: Optional[bool] = Query(None, description="Starting within next hour")
    ):
        self.game_id = validate_uuid(game_id, "Game ID") if game_id else None
        self.status = status
        self.min_entry_fee = min_entry_fee
        self.max_entry_fee = max_entry_fee
        self.has_available_slots = has_available_slots
        self.starting_soon = starting_soon


def get_session_query_params(
    game_id: Optional[str] = Query(None, description="Filter by game ID"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    min_entry_fee: Optional[float] = Query(None, description="Minimum entry fee"),
    max_entry_fee: Optional[float] = Query(None, description="Maximum entry fee"),
    has_available_slots: Optional[bool] = Query(None, description="Has available slots"),
    starting_soon: Optional[bool] = Query(None, description="Starting within next hour")
) -> SessionQueryParams:
    """Get session query parameters"""
    return SessionQueryParams(game_id, status, min_entry_fee, max_entry_fee, has_available_slots, starting_soon)