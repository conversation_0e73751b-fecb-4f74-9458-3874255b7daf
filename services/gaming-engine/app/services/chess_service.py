"""
BetBet Gaming Engine - Chess Service
===================================

Chess game service using python-chess library for move validation and game logic.
"""

import asyncio
import chess
import chess.engine
import chess.pgn
import chess.polyglot
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Optional, Dict, Any, List, Tuple
from uuid import UUID, uuid4
import json
import logging

from ..models.chess import (
    ChessGameConfig, ChessGameState, ChessMove, ChessGameCreate,
    ChessMoveRequest, ChessGameAction, ChessGameResponse, GameResult
)

logger = logging.getLogger(__name__)


class ChessService:
    """Chess game service with python-chess integration"""
    
    def __init__(self):
        self.active_games: Dict[UUID, chess.Board] = {}
        self.game_timers: Dict[UUID, Dict[str, Any]] = {}

        # Initialize test game for WebSocket testing
        self._initialize_test_game()

    def _initialize_test_game(self):
        """Initialize the test chess game for WebSocket testing"""
        test_game_id = UUID("550e8400-e29b-41d4-a716-************")

        # Create a standard chess board
        board = chess.Board()

        # Store the board
        self.active_games[test_game_id] = board

        # Initialize timers
        self.game_timers[test_game_id] = {
            "white_time": 300,  # 5 minutes
            "black_time": 300,  # 5 minutes
            "increment": 3,     # 3 seconds increment
            "last_move_time": datetime.utcnow(),
            "is_running": False
        }

        logger.info(f"Initialized test chess game {test_game_id}")

    async def create_game(self, creator_id: UUID, game_data: ChessGameCreate) -> UUID:
        """Create a new chess game"""
        game_id = uuid4()
        
        # Initialize chess board based on variant
        if game_data.config.variant == "chess960":
            # Generate random Chess960 starting position
            board = chess.Board(chess960=True)
        elif game_data.config.starting_fen:
            # Custom starting position
            board = chess.Board(game_data.config.starting_fen)
        else:
            # Standard chess
            board = chess.Board()
        
        # Store the board
        self.active_games[game_id] = board
        
        # Initialize timers
        self.game_timers[game_id] = {
            "white_time": game_data.config.initial_time_seconds,
            "black_time": game_data.config.initial_time_seconds,
            "increment": game_data.config.increment_seconds,
            "last_move_time": datetime.utcnow(),
            "is_running": False
        }
        
        logger.info(f"Created chess game {game_id} with variant {game_data.config.variant}")
        return game_id
    
    async def make_move(self, game_id: UUID, player_id: UUID, move_request: ChessMoveRequest) -> Tuple[bool, str, Optional[ChessMove]]:
        """Make a move in a chess game"""
        if game_id not in self.active_games:
            return False, "Game not found", None
        
        board = self.active_games[game_id]
        
        try:
            # Parse the move
            move = chess.Move.from_uci(move_request.move_uci)
            
            # Validate the move is legal
            if move not in board.legal_moves:
                legal_moves = [str(m) for m in board.legal_moves]
                return False, f"Illegal move. Legal moves: {legal_moves[:10]}...", None
            
            # Store position before move
            position_before = board.fen()
            
            # Make the move
            board.push(move)
            
            # Get position after move
            position_after = board.fen()
            
            # Update timers
            current_time = datetime.utcnow()
            timer_data = self.game_timers[game_id]
            
            if timer_data["is_running"]:
                elapsed = (current_time - timer_data["last_move_time"]).total_seconds()
                
                # Deduct time from current player and add increment
                if board.turn == chess.BLACK:  # White just moved
                    timer_data["white_time"] = max(0, timer_data["white_time"] - elapsed + timer_data["increment"])
                else:  # Black just moved
                    timer_data["black_time"] = max(0, timer_data["black_time"] - elapsed + timer_data["increment"])
            
            timer_data["last_move_time"] = current_time
            timer_data["is_running"] = True
            
            # Create move object
            chess_move = ChessMove(
                move_uci=move_request.move_uci,
                move_san=board.san(move),
                time_taken_ms=0,  # This would be calculated from client timing
                is_capture=board.is_capture(move),
                is_check=board.is_check(),
                is_checkmate=board.is_checkmate(),
                is_castling=board.is_castling(move),
                is_en_passant=board.is_en_passant(move),
                is_promotion=move.promotion is not None,
                promotion_piece=chess.piece_name(move.promotion).upper() if move.promotion else None,
                position_before=position_before,
                position_after=position_after
            )
            
            # Handle draw offers
            if move_request.offer_draw:
                # Store draw offer in game state
                pass
            
            # Check for game end conditions
            if board.is_checkmate():
                winner = "white" if board.turn == chess.BLACK else "black"
                logger.info(f"Game {game_id} ended: {winner} wins by checkmate")
            elif board.is_stalemate():
                logger.info(f"Game {game_id} ended: stalemate")
            elif board.is_insufficient_material():
                logger.info(f"Game {game_id} ended: insufficient material")
            elif board.can_claim_threefold_repetition():
                logger.info(f"Game {game_id}: threefold repetition available")
            elif board.can_claim_fifty_moves():
                logger.info(f"Game {game_id}: fifty move rule available")
            
            return True, "Move successful", chess_move
            
        except ValueError as e:
            return False, f"Invalid move format: {str(e)}", None
        except Exception as e:
            logger.error(f"Error making move in game {game_id}: {str(e)}")
            return False, f"Error processing move: {str(e)}", None
    
    async def get_game_state(self, game_id: UUID) -> Optional[ChessGameState]:
        """Get current game state"""
        if game_id not in self.active_games:
            # Try to load game from database
            await self._load_game_from_database(game_id)
            if game_id not in self.active_games:
                return None
        
        board = self.active_games[game_id]
        timer_data = self.game_timers.get(game_id, {})
        
        # Update time remaining if game is running
        current_time = datetime.utcnow()
        white_time = timer_data.get("white_time", 0)
        black_time = timer_data.get("black_time", 0)
        
        if timer_data.get("is_running", False):
            elapsed = (current_time - timer_data["last_move_time"]).total_seconds()
            if board.turn == chess.WHITE:
                white_time = max(0, white_time - elapsed)
            else:
                black_time = max(0, black_time - elapsed)
        
        # Get move history
        moves = []
        move_times = []
        temp_board = chess.Board()
        
        # Replay game to get move history
        for move in board.move_stack:
            moves.append(temp_board.san(move))
            move_times.append(1000)  # Placeholder - would store actual times
            temp_board.push(move)
        
        # Get captured pieces
        captured_pieces = {"white": [], "black": []}
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece is None:
                # Check if a piece was captured here (would need move history analysis)
                pass
        
        # Identify opening if still in opening phase
        opening_name = None
        opening_eco = None
        if len(moves) <= 15:  # Rough opening phase
            try:
                # This would require an opening database lookup
                opening_name = self._identify_opening(moves)
            except:
                pass
        
        return ChessGameState(
            fen=board.fen(),
            moves=moves,
            move_times=move_times,
            white_time_remaining=int(white_time),
            black_time_remaining=int(black_time),
            current_turn="white" if board.turn == chess.WHITE else "black",
            move_number=board.fullmove_number,
            halfmove_clock=board.halfmove_clock,
            is_check=board.is_check(),
            is_checkmate=board.is_checkmate(),
            is_stalemate=board.is_stalemate(),
            captured_pieces=captured_pieces,
            opening_name=opening_name,
            opening_eco=opening_eco
        )
    
    async def resign_game(self, game_id: UUID, player_id: UUID) -> Tuple[bool, str]:
        """Player resigns the game"""
        if game_id not in self.active_games:
            return False, "Game not found"
        
        # Stop the timer
        if game_id in self.game_timers:
            self.game_timers[game_id]["is_running"] = False
        
        logger.info(f"Player {player_id} resigned game {game_id}")
        return True, "Resignation successful"
    
    async def offer_draw(self, game_id: UUID, player_id: UUID) -> Tuple[bool, str]:
        """Player offers a draw"""
        if game_id not in self.active_games:
            return False, "Game not found"
        
        logger.info(f"Player {player_id} offered draw in game {game_id}")
        return True, "Draw offer sent"
    
    async def accept_draw(self, game_id: UUID, player_id: UUID) -> Tuple[bool, str]:
        """Player accepts a draw offer"""
        if game_id not in self.active_games:
            return False, "Game not found"
        
        # Stop the timer
        if game_id in self.game_timers:
            self.game_timers[game_id]["is_running"] = False
        
        logger.info(f"Player {player_id} accepted draw in game {game_id}")
        return True, "Draw accepted"
    
    async def claim_draw(self, game_id: UUID, player_id: UUID, claim_type: str) -> Tuple[bool, str]:
        """Player claims a draw (threefold repetition, fifty move rule)"""
        if game_id not in self.active_games:
            return False, "Game not found"
        
        board = self.active_games[game_id]
        
        if claim_type == "threefold_repetition":
            if board.can_claim_threefold_repetition():
                self.game_timers[game_id]["is_running"] = False
                return True, "Draw claimed by threefold repetition"
            else:
                return False, "Threefold repetition not available"
        
        elif claim_type == "fifty_move_rule":
            if board.can_claim_fifty_moves():
                self.game_timers[game_id]["is_running"] = False
                return True, "Draw claimed by fifty move rule"
            else:
                return False, "Fifty move rule not available"
        
        return False, "Invalid draw claim type"
    
    async def get_legal_moves(self, game_id: UUID) -> List[str]:
        """Get all legal moves for current position"""
        if game_id not in self.active_games:
            return []
        
        board = self.active_games[game_id]
        return [move.uci() for move in board.legal_moves]
    
    async def analyze_position(self, game_id: UUID, depth: int = 15) -> Optional[Dict[str, Any]]:
        """Analyze position with chess engine"""
        if game_id not in self.active_games:
            return None
        
        board = self.active_games[game_id]
        
        try:
            # This would require a chess engine like Stockfish
            # For now, return basic position info
            return {
                "fen": board.fen(),
                "legal_moves": len(list(board.legal_moves)),
                "is_check": board.is_check(),
                "is_checkmate": board.is_checkmate(),
                "is_stalemate": board.is_stalemate(),
                "can_claim_draw": board.can_claim_draw(),
                "halfmove_clock": board.halfmove_clock,
                "fullmove_number": board.fullmove_number
            }
        except Exception as e:
            logger.error(f"Error analyzing position for game {game_id}: {str(e)}")
            return None
    
    async def check_time_forfeit(self, game_id: UUID) -> Tuple[bool, Optional[str]]:
        """Check if a player has forfeited on time"""
        if game_id not in self.active_games or game_id not in self.game_timers:
            return False, None
        
        timer_data = self.game_timers[game_id]
        if not timer_data.get("is_running", False):
            return False, None
        
        current_time = datetime.utcnow()
        elapsed = (current_time - timer_data["last_move_time"]).total_seconds()
        
        board = self.active_games[game_id]
        
        if board.turn == chess.WHITE:
            remaining_time = timer_data["white_time"] - elapsed
            if remaining_time <= 0:
                timer_data["is_running"] = False
                return True, "white_timeout"
        else:
            remaining_time = timer_data["black_time"] - elapsed
            if remaining_time <= 0:
                timer_data["is_running"] = False
                return True, "black_timeout"
        
        return False, None
    
    async def get_pgn(self, game_id: UUID) -> Optional[str]:
        """Get PGN representation of the game"""
        if game_id not in self.active_games:
            return None
        
        board = self.active_games[game_id]
        
        # Create PGN game
        game = chess.pgn.Game()
        
        # Add headers
        game.headers["Event"] = "BetBet Chess Game"
        game.headers["Site"] = "BetBet Platform"
        game.headers["Date"] = datetime.utcnow().strftime("%Y.%m.%d")
        game.headers["Round"] = "1"
        
        # Add moves
        node = game
        temp_board = chess.Board()
        
        for move in board.move_stack:
            node = node.add_variation(move)
            temp_board.push(move)
        
        return str(game)

    async def _load_game_from_database(self, game_id: UUID):
        """Load a chess game from database into active games"""
        try:
            from sqlalchemy import text
            from app.api.dependencies import get_database_session

            # Get database session
            async for db in get_database_session():
                # Query chess game from database
                query = text("""
                    SELECT
                        cg.id,
                        cg.current_fen,
                        cg.white_time_remaining,
                        cg.black_time_remaining,
                        cg.status
                    FROM chess_engine.chess_games cg
                    WHERE cg.id = :game_id
                """)

                result = await db.execute(query, {"game_id": str(game_id)})
                row = result.fetchone()

                if row:
                    # Create chess board from FEN
                    board = chess.Board(row.current_fen)

                    # Add to active games
                    self.active_games[game_id] = board

                    # Set up timer data
                    self.game_timers[game_id] = {
                        "white_time": row.white_time_remaining or 300,
                        "black_time": row.black_time_remaining or 300,
                        "is_running": row.status == "active",
                        "last_move_time": datetime.utcnow()
                    }

                    logger.info(f"Loaded chess game {game_id} from database")
                break

        except Exception as e:
            logger.error(f"Failed to load chess game {game_id} from database: {e}")

    def _identify_opening(self, moves: List[str]) -> Optional[str]:
        """Identify chess opening from move list"""
        # This would require an opening database
        # For now, identify a few common openings
        move_string = " ".join(moves[:6])  # First 6 moves
        
        openings = {
            "e4 e5 Nf3 Nc6 Bc4": "Italian Game",
            "e4 e5 Nf3 Nc6 Bb5": "Ruy Lopez",
            "d4 d5 c4": "Queen's Gambit",
            "e4 c5": "Sicilian Defense",
            "e4 e6": "French Defense",
            "e4 c6": "Caro-Kann Defense",
            "d4 Nf6 c4 g6": "King's Indian Defense",
            "Nf3 d5 g3": "Reti Opening"
        }
        
        for opening_moves, opening_name in openings.items():
            if move_string.startswith(opening_moves):
                return opening_name
        
        return None
    
    async def validate_fen(self, fen: str) -> bool:
        """Validate FEN string"""
        try:
            chess.Board(fen)
            return True
        except ValueError:
            return False
    
    async def cleanup_game(self, game_id: UUID):
        """Clean up game resources"""
        if game_id in self.active_games:
            del self.active_games[game_id]
        if game_id in self.game_timers:
            del self.game_timers[game_id]
        logger.info(f"Cleaned up game {game_id}")


# Global chess service instance
chess_service = ChessService()