"""
BetBet Gaming Engine - Spectator Betting Models
===============================================

Pydantic models for spectator betting functionality and odds management.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, validator, ConfigDict


class BetType(str, Enum):
    """Bet type enumeration"""
    MATCH_WINNER = "match_winner"
    SESSION_WINNER = "session_winner"
    TOURNAMENT_WINNER = "tournament_winner"
    SCORE_OVER_UNDER = "score_over_under"
    FIRST_TO_SCORE = "first_to_score"
    ELIMINATION_ORDER = "elimination_order"
    PERFORMANCE_PROP = "performance_prop"
    CUSTOM = "custom"


class BetStatus(str, Enum):
    """Bet status enumeration"""
    PENDING = "pending"
    ACTIVE = "active"
    WINNING = "winning"
    LOSING = "losing"
    SETTLED = "settled"
    VOIDED = "voided"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class OddsFormat(str, Enum):
    """Odds format enumeration"""
    DECIMAL = "decimal"
    FRACTIONAL = "fractional"
    AMERICAN = "american"
    PERCENTAGE = "percentage"


class SpectatorBetCreate(BaseModel):
    """Spectator bet creation model - matches SQLAlchemy schema"""
    session_id: UUID = Field(..., description="Game session ID")
    bet_type: str = Field(..., description="Type of bet")
    bet_data: Dict[str, Any] = Field(default_factory=dict, description="Bet details and target")
    stake_amount: Decimal = Field(..., gt=0, description="Bet stake amount")


class SpectatorBet(BaseModel):
    """Complete spectator bet model - matches SQLAlchemy schema"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    session_id: UUID
    user_id: UUID
    bet_type: str
    bet_data: Dict[str, Any]
    stake_amount: Decimal
    potential_payout: Decimal
    actual_payout: Decimal
    odds_at_placement: Decimal
    market_data: Dict[str, Any]
    status: str
    placed_at: datetime
    settled_at: Optional[datetime]
    updated_at: datetime


class SpectatorBetResponse(BaseModel):
    """Spectator bet response model - matches SQLAlchemy schema"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    session_id: UUID
    user_id: UUID
    bet_type: str
    bet_data: Dict[str, Any]
    stake_amount: Decimal
    potential_payout: Decimal
    actual_payout: Decimal
    odds_at_placement: Decimal
    market_data: Dict[str, Any]
    status: str
    placed_at: datetime
    settled_at: Optional[datetime]
    updated_at: datetime


class BettingMarket(BaseModel):
    """Betting market model for a specific event"""
    market_id: str
    market_type: str
    market_name: str
    description: str
    is_active: bool
    options: List[Dict[str, Any]]
    min_stake: Decimal
    max_stake: Decimal
    market_data: Dict[str, Any] = Field(default_factory=dict)


class OddsCalculation(BaseModel):
    """Odds calculation model"""
    market_id: str = Field(..., description="Market identifier")
    bet_type: BetType = Field(..., description="Bet type")
    
    # Base odds
    true_odds: Dict[str, Decimal] = Field(default_factory=dict, description="True probability odds")
    display_odds: Dict[str, Decimal] = Field(default_factory=dict, description="Display odds with margin")
    
    # Factors
    house_edge: Decimal = Field(Decimal('0.05'), description="House edge percentage")
    liquidity_factor: Decimal = Field(Decimal('1.0'), description="Liquidity adjustment factor")
    risk_factor: Decimal = Field(Decimal('1.0'), description="Risk adjustment factor")
    
    # Market data
    total_volume: Decimal = Field(Decimal('0'), description="Total betting volume")
    volume_distribution: Dict[str, Decimal] = Field(default_factory=dict, description="Volume by outcome")
    
    # Real-time adjustments
    momentum_factor: Decimal = Field(Decimal('1.0'), description="Game momentum factor")
    participant_performance: Dict[str, Any] = Field(default_factory=dict, description="Live performance data")
    
    # Calculation metadata
    calculated_at: datetime = Field(..., description="Calculation timestamp")
    algorithm_version: str = Field("1.0", description="Odds algorithm version")
    confidence_score: float = Field(1.0, description="Calculation confidence score")


class BettingSearchFilter(BaseModel):
    """Betting search and filter model"""
    # Event filters
    session_id: Optional[UUID] = Field(None, description="Filter by session")
    tournament_id: Optional[UUID] = Field(None, description="Filter by tournament")
    game_id: Optional[UUID] = Field(None, description="Filter by game")
    
    # Bet filters
    bet_type: Optional[BetType] = Field(None, description="Filter by bet type")
    status: Optional[BetStatus] = Field(None, description="Filter by status")
    bettor_user_id: Optional[UUID] = Field(None, description="Filter by bettor")
    
    # Amount filters
    min_bet_amount: Optional[Decimal] = Field(None, description="Minimum bet amount")
    max_bet_amount: Optional[Decimal] = Field(None, description="Maximum bet amount")
    min_potential_payout: Optional[Decimal] = Field(None, description="Minimum potential payout")
    
    # Time filters
    placed_after: Optional[datetime] = Field(None, description="Placed after timestamp")
    placed_before: Optional[datetime] = Field(None, description="Placed before timestamp")
    settled_only: Optional[bool] = Field(None, description="Only settled bets")
    
    # Outcome filters
    winning_bets_only: Optional[bool] = Field(None, description="Only winning bets")
    
    # Sorting
    sort_by: str = Field("placed_at", description="Sort field")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['placed_at', 'bet_amount', 'potential_payout', 'actual_payout', 'settled_at']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of: {allowed_fields}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order must be "asc" or "desc"')
        return v


class BettingListResponse(BaseModel):
    """Paginated betting list response"""
    bets: List[SpectatorBetResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class BettingStats(BaseModel):
    """Betting statistics model"""
    # Event identification
    session_id: Optional[UUID] = None
    tournament_id: Optional[UUID] = None
    game_id: Optional[UUID] = None
    
    # Volume stats
    total_bets: int = Field(0, description="Total number of bets")
    total_volume: Decimal = Field(Decimal('0'), description="Total betting volume")
    total_payouts: Decimal = Field(Decimal('0'), description="Total payouts")
    platform_revenue: Decimal = Field(Decimal('0'), description="Platform revenue")
    
    # Bet type breakdown
    bets_by_type: Dict[str, int] = Field(default_factory=dict, description="Bets by type")
    volume_by_type: Dict[str, Decimal] = Field(default_factory=dict, description="Volume by type")
    
    # Performance stats
    win_rate: float = Field(0.0, description="Overall win rate")
    average_bet_amount: Decimal = Field(Decimal('0'), description="Average bet amount")
    average_payout: Decimal = Field(Decimal('0'), description="Average payout")
    
    # Popular markets
    most_popular_bets: List[Dict[str, Any]] = Field(default_factory=list, description="Most popular bet types")
    highest_volume_markets: List[Dict[str, Any]] = Field(default_factory=list, description="Highest volume markets")
    
    # Real-time data
    active_bets: int = Field(0, description="Currently active bets")
    pending_payouts: Decimal = Field(Decimal('0'), description="Pending payout amount")
    
    # Time period
    stats_period_start: Optional[datetime] = Field(None, description="Statistics period start")
    stats_period_end: Optional[datetime] = Field(None, description="Statistics period end")
    last_updated: datetime = Field(..., description="Last update timestamp")


class LiveOdds(BaseModel):
    """Live odds update model for real-time streaming"""
    market_id: str = Field(..., description="Market identifier")
    event_id: UUID = Field(..., description="Event ID (session or tournament)")
    
    # Current odds
    current_odds: Dict[str, Decimal] = Field(..., description="Current odds")
    previous_odds: Dict[str, Decimal] = Field(default_factory=dict, description="Previous odds")
    
    # Changes
    odds_movement: Dict[str, Decimal] = Field(default_factory=dict, description="Odds movement")
    significant_change: bool = Field(False, description="Significant odds change")
    
    # Market state
    market_open: bool = Field(True, description="Market is open")
    time_to_deadline: Optional[int] = Field(None, description="Seconds until betting deadline")
    
    # Volume data
    recent_bet_volume: Decimal = Field(Decimal('0'), description="Recent betting volume")
    volume_trend: str = Field("stable", description="Volume trend (increasing/decreasing/stable)")
    
    # Update metadata
    update_timestamp: datetime = Field(..., description="Update timestamp")
    update_sequence: int = Field(..., description="Update sequence number")
    update_reason: str = Field("scheduled", description="Reason for update")