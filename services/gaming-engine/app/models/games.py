"""
BetBet Gaming Engine - Game Models
==================================

Pydantic models for game management and configuration.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, Field, validator, ConfigDict


class GameCategory(str):
    """Game category enumeration"""
    TRIVIA = "trivia"
    REACTION_TIME = "reaction_time"
    STRATEGY = "strategy"
    SPORTS = "sports"
    PUZZLE = "puzzle"
    CUSTOM = "custom"


class ScoringSystem(str):
    """Scoring system enumeration"""
    POINTS = "points"
    TIME = "time"
    ACCURACY = "accuracy"
    CUSTOM = "custom"


class GameBase(BaseModel):
    """Base game model with common fields"""
    name: str = Field(..., min_length=3, max_length=200, description="Game name")
    slug: str = Field(..., min_length=3, max_length=100, description="URL-friendly game identifier")
    category: str = Field(..., description="Game category")
    description: Optional[str] = Field(None, max_length=1000, description="Game description")
    cover_image: Optional[str] = Field(None, description="URL to game cover image for UI enhancement")
    
    # Game configuration
    game_config: Dict[str, Any] = Field(default_factory=dict, description="Game-specific configuration")
    rules_text: Optional[str] = Field(None, description="Game rules and instructions")
    instructions: Optional[str] = Field(None, description="Player instructions")
    
    # Player limits
    min_players: int = Field(2, ge=1, le=20, description="Minimum number of players")
    max_players: int = Field(10, ge=1, le=20, description="Maximum number of players") 
    min_skill_level: int = Field(1, ge=1, le=10, description="Minimum skill level required")
    estimated_duration_minutes: int = Field(10, ge=1, le=180, description="Estimated game duration")
    
    # Game mechanics
    scoring_system: str = Field("points", description="Scoring system type")
    has_spectator_betting: bool = Field(True, description="Allow spectator betting")
    allows_practice_mode: bool = Field(True, description="Allow practice mode")
    
    # Status
    is_active: bool = Field(True, description="Game is active and available")
    is_featured: bool = Field(False, description="Featured game")
    is_beta: bool = Field(False, description="Beta/experimental game")
    
    # Plugin system
    plugin_name: Optional[str] = Field(None, max_length=100, description="Plugin name")
    plugin_version: Optional[str] = Field(None, max_length=20, description="Plugin version")
    plugin_config: Dict[str, Any] = Field(default_factory=dict, description="Plugin configuration")
    
    @validator('category')
    def validate_category(cls, v):
        allowed_categories = ['trivia', 'reaction_time', 'strategy', 'sports', 'puzzle', 'custom']
        if v not in allowed_categories:
            raise ValueError(f'Category must be one of: {allowed_categories}')
        return v
    
    @validator('scoring_system')
    def validate_scoring_system(cls, v):
        allowed_systems = ['points', 'time', 'accuracy', 'custom']
        if v not in allowed_systems:
            raise ValueError(f'Scoring system must be one of: {allowed_systems}')
        return v
    
    @validator('max_players')
    def validate_player_limits(cls, v, values):
        if 'min_players' in values and v < values['min_players']:
            raise ValueError('max_players must be >= min_players')
        return v
    
    @validator('slug')
    def validate_slug(cls, v):
        import re
        if not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError('Slug must contain only lowercase letters, numbers, and hyphens')
        return v


class GameCreate(GameBase):
    """Game creation model"""
    pass


class GameUpdate(BaseModel):
    """Game update model - all fields optional"""
    name: Optional[str] = Field(None, min_length=3, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    cover_image: Optional[str] = Field(None, description="URL to game cover image")
    game_config: Optional[Dict[str, Any]] = None
    rules_text: Optional[str] = None
    instructions: Optional[str] = None
    min_players: Optional[int] = Field(None, ge=1, le=20)
    max_players: Optional[int] = Field(None, ge=1, le=20)
    min_skill_level: Optional[int] = Field(None, ge=1, le=10)
    estimated_duration_minutes: Optional[int] = Field(None, ge=1, le=180)
    scoring_system: Optional[str] = None
    has_spectator_betting: Optional[bool] = None
    allows_practice_mode: Optional[bool] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    is_beta: Optional[bool] = None
    plugin_config: Optional[Dict[str, Any]] = None


class Game(GameBase):
    """Complete game model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Unique game identifier")
    
    # Analytics fields
    total_sessions_played: int = Field(0, description="Total sessions played")
    average_session_duration: Optional[Decimal] = Field(None, description="Average session duration")
    average_players_per_session: Optional[Decimal] = Field(None, description="Average players per session")
    popularity_score: Decimal = Field(Decimal('0'), description="Popularity score")
    
    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: UUID = Field(..., description="Creator user ID")
    updated_by: UUID = Field(..., description="Last updater user ID")
    version: int = Field(1, description="Record version")
    
    # Soft delete
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by: Optional[UUID] = Field(None, description="Deleter user ID")


class GameResponse(BaseModel):
    """Game response model for API endpoints"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    name: str
    slug: str
    category: str
    description: Optional[str]
    cover_image: Optional[str]
    
    # Player limits
    min_players: int
    max_players: int
    min_skill_level: int
    estimated_duration_minutes: int
    
    # Game mechanics
    scoring_system: str
    has_spectator_betting: bool
    allows_practice_mode: bool
    
    # Status
    is_active: bool
    is_featured: bool
    is_beta: bool
    
    # Analytics
    total_sessions_played: int
    average_session_duration: Optional[Decimal]
    average_players_per_session: Optional[Decimal]
    popularity_score: Decimal
    
    # Timestamps
    created_at: datetime
    updated_at: datetime


class GameStats(BaseModel):
    """Game statistics model"""
    game_id: UUID
    total_sessions: int = Field(0, description="Total sessions played")
    active_sessions: int = Field(0, description="Currently active sessions")
    total_players: int = Field(0, description="Total unique players")
    average_duration: Optional[float] = Field(None, description="Average session duration in minutes")
    popularity_score: float = Field(0.0, description="Popularity score")
    
    # Performance metrics
    average_players_per_session: float = Field(0.0, description="Average players per session")
    completion_rate: float = Field(0.0, description="Session completion rate")
    
    # Recent activity
    sessions_last_24h: int = Field(0, description="Sessions in last 24 hours")
    sessions_last_week: int = Field(0, description="Sessions in last week")


class GameSearchFilter(BaseModel):
    """Game search and filter model"""
    category: Optional[str] = Field(None, description="Filter by category")
    min_players: Optional[int] = Field(None, description="Minimum players required")
    max_players: Optional[int] = Field(None, description="Maximum players allowed")
    has_spectator_betting: Optional[bool] = Field(None, description="Has spectator betting")
    is_featured: Optional[bool] = Field(None, description="Featured games only")
    is_active: Optional[bool] = Field(True, description="Active games only")
    search: Optional[str] = Field(None, description="Search in name and description")
    
    # Sorting
    sort_by: str = Field("popularity_score", description="Sort field")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['name', 'popularity_score', 'total_sessions_played', 'created_at', 'updated_at']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of: {allowed_fields}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order must be "asc" or "desc"')
        return v


class GameListResponse(BaseModel):
    """Paginated game list response"""
    games: List[GameResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool