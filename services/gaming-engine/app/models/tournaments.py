"""
BetBet Gaming Engine - Tournament Models
=======================================

Pydantic models for tournament management and bracket systems.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, validator, ConfigDict


class TournamentFormat(str, Enum):
    """Tournament format enumeration"""
    SINGLE_ELIMINATION = "single_elimination"
    DOUBLE_ELIMINATION = "double_elimination"
    ROUND_ROBIN = "round_robin"
    SWISS = "swiss"
    LADDER = "ladder"
    CUSTOM = "custom"


class TournamentStatus(str, Enum):
    """Tournament status enumeration"""
    DRAFT = "draft"
    REGISTRATION_OPEN = "registration_open"
    REGISTRATION_CLOSED = "registration_closed"
    STARTING = "starting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    POSTPONED = "postponed"


class ParticipantTournamentStatus(str, Enum):
    """Tournament participant status enumeration"""
    REGISTERED = "registered"
    CONFIRMED = "confirmed"
    ACTIVE = "active"
    ELIMINATED = "eliminated"
    ADVANCED = "advanced"
    CHAMPION = "champion"
    WITHDRAWN = "withdrawn"
    DISQUALIFIED = "disqualified"


class TournamentBase(BaseModel):
    """Base tournament model"""
    name: str = Field(..., min_length=3, max_length=200, description="Tournament name")
    slug: str = Field(..., min_length=3, max_length=100, description="URL-friendly identifier")
    game_id: UUID = Field(..., description="Game ID")
    
    # Tournament configuration
    format: TournamentFormat = Field(..., description="Tournament format")
    description: Optional[str] = Field(None, max_length=2000, description="Tournament description")
    rules: Optional[str] = Field(None, description="Tournament-specific rules")
    
    # Participation
    max_participants: int = Field(64, ge=4, le=1024, description="Maximum participants")
    min_participants: int = Field(4, ge=2, le=64, description="Minimum participants")
    entry_fee: Decimal = Field(Decimal('0'), ge=0, description="Entry fee per participant")
    
    # Prize structure
    prize_pool: Decimal = Field(Decimal('0'), ge=0, description="Total prize pool")
    prize_distribution: Dict[str, Decimal] = Field(default_factory=dict, description="Prize distribution by rank")
    
    # Scheduling
    registration_start: datetime = Field(..., description="Registration start time")
    registration_end: datetime = Field(..., description="Registration end time")
    tournament_start: datetime = Field(..., description="Tournament start time")
    estimated_duration_hours: int = Field(4, ge=1, le=72, description="Estimated duration")
    
    # Features
    allow_spectators: bool = Field(True, description="Allow spectators")
    is_featured: bool = Field(False, description="Featured tournament")
    is_private: bool = Field(False, description="Private tournament")
    requires_approval: bool = Field(False, description="Requires manual approval")
    
    # Configuration
    tournament_config: Dict[str, Any] = Field(default_factory=dict, description="Tournament configuration")
    
    @validator('slug')
    def validate_slug(cls, v):
        import re
        if not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError('Slug must contain only lowercase letters, numbers, and hyphens')
        return v
    
    @validator('max_participants')
    def validate_participant_limits(cls, v, values):
        if 'min_participants' in values and v < values['min_participants']:
            raise ValueError('max_participants must be >= min_participants')
        return v
    
    @validator('registration_end')
    def validate_registration_timing(cls, v, values):
        if 'registration_start' in values and v <= values['registration_start']:
            raise ValueError('registration_end must be after registration_start')
        return v
    
    @validator('tournament_start')
    def validate_tournament_timing(cls, v, values):
        if 'registration_end' in values and v <= values['registration_end']:
            raise ValueError('tournament_start must be after registration_end')
        return v


class TournamentCreate(TournamentBase):
    """Tournament creation model"""
    created_by: UUID = Field(..., description="Creator user ID")


class TournamentUpdate(BaseModel):
    """Tournament update model"""
    name: Optional[str] = Field(None, min_length=3, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    rules: Optional[str] = None
    max_participants: Optional[int] = Field(None, ge=4, le=1024)
    entry_fee: Optional[Decimal] = Field(None, ge=0)
    prize_pool: Optional[Decimal] = Field(None, ge=0)
    prize_distribution: Optional[Dict[str, Decimal]] = None
    registration_start: Optional[datetime] = None
    registration_end: Optional[datetime] = None
    tournament_start: Optional[datetime] = None
    estimated_duration_hours: Optional[int] = Field(None, ge=1, le=72)
    allow_spectators: Optional[bool] = None
    is_featured: Optional[bool] = None
    is_private: Optional[bool] = None
    requires_approval: Optional[bool] = None
    status: Optional[TournamentStatus] = None
    tournament_config: Optional[Dict[str, Any]] = None


class Tournament(TournamentBase):
    """Complete tournament model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Tournament ID")
    
    # Status
    status: TournamentStatus = Field(TournamentStatus.DRAFT, description="Tournament status")
    current_participants: int = Field(0, description="Current participant count")
    current_round: int = Field(0, description="Current round number")
    
    # Timing
    actual_start_time: Optional[datetime] = Field(None, description="Actual start time")
    actual_end_time: Optional[datetime] = Field(None, description="Actual end time")
    actual_duration_hours: Optional[Decimal] = Field(None, description="Actual duration")
    
    # Financial
    total_entry_fees: Decimal = Field(Decimal('0'), description="Total entry fees collected")
    platform_fee: Decimal = Field(Decimal('0'), description="Platform fee collected")
    remaining_prize_pool: Decimal = Field(Decimal('0'), description="Remaining prize pool")
    
    # Results
    champion_user_id: Optional[UUID] = Field(None, description="Tournament champion")
    runner_up_user_id: Optional[UUID] = Field(None, description="Runner-up")
    final_standings: List[Dict[str, Any]] = Field(default_factory=list, description="Final tournament standings")
    
    # Bracket/Structure
    bracket_structure: Dict[str, Any] = Field(default_factory=dict, description="Tournament bracket structure")
    current_matches: List[UUID] = Field(default_factory=list, description="Current active match IDs")
    
    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: UUID = Field(..., description="Creator user ID")
    updated_by: UUID = Field(..., description="Last updater user ID")
    version: int = Field(1, description="Record version")
    
    # Soft delete
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by: Optional[UUID] = Field(None, description="Deleter user ID")


class TournamentResponse(BaseModel):
    """Tournament response model for API endpoints"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    name: str
    slug: str
    game_id: UUID
    format: TournamentFormat
    status: TournamentStatus
    description: Optional[str]
    
    # Participation
    max_participants: int
    min_participants: int
    current_participants: int
    entry_fee: Decimal
    
    # Prize structure
    prize_pool: Decimal
    total_entry_fees: Decimal
    
    # Scheduling
    registration_start: datetime
    registration_end: datetime
    tournament_start: datetime
    estimated_duration_hours: int
    actual_start_time: Optional[datetime]
    actual_end_time: Optional[datetime]
    
    # Progress
    current_round: int
    
    # Features
    allow_spectators: bool
    is_featured: bool
    is_private: bool
    requires_approval: bool
    
    # Results
    champion_user_id: Optional[UUID]
    runner_up_user_id: Optional[UUID]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime


class TournamentParticipantBase(BaseModel):
    """Base tournament participant model"""
    tournament_id: UUID = Field(..., description="Tournament ID")
    user_id: UUID = Field(..., description="User ID")
    
    # Registration
    entry_fee_paid: Decimal = Field(Decimal('0'), ge=0, description="Entry fee paid")
    registration_data: Dict[str, Any] = Field(default_factory=dict, description="Registration data")
    
    # Seeding
    seed_number: Optional[int] = Field(None, description="Tournament seed number")
    skill_rating: Optional[Decimal] = Field(None, description="Player skill rating at registration")


class TournamentParticipantCreate(TournamentParticipantBase):
    """Tournament participant creation model"""
    pass


class TournamentParticipant(TournamentParticipantBase):
    """Complete tournament participant model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Participant ID")
    
    # Status
    status: ParticipantTournamentStatus = Field(ParticipantTournamentStatus.REGISTERED, description="Participant status")
    
    # Performance
    current_round: int = Field(0, description="Current round (0 = eliminated)")
    wins: int = Field(0, description="Number of wins")
    losses: int = Field(0, description="Number of losses")
    points: Decimal = Field(Decimal('0'), description="Tournament points")
    
    # Results
    final_rank: Optional[int] = Field(None, description="Final tournament ranking")
    prize_amount: Decimal = Field(Decimal('0'), description="Prize amount won")
    bonus_amount: Decimal = Field(Decimal('0'), description="Bonus amount")
    
    # Match history
    match_history: List[Dict[str, Any]] = Field(default_factory=list, description="Match results history")
    
    # Audit fields
    registered_at: datetime = Field(..., description="Registration timestamp")
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    eliminated_at: Optional[datetime] = Field(None, description="Elimination timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    version: int = Field(1, description="Record version")


class TournamentParticipantResponse(BaseModel):
    """Tournament participant response model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    tournament_id: UUID
    user_id: UUID
    status: ParticipantTournamentStatus
    
    # Registration
    entry_fee_paid: Decimal
    seed_number: Optional[int]
    skill_rating: Optional[Decimal]
    
    # Performance
    current_round: int
    wins: int
    losses: int
    points: Decimal
    final_rank: Optional[int]
    
    # Rewards
    prize_amount: Decimal
    bonus_amount: Decimal
    
    # Timestamps
    registered_at: datetime
    approved_at: Optional[datetime]
    eliminated_at: Optional[datetime]


class TournamentBracket(BaseModel):
    """Tournament bracket model"""
    tournament_id: UUID
    format: TournamentFormat
    total_rounds: int
    current_round: int
    
    # Bracket structure
    rounds: List[Dict[str, Any]] = Field(default_factory=list, description="Round-by-round bracket")
    matches: List[Dict[str, Any]] = Field(default_factory=list, description="All tournament matches")
    
    # Participants
    participants: List[TournamentParticipantResponse]
    eliminated_participants: List[TournamentParticipantResponse]
    
    # Current state
    active_matches: List[UUID] = Field(default_factory=list, description="Currently active match IDs")
    next_matches: List[Dict[str, Any]] = Field(default_factory=list, description="Upcoming matches")


class TournamentSearchFilter(BaseModel):
    """Tournament search and filter model"""
    game_id: Optional[UUID] = Field(None, description="Filter by game")
    status: Optional[TournamentStatus] = Field(None, description="Filter by status")
    format: Optional[TournamentFormat] = Field(None, description="Filter by format")
    
    # Entry filters
    min_entry_fee: Optional[Decimal] = Field(None, description="Minimum entry fee")
    max_entry_fee: Optional[Decimal] = Field(None, description="Maximum entry fee")
    min_prize_pool: Optional[Decimal] = Field(None, description="Minimum prize pool")
    
    # Timing filters
    registration_open: Optional[bool] = Field(None, description="Registration currently open")
    starting_soon: Optional[bool] = Field(None, description="Starting within next 24 hours")
    
    # Feature filters
    is_featured: Optional[bool] = Field(None, description="Featured tournaments only")
    allow_spectators: Optional[bool] = Field(None, description="Allow spectators")
    has_available_slots: Optional[bool] = Field(None, description="Has available participant slots")
    
    # Sorting
    sort_by: str = Field("tournament_start", description="Sort field")
    sort_order: str = Field("asc", description="Sort order (asc/desc)")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['tournament_start', 'registration_start', 'created_at', 'prize_pool', 'current_participants']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of: {allowed_fields}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order must be "asc" or "desc"')
        return v


class TournamentListResponse(BaseModel):
    """Paginated tournament list response"""
    tournaments: List[TournamentResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class TournamentStats(BaseModel):
    """Tournament statistics model"""
    tournament_id: UUID
    
    # Participation stats
    total_registered: int = Field(0, description="Total registered participants")
    total_active: int = Field(0, description="Currently active participants")
    total_eliminated: int = Field(0, description="Eliminated participants")
    
    # Financial stats
    total_entry_fees: Decimal = Field(Decimal('0'), description="Total entry fees collected")
    total_prize_awarded: Decimal = Field(Decimal('0'), description="Total prizes awarded")
    platform_revenue: Decimal = Field(Decimal('0'), description="Platform revenue")
    
    # Progress stats
    completion_percentage: float = Field(0.0, description="Tournament completion percentage")
    matches_completed: int = Field(0, description="Completed matches")
    matches_remaining: int = Field(0, description="Remaining matches")
    
    # Performance stats
    average_match_duration: Optional[float] = Field(None, description="Average match duration")
    dropout_rate: float = Field(0.0, description="Participant dropout rate")
    
    # Real-time stats
    current_spectators: int = Field(0, description="Current spectators")
    peak_spectators: int = Field(0, description="Peak spectator count")


class TournamentLeaderboard(BaseModel):
    """Tournament leaderboard model"""
    tournament_id: UUID
    last_updated: datetime
    
    # Current standings
    standings: List[Dict[str, Any]] = Field(default_factory=list, description="Current tournament standings")
    
    # Categories
    by_wins: List[Dict[str, Any]] = Field(default_factory=list, description="Ranked by wins")
    by_points: List[Dict[str, Any]] = Field(default_factory=list, description="Ranked by points")
    by_seed: List[Dict[str, Any]] = Field(default_factory=list, description="Ranked by original seed")
    
    # Performance metrics
    top_performers: List[Dict[str, Any]] = Field(default_factory=list, description="Top performing participants")
    biggest_upsets: List[Dict[str, Any]] = Field(default_factory=list, description="Biggest tournament upsets")