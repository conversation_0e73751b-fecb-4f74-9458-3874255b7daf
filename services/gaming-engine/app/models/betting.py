"""
BetBet Gaming Engine - Betting Pydantic Models
==============================================

Pydantic models for request/response validation in betting APIs.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, field_validator


# Base Models
class BaseResponseModel(BaseModel):
    """Base response model with common fields"""
    model_config = ConfigDict(from_attributes=True)


# Betting Market Models
class BettingOutcomeCreate(BaseModel):
    """Create betting outcome request"""
    title: str = Field(..., max_length=200, description="Outcome title")
    description: Optional[str] = Field(None, description="Outcome description")
    outcome_type: str = Field(default="choice", max_length=50, description="Type of outcome")
    initial_odds: Decimal = Field(..., gt=0, description="Initial odds in decimal format (e.g., 2.5 for 2.5x payout)")
    implied_probability: Optional[Decimal] = Field(None, description="Implied probability percentage")
    external_id: Optional[str] = Field(None, max_length=100, description="External reference ID")
    
    @field_validator('initial_odds')
    @classmethod
    def validate_odds(cls, v):
        """Ensure odds are reasonable"""
        if v < 1.01:
            raise ValueError('Odds must be at least 1.01 (equivalent to 99% probability)')
        if v > 1000:
            raise ValueError('Odds cannot exceed 1000 (too unlikely)')
        return v


class BettingOutcomeUpdate(BaseModel):
    """Update betting outcome request"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    initial_odds: Optional[Decimal] = None
    implied_probability: Optional[Decimal] = None
    is_active: Optional[bool] = None
    
    @field_validator('initial_odds')
    @classmethod
    def validate_odds(cls, v):
        """Ensure odds are reasonable"""
        if v is not None:
            if v < 1.01:
                raise ValueError('Odds must be at least 1.01 (equivalent to 99% probability)')
            if v > 1000:
                raise ValueError('Odds cannot exceed 1000 (too unlikely)')
        return v


class BettingOutcomeResponse(BaseResponseModel):
    """Betting outcome response"""
    id: UUID
    market_id: UUID
    name: str  # Changed from title with alias to direct name field
    description: Optional[str]
    outcome_type: str
    odds: Decimal  # Changed from initial_odds with alias to direct odds field
    implied_probability: Optional[Decimal]
    total_bet_amount: Decimal
    bet_count: int
    is_active: bool
    is_winning_outcome: Optional[bool]
    external_id: Optional[str]
    created_at: datetime
    updated_at: datetime


class BettingMarketCreate(BaseModel):
    """Create betting market request"""
    session_id: Optional[UUID] = Field(None, description="Associated session ID")
    tournament_id: Optional[UUID] = Field(None, description="Associated tournament ID")
    title: str = Field(..., max_length=200, description="Market title")
    description: Optional[str] = Field(None, description="Market description")
    market_type: str = Field(default="outcome", max_length=50, description="Type of betting market")
    status: str = Field(default="active", description="Market status")
    auto_close_at: Optional[datetime] = Field(None, description="Auto-close time")
    market_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    settlement_criteria: Optional[Dict[str, Any]] = Field(default_factory=dict)
    outcomes: List[BettingOutcomeCreate] = Field(..., min_length=2, description="Market outcomes")
    
    # Remove strict validation - both session_id and tournament_id are optional per requirements
    # Markets can exist as general betting markets not tied to specific sessions/tournaments


class BettingMarketUpdate(BaseModel):
    """Update betting market request"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    status: Optional[str] = None
    auto_close_at: Optional[datetime] = None
    market_config: Optional[Dict[str, Any]] = None
    settlement_criteria: Optional[Dict[str, Any]] = None


class BettingMarketResponse(BaseResponseModel):
    """Betting market response"""
    id: UUID
    session_id: Optional[UUID]
    tournament_id: Optional[UUID]
    name: str  # Changed from title with alias to direct name field
    description: Optional[str]
    market_type: str
    status: str
    total_pool: Decimal
    total_bets: int
    closes_at: Optional[datetime]  # Changed from auto_close_at with alias
    settlement_time: Optional[datetime]
    market_config: Dict[str, Any]
    settlement_criteria: Optional[Dict[str, Any]]
    outcomes: List[BettingOutcomeResponse]
    created_at: datetime
    updated_at: datetime


class BettingMarketListResponse(BaseResponseModel):
    """Betting market list response with pagination"""
    markets: List[BettingMarketResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class BettingMarketStats(BaseResponseModel):
    """Betting market statistics"""
    market_id: UUID
    total_bets: int
    total_volume: float
    unique_bettors: int
    average_bet_size: float
    outcome_stats: List[Dict[str, Any]]


# User Bet Models
class UserBetCreate(BaseModel):
    """Place bet request"""
    market_id: UUID = Field(..., description="Betting market ID")
    outcome_id: UUID = Field(..., description="Betting outcome ID")
    bet_amount: Decimal = Field(..., gt=0, description="Bet amount in dollars")


class UserBetResponse(BaseResponseModel):
    """User bet response"""
    id: UUID
    user_id: UUID
    market_id: UUID
    outcome_id: UUID
    bet_amount: Decimal
    odds: Decimal
    potential_return: Decimal
    actual_return: Optional[Decimal]
    status: str
    placed_at: datetime
    settled_at: Optional[datetime]
    ip_address: Optional[str]
    user_agent: Optional[str]
    settlement_reason: Optional[str]
    settlement_data: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    market: Optional[BettingMarketResponse] = None
    outcome: Optional[BettingOutcomeResponse] = None


class UserBetListResponse(BaseResponseModel):
    """User bet list response with pagination"""
    bets: List[UserBetResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class BetCancellationRequest(BaseModel):
    """Bet cancellation request"""
    reason: str = Field(..., max_length=500, description="Reason for cancellation")


# Betting History Models
class BettingHistoryResponse(BaseResponseModel):
    """Comprehensive betting history response"""
    bets: List[UserBetResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class BettingStatsResponse(BaseResponseModel):
    """User betting statistics response"""
    total_bets: int
    total_wagered: float
    total_returned: float
    net_profit: float
    win_rate: float
    average_odds: float
    biggest_win: float
    longest_streak: int
    favorite_market_type: str
    total_pending: int


# Odds History Models
class BettingMarketOddsHistoryResponse(BaseResponseModel):
    """Betting market odds history response"""
    id: UUID
    outcome_id: UUID
    old_odds: Optional[Decimal]
    new_odds: Decimal
    old_implied_probability: Optional[Decimal]
    new_implied_probability: Optional[Decimal]
    change_reason: str
    change_amount: Optional[Decimal]
    timestamp: datetime
    created_by: UUID


# WebSocket Event Models
class BettingWebSocketEvent(BaseModel):
    """Base WebSocket event for betting updates"""
    event_type: str
    timestamp: datetime
    data: Dict[str, Any]


class OddsUpdateEvent(BettingWebSocketEvent):
    """Odds update WebSocket event"""
    event_type: str = "odds_update"
    outcome_id: UUID
    old_odds: Decimal
    new_odds: Decimal
    market_id: UUID


class NewBetEvent(BettingWebSocketEvent):
    """New bet placed WebSocket event"""
    event_type: str = "new_bet"
    market_id: UUID
    outcome_id: UUID
    bet_amount: Decimal
    total_pool: Decimal


class MarketStatusChangeEvent(BettingWebSocketEvent):
    """Market status change WebSocket event"""
    event_type: str = "market_status_change"
    market_id: UUID
    old_status: str
    new_status: str


# Error Models
class BettingErrorResponse(BaseModel):
    """Betting API error response"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None