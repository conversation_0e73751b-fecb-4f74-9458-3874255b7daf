"""
BetBet Gaming Engine - Chess Tournament and Invitation Models
============================================================

Enhanced models for chess tournaments, invitations, and advanced betting.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, Dict, Any, List, Literal
from uuid import UUID

from pydantic import BaseModel, Field, validator, ConfigDict


# Enums and Types
InvitationType = Literal["direct", "public", "friends", "league", "tournament"]
WagerType = Literal["matched", "unmatched", "none"]
ChessVariant = Literal["standard", "chess960", "king_of_the_hill", "three_check", "antichess", "atomic"]
TimeControl = Literal["bullet", "blitz", "rapid", "classical", "correspondence"]
TournamentType = Literal["single_elimination", "double_elimination", "round_robin", "swiss", "arena"]
GameStatus = Literal["waiting", "active", "paused", "completed", "aborted", "forfeit"]
GameResult = Literal[
    "white_wins", "black_wins", "draw", "white_timeout", "black_timeout",
    "white_resignation", "black_resignation", "stalemate", "insufficient_material",
    "threefold_repetition", "fifty_move_rule", "draw_agreement", "aborted"
]
BettingMarketType = Literal[
    "game_winner", "first_capture", "first_check", "castling_race", 
    "queen_trade", "first_queen_loss", "pawn_promotion", "opening_type",
    "total_moves_range", "game_duration", "checkmate_vs_other",
    "material_advantage", "first_blunder", "time_advantage"
]


class ChessInvitationCreate(BaseModel):
    """Model for creating chess game invitations"""
    challenged_id: Optional[UUID] = Field(None, description="Challenged player ID (None for public)")
    invitation_type: InvitationType = Field("direct", description="Type of invitation")
    
    # Game configuration
    chess_variant: ChessVariant = Field("standard", description="Chess variant")
    time_control: TimeControl = Field("blitz", description="Time control category")
    initial_time_seconds: int = Field(300, ge=30, le=86400, description="Initial time in seconds")
    increment_seconds: int = Field(3, ge=0, le=30, description="Increment per move")
    
    # Wager system
    challenger_wager: Decimal = Field(Decimal('0'), ge=0, description="Challenger's wager amount")
    challenged_wager: Optional[Decimal] = Field(None, ge=0, description="Required wager from challenged")
    wager_type: WagerType = Field("matched", description="Wager matching type")
    
    # Visibility and access
    is_public: bool = Field(False, description="Public challenge visible to all")
    is_rated: bool = Field(True, description="Affects player ratings")
    allow_spectators: bool = Field(True, description="Allow spectators")
    allow_spectator_betting: bool = Field(True, description="Enable spectator betting")
    password: Optional[str] = Field(None, description="Password for private games")
    
    # Context
    league_id: Optional[UUID] = Field(None, description="League/club context")
    tournament_id: Optional[UUID] = Field(None, description="Tournament context")
    tournament_round: Optional[int] = Field(None, description="Tournament round")
    
    # Additional
    message: Optional[str] = Field(None, max_length=500, description="Challenge message")
    expires_in_hours: int = Field(24, ge=1, le=168, description="Expiration time in hours")


class ChessInvitationResponse(BaseModel):
    """Response to chess game invitation"""
    accept: bool = Field(..., description="Accept or decline invitation")
    challenged_wager: Optional[Decimal] = Field(None, description="Counter-wager if unmatched")
    response_message: Optional[str] = Field(None, max_length=500, description="Response message")
    color_preference: Optional[Literal["white", "black", "random"]] = Field(None, description="Color preference")


class ChessInvitation(BaseModel):
    """Chess game invitation model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Invitation ID")
    challenger_id: UUID = Field(..., description="Challenger user ID")
    challenged_id: Optional[UUID] = Field(None, description="Challenged user ID")
    invitation_type: InvitationType = Field(..., description="Invitation type")
    
    # Game configuration
    chess_variant: ChessVariant = Field(..., description="Chess variant")
    time_control: TimeControl = Field(..., description="Time control")
    initial_time_seconds: int = Field(..., description="Initial time")
    increment_seconds: int = Field(..., description="Time increment")
    
    # Wager information
    challenger_wager: Decimal = Field(..., description="Challenger's wager")
    challenged_wager: Optional[Decimal] = Field(None, description="Challenged player's wager")
    wager_type: WagerType = Field(..., description="Wager type")
    total_prize_pool: Decimal = Field(..., description="Total prize pool")
    
    # Settings
    is_public: bool = Field(..., description="Public visibility")
    is_rated: bool = Field(..., description="Rated game")
    allow_spectators: bool = Field(..., description="Allow spectators")
    allow_spectator_betting: bool = Field(..., description="Enable betting")
    
    # Status
    status: str = Field(..., description="Invitation status")
    message: Optional[str] = Field(None, description="Challenge message")
    response_message: Optional[str] = Field(None, description="Response message")
    
    # Timing
    expires_at: datetime = Field(..., description="Expiration time")
    accepted_at: Optional[datetime] = Field(None, description="Acceptance time")
    game_id: Optional[UUID] = Field(None, description="Created game ID")
    
    # Metadata
    created_at: datetime = Field(..., description="Creation time")
    updated_at: datetime = Field(..., description="Last update")


class ChessGameInstanceCreate(BaseModel):
    """Model for creating chess game instances"""
    invitation_id: Optional[UUID] = Field(None, description="Source invitation")
    white_player_id: UUID = Field(..., description="White player ID")
    black_player_id: UUID = Field(..., description="Black player ID")
    
    # Game configuration
    variant: ChessVariant = Field("standard", description="Chess variant")
    time_control: TimeControl = Field("blitz", description="Time control")
    initial_time_seconds: int = Field(300, description="Initial time")
    increment_seconds: int = Field(3, description="Time increment")
    
    # Wager information
    white_wager: Decimal = Field(Decimal('0'), description="White player's wager")
    black_wager: Decimal = Field(Decimal('0'), description="Black player's wager")
    
    # Settings
    allow_spectators: bool = Field(True, description="Allow spectators")
    allow_spectator_betting: bool = Field(True, description="Enable betting")


class ChessGameInstance(BaseModel):
    """Chess game instance model with enhanced metadata"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Game instance ID")
    game_slug: str = Field(..., description="Searchable game slug")
    invitation_id: Optional[UUID] = Field(None, description="Source invitation")
    session_id: UUID = Field(..., description="Game session ID")
    
    # Players
    white_player_id: UUID = Field(..., description="White player ID")
    black_player_id: UUID = Field(..., description="Black player ID")
    white_player_username: str = Field(..., description="White player username")
    black_player_username: str = Field(..., description="Black player username")
    
    # Game configuration
    variant: ChessVariant = Field(..., description="Chess variant")
    time_control: TimeControl = Field(..., description="Time control")
    initial_time_seconds: int = Field(..., description="Initial time")
    increment_seconds: int = Field(..., description="Time increment")
    
    # Current state
    current_fen: str = Field(..., description="Current position FEN")
    move_history: List[Dict[str, Any]] = Field(..., description="Move history")
    current_turn: Literal["white", "black"] = Field(..., description="Current turn")
    move_number: int = Field(..., description="Current move number")
    
    # Time tracking
    white_time_remaining: int = Field(..., description="White time remaining")
    black_time_remaining: int = Field(..., description="Black time remaining")
    last_move_time: datetime = Field(..., description="Last move timestamp")
    
    # Game status
    status: GameStatus = Field(..., description="Game status")
    result: Optional[GameResult] = Field(None, description="Game result")
    result_reason: Optional[str] = Field(None, description="Result reason")
    
    # Wager information
    white_wager: Decimal = Field(..., description="White player's wager")
    black_wager: Decimal = Field(..., description="Black player's wager")
    total_prize_pool: Decimal = Field(..., description="Total prize pool")
    winner_payout: Decimal = Field(..., description="Winner's payout")
    is_wager_settled: bool = Field(..., description="Wager settlement status")
    
    # Spectator information
    spectator_count: int = Field(..., description="Current spectator count")
    spectator_betting_pool: Decimal = Field(..., description="Total betting pool")
    total_spectator_bets: int = Field(..., description="Number of bets")
    
    # Rating information
    white_rating_before: Optional[int] = Field(None, description="White rating before")
    black_rating_before: Optional[int] = Field(None, description="Black rating before")
    white_rating_after: Optional[int] = Field(None, description="White rating after")
    black_rating_after: Optional[int] = Field(None, description="Black rating after")
    rating_change_white: int = Field(0, description="White rating change")
    rating_change_black: int = Field(0, description="Black rating change")
    
    # Analysis
    opening_name: Optional[str] = Field(None, description="Opening name")
    opening_eco: Optional[str] = Field(None, description="ECO code")
    total_moves: int = Field(0, description="Total moves played")
    game_duration_seconds: Optional[int] = Field(None, description="Game duration")
    
    # Archive data
    pgn_data: Optional[str] = Field(None, description="PGN representation")
    is_archived: bool = Field(False, description="Archive status")
    tags: Dict[str, Any] = Field(default_factory=dict, description="Searchable tags")
    
    # Timestamps
    started_at: Optional[datetime] = Field(None, description="Game start time")
    finished_at: Optional[datetime] = Field(None, description="Game end time")
    created_at: datetime = Field(..., description="Creation time")
    updated_at: datetime = Field(..., description="Last update")


class ChessBettingMarketCreate(BaseModel):
    """Model for creating spectator betting markets"""
    game_id: UUID = Field(..., description="Chess game ID")
    market_type: BettingMarketType = Field(..., description="Market type")
    market_name: str = Field(..., min_length=3, max_length=200, description="Market name")
    market_description: Optional[str] = Field(None, description="Market description")
    
    # Market parameters
    closes_at: Optional[datetime] = Field(None, description="Market closing time")
    settlement_criteria: Dict[str, Any] = Field(..., description="Settlement conditions")
    house_edge: Decimal = Field(Decimal('0.05'), ge=0, le=0.2, description="House edge")
    
    # Outcomes
    outcomes: List[Dict[str, Any]] = Field(..., min_items=2, description="Betting outcomes")


class ChessBettingOutcome(BaseModel):
    """Chess betting outcome model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Outcome ID")
    market_id: UUID = Field(..., description="Market ID")
    outcome_name: str = Field(..., description="Outcome name")
    outcome_description: Optional[str] = Field(None, description="Outcome description")
    outcome_data: Dict[str, Any] = Field(..., description="Outcome parameters")
    
    # Odds
    current_odds: Decimal = Field(..., description="Current odds")
    opening_odds: Decimal = Field(..., description="Opening odds")
    implied_probability: Decimal = Field(..., description="Implied probability")
    
    # Volume
    total_bet_amount: Decimal = Field(..., description="Total bet amount")
    bet_count: int = Field(..., description="Number of bets")
    largest_bet: Decimal = Field(..., description="Largest single bet")
    
    # Status
    is_active: bool = Field(..., description="Active for betting")
    is_winning_outcome: bool = Field(..., description="Winning outcome")
    
    # Settlement
    payout_multiplier: Optional[Decimal] = Field(None, description="Payout multiplier")
    total_payout: Decimal = Field(..., description="Total payout")


class ChessSpectatorBetCreate(BaseModel):
    """Model for creating spectator bets"""
    game_id: UUID = Field(..., description="Chess game ID")
    market_id: UUID = Field(..., description="Betting market ID")
    outcome_id: UUID = Field(..., description="Chosen outcome ID")
    stake_amount: Decimal = Field(..., gt=0, description="Stake amount")
    
    # Optional constraints
    max_odds: Optional[Decimal] = Field(None, description="Maximum acceptable odds")
    min_odds: Optional[Decimal] = Field(None, description="Minimum acceptable odds")


class ChessSpectatorBet(BaseModel):
    """Chess spectator bet model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Bet ID")
    game_id: UUID = Field(..., description="Game ID")
    market_id: UUID = Field(..., description="Market ID")
    outcome_id: UUID = Field(..., description="Outcome ID")
    user_id: UUID = Field(..., description="Bettor user ID")
    
    # Bet details
    stake_amount: Decimal = Field(..., description="Stake amount")
    odds_at_placement: Decimal = Field(..., description="Odds when bet was placed")
    potential_payout: Decimal = Field(..., description="Potential payout")
    
    # Context
    placed_at_move: Optional[int] = Field(None, description="Move number when placed")
    game_state_at_placement: Optional[Dict[str, Any]] = Field(None, description="Game state")
    
    # Settlement
    status: str = Field(..., description="Bet status")
    actual_payout: Decimal = Field(..., description="Actual payout")
    settled_at: Optional[datetime] = Field(None, description="Settlement time")
    settlement_notes: Optional[str] = Field(None, description="Settlement notes")
    
    # Risk management
    is_suspicious: bool = Field(..., description="Flagged as suspicious")
    risk_score: Decimal = Field(..., description="Risk assessment score")
    
    # Timestamps
    created_at: datetime = Field(..., description="Creation time")
    updated_at: datetime = Field(..., description="Last update")


class ChessTournamentCreate(BaseModel):
    """Model for creating chess tournaments"""
    name: str = Field(..., min_length=3, max_length=200, description="Tournament name")
    description: Optional[str] = Field(None, description="Tournament description")
    tournament_type: TournamentType = Field("single_elimination", description="Tournament format")
    
    # Chess configuration
    chess_variant: ChessVariant = Field("standard", description="Chess variant")
    time_control: TimeControl = Field("blitz", description="Time control")
    initial_time_seconds: int = Field(300, ge=30, description="Initial time")
    increment_seconds: int = Field(3, ge=0, description="Time increment")
    
    # Entry and participation
    entry_fee: Decimal = Field(Decimal('0'), ge=0, description="Entry fee")
    max_participants: int = Field(32, ge=4, le=1024, description="Maximum participants")
    min_participants: int = Field(4, ge=2, description="Minimum participants")
    
    # Rating restrictions
    min_rating: Optional[int] = Field(None, ge=100, le=3000, description="Minimum rating")
    max_rating: Optional[int] = Field(None, ge=100, le=3000, description="Maximum rating")
    rating_category: str = Field("blitz", description="Rating category")
    
    # Prize structure
    prize_distribution: Optional[Dict[str, Any]] = Field(None, description="Prize distribution")
    
    # Scheduling
    registration_opens_at: Optional[datetime] = Field(None, description="Registration open time")
    registration_closes_at: Optional[datetime] = Field(None, description="Registration close time")
    starts_at: datetime = Field(..., description="Tournament start time")
    
    # Configuration
    time_between_rounds: int = Field(300, ge=60, description="Time between rounds in seconds")
    
    # Visibility
    is_public: bool = Field(True, description="Public tournament")
    is_rated: bool = Field(True, description="Rated tournament")
    
    @validator('max_rating')
    def validate_rating_range(cls, v, values):
        if v is not None and 'min_rating' in values and values['min_rating'] is not None:
            if v <= values['min_rating']:
                raise ValueError('max_rating must be greater than min_rating')
        return v


class ChessTournament(BaseModel):
    """Chess tournament model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Tournament ID")
    name: str = Field(..., description="Tournament name")
    slug: str = Field(..., description="Tournament slug")
    description: Optional[str] = Field(None, description="Description")
    tournament_type: TournamentType = Field(..., description="Tournament type")
    
    # Chess configuration
    chess_variant: ChessVariant = Field(..., description="Chess variant")
    time_control: TimeControl = Field(..., description="Time control")
    initial_time_seconds: int = Field(..., description="Initial time")
    increment_seconds: int = Field(..., description="Time increment")
    
    # Participation
    entry_fee: Decimal = Field(..., description="Entry fee")
    max_participants: int = Field(..., description="Maximum participants")
    current_participants: int = Field(..., description="Current participants")
    min_participants: int = Field(..., description="Minimum participants")
    
    # Rating restrictions
    min_rating: Optional[int] = Field(None, description="Minimum rating")
    max_rating: Optional[int] = Field(None, description="Maximum rating")
    rating_category: str = Field(..., description="Rating category")
    
    # Prize information
    prize_pool: Decimal = Field(..., description="Total prize pool")
    prize_distribution: Optional[Dict[str, Any]] = Field(None, description="Prize distribution")
    
    # Status
    status: str = Field(..., description="Tournament status")
    rounds_total: Optional[int] = Field(None, description="Total rounds")
    current_round: int = Field(..., description="Current round")
    
    # Scheduling
    registration_opens_at: Optional[datetime] = Field(None, description="Registration opens")
    registration_closes_at: Optional[datetime] = Field(None, description="Registration closes")
    starts_at: datetime = Field(..., description="Tournament starts")
    estimated_ends_at: Optional[datetime] = Field(None, description="Estimated end")
    actual_ended_at: Optional[datetime] = Field(None, description="Actual end")
    
    # Results
    champion_id: Optional[UUID] = Field(None, description="Tournament champion")
    runner_up_id: Optional[UUID] = Field(None, description="Runner-up")
    final_standings: Optional[Dict[str, Any]] = Field(None, description="Final standings")
    
    # Settings
    is_public: bool = Field(..., description="Public tournament")
    is_rated: bool = Field(..., description="Rated games")
    organizer_id: Optional[UUID] = Field(None, description="Organizer user ID")
    
    # Timestamps
    created_at: datetime = Field(..., description="Creation time")
    updated_at: datetime = Field(..., description="Last update")


class ChessGameArchive(BaseModel):
    """Chess game archive model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Archive ID")
    original_game_id: UUID = Field(..., description="Original game ID")
    game_slug: str = Field(..., description="Game slug for search")
    
    # Players
    white_player_username: str = Field(..., description="White player")
    black_player_username: str = Field(..., description="Black player")
    white_rating: Optional[int] = Field(None, description="White player rating")
    black_rating: Optional[int] = Field(None, description="Black player rating")
    
    # Game metadata
    variant: ChessVariant = Field(..., description="Chess variant")
    time_control: TimeControl = Field(..., description="Time control")
    result: GameResult = Field(..., description="Game result")
    total_moves: int = Field(..., description="Total moves")
    game_duration_seconds: Optional[int] = Field(None, description="Game duration")
    
    # Game data
    pgn_data: str = Field(..., description="Complete PGN")
    move_history: List[Dict[str, Any]] = Field(..., description="Move analysis")
    final_position: str = Field(..., description="Final FEN")
    
    # Analysis
    opening_name: Optional[str] = Field(None, description="Opening name")
    opening_eco: Optional[str] = Field(None, description="ECO code")
    engine_analysis: Optional[Dict[str, Any]] = Field(None, description="Computer analysis")
    critical_moments: Optional[Dict[str, Any]] = Field(None, description="Key moments")
    
    # Search metadata
    game_date: date = Field(..., description="Game date")
    tags: List[str] = Field(..., description="Search tags")
    
    # Archive metadata
    archived_at: datetime = Field(..., description="Archive timestamp")
    archive_reason: str = Field(..., description="Archive reason")


class ChessGameSearchFilters(BaseModel):
    """Chess game search and filter model"""
    # Player filters
    player_username: Optional[str] = Field(None, description="Player username search")
    white_player: Optional[str] = Field(None, description="White player username")
    black_player: Optional[str] = Field(None, description="Black player username")
    
    # Game filters
    variant: Optional[ChessVariant] = Field(None, description="Chess variant")
    time_control: Optional[TimeControl] = Field(None, description="Time control")
    result: Optional[GameResult] = Field(None, description="Game result")
    
    # Rating filters
    min_rating: Optional[int] = Field(None, ge=0, description="Minimum player rating")
    max_rating: Optional[int] = Field(None, le=5000, description="Maximum player rating")
    
    # Time filters
    date_from: Optional[date] = Field(None, description="Games from this date")
    date_to: Optional[date] = Field(None, description="Games until this date")
    
    # Game characteristics
    min_moves: Optional[int] = Field(None, ge=1, description="Minimum moves")
    max_moves: Optional[int] = Field(None, le=500, description="Maximum moves")
    opening_eco: Optional[str] = Field(None, description="ECO opening code")
    opening_name: Optional[str] = Field(None, description="Opening name search")
    
    # Advanced search
    search_query: Optional[str] = Field(None, description="Full-text search")
    tags: Optional[List[str]] = Field(None, description="Search by tags")
    game_slug: Optional[str] = Field(None, description="Search by slug pattern")
    
    # Sorting and pagination
    sort_by: str = Field("created_at", description="Sort field")
    sort_order: Literal["asc", "desc"] = Field("desc", description="Sort order")
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(20, ge=1, le=100, description="Results per page")


class ChessGameSearchResult(BaseModel):
    """Chess game search result model"""
    games: List[ChessGameArchive] = Field(..., description="Found games")
    total_count: int = Field(..., description="Total matching games")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Results per page")
    has_next: bool = Field(..., description="Has next page")
    has_prev: bool = Field(..., description="Has previous page")
    search_time_ms: Optional[float] = Field(None, description="Search execution time")