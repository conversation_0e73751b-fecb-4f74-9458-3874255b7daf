"""
BetBet Gaming Engine - Session Models
====================================

Pydantic models for game session management and real-time state tracking.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, validator, ConfigDict


class SessionStatus(str, Enum):
    """Game session status enumeration"""
    WAITING = "waiting"
    STARTING = "starting" 
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"


class ParticipantStatus(str, Enum):
    """Session participant status enumeration"""
    JOINED = "joined"
    READY = "ready"
    PLAYING = "playing"
    SPECTATING = "spectating"
    DISCONNECTED = "disconnected"
    ELIMINATED = "eliminated"
    FINISHED = "finished"


class GameSessionBase(BaseModel):
    """Base game session model"""
    game_id: UUID = Field(..., description="Game ID")
    session_name: str = Field(..., min_length=3, max_length=200, description="Session name")
    
    # Entry requirements
    entry_fee: Decimal = Field(Decimal('0'), ge=0, description="Entry fee amount")
    min_participants_to_start: int = Field(2, ge=1, le=20, description="Minimum participants to start")
    max_participants: int = Field(10, ge=1, le=20, description="Maximum participants")
    
    # Game configuration
    game_config: Dict[str, Any] = Field(default_factory=dict, description="Game-specific configuration")
    session_rules: Optional[str] = Field(None, description="Session-specific rules")
    
    # Timing
    scheduled_start_time: Optional[datetime] = Field(None, description="Scheduled start time")
    estimated_duration_minutes: int = Field(10, ge=1, le=180, description="Estimated duration")
    
    # Features
    allow_spectators: bool = Field(True, description="Allow spectators")
    allow_practice_mode: bool = Field(False, description="Practice mode session")
    is_tournament_session: bool = Field(False, description="Part of a tournament")
    tournament_id: Optional[UUID] = Field(None, description="Tournament ID if applicable")
    
    @validator('max_participants')
    def validate_participant_limits(cls, v, values):
        if 'min_participants_to_start' in values and v < values['min_participants_to_start']:
            raise ValueError('max_participants must be >= min_participants_to_start')
        return v


class GameSessionCreate(GameSessionBase):
    """Game session creation model"""
    created_by: str = Field(..., description="Creator user ID")


class GameSessionUpdate(BaseModel):
    """Game session update model"""
    session_name: Optional[str] = Field(None, min_length=3, max_length=200)
    game_config: Optional[Dict[str, Any]] = None
    session_rules: Optional[str] = None
    scheduled_start_time: Optional[datetime] = None
    estimated_duration_minutes: Optional[int] = Field(None, ge=1, le=180)
    allow_spectators: Optional[bool] = None
    status: Optional[SessionStatus] = None


class GameSession(GameSessionBase):
    """Complete game session model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Session ID")
    
    # Status and timing
    status: SessionStatus = Field(SessionStatus.WAITING, description="Session status")
    actual_start_time: Optional[datetime] = Field(None, description="Actual start time")
    actual_end_time: Optional[datetime] = Field(None, description="Actual end time")
    actual_duration_minutes: Optional[Decimal] = Field(None, description="Actual duration")
    
    # Participants
    current_participants: int = Field(0, description="Current participant count")
    current_spectators: int = Field(0, description="Current spectator count")
    
    # Financial
    total_prize_pool: Decimal = Field(Decimal('0'), description="Total prize pool")
    platform_fee: Decimal = Field(Decimal('0'), description="Platform fee collected")
    
    # Results
    winner_user_id: Optional[UUID] = Field(None, description="Winner user ID")
    final_scores: Dict[str, Any] = Field(default_factory=dict, description="Final participant scores")
    
    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: UUID = Field(..., description="Creator user ID")
    updated_by: UUID = Field(..., description="Last updater user ID")
    version: int = Field(1, description="Record version")
    
    # Soft delete
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by: Optional[UUID] = Field(None, description="Deleter user ID")


class GameSessionResponse(BaseModel):
    """Game session response model for API endpoints"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    game_id: UUID
    session_name: str
    status: SessionStatus
    
    # Entry requirements
    entry_fee: Decimal
    min_participants_to_start: int
    max_participants: int
    current_participants: int
    current_spectators: int
    
    # Timing
    scheduled_start_time: Optional[datetime]
    actual_start_time: Optional[datetime]
    actual_end_time: Optional[datetime]
    estimated_duration_minutes: int
    actual_duration_minutes: Optional[Decimal]
    
    # Financial
    total_prize_pool: Decimal
    
    # Features
    allow_spectators: bool
    allow_practice_mode: bool
    is_tournament_session: bool
    tournament_id: Optional[UUID]
    
    # Results
    winner_user_id: Optional[UUID]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime


class SessionParticipantBase(BaseModel):
    """Base session participant model"""
    session_id: UUID = Field(..., description="Session ID")
    user_id: UUID = Field(..., description="User ID")
    
    # Participation details
    entry_fee_paid: Decimal = Field(Decimal('0'), ge=0, description="Entry fee paid")
    is_spectator: bool = Field(False, description="Spectator mode")
    
    # Game state
    current_score: Decimal = Field(Decimal('0'), description="Current score")
    game_data: Dict[str, Any] = Field(default_factory=dict, description="Player-specific game data")
    
    # Connection
    connection_id: Optional[str] = Field(None, description="WebSocket connection ID")
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")


class SessionParticipantCreate(SessionParticipantBase):
    """Session participant creation model"""
    pass


class SessionParticipant(SessionParticipantBase):
    """Complete session participant model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Participant ID")
    
    # Status
    status: ParticipantStatus = Field(ParticipantStatus.JOINED, description="Participant status")
    join_order: int = Field(..., description="Order of joining (1-based)")
    
    # Performance
    final_score: Optional[Decimal] = Field(None, description="Final score")
    final_rank: Optional[int] = Field(None, description="Final ranking")
    performance_data: Dict[str, Any] = Field(default_factory=dict, description="Performance metrics")
    
    # Rewards
    prize_amount: Decimal = Field(Decimal('0'), description="Prize amount won")
    bonus_amount: Decimal = Field(Decimal('0'), description="Bonus amount")
    
    # Audit fields
    joined_at: datetime = Field(..., description="Join timestamp")
    left_at: Optional[datetime] = Field(None, description="Leave timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    version: int = Field(1, description="Record version")


class SessionParticipantResponse(BaseModel):
    """Session participant response model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    session_id: UUID
    user_id: UUID
    status: ParticipantStatus
    
    # Participation
    entry_fee_paid: Decimal
    is_spectator: bool
    join_order: int
    
    # Performance
    current_score: Decimal
    final_score: Optional[Decimal]
    final_rank: Optional[int]
    
    # Rewards
    prize_amount: Decimal
    bonus_amount: Decimal
    
    # Connection
    connection_id: Optional[str]
    last_activity: Optional[datetime]
    
    # Timestamps
    joined_at: datetime
    left_at: Optional[datetime]


class GameStateBase(BaseModel):
    """Base game state model for real-time updates"""
    session_id: UUID = Field(..., description="Session ID")
    
    # State data
    state_data: Dict[str, Any] = Field(..., description="Current game state")
    round_number: int = Field(1, ge=1, description="Current round number")
    turn_order: List[UUID] = Field(default_factory=list, description="Turn order (user IDs)")
    current_turn_user_id: Optional[UUID] = Field(None, description="Current turn player")
    
    # Timing
    round_start_time: Optional[datetime] = Field(None, description="Round start time")
    turn_deadline: Optional[datetime] = Field(None, description="Turn deadline")
    
    # Game events
    recent_events: List[Dict[str, Any]] = Field(default_factory=list, description="Recent game events")


class GameStateCreate(GameStateBase):
    """Game state creation model"""
    pass


class GameState(GameStateBase):
    """Complete game state model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="State ID")
    
    # Version control for concurrent updates
    state_version: int = Field(1, description="State version for optimistic locking")
    
    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    updated_by: UUID = Field(..., description="Last updater user ID")


class SessionSearchFilter(BaseModel):
    """Session search and filter model"""
    game_id: Optional[UUID] = Field(None, description="Filter by game")
    status: Optional[SessionStatus] = Field(None, description="Filter by status")
    min_entry_fee: Optional[Decimal] = Field(None, description="Minimum entry fee")
    max_entry_fee: Optional[Decimal] = Field(None, description="Maximum entry fee")
    allow_spectators: Optional[bool] = Field(None, description="Allow spectators")
    is_tournament_session: Optional[bool] = Field(None, description="Tournament sessions only")
    
    # Participation filters
    has_available_slots: Optional[bool] = Field(None, description="Has available participant slots")
    starting_soon: Optional[bool] = Field(None, description="Starting within next hour")
    
    # Sorting
    sort_by: str = Field("created_at", description="Sort field")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'scheduled_start_time', 'entry_fee', 'current_participants', 'total_prize_pool']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of: {allowed_fields}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order must be "asc" or "desc"')
        return v


class SessionListResponse(BaseModel):
    """Paginated session list response"""
    sessions: List[GameSessionResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class SessionStats(BaseModel):
    """Session statistics model"""
    session_id: UUID
    
    # Participation stats
    total_participants: int = Field(0, description="Total participants")
    active_participants: int = Field(0, description="Currently active participants")
    spectators: int = Field(0, description="Current spectators")
    
    # Financial stats
    total_entry_fees: Decimal = Field(Decimal('0'), description="Total entry fees collected")
    total_prize_pool: Decimal = Field(Decimal('0'), description="Total prize pool")
    platform_revenue: Decimal = Field(Decimal('0'), description="Platform revenue")
    
    # Performance stats
    average_score: Optional[Decimal] = Field(None, description="Average participant score")
    completion_rate: float = Field(0.0, description="Session completion rate")
    average_session_time: Optional[float] = Field(None, description="Average session duration")
    
    # Real-time stats
    messages_sent: int = Field(0, description="Total messages/events sent")
    actions_per_minute: float = Field(0.0, description="Actions per minute")