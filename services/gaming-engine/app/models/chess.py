"""
BetBet Gaming Engine - Chess Game Models
=======================================

Pydantic models for chess game management and gameplay.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List, Literal
from uuid import UUID

from pydantic import BaseModel, Field, validator, ConfigDict


# Chess-specific enums
ChessVariant = Literal["standard", "chess960", "king_of_the_hill", "three_check", "antichess"]
TimeControl = Literal["bullet", "blitz", "rapid", "classical", "correspondence"]
ChessColor = Literal["white", "black"]
GameResult = Literal[
    "white_wins", "black_wins", "draw", "white_timeout", "black_timeout",
    "white_resignation", "black_resignation", "stalemate", "insufficient_material",
    "threefold_repetition", "fifty_move_rule", "draw_agreement", "aborted"
]
PieceType = Literal["Q", "R", "B", "N"]


class ChessGameConfig(BaseModel):
    """Chess game configuration model"""
    variant: ChessVariant = Field("standard", description="Chess variant to play")
    time_control: TimeControl = Field("blitz", description="Time control category")
    initial_time_seconds: int = Field(300, ge=30, le=86400, description="Initial time in seconds")
    increment_seconds: int = Field(3, ge=0, le=30, description="Increment per move in seconds")
    rated: bool = Field(True, description="Whether the game affects ratings")
    
    # Advanced options
    starting_fen: Optional[str] = Field(None, description="Custom starting position (FEN)")
    allow_spectators: bool = Field(True, description="Allow spectators to watch")
    allow_spectator_betting: bool = Field(True, description="Allow spectator betting")
    engine_analysis: bool = Field(True, description="Enable post-game engine analysis")


class ChessGameState(BaseModel):
    """Current chess game state"""
    fen: str = Field(..., description="Current position in FEN notation")
    moves: List[str] = Field(default_factory=list, description="List of moves in algebraic notation")
    move_times: List[int] = Field(default_factory=list, description="Time taken for each move in milliseconds")
    
    # Time tracking
    white_time_remaining: int = Field(..., description="White's remaining time in seconds")
    black_time_remaining: int = Field(..., description="Black's remaining time in seconds")
    current_turn: ChessColor = Field("white", description="Whose turn it is")
    
    # Game status
    move_number: int = Field(1, description="Current move number")
    halfmove_clock: int = Field(0, description="Halfmove clock for 50-move rule")
    is_check: bool = Field(False, description="Current player is in check")
    is_checkmate: bool = Field(False, description="Game ended in checkmate")
    is_stalemate: bool = Field(False, description="Game ended in stalemate")
    
    # Additional game info
    captured_pieces: Dict[str, List[str]] = Field(
        default_factory=lambda: {"white": [], "black": []},
        description="Captured pieces by color"
    )
    draw_offered_by: Optional[ChessColor] = Field(None, description="Who offered a draw")
    last_move_uci: Optional[str] = Field(None, description="Last move in UCI notation")
    
    # Opening information
    opening_name: Optional[str] = Field(None, description="Opening name if identified")
    opening_eco: Optional[str] = Field(None, description="ECO code if identified")
    
    # Engine evaluation (if enabled)
    evaluation: Optional[Dict[str, Any]] = Field(None, description="Engine evaluation data")


class ChessMove(BaseModel):
    """Chess move model"""
    move_uci: str = Field(..., description="Move in UCI notation (e.g., 'e2e4')")
    move_san: str = Field(..., description="Move in algebraic notation (e.g., 'e4')")
    time_taken_ms: int = Field(..., ge=0, description="Time taken for the move in milliseconds")
    
    # Move analysis
    is_capture: bool = Field(False, description="Move captures a piece")
    is_check: bool = Field(False, description="Move gives check")
    is_checkmate: bool = Field(False, description="Move delivers checkmate")
    is_castling: bool = Field(False, description="Move is castling")
    is_en_passant: bool = Field(False, description="Move is en passant capture")
    is_promotion: bool = Field(False, description="Move is pawn promotion")
    promotion_piece: Optional[PieceType] = Field(None, description="Piece promoted to")
    
    # Position data
    position_before: str = Field(..., description="FEN before the move")
    position_after: str = Field(..., description="FEN after the move")
    
    # Optional analysis data
    engine_evaluation: Optional[int] = Field(None, description="Engine evaluation in centipawns")
    best_move: Optional[str] = Field(None, description="Engine's best move")
    move_quality: Optional[str] = Field(None, description="Move quality assessment")


class ChessGameCreate(BaseModel):
    """Chess game creation request"""
    opponent_id: Optional[UUID] = Field(None, description="Opponent user ID (None for open challenge)")
    config: ChessGameConfig = Field(..., description="Game configuration")
    stake_amount: Decimal = Field(Decimal('0'), ge=0, description="Stake amount per player")
    is_private: bool = Field(False, description="Private game (invitation only)")
    password: Optional[str] = Field(None, description="Game password for private games")


class ChessGameJoin(BaseModel):
    """Chess game join request"""
    password: Optional[str] = Field(None, description="Game password if required")
    color_preference: Optional[ChessColor] = Field(None, description="Preferred color")


class ChessMoveRequest(BaseModel):
    """Chess move request"""
    move_uci: str = Field(..., description="Move in UCI notation")
    offer_draw: bool = Field(False, description="Offer draw with this move")
    claim_draw: bool = Field(False, description="Claim draw by repetition/50-move rule")


class ChessGameAction(BaseModel):
    """Chess game action (resign, draw offer, etc.)"""
    action: Literal["resign", "offer_draw", "accept_draw", "decline_draw", "claim_draw", "abort"] = Field(
        ..., description="Action to perform"
    )
    reason: Optional[str] = Field(None, description="Optional reason for the action")


class ChessGameResponse(BaseModel):
    """Chess game response model"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Game ID")
    session_id: UUID = Field(..., description="Associated game session ID")
    
    # Players
    white_player_id: UUID = Field(..., description="White player ID")
    black_player_id: UUID = Field(..., description="Black player ID")
    white_player_name: Optional[str] = Field(None, description="White player name")
    black_player_name: Optional[str] = Field(None, description="Black player name")
    
    # Ratings
    white_rating: Optional[int] = Field(None, description="White player's rating")
    black_rating: Optional[int] = Field(None, description="Black player's rating")
    rating_category: str = Field(..., description="Rating category")
    
    # Game configuration
    variant: ChessVariant = Field(..., description="Chess variant")
    time_control: TimeControl = Field(..., description="Time control")
    initial_time_seconds: int = Field(..., description="Initial time in seconds")
    increment_seconds: int = Field(..., description="Increment per move")
    
    # Current state
    current_state: ChessGameState = Field(..., description="Current game state")
    
    # Game status
    status: str = Field(..., description="Game status")
    result: Optional[GameResult] = Field(None, description="Game result if finished")
    result_reason: Optional[str] = Field(None, description="Reason for the result")
    
    # Timing
    started_at: Optional[datetime] = Field(None, description="Game start time")
    finished_at: Optional[datetime] = Field(None, description="Game finish time")
    
    # Spectator info
    spectator_count: int = Field(0, description="Number of spectators")
    spectator_pool: Decimal = Field(Decimal('0'), description="Total spectator betting pool")
    
    # Metadata
    created_at: datetime = Field(..., description="Creation time")
    updated_at: datetime = Field(..., description="Last update time")


class ChessSpectatorBet(BaseModel):
    """Chess spectator betting model"""
    bet_type: Literal[
        "game_winner", "total_moves", "game_duration", "first_capture",
        "castling_race", "queen_trade", "pawn_promotion", "next_move"
    ] = Field(..., description="Type of bet")
    
    prediction: Dict[str, Any] = Field(..., description="Bet prediction data")
    stake_amount: Decimal = Field(..., gt=0, description="Stake amount")
    odds: Decimal = Field(..., gt=0, description="Odds at time of bet")
    potential_payout: Decimal = Field(..., gt=0, description="Potential payout")
    
    # Timing constraints
    placed_at_move: Optional[int] = Field(None, description="Move number when bet was placed")
    valid_until_move: Optional[int] = Field(None, description="Move number until bet is valid")


class ChessGameStats(BaseModel):
    """Chess game statistics"""
    total_games: int = Field(0, description="Total games played")
    wins: int = Field(0, description="Games won")
    draws: int = Field(0, description="Games drawn")
    losses: int = Field(0, description="Games lost")
    
    # Performance metrics
    win_rate: float = Field(0.0, description="Win percentage")
    average_game_length: float = Field(0.0, description="Average game length in moves")
    average_move_time: float = Field(0.0, description="Average time per move in seconds")
    
    # Opening statistics
    most_played_openings: List[Dict[str, Any]] = Field(
        default_factory=list, description="Most frequently played openings"
    )
    opening_success_rate: Dict[str, float] = Field(
        default_factory=dict, description="Success rate by opening"
    )
    
    # Time control performance
    performance_by_time_control: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="Performance statistics by time control"
    )


class ChessRatingHistory(BaseModel):
    """Chess rating history entry"""
    rating: int = Field(..., description="Rating after the game")
    rating_change: int = Field(..., description="Rating change from this game")
    game_id: UUID = Field(..., description="Game that caused the rating change")
    opponent_rating: int = Field(..., description="Opponent's rating")
    result: GameResult = Field(..., description="Game result")
    timestamp: datetime = Field(..., description="When the rating change occurred")


class ChessLeaderboard(BaseModel):
    """Chess leaderboard entry"""
    user_id: UUID = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    rating: int = Field(..., description="Current rating")
    games_played: int = Field(..., description="Total games played")
    win_rate: float = Field(..., description="Win percentage")
    rank: int = Field(..., description="Current rank")
    rating_change_24h: int = Field(0, description="Rating change in last 24 hours")


class ChessSearchFilters(BaseModel):
    """Chess game search filters"""
    variant: Optional[ChessVariant] = Field(None, description="Filter by variant")
    time_control: Optional[TimeControl] = Field(None, description="Filter by time control")
    rating_range: Optional[tuple[int, int]] = Field(None, description="Rating range filter")
    status: Optional[str] = Field(None, description="Game status filter")
    min_stake: Optional[Decimal] = Field(None, description="Minimum stake amount")
    max_stake: Optional[Decimal] = Field(None, description="Maximum stake amount")
    is_rated: Optional[bool] = Field(None, description="Filter by rated games")
    has_spectators: Optional[bool] = Field(None, description="Filter by spectator availability")