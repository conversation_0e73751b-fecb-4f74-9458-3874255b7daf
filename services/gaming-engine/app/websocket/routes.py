"""
BetBet Gaming Engine - WebSocket Routes
=======================================

WebSocket endpoint definitions for real-time communication.
"""

from fastapi import WebSocket, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.api.dependencies import get_database_session
from app.websocket.handlers import game_session_handler, spectator_handler, betting_handler
from app.websocket.chess_handler import chess_game_handler, chess_spectator_handler

# Configure logging
logger = structlog.get_logger()


async def websocket_session_endpoint(
    websocket: WebSocket,
    session_id: str,
    token: str = Query(..., description="Authentication token"),
    db: AsyncSession = Depends(get_database_session)
):
    """
    WebSocket endpoint for game session participants
    
    Handles real-time communication for game sessions including:
    - Game actions and state updates
    - Chat messages
    - Participant status changes
    - Connection management
    """
    await game_session_handler.handle_connection(websocket, session_id, token, db)


async def websocket_spectator_endpoint(
    websocket: WebSocket,
    session_id: str,
    token: str = Query(None, description="Optional authentication token")
):
    """
    WebSocket endpoint for spectators
    
    Handles spectator connections with limited functionality:
    - View-only access to game state
    - No game interaction capabilities
    - Optional authentication
    """
    await spectator_handler.handle_spectator_connection(websocket, session_id, token)


async def websocket_tournament_endpoint(
    websocket: WebSocket,
    tournament_id: str,
    token: str = Query(..., description="Authentication token")
):
    """
    WebSocket endpoint for tournament participants and spectators
    
    TODO: Implement tournament WebSocket handling
    """
    # TODO: Implement tournament WebSocket functionality
    await websocket.accept()
    await websocket.send_text('{"type": "error", "message": "Tournament WebSocket not yet implemented"}')
    await websocket.close()


async def websocket_betting_endpoint(
    websocket: WebSocket,
    market_id: str,
    token: str = Query(None, description="Optional authentication token")
):
    """
    WebSocket endpoint for betting market updates
    
    Handles real-time betting updates including:
    - Live odds changes
    - New bet notifications
    - Market status changes
    - Market statistics updates
    """
    await betting_handler.handle_betting_subscription(websocket, market_id, token)


async def websocket_chess_game_endpoint(
    websocket: WebSocket,
    game_id: str,
    token: str = Query(..., description="Authentication token")
    # db: AsyncSession = Depends(get_database_session)  # Temporarily removed for testing
):
    """
    WebSocket endpoint for chess game players
    
    Handles real-time P2P chess gameplay including:
    - Move synchronization between players
    - Game state updates
    - Draw offers and responses
    - Game actions (resign, abort)
    - Chat messages
    - Time management
    """
    await chess_game_handler.handle_chess_connection(websocket, game_id, token, None)  # Pass None for db temporarily


async def websocket_chess_spectator_endpoint(
    websocket: WebSocket,
    game_id: str,
    token: str = Query(None, description="Optional authentication token")
):
    """
    WebSocket endpoint for chess game spectators
    
    Handles real-time spectator features including:
    - Live game state viewing
    - Move history updates
    - Game end notifications
    - Read-only access to game data
    """
    await chess_spectator_handler.handle_spectator_connection(websocket, game_id, token)


async def websocket_test_endpoint(websocket: WebSocket):
    """
    Simple test WebSocket endpoint without authentication
    """
    # Accept connection with proper headers for browser compatibility
    await websocket.accept()
    logger.info(f"Test WebSocket connection established from {websocket.client}")

    try:
        # Send welcome message
        await websocket.send_json({
            "type": "welcome",
            "message": "WebSocket connection successful!",
            "timestamp": "2024-01-15T12:00:00Z"
        })

        # Keep connection alive and echo messages
        while True:
            data = await websocket.receive_text()
            logger.info(f"Received message: {data}")

            # Echo the message back
            await websocket.send_json({
                "type": "echo",
                "original_message": data,
                "timestamp": "2024-01-15T12:00:00Z"
            })

    except Exception as e:
        logger.error(f"Test WebSocket error: {e}")
    finally:
        logger.info("Test WebSocket connection closed")


# WebSocket route registration helper
def register_websocket_routes(app):
    """Register WebSocket routes with the FastAPI app"""

    print("🔌 Registering WebSocket routes...")
    logger.info("Starting WebSocket route registration")

    try:
        app.websocket("/ws/sessions/{session_id}")(websocket_session_endpoint)
        app.websocket("/ws/spectate/{session_id}")(websocket_spectator_endpoint)
        app.websocket("/ws/tournaments/{tournament_id}")(websocket_tournament_endpoint)
        app.websocket("/ws/betting/{market_id}")(websocket_betting_endpoint)

        # Chess-specific WebSocket routes
        app.websocket("/ws/chess/{game_id}")(websocket_chess_game_endpoint)
        app.websocket("/ws/chess/{game_id}/spectate")(websocket_chess_spectator_endpoint)

        # Test WebSocket route
        app.websocket("/ws/test")(websocket_test_endpoint)

        print("✅ WebSocket routes registered successfully")
        logger.info("WebSocket routes registered with chess support")

        # Print registered routes for debugging
        print("📋 Registered WebSocket routes:")
        print("   - /ws/sessions/{session_id}")
        print("   - /ws/spectate/{session_id}")
        print("   - /ws/tournaments/{tournament_id}")
        print("   - /ws/betting/{market_id}")
        print("   - /ws/chess/{game_id}")
        print("   - /ws/chess/{game_id}/spectate")
        print("   - /ws/test")

    except Exception as e:
        print(f"❌ Error registering WebSocket routes: {e}")
        logger.error(f"Failed to register WebSocket routes: {e}")
        raise