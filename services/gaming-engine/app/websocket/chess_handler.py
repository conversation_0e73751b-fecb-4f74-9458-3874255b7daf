"""
BetBet Gaming Engine - Chess WebSocket Handler
==============================================

Real-time P2P chess game synchronization and communication.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import structlog

# Import shared libraries
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from shared.core.messaging.websocket_manager import (
    get_websocket_manager, WebSocketMessage, MessageType
)
from shared.core.auth.jwt_handler import get_auth_manager

# Import chess service and models
from ..services.chess_service import chess_service
from ..models.chess import ChessMoveRequest, ChessGameAction

# Configure logging
logger = structlog.get_logger()


class ChessGameHandler:
    """WebSocket handler for real-time chess P2P gameplay"""
    
    def __init__(self):
        self.ws_manager = get_websocket_manager()
        self.auth_manager = get_auth_manager()
        # Track active chess connections
        self.chess_connections: Dict[str, Dict[str, Any]] = {}
    
    async def handle_chess_connection(
        self,
        websocket: WebSocket,
        game_id: str,
        token: str,
        db: AsyncSession
    ):
        """Handle WebSocket connection for a chess game"""
        try:
            # Use Clerk authentication for WebSocket connections
            if token:
                try:
                    # Try Clerk authentication first
                    from app.auth.clerk_auth import ClerkAuthManager
                    from app.api.dependencies import get_settings

                    settings = get_settings()
                    if settings.clerk_secret_key:
                        clerk_manager = ClerkAuthManager(settings.clerk_secret_key)
                        user_data = await clerk_manager.verify_token(token)
                        user_id = user_data.user_id
                        logger.info(f"Chess WebSocket connection authenticated via Clerk for user {user_id} to game {game_id}")
                    else:
                        # Fallback to mock for development
                        user_id = "test-user-123"
                        logger.info(f"Chess WebSocket connection using mock auth for user {user_id} to game {game_id}")

                except Exception as auth_error:
                    logger.warning(f"Clerk authentication failed, using mock auth: {auth_error}")
                    # Fallback to mock for development/testing
                    user_id = "test-user-123"
                    logger.info(f"Chess WebSocket connection using fallback mock auth for user {user_id} to game {game_id}")
            else:
                raise ValueError("No authentication token provided")

            # Verify user is a participant in this chess game
            # TODO: Add database check for chess game participation

            # Add to chess game room (WebSocketManager will accept the connection)
            room_name = f"chess_game_{game_id}"
            connection_id = await self.ws_manager.connect(
                websocket,
                user_id=user_id,
                room=room_name
            )
            
            # Track chess-specific connection data
            self.chess_connections[connection_id] = {
                "game_id": game_id,
                "user_id": user_id,
                "websocket": websocket,
                "connected_at": datetime.utcnow()
            }
            
            # Send current game state to the connecting player
            await self._send_chess_game_state(game_id, user_id)
            
            # Notify opponent of connection
            await self._broadcast_chess_event(
                game_id, user_id, "player_connected", {"user_id": user_id}
            )
            
            logger.info(
                "Player connected to chess game",
                game_id=game_id,
                user_id=user_id,
                connection_id=connection_id
            )
            
            # Handle chess-specific messages
            await self._handle_chess_messages(
                websocket, game_id, user_id, connection_id, db
            )
            
        except Exception as e:
            logger.error(
                "Chess connection error",
                game_id=game_id,
                error=str(e)
            )
            await websocket.close(code=4000, reason="Connection error")
    
    async def _handle_chess_messages(
        self,
        websocket: WebSocket,
        game_id: str,
        user_id: str,
        connection_id: str,
        db: AsyncSession
    ):
        """Handle incoming WebSocket messages for chess gameplay"""
        try:
            while True:
                # Receive message
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type")
                payload = message_data.get("payload", {})
                
                logger.debug(
                    "Received chess message",
                    game_id=game_id,
                    user_id=user_id,
                    message_type=message_type
                )
                
                # Route message based on type
                if message_type == "chess_move":
                    await self._handle_chess_move(game_id, user_id, payload, db)
                elif message_type == "game_action":
                    await self._handle_game_action(game_id, user_id, payload, db)
                elif message_type == "request_game_state":
                    await self._send_chess_game_state(game_id, user_id)
                elif message_type == "chat_message":
                    await self._handle_chess_chat(game_id, user_id, payload)
                elif message_type == "draw_offer":
                    await self._handle_draw_offer(game_id, user_id, payload)
                elif message_type == "draw_response":
                    await self._handle_draw_response(game_id, user_id, payload)
                elif message_type == "ping":
                    await self._handle_ping(websocket, user_id)
                elif message_type == "request_legal_moves":
                    await self._send_legal_moves(game_id, user_id)
                else:
                    logger.warning(
                        "Unknown chess message type",
                        game_id=game_id,
                        user_id=user_id,
                        message_type=message_type
                    )
                
        except WebSocketDisconnect:
            logger.info(
                "Player disconnected from chess game",
                game_id=game_id,
                user_id=user_id
            )
            await self._handle_chess_disconnect(game_id, user_id, connection_id)
        except Exception as e:
            logger.error(
                "Chess message handling error",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_chess_move(
        self,
        game_id: str,
        user_id: str,
        payload: Dict[str, Any],
        db: AsyncSession
    ):
        """Handle chess move from a player"""
        try:
            # Parse move request
            move_uci = payload.get("move_uci")
            offer_draw = payload.get("offer_draw", False)
            
            if not move_uci:
                await self._send_error_to_user(user_id, "Missing move_uci in payload")
                return
            
            # Create move request
            move_request = ChessMoveRequest(
                move_uci=move_uci,
                offer_draw=offer_draw
            )
            
            # Process move through chess service
            success, message, chess_move = await chess_service.make_move(
                UUID(game_id), UUID(user_id), move_request
            )
            
            if not success:
                # Send error back to player who made invalid move
                await self._send_error_to_user(user_id, message)
                return
            
            # Move was successful - broadcast to all players and spectators
            move_data = {
                "move_uci": move_uci,
                "move_san": chess_move.move_san if chess_move else "",
                "player_id": user_id,
                "is_capture": chess_move.is_capture if chess_move else False,
                "is_check": chess_move.is_check if chess_move else False,
                "is_checkmate": chess_move.is_checkmate if chess_move else False,
                "position_after": chess_move.position_after if chess_move else "",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Broadcast move to all connected clients
            await self._broadcast_chess_event(
                game_id, user_id, "chess_move", move_data
            )
            
            # Send updated game state to all players
            await self._broadcast_game_state_update(game_id)
            
            # Check for game end conditions
            game_state = await chess_service.get_game_state(UUID(game_id))
            if game_state and (game_state.is_checkmate or game_state.is_stalemate):
                await self._handle_game_end(game_id, game_state)
            
            # Check for time forfeit
            is_timeout, timeout_result = await chess_service.check_time_forfeit(UUID(game_id))
            if is_timeout:
                await self._handle_time_forfeit(game_id, timeout_result)
            
            logger.info(
                "Processed chess move",
                game_id=game_id,
                user_id=user_id,
                move_uci=move_uci,
                success=success
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle chess move",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
            await self._send_error_to_user(user_id, f"Move processing error: {str(e)}")
    
    async def _handle_game_action(
        self,
        game_id: str,
        user_id: str,
        payload: Dict[str, Any],
        db: AsyncSession
    ):
        """Handle game actions like resign, abort, etc."""
        try:
            action_type = payload.get("action")
            reason = payload.get("reason")
            
            if not action_type:
                await self._send_error_to_user(user_id, "Missing action in payload")
                return
            
            # Create game action
            game_action = ChessGameAction(action=action_type, reason=reason)
            
            # Process action through chess service
            if action_type == "resign":
                success, message = await chess_service.resign_game(UUID(game_id), UUID(user_id))
                if success:
                    await self._broadcast_chess_event(
                        game_id, user_id, "game_resigned", {"player_id": user_id}
                    )
                    await self._handle_game_end(game_id, None, f"{user_id}_resignation")
            
            elif action_type == "abort":
                # Handle game abort
                await self._broadcast_chess_event(
                    game_id, user_id, "game_aborted", {"player_id": user_id}
                )
                await self._handle_game_end(game_id, None, "aborted")
            
            logger.info(
                "Processed chess game action",
                game_id=game_id,
                user_id=user_id,
                action=action_type
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle game action",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_draw_offer(
        self,
        game_id: str,
        user_id: str,
        payload: Dict[str, Any]
    ):
        """Handle draw offer from a player"""
        try:
            success, message = await chess_service.offer_draw(UUID(game_id), UUID(user_id))
            
            if success:
                # Broadcast draw offer to opponent
                await self._broadcast_chess_event(
                    game_id, user_id, "draw_offered", {"offered_by": user_id}
                )
            else:
                await self._send_error_to_user(user_id, message)
            
            logger.info(
                "Processed draw offer",
                game_id=game_id,
                user_id=user_id,
                success=success
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle draw offer",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_draw_response(
        self,
        game_id: str,
        user_id: str,
        payload: Dict[str, Any]
    ):
        """Handle draw offer response (accept/decline)"""
        try:
            accepted = payload.get("accepted", False)
            
            if accepted:
                success, message = await chess_service.accept_draw(UUID(game_id), UUID(user_id))
                if success:
                    await self._broadcast_chess_event(
                        game_id, user_id, "draw_accepted", {"accepted_by": user_id}
                    )
                    await self._handle_game_end(game_id, None, "draw_agreement")
                else:
                    await self._send_error_to_user(user_id, message)
            else:
                # Draw declined
                await self._broadcast_chess_event(
                    game_id, user_id, "draw_declined", {"declined_by": user_id}
                )
            
            logger.info(
                "Processed draw response",
                game_id=game_id,
                user_id=user_id,
                accepted=accepted
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle draw response",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _send_chess_game_state(self, game_id: str, user_id: str):
        """Send current chess game state to a specific user"""
        try:
            game_state = await chess_service.get_game_state(UUID(game_id))
            
            if not game_state:
                await self._send_error_to_user(user_id, "Game state not found")
                return
            
            state_data = {
                "type": "game_state_update",
                "payload": game_state.dict(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.GAME_STATE,
                data=state_data,
                timestamp=datetime.utcnow().timestamp(),
                user_id=None
            )
            
            await self.ws_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(
                "Failed to send chess game state",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _send_legal_moves(self, game_id: str, user_id: str):
        """Send legal moves to a specific user"""
        try:
            legal_moves = await chess_service.get_legal_moves(UUID(game_id))
            
            moves_data = {
                "type": "legal_moves",
                "payload": {"legal_moves": legal_moves},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.GAME_ACTION,
                data=moves_data,
                timestamp=datetime.utcnow().timestamp(),
                user_id=None
            )
            
            await self.ws_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(
                "Failed to send legal moves",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _broadcast_game_state_update(self, game_id: str):
        """Broadcast updated game state to all connected players and spectators"""
        try:
            game_state = await chess_service.get_game_state(UUID(game_id))
            
            if not game_state:
                return
            
            state_data = {
                "type": "game_state_update",
                "payload": game_state.dict(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Broadcast to players
            room_name = f"chess_game_{game_id}"
            message = WebSocketMessage(
                type=MessageType.GAME_STATE,
                data=state_data,
                timestamp=datetime.utcnow().timestamp(),
                user_id=None
            )
            
            await self.ws_manager.broadcast_to_room(room_name, message)
            
            # Also broadcast to spectators
            spectator_room = f"chess_spectator_{game_id}"
            await self.ws_manager.broadcast_to_room(spectator_room, message)
            
        except Exception as e:
            logger.error(
                "Failed to broadcast game state update",
                game_id=game_id,
                error=str(e)
            )
    
    async def _broadcast_chess_event(
        self,
        game_id: str,
        sender_id: str,
        event_type: str,
        event_data: Dict[str, Any]
    ):
        """Broadcast chess-specific event to all connected clients"""
        try:
            message_data = {
                "type": event_type,
                "payload": event_data,
                "sender_id": sender_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.GAME_ACTION,
                data=message_data,
                timestamp=datetime.utcnow().timestamp(),
                user_id=sender_id
            )
            
            # Broadcast to players
            room_name = f"chess_game_{game_id}"
            await self.ws_manager.broadcast_to_room(room_name, message)
            
            # Also broadcast to spectators
            spectator_room = f"chess_spectator_{game_id}"
            await self.ws_manager.broadcast_to_room(spectator_room, message)
            
        except Exception as e:
            logger.error(
                "Failed to broadcast chess event",
                game_id=game_id,
                event_type=event_type,
                error=str(e)
            )
    
    async def _handle_chess_chat(
        self,
        game_id: str,
        user_id: str,
        payload: Dict[str, Any]
    ):
        """Handle chat message during chess game"""
        try:
            message_text = payload.get("message", "").strip()
            if not message_text or len(message_text) > 500:
                return
            
            chat_data = {
                "user_id": user_id,
                "message": message_text,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self._broadcast_chess_event(
                game_id, user_id, "chat_message", chat_data
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle chess chat",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_ping(self, websocket: WebSocket, user_id: str):
        """Handle ping/keepalive message"""
        try:
            pong_message = {
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send_text(json.dumps(pong_message))
        except Exception as e:
            logger.error("Failed to send pong", user_id=user_id, error=str(e))
    
    async def _send_error_to_user(self, user_id: str, error_message: str):
        """Send error message to specific user"""
        try:
            error_data = {
                "type": "error",
                "payload": {"message": error_message},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.ERROR,
                data=error_data,
                timestamp=datetime.utcnow().timestamp(),
                user_id=None
            )
            
            await self.ws_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(
                "Failed to send error to user",
                user_id=user_id,
                error_message=error_message,
                error=str(e)
            )
    
    async def _handle_game_end(
        self,
        game_id: str,
        game_state: Optional[Any] = None,
        result_reason: Optional[str] = None
    ):
        """Handle game end - update database, ratings, settle bets"""
        try:
            # Determine game result
            result = "draw"
            if game_state:
                if game_state.is_checkmate:
                    result = "white_wins" if game_state.current_turn == "black" else "black_wins"
                elif game_state.is_stalemate:
                    result = "stalemate"
            elif result_reason:
                result = result_reason
            
            # Broadcast game end event
            end_data = {
                "result": result,
                "reason": result_reason or "game_completed",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self._broadcast_chess_event(
                game_id, None, "game_ended", end_data
            )
            
            # TODO: Update database with game result
            # TODO: Calculate and update player ratings
            # TODO: Settle spectator bets
            # TODO: Update player statistics
            
            # Clean up game resources after a delay
            await chess_service.cleanup_game(UUID(game_id))
            
            logger.info(
                "Handled chess game end",
                game_id=game_id,
                result=result,
                reason=result_reason
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle game end",
                game_id=game_id,
                error=str(e)
            )
    
    async def _handle_time_forfeit(self, game_id: str, timeout_result: str):
        """Handle time forfeit situation"""
        try:
            forfeit_data = {
                "result": timeout_result,
                "reason": "time_forfeit",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self._broadcast_chess_event(
                game_id, None, "time_forfeit", forfeit_data
            )
            
            await self._handle_game_end(game_id, None, timeout_result)
            
        except Exception as e:
            logger.error(
                "Failed to handle time forfeit",
                game_id=game_id,
                error=str(e)
            )
    
    async def _handle_chess_disconnect(
        self,
        game_id: str,
        user_id: str,
        connection_id: str
    ):
        """Handle player disconnect from chess game"""
        try:
            # Remove from WebSocket manager
            await self.ws_manager.disconnect(connection_id)
            
            # Remove from chess connections tracking
            if connection_id in self.chess_connections:
                del self.chess_connections[connection_id]
            
            # Notify opponent of disconnect
            await self._broadcast_chess_event(
                game_id, user_id, "player_disconnected", {"user_id": user_id}
            )
            
            # TODO: Start disconnect timer - if player doesn't reconnect within time limit,
            # they forfeit the game
            
            logger.info(
                "Handled chess player disconnect",
                game_id=game_id,
                user_id=user_id
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle chess disconnect",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )


# Chess spectator handler for live game watching
class ChessSpectatorHandler:
    """WebSocket handler for chess game spectators"""
    
    def __init__(self):
        self.ws_manager = get_websocket_manager()
        self.auth_manager = get_auth_manager()
    
    async def handle_spectator_connection(
        self,
        websocket: WebSocket,
        game_id: str,
        token: Optional[str] = None
    ):
        """Handle WebSocket connection for chess spectators"""
        try:
            user_id = None
            
            # Optional authentication for spectators
            if token:
                try:
                    token_data = self.auth_manager.verify_token(token)
                    user_id = token_data.user_id
                except:
                    pass  # Allow anonymous spectators
            
            # Add to chess spectator room (WebSocketManager will accept the connection)
            room_name = f"chess_spectator_{game_id}"
            connection_id = await self.ws_manager.connect(
                websocket,
                user_id=user_id,
                room=room_name
            )
            
            # Send current game state to spectator
            if user_id:
                await self._send_spectator_game_state(game_id, user_id)
            
            logger.info(
                "Chess spectator connected",
                game_id=game_id,
                user_id=user_id,
                connection_id=connection_id
            )
            
            # Handle spectator messages (limited functionality)
            await self._handle_spectator_messages(
                websocket, game_id, user_id, connection_id
            )
            
        except Exception as e:
            logger.error(
                "Chess spectator connection error",
                game_id=game_id,
                error=str(e)
            )
            await websocket.close(code=4000, reason="Connection error")
    
    async def _handle_spectator_messages(
        self,
        websocket: WebSocket,
        game_id: str,
        user_id: Optional[str],
        connection_id: str
    ):
        """Handle messages from chess spectators"""
        try:
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(pong_message))
                elif message_type == "request_game_state":
                    if user_id:
                        await self._send_spectator_game_state(game_id, user_id)
                else:
                    logger.debug(
                        "Ignored chess spectator message",
                        game_id=game_id,
                        user_id=user_id,
                        message_type=message_type
                    )
                
        except WebSocketDisconnect:
            logger.info(
                "Chess spectator disconnected",
                game_id=game_id,
                user_id=user_id
            )
            await self.ws_manager.disconnect(connection_id)
        except Exception as e:
            logger.error(
                "Chess spectator message handling error",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _send_spectator_game_state(self, game_id: str, user_id: str):
        """Send current game state to spectator"""
        try:
            game_state = await chess_service.get_game_state(UUID(game_id))
            
            if not game_state:
                return
            
            # Spectators get read-only game state
            spectator_state = {
                "type": "spectator_game_state",
                "payload": {
                    "fen": game_state.fen,
                    "moves": game_state.moves,
                    "current_turn": game_state.current_turn,
                    "move_number": game_state.move_number,
                    "is_check": game_state.is_check,
                    "is_checkmate": game_state.is_checkmate,
                    "is_stalemate": game_state.is_stalemate,
                    "white_time_remaining": game_state.white_time_remaining,
                    "black_time_remaining": game_state.black_time_remaining,
                    "opening_name": game_state.opening_name
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.GAME_STATE,
                data=spectator_state,
                timestamp=datetime.utcnow().timestamp(),
                user_id=None
            )
            
            await self.ws_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(
                "Failed to send spectator game state",
                game_id=game_id,
                user_id=user_id,
                error=str(e)
            )


# Handler instances
chess_game_handler = ChessGameHandler()
chess_spectator_handler = ChessSpectatorHandler()