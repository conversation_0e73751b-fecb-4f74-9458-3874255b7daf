"""
BetBet Gaming Engine - WebSocket Handlers
=========================================

WebSocket connection handlers and event processors for real-time gaming.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
import structlog

# Import shared libraries
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from shared.core.messaging.websocket_manager import (
    get_websocket_manager, WebSocketMessage, MessageType
)
from shared.core.auth.jwt_handler import get_auth_manager

# Import models
from app.models.sessions import GameSession, SessionParticipant, GameState, ParticipantStatus
from app.models.games import Game
from app.database.models import BettingMarket, BettingOutcome, UserBet, BettingMarketOddsHistory
from app.models.betting import (
    OddsUpdateEvent, NewBetEvent, MarketStatusChangeEvent, BettingWebSocketEvent
)
from app.api.dependencies import get_database_session

# Configure logging
logger = structlog.get_logger()


class GameSessionHandler:
    """WebSocket handler for game session events"""
    
    def __init__(self):
        self.ws_manager = get_websocket_manager()
        self.auth_manager = get_auth_manager()
    
    async def handle_connection(
        self,
        websocket: WebSocket,
        session_id: str,
        token: str,
        db: AsyncSession
    ):
        """Handle WebSocket connection for a game session"""
        try:
            # Verify authentication
            token_data = self.auth_manager.verify_token(token)
            user_id = token_data.user_id
            
            # Verify session exists and user is participant
            session_uuid = UUID(session_id)
            session_query = select(GameSession).where(GameSession.id == session_uuid)
            session_result = await db.execute(session_query)
            session = session_result.scalar_one_or_none()
            
            if not session:
                await websocket.close(code=4004, reason="Session not found")
                return
            
            participant_query = select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_uuid,
                    SessionParticipant.user_id == UUID(user_id)
                )
            )
            participant_result = await db.execute(participant_query)
            participant = participant_result.scalar_one_or_none()
            
            if not participant:
                await websocket.close(code=4003, reason="Not a session participant")
                return
            
            # Accept connection
            await websocket.accept()
            
            # Add to session room
            room_name = f"session_{session_id}"
            connection_id = await self.ws_manager.connect(
                websocket, 
                user_id=user_id,
                room=room_name
            )
            
            # Update participant connection info
            participant.connection_id = connection_id
            participant.last_activity = datetime.utcnow()
            await db.commit()
            
            # Send session state
            await self._send_session_state(session_id, user_id, db)
            
            # Notify other participants
            await self._broadcast_participant_event(
                session_id, user_id, "user_connected", db
            )
            
            logger.info(
                "User connected to session",
                session_id=session_id,
                user_id=user_id,
                connection_id=connection_id
            )
            
            # Handle messages
            await self._handle_session_messages(
                websocket, session_id, user_id, connection_id, db
            )
            
        except Exception as e:
            logger.error(
                "Session connection error",
                session_id=session_id,
                error=str(e)
            )
            await websocket.close(code=4000, reason="Connection error")
    
    async def _handle_session_messages(
        self,
        websocket: WebSocket,
        session_id: str,
        user_id: str,
        connection_id: str,
        db: AsyncSession
    ):
        """Handle incoming WebSocket messages for a session"""
        try:
            while True:
                # Receive message
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # Process message based on type
                message_type = message_data.get("type")
                payload = message_data.get("payload", {})
                
                logger.debug(
                    "Received session message",
                    session_id=session_id,
                    user_id=user_id,
                    message_type=message_type
                )
                
                if message_type == "game_action":
                    await self._handle_game_action(session_id, user_id, payload, db)
                elif message_type == "chat_message":
                    await self._handle_chat_message(session_id, user_id, payload, db)
                elif message_type == "ready_status":
                    await self._handle_ready_status(session_id, user_id, payload, db)
                elif message_type == "ping":
                    await self._handle_ping(websocket, user_id)
                else:
                    logger.warning(
                        "Unknown message type",
                        session_id=session_id,
                        user_id=user_id,
                        message_type=message_type
                    )
                
                # Update last activity
                await self._update_participant_activity(session_id, user_id, db)
                
        except WebSocketDisconnect:
            logger.info(
                "User disconnected from session",
                session_id=session_id,
                user_id=user_id
            )
            await self._handle_disconnect(session_id, user_id, connection_id, db)
        except Exception as e:
            logger.error(
                "Session message handling error",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _send_session_state(self, session_id: str, user_id: str, db: AsyncSession):
        """Send current session state to user"""
        try:
            # Get session data
            session_uuid = UUID(session_id)
            session_query = select(GameSession).where(GameSession.id == session_uuid)
            session_result = await db.execute(session_query)
            session = session_result.scalar_one_or_none()
            
            if not session:
                return
            
            # Get participants
            participants_query = select(SessionParticipant).where(
                SessionParticipant.session_id == session_uuid
            ).order_by(SessionParticipant.join_order)
            participants_result = await db.execute(participants_query)
            participants = participants_result.scalars().all()
            
            # Get game state if exists
            game_state_query = select(GameState).where(
                GameState.session_id == session_uuid
            ).order_by(GameState.updated_at.desc())
            game_state_result = await db.execute(game_state_query)
            game_state = game_state_result.scalar_one_or_none()
            
            # Build state message
            state_data = {
                "session": {
                    "id": str(session.id),
                    "name": session.session_name,
                    "status": session.status,
                    "current_participants": session.current_participants,
                    "max_participants": session.max_participants,
                    "entry_fee": float(session.entry_fee),
                    "total_prize_pool": float(session.total_prize_pool)
                },
                "participants": [
                    {
                        "user_id": str(p.user_id),
                        "status": p.status,
                        "is_spectator": p.is_spectator,
                        "join_order": p.join_order,
                        "current_score": float(p.current_score),
                        "is_connected": bool(p.connection_id)
                    }
                    for p in participants
                ],
                "game_state": {
                    "round_number": game_state.round_number if game_state else 1,
                    "state_data": game_state.state_data if game_state else {},
                    "turn_order": [str(uid) for uid in game_state.turn_order] if game_state else [],
                    "current_turn_user_id": str(game_state.current_turn_user_id) if game_state and game_state.current_turn_user_id else None
                } if game_state else None
            }
            
            message = WebSocketMessage(
                type=MessageType.GAME_STATE,
                data=state_data,
                timestamp=datetime.utcnow(),
                sender_id=None  # System message
            )
            
            await self.ws_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(
                "Failed to send session state",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_game_action(
        self, session_id: str, user_id: str, payload: Dict[str, Any], db: AsyncSession
    ):
        """Handle game action from a participant"""
        try:
            action_type = payload.get("action_type")
            action_data = payload.get("action_data", {})
            
            # Validate user can perform action
            # TODO: Implement game-specific action validation
            
            # Update game state
            # TODO: Implement game state updates
            
            # Broadcast action to all participants
            room_name = f"session_{session_id}"
            message = WebSocketMessage(
                type=MessageType.GAME_ACTION,
                data={
                    "user_id": user_id,
                    "action_type": action_type,
                    "action_data": action_data,
                    "timestamp": datetime.utcnow().isoformat()
                },
                timestamp=datetime.utcnow(),
                sender_id=user_id
            )
            
            await self.ws_manager.broadcast_to_room(room_name, message)
            
            logger.info(
                "Processed game action",
                session_id=session_id,
                user_id=user_id,
                action_type=action_type
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle game action",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_chat_message(
        self, session_id: str, user_id: str, payload: Dict[str, Any], db: AsyncSession
    ):
        """Handle chat message from a participant"""
        try:
            message_text = payload.get("message", "").strip()
            if not message_text or len(message_text) > 500:
                return
            
            # Broadcast chat message to all participants
            room_name = f"session_{session_id}"
            message = WebSocketMessage(
                type=MessageType.CHAT,
                data={
                    "user_id": user_id,
                    "message": message_text,
                    "timestamp": datetime.utcnow().isoformat()
                },
                timestamp=datetime.utcnow(),
                sender_id=user_id
            )
            
            await self.ws_manager.broadcast_to_room(room_name, message)
            
            logger.debug(
                "Processed chat message",
                session_id=session_id,
                user_id=user_id,
                message_length=len(message_text)
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle chat message",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_ready_status(
        self, session_id: str, user_id: str, payload: Dict[str, Any], db: AsyncSession
    ):
        """Handle ready status change from a participant"""
        try:
            is_ready = payload.get("ready", False)
            
            # Update participant status
            session_uuid = UUID(session_id)
            participant_query = select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_uuid,
                    SessionParticipant.user_id == UUID(user_id)
                )
            )
            participant_result = await db.execute(participant_query)
            participant = participant_result.scalar_one_or_none()
            
            if participant:
                participant.status = ParticipantStatus.READY if is_ready else ParticipantStatus.JOINED
                participant.updated_at = datetime.utcnow()
                await db.commit()
                
                # Broadcast status change
                await self._broadcast_participant_event(
                    session_id, user_id, "ready_status_changed", db, {"ready": is_ready}
                )
                
                logger.info(
                    "Updated participant ready status",
                    session_id=session_id,
                    user_id=user_id,
                    is_ready=is_ready
                )
            
        except Exception as e:
            logger.error(
                "Failed to handle ready status",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_ping(self, websocket: WebSocket, user_id: str):
        """Handle ping/keepalive message"""
        try:
            pong_message = {
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send_text(json.dumps(pong_message))
        except Exception as e:
            logger.error("Failed to send pong", user_id=user_id, error=str(e))
    
    async def _broadcast_participant_event(
        self,
        session_id: str,
        user_id: str,
        event_type: str,
        db: AsyncSession,
        additional_data: Dict[str, Any] = None
    ):
        """Broadcast participant event to all session participants"""
        try:
            room_name = f"session_{session_id}"
            data = {
                "user_id": user_id,
                "event_type": event_type,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if additional_data:
                data.update(additional_data)
            
            message = WebSocketMessage(
                type=MessageType.PARTICIPANT_EVENT,
                data=data,
                timestamp=datetime.utcnow(),
                sender_id=user_id
            )
            
            await self.ws_manager.broadcast_to_room(room_name, message)
            
        except Exception as e:
            logger.error(
                "Failed to broadcast participant event",
                session_id=session_id,
                user_id=user_id,
                event_type=event_type,
                error=str(e)
            )
    
    async def _update_participant_activity(
        self, session_id: str, user_id: str, db: AsyncSession
    ):
        """Update participant's last activity timestamp"""
        try:
            session_uuid = UUID(session_id)
            participant_query = select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_uuid,
                    SessionParticipant.user_id == UUID(user_id)
                )
            )
            participant_result = await db.execute(participant_query)
            participant = participant_result.scalar_one_or_none()
            
            if participant:
                participant.last_activity = datetime.utcnow()
                await db.commit()
                
        except Exception as e:
            logger.error(
                "Failed to update participant activity",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_disconnect(
        self, session_id: str, user_id: str, connection_id: str, db: AsyncSession
    ):
        """Handle participant disconnect"""
        try:
            # Remove from WebSocket manager
            await self.ws_manager.disconnect(connection_id)
            
            # Update participant connection status
            session_uuid = UUID(session_id)
            participant_query = select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_uuid,
                    SessionParticipant.user_id == UUID(user_id)
                )
            )
            participant_result = await db.execute(participant_query)
            participant = participant_result.scalar_one_or_none()
            
            if participant:
                participant.connection_id = None
                participant.status = ParticipantStatus.DISCONNECTED
                participant.updated_at = datetime.utcnow()
                await db.commit()
                
                # Notify other participants
                await self._broadcast_participant_event(
                    session_id, user_id, "user_disconnected", db
                )
            
            logger.info(
                "Handled participant disconnect",
                session_id=session_id,
                user_id=user_id
            )
            
        except Exception as e:
            logger.error(
                "Failed to handle disconnect",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )


class SpectatorHandler:
    """WebSocket handler for spectator connections"""
    
    def __init__(self):
        self.ws_manager = get_websocket_manager()
        self.auth_manager = get_auth_manager()
    
    async def handle_spectator_connection(
        self,
        websocket: WebSocket,
        session_id: str,
        token: Optional[str] = None
    ):
        """Handle WebSocket connection for spectators"""
        try:
            user_id = None
            
            # Optional authentication for spectators
            if token:
                try:
                    token_data = self.auth_manager.verify_token(token)
                    user_id = token_data.user_id
                except:
                    pass  # Allow anonymous spectators
            
            # Accept connection
            await websocket.accept()
            
            # Add to spectator room
            room_name = f"spectator_{session_id}"
            connection_id = await self.ws_manager.connect(
                websocket,
                user_id=user_id,
                room=room_name
            )
            
            logger.info(
                "Spectator connected",
                session_id=session_id,
                user_id=user_id,
                connection_id=connection_id
            )
            
            # Handle spectator messages (limited functionality)
            await self._handle_spectator_messages(
                websocket, session_id, user_id, connection_id
            )
            
        except Exception as e:
            logger.error(
                "Spectator connection error",
                session_id=session_id,
                error=str(e)
            )
            await websocket.close(code=4000, reason="Connection error")
    
    async def _handle_spectator_messages(
        self,
        websocket: WebSocket,
        session_id: str,
        user_id: Optional[str],
        connection_id: str
    ):
        """Handle messages from spectators (limited functionality)"""
        try:
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(pong_message))
                else:
                    # Spectators have limited message types
                    logger.debug(
                        "Ignored spectator message",
                        session_id=session_id,
                        user_id=user_id,
                        message_type=message_type
                    )
                
        except WebSocketDisconnect:
            logger.info(
                "Spectator disconnected",
                session_id=session_id,
                user_id=user_id
            )
            await self.ws_manager.disconnect(connection_id)
        except Exception as e:
            logger.error(
                "Spectator message handling error",
                session_id=session_id,
                user_id=user_id,
                error=str(e)
            )


class BettingHandler:
    """WebSocket handler for betting updates and live odds"""
    
    def __init__(self):
        self.ws_manager = get_websocket_manager()
        self.auth_manager = get_auth_manager()
    
    async def broadcast_odds_update(
        self,
        market_id: str,
        outcome_id: str,
        old_odds: float,
        new_odds: float,
        db: AsyncSession
    ):
        """Broadcast odds update to all market subscribers"""
        try:
            # Get market info for context
            market_query = select(BettingMarket).where(BettingMarket.id == UUID(market_id))
            market_result = await db.execute(market_query)
            market = market_result.scalar_one_or_none()
            
            if not market:
                logger.warning("Market not found for odds update", market_id=market_id)
                return
            
            # Create odds update event
            event_data = {
                "event_type": "odds_update",
                "market_id": market_id,
                "outcome_id": outcome_id,
                "old_odds": old_odds,
                "new_odds": new_odds,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.BETTING_UPDATE,
                data=event_data,
                timestamp=datetime.utcnow(),
                sender_id=None  # System message
            )
            
            # Broadcast to market room
            market_room = f"betting_market_{market_id}"
            await self.ws_manager.broadcast_to_room(market_room, message)
            
            # Also broadcast to session/tournament rooms
            if market.session_id:
                session_room = f"session_{market.session_id}"
                await self.ws_manager.broadcast_to_room(session_room, message)
                
                spectator_room = f"spectator_{market.session_id}"
                await self.ws_manager.broadcast_to_room(spectator_room, message)
            
            if market.tournament_id:
                tournament_room = f"tournament_{market.tournament_id}"
                await self.ws_manager.broadcast_to_room(tournament_room, message)
            
            logger.info(
                "Broadcasted odds update",
                market_id=market_id,
                outcome_id=outcome_id,
                old_odds=old_odds,
                new_odds=new_odds
            )
            
        except Exception as e:
            logger.error(
                "Failed to broadcast odds update",
                market_id=market_id,
                outcome_id=outcome_id,
                error=str(e)
            )
    
    async def broadcast_new_bet(
        self,
        market_id: str,
        outcome_id: str,
        bet_amount: float,
        total_pool: float,
        db: AsyncSession
    ):
        """Broadcast new bet placement to market subscribers"""
        try:
            # Get market info for context
            market_query = select(BettingMarket).where(BettingMarket.id == UUID(market_id))
            market_result = await db.execute(market_query)
            market = market_result.scalar_one_or_none()
            
            if not market:
                logger.warning("Market not found for new bet broadcast", market_id=market_id)
                return
            
            # Create new bet event
            event_data = {
                "event_type": "new_bet",
                "market_id": market_id,
                "outcome_id": outcome_id,
                "bet_amount": bet_amount,
                "total_pool": total_pool,
                "total_bets": market.total_bets,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.BETTING_UPDATE,
                data=event_data,
                timestamp=datetime.utcnow(),
                sender_id=None  # System message
            )
            
            # Broadcast to market room
            market_room = f"betting_market_{market_id}"
            await self.ws_manager.broadcast_to_room(market_room, message)
            
            # Also broadcast to session/tournament rooms
            if market.session_id:
                session_room = f"session_{market.session_id}"
                await self.ws_manager.broadcast_to_room(session_room, message)
                
                spectator_room = f"spectator_{market.session_id}"
                await self.ws_manager.broadcast_to_room(spectator_room, message)
            
            if market.tournament_id:
                tournament_room = f"tournament_{market.tournament_id}"
                await self.ws_manager.broadcast_to_room(tournament_room, message)
            
            logger.info(
                "Broadcasted new bet",
                market_id=market_id,
                outcome_id=outcome_id,
                bet_amount=bet_amount,
                total_pool=total_pool
            )
            
        except Exception as e:
            logger.error(
                "Failed to broadcast new bet",
                market_id=market_id,
                outcome_id=outcome_id,
                error=str(e)
            )
    
    async def broadcast_market_status_change(
        self,
        market_id: str,
        old_status: str,
        new_status: str,
        db: AsyncSession
    ):
        """Broadcast market status change to subscribers"""
        try:
            # Get market info for context
            market_query = select(BettingMarket).where(BettingMarket.id == UUID(market_id))
            market_result = await db.execute(market_query)
            market = market_result.scalar_one_or_none()
            
            if not market:
                logger.warning("Market not found for status change broadcast", market_id=market_id)
                return
            
            # Create market status change event
            event_data = {
                "event_type": "market_status_change",
                "market_id": market_id,
                "market_name": market.name,
                "old_status": old_status,
                "new_status": new_status,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            message = WebSocketMessage(
                type=MessageType.BETTING_UPDATE,
                data=event_data,
                timestamp=datetime.utcnow(),
                sender_id=None  # System message
            )
            
            # Broadcast to market room
            market_room = f"betting_market_{market_id}"
            await self.ws_manager.broadcast_to_room(market_room, message)
            
            # Also broadcast to session/tournament rooms
            if market.session_id:
                session_room = f"session_{market.session_id}"
                await self.ws_manager.broadcast_to_room(session_room, message)
                
                spectator_room = f"spectator_{market.session_id}"
                await self.ws_manager.broadcast_to_room(spectator_room, message)
            
            if market.tournament_id:
                tournament_room = f"tournament_{market.tournament_id}"
                await self.ws_manager.broadcast_to_room(tournament_room, message)
            
            logger.info(
                "Broadcasted market status change",
                market_id=market_id,
                old_status=old_status,
                new_status=new_status
            )
            
        except Exception as e:
            logger.error(
                "Failed to broadcast market status change",
                market_id=market_id,
                old_status=old_status,
                new_status=new_status,
                error=str(e)
            )
    
    async def handle_betting_subscription(
        self,
        websocket: WebSocket,
        market_id: str,
        token: Optional[str] = None
    ):
        """Handle WebSocket connection for betting market updates"""
        try:
            user_id = None
            
            # Optional authentication for betting updates
            if token:
                try:
                    token_data = self.auth_manager.verify_token(token)
                    user_id = token_data.user_id
                except:
                    pass  # Allow anonymous betting subscription
            
            # Accept connection
            await websocket.accept()
            
            # Add to betting market room
            room_name = f"betting_market_{market_id}"
            connection_id = await self.ws_manager.connect(
                websocket,
                user_id=user_id,
                room=room_name
            )
            
            logger.info(
                "User subscribed to betting market",
                market_id=market_id,
                user_id=user_id,
                connection_id=connection_id
            )
            
            # Send initial market state
            await self._send_market_state(market_id, user_id)
            
            # Handle betting subscription messages
            await self._handle_betting_messages(
                websocket, market_id, user_id, connection_id
            )
            
        except Exception as e:
            logger.error(
                "Betting subscription error",
                market_id=market_id,
                error=str(e)
            )
            await websocket.close(code=4000, reason="Connection error")
    
    async def _send_market_state(self, market_id: str, user_id: Optional[str]):
        """Send current market state to subscriber"""
        try:
            from app.api.dependencies import get_read_database_session
            
            # Get database session
            session_gen = get_read_database_session()
            db = await session_gen.__anext__()
            
            try:
                # Get market with outcomes
                market_query = select(BettingMarket).options(
                    selectinload(BettingMarket.outcomes)
                ).where(BettingMarket.id == UUID(market_id))
                
                market_result = await db.execute(market_query)
                market = market_result.scalar_one_or_none()
                
                if not market:
                    logger.warning("Market not found for state send", market_id=market_id)
                    return
                
                # Build market state
                market_state = {
                    "market": {
                        "id": str(market.id),
                        "name": market.name,
                        "description": market.description,
                        "market_type": market.market_type,
                        "status": market.status,
                        "total_pool": float(market.total_pool),
                        "total_bets": market.total_bets,
                        "opens_at": market.opens_at.isoformat() if market.opens_at else None,
                        "closes_at": market.closes_at.isoformat() if market.closes_at else None
                    },
                    "outcomes": [
                        {
                            "id": str(outcome.id),
                            "name": outcome.name,
                            "description": outcome.description,
                            "outcome_type": outcome.outcome_type,
                            "odds": float(outcome.odds),
                            "implied_probability": float(outcome.implied_probability) if outcome.implied_probability else None,
                            "total_bet_amount": float(outcome.total_bet_amount),
                            "bet_count": outcome.bet_count,
                            "is_active": outcome.is_active,
                            "is_winning_outcome": outcome.is_winning_outcome
                        }
                        for outcome in market.outcomes
                    ],
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                message = WebSocketMessage(
                    type=MessageType.BETTING_STATE,
                    data=market_state,
                    timestamp=datetime.utcnow(),
                    sender_id=None  # System message
                )
                
                if user_id:
                    await self.ws_manager.send_to_user(user_id, message)
                
            finally:
                await session_gen.aclose()
                
        except Exception as e:
            logger.error(
                "Failed to send market state",
                market_id=market_id,
                user_id=user_id,
                error=str(e)
            )
    
    async def _handle_betting_messages(
        self,
        websocket: WebSocket,
        market_id: str,
        user_id: Optional[str],
        connection_id: str
    ):
        """Handle betting subscription messages"""
        try:
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(pong_message))
                elif message_type == "subscribe_outcome":
                    # Handle outcome-specific subscription
                    outcome_id = message_data.get("outcome_id")
                    if outcome_id:
                        # Add to outcome-specific room
                        outcome_room = f"betting_outcome_{outcome_id}"
                        await self.ws_manager.add_to_room(connection_id, outcome_room)
                        logger.debug(
                            "Added to outcome room",
                            market_id=market_id,
                            outcome_id=outcome_id,
                            user_id=user_id
                        )
                else:
                    logger.debug(
                        "Ignored betting message",
                        market_id=market_id,
                        user_id=user_id,
                        message_type=message_type
                    )
                
        except WebSocketDisconnect:
            logger.info(
                "Betting subscriber disconnected",
                market_id=market_id,
                user_id=user_id
            )
            await self.ws_manager.disconnect(connection_id)
        except Exception as e:
            logger.error(
                "Betting message handling error",
                market_id=market_id,
                user_id=user_id,
                error=str(e)
            )


# Handler instances
game_session_handler = GameSessionHandler()
spectator_handler = SpectatorHandler()
betting_handler = BettingHandler()