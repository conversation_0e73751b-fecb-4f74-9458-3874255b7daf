#!/usr/bin/env python3
"""
Claude-DB: Direct Betting Tables Deployment
==========================================

Deploy betting tables using the Gaming Engine's existing database connection.
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add the project path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

async def deploy_betting_tables_direct():
    """Deploy betting tables using the existing database connection"""
    try:
        print("🎲 Claude-DB: Direct Betting Tables Deployment")
        print("=" * 55)
        
        # Import the shared database connection
        from shared.core.database.connection import _db_manager
        
        db_manager = _db_manager
        
        if not db_manager:
            print("❌ Database manager not available")
            print("💡 Make sure Gaming Engine is running with database connection")
            return False
        
        print("✅ Database manager available")
        
        # Read the betting migration SQL
        migration_file = Path(__file__).parent / "app" / "database" / "migrations" / "001_add_betting_tables.sql"
        
        if not migration_file.exists():
            print(f"❌ Migration file not found: {migration_file}")
            return False
        
        migration_sql = migration_file.read_text()
        print(f"📄 Loaded migration: {migration_file.name}")
        print(f"📊 Migration size: {len(migration_sql)} characters")
        
        # Execute the migration using raw connection (like the existing API pattern)
        print("🚀 Executing betting tables migration...")
        
        async with db_manager.get_raw_connection() as conn:
            await conn.execute(migration_sql)
        
        print("✅ Migration executed successfully!")
        
        # Verify tables were created
        print("🔍 Verifying betting tables creation...")
        
        tables = await db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine' AND table_name LIKE 'betting_%' ORDER BY table_name",
            fetch_all=True
        )
        
        if tables:
            table_names = [dict(row)['table_name'] for row in tables]
            print(f"✅ Betting tables created: {len(table_names)} tables")
            for table in table_names:
                print(f"   📋 {table}")
            
            # Verify all expected tables exist
            expected_tables = ['betting_markets', 'betting_outcomes', 'user_bets', 'betting_market_odds_history']
            missing_tables = [t for t in expected_tables if t not in table_names]
            
            if missing_tables:
                print(f"⚠️  Missing expected tables: {missing_tables}")
                return False
            else:
                print("✅ All expected betting tables created successfully!")
            
            # Verify indexes were created
            print("🔍 Verifying betting indexes...")
            indexes = await db_manager.execute_query(
                "SELECT indexname FROM pg_indexes WHERE schemaname = 'gaming_engine' AND indexname LIKE '%betting%' ORDER BY indexname",
                fetch_all=True
            )
            
            if indexes:
                index_names = [dict(row)['indexname'] for row in indexes]
                print(f"✅ Betting indexes created: {len(index_names)} indexes")
            
            # Verify triggers were created
            print("🔍 Verifying betting triggers...")
            triggers = await db_manager.execute_query(
                "SELECT trigger_name FROM information_schema.triggers WHERE event_object_schema = 'gaming_engine' AND event_object_table LIKE 'betting_%' ORDER BY trigger_name",
                fetch_all=True
            )
            
            if triggers:
                trigger_names = [dict(row)['trigger_name'] for row in triggers]
                print(f"✅ Betting triggers created: {len(trigger_names)} triggers")
            
            return True
        else:
            print("❌ No betting tables found after migration!")
            return False
            
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        print("🔍 Error details:")
        traceback.print_exc()
        return False

async def test_betting_api_connectivity():
    """Test that betting API endpoints can now connect to database"""
    try:
        print("\n🧪 Testing Betting API Connectivity...")
        
        from shared.core.database.connection import get_db_manager
        
        db_manager = get_db_manager()
        
        # Test a simple betting markets query
        markets = await db_manager.execute_query(
            "SELECT COUNT(*) as count FROM gaming_engine.betting_markets",
            fetch_all=True
        )
        
        if markets:
            count = dict(markets[0])['count']
            print(f"✅ Betting markets table accessible: {count} markets")
        
        # Test betting outcomes query
        outcomes = await db_manager.execute_query(
            "SELECT COUNT(*) as count FROM gaming_engine.betting_outcomes",
            fetch_all=True
        )
        
        if outcomes:
            count = dict(outcomes[0])['count']
            print(f"✅ Betting outcomes table accessible: {count} outcomes")
        
        # Test user bets query
        bets = await db_manager.execute_query(
            "SELECT COUNT(*) as count FROM gaming_engine.user_bets",
            fetch_all=True
        )
        
        if bets:
            count = dict(bets[0])['count']
            print(f"✅ User bets table accessible: {count} bets")
        
        print("✅ All betting tables are accessible and ready for API use!")
        return True
        
    except Exception as e:
        print(f"❌ API connectivity test failed: {e}")
        return False

async def main():
    """Main deployment function"""
    try:
        # Deploy betting tables
        success = await deploy_betting_tables_direct()
        
        if success:
            # Test API connectivity
            await test_betting_api_connectivity()
            
            print("\n🎉 DEPLOYMENT SUCCESSFUL!")
            print("=" * 55)
            print("✅ Betting database tables deployed and verified")
            print("✅ Betting API endpoints are now functional")
            print("✅ WebSocket betting updates are ready")
            print("✅ Frontend betting integration can proceed")
            print()
            print("🔗 Next Steps:")
            print("1. Test betting API endpoints:")
            print("   curl http://localhost:8002/api/v1/gaming/betting-markets")
            print("2. Notify Claude-Frontend that database is ready")
            print("3. Begin end-to-end betting integration testing")
            print()
            print("📊 Handoff to Claude-Frontend ready!")
            
        else:
            print("\n💥 DEPLOYMENT FAILED!")
            print("❌ Betting tables could not be deployed")
            print("💡 Check database connection and permissions")
        
        return success
        
    except Exception as e:
        print(f"\n💥 DEPLOYMENT ERROR: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)