#!/usr/bin/env python3
"""
Railway PostgreSQL Database Setup Script
========================================

This script sets up the PostgreSQL database for Railway deployment,
including running migrations and creating initial data.
"""

import asyncio
import os
import sys
import json
from pathlib import Path
from typing import Dict, Any

import asyncpg
import structlog

# Configure logging
logger = structlog.get_logger()

class RailwayDatabaseSetup:
    """Railway PostgreSQL database setup"""
    
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable not set")
        
        # Parse Railway database URL format
        # postgresql://user:pass@host:port/dbname
        self.conn = None
    
    async def connect(self):
        """Connect to Railway PostgreSQL database"""
        try:
            logger.info("Connecting to Railway PostgreSQL database...")
            self.conn = await asyncpg.connect(self.database_url)
            logger.info("✅ Connected to Railway PostgreSQL database")
            return True
        except Exception as e:
            logger.error("❌ Failed to connect to database", error=str(e))
            return False
    
    async def disconnect(self):
        """Disconnect from database"""
        if self.conn:
            await self.conn.close()
            logger.info("Database connection closed")
    
    async def create_schemas(self):
        """Create required schemas"""
        try:
            logger.info("Creating database schemas...")
            
            # Create schemas
            schemas = [
                "gaming_engine",
                "public"  # Ensure public schema exists
            ]
            
            for schema in schemas:
                await self.conn.execute(f"CREATE SCHEMA IF NOT EXISTS {schema}")
                logger.info(f"✅ Schema created/verified: {schema}")
            
            # Enable UUID extension
            await self.conn.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
            logger.info("✅ UUID extension enabled")
            
            return True
            
        except Exception as e:
            logger.error("❌ Failed to create schemas", error=str(e))
            return False
    
    async def run_migration(self, migration_file: Path):
        """Run a single migration file"""
        try:
            migration_content = migration_file.read_text()
            migration_name = migration_file.name
            
            logger.info(f"🔄 Running migration: {migration_name}")
            
            async with self.conn.transaction():
                # Split migration into individual statements
                statements = [stmt.strip() for stmt in migration_content.split(';') if stmt.strip()]
                
                for i, statement in enumerate(statements):
                    if statement:
                        try:
                            await self.conn.execute(statement)
                        except Exception as e:
                            logger.error(f"❌ Failed statement {i+1} in {migration_name}", error=str(e))
                            raise
                
                logger.info(f"✅ Migration completed: {migration_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {migration_file.name}", error=str(e))
            return False
    
    async def run_all_migrations(self):
        """Run all available migrations"""
        try:
            migrations_dir = Path(__file__).parent / "app" / "database" / "migrations"
            migration_files = sorted(migrations_dir.glob("*.sql"))
            
            if not migration_files:
                logger.info("No migration files found")
                return True
            
            logger.info(f"Found {len(migration_files)} migration files")
            
            success_count = 0
            for migration_file in migration_files:
                if await self.run_migration(migration_file):
                    success_count += 1
                else:
                    logger.error(f"Migration failed, stopping: {migration_file.name}")
                    return False
            
            logger.info(f"✅ All migrations completed: {success_count}/{len(migration_files)}")
            return True
            
        except Exception as e:
            logger.error("❌ Migration process failed", error=str(e))
            return False
    
    async def create_initial_data(self):
        """Create initial data for the application"""
        try:
            logger.info("Creating initial data...")
            
            # Create sample games with cover images
            games_data = [
                {
                    'name': 'Reaction Time Master',
                    'slug': 'reaction-time-master',
                    'category': 'skill',
                    'description': 'Test your reflexes in this fast-paced reaction game',
                    'cover_image': 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=400',
                    'min_players': 2,
                    'max_players': 8,
                    'min_skill_level': 1,
                    'estimated_duration_minutes': 15,
                    'scoring_system': 'time_based',
                    'has_spectator_betting': True,
                    'is_featured': True
                },
                {
                    'name': 'Memory Palace',
                    'slug': 'memory-palace',
                    'category': 'puzzle',
                    'description': 'Challenge your memory with increasingly complex patterns',
                    'cover_image': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=400',
                    'min_players': 1,
                    'max_players': 6,
                    'min_skill_level': 2,
                    'estimated_duration_minutes': 20,
                    'scoring_system': 'points_based',
                    'has_spectator_betting': True,
                    'is_featured': True
                },
                {
                    'name': 'Word Warrior',
                    'slug': 'word-warrior',
                    'category': 'word',
                    'description': 'Battle with words in this competitive vocabulary game',
                    'cover_image': 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=400',
                    'min_players': 2,
                    'max_players': 4,
                    'min_skill_level': 1,
                    'estimated_duration_minutes': 12,
                    'scoring_system': 'points_based',
                    'has_spectator_betting': True,
                    'is_featured': False
                }
            ]
            
            for game_data in games_data:
                await self.conn.execute("""
                    INSERT INTO gaming_engine.games (
                        name, slug, category, description, cover_image,
                        min_players, max_players, min_skill_level,
                        estimated_duration_minutes, scoring_system,
                        has_spectator_betting, is_active, is_featured
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, true, $12)
                    ON CONFLICT (slug) DO UPDATE SET
                        cover_image = EXCLUDED.cover_image,
                        is_featured = EXCLUDED.is_featured,
                        updated_at = NOW()
                """, 
                    game_data['name'], game_data['slug'], game_data['category'],
                    game_data['description'], game_data['cover_image'],
                    game_data['min_players'], game_data['max_players'], 
                    game_data['min_skill_level'], game_data['estimated_duration_minutes'],
                    game_data['scoring_system'], game_data['has_spectator_betting'],
                    game_data['is_featured']
                )
            
            logger.info(f"✅ Created {len(games_data)} sample games")
            
            # Create sample betting markets (general markets not tied to specific sessions)
            betting_markets = [
                {
                    'name': 'Premier League Winner 2025',
                    'description': 'Who will win the Premier League this season?',
                    'market_type': 'tournament_winner',
                    'outcomes': [
                        {'name': 'Manchester City', 'odds': -200, 'implied_probability': 66.67},
                        {'name': 'Arsenal', 'odds': 300, 'implied_probability': 25.0},
                        {'name': 'Liverpool', 'odds': 400, 'implied_probability': 20.0},
                        {'name': 'Chelsea', 'odds': 800, 'implied_probability': 11.11}
                    ]
                },
                {
                    'name': 'NBA Finals 2025 Winner',
                    'description': 'Which team will win the NBA Finals?',
                    'market_type': 'tournament_winner', 
                    'outcomes': [
                        {'name': 'Boston Celtics', 'odds': 250, 'implied_probability': 28.57},
                        {'name': 'Lakers', 'odds': 300, 'implied_probability': 25.0},
                        {'name': 'Warriors', 'odds': 400, 'implied_probability': 20.0}
                    ]
                }
            ]
            
            for market_data in betting_markets:
                # Insert market
                market_id = await self.conn.fetchval("""
                    INSERT INTO gaming_engine.betting_markets (
                        name, description, market_type, status
                    ) VALUES ($1, $2, $3, 'active')
                    RETURNING id
                """, market_data['name'], market_data['description'], market_data['market_type'])
                
                # Insert outcomes
                for outcome_data in market_data['outcomes']:
                    await self.conn.execute("""
                        INSERT INTO gaming_engine.betting_outcomes (
                            market_id, name, odds, implied_probability, outcome_type, is_active
                        ) VALUES ($1, $2, $3, $4, 'team', true)
                    """, market_id, outcome_data['name'], outcome_data['odds'], outcome_data['implied_probability'])
            
            logger.info(f"✅ Created {len(betting_markets)} sample betting markets")
            return True
            
        except Exception as e:
            logger.error("❌ Failed to create initial data", error=str(e))
            return False
    
    async def verify_setup(self):
        """Verify the database setup is working"""
        try:
            logger.info("Verifying database setup...")
            
            # Check schemas
            schemas = await self.conn.fetch("SELECT schema_name FROM information_schema.schemata WHERE schema_name IN ('gaming_engine', 'public')")
            logger.info(f"✅ Schemas verified: {[s['schema_name'] for s in schemas]}")
            
            # Check tables
            tables = await self.conn.fetch("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'gaming_engine' 
                ORDER BY table_name
            """)
            table_names = [t['table_name'] for t in tables]
            logger.info(f"✅ Tables verified: {table_names}")
            
            # Check data
            games_count = await self.conn.fetchval("SELECT COUNT(*) FROM gaming_engine.games")
            markets_count = await self.conn.fetchval("SELECT COUNT(*) FROM gaming_engine.betting_markets")
            
            logger.info(f"✅ Data verified: {games_count} games, {markets_count} betting markets")
            
            return True
            
        except Exception as e:
            logger.error("❌ Database verification failed", error=str(e))
            return False

async def main():
    """Main setup function"""
    setup = RailwayDatabaseSetup()
    
    try:
        # Connect to database
        if not await setup.connect():
            logger.error("❌ Database connection failed")
            sys.exit(1)
        
        # Create schemas
        if not await setup.create_schemas():
            logger.error("❌ Schema creation failed")
            sys.exit(1)
        
        # Run migrations
        if not await setup.run_all_migrations():
            logger.error("❌ Migrations failed")
            sys.exit(1)
        
        # Create initial data
        if not await setup.create_initial_data():
            logger.error("❌ Initial data creation failed")
            sys.exit(1)
        
        # Verify setup
        if not await setup.verify_setup():
            logger.error("❌ Database verification failed")
            sys.exit(1)
        
        logger.info("🎉 Railway PostgreSQL database setup completed successfully!")
        
    except Exception as e:
        logger.error("❌ Database setup failed", error=str(e))
        sys.exit(1)
    
    finally:
        await setup.disconnect()

if __name__ == "__main__":
    asyncio.run(main())