#!/usr/bin/env python3
"""
Create test data for betting functionality
"""

import requests
import json


def create_test_data():
    """Create test data using admin endpoints"""
    print("📝 Creating Test Data for Betting")
    print("=" * 50)
    
    base_url = "http://localhost:8002"
    
    # First, add test data using the admin endpoint
    print("\n1️⃣ Adding test games and users...")
    response = requests.post(f"{base_url}/admin/add-test-data")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Test data created successfully!")
        print(f"   - Games created: {data.get('games_created', 0)}")
        if 'games' in data:
            for game in data['games']:
                print(f"     • {game['name']} ({game['category']}) - Score: {game['score']}")
    else:
        print(f"❌ Failed to create test data: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Check if we have any game sessions
    print("\n2️⃣ Checking for game sessions...")
    response = requests.get(f"{base_url}/api/v1/gaming/sessions")
    
    if response.status_code == 200:
        sessions = response.json()
        if isinstance(sessions, list) and len(sessions) > 0:
            print(f"✅ Found {len(sessions)} game sessions")
            for session in sessions[:3]:  # Show first 3
                print(f"   - Session: {session.get('id')} - {session.get('session_name', 'Unnamed')}")
        else:
            print("ℹ️  No game sessions found")
            print("   Creating a test session would require authentication")
    elif response.status_code == 401:
        print("ℹ️  Authentication required to list sessions")
    else:
        print(f"❌ Failed to get sessions: {response.status_code}")
    
    # Try to get games list
    print("\n3️⃣ Getting available games...")
    response = requests.get(f"{base_url}/api/v1/gaming/games")
    
    if response.status_code == 200:
        games = response.json()
        if isinstance(games, dict) and 'items' in games:
            games_list = games['items']
        else:
            games_list = games if isinstance(games, list) else []
            
        if games_list:
            print(f"✅ Found {len(games_list)} games")
            for game in games_list[:5]:  # Show first 5
                print(f"   - {game.get('name')} ({game.get('category')}) - ID: {game.get('id')}")
        else:
            print("ℹ️  No games found in response")
    else:
        print(f"❌ Failed to get games: {response.status_code}")
    
    print("\n✅ Test data creation complete!")
    print("\n📝 Next steps:")
    print("1. Use a valid user token to create game sessions")
    print("2. Create betting markets for those sessions")
    print("3. Place test bets on the markets")


if __name__ == "__main__":
    create_test_data()