# BetBet Gaming Engine - Production Container
# ==========================================
# Multi-stage build for optimized Python production deployment

# Stage 1: Dependencies and Build
FROM python:3.11-slim as builder

LABEL maintainer="BetBet Platform Team"
LABEL version="1.0.0"
LABEL description="BetBet Gaming Engine API - Production Build"

# Set working directory
WORKDIR /build

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --user -r requirements.txt

# Stage 2: Production Runtime
FROM python:3.11-slim as runtime

# Install security updates and runtime dependencies
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    dumb-init \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r betbet && useradd --no-log-init -r -g betbet betbet

# Set working directory
WORKDIR /app

# Copy Python dependencies from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY app/ ./app/
COPY --chown=betbet:betbet ../shared/core/ ./shared/core/

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PATH=/home/<USER>/.local/bin:$PATH

# Health check configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Performance optimizations
ENV PYTHONOPTIMIZE=2
ENV UVICORN_WORKERS=4

# Security: Switch to non-root user
USER betbet

# Expose port
EXPOSE 8000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start application with production settings
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--access-log", "--loop", "uvloop"]