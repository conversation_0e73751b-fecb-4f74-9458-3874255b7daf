"""
Simple FastAPI test to verify the service is working
"""
from fastapi import FastAPI

app = FastAPI(title="Gaming Engine Test")

@app.get("/")
def read_root():
    return {"message": "Gaming Engine Test is working!"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "gaming-engine-test"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)