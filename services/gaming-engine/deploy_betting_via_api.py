#!/usr/bin/env python3
"""
Deploy betting tables via Gaming Engine API
==========================================

Uses the same approach as the existing /admin/deploy-schema endpoint
to deploy betting tables to the database.
"""

import requests
import json
import time
from pathlib import Path

def deploy_betting_tables():
    """Deploy betting tables using API approach"""
    
    print("🎲 Claude-DB: Deploying Betting Tables via API...")
    
    try:
        # Check if Gaming Engine is running
        print("🔍 Checking Gaming Engine status...")
        response = requests.get("http://localhost:8002/health", timeout=5)
        
        if response.status_code != 200:
            print(f"❌ Gaming Engine not available: {response.status_code}")
            return False
        
        health_data = response.json()
        print(f"✅ Gaming Engine healthy: {health_data['service']} v{health_data['version']}")
        
        # Check database readiness
        print("🔍 Checking database readiness...")
        response = requests.get("http://localhost:8002/ready", timeout=5)
        
        if response.status_code != 200:
            print(f"❌ Database not ready: {response.status_code}")
            return False
            
        ready_data = response.json()
        db_status = ready_data.get('checks', {}).get('database', 'unknown')
        print(f"✅ Database status: {db_status}")
        
        if db_status != 'healthy':
            print(f"❌ Database not healthy: {db_status}")
            return False
        
        # Read betting migration SQL
        migration_file = Path("app/database/migrations/001_add_betting_tables.sql")
        if not migration_file.exists():
            print(f"❌ Migration file not found: {migration_file}")
            return False
        
        migration_sql = migration_file.read_text()
        print(f"📄 Loaded migration: {migration_file.name} ({len(migration_sql)} chars)")
        
        # Since we can't directly add a new endpoint without restarting the server,
        # we'll use a different approach: execute the SQL via a custom API call
        
        # Split the SQL into manageable chunks for execution
        statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
        
        # Filter out just the table creation statements first
        table_statements = []
        index_statements = []
        trigger_statements = []
        
        for stmt in statements:
            if 'CREATE TABLE' in stmt.upper():
                table_statements.append(stmt)
            elif 'CREATE INDEX' in stmt.upper():
                index_statements.append(stmt)
            elif 'CREATE TRIGGER' in stmt.upper() or 'CREATE OR REPLACE FUNCTION' in stmt.upper():
                trigger_statements.append(stmt)
        
        print(f"📊 SQL Analysis:")
        print(f"  - Tables: {len(table_statements)}")
        print(f"  - Indexes: {len(index_statements)}")
        print(f"  - Triggers/Functions: {len(trigger_statements)}")
        
        # Since we need to execute SQL but don't have a direct endpoint,
        # let's verify the tables don't already exist and then provide manual instructions
        
        print("🔍 Checking current database state...")
        
        # Try to query for existing betting tables
        response = requests.get("http://localhost:8002/admin/test-query", timeout=10)
        if response.status_code == 200:
            print("✅ Database query successful")
        
        # For now, we'll create the SQL file in a location where it can be executed manually
        output_file = Path("betting_tables_ready_for_deployment.sql")
        with open(output_file, 'w') as f:
            f.write(migration_sql)
        
        print(f"📄 SQL ready for deployment at: {output_file}")
        print()
        print("🚀 DEPLOYMENT OPTIONS:")
        print("1. Manual PostgreSQL execution:")
        print(f"   psql -d your_database -f {output_file}")
        print()
        print("2. Or execute the SQL manually in pgAdmin/psql:")
        print(f"   Copy the content from {output_file}")
        print()
        print("3. Or restart Gaming Engine with the new betting endpoint and use:")
        print("   curl -X POST http://localhost:8002/admin/deploy-betting-tables")
        
        return True
        
    except Exception as e:
        print(f"❌ Deployment preparation failed: {e}")
        return False

def verify_betting_tables():
    """Verify betting tables exist in the database"""
    try:
        print("🔍 Verifying betting tables...")
        
        # This would need a custom verification endpoint
        # For now, provide instructions for manual verification
        
        print("📋 VERIFICATION STEPS:")
        print("Execute this query to verify tables:")
        print("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'gaming_engine' 
        AND table_name LIKE 'betting_%' 
        ORDER BY table_name;
        """)
        
        print("Expected results:")
        print("  - betting_markets")
        print("  - betting_outcomes")
        print("  - user_bets")
        print("  - betting_market_odds_history")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification preparation failed: {e}")
        return False

if __name__ == "__main__":
    print("🗄️ Claude-DB: Betting Tables Deployment")
    print("=" * 50)
    
    success = deploy_betting_tables()
    
    if success:
        print()
        print("✅ Deployment preparation completed!")
        verify_betting_tables()
        
        print()
        print("🎯 NEXT STEPS:")
        print("1. Execute the SQL against your PostgreSQL database")
        print("2. Verify tables are created")
        print("3. Test betting API endpoints")
        print("4. Notify Claude-Frontend that database is ready")
    else:
        print()
        print("❌ Deployment preparation failed!")
        
    print()
    print("=" * 50)