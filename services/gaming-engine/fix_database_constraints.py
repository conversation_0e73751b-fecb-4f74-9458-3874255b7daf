"""
Database Constraint Fixes for BetBet Gaming Engine
==================================================

Fixes critical database constraints blocking betting API functionality.
"""

import asyncio
import asyncpg
import os
from datetime import datetime

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:123Bubblegums@localhost:5432/betbet_db")

async def check_current_constraints():
    """Check current database constraints"""
    print("🔍 Analyzing current database constraints...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check betting_markets constraints
        query = """
        SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as definition
        FROM pg_constraint 
        WHERE conrelid = 'gaming_engine.betting_markets'::regclass 
          AND contype = 'c';
        """
        
        results = await conn.fetch(query)
        
        print(f"\n📋 Current betting_markets check constraints:")
        for row in results:
            print(f"   Constraint: {row['constraint_name']}")
            print(f"   Definition: {row['definition']}")
        
        # Check if game_sessions has slug column
        slug_query = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'gaming_engine' 
          AND table_name = 'game_sessions' 
          AND column_name = 'slug';
        """
        
        slug_result = await conn.fetch(slug_query)
        
        print(f"\n📋 game_sessions slug column status:")
        if slug_result:
            print("   ✅ slug column exists")
        else:
            print("   ❌ slug column missing")
            
        await conn.close()
        return results, bool(slug_result)
        
    except Exception as e:
        print(f"❌ Error checking constraints: {e}")
        return [], False

async def fix_betting_markets_constraint():
    """Fix the betting_markets check constraint to allow general markets"""
    print("\n🔧 Fixing betting_markets check constraint...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # First, check if the constraint exists
        check_query = """
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'gaming_engine.betting_markets'::regclass 
          AND contype = 'c' 
          AND conname LIKE '%check%';
        """
        
        existing_constraints = await conn.fetch(check_query)
        
        # Drop existing check constraint if it exists
        for constraint in existing_constraints:
            constraint_name = constraint['conname']
            print(f"   Dropping existing constraint: {constraint_name}")
            
            drop_query = f"""
            ALTER TABLE gaming_engine.betting_markets 
            DROP CONSTRAINT IF EXISTS {constraint_name};
            """
            await conn.execute(drop_query)
            print(f"   ✅ Dropped constraint: {constraint_name}")
        
        # Add new constraint that allows general markets
        new_constraint_query = """
        ALTER TABLE gaming_engine.betting_markets 
        ADD CONSTRAINT betting_markets_session_or_tournament_check CHECK (
            (session_id IS NOT NULL AND tournament_id IS NULL) OR
            (session_id IS NULL AND tournament_id IS NOT NULL) OR
            (session_id IS NULL AND tournament_id IS NULL)
        );
        """
        
        await conn.execute(new_constraint_query)
        print("   ✅ Added new constraint allowing general betting markets")
        
        # Verify the new constraint
        verify_query = """
        SELECT pg_get_constraintdef(oid) as definition
        FROM pg_constraint 
        WHERE conrelid = 'gaming_engine.betting_markets'::regclass 
          AND conname = 'betting_markets_session_or_tournament_check';
        """
        
        result = await conn.fetchrow(verify_query)
        if result:
            print(f"   ✅ New constraint verified: {result['definition']}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing betting constraint: {e}")
        return False

async def add_slug_column():
    """Add missing slug column to game_sessions table"""
    print("\n🔧 Adding slug column to game_sessions table...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check if column already exists
        check_query = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'gaming_engine' 
          AND table_name = 'game_sessions' 
          AND column_name = 'slug';
        """
        
        existing = await conn.fetch(check_query)
        
        if existing:
            print("   ✅ slug column already exists")
            await conn.close()
            return True
        
        # Add slug column
        add_column_query = """
        ALTER TABLE gaming_engine.game_sessions 
        ADD COLUMN slug VARCHAR(300) UNIQUE;
        """
        
        await conn.execute(add_column_query)
        print("   ✅ Added slug column to game_sessions")
        
        # Create index for performance
        index_query = """
        CREATE INDEX IF NOT EXISTS idx_game_sessions_slug 
        ON gaming_engine.game_sessions(slug);
        """
        
        await conn.execute(index_query)
        print("   ✅ Added index on slug column")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding slug column: {e}")
        return False

async def test_betting_market_creation():
    """Test that betting market creation works after constraint fixes"""
    print("\n🧪 Testing betting market creation...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test creating a general betting market (no session_id or tournament_id)
        test_query = """
        INSERT INTO gaming_engine.betting_markets 
        (id, name, description, market_type, status, created_by, updated_by)
        VALUES 
        ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id;
        """
        
        import uuid
        test_id = uuid.uuid4()
        test_user_id = uuid.UUID("550e8400-e29b-41d4-a716-************")
        
        result = await conn.fetchrow(
            test_query,
            test_id,
            "Test General Market",
            "Test general betting market",
            "outcome",
            "active",
            test_user_id,
            test_user_id
        )
        
        if result:
            print(f"   ✅ Successfully created general betting market: {result['id']}")
            
            # Clean up test record
            cleanup_query = "DELETE FROM gaming_engine.betting_markets WHERE id = $1"
            await conn.execute(cleanup_query, test_id)
            print("   ✅ Cleaned up test record")
            
            await conn.close()
            return True
        
    except Exception as e:
        print(f"❌ Error testing betting market creation: {e}")
        return False

async def add_performance_indexes():
    """Add performance indexes for betting queries"""
    print("\n⚡ Adding performance indexes for betting queries...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        indexes = [
            # Betting markets indexes
            "CREATE INDEX IF NOT EXISTS idx_betting_markets_session_id ON gaming_engine.betting_markets(session_id) WHERE session_id IS NOT NULL;",
            "CREATE INDEX IF NOT EXISTS idx_betting_markets_tournament_id ON gaming_engine.betting_markets(tournament_id) WHERE tournament_id IS NOT NULL;",
            "CREATE INDEX IF NOT EXISTS idx_betting_markets_status ON gaming_engine.betting_markets(status);",
            "CREATE INDEX IF NOT EXISTS idx_betting_markets_created_at ON gaming_engine.betting_markets(created_at DESC);",
            
            # Betting outcomes indexes
            "CREATE INDEX IF NOT EXISTS idx_betting_outcomes_market_id ON gaming_engine.betting_outcomes(market_id);",
            "CREATE INDEX IF NOT EXISTS idx_betting_outcomes_active ON gaming_engine.betting_outcomes(is_active) WHERE is_active = true;",
            
            # User bets indexes  
            "CREATE INDEX IF NOT EXISTS idx_user_bets_user_id ON gaming_engine.user_bets(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_bets_market_id ON gaming_engine.user_bets(market_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_bets_status ON gaming_engine.user_bets(status);",
            "CREATE INDEX IF NOT EXISTS idx_user_bets_placed_at ON gaming_engine.user_bets(placed_at DESC);",
            "CREATE INDEX IF NOT EXISTS idx_user_bets_user_placed ON gaming_engine.user_bets(user_id, placed_at DESC);",
            
            # Odds history indexes
            "CREATE INDEX IF NOT EXISTS idx_odds_history_outcome_id ON gaming_engine.betting_market_odds_history(outcome_id);",
            "CREATE INDEX IF NOT EXISTS idx_odds_history_timestamp ON gaming_engine.betting_market_odds_history(timestamp DESC);"
        ]
        
        for i, index_query in enumerate(indexes, 1):
            try:
                await conn.execute(index_query)
                print(f"   ✅ Created index {i}/{len(indexes)}")
            except Exception as e:
                print(f"   ⚠️ Index {i} may already exist: {str(e)[:100]}")
        
        await conn.close()
        print(f"   ✅ Completed adding {len(indexes)} performance indexes")
        return True
        
    except Exception as e:
        print(f"❌ Error adding indexes: {e}")
        return False

async def main():
    """Main function to fix all database issues"""
    print("🚀 BetBet Gaming Engine - Database Constraint Fixes")
    print("=" * 55)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Database: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'localhost'}")
    
    # Step 1: Analyze current state
    constraints, has_slug = await check_current_constraints()
    
    # Step 2: Fix betting markets constraint
    if await fix_betting_markets_constraint():
        print("✅ Betting markets constraint fixed")
    else:
        print("❌ Failed to fix betting markets constraint")
        return
    
    # Step 3: Add slug column if missing
    if not has_slug:
        if await add_slug_column():
            print("✅ Slug column added")
        else:
            print("❌ Failed to add slug column")
            return
    
    # Step 4: Test betting market creation
    if await test_betting_market_creation():
        print("✅ Betting market creation test passed")
    else:
        print("❌ Betting market creation test failed")
        return
    
    # Step 5: Add performance indexes
    if await add_performance_indexes():
        print("✅ Performance indexes added")
    
    print("\n🎉 DATABASE CONSTRAINT FIXES COMPLETED SUCCESSFULLY!")
    print("\n📋 Next Steps:")
    print("   1. Restart the gaming-engine service")
    print("   2. Test betting market creation via API")
    print("   3. Verify session/tournament betting endpoints")
    print("   4. Run performance tests")

if __name__ == "__main__":
    asyncio.run(main())