#!/bin/bash
# Railway Database Deployment Script
# ==================================
# 
# This script handles PostgreSQL database setup for Railway deployment

set -e

echo "🚀 Starting Railway Database Deployment..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable not set"
    echo "Please set DATABASE_URL to your Railway PostgreSQL connection string"
    exit 1
fi

echo "✅ DATABASE_URL found"

# Install Python dependencies if needed
echo "📦 Installing Python dependencies..."
pip install asyncpg structlog

# Run database setup
echo "🔄 Running database setup and migrations..."
python railway-db-setup.py

if [ $? -eq 0 ]; then
    echo "✅ Database setup completed successfully!"
    echo ""
    echo "🎯 Database Status:"
    echo "  - Schemas: gaming_engine, public"
    echo "  - Tables: All betting and gaming tables created"
    echo "  - Initial Data: Sample games and betting markets loaded"
    echo "  - Ready for: Production traffic"
    echo ""
    echo "🔗 Your backend should now be able to connect at:"
    echo "  https://gaming-engine-production.up.railway.app"
else
    echo "❌ Database setup failed!"
    exit 1
fi