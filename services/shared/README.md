# BetBet Platform - Shared Services

## Overview

The Shared Services module contains common utilities, libraries, and components used across multiple services in the BetBet platform. This module promotes code reuse, consistency, and maintainability by centralizing common functionality.

## Components

### Core Modules
- **Authentication**: Unified authentication and authorization
- **Database**: Common database utilities and connection management
- **Caching**: Redis caching abstractions and utilities
- **Messaging**: WebSocket and event messaging utilities
- **Security**: Security utilities and encryption helpers
- **Monitoring**: Common monitoring and logging utilities
- **Financial**: Transaction and payment processing utilities

### Utilities
- **Error Handling**: Standardized error responses and exceptions
- **Validation**: Common validation schemas and utilities
- **Configuration**: Environment and configuration management
- **Testing**: Shared testing utilities and fixtures

## Architecture

```
shared/
├── core/
│   ├── auth/           # Authentication and authorization
│   ├── cache/          # Caching utilities
│   ├── database/       # Database connection and utilities
│   ├── financial/      # Financial transaction utilities
│   ├── messaging/      # WebSocket and messaging
│   ├── security/       # Security and encryption
│   └── utils/          # General utilities
├── exceptions/         # Custom exception classes
├── monitoring/         # Monitoring and metrics
└── requirements/       # Shared dependencies
    └── base.txt
```

## Authentication Module

### Unified Auth Service
```python
from shared.core.auth import UnifiedAuthService

auth_service = UnifiedAuthService()

# Validate JWT token
user = await auth_service.validate_token(token)

# Check permissions
if auth_service.has_permission(user, "bet.place"):
    # Allow betting operation
    pass
```

### Features
- **Multi-Provider Support**: Clerk, custom JWT, and OAuth
- **Role-Based Access Control**: Granular permission system
- **Token Management**: Token generation, validation, and refresh
- **Session Management**: User session tracking and management

## Database Module

### Connection Management
```python
from shared.core.database import DatabaseManager

db = DatabaseManager()

# Get async connection
async with db.get_connection() as conn:
    result = await conn.fetch("SELECT * FROM users")

# Transaction support
async with db.transaction() as tx:
    await tx.execute("INSERT INTO users (...)")
    await tx.execute("UPDATE profiles (...)")
```

### Features
- **Connection Pooling**: Optimized connection pool management
- **Transaction Support**: Async transaction handling
- **Migration Utilities**: Database migration helpers
- **Health Monitoring**: Connection health and performance monitoring

## Caching Module

### Redis Abstractions
```python
from shared.core.cache import CacheManager

cache = CacheManager()

# Simple caching
await cache.set("user:123", user_data, ttl=3600)
user_data = await cache.get("user:123")

# Distributed locking
async with cache.lock("market:update:123"):
    # Critical section
    pass
```

### Features
- **Distributed Caching**: Redis-based distributed caching
- **Lock Management**: Distributed locking for critical sections
- **Pub/Sub Support**: Redis pub/sub for event messaging
- **Serialization**: Automatic JSON serialization/deserialization

## Messaging Module

### WebSocket Management
```python
from shared.core.messaging import WebSocketManager

ws_manager = WebSocketManager()

# Broadcast to room
await ws_manager.broadcast_to_room("game:123", {
    "type": "game_update",
    "data": game_state
})

# Send to specific user
await ws_manager.send_to_user(user_id, message)
```

### Features
- **Room Management**: User grouping and room-based messaging
- **Event Broadcasting**: Efficient message broadcasting
- **Connection Tracking**: WebSocket connection lifecycle management
- **Message Queuing**: Reliable message delivery with queuing

## Security Module

### Encryption Utilities
```python
from shared.core.security import SecurityManager

security = SecurityManager()

# Encrypt sensitive data
encrypted = security.encrypt_data(sensitive_info)
decrypted = security.decrypt_data(encrypted)

# Password hashing
hashed = security.hash_password(password)
is_valid = security.verify_password(password, hashed)
```

### Features
- **Data Encryption**: AES encryption for sensitive data
- **Password Security**: Secure password hashing with bcrypt
- **Token Security**: Secure token generation and validation
- **Input Sanitization**: XSS and injection prevention

## Financial Module

### Transaction Processing
```python
from shared.core.financial import TransactionManager

tx_manager = TransactionManager()

# Process payment
result = await tx_manager.process_payment({
    "user_id": "123",
    "amount": 100.00,
    "type": "bet_placement",
    "reference": "bet:456"
})

# Handle escrow
escrow_id = await tx_manager.create_escrow(amount, participants)
```

### Features
- **Payment Processing**: Secure payment transaction handling
- **Escrow Management**: Multi-party escrow functionality
- **Transaction Logging**: Comprehensive transaction audit trail
- **Reconciliation**: Automated financial reconciliation

## Monitoring Module

### Metrics and Logging
```python
from shared.monitoring import MetricsCollector, Logger

metrics = MetricsCollector()
logger = Logger()

# Record metrics
metrics.increment("api.requests", tags={"service": "betting"})
metrics.histogram("response.time", duration)

# Structured logging
logger.info("User login", extra={
    "user_id": "123",
    "ip_address": "*******",
    "user_agent": "Mozilla/5.0..."
})
```

### Features
- **Metrics Collection**: Prometheus-compatible metrics
- **Structured Logging**: JSON-based structured logging
- **Performance Monitoring**: Request timing and performance tracking
- **Error Tracking**: Comprehensive error logging and alerting

## Exception Handling

### Custom Exceptions
```python
from shared.exceptions import (
    AuthenticationError,
    ValidationError,
    BusinessLogicError,
    ExternalServiceError
)

# Raise business logic exceptions
if insufficient_balance:
    raise BusinessLogicError("Insufficient balance for bet placement")

# Handle external service failures
try:
    result = await external_api_call()
except ExternalServiceError as e:
    logger.error("External service failed", extra={"error": str(e)})
```

### Features
- **Standardized Errors**: Consistent error types across services
- **Error Context**: Rich error context and metadata
- **HTTP Integration**: Automatic HTTP status code mapping
- **Logging Integration**: Automatic error logging and tracking

## Usage in Services

### Installing Shared Module

Add to service requirements.txt:
```txt
# Add to your service's requirements.txt
-e ../shared
```

### Service Integration
```python
# In your service's main.py
from shared.core.auth import UnifiedAuthService
from shared.core.database import DatabaseManager
from shared.core.cache import CacheManager
from shared.exceptions import ValidationError

app = FastAPI()

# Initialize shared components
auth_service = UnifiedAuthService()
db_manager = DatabaseManager()
cache_manager = CacheManager()

@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Use shared authentication
    try:
        user = await auth_service.validate_request(request)
        request.state.user = user
    except AuthenticationError:
        return JSONResponse({"error": "Unauthorized"}, status_code=401)
    
    return await call_next(request)
```

## Configuration

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=****************************************/database
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://:password@redis:6379
REDIS_MAX_CONNECTIONS=50

# Authentication
JWT_SECRET=your-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Security
ENCRYPTION_KEY=your-encryption-key
PASSWORD_MIN_LENGTH=8

# Monitoring
LOG_LEVEL=INFO
METRICS_ENABLED=true
```

## Development

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements/base.txt
   ```

2. **Run tests:**
   ```bash
   pytest tests/
   ```

3. **Type checking:**
   ```bash
   mypy shared/
   ```

## Testing

### Test Utilities
```python
from shared.testing import DatabaseTestCase, CacheTestCase

class TestUserService(DatabaseTestCase):
    async def test_user_creation(self):
        # Test with shared database utilities
        user = await self.create_test_user()
        assert user.id is not None

class TestCaching(CacheTestCase):
    async def test_cache_operations(self):
        # Test with shared cache utilities
        await self.cache.set("test:key", "value")
        result = await self.cache.get("test:key")
        assert result == "value"
```

### Fixtures and Utilities
- **Database Fixtures**: Pre-configured test database setup
- **Cache Fixtures**: Redis test instance management
- **Mock Services**: Mock external service dependencies
- **Test Data**: Common test data generators

## Performance Considerations

### Connection Pooling
- **Database Pools**: Optimized connection pool sizes
- **Redis Pools**: Connection pooling for Redis operations
- **HTTP Pools**: Connection pooling for external APIs

### Caching Strategy
- **L1 Cache**: In-memory caching for frequently accessed data
- **L2 Cache**: Redis caching for shared data
- **Cache Invalidation**: Smart cache invalidation strategies
- **TTL Management**: Appropriate cache expiration times

## Security Best Practices

### Data Protection
- **Encryption at Rest**: Sensitive data encrypted in database
- **Encryption in Transit**: TLS for all network communication
- **Key Management**: Secure key storage and rotation
- **Access Control**: Principle of least privilege

### Authentication Security
- **Token Security**: Secure token generation and validation
- **Session Management**: Secure session handling
- **Rate Limiting**: Prevent brute force attacks
- **Audit Logging**: Complete authentication audit trail

## Contributing

### Code Standards
1. **Type Hints**: All functions must include type hints
2. **Documentation**: Comprehensive docstrings for all modules
3. **Testing**: 90%+ code coverage required
4. **Error Handling**: Proper exception handling and logging
5. **Performance**: Consider performance impact of shared utilities

### Adding New Utilities

1. **Create module:**
   ```python
   # shared/core/new_utility.py
   class NewUtility:
       """Utility for new functionality."""
       
       def __init__(self, config: Dict[str, Any]):
           self.config = config
       
       async def process(self, data: Any) -> Any:
           """Process data with new utility."""
           pass
   ```

2. **Add tests:**
   ```python
   # tests/test_new_utility.py
   import pytest
   from shared.core.new_utility import NewUtility
   
   class TestNewUtility:
       def test_process(self):
           utility = NewUtility({})
           result = utility.process("test")
           assert result is not None
   ```

3. **Update documentation:**
   - Add to README.md
   - Include usage examples
   - Document configuration options

## License

MIT License - see LICENSE file for details.

---

**Part of the BetBet Platform** - Providing shared utilities and common functionality across all services.