"""
BetBet Platform - WebSocket Management
======================================

Provides enterprise-grade WebSocket management with:
- Connection pooling and lifecycle management
- Real-time event broadcasting
- Room-based communication
- Message ordering and delivery guarantees
- Connection recovery and reconnection logic
- Performance monitoring and metrics
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Set, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import weakref
import uuid

from fastapi import WebSocket, WebSocketDisconnect
import structlog

logger = structlog.get_logger()


class MessageType(Enum):
    """WebSocket message types"""
    SYSTEM = "system"
    USER_ACTION = "user_action"
    GAME_UPDATE = "game_update"
    BETTING_UPDATE = "betting_update"
    NOTIFICATION = "notification"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


@dataclass
class WebSocketMessage:
    """Standard WebSocket message format"""
    type: MessageType
    data: Dict[str, Any]
    timestamp: float = None
    message_id: str = None
    user_id: str = None
    room: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.message_id is None:
            self.message_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization"""
        return {
            "type": self.type.value,
            "data": self.data,
            "timestamp": self.timestamp,
            "message_id": self.message_id,
            "user_id": self.user_id,
            "room": self.room
        }


class WebSocketConnection:
    """Represents a WebSocket connection with metadata"""
    
    def __init__(self, websocket: WebSocket, user_id: str = None, connection_id: str = None):
        self.websocket = websocket
        self.user_id = user_id
        self.connection_id = connection_id or str(uuid.uuid4())
        self.connected_at = time.time()
        self.last_activity = time.time()
        self.rooms: Set[str] = set()
        self.is_alive = True
        self.message_count = 0
        self.bytes_sent = 0
        self.bytes_received = 0
    
    async def send_message(self, message: WebSocketMessage) -> bool:
        """
        Send a message through this connection
        
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            if not self.is_alive:
                return False
            
            message_json = json.dumps(message.to_dict())
            await self.websocket.send_text(message_json)
            
            self.message_count += 1
            self.bytes_sent += len(message_json)
            self.last_activity = time.time()
            
            return True
            
        except Exception as e:
            logger.warning(
                "Failed to send WebSocket message",
                connection_id=self.connection_id,
                user_id=self.user_id,
                error=str(e)
            )
            self.is_alive = False
            return False
    
    async def receive_message(self) -> Optional[WebSocketMessage]:
        """
        Receive a message from this connection
        
        Returns:
            WebSocketMessage object or None if connection closed
        """
        try:
            data = await self.websocket.receive_text()
            self.bytes_received += len(data)
            self.last_activity = time.time()
            
            message_data = json.loads(data)
            
            # Validate message format
            if "type" not in message_data or "data" not in message_data:
                raise ValueError("Invalid message format")
            
            return WebSocketMessage(
                type=MessageType(message_data["type"]),
                data=message_data["data"],
                timestamp=message_data.get("timestamp", time.time()),
                message_id=message_data.get("message_id"),
                user_id=self.user_id,
                room=message_data.get("room")
            )
            
        except WebSocketDisconnect:
            self.is_alive = False
            return None
        except Exception as e:
            logger.warning(
                "Failed to receive WebSocket message",
                connection_id=self.connection_id,
                user_id=self.user_id,
                error=str(e)
            )
            return None
    
    def join_room(self, room: str):
        """Join a room"""
        self.rooms.add(room)
    
    def leave_room(self, room: str):
        """Leave a room"""
        self.rooms.discard(room)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "connection_id": self.connection_id,
            "user_id": self.user_id,
            "connected_at": self.connected_at,
            "last_activity": self.last_activity,
            "uptime_seconds": time.time() - self.connected_at,
            "rooms": list(self.rooms),
            "message_count": self.message_count,
            "bytes_sent": self.bytes_sent,
            "bytes_received": self.bytes_received,
            "is_alive": self.is_alive
        }


class WebSocketManager:
    """
    Enterprise WebSocket connection manager with advanced features:
    - Connection lifecycle management
    - Room-based broadcasting
    - Message queuing and delivery guarantees
    - Performance monitoring
    - Automatic cleanup and reconnection handling
    """
    
    def __init__(self):
        # Connection storage
        self._connections: Dict[str, WebSocketConnection] = {}
        self._user_connections: Dict[str, Set[str]] = {}  # user_id -> connection_ids
        self._room_connections: Dict[str, Set[str]] = {}  # room -> connection_ids
        
        # Message handlers
        self._message_handlers: Dict[MessageType, List[Callable]] = {}
        
        # Performance metrics
        self._total_connections = 0
        self._total_messages = 0
        self._total_bytes = 0
        
        # Cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        # Configuration
        self.heartbeat_interval = 30  # seconds
        self.connection_timeout = 300  # 5 minutes
        self.max_connections_per_user = 5
        
        # Start background tasks
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background tasks for cleanup and heartbeat"""
        loop = asyncio.get_event_loop()
        self._cleanup_task = loop.create_task(self._cleanup_connections())
        self._heartbeat_task = loop.create_task(self._send_heartbeats())
    
    async def connect(self, websocket: WebSocket, user_id: str = None, room: str = None) -> str:
        """
        Register a new WebSocket connection

        Args:
            websocket: WebSocket instance
            user_id: Optional user identifier
            room: Optional room identifier for grouping connections

        Returns:
            Connection ID string
        """
        await websocket.accept()
        
        connection = WebSocketConnection(websocket, user_id)
        
        # Check connection limits per user
        if user_id and self._get_user_connection_count(user_id) >= self.max_connections_per_user:
            await websocket.close(code=1008, reason="Too many connections")
            logger.warning("Connection rejected: too many connections", user_id=user_id)
            return None
        
        # Store connection
        self._connections[connection.connection_id] = connection
        
        if user_id:
            if user_id not in self._user_connections:
                self._user_connections[user_id] = set()
            self._user_connections[user_id].add(connection.connection_id)

        # Add to room if specified
        if room:
            if room not in self._room_connections:
                self._room_connections[room] = set()
            self._room_connections[room].add(connection.connection_id)
            connection.rooms.add(room)

        self._total_connections += 1
        
        logger.info(
            "WebSocket connection established",
            connection_id=connection.connection_id,
            user_id=user_id,
            room=room,
            total_connections=len(self._connections)
        )
        
        # Send welcome message
        welcome_message = WebSocketMessage(
            type=MessageType.SYSTEM,
            data={
                "event": "connected",
                "connection_id": connection.connection_id,
                "timestamp": time.time()
            }
        )
        await connection.send_message(welcome_message)
        
        return connection.connection_id
    
    async def disconnect(self, connection_id: str):
        """
        Disconnect and cleanup a WebSocket connection
        
        Args:
            connection_id: Connection identifier
        """
        connection = self._connections.get(connection_id)
        if not connection:
            return
        
        # Remove from rooms
        for room in list(connection.rooms):
            await self.leave_room(connection_id, room)
        
        # Remove from user connections
        if connection.user_id:
            user_connections = self._user_connections.get(connection.user_id)
            if user_connections:
                user_connections.discard(connection_id)
                if not user_connections:
                    del self._user_connections[connection.user_id]
        
        # Remove connection
        del self._connections[connection_id]
        
        logger.info(
            "WebSocket connection disconnected",
            connection_id=connection_id,
            user_id=connection.user_id,
            uptime_seconds=time.time() - connection.connected_at,
            total_connections=len(self._connections)
        )
    
    async def send_to_connection(self, connection_id: str, message: WebSocketMessage) -> bool:
        """
        Send a message to a specific connection
        
        Args:
            connection_id: Target connection ID
            message: Message to send
            
        Returns:
            True if message was sent successfully
        """
        connection = self._connections.get(connection_id)
        if not connection:
            return False
        
        success = await connection.send_message(message)
        if success:
            self._total_messages += 1
        
        return success
    
    async def send_to_user(self, user_id: str, message: WebSocketMessage) -> int:
        """
        Send a message to all connections for a specific user
        
        Args:
            user_id: Target user ID
            message: Message to send
            
        Returns:
            Number of successful sends
        """
        user_connections = self._user_connections.get(user_id, set())
        success_count = 0
        
        for connection_id in list(user_connections):
            if await self.send_to_connection(connection_id, message):
                success_count += 1
        
        return success_count
    
    async def join_room(self, connection_id: str, room: str) -> bool:
        """
        Add a connection to a room
        
        Args:
            connection_id: Connection to add
            room: Room name
            
        Returns:
            True if successfully joined
        """
        connection = self._connections.get(connection_id)
        if not connection:
            return False
        
        connection.join_room(room)
        
        if room not in self._room_connections:
            self._room_connections[room] = set()
        self._room_connections[room].add(connection_id)
        
        logger.debug(
            "Connection joined room",
            connection_id=connection_id,
            room=room,
            room_size=len(self._room_connections[room])
        )
        
        return True
    
    async def leave_room(self, connection_id: str, room: str) -> bool:
        """
        Remove a connection from a room
        
        Args:
            connection_id: Connection to remove
            room: Room name
            
        Returns:
            True if successfully left
        """
        connection = self._connections.get(connection_id)
        if not connection:
            return False
        
        connection.leave_room(room)
        
        room_connections = self._room_connections.get(room)
        if room_connections:
            room_connections.discard(connection_id)
            if not room_connections:
                del self._room_connections[room]
        
        logger.debug("Connection left room", connection_id=connection_id, room=room)
        return True
    
    async def broadcast_to_room(self, room: str, message: WebSocketMessage) -> int:
        """
        Broadcast a message to all connections in a room
        
        Args:
            room: Room name
            message: Message to broadcast
            
        Returns:
            Number of successful sends
        """
        room_connections = self._room_connections.get(room, set())
        success_count = 0
        
        for connection_id in list(room_connections):
            if await self.send_to_connection(connection_id, message):
                success_count += 1
        
        if success_count > 0:
            logger.debug(
                "Message broadcasted to room",
                room=room,
                message_type=message.type.value,
                recipients=success_count
            )
        
        return success_count
    
    async def broadcast_to_all(self, message: WebSocketMessage) -> int:
        """
        Broadcast a message to all active connections
        
        Args:
            message: Message to broadcast
            
        Returns:
            Number of successful sends
        """
        success_count = 0
        
        for connection_id in list(self._connections.keys()):
            if await self.send_to_connection(connection_id, message):
                success_count += 1
        
        logger.info(
            "Message broadcasted to all connections",
            message_type=message.type.value,
            recipients=success_count
        )
        
        return success_count
    
    def add_message_handler(self, message_type: MessageType, handler: Callable):
        """
        Add a message handler for a specific message type
        
        Args:
            message_type: Type of message to handle
            handler: Async function to handle the message
        """
        if message_type not in self._message_handlers:
            self._message_handlers[message_type] = []
        self._message_handlers[message_type].append(handler)
    
    async def handle_message(self, connection_id: str, message: WebSocketMessage):
        """
        Process an incoming message through registered handlers
        
        Args:
            connection_id: Source connection ID
            message: Received message
        """
        handlers = self._message_handlers.get(message.type, [])
        
        for handler in handlers:
            try:
                await handler(connection_id, message)
            except Exception as e:
                logger.error(
                    "Message handler error",
                    handler=handler.__name__,
                    message_type=message.type.value,
                    error=str(e)
                )
    
    def get_connection_stats(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific connection"""
        connection = self._connections.get(connection_id)
        return connection.get_stats() if connection else None
    
    def get_room_stats(self, room: str) -> Dict[str, Any]:
        """Get statistics for a room"""
        room_connections = self._room_connections.get(room, set())
        return {
            "room": room,
            "connection_count": len(room_connections),
            "connections": list(room_connections)
        }
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Get global WebSocket manager statistics"""
        active_connections = len(self._connections)
        total_rooms = len(self._room_connections)
        total_users = len(self._user_connections)
        
        return {
            "active_connections": active_connections,
            "total_connections_ever": self._total_connections,
            "total_rooms": total_rooms,
            "total_users": total_users,
            "total_messages": self._total_messages,
            "total_bytes": self._total_bytes,
            "average_connections_per_user": active_connections / max(total_users, 1),
            "uptime_seconds": time.time() - getattr(self, '_start_time', time.time())
        }
    
    def _get_user_connection_count(self, user_id: str) -> int:
        """Get number of active connections for a user"""
        return len(self._user_connections.get(user_id, set()))
    
    async def _cleanup_connections(self):
        """Background task to cleanup dead connections"""
        while True:
            try:
                current_time = time.time()
                connections_to_remove = []
                
                for connection_id, connection in self._connections.items():
                    # Check if connection is dead or timed out
                    if (not connection.is_alive or 
                        current_time - connection.last_activity > self.connection_timeout):
                        connections_to_remove.append(connection_id)
                
                # Remove dead connections
                for connection_id in connections_to_remove:
                    await self.disconnect(connection_id)
                
                if connections_to_remove:
                    logger.info(
                        "Cleaned up dead connections",
                        removed_count=len(connections_to_remove)
                    )
                
                await asyncio.sleep(60)  # Run cleanup every minute
                
            except Exception as e:
                logger.error("Error in connection cleanup", error=str(e))
                await asyncio.sleep(60)
    
    async def _send_heartbeats(self):
        """Background task to send heartbeat messages"""
        while True:
            try:
                heartbeat_message = WebSocketMessage(
                    type=MessageType.HEARTBEAT,
                    data={"timestamp": time.time()}
                )
                
                await self.broadcast_to_all(heartbeat_message)
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error("Error sending heartbeats", error=str(e))
                await asyncio.sleep(self.heartbeat_interval)


# Global WebSocket manager instance
_websocket_manager: Optional[WebSocketManager] = None


def get_websocket_manager() -> WebSocketManager:
    """Get the global WebSocket manager instance"""
    global _websocket_manager
    if not _websocket_manager:
        _websocket_manager = WebSocketManager()
    return _websocket_manager