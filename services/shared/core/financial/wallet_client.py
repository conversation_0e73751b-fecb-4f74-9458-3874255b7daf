"""
BetBet Platform - Shared Wallet Client
=====================================

Hub-and-spoke wallet integration client for all platform services.
Provides unified interface for financial operations across the ecosystem.
"""

import asyncio
import aiohttp
import structlog
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from uuid import UUID

logger = structlog.get_logger()


class TransactionType(str, Enum):
    """Types of wallet transactions"""
    GAME_WAGER = "game_wager"
    GAME_PAYOUT = "game_payout"
    SPECTATOR_BET = "spectator_bet"
    SPECTATOR_PAYOUT = "spectator_payout"
    TRADE_MARGIN = "trade_margin"
    TRADE_SETTLEMENT = "trade_settlement"
    SUBSCRIPTION_PAYMENT = "subscription_payment"
    SUBSCRIPTION_REFUND = "subscription_refund"
    TOURNAMENT_ENTRY = "tournament_entry"
    TOURNAMENT_PRIZE = "tournament_prize"


class FeeCategory(str, Enum):
    """Platform fee categories"""
    GAMING = "gaming"
    TRADING = "trading"
    SUBSCRIPTIONS = "subscriptions"
    WITHDRAWALS = "withdrawals"


class FeeType(str, Enum):
    """Fee types"""
    STANDARD = "standard"
    PREMIUM = "premium"
    PROMOTIONAL = "promotional"
    HIGH_VOLUME = "high_volume"
    MAKER = "maker"
    TAKER = "taker"
    EXPERT = "expert"
    ENTERPRISE = "enterprise"


@dataclass
class WalletBalance:
    """User wallet balance information"""
    user_id: str
    available_balance: Decimal
    locked_balance: Decimal
    total_balance: Decimal
    total_deposited: Decimal
    total_withdrawn: Decimal
    lifetime_winnings: Decimal
    lifetime_losses: Decimal
    currency: str = "USD"


@dataclass
class TransactionResult:
    """Result of a wallet transaction"""
    success: bool
    transaction_id: Optional[str] = None
    new_balance: Optional[Decimal] = None
    fee_amount: Optional[Decimal] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class FeeCalculation:
    """Platform fee calculation result"""
    base_percentage: Decimal
    applied_percentage: Decimal
    fee_amount: Decimal
    promotional_discount: Optional[Decimal] = None
    campaign_applied: Optional[str] = None
    volume_discount: Optional[Decimal] = None


class WalletClientError(Exception):
    """Base exception for wallet client errors"""
    pass


class InsufficientFundsError(WalletClientError):
    """Raised when user has insufficient funds"""
    pass


class WalletServiceUnavailableError(WalletClientError):
    """Raised when wallet service is unavailable"""
    pass


class WalletClient:
    """
    Unified wallet client for all BetBet platform services.
    
    Provides hub-and-spoke architecture for financial operations:
    - Balance checking and management
    - Fund locking and releasing (escrow)
    - Fee calculation and collection
    - Transaction history and analytics
    - Event-driven settlement
    """
    
    def __init__(
        self,
        wallet_service_url: str = "http://wallet-service:8007",
        service_name: str = "unknown",
        timeout: int = 30,
        max_retries: int = 3
    ):
        self.wallet_service_url = wallet_service_url.rstrip('/')
        self.service_name = service_name
        self.timeout = timeout
        self.max_retries = max_retries
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
        return self.session
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to wallet service with retry logic"""
        
        url = f"{self.wallet_service_url}{endpoint}"
        session = await self._get_session()
        
        for attempt in range(self.max_retries):
            try:
                logger.info(
                    "Wallet API request",
                    service=self.service_name,
                    method=method,
                    endpoint=endpoint,
                    attempt=attempt + 1
                )
                
                async with session.request(
                    method=method,
                    url=url,
                    json=data,
                    params=params
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        logger.info(
                            "Wallet API success",
                            service=self.service_name,
                            endpoint=endpoint,
                            status=response.status
                        )
                        return result
                    
                    elif response.status == 400:
                        error_data = await response.json()
                        error_msg = error_data.get('detail', 'Bad request')
                        
                        if 'insufficient' in error_msg.lower():
                            raise InsufficientFundsError(error_msg)
                        else:
                            raise WalletClientError(f"Bad request: {error_msg}")
                    
                    elif response.status in [500, 502, 503, 504]:
                        # Server errors - retry
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # Exponential backoff
                            continue
                        else:
                            raise WalletServiceUnavailableError(
                                f"Wallet service unavailable: {response.status}"
                            )
                    
                    else:
                        error_text = await response.text()
                        raise WalletClientError(
                            f"Unexpected response {response.status}: {error_text}"
                        )
                        
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                if attempt < self.max_retries - 1:
                    logger.warning(
                        "Wallet API request failed, retrying",
                        service=self.service_name,
                        endpoint=endpoint,
                        error=str(e),
                        attempt=attempt + 1
                    )
                    await asyncio.sleep(2 ** attempt)
                    continue
                else:
                    logger.error(
                        "Wallet API request failed after retries",
                        service=self.service_name,
                        endpoint=endpoint,
                        error=str(e)
                    )
                    raise WalletServiceUnavailableError(f"Connection failed: {str(e)}")
    
    # =============================================
    # BALANCE OPERATIONS
    # =============================================
    
    async def get_balance(self, user_id: str) -> WalletBalance:
        """Get user's current wallet balance"""
        
        result = await self._make_request(
            method="GET",
            endpoint=f"/api/v1/wallet/balance/{user_id}"
        )
        
        return WalletBalance(
            user_id=result["user_id"],
            available_balance=Decimal(str(result["available_balance"])),
            locked_balance=Decimal(str(result["locked_balance"])),
            total_balance=Decimal(str(result["total_balance"])),
            total_deposited=Decimal(str(result["total_deposited"])),
            total_withdrawn=Decimal(str(result["total_withdrawn"])),
            lifetime_winnings=Decimal(str(result["lifetime_winnings"])),
            lifetime_losses=Decimal(str(result["lifetime_losses"])),
            currency=result["currency"]
        )
    
    async def check_sufficient_balance(
        self,
        user_id: str,
        required_amount: Decimal
    ) -> bool:
        """Check if user has sufficient available balance"""
        
        balance = await self.get_balance(user_id)
        return balance.available_balance >= required_amount
    
    # =============================================
    # ESCROW OPERATIONS (Fund Locking/Unlocking)
    # =============================================
    
    async def lock_funds(
        self,
        user_id: str,
        amount: Decimal,
        reference_type: str,
        reference_id: str,
        description: Optional[str] = None
    ) -> TransactionResult:
        """
        Lock funds in escrow for a pending transaction.
        Used for game wagers, bets, trading margins, etc.
        """
        
        data = {
            "user_id": user_id,
            "amount": float(amount),
            "reference_type": reference_type,
            "reference_id": reference_id,
            "description": description or f"{self.service_name} escrow hold"
        }
        
        try:
            result = await self._make_request(
                method="POST",
                endpoint="/api/v1/wallet/lock-funds",
                data=data
            )
            
            return TransactionResult(
                success=result.get("success", False),
                new_balance=Decimal(str(result["available_balance"])),
                metadata={
                    "locked_amount": amount,
                    "locked_balance": Decimal(str(result["locked_balance"]))
                }
            )
            
        except InsufficientFundsError:
            return TransactionResult(
                success=False,
                error_message=f"Insufficient funds: required {amount}"
            )
    
    async def unlock_funds(
        self,
        user_id: str,
        locked_amount: Decimal,
        win_amount: Decimal,
        reference_type: str,
        reference_id: str,
        description: Optional[str] = None
    ) -> TransactionResult:
        """
        Unlock funds from escrow and distribute winnings.
        Used for settling games, bets, trades, etc.
        """
        
        data = {
            "user_id": user_id,
            "amount": float(locked_amount),
            "win_amount": float(win_amount),
            "reference_type": reference_type,
            "reference_id": reference_id,
            "description": description or f"{self.service_name} settlement"
        }
        
        result = await self._make_request(
            method="POST",
            endpoint="/api/v1/wallet/unlock-funds",
            data=data
        )
        
        return TransactionResult(
            success=result.get("success", False),
            new_balance=Decimal(str(result["available_balance"])),
            metadata={
                "unlocked_amount": locked_amount,
                "win_amount": win_amount,
                "locked_balance": Decimal(str(result["locked_balance"]))
            }
        )
    
    # =============================================
    # FEE OPERATIONS
    # =============================================
    
    async def calculate_platform_fee(
        self,
        user_id: str,
        transaction_amount: Decimal,
        fee_category: FeeCategory,
        fee_type: FeeType,
        user_tier: Optional[str] = None,
        user_volume_30d: Optional[Decimal] = None
    ) -> FeeCalculation:
        """Calculate platform fee for a transaction"""
        
        data = {
            "user_id": user_id,
            "category": fee_category.value,
            "fee_type": fee_type.value,
            "transaction_amount": float(transaction_amount),
            "user_tier": user_tier,
            "user_volume_30d": float(user_volume_30d) if user_volume_30d else None
        }
        
        result = await self._make_request(
            method="POST",
            endpoint="/api/v1/platform-fees/calculate",
            data=data
        )
        
        return FeeCalculation(
            base_percentage=Decimal(str(result["base_percentage"])),
            applied_percentage=Decimal(str(result["applied_percentage"])),
            fee_amount=Decimal(str(result["fee_amount"])),
            promotional_discount=Decimal(str(result["promotional_discount"])) if result.get("promotional_discount") else None,
            campaign_applied=result.get("campaign_applied"),
            volume_discount=Decimal(str(result["volume_discount"])) if result.get("volume_discount") else None
        )
    
    # =============================================
    # TRANSFER OPERATIONS
    # =============================================
    
    async def transfer_funds(
        self,
        from_user_id: str,
        to_user_id: str,
        amount: Decimal,
        description: Optional[str] = None
    ) -> TransactionResult:
        """Transfer funds between users"""
        
        data = {
            "from_user_id": from_user_id,
            "to_user_id": to_user_id,
            "amount": float(amount),
            "description": description or f"{self.service_name} transfer"
        }
        
        result = await self._make_request(
            method="POST",
            endpoint="/api/v1/wallet/transfer",
            data=data
        )
        
        return TransactionResult(
            success=result.get("success", False),
            transaction_id=result.get("from_transaction_id"),
            metadata={
                "from_transaction_id": result.get("from_transaction_id"),
                "to_transaction_id": result.get("to_transaction_id"),
                "amount": amount
            }
        )
    
    # =============================================
    # TRANSACTION HISTORY
    # =============================================
    
    async def get_transaction_history(
        self,
        user_id: str,
        page: int = 1,
        limit: int = 50,
        transaction_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get user's transaction history"""
        
        params = {
            "page": page,
            "limit": limit
        }
        if transaction_type:
            params["transaction_type"] = transaction_type
            
        return await self._make_request(
            method="GET",
            endpoint=f"/api/v1/wallet/transactions/{user_id}",
            params=params
        )
    
    # =============================================
    # SERVICE-SPECIFIC CONVENIENCE METHODS
    # =============================================
    
    async def create_game_wager(
        self,
        user_id: str,
        wager_amount: Decimal,
        game_id: str,
        game_type: str = "chess"
    ) -> TransactionResult:
        """Lock funds for a game wager"""
        
        return await self.lock_funds(
            user_id=user_id,
            amount=wager_amount,
            reference_type=f"game_wager_{game_type}",
            reference_id=game_id,
            description=f"{game_type.title()} game wager"
        )
    
    async def settle_game_wager(
        self,
        user_id: str,
        original_wager: Decimal,
        payout_amount: Decimal,
        game_id: str,
        game_type: str = "chess"
    ) -> TransactionResult:
        """Settle a game wager with winnings"""
        
        return await self.unlock_funds(
            user_id=user_id,
            locked_amount=original_wager,
            win_amount=payout_amount,
            reference_type=f"game_settlement_{game_type}",
            reference_id=game_id,
            description=f"{game_type.title()} game settlement"
        )
    
    async def create_spectator_bet(
        self,
        user_id: str,
        bet_amount: Decimal,
        bet_id: str,
        market_type: str
    ) -> TransactionResult:
        """Lock funds for a spectator bet"""
        
        return await self.lock_funds(
            user_id=user_id,
            amount=bet_amount,
            reference_type=f"spectator_bet_{market_type}",
            reference_id=bet_id,
            description=f"Spectator bet on {market_type}"
        )
    
    async def settle_spectator_bet(
        self,
        user_id: str,
        original_stake: Decimal,
        payout_amount: Decimal,
        bet_id: str,
        market_type: str
    ) -> TransactionResult:
        """Settle a spectator bet"""
        
        return await self.unlock_funds(
            user_id=user_id,
            locked_amount=original_stake,
            win_amount=payout_amount,
            reference_type=f"spectator_settlement_{market_type}",
            reference_id=bet_id,
            description=f"Spectator bet settlement"
        )
    
    async def create_trading_margin(
        self,
        user_id: str,
        margin_amount: Decimal,
        order_id: str,
        market_id: str
    ) -> TransactionResult:
        """Lock funds for trading margin"""
        
        return await self.lock_funds(
            user_id=user_id,
            amount=margin_amount,
            reference_type="trading_margin",
            reference_id=order_id,
            description=f"Trading margin for market {market_id}"
        )
    
    async def settle_trade(
        self,
        user_id: str,
        margin_amount: Decimal,
        settlement_amount: Decimal,
        order_id: str
    ) -> TransactionResult:
        """Settle a completed trade"""
        
        return await self.unlock_funds(
            user_id=user_id,
            locked_amount=margin_amount,
            win_amount=settlement_amount,
            reference_type="trade_settlement",
            reference_id=order_id,
            description="Trade settlement"
        )
    
    async def process_subscription_payment(
        self,
        user_id: str,
        subscription_amount: Decimal,
        subscription_id: str,
        expert_id: str,
        subscription_type: str = "monthly"
    ) -> TransactionResult:
        """Process subscription payment with fee collection"""
        
        # Calculate platform fee
        fee_calc = await self.calculate_platform_fee(
            user_id=user_id,
            transaction_amount=subscription_amount,
            fee_category=FeeCategory.SUBSCRIPTIONS,
            fee_type=FeeType.EXPERT
        )
        
        total_amount = subscription_amount + fee_calc.fee_amount
        
        # Check sufficient balance
        if not await self.check_sufficient_balance(user_id, total_amount):
            return TransactionResult(
                success=False,
                error_message=f"Insufficient funds: required {total_amount}"
            )
        
        # Lock total amount
        lock_result = await self.lock_funds(
            user_id=user_id,
            amount=total_amount,
            reference_type="subscription_payment",
            reference_id=subscription_id,
            description=f"{subscription_type.title()} subscription payment"
        )
        
        if not lock_result.success:
            return lock_result
        
        # TODO: Transfer to expert and collect platform fee
        # This would involve multiple wallet operations
        
        return TransactionResult(
            success=True,
            fee_amount=fee_calc.fee_amount,
            metadata={
                "subscription_amount": subscription_amount,
                "platform_fee": fee_calc.fee_amount,
                "total_charged": total_amount,
                "expert_id": expert_id
            }
        )
    
    # =============================================
    # HEALTH AND MONITORING
    # =============================================
    
    async def health_check(self) -> Dict[str, Any]:
        """Check wallet service health"""
        
        try:
            result = await self._make_request(
                method="GET",
                endpoint="/health"
            )
            return {
                "wallet_service": "healthy",
                "details": result
            }
        except Exception as e:
            return {
                "wallet_service": "unhealthy",
                "error": str(e)
            }


# =============================================
# GLOBAL WALLET CLIENT INSTANCES
# =============================================

def create_wallet_client(service_name: str) -> WalletClient:
    """Create a wallet client instance for a service"""
    return WalletClient(service_name=service_name)


# Service-specific instances
gaming_wallet = WalletClient(service_name="gaming-engine")
betting_wallet = WalletClient(service_name="custom-betting")
trading_wallet = WalletClient(service_name="odds-exchange")
expert_wallet = WalletClient(service_name="expert-analysts")
sports_wallet = WalletClient(service_name="sports-analysis")
leaderboards_wallet = WalletClient(service_name="leaderboards")