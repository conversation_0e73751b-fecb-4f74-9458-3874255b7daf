"""
BetBet Platform - Shared Core Library
=====================================

This module provides shared functionality across all BetBet microservices including:
- Authentication and authorization
- Database connections and models
- Caching and session management
- Real-time messaging and WebSocket management
- Financial transaction handling
- Security utilities
- Common utilities and helpers

All services should import core functionality from this shared library to ensure
consistency and maintainability across the platform.
"""

__version__ = "1.0.0"
__author__ = "BetBet Development Team"

# Core imports for easy access
try:
    from .auth import AuthManager, get_auth_manager
    from .auth.models import TokenData
except ImportError:
    pass

# TODO: Implement remaining modules
# from .database import get_database, DatabaseManager
# from .cache import CacheManager, get_cache
# from .messaging import WebSocketManager, EventBus
# from .financial import TransactionManager, EscrowService
# from .security import SecurityManager, encrypt_data, decrypt_data
# from .utils import generate_id, validate_email, format_currency

__all__ = [
    "AuthManager",
    "get_auth_manager",
    "TokenData"
]