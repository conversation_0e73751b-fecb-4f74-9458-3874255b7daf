"""
BetBet Platform - Database Connection Management
================================================

Provides centralized database connection management with connection pooling,
read/write splitting, and performance optimization for the BetBet platform.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import text
import structlog
from pydantic_settings import BaseSettings

logger = structlog.get_logger()


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""

    # Primary database (read/write)
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"

    # Read replica (read-only)
    DATABASE_READ_URL: Optional[str] = None

    # Connection pool settings
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600

    # Query timeout settings
    DB_QUERY_TIMEOUT: int = 30
    DB_SLOW_QUERY_THRESHOLD: float = 0.1  # 100ms

    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"  # Allow extra fields in .env


class DatabaseManager:
    """
    Centralized database connection manager with advanced features:
    - Connection pooling with read/write splitting
    - Automatic retries and failover
    - Query performance monitoring
    - Transaction management
    """

    def __init__(self, settings: DatabaseSettings):
        self.settings = settings
        self._write_engine = None
        self._read_engine = None
        self._write_session_factory = None
        self._read_session_factory = None
        self._connection_pool = None

    async def initialize(self):
        """Initialize database connections and connection pools"""
        try:
            # Create write engine (primary database)
            self._write_engine = create_async_engine(
                self.settings.DATABASE_URL,
                poolclass=NullPool if "test" in self.settings.DATABASE_URL else None,
                pool_size=self.settings.DB_POOL_SIZE,
                max_overflow=self.settings.DB_MAX_OVERFLOW,
                pool_timeout=self.settings.DB_POOL_TIMEOUT,
                pool_recycle=self.settings.DB_POOL_RECYCLE,
                echo=False,  # Set to True for SQL debugging
                echo_pool=False,
                future=True,
            )

            # Create read engine (read replica or same as write)
            read_url = self.settings.DATABASE_READ_URL or self.settings.DATABASE_URL
            self._read_engine = create_async_engine(
                read_url,
                poolclass=NullPool if "test" in read_url else None,
                pool_size=self.settings.DB_POOL_SIZE // 2,  # Smaller pool for reads
                max_overflow=self.settings.DB_MAX_OVERFLOW // 2,
                pool_timeout=self.settings.DB_POOL_TIMEOUT,
                pool_recycle=self.settings.DB_POOL_RECYCLE,
                echo=False,
                future=True,
            )

            # Create session factories
            self._write_session_factory = async_sessionmaker(
                bind=self._write_engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )

            self._read_session_factory = async_sessionmaker(
                bind=self._read_engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False,
            )

            # Initialize direct connection pool for high-performance operations
            self._connection_pool = await asyncpg.create_pool(
                self.settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://"),
                min_size=5,
                max_size=self.settings.DB_POOL_SIZE,
                command_timeout=self.settings.DB_QUERY_TIMEOUT,
            )

            logger.info("Database connections initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize database connections", error=str(e))
            raise

    async def shutdown(self):
        """Cleanup database connections"""
        try:
            if self._write_engine:
                await self._write_engine.dispose()
            if self._read_engine:
                await self._read_engine.dispose()
            if self._connection_pool:
                await self._connection_pool.close()
            logger.info("Database connections closed successfully")
        except Exception as e:
            logger.error("Error closing database connections", error=str(e))

    @asynccontextmanager
    async def get_write_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a write session with automatic cleanup and error handling"""
        if not self._write_session_factory:
            raise RuntimeError("Database not initialized. Call initialize() first.")

        session = self._write_session_factory()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error("Database write session error", error=str(e))
            raise
        finally:
            await session.close()

    @asynccontextmanager
    async def get_read_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a read-only session with automatic cleanup"""
        if not self._read_session_factory:
            raise RuntimeError("Database not initialized. Call initialize() first.")

        session = self._read_session_factory()
        try:
            yield session
        except Exception as e:
            logger.error("Database read session error", error=str(e))
            raise
        finally:
            await session.close()

    @asynccontextmanager
    async def get_raw_connection(self):
        """Get a raw asyncpg connection for high-performance operations"""
        if not self._connection_pool:
            raise RuntimeError("Connection pool not initialized. Call initialize() first.")

        async with self._connection_pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                logger.error("Raw connection error", error=str(e))
                raise

    async def execute_query(self, query: str, *args, fetch_one: bool = False, fetch_all: bool = False):
        """
        Execute a raw SQL query with performance monitoring
        
        Args:
            query: SQL query string
            *args: Query parameters
            fetch_one: Return single row
            fetch_all: Return all rows
        """
        import time
        start_time = time.time()

        try:
            async with self.get_raw_connection() as conn:
                if fetch_one:
                    result = await conn.fetchrow(query, *args)
                elif fetch_all:
                    result = await conn.fetch(query, *args)
                else:
                    result = await conn.execute(query, *args)

                execution_time = time.time() - start_time

                # Log slow queries
                if execution_time > self.settings.DB_SLOW_QUERY_THRESHOLD:
                    logger.warning(
                        "Slow query detected",
                        query=query[:100],
                        execution_time=execution_time,
                        args=args
                    )

                return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                "Query execution failed",
                query=query[:100],
                execution_time=execution_time,
                error=str(e)
            )
            raise

    async def health_check(self) -> dict:
        """Perform database health check"""
        try:
            # Test write connection
            async with self.get_write_session() as session:
                result = await session.execute(text("SELECT 1 as health_check"))
                write_healthy = result.scalar() == 1

            # Test read connection
            async with self.get_read_session() as session:
                result = await session.execute(text("SELECT 1 as health_check"))
                read_healthy = result.scalar() == 1

            # Test connection pool
            async with self.get_raw_connection() as conn:
                await conn.fetchval("SELECT 1")
                pool_healthy = True

            return {
                "write_connection": write_healthy,
                "read_connection": read_healthy,
                "connection_pool": pool_healthy,
                "pool_size": self._connection_pool.get_size() if self._connection_pool else 0,
                "pool_available": self._connection_pool.get_idle_size() if self._connection_pool else 0,
            }

        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return {
                "write_connection": False,
                "read_connection": False,
                "connection_pool": False,
                "error": str(e)
            }


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


async def initialize_database(settings: DatabaseSettings = None):
    """Initialize the global database manager"""
    global _db_manager
    if settings is None:
        settings = DatabaseSettings()

    _db_manager = DatabaseManager(settings)
    await _db_manager.initialize()


async def get_database_write() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for write database sessions"""
    if not _db_manager:
        raise RuntimeError("Database not initialized")

    async with _db_manager.get_write_session() as session:
        yield session


async def get_database_read() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for read database sessions"""
    if not _db_manager:
        raise RuntimeError("Database not initialized")

    async with _db_manager.get_read_session() as session:
        yield session


# Convenience alias for write sessions (most common use case)
get_database = get_database_write


async def close_database():
    """Close all database connections"""
    global _db_manager
    if _db_manager:
        await _db_manager.shutdown()
        _db_manager = None
