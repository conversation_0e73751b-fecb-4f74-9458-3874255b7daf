"""
BetBet Unified Authentication Service
=====================================

Centralized authentication service that provides unified JWT validation,
token management, and cross-service permission handling for all BetBet modules.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Any
import jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from pydantic import BaseModel
import redis.asyncio as redis
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserRole(str, Enum):
    """User roles for permission management"""
    ADMIN = "admin"
    EXPERT = "expert"
    PREMIUM = "premium"
    USER = "user"
    GUEST = "guest"

class ModulePermission(str, Enum):
    """Module-specific permissions"""
    GAMING_PLAY = "gaming:play"
    GAMING_SPECTATE = "gaming:spectate"
    BETTING_CREATE = "betting:create"
    BETTING_PARTICIPATE = "betting:participate"
    EXPERT_PUBLISH = "expert:publish"
    EXPERT_SUBSCRIBE = "expert:subscribe"
    SPORTS_ANALYSIS = "sports:analysis"
    SPORTS_CHAT = "sports:chat"
    TRADING_ORDER = "trading:order"
    TRADING_VIEW = "trading:view"
    LEADERBOARD_VIEW = "leaderboard:view"
    LEADERBOARD_COMPETE = "leaderboard:compete"

# Pydantic models
class TokenPayload(BaseModel):
    """JWT token payload"""
    user_id: str
    email: str
    role: UserRole
    permissions: List[ModulePermission]
    exp: datetime
    iat: datetime
    iss: str = "betbet-platform"

class LoginCredentials(BaseModel):
    """Login request model"""
    email: str
    password: str

class AuthResponse(BaseModel):
    """Authentication response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]

class RefreshTokenRequest(BaseModel):
    """Refresh token request"""
    refresh_token: str

class UserCreate(BaseModel):
    """User creation model"""
    email: str
    password: str
    role: UserRole = UserRole.USER

class UnifiedAuthService:
    """Centralized authentication service"""
    
    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 60,
        refresh_token_expire_days: int = 7,
        redis_url: str = "redis://redis:6379"
    ):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self.redis: Optional[redis.Redis] = None
        self.redis_url = redis_url
        
        # Role-based permissions
        self.role_permissions = {
            UserRole.ADMIN: [
                ModulePermission.GAMING_PLAY,
                ModulePermission.GAMING_SPECTATE,
                ModulePermission.BETTING_CREATE,
                ModulePermission.BETTING_PARTICIPATE,
                ModulePermission.EXPERT_PUBLISH,
                ModulePermission.EXPERT_SUBSCRIBE,
                ModulePermission.SPORTS_ANALYSIS,
                ModulePermission.SPORTS_CHAT,
                ModulePermission.TRADING_ORDER,
                ModulePermission.TRADING_VIEW,
                ModulePermission.LEADERBOARD_VIEW,
                ModulePermission.LEADERBOARD_COMPETE,
            ],
            UserRole.EXPERT: [
                ModulePermission.GAMING_PLAY,
                ModulePermission.GAMING_SPECTATE,
                ModulePermission.BETTING_CREATE,
                ModulePermission.BETTING_PARTICIPATE,
                ModulePermission.EXPERT_PUBLISH,
                ModulePermission.EXPERT_SUBSCRIBE,
                ModulePermission.SPORTS_ANALYSIS,
                ModulePermission.SPORTS_CHAT,
                ModulePermission.TRADING_VIEW,
                ModulePermission.LEADERBOARD_VIEW,
                ModulePermission.LEADERBOARD_COMPETE,
            ],
            UserRole.PREMIUM: [
                ModulePermission.GAMING_PLAY,
                ModulePermission.GAMING_SPECTATE,
                ModulePermission.BETTING_PARTICIPATE,
                ModulePermission.EXPERT_SUBSCRIBE,
                ModulePermission.SPORTS_ANALYSIS,
                ModulePermission.SPORTS_CHAT,
                ModulePermission.TRADING_ORDER,
                ModulePermission.TRADING_VIEW,
                ModulePermission.LEADERBOARD_VIEW,
                ModulePermission.LEADERBOARD_COMPETE,
            ],
            UserRole.USER: [
                ModulePermission.GAMING_PLAY,
                ModulePermission.GAMING_SPECTATE,
                ModulePermission.BETTING_PARTICIPATE,
                ModulePermission.SPORTS_CHAT,
                ModulePermission.TRADING_VIEW,
                ModulePermission.LEADERBOARD_VIEW,
                ModulePermission.LEADERBOARD_COMPETE,
            ],
            UserRole.GUEST: [
                ModulePermission.GAMING_SPECTATE,
                ModulePermission.TRADING_VIEW,
                ModulePermission.LEADERBOARD_VIEW,
            ]
        }
        
    async def init_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis = redis.from_url(self.redis_url, decode_responses=True)
            await self.redis.ping()
            logger.info("Redis connection established for auth service")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            
    def get_user_permissions(self, role: UserRole) -> List[ModulePermission]:
        """Get permissions for user role"""
        return self.role_permissions.get(role, [])
        
    def create_access_token(self, user_data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        permissions = self.get_user_permissions(UserRole(user_data["role"]))
        
        payload = {
            "user_id": str(user_data["id"]),
            "email": user_data["email"],
            "role": user_data["role"],
            "permissions": [p.value for p in permissions],
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes),
            "iss": "betbet-platform",
            "type": "access"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
    def create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token"""
        payload = {
            "user_id": user_id,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(days=self.refresh_token_expire_days),
            "iss": "betbet-platform",
            "type": "refresh"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Verify token type
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
                
            # Check if token is expired
            if datetime.fromtimestamp(payload["exp"]) < datetime.utcnow():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token expired"
                )
                
            return payload
            
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
            
    async def store_refresh_token(self, user_id: str, refresh_token: str):
        """Store refresh token in Redis with expiration"""
        if self.redis:
            key = f"refresh_token:{user_id}"
            await self.redis.setex(
                key, 
                timedelta(days=self.refresh_token_expire_days), 
                refresh_token
            )
            
    async def validate_refresh_token(self, user_id: str, refresh_token: str) -> bool:
        """Validate refresh token against Redis store"""
        if not self.redis:
            return False
            
        key = f"refresh_token:{user_id}"
        stored_token = await self.redis.get(key)
        return stored_token == refresh_token
        
    async def revoke_refresh_token(self, user_id: str):
        """Revoke refresh token by removing from Redis"""
        if self.redis:
            key = f"refresh_token:{user_id}"
            await self.redis.delete(key)
            
    async def blacklist_token(self, jti: str, exp: datetime):
        """Add token to blacklist until expiration"""
        if self.redis:
            key = f"blacklist:{jti}"
            ttl = int((exp - datetime.utcnow()).total_seconds())
            if ttl > 0:
                await self.redis.setex(key, ttl, "true")
                
    async def is_token_blacklisted(self, jti: str) -> bool:
        """Check if token is blacklisted"""
        if self.redis:
            key = f"blacklist:{jti}"
            return bool(await self.redis.get(key))
        return False
        
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
        
    def get_password_hash(self, password: str) -> str:
        """Hash password"""
        return pwd_context.hash(password)
        
    def check_permission(self, user_permissions: List[str], required_permission: ModulePermission) -> bool:
        """Check if user has required permission"""
        return required_permission.value in user_permissions
        
    async def authenticate_user(self, credentials: LoginCredentials) -> Dict[str, Any]:
        """Authenticate user and return user data"""
        # This would typically query your user database
        # For now, we'll use a placeholder implementation
        # In production, integrate with your actual user store
        
        # Example user lookup (replace with actual database query)
        user = await self._lookup_user(credentials.email)
        
        if not user or not self.verify_password(credentials.password, user["password_hash"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
            
        return user
        
    async def _lookup_user(self, email: str) -> Optional[Dict[str, Any]]:
        """Lookup user by email (placeholder - implement with your database)"""
        # This is a placeholder - replace with actual database lookup
        demo_users = {
            "<EMAIL>": {
                "id": "1",
                "email": "<EMAIL>",
                "password_hash": self.get_password_hash("admin123"),
                "role": "admin",
                "is_active": True
            },
            "<EMAIL>": {
                "id": "2",
                "email": "<EMAIL>",
                "password_hash": self.get_password_hash("user123"),
                "role": "user",
                "is_active": True
            }
        }
        
        return demo_users.get(email)
        
    async def login(self, credentials: LoginCredentials) -> AuthResponse:
        """Complete login process"""
        user = await self.authenticate_user(credentials)
        
        # Create tokens
        access_token = self.create_access_token(user)
        refresh_token = self.create_refresh_token(str(user["id"]))
        
        # Store refresh token
        await self.store_refresh_token(str(user["id"]), refresh_token)
        
        # Remove sensitive data from user object
        safe_user = {k: v for k, v in user.items() if k != "password_hash"}
        
        return AuthResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=self.access_token_expire_minutes * 60,
            user=safe_user
        )
        
    async def refresh_access_token(self, refresh_request: RefreshTokenRequest) -> AuthResponse:
        """Refresh access token using refresh token"""
        # Verify refresh token
        payload = self.verify_token(refresh_request.refresh_token, "refresh")
        user_id = payload["user_id"]
        
        # Validate refresh token in Redis
        if not await self.validate_refresh_token(user_id, refresh_request.refresh_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
            
        # Get user data
        user = await self._lookup_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
            
        # Create new access token
        access_token = self.create_access_token(user)
        
        safe_user = {k: v for k, v in user.items() if k != "password_hash"}
        
        return AuthResponse(
            access_token=access_token,
            refresh_token=refresh_request.refresh_token,  # Keep same refresh token
            expires_in=self.access_token_expire_minutes * 60,
            user=safe_user
        )
        
    async def _lookup_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Lookup user by ID (placeholder - implement with your database)"""
        # Placeholder implementation
        if user_id == "1":
            return {
                "id": "1",
                "email": "<EMAIL>",
                "role": "admin",
                "is_active": True
            }
        elif user_id == "2":
            return {
                "id": "2",
                "email": "<EMAIL>",
                "role": "user",
                "is_active": True
            }
        return None
        
    async def logout(self, access_token: str, refresh_token: str):
        """Logout user by blacklisting tokens"""
        # Verify and blacklist access token
        access_payload = self.verify_token(access_token)
        if "jti" in access_payload:
            await self.blacklist_token(
                access_payload["jti"], 
                datetime.fromtimestamp(access_payload["exp"])
            )
            
        # Revoke refresh token
        await self.revoke_refresh_token(access_payload["user_id"])
        
    def create_service_token(self, service_name: str) -> str:
        """Create service-to-service authentication token"""
        payload = {
            "service": service_name,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(hours=1),  # Shorter expiration for service tokens
            "iss": "betbet-platform",
            "type": "service"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
    def verify_service_token(self, token: str) -> Dict[str, Any]:
        """Verify service-to-service token"""
        return self.verify_token(token, "service")

# Global auth service instance (to be initialized in each service)
auth_service = None

def get_auth_service() -> UnifiedAuthService:
    """Get the global auth service instance"""
    global auth_service
    if not auth_service:
        raise RuntimeError("Auth service not initialized")
    return auth_service

def init_auth_service(secret_key: str, **kwargs) -> UnifiedAuthService:
    """Initialize the global auth service"""
    global auth_service
    auth_service = UnifiedAuthService(secret_key, **kwargs)
    return auth_service