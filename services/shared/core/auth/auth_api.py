"""
BetBet Authentication API Endpoints
===================================

FastAPI router for authentication endpoints that can be included in any service
or deployed as a standalone authentication service.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
import logging

from .unified_auth_service import (
    get_auth_service, 
    LoginCredentials, 
    AuthResponse, 
    RefreshTokenRequest,
    UserCreate
)
from .dependencies import get_current_user, CurrentUser, security

logger = logging.getLogger(__name__)

# Create router
auth_router = APIRouter(prefix="/auth", tags=["authentication"])

@auth_router.post("/login", response_model=AuthResponse)
async def login(credentials: LoginCredentials):
    """
    Authenticate user and return access/refresh tokens
    """
    try:
        auth_service = get_auth_service()
        return await auth_service.login(credentials)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@auth_router.post("/refresh", response_model=AuthResponse)
async def refresh_token(refresh_request: RefreshTokenRequest):
    """
    Refresh access token using refresh token
    """
    try:
        auth_service = get_auth_service()
        return await auth_service.refresh_access_token(refresh_request)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@auth_router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Logout user by blacklisting current token
    """
    try:
        auth_service = get_auth_service()
        
        # For simplicity, we'll just revoke the refresh token
        # In a full implementation, you'd also blacklist the access token
        await auth_service.revoke_refresh_token(current_user.user_id)
        
        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )

@auth_router.get("/me")
async def get_current_user_info(current_user: CurrentUser = Depends(get_current_user)):
    """
    Get current user information
    """
    return {
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role.value,
        "permissions": current_user.permissions,
        "is_service": current_user.is_service,
        "service_name": current_user.service_name
    }

@auth_router.get("/verify")
async def verify_token(current_user: CurrentUser = Depends(get_current_user)):
    """
    Verify if current token is valid
    """
    return {
        "valid": True,
        "user_id": current_user.user_id,
        "role": current_user.role.value
    }

@auth_router.post("/register", response_model=AuthResponse)
async def register_user(user_data: UserCreate):
    """
    Register new user (placeholder - implement with your user store)
    """
    try:
        # This is a placeholder implementation
        # In production, you'd create the user in your database
        auth_service = get_auth_service()
        
        # Check if user already exists (placeholder)
        existing_user = await auth_service._lookup_user(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create user (placeholder - implement with your database)
        new_user = {
            "id": "new_user_id",  # Generate proper ID
            "email": user_data.email,
            "password_hash": auth_service.get_password_hash(user_data.password),
            "role": user_data.role.value,
            "is_active": True
        }
        
        # Return login response
        return await auth_service.login(LoginCredentials(
            email=user_data.email,
            password=user_data.password
        ))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

# Health check for auth service
@auth_router.get("/health")
async def auth_health_check():
    """
    Health check for authentication service
    """
    try:
        auth_service = get_auth_service()
        # Check Redis connection if available
        if auth_service.redis:
            await auth_service.redis.ping()
        
        return {"status": "healthy", "service": "authentication"}
    except Exception as e:
        logger.error(f"Auth health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}