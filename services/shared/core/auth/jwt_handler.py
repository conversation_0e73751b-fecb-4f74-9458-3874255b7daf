"""
BetBet Platform - JWT Authentication Handler
===========================================

Provides comprehensive JWT token management with security best practices:
- Access and refresh token handling
- Role-based access control
- Token blacklisting and revocation
- Multi-factor authentication support
"""

from jose import jwt
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from passlib.context import CryptContext
from pydantic_settings import BaseSettings
import structlog
import secrets
import hashlib
import redis
import asyncio
from contextlib import asynccontextmanager

logger = structlog.get_logger()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token security scheme
security = HTTPBearer()


class AuthSettings(BaseSettings):
    """Authentication configuration settings"""
    
    # JWT Settings - REQUIRED for security
    JWT_SECRET_KEY: str  # No default - must be provided in environment
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Redis Settings for distributed token blacklist
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_TOKEN_BLACKLIST_KEY_PREFIX: str = "betbet:blacklist:"
    
    # Password settings
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SYMBOLS: bool = True
    
    # Security settings
    MAX_LOGIN_ATTEMPTS: int = 5
    LOCKOUT_DURATION_MINUTES: int = 30
    
    # Multi-factor authentication
    MFA_ENABLED: bool = True
    MFA_ISSUER: str = "BetBet Platform"
    
    class Config:
        env_file = ".env"
        extra = "ignore"  # Allow extra fields in .env


class TokenData:
    """Represents decoded JWT token data"""
    
    def __init__(self, user_id: str, username: str, email: str, roles: List[str], 
                 permissions: List[str], is_active: bool = True, mfa_verified: bool = False):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.roles = roles or []
        self.permissions = permissions or []
        self.is_active = is_active
        self.mfa_verified = mfa_verified


class AuthManager:
    """
    Comprehensive authentication manager with enterprise security features
    """
    
    def __init__(self, settings: AuthSettings = None):
        self.settings = settings or AuthSettings()
        
        # Validate critical settings
        if not self.settings.JWT_SECRET_KEY:
            logger.error("FATAL ERROR: JWT_SECRET_KEY is not defined.")
            raise ValueError("JWT_SECRET_KEY environment variable is required")
        
        # Initialize Redis connection for distributed token blacklist
        try:
            self.redis_client = redis.from_url(
                self.settings.REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # Test Redis connection
            self.redis_client.ping()
            logger.info("Redis connection established for token blacklist")
        except Exception as e:
            logger.error("Failed to connect to Redis for token blacklist", error=str(e))
            # Fallback to in-memory for development only
            self._blacklisted_tokens = set()
            self.redis_client = None
            logger.warning("Using in-memory token blacklist - NOT for production!")
        
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """
        Validate password strength according to security policy
        
        Returns:
            Dict with validation results and specific requirements
        """
        errors = []
        
        if len(password) < self.settings.PASSWORD_MIN_LENGTH:
            errors.append(f"Password must be at least {self.settings.PASSWORD_MIN_LENGTH} characters long")
        
        if self.settings.PASSWORD_REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if self.settings.PASSWORD_REQUIRE_LOWERCASE and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if self.settings.PASSWORD_REQUIRE_NUMBERS and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if self.settings.PASSWORD_REQUIRE_SYMBOLS and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "strength_score": self._calculate_password_strength(password)
        }
    
    def _calculate_password_strength(self, password: str) -> int:
        """Calculate password strength score (0-100)"""
        score = 0
        
        # Length bonus
        score += min(25, len(password) * 2)
        
        # Character variety bonus
        if any(c.isupper() for c in password):
            score += 15
        if any(c.islower() for c in password):
            score += 15
        if any(c.isdigit() for c in password):
            score += 15
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 20
        
        # Complexity bonus
        unique_chars = len(set(password))
        score += min(10, unique_chars)
        
        return min(100, score)
    
    def create_access_token(self, user_data: Dict[str, Any]) -> str:
        """
        Create a JWT access token
        
        Args:
            user_data: Dictionary containing user information
            
        Returns:
            Encoded JWT token string
        """
        expire = datetime.utcnow() + timedelta(minutes=self.settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        payload = {
            "user_id": str(user_data["user_id"]),
            "username": user_data["username"],
            "email": user_data["email"],
            "roles": user_data.get("roles", []),
            "permissions": user_data.get("permissions", []),
            "is_active": user_data.get("is_active", True),
            "mfa_verified": user_data.get("mfa_verified", False),
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        token = jwt.encode(payload, self.settings.JWT_SECRET_KEY, algorithm=self.settings.JWT_ALGORITHM)
        
        logger.info("Access token created", user_id=user_data["user_id"], expires_at=expire)
        return token
    
    def create_refresh_token(self, user_id: str) -> str:
        """
        Create a JWT refresh token
        
        Args:
            user_id: User identifier
            
        Returns:
            Encoded JWT refresh token string
        """
        expire = datetime.utcnow() + timedelta(days=self.settings.REFRESH_TOKEN_EXPIRE_DAYS)
        
        payload = {
            "user_id": str(user_id),
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh",
            "jti": secrets.token_urlsafe(32)  # Unique token ID for revocation
        }
        
        token = jwt.encode(payload, self.settings.JWT_SECRET_KEY, algorithm=self.settings.JWT_ALGORITHM)
        
        logger.info("Refresh token created", user_id=user_id, expires_at=expire)
        return token
    
    def verify_token(self, token: str) -> TokenData:
        """
        Verify and decode a JWT token
        
        Args:
            token: JWT token string
            
        Returns:
            TokenData object with user information
            
        Raises:
            HTTPException: If token is invalid, expired, or blacklisted
        """
        try:
            # Check if token is blacklisted
            if self._is_token_blacklisted(token):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked"
                )
            
            # Decode token
            payload = jwt.decode(
                token, 
                self.settings.JWT_SECRET_KEY, 
                algorithms=[self.settings.JWT_ALGORITHM]
            )
            
            # Validate token type
            if payload.get("type") != "access":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            # Extract user data
            user_id = payload.get("user_id")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing user_id"
                )
            
            return TokenData(
                user_id=user_id,
                username=payload.get("username", ""),
                email=payload.get("email", ""),
                roles=payload.get("roles", []),
                permissions=payload.get("permissions", []),
                is_active=payload.get("is_active", True),
                mfa_verified=payload.get("mfa_verified", False)
            )
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError as e:
            logger.warning("JWT verification failed", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def verify_refresh_token(self, token: str) -> str:
        """
        Verify a refresh token and return user_id
        
        Args:
            token: JWT refresh token string
            
        Returns:
            User ID string
            
        Raises:
            HTTPException: If token is invalid, expired, or blacklisted
        """
        try:
            # Check if token is blacklisted
            if self._is_token_blacklisted(token):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Refresh token has been revoked"
                )
            
            # Decode token
            payload = jwt.decode(
                token, 
                self.settings.JWT_SECRET_KEY, 
                algorithms=[self.settings.JWT_ALGORITHM]
            )
            
            # Validate token type
            if payload.get("type") != "refresh":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            user_id = payload.get("user_id")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token: missing user_id"
                )
            
            return user_id
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token has expired"
            )
        except jwt.JWTError as e:
            logger.warning("Refresh token verification failed", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
    
    def blacklist_token(self, token: str):
        """Add a token to the distributed blacklist"""
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        if self.redis_client:
            try:
                # Store in Redis with expiration based on token expiry
                # Extract expiration from token to set appropriate TTL
                try:
                    payload = jwt.decode(
                        token, 
                        self.settings.JWT_SECRET_KEY, 
                        algorithms=[self.settings.JWT_ALGORITHM],
                        options={"verify_exp": False}  # Don't verify expiration for blacklisting
                    )
                    exp_timestamp = payload.get("exp")
                    if exp_timestamp:
                        # Calculate TTL until token expires
                        exp_datetime = datetime.fromtimestamp(exp_timestamp)
                        ttl_seconds = max(0, int((exp_datetime - datetime.utcnow()).total_seconds()))
                        
                        # Store in Redis with TTL
                        redis_key = f"{self.settings.REDIS_TOKEN_BLACKLIST_KEY_PREFIX}{token_hash}"
                        self.redis_client.setex(redis_key, ttl_seconds, "blacklisted")
                        logger.info("Token blacklisted in Redis", token_hash=token_hash[:16], ttl_seconds=ttl_seconds)
                    else:
                        # Fallback: store for maximum possible duration
                        redis_key = f"{self.settings.REDIS_TOKEN_BLACKLIST_KEY_PREFIX}{token_hash}"
                        max_ttl = self.settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 3600  # Convert to seconds
                        self.redis_client.setex(redis_key, max_ttl, "blacklisted")
                        logger.info("Token blacklisted in Redis with max TTL", token_hash=token_hash[:16])
                        
                except jwt.JWTError:
                    # If we can't decode the token, store it with max TTL
                    redis_key = f"{self.settings.REDIS_TOKEN_BLACKLIST_KEY_PREFIX}{token_hash}"
                    max_ttl = self.settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 3600
                    self.redis_client.setex(redis_key, max_ttl, "blacklisted")
                    logger.info("Token blacklisted in Redis (decode failed)", token_hash=token_hash[:16])
                    
            except Exception as e:
                logger.error("Failed to blacklist token in Redis", error=str(e), token_hash=token_hash[:16])
                # Fallback to in-memory if Redis fails
                if hasattr(self, '_blacklisted_tokens'):
                    self._blacklisted_tokens.add(token_hash)
                    logger.warning("Token blacklisted in memory (Redis failed)", token_hash=token_hash[:16])
        else:
            # Fallback to in-memory storage
            if hasattr(self, '_blacklisted_tokens'):
                self._blacklisted_tokens.add(token_hash)
                logger.warning("Token blacklisted in memory (no Redis)", token_hash=token_hash[:16])
    
    def _is_token_blacklisted(self, token: str) -> bool:
        """Check if a token is blacklisted in distributed storage"""
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        if self.redis_client:
            try:
                redis_key = f"{self.settings.REDIS_TOKEN_BLACKLIST_KEY_PREFIX}{token_hash}"
                return self.redis_client.exists(redis_key) > 0
            except Exception as e:
                logger.error("Failed to check Redis blacklist", error=str(e), token_hash=token_hash[:16])
                # Fallback to in-memory check if Redis fails
                if hasattr(self, '_blacklisted_tokens'):
                    return token_hash in self._blacklisted_tokens
                return False
        else:
            # Fallback to in-memory storage
            if hasattr(self, '_blacklisted_tokens'):
                return token_hash in self._blacklisted_tokens
            return False
    
    def generate_mfa_secret(self) -> str:
        """Generate a new MFA secret for TOTP"""
        return secrets.token_urlsafe(32)
    
    def verify_mfa_token(self, secret: str, token: str) -> bool:
        """Verify a TOTP MFA token"""
        import pyotp
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)


# Global auth manager instance
_auth_manager: Optional[AuthManager] = None


def get_auth_manager() -> AuthManager:
    """Get the global auth manager instance"""
    global _auth_manager
    if not _auth_manager:
        _auth_manager = AuthManager()
    return _auth_manager


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """
    FastAPI dependency to get the current authenticated user
    
    Args:
        credentials: HTTP Authorization credentials
        
    Returns:
        TokenData object with current user information
    """
    auth_manager = get_auth_manager()
    return auth_manager.verify_token(credentials.credentials)


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions for an endpoint
    
    Args:
        *required_permissions: Permission strings required
        
    Returns:
        FastAPI dependency function
    """
    async def check_permissions(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive"
            )
        
        # Check if user has all required permissions
        user_permissions = set(current_user.permissions)
        missing_permissions = set(required_permissions) - user_permissions
        
        if missing_permissions:
            logger.warning(
                "Access denied: insufficient permissions",
                user_id=current_user.user_id,
                required=list(required_permissions),
                user_permissions=list(user_permissions),
                missing=list(missing_permissions)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Missing: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return check_permissions


def require_roles(*required_roles: str):
    """
    Decorator to require specific roles for an endpoint
    
    Args:
        *required_roles: Role strings required
        
    Returns:
        FastAPI dependency function
    """
    async def check_roles(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive"
            )
        
        # Check if user has any of the required roles
        user_roles = set(current_user.roles)
        has_required_role = bool(user_roles.intersection(set(required_roles)))
        
        if not has_required_role:
            logger.warning(
                "Access denied: insufficient roles",
                user_id=current_user.user_id,
                required=list(required_roles),
                user_roles=list(user_roles)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient roles. Required one of: {', '.join(required_roles)}"
            )
        
        return current_user
    
    return check_roles