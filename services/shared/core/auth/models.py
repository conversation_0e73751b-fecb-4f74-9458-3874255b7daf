"""
BetBet Platform - Authentication Models
======================================

Data models for authentication and authorization.
"""

from datetime import datetime
from typing import List, Optional
from enum import Enum
from pydantic import BaseModel


class UserRole(str, Enum):
    """User role enumeration"""
    USER = "user"
    PREMIUM_USER = "premium_user"
    MODERATOR = "moderator"
    GAME_ADMIN = "game_admin"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class TokenData(BaseModel):
    """JWT token data model"""
    user_id: str
    username: str
    email: Optional[str] = None
    roles: List[str] = []
    permissions: List[str] = []
    is_active: bool = True
    exp: Optional[datetime] = None
    iat: Optional[datetime] = None


class LoginRequest(BaseModel):
    """User login request model"""
    username: str
    password: str
    remember_me: bool = False


class TokenResponse(BaseModel):
    """Authentication token response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: str
    roles: List[str]