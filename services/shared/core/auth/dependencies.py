"""
BetBet Authentication Dependencies
=================================

FastAPI dependencies for unified authentication across all BetBet services.
Provides JWT validation, permission checking, and user context injection.
"""

from typing import Optional, List
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from .unified_auth_service import get_auth_service, ModulePermission, UserRole

logger = logging.getLogger(__name__)

# HTTP Bearer token security scheme
security = HTTPBearer(auto_error=False)

class CurrentUser:
    """Current authenticated user context"""
    
    def __init__(
        self,
        user_id: str,
        email: str,
        role: UserRole,
        permissions: List[str],
        is_service: bool = False,
        service_name: Optional[str] = None
    ):
        self.user_id = user_id
        self.email = email
        self.role = role
        self.permissions = permissions
        self.is_service = is_service
        self.service_name = service_name
        
    def has_permission(self, permission: ModulePermission) -> bool:
        """Check if user has specific permission"""
        return permission.value in self.permissions
        
    def has_role(self, role: UserRole) -> bool:
        """Check if user has specific role"""
        return self.role == role
        
    def has_any_role(self, roles: List[UserRole]) -> bool:
        """Check if user has any of the specified roles"""
        return self.role in roles
        
    def __repr__(self):
        return f"CurrentUser(user_id={self.user_id}, role={self.role}, is_service={self.is_service})"

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[CurrentUser]:
    """
    Get current user from JWT token (optional - returns None if no token)
    Use this for endpoints that work with or without authentication
    """
    if not credentials:
        return None
        
    try:
        auth_service = get_auth_service()
        
        # Check if it's a service token
        try:
            payload = auth_service.verify_service_token(credentials.credentials)
            return CurrentUser(
                user_id="",
                email="",
                role=UserRole.ADMIN,  # Service tokens have admin permissions
                permissions=[p.value for p in ModulePermission],
                is_service=True,
                service_name=payload.get("service")
            )
        except:
            # Not a service token, try as user token
            pass
            
        # Verify user token
        payload = auth_service.verify_token(credentials.credentials)
        
        # Check if token is blacklisted
        if "jti" in payload:
            if await auth_service.is_token_blacklisted(payload["jti"]):
                return None
                
        return CurrentUser(
            user_id=payload["user_id"],
            email=payload["email"],
            role=UserRole(payload["role"]),
            permissions=payload["permissions"]
        )
        
    except Exception as e:
        logger.warning(f"Token validation failed: {e}")
        return None

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> CurrentUser:
    """
    Get current user from JWT token (required - raises 401 if no valid token)
    Use this for endpoints that require authentication
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication credentials required",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    try:
        auth_service = get_auth_service()
        
        # Check if it's a service token
        try:
            payload = auth_service.verify_service_token(credentials.credentials)
            return CurrentUser(
                user_id="",
                email="",
                role=UserRole.ADMIN,
                permissions=[p.value for p in ModulePermission],
                is_service=True,
                service_name=payload.get("service")
            )
        except:
            # Not a service token, try as user token
            pass
            
        # Verify user token
        payload = auth_service.verify_token(credentials.credentials)
        
        # Check if token is blacklisted
        if "jti" in payload:
            if await auth_service.is_token_blacklisted(payload["jti"]):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked"
                )
                
        return CurrentUser(
            user_id=payload["user_id"],
            email=payload["email"],
            role=UserRole(payload["role"]),
            permissions=payload["permissions"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )

def require_permission(permission: ModulePermission):
    """
    Dependency factory for permission-based access control
    Usage: @app.get("/protected", dependencies=[Depends(require_permission(ModulePermission.GAMING_PLAY))])
    """
    async def check_permission(current_user: CurrentUser = Depends(get_current_user)):
        if not current_user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission.value}"
            )
        return current_user
    
    return check_permission

def require_role(role: UserRole):
    """
    Dependency factory for role-based access control
    Usage: @app.get("/admin", dependencies=[Depends(require_role(UserRole.ADMIN))])
    """
    async def check_role(current_user: CurrentUser = Depends(get_current_user)):
        if not current_user.has_role(role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required: {role.value}"
            )
        return current_user
    
    return check_role

def require_any_role(roles: List[UserRole]):
    """
    Dependency factory for multiple role access control
    Usage: @app.get("/premium", dependencies=[Depends(require_any_role([UserRole.PREMIUM, UserRole.ADMIN]))])
    """
    async def check_roles(current_user: CurrentUser = Depends(get_current_user)):
        if not current_user.has_any_role(roles):
            role_names = [role.value for role in roles]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required one of: {role_names}"
            )
        return current_user
    
    return check_roles

# Convenience dependencies for common permission patterns

async def require_gaming_access(current_user: CurrentUser = Depends(get_current_user)):
    """Require gaming module access"""
    if not current_user.has_permission(ModulePermission.GAMING_PLAY):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Gaming access required"
        )
    return current_user

async def require_betting_access(current_user: CurrentUser = Depends(get_current_user)):
    """Require betting module access"""
    if not (current_user.has_permission(ModulePermission.BETTING_CREATE) or 
            current_user.has_permission(ModulePermission.BETTING_PARTICIPATE)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Betting access required"
        )
    return current_user

async def require_expert_access(current_user: CurrentUser = Depends(get_current_user)):
    """Require expert analyst access"""
    if not current_user.has_permission(ModulePermission.EXPERT_PUBLISH):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Expert analyst privileges required"
        )
    return current_user

async def require_trading_access(current_user: CurrentUser = Depends(get_current_user)):
    """Require trading module access"""
    if not current_user.has_permission(ModulePermission.TRADING_ORDER):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Trading access required"
        )
    return current_user

async def require_admin_access(current_user: CurrentUser = Depends(get_current_user)):
    """Require admin access"""
    if not current_user.has_role(UserRole.ADMIN):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrator access required"
        )
    return current_user

# Service-to-service authentication
async def get_service_context(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> CurrentUser:
    """
    Get service context for service-to-service communication
    Only accepts service tokens, rejects user tokens
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Service authentication required"
        )
        
    try:
        auth_service = get_auth_service()
        payload = auth_service.verify_service_token(credentials.credentials)
        
        return CurrentUser(
            user_id="",
            email="",
            role=UserRole.ADMIN,
            permissions=[p.value for p in ModulePermission],
            is_service=True,
            service_name=payload.get("service")
        )
        
    except Exception as e:
        logger.error(f"Service token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid service token"
        )

# WebSocket authentication helper
async def authenticate_websocket(token: str) -> CurrentUser:
    """
    Authenticate WebSocket connection using token
    Returns CurrentUser or raises exception
    """
    try:
        auth_service = get_auth_service()
        
        # Try service token first
        try:
            payload = auth_service.verify_service_token(token)
            return CurrentUser(
                user_id="",
                email="",
                role=UserRole.ADMIN,
                permissions=[p.value for p in ModulePermission],
                is_service=True,
                service_name=payload.get("service")
            )
        except:
            pass
            
        # Try user token
        payload = auth_service.verify_token(token)
        
        # Check blacklist
        if "jti" in payload:
            if await auth_service.is_token_blacklisted(payload["jti"]):
                raise Exception("Token revoked")
                
        return CurrentUser(
            user_id=payload["user_id"],
            email=payload["email"],
            role=UserRole(payload["role"]),
            permissions=payload["permissions"]
        )
        
    except Exception as e:
        logger.error(f"WebSocket authentication error: {e}")
        raise Exception("Authentication failed")