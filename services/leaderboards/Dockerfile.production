# BetBet Leaderboards Service - Production Dockerfile
# Cross-Platform Leaderboards & Social Competition System
# Final module completing 100% platform implementation

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY leaderboards/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules and application code
COPY shared/ ./shared/
COPY leaderboards/ .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash leaderboards && \
    chown -R leaderboards:leaderboards /app
USER leaderboards

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8006}/health || exit 1

# Expose port
EXPOSE ${PORT:-8006}

# Production command
CMD ["python", "main.py"]