# BetBet Leaderboards Service - Production Dependencies
# Cross-Platform Leaderboards & Social Competition System
# Final module completing 100% platform implementation

# Core framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.4.2
pydantic-settings==2.0.3

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Logging & Monitoring
structlog==23.2.0
python-json-logger==2.0.7

# HTTP & WebSocket
httpx==0.25.2
websockets==12.0
aiofiles==23.2.1

# Redis (for high-performance caching)
redis==5.0.1
hiredis==2.2.3

# Performance optimization
orjson==3.9.10
numpy==1.24.4

# Background processing
celery==5.3.4
kombu==5.3.4

# Testing & Development
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
requests==2.31.0

# Production server
gunicorn==21.2.0

# Additional utilities for leaderboards
python-dateutil==2.8.2
pytz==2023.3