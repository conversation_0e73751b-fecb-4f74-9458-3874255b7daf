[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
asyncio_mode = auto

markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    websocket: WebSocket tests
    slow: Slow running tests