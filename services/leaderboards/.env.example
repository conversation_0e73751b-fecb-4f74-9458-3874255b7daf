# BetBet Leaderboards Service - Environment Configuration
# Copy to .env and update values for local development

# Database Configuration
DATABASE_URL=postgresql://localhost:5432/betbet
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration  
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=20

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRES_HOURS=24

# Service Configuration
PORT=8006
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info

# Leaderboard Specific Settings
CACHE_TTL_SECONDS=300
MAX_LEADERBOARD_SIZE=1000
PERFORMANCE_UPDATE_BATCH_SIZE=100
WEBSOCKET_BROADCAST_TIMEOUT=5

# Performance Targets (for monitoring)
LEADERBOARD_QUERY_TARGET_MS=10
REALTIME_UPDATE_TARGET_MS=5
CONCURRENT_USER_TARGET=100000

# Background Task Settings
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1
ACHIEVEMENT_CHECK_INTERVAL_SECONDS=300

# Monitoring & Observability
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=

# Feature Flags
ENABLE_ACHIEVEMENTS=true
ENABLE_CHALLENGES=true
ENABLE_CHAMPIONSHIPS=true
ENABLE_REAL_TIME_UPDATES=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_BURST=100