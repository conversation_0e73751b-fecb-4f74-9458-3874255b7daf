"""
BetBet Leaderboards Service - Test Configuration
===============================================

Pytest configuration and fixtures for the leaderboards service tests.
"""

import asyncio
import pytest
from typing import AsyncGenerator
from uuid import uuid4

from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import St<PERSON><PERSON>ool

from app.main import app
from app.database.models import Base
from app.api.dependencies import get_database_session


# Test database URL (in-memory SQLite for speed)
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_db_engine():
    """Create test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session"""
    async_session = async_sessionmaker(
        test_db_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session


@pytest.fixture
async def test_client(test_db_session):
    """Create test client with database dependency override"""
    
    async def override_get_database_session():
        yield test_db_session
    
    app.dependency_overrides[get_database_session] = override_get_database_session
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_id():
    """Generate a sample user ID for testing"""
    return uuid4()


@pytest.fixture
def sample_achievement_data():
    """Sample achievement data for testing"""
    return {
        "achievement_code": "TEST_ACHIEVEMENT",
        "name": "Test Achievement",
        "description": "A test achievement for unit tests",
        "category": "MULTI_MODULE",
        "modules_required": ["gaming", "betting"],
        "tier": "Bronze",
        "points": 100,
        "rarity": "Common",
        "criteria": {"min_score": 1000},
        "unlock_conditions": {"operator": "AND", "conditions": ["unified_platform_score >= 1000"]},
        "is_active": True,
        "is_hidden": False
    }


@pytest.fixture
def sample_performance_data():
    """Sample performance data for testing"""
    return {
        "gaming_stats": {"games_played": 50, "win_rate": 0.6},
        "betting_stats": {"total_bets": 100, "win_rate": 0.55, "roi": 0.1},
        "analyst_tier": "silver",
        "unified_platform_score": 1500,
        "platform_tier": "Silver",
        "community_reputation": 500,
        "modules_active": ["gaming", "betting", "analysts"]
    }