import os
import sys
import logging
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from sqlalchemy.orm import selectinload

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define models for leaderboards
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class UserRanking(Base):
    __tablename__ = "user_rankings"
    __table_args__ = {"schema": "leaderboards"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    leaderboard_type = Column(String(50), nullable=False)
    points = Column(Integer, default=0)
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    win_rate = Column(Numeric(5, 4), default=0)
    total_winnings = Column(Numeric(15, 2), default=0)
    rank = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class Achievement(Base):
    __tablename__ = "achievements"
    __table_args__ = {"schema": "leaderboards"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PostgresUUID(as_uuid=True), nullable=False)
    achievement_type = Column(String(50), nullable=False)
    title = Column(String(100), nullable=False)
    description = Column(Text)
    points_awarded = Column(Integer, default=0)
    unlocked_at = Column(DateTime(timezone=True), server_default=func.now())

class Settings(BaseSettings):
    """Application settings with secure defaults"""
    
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000",
        "https://betbet.com",
        "https://admin.betbet.com"
    ]
    
    SERVICE_NAME: str = "leaderboards"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8006
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"

settings = Settings()

app = FastAPI(
    title="BetBet Leaderboards",
    description="User rankings and achievements system",
    version=settings.SERVICE_VERSION
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Leaderboards Service connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

@app.get("/")
def read_root():
    return {
        "service": "BetBet Leaderboards",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "global": "/api/v1/leaderboards/global",
            "achievements": "/api/v1/leaderboards/achievements",
            "analytics": "/api/v1/leaderboards/analytics/overview"
        }
    }

@app.get("/health")
async def health_check():
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/leaderboards/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get leaderboards analytics overview"""

    # Get total users in leaderboards
    total_users_query = select(func.count(func.distinct(UserRanking.user_id)))
    total_users_result = await db.execute(total_users_query)
    total_users = total_users_result.scalar() or 0

    # Get total achievements
    total_achievements_query = select(func.count(Achievement.id))
    total_achievements_result = await db.execute(total_achievements_query)
    total_achievements = total_achievements_result.scalar() or 0

    return {
        "total_users": total_users,
        "total_achievements": total_achievements,
        "active_competitions": 5,
        "total_points_awarded": 125670
    }

@app.get("/api/v1/leaderboards/global")
async def get_global_leaderboard(
    leaderboard_type: str = Query("overall", description="Type of leaderboard"),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get global leaderboard rankings"""
    
    query = select(UserRanking).where(
        UserRanking.leaderboard_type == leaderboard_type
    ).order_by(UserRanking.rank.asc())
    
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    rankings = result.scalars().all()
    
    count_query = select(func.count(UserRanking.id)).where(
        UserRanking.leaderboard_type == leaderboard_type
    )
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    return {
        "leaderboard": [{
            "user_id": str(ranking.user_id),
            "rank": ranking.rank,
            "points": ranking.points,
            "wins": ranking.wins,
            "losses": ranking.losses,
            "win_rate": float(ranking.win_rate),
            "total_winnings": float(ranking.total_winnings)
        } for ranking in rankings],
        "leaderboard_type": leaderboard_type,
        "total_count": total_count,
        "page": page,
        "limit": limit
    }

@app.get("/api/v1/leaderboards/user/{user_id}/achievements")
async def get_user_achievements(
    user_id: str,
    db: AsyncSession = Depends(get_database_write)
):
    """Get achievements for a specific user"""
    
    query = select(Achievement).where(
        Achievement.user_id == UUID(user_id)
    ).order_by(Achievement.unlocked_at.desc())
    
    result = await db.execute(query)
    achievements = result.scalars().all()
    
    return [{
        "id": str(achievement.id),
        "achievement_type": achievement.achievement_type,
        "title": achievement.title,
        "description": achievement.description,
        "points_awarded": achievement.points_awarded,
        "unlocked_at": achievement.unlocked_at.isoformat()
    } for achievement in achievements]

@app.get("/api/v1/leaderboards/achievements")
async def get_achievements(
    achievement_type: Optional[str] = Query(None, description="Filter by achievement type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_database_write)
):
    """Get achievements with optional filtering"""
    
    query = select(Achievement)
    
    # Apply filters
    if achievement_type:
        query = query.where(Achievement.achievement_type == achievement_type)
    if user_id:
        query = query.where(Achievement.user_id == UUID(user_id))
    
    # Get total count
    count_query = select(func.count(Achievement.id))
    if achievement_type:
        count_query = count_query.where(Achievement.achievement_type == achievement_type)
    if user_id:
        count_query = count_query.where(Achievement.user_id == UUID(user_id))
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    # Apply pagination and ordering
    offset = (page - 1) * limit
    query = query.order_by(Achievement.unlocked_at.desc()).offset(offset).limit(limit)
    
    result = await db.execute(query)
    achievements = result.scalars().all()
    
    return {
        "achievements": [{
            "id": str(achievement.id),
            "user_id": str(achievement.user_id),
            "achievement_type": achievement.achievement_type,
            "title": achievement.title,
            "description": achievement.description,
            "points_awarded": achievement.points_awarded,
            "unlocked_at": achievement.unlocked_at.isoformat()
        } for achievement in achievements],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }

@app.get("/api/v1/leaderboards/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get leaderboards analytics overview"""
    
    # Total users in rankings
    total_users_query = select(func.count(func.distinct(UserRanking.user_id)))
    total_users_result = await db.execute(total_users_query)
    total_users = total_users_result.scalar() or 0
    
    # Total achievements unlocked
    total_achievements_query = select(func.count(Achievement.id))
    total_achievements_result = await db.execute(total_achievements_query)
    total_achievements = total_achievements_result.scalar() or 0
    
    # Average points
    avg_points_query = select(func.avg(UserRanking.points)).where(
        UserRanking.leaderboard_type == 'overall'
    )
    avg_points_result = await db.execute(avg_points_query)
    avg_points = avg_points_result.scalar() or 0
    
    # Top user points
    top_points_query = select(func.max(UserRanking.points)).where(
        UserRanking.leaderboard_type == 'overall'
    )
    top_points_result = await db.execute(top_points_query)
    top_points = top_points_result.scalar() or 0
    
    return {
        "total_ranked_users": total_users,
        "total_achievements_unlocked": total_achievements,
        "average_points": float(avg_points),
        "highest_points": top_points,
        "timestamp": datetime.utcnow().isoformat()
    }

# New unified leaderboard endpoints
@app.get("/api/v1/leaderboards/unified-leaderboards")
async def get_unified_leaderboard(
    view: str = Query("unified", description="Leaderboard view type"),
    time_range: str = Query("all_time", description="Time range filter"),
    affiliation_filter: str = Query(None, description="Affiliation filter"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: AsyncSession = Depends(get_database_write)
):
    """Get unified cross-platform leaderboard"""

    try:
        # Query unified user performance with user details
        query = text("""
            SELECT
                u.id as user_id,
                u.username,
                u.avatar_url,
                u.first_name,
                u.last_name,
                oup.unified_platform_score,
                oup.platform_tier,
                oup.gaming_stats,
                oup.betting_stats,
                oup.trading_performance,
                oup.analyst_metrics,
                oup.cross_platform_consistency_score,
                ROW_NUMBER() OVER (ORDER BY oup.unified_platform_score DESC) as rank
            FROM leaderboards.unified_user_performance oup
            JOIN public.users u ON oup.user_id = u.id
            WHERE u.is_active = true
            ORDER BY oup.unified_platform_score DESC
            LIMIT :limit OFFSET :offset
        """)

        result = await db.execute(query, {"limit": limit, "offset": offset})
        rows = result.fetchall()

        # Count total users
        count_query = text("""
            SELECT COUNT(*)
            FROM leaderboards.unified_user_performance oup
            JOIN public.users u ON oup.user_id = u.id
            WHERE u.is_active = true
        """)
        count_result = await db.execute(count_query)
        total_count = count_result.scalar()

        # Format leaderboard entries
        leaderboard = []
        for row in rows:
            gaming_stats = row.gaming_stats or {}
            betting_stats = row.betting_stats or {}
            trading_performance = row.trading_performance or {}
            analyst_metrics = row.analyst_metrics or {}

            # Calculate tier progress (simplified)
            tier_progress = 0.5  # Default progress

            entry = {
                "rank": row.rank,
                "user_id": str(row.user_id),
                "username": row.username or f"User_{str(row.user_id)[:8]}",
                "avatar_url": row.avatar_url,
                "affiliation": None,  # TODO: Add affiliation support
                "unified_score": float(row.unified_platform_score or 0),
                "platform_tier": row.platform_tier or "Bronze",
                "tier_progress": tier_progress,
                "gaming_score": gaming_stats.get("chess", {}).get("rating", 0),
                "gaming_change": 0,  # TODO: Calculate from historical data
                "betting_score": betting_stats.get("total_winnings", 0),
                "betting_change": 0,
                "trading_score": trading_performance.get("total_pnl", 0),
                "trading_change": 0,
                "analyst_score": analyst_metrics.get("reputation_score", 0),
                "analyst_change": 0,
                "cross_platform_consistency": float(row.cross_platform_consistency_score or 0),
                "active_modules": ["gaming", "betting", "trading", "analyst"],  # TODO: Calculate dynamically
                "recent_achievements": [],  # TODO: Add achievements
                "score_history": []  # TODO: Add historical data
            }
            leaderboard.append(entry)

        return {
            "success": True,
            "data": {
                "leaderboard": leaderboard,
                "total_count": total_count
            }
        }

    except Exception as e:
        logger.error(f"Failed to get unified leaderboard: {e}")
        return {
            "success": False,
            "error": f"Failed to retrieve leaderboard: {str(e)}",
            "data": {"leaderboard": [], "total_count": 0}
        }

@app.get("/api/v1/leaderboards/personal-ranking/{user_id}")
async def get_personal_ranking(
    user_id: str,
    db: AsyncSession = Depends(get_database_write)
):
    """Get personal ranking and performance for a user"""

    try:
        # Query user's performance data
        query = text("""
            SELECT
                u.id as user_id,
                u.username,
                oup.unified_platform_score,
                oup.platform_tier,
                oup.gaming_stats,
                oup.betting_stats,
                oup.trading_performance,
                oup.analyst_metrics,
                oup.cross_platform_consistency_score,
                (SELECT COUNT(*) + 1 FROM leaderboards.unified_user_performance oup2
                 JOIN public.users u2 ON oup2.user_id = u2.id
                 WHERE u2.is_active = true AND oup2.unified_platform_score > oup.unified_platform_score) as rank,
                (SELECT COUNT(*) FROM leaderboards.unified_user_performance oup3
                 JOIN public.users u3 ON oup3.user_id = u3.id
                 WHERE u3.is_active = true) as total_users
            FROM leaderboards.unified_user_performance oup
            JOIN public.users u ON oup.user_id = u.id
            WHERE u.id = :user_id AND u.is_active = true
        """)

        result = await db.execute(query, {"user_id": user_id})
        row = result.fetchone()

        if not row:
            return {
                "success": False,
                "error": "User not found or no performance data",
                "data": None
            }

        # Calculate percentile
        percentile = ((row.total_users - row.rank) / row.total_users) * 100 if row.total_users > 0 else 0

        # Determine next tier
        current_tier = row.platform_tier or "Bronze"
        tier_order = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master"]
        current_index = tier_order.index(current_tier) if current_tier in tier_order else 0
        next_tier = tier_order[min(current_index + 1, len(tier_order) - 1)]

        # Calculate tier progress and points to next
        tier_thresholds = {"Bronze": 0, "Silver": 2000, "Gold": 3500, "Platinum": 5000, "Diamond": 6500, "Master": 8000}
        current_score = float(row.unified_platform_score or 0)
        current_threshold = tier_thresholds.get(current_tier, 0)
        next_threshold = tier_thresholds.get(next_tier, 10000)

        if next_threshold > current_threshold:
            tier_progress = (current_score - current_threshold) / (next_threshold - current_threshold)
            tier_progress = max(0, min(1, tier_progress))
            points_to_next = max(0, next_threshold - current_score)
        else:
            tier_progress = 1.0
            points_to_next = 0

        # Format response
        performance_data = {
            "user_id": str(row.user_id),
            "global_ranking": {
                "unified_rank": row.rank,
                "unified_score": current_score,
                "percentile": round(percentile, 1),
                "total_users": row.total_users
            },
            "platform_tier": {
                "current": current_tier,
                "next": next_tier,
                "progress": round(tier_progress, 3),
                "points_to_next": round(points_to_next, 1)
            },
            "module_rankings": {
                "gaming": {
                    "rank": row.rank,  # Simplified
                    "score": (row.gaming_stats or {}).get("chess", {}).get("rating", 0),
                    "percentile": percentile,
                    "change_24h": 0,
                    "change_7d": 0,
                    "best_rank_ever": row.rank,
                    "best_score_ever": (row.gaming_stats or {}).get("chess", {}).get("best_rating", 0)
                },
                "betting": {
                    "rank": row.rank,
                    "score": (row.betting_stats or {}).get("total_winnings", 0),
                    "percentile": percentile,
                    "change_24h": 0,
                    "change_7d": 0,
                    "best_rank_ever": row.rank,
                    "best_score_ever": (row.betting_stats or {}).get("total_winnings", 0)
                },
                "trading": {
                    "rank": row.rank,
                    "score": (row.trading_performance or {}).get("total_pnl", 0),
                    "percentile": percentile,
                    "change_24h": 0,
                    "change_7d": 0,
                    "best_rank_ever": row.rank,
                    "best_score_ever": (row.trading_performance or {}).get("total_pnl", 0)
                },
                "analyst": {
                    "rank": row.rank,
                    "score": (row.analyst_metrics or {}).get("reputation_score", 0),
                    "percentile": percentile,
                    "change_24h": 0,
                    "change_7d": 0,
                    "best_rank_ever": row.rank,
                    "best_score_ever": (row.analyst_metrics or {}).get("reputation_score", 0)
                }
            },
            "cross_platform_consistency": float(row.cross_platform_consistency_score or 0),
            "active_modules": ["gaming", "betting", "trading", "analyst"],
            "performance_trends": {
                "daily_scores": [],
                "weekly_scores": [],
                "monthly_scores": []
            },
            "achievement_summary": {
                "total_unlocked": 0,
                "total_available": 4,
                "recent_unlocks": [],
                "progress_towards_next": []
            }
        }

        return {
            "success": True,
            "data": performance_data
        }

    except Exception as e:
        logger.error(f"Failed to get personal ranking for user {user_id}: {e}")
        return {
            "success": False,
            "error": f"Failed to retrieve personal ranking: {str(e)}",
            "data": None
        }

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)