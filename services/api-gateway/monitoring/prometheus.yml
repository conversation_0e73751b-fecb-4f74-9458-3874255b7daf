# Prometheus Configuration for BetBet API Gateway

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Kong API Gateway metrics
  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  # Kong Proxy metrics
  - job_name: 'kong-proxy'
    static_configs:
      - targets: ['kong:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

# Alerting rules (future implementation)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093