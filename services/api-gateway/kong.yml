# BetBet Platform API Gateway Configuration
# Kong Declarative Configuration for Unified Service Routing

_format_version: "3.0"

# Services Configuration
services:
  - name: gaming-service
    url: http://gaming-engine:8001
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    
  - name: custom-betting-service
    url: http://custom-betting:8002
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    
  - name: expert-analysts-service
    url: http://expert-analysts:8003
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    
  - name: sports-analysis-service
    url: http://sports-analysis:8004
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    
  - name: odds-trading-service
    url: http://odds-exchange:8005
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    
  - name: leaderboards-service
    url: http://leaderboards:8006
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000

# Routes Configuration
routes:
  # Gaming Engine Routes
  - name: gaming-api
    service: gaming-service
    paths: ["/api/v1/gaming"]
    strip_path: false
    preserve_host: true
    
  # Custom Betting Routes
  - name: betting-api
    service: custom-betting-service
    paths: ["/api/v1/betting"]
    strip_path: false
    preserve_host: true
    
  # Expert Analysts Routes
  - name: experts-api
    service: expert-analysts-service
    paths: ["/api/v1/experts"]
    strip_path: false
    preserve_host: true
    
  # Sports Analysis Routes
  - name: sports-api
    service: sports-analysis-service
    paths: ["/api/v1/sports"]
    strip_path: false
    preserve_host: true
    
  # Trading Routes
  - name: trading-api
    service: odds-trading-service
    paths: ["/api/v1/trading"]
    strip_path: false
    preserve_host: true
    
  # Leaderboards Routes
  - name: leaderboards-api
    service: leaderboards-service
    paths: ["/api/v1/leaderboards"]
    strip_path: false
    preserve_host: true

# Global Plugins
plugins:
  # CORS Support for Web/Mobile
  - name: cors
    config:
      origins:
        - "http://localhost:3000"
        - "https://betbet-platform.com"
        - "https://*.betbet-platform.com"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Authorization
        - Content-Type
        - Origin
        - X-Requested-With
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600
      
  # Rate Limiting
  - name: rate-limiting
    config:
      minute: 100
      hour: 1000
      day: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      
  # Request Size Limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false
      
  # Security Headers
  - name: response-transformer
    config:
      add:
        headers:
          - "X-Content-Type-Options: nosniff"
          - "X-Frame-Options: DENY"
          - "X-XSS-Protection: 1; mode=block"
          - "Referrer-Policy: strict-origin-when-cross-origin"

# JWT Authentication Plugin (Applied to all routes)
plugins:
  - name: jwt
    config:
      uri_param_names:
        - jwt
      cookie_names:
        - jwt
      claims_to_verify:
        - exp
        - iat
      key_claim_name: iss
      secret_is_base64: false
      anonymous: ""
      run_on_preflight: true
      maximum_expiration: 3600

# Service-Specific Plugins
services:
  - name: gaming-service
    plugins:
      - name: prometheus
        config:
          per_consumer: true
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true

# Consumer Configuration for Service-to-Service Communication
consumers:
  - username: betbet-frontend
    custom_id: web-app
    jwt_secrets:
      - key: betbet-web-app
        secret: "your-jwt-secret-key-here"
        
  - username: betbet-mobile
    custom_id: mobile-app  
    jwt_secrets:
      - key: betbet-mobile-app
        secret: "your-jwt-secret-key-here"

# Upstream Configuration for Load Balancing
upstreams:
  - name: gaming-cluster
    algorithm: round-robin
    targets:
      - target: gaming-engine:8001
        weight: 100
        
  - name: betting-cluster
    algorithm: round-robin
    targets:
      - target: custom-betting:8002
        weight: 100
        
  - name: experts-cluster
    algorithm: round-robin
    targets:
      - target: expert-analysts:8003
        weight: 100
        
  - name: sports-cluster
    algorithm: round-robin
    targets:
      - target: sports-analysis:8004
        weight: 100
        
  - name: trading-cluster
    algorithm: round-robin
    targets:
      - target: odds-exchange:8005
        weight: 100
        
  - name: leaderboards-cluster
    algorithm: round-robin
    targets:
      - target: leaderboards:8006
        weight: 100