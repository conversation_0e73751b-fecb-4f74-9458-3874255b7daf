/**
 * Clerk Authentication Integration for API Gateway
 * Handles Clerk JWT token verification and user management
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

class ClerkAuthManager {
  constructor(secretKey) {
    this.secretKey = secretKey;
    this.baseUrl = 'https://api.clerk.com/v1';
  }

  /**
   * Verify Clerk JWT token
   */
  async verifyToken(token) {
    if (!token) {
      throw new Error('No authentication token provided');
    }

    // Remove 'Bearer ' prefix if present
    if (token.startsWith('Bearer ')) {
      token = token.substring(7);
    }

    try {
      // For development, we'll decode the JWT without verification
      // In production, you should fetch and cache <PERSON>'s public keys
      logger.info('Attempting to decode token', { tokenLength: token.length, tokenStart: token.substring(0, 20) + '...' });
      
      const decoded = jwt.decode(token, { complete: true });
      
      if (!decoded || !decoded.payload) {
        logger.error('Failed to decode token', { decoded });
        throw new Error('Invalid token format');
      }

      const payload = decoded.payload;
      const userId = payload.sub;

      if (!userId) {
        throw new Error('Invalid token: no user ID found');
      }

      // Check if token is expired
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        throw new Error('Token has expired');
      }

      // Get user details from token or fetch from Clerk
      const userData = {
        id: userId,
        email: payload.email || '',
        username: payload.username || '',
        firstName: payload.given_name || '',
        lastName: payload.family_name || '',
        imageUrl: payload.picture || '',
        role: this.extractUserRole(payload),
        roles: this.extractUserRoles(payload),
        metadata: payload.public_metadata || {}
      };
      
      logger.info('Successfully verified Clerk token', { userId });
      return userData;
    } catch (error) {
      logger.error('Clerk token verification failed', { error: error.message });
      throw new Error('Authentication verification failed');
    }
  }

  /**
   * Get user details from Clerk
   */
  async getUserDetails(userId, headers) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/users/${userId}`,
        { headers }
      );

      if (response.status === 200) {
        const userData = response.data;
        
        return {
          id: userData.id,
          email: userData.email_addresses?.[0]?.email_address || '',
          username: userData.username || '',
          firstName: userData.first_name || '',
          lastName: userData.last_name || '',
          imageUrl: userData.image_url || '',
          createdAt: userData.created_at,
          lastSignInAt: userData.last_sign_in_at,
          role: this.extractUserRole(userData),
          roles: this.extractUserRoles(userData),
          metadata: userData.public_metadata || {}
        };
      } else {
        logger.warn('Failed to get user details from Clerk', { userId });
        return {
          id: userId,
          email: '',
          username: '',
          role: 'user',
          roles: ['user'],
          metadata: {}
        };
      }
    } catch (error) {
      logger.error('Error fetching user details', { userId, error: error.message });
      return {
        id: userId,
        email: '',
        username: '',
        role: 'user',
        roles: ['user'],
        metadata: {}
      };
    }
  }

  /**
   * Extract primary user role from JWT payload
   */
  extractUserRole(payload) {
    const metadata = payload.public_metadata || {};
    
    if (metadata.is_admin || metadata.roles?.includes('admin')) {
      return 'admin';
    }
    
    if (metadata.is_moderator || metadata.roles?.includes('moderator')) {
      return 'moderator';
    }
    
    return 'user';
  }

  /**
   * Extract all user roles from JWT payload
   */
  extractUserRoles(payload) {
    const metadata = payload.public_metadata || {};
    let roles = metadata.roles || [];
    
    // Default role
    if (!roles.length) {
      roles = ['user'];
    }
    
    // Add admin role if user has admin privileges
    if (metadata.is_admin && !roles.includes('admin')) {
      roles.push('admin');
    }
    
    // Add moderator role if applicable
    if (metadata.is_moderator && !roles.includes('moderator')) {
      roles.push('moderator');
    }
    
    return roles;
  }
}

/**
 * Middleware for Clerk authentication
 */
const authenticateClerkToken = (clerkManager) => {
  return async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    logger.info('Authentication attempt', { 
      path: req.path, 
      hasAuthHeader: !!authHeader, 
      hasToken: !!token,
      authHeaderStart: authHeader ? authHeader.substring(0, 20) + '...' : 'none'
    });

    if (!token) {
      logger.warn('No token provided', { path: req.path });
      return res.status(401).json({ error: 'Access token required' });
    }

    try {
      const userData = await clerkManager.verifyToken(token);
      req.user = userData;
      logger.info('Authentication successful', { userId: userData.id, path: req.path });
      next();
    } catch (error) {
      logger.warn('Clerk authentication failed', { 
        error: error.message, 
        ip: req.ip,
        path: req.path
      });
      return res.status(403).json({ error: 'Invalid token' });
    }
  };
};

/**
 * Admin authentication middleware
 */
const authenticateAdmin = (req, res, next) => {
  if (!req.user || !['admin', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

module.exports = {
  ClerkAuthManager,
  authenticateClerkToken,
  authenticateAdmin
};