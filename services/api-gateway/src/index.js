const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const redis = require('redis');
const winston = require('winston');
// const swaggerUi = require('swagger-ui-express');
// const YAML = require('yamljs');
const path = require('path');
const { ClerkAuthManager, authenticateClerkToken, authenticateAdmin } = require('./clerk-auth');
// Load environment variables from project root
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });
require('dotenv').config({ path: path.join(__dirname, '../../../.env.local') });

const app = express();
const PORT = process.env.PORT || 8000;

// Validate critical environment variables
if (!process.env.CLERK_SECRET_KEY) {
  console.error('FATAL ERROR: CLERK_SECRET_KEY is not defined.');
  process.exit(1);
}

// Initialize Clerk authentication
const clerkAuthManager = new ClerkAuthManager(process.env.CLERK_SECRET_KEY);

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'api-gateway.log' })
  ]
});

// Redis client for caching and session management - Optional for local development
let redisClient;
try {
  redisClient = redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  });
} catch (error) {
  logger.warn('Redis not available, continuing without caching:', error.message);
}

redisClient.on('error', (err) => {
  logger.error('Redis connection error:', err);
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:3000',  // Web frontend
    'http://localhost:3001',  // Admin frontend
    'http://localhost:19006'  // Mobile app (Expo)
  ],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs (increased for development)
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Create Clerk authentication middleware
const authenticateToken = authenticateClerkToken(clerkAuthManager);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Load OpenAPI specification
let swaggerDocument;
try {
  const swaggerPath = path.join(__dirname, '../../../docs/api/openapi.yaml');
  swaggerDocument = YAML.load(swaggerPath);
  
  // Update server URLs based on environment
  if (process.env.NODE_ENV === 'development') {
    swaggerDocument.servers = [
      { url: `http://localhost:${PORT}`, description: 'Local Development' }
    ];
  }
} catch (error) {
  logger.warn('Could not load OpenAPI specification:', error.message);
  swaggerDocument = {
    openapi: '3.0.3',
    info: {
      title: 'BetBet Platform API',
      version: '1.0.0',
      description: 'API documentation not available. Please check the docs/api/openapi.yaml file.'
    },
    paths: {}
  };
}

// Swagger UI routes - TEMPORARILY DISABLED
// app.use('/api-docs', swaggerUi.serve);
/*
app.get('/api-docs', swaggerUi.setup(swaggerDocument, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'BetBet Platform API Documentation',
  explorer: true,
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    tryItOutEnabled: true
  }
}));
*/

// API documentation redirect - TEMPORARILY DISABLED
/*
app.get('/docs', (req, res) => {
  res.redirect('/api-docs');
});
*/

// OpenAPI JSON endpoint
app.get('/openapi.json', (req, res) => {
  res.json(swaggerDocument);
});

// Service routes configuration - Unified routing for all services
const services = {
  gaming: {
    target: 'http://betbet-gaming:8001',
    pathRewrite: {
      '^/api/gaming/health$': '/health',
      '^/api/gaming/docs$': '/docs',
      '^/api/gaming': '/api/v1/gaming'
    }
  },
  'custom-betting': {
    target: 'http://betbet-custom-betting:8002',
    pathRewrite: {
      '^/api/custom-betting/health$': '/health',
      '^/api/custom-betting/docs$': '/docs',
      '^/api/custom-betting': '/api/v1/custom-betting'
    }
  },
  trading: {
    target: 'http://betbet-trading:8003',
    pathRewrite: {
      '^/api/trading/health$': '/health',
      '^/api/trading/docs$': '/docs',
      '^/api/trading': '/api/v1/trading'
    }
  },
  experts: {
    target: 'http://betbet-experts:8004',
    pathRewrite: {
      '^/api/experts/health$': '/health',
      '^/api/experts/docs$': '/docs',
      '^/api/experts': '/api/v1/experts'
    }
  },
  sports: {
    target: 'http://betbet-sports:8005',
    pathRewrite: {
      '^/api/sports/health$': '/health',
      '^/api/sports/docs$': '/docs',
      '^/api/sports': '/api/v1/analysis'
    }
  },
  leaderboards: {
    target: 'http://betbet-leaderboards:8006',
    pathRewrite: {
      '^/api/leaderboards/health$': '/health',
      '^/api/leaderboards/docs$': '/docs',
      '^/api/leaderboards': '/api/v1/leaderboards'
    }
  },
  wallet: {
    target: 'http://betbet-wallet:8007',
    pathRewrite: {
      '^/api/wallet/health$': '/health',
      '^/api/wallet/docs$': '/docs',
      '^/api/wallet': '/api/v1/wallet'
    }
  },
  events: {
    target: 'http://betbet-events:8008',
    pathRewrite: {
      '^/api/events/health$': '/health',
      '^/api/events/docs$': '/docs',
      '^/api/events': '/api/v1/events'
    }
  },
  'user-sync': {
    target: 'http://betbet-user-sync:8009',
    pathRewrite: {
      '^/api/user-sync/health$': '/health',
      '^/api/user-sync/docs$': '/docs',
      '^/api/user-sync': '/api/v1/user-sync'
    }
  }
};

// Public routes (no authentication required)
const publicRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh',
  '/api/sports/fixtures',
  '/api/sports/leagues',
  '/api/sports/teams',
  '/api/leaderboards/unified',
  '/health',
  '/docs',
  // Health endpoints
  '/api/gaming/health',
  '/api/custom-betting/health',
  '/api/experts/health',
  '/api/sports/health',
  '/api/trading/health',
  '/api/leaderboards/health',
  '/api/wallet/health',
  '/api/events/health',
  '/api/user-sync/health',
  // Docs endpoints
  '/api/gaming/docs',
  '/api/custom-betting/docs',
  '/api/experts/docs',
  '/api/sports/docs',
  '/api/trading/docs',
  '/api/leaderboards/docs',
  '/api/wallet/docs',
  '/api/events/docs',
  '/api/user-sync/docs',
  // Temporary public access for testing (remove in production)
  '/api/gaming',
  '/api/custom-betting',
  '/api/experts',
  '/api/sports',
  '/api/trading',
  '/api/leaderboards',
  '/api/wallet',
  '/api/events',
  '/api/user-sync'
];

// Admin-only routes
const adminRoutes = [
  '/api/admin',
  '/api/betting/admin',
  '/api/gaming/admin',
  '/api/experts/admin',
  '/api/sports/admin',
  '/api/leaderboards/admin'
];

// Setup proxies for each service FIRST (before authentication middleware)
// This ensures proxy routes are handled before authentication interferes
Object.entries(services).forEach(([serviceName, config]) => {
  const proxyPath = `/api/${serviceName}`;
  
  app.use(proxyPath, createProxyMiddleware({
    target: config.target,
    changeOrigin: true,
    pathRewrite: config.pathRewrite,
    timeout: 30000, // 30 second timeout
    proxyTimeout: 30000, // 30 second proxy timeout
    logLevel: 'debug', // Enable debug logging
    secure: false, // Allow self-signed certificates
    followRedirects: true,
    onError: (err, req, res) => {
      logger.error(`Proxy error for ${serviceName}:`, err.message);
      res.status(502).json({
        error: 'Service temporarily unavailable',
        service: serviceName
      });
    },
    onProxyReq: (proxyReq, req, res) => {
      // Add user info to headers for downstream services
      if (req.user) {
        proxyReq.setHeader('X-User-ID', req.user.id);
        proxyReq.setHeader('X-User-Role', req.user.role);
        proxyReq.setHeader('X-User-Email', req.user.email);
        proxyReq.setHeader('X-User-Username', req.user.username || '');
        proxyReq.setHeader('X-User-Name', `${req.user.firstName || ''} ${req.user.lastName || ''}`.trim());
        
        // Pass through original Authorization header for Clerk token
        if (req.headers.authorization) {
          proxyReq.setHeader('Authorization', req.headers.authorization);
        }
      }
      
      logger.info(`Proxying ${req.method} ${req.path} to ${serviceName} - Target URL: ${proxyReq.path}`);
    }
  }));
});

// Authentication middleware - apply AFTER proxy setup to avoid interference
app.use('/api', (req, res, next) => {
  const path = req.path;
  const fullPath = '/api' + path; // Reconstruct full path since Express strips the mount path

  // Debug logging
  console.log(`Full URL: ${req.url}, Path: ${path}, Full Path: ${fullPath}, Method: ${req.method}`);
  console.log(`Request path: ${fullPath}, Public routes: ${JSON.stringify(publicRoutes)}`);

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => fullPath.startsWith(route));
  console.log(`Is public route: ${isPublicRoute}`);
  if (isPublicRoute) {
    return next();
  }

  // Check if route requires admin access
  const isAdminRoute = adminRoutes.some(route => fullPath.startsWith(route));
  if (isAdminRoute) {
    return authenticateToken(req, res, () => {
      authenticateAdmin(req, res, next);
    });
  }

  // All other routes require authentication
  authenticateToken(req, res, next);
});

// Chess WebSocket proxy - route to gaming engine
app.use('/ws/chess', createProxyMiddleware({
  target: 'http://betbet-gaming:8001',
  ws: true,
  changeOrigin: true,
  onError: (err, req, res) => {
    logger.error('Chess WebSocket proxy error:', err.message);
    res.status(502).json({
      error: 'Chess WebSocket service temporarily unavailable'
    });
  }
}));

// General WebSocket proxy for real-time features
app.use('/ws', createProxyMiddleware({
  target: 'http://websocket-manager:8080', // Use Docker container name
  ws: true,
  changeOrigin: true,
  onError: (err, req, res) => {
    logger.error('WebSocket proxy error:', err.message);
    res.status(502).json({
      error: 'WebSocket service temporarily unavailable'
    });
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    requestId: req.headers['x-request-id'] || 'unknown'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.path,
    method: req.method
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`API Gateway running on port ${PORT}`);
  logger.info('Available services:', Object.keys(services));
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  redisClient.quit();
  process.exit(0);
});

module.exports = app;