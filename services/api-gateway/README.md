# BetBet API Gateway

**Unified API Gateway for BetBet Platform Integration**

This API Gateway provides centralized routing, authentication, and monitoring for all BetBet microservices, enabling seamless integration between web and mobile applications.

## 🎯 Purpose

The API Gateway serves as the single entry point for all client requests, providing:

- **Unified Routing**: Route requests to appropriate microservices
- **Authentication**: Centralized JWT validation across all services
- **Rate Limiting**: DDoS protection and fair usage policies
- **CORS Management**: Cross-origin request handling for web/mobile
- **Monitoring**: Centralized logging and metrics collection

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Apps   │────│   API Gateway    │────│  Microservices  │
│  - Web App      │    │  - Kong Proxy    │    │  - Gaming       │
│  - Mobile App   │    │  - Rate Limiting │    │  - Betting      │
│  - Admin Panel  │    │  - Auth Validation│    │  - Expert Analyst│
└─────────────────┘    │  - Load Balancing│    │  - Sports AI    │
                       └──────────────────┘    │  - Trading      │
                                               │  - Leaderboards │
                                               └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Access to BetBet microservices network

### 1. Environment Setup

```bash
# Navigate to API Gateway directory
cd services/api-gateway

# Create monitoring directory for Prometheus
mkdir -p monitoring
```

### 2. Start API Gateway

```bash
# Start all gateway services
docker-compose up -d

# Verify services are running
docker-compose ps

# Check Kong health
curl -i http://localhost:8001/status
```

### 3. Access Points

- **API Gateway Proxy**: http://localhost:8000
- **Kong Admin API**: http://localhost:8001
- **Konga GUI**: http://localhost:1337
- **Prometheus Monitoring**: http://localhost:9090
- **Redis Cache**: localhost:6379

## 📊 Service Routing

All client requests should go through the API Gateway at `http://localhost:8000`:

| Service | Original Port | Gateway Route | Example |
|---------|---------------|---------------|---------|
| Gaming Engine | 8001 | `/api/v1/gaming/*` | `localhost:8000/api/v1/gaming/sessions` |
| Custom Betting | 8002 | `/api/v1/betting/*` | `localhost:8000/api/v1/betting/bets` |
| Expert Analysts | 8003 | `/api/v1/experts/*` | `localhost:8000/api/v1/experts/picks` |
| Sports Analysis | 8004 | `/api/v1/sports/*` | `localhost:8000/api/v1/sports/analysis` |
| Trading Exchange | 8005 | `/api/v1/trading/*` | `localhost:8000/api/v1/trading/orders` |
| Leaderboards | 8006 | `/api/v1/leaderboards/*` | `localhost:8000/api/v1/leaderboards/unified` |

## 🔐 Authentication

The API Gateway implements unified JWT authentication:

```typescript
// Frontend integration example
const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  headers: {
    'Authorization': `Bearer ${jwtToken}`,
    'Content-Type': 'application/json'
  }
});

// All service requests now go through gateway
const gameSession = await apiClient.get('/api/v1/gaming/sessions/123');
const userBets = await apiClient.get('/api/v1/betting/bets?user_id=456');
```

## 📈 Rate Limiting

Default rate limits (configurable in `kong.yml`):

- **Per Minute**: 100 requests
- **Per Hour**: 1,000 requests  
- **Per Day**: 10,000 requests

## 🔧 Configuration

### JWT Secret Configuration

Update JWT secrets in `kong.yml`:

```yaml
consumers:
  - username: betbet-frontend
    jwt_secrets:
      - key: betbet-web-app
        secret: "your-actual-jwt-secret-key"
```

### Service URLs Configuration

Update service URLs in `kong.yml` for your environment:

```yaml
services:
  - name: gaming-service
    url: http://gaming-engine:8001  # Update for your setup
```

## 📊 Monitoring

### Health Checks

```bash
# Check API Gateway health
curl http://localhost:8001/status

# Check service connectivity
curl http://localhost:8000/api/v1/gaming/health
curl http://localhost:8000/api/v1/betting/health
```

### Prometheus Metrics

Kong automatically exposes metrics at:
- **Kong Metrics**: http://localhost:8001/metrics
- **Prometheus UI**: http://localhost:9090

Key metrics to monitor:
- Request latency across services
- Error rates by service
- Request volume by endpoint
- Authentication success/failure rates

## 🛠️ Development Commands

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f kong

# Restart Kong with new config
docker-compose restart kong

# Stop all services
docker-compose down

# Reset everything (including data)
docker-compose down -v
```

## 🚀 Production Deployment

For production deployment:

1. **Update Secrets**: Replace all default passwords and JWT secrets
2. **Configure SSL**: Add SSL termination at the gateway level
3. **Update CORS**: Configure proper domain origins
4. **Scale Services**: Use Kong's clustering for high availability
5. **Monitoring**: Connect to your monitoring stack

### Environment Variables

Create `.env` file for production:

```bash
KONG_PG_PASSWORD=your-secure-password
JWT_SECRET=your-jwt-secret-key
REDIS_PASSWORD=your-redis-password
```

## 🔄 Integration with Frontend

Update your frontend API configuration:

```typescript
// Before: Multiple service endpoints
const GAMING_API = 'http://localhost:8001';
const BETTING_API = 'http://localhost:8002';
// ...

// After: Single gateway endpoint
const API_BASE_URL = 'http://localhost:8000';

export const apiRoutes = {
  gaming: '/api/v1/gaming',
  betting: '/api/v1/betting',
  experts: '/api/v1/experts',
  sports: '/api/v1/sports',
  trading: '/api/v1/trading',
  leaderboards: '/api/v1/leaderboards'
};
```

## 🚨 Troubleshooting

### Common Issues

1. **Service not reachable**: Ensure all services are on the `betbet-network`
2. **Authentication fails**: Verify JWT secret configuration
3. **CORS errors**: Update CORS origins in `kong.yml`
4. **Rate limiting**: Adjust limits for development environment

### Debug Commands

```bash
# Check Kong configuration
docker-compose exec kong kong config parse /kong/kong.yml

# Test service connectivity
docker-compose exec kong curl http://gaming-engine:8001/health

# View Kong error logs
docker-compose logs kong | grep ERROR
```

## 📚 Next Steps

1. **WebSocket Manager**: Implement unified WebSocket routing
2. **Mobile Integration**: Configure mobile app endpoints
3. **Analytics**: Set up detailed request analytics
4. **Security**: Implement advanced security policies
5. **Performance**: Add caching and optimization

---

**Status**: ✅ API Gateway Infrastructure Complete
**Next**: WebSocket Manager Implementation