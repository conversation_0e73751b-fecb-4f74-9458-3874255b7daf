"""
Stripe Payment Integration for BetBet Wallet Service
==================================================

This module handles Stripe payment processing for deposits and withdrawals.
"""

import stripe
import os
from typing import Dict, Any, Optional
from decimal import Decimal
from fastapi import HTTPException
import structlog

# Configure Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY", "sk_test_...")

logger = structlog.get_logger()

class StripePaymentProcessor:
    """Handles Stripe payment operations"""
    
    def __init__(self):
        self.stripe_key = os.getenv("STRIPE_SECRET_KEY")
        self.webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
        
        if not self.stripe_key:
            logger.warning("Stripe secret key not configured")
    
    async def create_payment_intent(
        self, 
        amount: Decimal, 
        currency: str = "usd",
        user_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a Stripe payment intent for deposit"""
        
        try:
            # Convert amount to cents (Stripe requirement)
            amount_cents = int(amount * 100)
            
            # Create payment intent
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency=currency,
                metadata={
                    "user_id": user_id,
                    "service": "betbet-wallet",
                    **(metadata or {})
                },
                # Enable automatic payment methods
                automatic_payment_methods={
                    'enabled': True,
                },
            )
            
            logger.info(
                "Payment intent created",
                payment_intent_id=intent.id,
                amount=amount,
                user_id=user_id
            )
            
            return {
                "client_secret": intent.client_secret,
                "payment_intent_id": intent.id,
                "amount": amount,
                "currency": currency
            }
            
        except stripe.error.StripeError as e:
            logger.error("Stripe payment intent creation failed", error=str(e))
            raise HTTPException(
                status_code=400,
                detail=f"Payment processing error: {str(e)}"
            )
    
    async def confirm_payment_intent(self, payment_intent_id: str) -> Dict[str, Any]:
        """Confirm and retrieve payment intent status"""
        
        try:
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            return {
                "id": intent.id,
                "status": intent.status,
                "amount": intent.amount / 100,  # Convert back from cents
                "currency": intent.currency,
                "metadata": intent.metadata
            }
            
        except stripe.error.StripeError as e:
            logger.error("Failed to retrieve payment intent", error=str(e))
            raise HTTPException(
                status_code=400,
                detail=f"Payment verification error: {str(e)}"
            )
    
    async def create_refund(
        self, 
        payment_intent_id: str, 
        amount: Optional[Decimal] = None,
        reason: str = "requested_by_customer"
    ) -> Dict[str, Any]:
        """Create a refund for a payment"""
        
        try:
            refund_data = {
                "payment_intent": payment_intent_id,
                "reason": reason
            }
            
            if amount:
                refund_data["amount"] = int(amount * 100)  # Convert to cents
            
            refund = stripe.Refund.create(**refund_data)
            
            logger.info(
                "Refund created",
                refund_id=refund.id,
                payment_intent_id=payment_intent_id,
                amount=refund.amount / 100
            )
            
            return {
                "refund_id": refund.id,
                "status": refund.status,
                "amount": refund.amount / 100,
                "currency": refund.currency
            }
            
        except stripe.error.StripeError as e:
            logger.error("Refund creation failed", error=str(e))
            raise HTTPException(
                status_code=400,
                detail=f"Refund processing error: {str(e)}"
            )
    
    async def handle_webhook(self, payload: bytes, signature: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        
        if not self.webhook_secret:
            raise HTTPException(
                status_code=400,
                detail="Webhook secret not configured"
            )
        
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            logger.info("Stripe webhook received", event_type=event['type'])
            
            # Handle different event types
            if event['type'] == 'payment_intent.succeeded':
                return await self._handle_payment_succeeded(event['data']['object'])
            
            elif event['type'] == 'payment_intent.payment_failed':
                return await self._handle_payment_failed(event['data']['object'])
            
            elif event['type'] == 'charge.dispute.created':
                return await self._handle_dispute_created(event['data']['object'])
            
            else:
                logger.info("Unhandled webhook event type", event_type=event['type'])
                return {"status": "unhandled"}
            
        except ValueError as e:
            logger.error("Invalid webhook payload", error=str(e))
            raise HTTPException(status_code=400, detail="Invalid payload")
        
        except stripe.error.SignatureVerificationError as e:
            logger.error("Invalid webhook signature", error=str(e))
            raise HTTPException(status_code=400, detail="Invalid signature")
    
    async def _handle_payment_succeeded(self, payment_intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle successful payment webhook"""
        
        user_id = payment_intent.get('metadata', {}).get('user_id')
        amount = payment_intent['amount'] / 100
        
        logger.info(
            "Payment succeeded webhook",
            payment_intent_id=payment_intent['id'],
            user_id=user_id,
            amount=amount
        )
        
        # Here you would update the user's balance in the database
        # This is handled by your main wallet service
        
        return {
            "status": "payment_succeeded",
            "payment_intent_id": payment_intent['id'],
            "user_id": user_id,
            "amount": amount
        }
    
    async def _handle_payment_failed(self, payment_intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle failed payment webhook"""
        
        user_id = payment_intent.get('metadata', {}).get('user_id')
        
        logger.warning(
            "Payment failed webhook",
            payment_intent_id=payment_intent['id'],
            user_id=user_id
        )
        
        return {
            "status": "payment_failed",
            "payment_intent_id": payment_intent['id'],
            "user_id": user_id
        }
    
    async def _handle_dispute_created(self, charge: Dict[str, Any]) -> Dict[str, Any]:
        """Handle dispute creation webhook"""
        
        logger.warning(
            "Dispute created",
            charge_id=charge['id'],
            amount=charge['amount'] / 100
        )
        
        return {
            "status": "dispute_created",
            "charge_id": charge['id'],
            "amount": charge['amount'] / 100
        }

# Global processor instance
stripe_processor = StripePaymentProcessor()