import os
import sys
from pathlib import Path
from typing import List, Optional, Dict, Any
from decimal import Decimal
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum

from fastapi import FastAPI, HTTPException, Depends, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
from pydantic_settings import BaseSettings
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

# Import platform fees module
from platform_fees import router as platform_fees_router

# Add the services directory to the Python path to import shared modules
services_dir = Path(__file__).parent.parent
sys.path.insert(0, str(services_dir))

from shared.core.database.connection import initialize_database, get_database_write, DatabaseSettings, close_database

# Define wallet models matching existing schema
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class TransactionType(str, Enum):
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    BET = "bet"
    WIN = "win"
    LOSS = "loss"
    REFUND = "refund"
    FEE = "fee"
    BONUS = "bonus"

class UserBalance(Base):
    __tablename__ = "user_balances"
    __table_args__ = {"schema": "wallet"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(String(255), nullable=False, unique=True)  # Support Clerk user IDs
    currency = Column(String(10), default='USD')
    available_balance = Column(Numeric(15, 2), default=0.00)
    locked_balance = Column(Numeric(15, 2), default=0.00)
    total_deposited = Column(Numeric(15, 2), default=0.00)
    total_withdrawn = Column(Numeric(15, 2), default=0.00)
    lifetime_winnings = Column(Numeric(15, 2), default=0.00)
    lifetime_losses = Column(Numeric(15, 2), default=0.00)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class Transaction(Base):
    __tablename__ = "transactions"
    __table_args__ = {"schema": "wallet"}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(String(255), nullable=False)  # Support Clerk user IDs
    type = Column(String(50), nullable=False)
    amount = Column(Numeric(15, 2), nullable=False)
    currency = Column(String(10), default='USD')
    balance_before = Column(Numeric(15, 2), nullable=False)
    balance_after = Column(Numeric(15, 2), nullable=False)
    reference_type = Column(String(50))  # payment_intent, bet_id, game_id, etc.
    reference_id = Column(PostgresUUID(as_uuid=True))
    description = Column(Text)
    transaction_metadata = Column(JSONB)
    status = Column(String(20), default='completed')
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Pydantic models for API
class DepositRequest(BaseModel):
    amount: Decimal
    payment_method: str = "stripe"
    currency: str = "USD"
    return_url: Optional[str] = None

class WithdrawalRequest(BaseModel):
    amount: Decimal
    withdrawal_method: str = "bank_transfer"
    account_details: Dict[str, Any]

class BalanceResponse(BaseModel):
    user_id: str
    available_balance: Decimal
    locked_balance: Decimal
    total_balance: Decimal
    total_deposited: Decimal
    total_withdrawn: Decimal
    lifetime_winnings: Decimal
    lifetime_losses: Decimal
    currency: str

class TransactionResponse(BaseModel):
    id: str
    type: str
    amount: Decimal
    balance_before: Decimal
    balance_after: Decimal
    description: Optional[str]
    status: str
    created_at: datetime

class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
        "http://localhost:8000",
        "https://betbet.com",
        "https://admin.betbet.com"
    ]
    
    # Stripe Configuration
    STRIPE_SECRET_KEY: str = "sk_test_..."  # Add your Stripe key
    STRIPE_PUBLISHABLE_KEY: str = "pk_test_..."
    STRIPE_WEBHOOK_SECRET: str = "whsec_..."
    
    SERVICE_NAME: str = "wallet-service"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8007
    
    class Config:
        env_file = [".env.local", ".env"]
        extra = "ignore"

settings = Settings()

app = FastAPI(
    title="BetBet Wallet Service",
    description="Centralized wallet and payment management",
    version=settings.SERVICE_VERSION
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include platform fees router
app.include_router(platform_fees_router)

@app.on_event("startup")
async def startup_event():
    db_settings = DatabaseSettings()
    await initialize_database(db_settings)
    print(f"✅ Wallet Service connected to database")

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

@app.get("/")
def read_root():
    return {
        "service": "BetBet Wallet Service",
        "status": "running",
        "version": settings.SERVICE_VERSION,
        "database": "enabled",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "balance": "/api/v1/wallet/balance/{user_id}",
            "deposit": "/api/v1/wallet/deposit",
            "withdraw": "/api/v1/wallet/withdraw",
            "transactions": "/api/v1/wallet/transactions/{user_id}",
            "transfer": "/api/v1/wallet/transfer",
            "analytics": "/api/v1/wallet/analytics/overview"
        }
    }

@app.get("/health")
async def health_check():
    try:
        from shared.core.database.connection import _db_manager
        if _db_manager:
            db_health = await _db_manager.health_check()
            db_status = db_health.get('write_connection', False)
        else:
            db_status = False

        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.SERVICE_VERSION,
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.SERVICE_NAME,
            "error": str(e)
        }

@app.get("/api/v1/wallet/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get wallet analytics overview"""

    # Get total users count (from user balance records)
    total_users_query = select(func.count(func.distinct(UserBalance.user_id)))
    total_users_result = await db.execute(total_users_query)
    total_users = total_users_result.scalar() or 0

    # Get total balance across all user balances
    total_balance_query = select(func.coalesce(func.sum(UserBalance.available_balance + UserBalance.locked_balance), 0))
    total_balance_result = await db.execute(total_balance_query)
    total_balance = total_balance_result.scalar() or 0

    # Get today's transactions count
    from datetime import date
    today = date.today()
    transactions_today_query = select(func.count(Transaction.id)).where(
        func.date(Transaction.created_at) == today
    )
    transactions_today_result = await db.execute(transactions_today_query)
    transactions_today = transactions_today_result.scalar() or 0

    # Get today's volume
    volume_today_query = select(func.coalesce(func.sum(Transaction.amount), 0)).where(
        func.date(Transaction.created_at) == today
    )
    volume_today_result = await db.execute(volume_today_query)
    volume_today = volume_today_result.scalar() or 0

    return {
        "total_users": total_users,
        "total_balance": float(total_balance),
        "transactions_today": transactions_today,
        "volume_today": float(volume_today)
    }

async def get_or_create_balance(user_id: str, db: AsyncSession) -> UserBalance:
    """Get user balance or create if doesn't exist"""
    query = select(UserBalance).where(UserBalance.user_id == user_id)
    result = await db.execute(query)
    balance = result.scalar_one_or_none()
    
    if not balance:
        balance = UserBalance(user_id=user_id)
        db.add(balance)
        await db.commit()
        await db.refresh(balance)
    
    return balance

async def create_transaction(
    user_id: str,
    transaction_type: TransactionType,
    amount: Decimal,
    balance_before: Decimal,
    balance_after: Decimal,
    db: AsyncSession,
    reference_type: str = None,
    reference_id: UUID = None,
    description: str = None,
    metadata: dict = None
) -> Transaction:
    """Create a transaction record"""
    transaction = Transaction(
        user_id=user_id,
        type=transaction_type.value,
        amount=amount,
        currency="USD",
        balance_before=balance_before,
        balance_after=balance_after,
        reference_type=reference_type,
        reference_id=reference_id,
        description=description,
        transaction_metadata=metadata
    )
    
    db.add(transaction)
    await db.commit()
    await db.refresh(transaction)
    return transaction

@app.get("/api/v1/wallet/balance/{user_id}", response_model=BalanceResponse)
async def get_balance(user_id: str, db: AsyncSession = Depends(get_database_write)):
    """Get user's wallet balance"""
    
    balance = await get_or_create_balance(user_id, db)
    
    return BalanceResponse(
        user_id=str(balance.user_id),
        available_balance=balance.available_balance,
        locked_balance=balance.locked_balance,
        total_balance=balance.available_balance + balance.locked_balance,
        total_deposited=balance.total_deposited,
        total_withdrawn=balance.total_withdrawn,
        lifetime_winnings=balance.lifetime_winnings,
        lifetime_losses=balance.lifetime_losses,
        currency=balance.currency
    )

@app.post("/api/v1/wallet/create-payment-intent")
async def create_payment_intent(
    user_id: str,
    amount: Decimal = Body(...),
    currency: str = Body("USD"),
    db: AsyncSession = Depends(get_database_write)
):
    """Create Stripe payment intent for deposit"""
    
    try:
        from stripe_integration import stripe_processor
        
        payment_intent = await stripe_processor.create_payment_intent(
            amount=amount,
            currency=currency.lower(),
            user_id=user_id,
            metadata={
                "service": "betbet-wallet",
                "type": "deposit"
            }
        )
        
        return payment_intent
        
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to create payment intent: {str(e)}"
        )

@app.post("/api/v1/wallet/deposit")
async def create_deposit(
    user_id: str,
    deposit_request: DepositRequest,
    db: AsyncSession = Depends(get_database_write)
):
    """Create a deposit (integrates with Stripe)"""
    
    # Get current balance
    balance = await get_or_create_balance(user_id, db)
    
    if deposit_request.payment_method == "stripe":
        # For Stripe payments, create payment intent instead of immediate deposit
        try:
            from stripe_integration import stripe_processor
            
            payment_intent = await stripe_processor.create_payment_intent(
                amount=deposit_request.amount,
                currency=deposit_request.currency.lower(),
                user_id=user_id
            )
            
            return {
                "success": True,
                "requires_payment": True,
                "client_secret": payment_intent["client_secret"],
                "payment_intent_id": payment_intent["payment_intent_id"],
                "amount": deposit_request.amount,
                "message": "Payment intent created. Complete payment on frontend."
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Payment processing failed: {str(e)}"
            )
    
    else:
        # For other payment methods (demo/test), process immediately
        old_balance = balance.available_balance
        new_balance = old_balance + deposit_request.amount
        
        # Update balance
        balance.available_balance = new_balance
        balance.total_deposited += deposit_request.amount
        
        # Create transaction record
        transaction = await create_transaction(
            user_id=user_id,
            transaction_type=TransactionType.DEPOSIT,
            amount=deposit_request.amount,
            balance_before=old_balance,
            balance_after=new_balance,
            db=db,
            reference_type="deposit",
            description=f"Deposit via {deposit_request.payment_method}",
            metadata={
                "payment_method": deposit_request.payment_method,
                "currency": deposit_request.currency
            }
        )
        
        await db.commit()
        
        return {
            "success": True,
            "transaction_id": str(transaction.id),
            "amount": deposit_request.amount,
            "new_balance": new_balance,
            "message": "Deposit successful"
        }

@app.post("/api/v1/wallet/withdraw")
async def create_withdrawal(
    user_id: str,
    withdrawal_request: WithdrawalRequest,
    db: AsyncSession = Depends(get_database_write)
):
    """Create a withdrawal request"""
    
    balance = await get_or_create_balance(user_id, db)
    
    # Check sufficient funds
    if balance.available_balance < withdrawal_request.amount:
        raise HTTPException(
            status_code=400,
            detail="Insufficient funds"
        )
    
    old_balance = balance.available_balance
    new_balance = old_balance - withdrawal_request.amount
    
    # Update balance
    balance.available_balance = new_balance
    balance.total_withdrawn += withdrawal_request.amount
    
    # Create transaction record
    transaction = await create_transaction(
        user_id=user_id,
        transaction_type=TransactionType.WITHDRAWAL,
        amount=withdrawal_request.amount,
        balance_before=old_balance,
        balance_after=new_balance,
        db=db,
        reference_type="withdrawal",
        description=f"Withdrawal via {withdrawal_request.withdrawal_method}",
        metadata={
            "withdrawal_method": withdrawal_request.withdrawal_method,
            "account_details": withdrawal_request.account_details
        }
    )
    
    await db.commit()
    
    return {
        "success": True,
        "transaction_id": str(transaction.id),
        "amount": withdrawal_request.amount,
        "new_balance": new_balance,
        "message": "Withdrawal request created"
    }

@app.get("/api/v1/wallet/transactions/{user_id}")
async def get_transactions(
    user_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    transaction_type: Optional[str] = None,
    db: AsyncSession = Depends(get_database_write)
):
    """Get user's transaction history"""
    
    query = select(Transaction).where(Transaction.user_id == user_id)
    
    if transaction_type:
        query = query.where(Transaction.type == transaction_type)
    
    query = query.order_by(Transaction.created_at.desc())
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    transactions = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(Transaction.id)).where(Transaction.user_id == user_id)
    if transaction_type:
        count_query = count_query.where(Transaction.type == transaction_type)
    
    count_result = await db.execute(count_query)
    total_count = count_result.scalar()
    
    return {
        "transactions": [
            TransactionResponse(
                id=str(t.id),
                type=t.type,
                amount=t.amount,
                balance_before=t.balance_before,
                balance_after=t.balance_after,
                description=t.description,
                status=t.status,
                created_at=t.created_at
            ) for t in transactions
        ],
        "total_count": total_count,
        "page": page,
        "limit": limit,
        "has_next": (page * limit) < total_count,
        "has_prev": page > 1
    }

@app.post("/api/v1/wallet/transfer")
async def transfer_funds(
    from_user_id: str,
    to_user_id: str,
    amount: Decimal = Body(...),
    description: str = Body(None),
    db: AsyncSession = Depends(get_database_write)
):
    """Transfer funds between users"""
    
    if from_user_id == to_user_id:
        raise HTTPException(status_code=400, detail="Cannot transfer to yourself")
    
    # Get balances
    from_balance = await get_or_create_balance(UUID(from_user_id), db)
    to_balance = await get_or_create_balance(UUID(to_user_id), db)
    
    # Check sufficient funds
    if from_balance.available_balance < amount:
        raise HTTPException(status_code=400, detail="Insufficient funds")
    
    # Update balances
    old_from_balance = from_balance.available_balance
    old_to_balance = to_balance.available_balance
    
    from_balance.available_balance -= amount
    to_balance.available_balance += amount
    
    # Create transaction records
    from_transaction = await create_transaction(
        user_id=UUID(from_user_id),
        transaction_type=TransactionType.BET,  # Using BET as outgoing transfer
        amount=amount,
        balance_before=old_from_balance,
        balance_after=from_balance.available_balance,
        db=db,
        reference_type="transfer",
        reference_id=UUID(to_user_id),
        description=f"Transfer to user {to_user_id}: {description or 'No description'}"
    )
    
    to_transaction = await create_transaction(
        user_id=UUID(to_user_id),
        transaction_type=TransactionType.WIN,  # Using WIN as incoming transfer
        amount=amount,
        balance_before=old_to_balance,
        balance_after=to_balance.available_balance,
        db=db,
        reference_type="transfer",
        reference_id=UUID(from_user_id),
        description=f"Transfer from user {from_user_id}: {description or 'No description'}"
    )
    
    await db.commit()
    
    return {
        "success": True,
        "from_transaction_id": str(from_transaction.id),
        "to_transaction_id": str(to_transaction.id),
        "amount": amount,
        "message": "Transfer completed successfully"
    }

@app.post("/api/v1/wallet/lock-funds")
async def lock_funds(
    user_id: str,
    amount: Decimal = Body(...),
    reference_type: str = Body(...),
    reference_id: str = Body(...),
    description: str = Body(None),
    db: AsyncSession = Depends(get_database_write)
):
    """Lock funds for a bet or order"""
    
    balance = await get_or_create_balance(user_id, db)
    
    if balance.available_balance < amount:
        raise HTTPException(status_code=400, detail="Insufficient funds")
    
    # Move funds from available to locked
    balance.available_balance -= amount
    balance.locked_balance += amount
    
    await db.commit()
    
    return {
        "success": True,
        "locked_amount": amount,
        "available_balance": balance.available_balance,
        "locked_balance": balance.locked_balance
    }

@app.post("/api/v1/wallet/unlock-funds")
async def unlock_funds(
    user_id: str,
    amount: Decimal = Body(...),
    win_amount: Decimal = Body(0),
    reference_type: str = Body(...),
    reference_id: str = Body(...),
    description: str = Body(None),
    db: AsyncSession = Depends(get_database_write)
):
    """Unlock funds after bet settlement"""
    
    balance = await get_or_create_balance(user_id, db)
    
    if balance.locked_balance < amount:
        raise HTTPException(status_code=400, detail="Insufficient locked funds")
    
    old_balance = balance.available_balance
    
    # Unlock the original amount and add any winnings
    balance.locked_balance -= amount
    balance.available_balance += win_amount  # Only winnings go back (stake was locked)
    
    # Update lifetime stats
    if win_amount > amount:  # User won
        net_win = win_amount - amount
        balance.lifetime_winnings += net_win
    else:  # User lost
        net_loss = amount - win_amount
        balance.lifetime_losses += net_loss
    
    # Create transaction for the settlement
    if win_amount > 0:
        await create_transaction(
            user_id=user_id,
            transaction_type=TransactionType.WIN,
            amount=win_amount,
            balance_before=old_balance,
            balance_after=balance.available_balance,
            db=db,
            reference_type=reference_type,
            reference_id=UUID(reference_id),
            description=description or f"Settlement for {reference_type}"
        )
    
    await db.commit()
    
    return {
        "success": True,
        "unlocked_amount": amount,
        "win_amount": win_amount,
        "available_balance": balance.available_balance,
        "locked_balance": balance.locked_balance
    }

@app.get("/api/v1/wallet/analytics/overview")
async def get_analytics_overview(db: AsyncSession = Depends(get_database_write)):
    """Get wallet analytics overview"""
    
    # Total users with wallets
    total_users_query = select(func.count(func.distinct(UserBalance.user_id)))
    total_users_result = await db.execute(total_users_query)
    total_users = total_users_result.scalar() or 0
    
    # Total balance across all users
    total_balance_query = select(func.coalesce(func.sum(UserBalance.available_balance + UserBalance.locked_balance), 0))
    total_balance_result = await db.execute(total_balance_query)
    total_balance = total_balance_result.scalar() or 0
    
    # Total transactions today
    today = datetime.utcnow().date()
    transactions_today_query = select(func.count(Transaction.id)).where(
        Transaction.created_at >= today
    )
    transactions_today_result = await db.execute(transactions_today_query)
    transactions_today = transactions_today_result.scalar() or 0
    
    # Total transaction volume today
    volume_today_query = select(func.coalesce(func.sum(Transaction.amount), 0)).where(
        Transaction.created_at >= today
    )
    volume_today_result = await db.execute(volume_today_query)
    volume_today = volume_today_result.scalar() or 0
    
    return {
        "total_users": total_users,
        "total_balance": float(total_balance),
        "transactions_today": transactions_today,
        "volume_today": float(volume_today),
        "timestamp": datetime.utcnow().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", settings.SERVICE_PORT))
    uvicorn.run(app, host="0.0.0.0", port=port)