"""
BetBet Platform - Dynamic Fee Management System
==============================================

Handles platform fee configuration, promotional campaigns, and dynamic fee adjustments.
"""

import os
import json
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4
from enum import Enum

from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

# Add this to wallet service main.py imports
# from platform_fees import router as platform_fees_router

class FeeCategory(str, Enum):
    GAMING = "gaming"
    TRADING = "trading"
    SUBSCRIPTIONS = "subscriptions"
    WITHDRAWALS = "withdrawals"

class FeeType(str, Enum):
    STANDARD = "standard"
    PREMIUM = "premium"
    PROMOTIONAL = "promotional"
    HIGH_VOLUME = "high_volume"
    MAKER = "maker"
    TAKER = "taker"
    EXPERT = "expert"
    ENTERPRISE = "enterprise"

class CampaignStatus(str, Enum):
    ACTIVE = "active"
    SCHEDULED = "scheduled"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class FeeRule(BaseModel):
    """Platform fee configuration rule"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    category: FeeCategory
    fee_type: FeeType
    percentage: Decimal = Field(..., ge=0, le=100, description="Fee percentage (0-100)")
    minimum_fee: Optional[Decimal] = Field(None, ge=0, description="Minimum fee amount")
    maximum_fee: Optional[Decimal] = Field(None, ge=0, description="Maximum fee amount")
    
    # Conditional rules
    min_transaction_amount: Optional[Decimal] = None
    max_transaction_amount: Optional[Decimal] = None
    user_tier: Optional[str] = None
    volume_threshold: Optional[Decimal] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    is_active: bool = True

class PromotionalCampaign(BaseModel):
    """Promotional fee reduction campaign"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., max_length=500)
    
    # Campaign details
    fee_category: FeeCategory
    reduction_percentage: Decimal = Field(..., ge=0, le=100)
    
    # Timing
    start_date: datetime
    end_date: datetime
    
    # Eligibility
    eligible_users: str = Field(..., description="User eligibility criteria")
    max_uses: Optional[int] = None
    max_uses_per_user: Optional[int] = None
    
    # Current stats
    current_uses: int = 0
    total_savings: Decimal = Decimal('0')
    
    # Status
    status: CampaignStatus = CampaignStatus.SCHEDULED
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class FeeCalculationRequest(BaseModel):
    """Request to calculate fee for a transaction"""
    user_id: str
    category: FeeCategory
    fee_type: FeeType
    transaction_amount: Decimal
    user_tier: Optional[str] = None
    user_volume_30d: Optional[Decimal] = None

class FeeCalculationResponse(BaseModel):
    """Response with calculated fee"""
    base_percentage: Decimal
    applied_percentage: Decimal
    fee_amount: Decimal
    promotional_discount: Optional[Decimal] = None
    campaign_applied: Optional[str] = None
    volume_discount: Optional[Decimal] = None

class PlatformFeeManager:
    """Manages platform fee configuration and calculations"""
    
    def __init__(self):
        self.fee_rules: Dict[str, FeeRule] = {}
        self.campaigns: Dict[str, PromotionalCampaign] = {}
        self._load_default_fees()
    
    def _load_default_fees(self):
        """Load default fee structure"""
        default_fees = [
            FeeRule(
                category=FeeCategory.GAMING,
                fee_type=FeeType.STANDARD,
                percentage=Decimal('10.0'),
                minimum_fee=Decimal('0.10')
            ),
            FeeRule(
                category=FeeCategory.GAMING,
                fee_type=FeeType.PREMIUM,
                percentage=Decimal('8.0'),
                minimum_fee=Decimal('0.10')
            ),
            FeeRule(
                category=FeeCategory.GAMING,
                fee_type=FeeType.PROMOTIONAL,
                percentage=Decimal('5.0'),
                minimum_fee=Decimal('0.10')
            ),
            FeeRule(
                category=FeeCategory.GAMING,
                fee_type=FeeType.HIGH_VOLUME,
                percentage=Decimal('7.0'),
                minimum_fee=Decimal('0.10'),
                volume_threshold=Decimal('10000')
            ),
            FeeRule(
                category=FeeCategory.TRADING,
                fee_type=FeeType.MAKER,
                percentage=Decimal('0.3'),
                minimum_fee=Decimal('0.01')
            ),
            FeeRule(
                category=FeeCategory.TRADING,
                fee_type=FeeType.TAKER,
                percentage=Decimal('0.7'),
                minimum_fee=Decimal('0.01')
            ),
            FeeRule(
                category=FeeCategory.SUBSCRIPTIONS,
                fee_type=FeeType.EXPERT,
                percentage=Decimal('15.0'),
                minimum_fee=Decimal('0.50')
            ),
        ]
        
        for fee in default_fees:
            self.fee_rules[f"{fee.category}_{fee.fee_type}"] = fee
    
    async def calculate_fee(self, request: FeeCalculationRequest) -> FeeCalculationResponse:
        """Calculate fee for a transaction with all applicable discounts"""
        
        # Get base fee rule
        rule_key = f"{request.category}_{request.fee_type}"
        base_rule = self.fee_rules.get(rule_key)
        
        if not base_rule:
            raise ValueError(f"No fee rule found for {rule_key}")
        
        base_percentage = base_rule.percentage
        applied_percentage = base_percentage
        
        # Apply volume discounts
        volume_discount = None
        if request.user_volume_30d and base_rule.volume_threshold:
            if request.user_volume_30d >= base_rule.volume_threshold:
                volume_discount = Decimal('20.0')  # 20% discount for high volume
                applied_percentage = applied_percentage * (1 - volume_discount / 100)
        
        # Apply promotional campaigns
        promotional_discount = None
        campaign_applied = None
        active_campaigns = self._get_active_campaigns(request.category)
        
        for campaign in active_campaigns:
            if self._is_user_eligible(campaign, request.user_id):
                if promotional_discount is None or campaign.reduction_percentage > promotional_discount:
                    promotional_discount = campaign.reduction_percentage
                    campaign_applied = campaign.name
        
        if promotional_discount:
            applied_percentage = applied_percentage * (1 - promotional_discount / 100)
        
        # Calculate final fee amount
        fee_amount = (request.transaction_amount * applied_percentage / 100)
        
        # Apply minimum/maximum limits
        if base_rule.minimum_fee and fee_amount < base_rule.minimum_fee:
            fee_amount = base_rule.minimum_fee
        if base_rule.maximum_fee and fee_amount > base_rule.maximum_fee:
            fee_amount = base_rule.maximum_fee
        
        return FeeCalculationResponse(
            base_percentage=base_percentage,
            applied_percentage=applied_percentage,
            fee_amount=fee_amount,
            promotional_discount=promotional_discount,
            campaign_applied=campaign_applied,
            volume_discount=volume_discount
        )
    
    def _get_active_campaigns(self, category: FeeCategory) -> List[PromotionalCampaign]:
        """Get active campaigns for a fee category"""
        now = datetime.now(timezone.utc)
        return [
            campaign for campaign in self.campaigns.values()
            if (campaign.fee_category == category and 
                campaign.status == CampaignStatus.ACTIVE and
                campaign.start_date <= now <= campaign.end_date and
                (campaign.max_uses is None or campaign.current_uses < campaign.max_uses))
        ]
    
    def _is_user_eligible(self, campaign: PromotionalCampaign, user_id: str) -> bool:
        """Check if user is eligible for a campaign"""
        # This would implement complex eligibility logic
        # For now, simple implementation
        
        if campaign.eligible_users == "all_users":
            return True
        elif campaign.eligible_users == "new_users":
            # Check if user registered in last 30 days
            return True  # Simplified
        elif campaign.eligible_users == "high_volume":
            # Check if user has high volume
            return True  # Simplified
        
        return False
    
    async def update_fee_rule(self, rule: FeeRule) -> FeeRule:
        """Update or create a fee rule"""
        rule.updated_at = datetime.now(timezone.utc)
        rule_key = f"{rule.category}_{rule.fee_type}"
        self.fee_rules[rule_key] = rule
        
        # TODO: Save to database
        return rule
    
    async def create_campaign(self, campaign: PromotionalCampaign) -> PromotionalCampaign:
        """Create a new promotional campaign"""
        self.campaigns[campaign.id] = campaign
        
        # TODO: Save to database
        return campaign
    
    async def get_all_fees(self) -> Dict[str, Dict[str, Any]]:
        """Get all fee rules organized by category"""
        result = {}
        
        for rule_key, rule in self.fee_rules.items():
            if rule.category not in result:
                result[rule.category] = {}
            
            result[rule.category][rule.fee_type] = {
                "percentage": float(rule.percentage),
                "minimum_fee": float(rule.minimum_fee) if rule.minimum_fee else None,
                "maximum_fee": float(rule.maximum_fee) if rule.maximum_fee else None,
                "volume_threshold": float(rule.volume_threshold) if rule.volume_threshold else None,
                "is_active": rule.is_active,
                "updated_at": rule.updated_at.isoformat()
            }
        
        return result
    
    async def get_campaigns(self) -> List[Dict[str, Any]]:
        """Get all promotional campaigns"""
        return [
            {
                "id": campaign.id,
                "name": campaign.name,
                "description": campaign.description,
                "fee_category": campaign.fee_category,
                "reduction_percentage": float(campaign.reduction_percentage),
                "start_date": campaign.start_date.isoformat(),
                "end_date": campaign.end_date.isoformat(),
                "status": campaign.status,
                "current_uses": campaign.current_uses,
                "max_uses": campaign.max_uses,
                "total_savings": float(campaign.total_savings)
            }
            for campaign in self.campaigns.values()
        ]

# Global fee manager instance
fee_manager = PlatformFeeManager()

# FastAPI router
router = APIRouter(prefix="/api/v1/platform-fees", tags=["platform-fees"])

@router.get("/")
async def get_fee_structure():
    """Get current platform fee structure"""
    fees = await fee_manager.get_all_fees()
    campaigns = await fee_manager.get_campaigns()
    
    return {
        "fees": fees,
        "campaigns": campaigns,
        "last_updated": datetime.now(timezone.utc).isoformat()
    }

@router.post("/calculate")
async def calculate_transaction_fee(request: FeeCalculationRequest):
    """Calculate fee for a specific transaction"""
    try:
        result = await fee_manager.calculate_fee(request)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/fees/{category}/{fee_type}")
async def update_fee_rule(
    category: FeeCategory,
    fee_type: FeeType,
    percentage: Decimal = Body(..., ge=0, le=100),
    minimum_fee: Optional[Decimal] = Body(None, ge=0),
    maximum_fee: Optional[Decimal] = Body(None, ge=0),
    volume_threshold: Optional[Decimal] = Body(None, ge=0)
):
    """Update a specific fee rule"""
    
    rule = FeeRule(
        category=category,
        fee_type=fee_type,
        percentage=percentage,
        minimum_fee=minimum_fee,
        maximum_fee=maximum_fee,
        volume_threshold=volume_threshold
    )
    
    updated_rule = await fee_manager.update_fee_rule(rule)
    
    return {
        "success": True,
        "rule": {
            "category": updated_rule.category,
            "fee_type": updated_rule.fee_type,
            "percentage": float(updated_rule.percentage),
            "minimum_fee": float(updated_rule.minimum_fee) if updated_rule.minimum_fee else None,
            "maximum_fee": float(updated_rule.maximum_fee) if updated_rule.maximum_fee else None,
            "updated_at": updated_rule.updated_at.isoformat()
        }
    }

@router.post("/campaigns")
async def create_promotional_campaign(campaign: PromotionalCampaign):
    """Create a new promotional campaign"""
    
    created_campaign = await fee_manager.create_campaign(campaign)
    
    return {
        "success": True,
        "campaign": {
            "id": created_campaign.id,
            "name": created_campaign.name,
            "description": created_campaign.description,
            "reduction_percentage": float(created_campaign.reduction_percentage),
            "start_date": created_campaign.start_date.isoformat(),
            "end_date": created_campaign.end_date.isoformat(),
            "status": created_campaign.status
        }
    }

@router.get("/analytics/revenue-impact")
async def get_revenue_impact():
    """Get analytics on fee changes and revenue impact"""
    
    # Mock analytics data
    return {
        "current_daily_revenue": 45200.75,
        "projected_monthly": 1356022.50,
        "fee_breakdown": {
            "gaming": {"revenue": 28500.00, "percentage": 63.1},
            "trading": {"revenue": 12800.00, "percentage": 28.3},
            "subscriptions": {"revenue": 3900.75, "percentage": 8.6}
        },
        "promotional_impact": {
            "total_discounts_given": 5600.25,
            "additional_volume_generated": 45000.00,
            "net_revenue_impact": 2800.50
        },
        "optimization_suggestions": [
            {
                "category": "gaming",
                "current_rate": 10.0,
                "suggested_rate": 9.5,
                "projected_volume_increase": "12%",
                "projected_revenue_impact": "+$2,400/month"
            }
        ]
    }

@router.get("/health")
async def fee_service_health():
    """Health check for fee management service"""
    return {
        "status": "healthy",
        "fee_rules_loaded": len(fee_manager.fee_rules),
        "active_campaigns": len([c for c in fee_manager.campaigns.values() if c.status == CampaignStatus.ACTIVE]),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }