-- BetBet Odds Exchange/Trading Database Schema
-- ============================================

\c betbet_trading;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Trading instruments (what can be traded)
CREATE TABLE instruments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('odds', 'spread', 'total', 'prop')),
    underlying_event_id UUID, -- References betting event
    underlying_market_id UUID, -- References betting market
    description TEXT,
    min_price DECIMAL(8,4) DEFAULT 0.0001,
    max_price DECIMAL(8,4) DEFAULT 1000.0000,
    tick_size DECIMAL(8,4) DEFAULT 0.0001,
    min_quantity DECIMAL(10,2) DEFAULT 1.00,
    max_quantity DECIMAL(15,2) DEFAULT 100000.00,
    is_active BOOLEAN DEFAULT true,
    expiry_time TIMESTAMP WITH TIME ZONE,
    settlement_price DECIMAL(8,4),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User trading accounts
CREATE TABLE trading_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE, -- References users.id from main DB
    balance DECIMAL(15,2) DEFAULT 0.00,
    reserved_balance DECIMAL(15,2) DEFAULT 0.00, -- Locked in open orders
    lifetime_pnl DECIMAL(15,2) DEFAULT 0.00,
    total_traded_volume DECIMAL(20,2) DEFAULT 0.00,
    trade_count INTEGER DEFAULT 0,
    win_count INTEGER DEFAULT 0,
    loss_count INTEGER DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0.0000,
    max_drawdown DECIMAL(15,2) DEFAULT 0.00,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0.0000,
    risk_level VARCHAR(20) DEFAULT 'medium' CHECK (risk_level IN ('low', 'medium', 'high', 'extreme')),
    daily_limit DECIMAL(15,2) DEFAULT 10000.00,
    position_limit DECIMAL(15,2) DEFAULT 50000.00,
    is_active BOOLEAN DEFAULT true,
    last_trade_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Orders (buy/sell orders in the exchange)
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    trading_account_id UUID NOT NULL REFERENCES trading_accounts(id),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    order_type VARCHAR(20) NOT NULL CHECK (order_type IN ('market', 'limit', 'stop', 'stop_limit')),
    side VARCHAR(10) NOT NULL CHECK (side IN ('buy', 'sell')),
    quantity DECIMAL(15,2) NOT NULL,
    price DECIMAL(8,4), -- NULL for market orders
    stop_price DECIMAL(8,4), -- For stop orders
    filled_quantity DECIMAL(15,2) DEFAULT 0.00,
    remaining_quantity DECIMAL(15,2) NOT NULL,
    average_fill_price DECIMAL(8,4) DEFAULT 0.0000,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'partially_filled', 'filled', 'cancelled', 'rejected')),
    time_in_force VARCHAR(20) DEFAULT 'gtc' CHECK (time_in_force IN ('gtc', 'ioc', 'fok', 'day')), -- Good Till Cancelled, Immediate or Cancel, Fill or Kill, Day
    post_only BOOLEAN DEFAULT false,
    reduce_only BOOLEAN DEFAULT false,
    client_order_id VARCHAR(100),
    reject_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trades/executions (matched orders)
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    buyer_order_id UUID NOT NULL REFERENCES orders(id),
    seller_order_id UUID NOT NULL REFERENCES orders(id),
    buyer_user_id UUID NOT NULL,
    seller_user_id UUID NOT NULL,
    quantity DECIMAL(15,2) NOT NULL,
    price DECIMAL(8,4) NOT NULL,
    value DECIMAL(20,2) NOT NULL, -- quantity * price
    buyer_fee DECIMAL(10,2) DEFAULT 0.00,
    seller_fee DECIMAL(10,2) DEFAULT 0.00,
    is_buyer_maker BOOLEAN NOT NULL, -- TRUE if buyer was maker, FALSE if taker
    sequence_number BIGSERIAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User positions (current holdings)
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    trading_account_id UUID NOT NULL REFERENCES trading_accounts(id),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    side VARCHAR(10) NOT NULL CHECK (side IN ('long', 'short')),
    quantity DECIMAL(15,2) NOT NULL,
    average_price DECIMAL(8,4) NOT NULL,
    market_value DECIMAL(20,2) NOT NULL,
    unrealized_pnl DECIMAL(15,2) DEFAULT 0.00,
    realized_pnl DECIMAL(15,2) DEFAULT 0.00,
    opened_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, instrument_id)
);

-- Order book (current market depth)
CREATE TABLE order_book (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    side VARCHAR(10) NOT NULL CHECK (side IN ('buy', 'sell')),
    price DECIMAL(8,4) NOT NULL,
    quantity DECIMAL(15,2) NOT NULL,
    order_count INTEGER NOT NULL DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instrument_id, side, price)
);

-- Market data (price history and statistics)
CREATE TABLE market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    interval_type VARCHAR(10) NOT NULL CHECK (interval_type IN ('1m', '5m', '15m', '1h', '4h', '1d')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    open_price DECIMAL(8,4) NOT NULL,
    high_price DECIMAL(8,4) NOT NULL,
    low_price DECIMAL(8,4) NOT NULL,
    close_price DECIMAL(8,4) NOT NULL,
    volume DECIMAL(20,2) DEFAULT 0.00,
    trade_count INTEGER DEFAULT 0,
    vwap DECIMAL(8,4), -- Volume Weighted Average Price
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instrument_id, interval_type, timestamp)
);

-- Portfolio history (daily snapshots)
CREATE TABLE portfolio_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    trading_account_id UUID NOT NULL REFERENCES trading_accounts(id),
    date DATE NOT NULL,
    balance DECIMAL(15,2) NOT NULL,
    equity DECIMAL(15,2) NOT NULL, -- balance + unrealized PnL
    daily_pnl DECIMAL(15,2) DEFAULT 0.00,
    total_volume DECIMAL(20,2) DEFAULT 0.00,
    trade_count INTEGER DEFAULT 0,
    win_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, date)
);

-- Risk management rules
CREATE TABLE risk_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('position_limit', 'daily_loss_limit', 'concentration_limit', 'volatility_limit')),
    parameters JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    applies_to VARCHAR(20) DEFAULT 'all' CHECK (applies_to IN ('all', 'user', 'instrument')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User-specific risk limits
CREATE TABLE user_risk_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    trading_account_id UUID NOT NULL REFERENCES trading_accounts(id),
    risk_rule_id UUID NOT NULL REFERENCES risk_rules(id),
    limit_value DECIMAL(15,2) NOT NULL,
    current_exposure DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, risk_rule_id)
);

-- Indexes
CREATE INDEX idx_instruments_type ON instruments(type);
CREATE INDEX idx_instruments_is_active ON instruments(is_active);
CREATE INDEX idx_instruments_expiry_time ON instruments(expiry_time);

CREATE INDEX idx_trading_accounts_user_id ON trading_accounts(user_id);
CREATE INDEX idx_trading_accounts_is_active ON trading_accounts(is_active);

CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_instrument_id ON orders(instrument_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_side ON orders(side);
CREATE INDEX idx_orders_created_at ON orders(created_at);

CREATE INDEX idx_trades_instrument_id ON trades(instrument_id);
CREATE INDEX idx_trades_buyer_user_id ON trades(buyer_user_id);
CREATE INDEX idx_trades_seller_user_id ON trades(seller_user_id);
CREATE INDEX idx_trades_created_at ON trades(created_at);
CREATE INDEX idx_trades_sequence_number ON trades(sequence_number);

CREATE INDEX idx_positions_user_id ON positions(user_id);
CREATE INDEX idx_positions_instrument_id ON positions(instrument_id);

CREATE INDEX idx_order_book_instrument_id ON order_book(instrument_id);
CREATE INDEX idx_order_book_side ON order_book(side);
CREATE INDEX idx_order_book_price ON order_book(price);

CREATE INDEX idx_market_data_instrument_id ON market_data(instrument_id);
CREATE INDEX idx_market_data_interval_type ON market_data(interval_type);
CREATE INDEX idx_market_data_timestamp ON market_data(timestamp);

CREATE INDEX idx_portfolio_history_user_id ON portfolio_history(user_id);
CREATE INDEX idx_portfolio_history_date ON portfolio_history(date);

CREATE INDEX idx_user_risk_limits_user_id ON user_risk_limits(user_id);
CREATE INDEX idx_user_risk_limits_risk_rule_id ON user_risk_limits(risk_rule_id);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_instruments_updated_at 
    BEFORE UPDATE ON instruments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trading_accounts_updated_at 
    BEFORE UPDATE ON trading_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at 
    BEFORE UPDATE ON positions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_risk_rules_updated_at 
    BEFORE UPDATE ON risk_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_risk_limits_updated_at 
    BEFORE UPDATE ON user_risk_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();