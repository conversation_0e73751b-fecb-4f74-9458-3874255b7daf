-- BetBet Betting Service Database Schema
-- =====================================

\c betbet_betting;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Sports and leagues
CREATE TABLE sports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon_url TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE leagues (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sport_id UUID NOT NULL REFERENCES sports(id),
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL,
    country VARCHAR(100),
    level INTEGER DEFAULT 1, -- 1=top tier, 2=second tier, etc.
    is_active BOOLEAN DEFAULT true,
    external_id VARCHAR(100), -- For API integrations
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(sport_id, slug)
);

-- Teams/competitors
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    short_name VARCHAR(50),
    slug VARCHAR(200) NOT NULL UNIQUE,
    sport_id UUID NOT NULL REFERENCES sports(id),
    country VARCHAR(100),
    founded_year INTEGER,
    logo_url TEXT,
    is_active BOOLEAN DEFAULT true,
    external_id VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Events/matches
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sport_id UUID NOT NULL REFERENCES sports(id),
    league_id UUID REFERENCES leagues(id),
    home_team_id UUID REFERENCES teams(id),
    away_team_id UUID REFERENCES teams(id),
    name VARCHAR(300) NOT NULL,
    event_type VARCHAR(50) DEFAULT 'match' CHECK (event_type IN ('match', 'race', 'fight', 'tournament', 'outright')),
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'finished', 'postponed', 'cancelled')),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    venue VARCHAR(200),
    round VARCHAR(50),
    week INTEGER,
    season VARCHAR(50),
    home_score INTEGER,
    away_score INTEGER,
    result JSONB, -- Detailed match result
    external_id VARCHAR(100),
    live_data JSONB, -- Real-time match data
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Betting markets (different types of bets for events)
CREATE TABLE markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    market_type VARCHAR(100) NOT NULL, -- '1x2', 'over_under', 'handicap', 'correct_score', etc.
    description TEXT,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'settled', 'cancelled')),
    settlement_time TIMESTAMP WITH TIME ZONE,
    winning_selection VARCHAR(200),
    total_volume DECIMAL(15,2) DEFAULT 0.00,
    total_matched DECIMAL(15,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.0500, -- 5% commission
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Market selections/outcomes
CREATE TABLE market_selections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES markets(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    selection_type VARCHAR(50), -- 'home', 'draw', 'away', 'over', 'under', etc.
    odds DECIMAL(8,2) NOT NULL DEFAULT 1.00,
    probability DECIMAL(5,4), -- Implied probability
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'winner', 'loser')),
    total_volume DECIMAL(15,2) DEFAULT 0.00,
    back_volume DECIMAL(15,2) DEFAULT 0.00,
    lay_volume DECIMAL(15,2) DEFAULT 0.00,
    sort_order INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Custom betting markets (user-created markets)
CREATE TABLE custom_markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_user_id UUID NOT NULL, -- References users.id from main DB
    title VARCHAR(300) NOT NULL,
    description TEXT NOT NULL,
    event_criteria TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'other',
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'active', 'settling', 'settled', 'cancelled', 'disputed')),
    settlement_type VARCHAR(50) DEFAULT 'manual' CHECK (settlement_type IN ('manual', 'oracle', 'consensus')),
    min_stake DECIMAL(10,2) DEFAULT 1.00,
    max_stake DECIMAL(10,2) DEFAULT 1000.00,
    total_stakes DECIMAL(15,2) DEFAULT 0.00,
    total_participants INTEGER DEFAULT 0,
    commission_rate DECIMAL(5,4) DEFAULT 0.0200,
    creator_fee DECIMAL(5,4) DEFAULT 0.0100,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    verification_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    settlement_time TIMESTAMP WITH TIME ZONE,
    winning_outcome_id UUID,
    settlement_notes TEXT,
    dispute_count INTEGER DEFAULT 0,
    evidence_urls TEXT[],
    tags VARCHAR(50)[],
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Custom market outcomes
CREATE TABLE custom_market_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES custom_markets(id) ON DELETE CASCADE,
    outcome_text VARCHAR(300) NOT NULL,
    description TEXT,
    current_odds DECIMAL(8,2) DEFAULT 1.00,
    total_stakes DECIMAL(15,2) DEFAULT 0.00,
    participant_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Bets placed by users
CREATE TABLE bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    market_id UUID REFERENCES markets(id),
    custom_market_id UUID REFERENCES custom_markets(id),
    selection_id UUID REFERENCES market_selections(id),
    custom_outcome_id UUID REFERENCES custom_market_outcomes(id),
    bet_type VARCHAR(50) DEFAULT 'single' CHECK (bet_type IN ('single', 'multiple', 'system', 'custom')),
    position_type VARCHAR(20) DEFAULT 'back' CHECK (position_type IN ('back', 'lay')),
    stake DECIMAL(10,2) NOT NULL,
    odds DECIMAL(8,2) NOT NULL,
    potential_win DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'matched', 'won', 'lost', 'void', 'cancelled')),
    matched_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE,
    settlement_amount DECIMAL(10,2) DEFAULT 0.00,
    is_live BOOLEAN DEFAULT false,
    ip_address INET,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Multiple/combo bets (accumulators)
CREATE TABLE multiple_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    bet_type VARCHAR(50) NOT NULL CHECK (bet_type IN ('accumulator', 'system', 'combo')),
    total_stake DECIMAL(10,2) NOT NULL,
    total_odds DECIMAL(10,2) NOT NULL,
    potential_win DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'void', 'cancelled')),
    legs_count INTEGER NOT NULL,
    legs_won INTEGER DEFAULT 0,
    legs_lost INTEGER DEFAULT 0,
    settlement_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Individual legs of multiple bets
CREATE TABLE multiple_bet_legs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    multiple_bet_id UUID NOT NULL REFERENCES multiple_bets(id) ON DELETE CASCADE,
    bet_id UUID NOT NULL REFERENCES bets(id),
    odds DECIMAL(8,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'void', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Betting statistics (daily aggregates)
CREATE TABLE betting_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    sport_id UUID REFERENCES sports(id),
    total_bets INTEGER DEFAULT 0,
    total_stakes DECIMAL(20,2) DEFAULT 0.00,
    total_payouts DECIMAL(20,2) DEFAULT 0.00,
    gross_profit DECIMAL(20,2) DEFAULT 0.00,
    unique_bettors INTEGER DEFAULT 0,
    average_stake DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, sport_id)
);

-- Indexes
CREATE INDEX idx_leagues_sport_id ON leagues(sport_id);
CREATE INDEX idx_teams_sport_id ON teams(sport_id);
CREATE INDEX idx_events_sport_id ON events(sport_id);
CREATE INDEX idx_events_league_id ON events(league_id);
CREATE INDEX idx_events_start_time ON events(start_time);
CREATE INDEX idx_events_status ON events(status);

CREATE INDEX idx_markets_event_id ON markets(event_id);
CREATE INDEX idx_markets_status ON markets(status);
CREATE INDEX idx_markets_market_type ON markets(market_type);

CREATE INDEX idx_market_selections_market_id ON market_selections(market_id);
CREATE INDEX idx_market_selections_status ON market_selections(status);

CREATE INDEX idx_custom_markets_creator_user_id ON custom_markets(creator_user_id);
CREATE INDEX idx_custom_markets_status ON custom_markets(status);
CREATE INDEX idx_custom_markets_category ON custom_markets(category);
CREATE INDEX idx_custom_markets_deadline ON custom_markets(deadline);

CREATE INDEX idx_custom_market_outcomes_market_id ON custom_market_outcomes(market_id);

CREATE INDEX idx_bets_user_id ON bets(user_id);
CREATE INDEX idx_bets_market_id ON bets(market_id);
CREATE INDEX idx_bets_custom_market_id ON bets(custom_market_id);
CREATE INDEX idx_bets_status ON bets(status);
CREATE INDEX idx_bets_created_at ON bets(created_at);

CREATE INDEX idx_multiple_bets_user_id ON multiple_bets(user_id);
CREATE INDEX idx_multiple_bets_status ON multiple_bets(status);

CREATE INDEX idx_multiple_bet_legs_multiple_bet_id ON multiple_bet_legs(multiple_bet_id);
CREATE INDEX idx_multiple_bet_legs_bet_id ON multiple_bet_legs(bet_id);

CREATE INDEX idx_betting_statistics_date ON betting_statistics(date);
CREATE INDEX idx_betting_statistics_sport_id ON betting_statistics(sport_id);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_events_updated_at 
    BEFORE UPDATE ON events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_markets_updated_at 
    BEFORE UPDATE ON markets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_market_selections_updated_at 
    BEFORE UPDATE ON market_selections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_markets_updated_at 
    BEFORE UPDATE ON custom_markets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_multiple_bets_updated_at 
    BEFORE UPDATE ON multiple_bets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();