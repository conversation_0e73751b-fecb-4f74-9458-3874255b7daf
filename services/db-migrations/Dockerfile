FROM python:3.11-alpine

# Install postgresql-client for psql commands
RUN apk add --no-cache postgresql-client curl

WORKDIR /migrations

# Install Python dependencies for migration tools
RUN pip install --no-cache-dir psycopg2-binary python-dotenv

# Copy migration scripts and SQL files
COPY . .

# Make migration script executable before switching users
RUN chmod +x run-migrations.sh

# Create non-root user and change ownership
RUN adduser -D -s /bin/sh migration-user && \
    chown -R migration-user:migration-user /migrations

USER migration-user

CMD ["./run-migrations.sh"]