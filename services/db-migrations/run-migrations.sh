#!/bin/bash

# Database Migration Runner for BetBet Platform
# This script automatically applies database migrations in the correct order

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Wait for PostgreSQL to be ready
log "Waiting for PostgreSQL to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if pg_isready -h "$POSTGRES_HOST" -p "5432" -U "$POSTGRES_USER" -d "$POSTGRES_DB"; then
        success "PostgreSQL is ready"
        break
    fi
    
    log "PostgreSQL not ready, waiting... (attempt $((attempt + 1))/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

if [ $attempt -eq $max_attempts ]; then
    error "PostgreSQL did not become ready within expected time"
fi

# Database connection parameters
export PGHOST="$POSTGRES_HOST"
export PGPORT="5432"
export PGUSER="$POSTGRES_USER"
export PGPASSWORD="$POSTGRES_PASSWORD"
export PGDATABASE="$POSTGRES_DB"

# Create migration tracking table if it doesn't exist
log "Creating migration tracking table..."
psql -c "
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);"

# Function to check if migration has been applied
migration_applied() {
    local version="$1"
    local count=$(psql -t -c "SELECT COUNT(*) FROM schema_migrations WHERE version = '$version';" | tr -d ' ')
    [ "$count" -gt 0 ]
}

# Function to apply a migration
apply_migration() {
    local migration_file="$1"
    local version=$(basename "$migration_file" .sql)
    local description="${version#*_}"  # Remove version number prefix
    
    if migration_applied "$version"; then
        log "Migration $version already applied, skipping"
        return 0
    fi
    
    log "Applying migration: $version"
    
    # Begin transaction
    psql -c "BEGIN;"
    
    # Apply migration
    if psql -f "$migration_file"; then
        # Record successful migration
        psql -c "INSERT INTO schema_migrations (version, description) VALUES ('$version', '$description');"
        psql -c "COMMIT;"
        success "Migration $version applied successfully"
    else
        psql -c "ROLLBACK;"
        error "Migration $version failed"
    fi
}

# Apply database initialization scripts
log "Applying database initialization scripts..."
if [ -d "/migrations/init" ]; then
    for init_file in /migrations/init/*.sql; do
        if [ -f "$init_file" ]; then
            log "Applying init script: $(basename "$init_file")"
            psql -f "$init_file" || warning "Init script $(basename "$init_file") failed"
        fi
    done
fi

# Apply migration files in version order
log "Applying migration files..."
if [ -d "/migrations/versions" ]; then
    # Sort migration files by version number
    for migration_file in $(find /migrations/versions -name "*.sql" | sort); do
        apply_migration "$migration_file"
    done
else
    warning "No migration versions directory found"
fi

# Apply consolidated service migrations
log "Applying consolidated service migrations..."
if [ -d "/migrations/service-schemas" ]; then
    # Sort service migration files by name (001_, 002_, etc.)
    for migration_file in $(find /migrations/service-schemas -name "*.sql" | sort); do
        apply_migration "$migration_file"
    done
else
    warning "No service-schemas directory found"
fi

# Apply service-specific schemas (legacy support - will be deprecated)
log "Applying legacy service schemas..."
services=("gaming-engine" "betting-service" "odds-exchange" "expert-analysis" "sports-analysis" "leaderboards-service")

for service in "${services[@]}"; do
    schema_file="/migrations/schemas/${service}.sql"
    if [ -f "$schema_file" ]; then
        log "Applying schema for $service"
        version="schema_${service}"
        
        if ! migration_applied "$version"; then
            psql -c "BEGIN;"
            if psql -f "$schema_file"; then
                psql -c "INSERT INTO schema_migrations (version, description) VALUES ('$version', 'Schema for $service');"
                psql -c "COMMIT;"
                success "Schema for $service applied successfully"
            else
                psql -c "ROLLBACK;"
                error "Schema for $service failed"
            fi
        else
            log "Schema for $service already applied"
        fi
    else
        warning "Schema file for $service not found: $schema_file"
    fi
done

# Show migration status
log "Migration status:"
psql -c "SELECT version, applied_at, description FROM schema_migrations ORDER BY applied_at;"

success "All migrations completed successfully"