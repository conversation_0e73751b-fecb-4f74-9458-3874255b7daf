-- BetBet Gaming Engine Database Schema
-- ====================================

\c betbet_gaming;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Game categories
CREATE TABLE game_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Games
CREATE TABLE games (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES game_categories(id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    rules JSONB,
    min_players INTEGER DEFAULT 1,
    max_players INTEGER DEFAULT 1,
    min_bet DECIMAL(10,2) DEFAULT 1.00,
    max_bet DECIMAL(10,2) DEFAULT 1000.00,
    house_edge DECIMAL(5,4) DEFAULT 0.0200,
    rtp DECIMAL(5,4) DEFAULT 0.9800, -- Return to Player
    volatility VARCHAR(20) DEFAULT 'medium' CHECK (volatility IN ('low', 'medium', 'high')),
    game_type VARCHAR(50) DEFAULT 'slot' CHECK (game_type IN ('slot', 'table', 'live', 'instant', 'tournament')),
    provider VARCHAR(100),
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Game sessions (individual gameplay sessions)
CREATE TABLE game_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    game_id UUID NOT NULL REFERENCES games(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'abandoned')),
    balance_start DECIMAL(15,2) NOT NULL,
    balance_current DECIMAL(15,2) NOT NULL,
    total_wagered DECIMAL(15,2) DEFAULT 0.00,
    total_won DECIMAL(15,2) DEFAULT 0.00,
    rounds_played INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Individual game rounds/spins
CREATE TABLE game_rounds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES game_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    game_id UUID NOT NULL REFERENCES games(id),
    round_number INTEGER NOT NULL,
    bet_amount DECIMAL(10,2) NOT NULL,
    win_amount DECIMAL(10,2) DEFAULT 0.00,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    game_state JSONB, -- Game-specific state (reels, cards, etc.)
    result JSONB, -- Game result details
    multiplier DECIMAL(8,4) DEFAULT 0.0000,
    is_jackpot BOOLEAN DEFAULT false,
    is_bonus BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id, round_number)
);

-- Tournaments
CREATE TABLE tournaments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    game_id UUID NOT NULL REFERENCES games(id),
    type VARCHAR(50) DEFAULT 'scheduled' CHECK (type IN ('scheduled', 'sit_go', 'freeroll')),
    status VARCHAR(50) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'registering', 'active', 'completed', 'cancelled')),
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    guaranteed_prize DECIMAL(15,2) DEFAULT 0.00,
    max_participants INTEGER,
    min_participants INTEGER DEFAULT 1,
    current_participants INTEGER DEFAULT 0,
    registration_start TIMESTAMP WITH TIME ZONE,
    registration_end TIMESTAMP WITH TIME ZONE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    rules JSONB DEFAULT '{}',
    prize_structure JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tournament participants
CREATE TABLE tournament_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    position INTEGER,
    score DECIMAL(15,2) DEFAULT 0.00,
    prize_won DECIMAL(10,2) DEFAULT 0.00,
    rounds_played INTEGER DEFAULT 0,
    total_wagered DECIMAL(15,2) DEFAULT 0.00,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    eliminated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(tournament_id, user_id)
);

-- Jackpots
CREATE TABLE jackpots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50) DEFAULT 'progressive' CHECK (type IN ('progressive', 'fixed', 'daily', 'weekly')),
    game_id UUID REFERENCES games(id),
    current_amount DECIMAL(15,2) DEFAULT 0.00,
    seed_amount DECIMAL(15,2) DEFAULT 0.00,
    contribution_rate DECIMAL(5,4) DEFAULT 0.0100, -- 1% of bets
    last_won TIMESTAMP WITH TIME ZONE,
    last_winner_id UUID,
    total_won_count INTEGER DEFAULT 0,
    total_won_amount DECIMAL(20,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Jackpot wins
CREATE TABLE jackpot_wins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    jackpot_id UUID NOT NULL REFERENCES jackpots(id),
    user_id UUID NOT NULL,
    game_round_id UUID REFERENCES game_rounds(id),
    amount DECIMAL(15,2) NOT NULL,
    jackpot_amount_before DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Game statistics (aggregated daily)
CREATE TABLE game_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID NOT NULL REFERENCES games(id),
    date DATE NOT NULL,
    total_sessions INTEGER DEFAULT 0,
    unique_players INTEGER DEFAULT 0,
    total_rounds INTEGER DEFAULT 0,
    total_wagered DECIMAL(20,2) DEFAULT 0.00,
    total_won DECIMAL(20,2) DEFAULT 0.00,
    house_profit DECIMAL(20,2) DEFAULT 0.00,
    actual_rtp DECIMAL(5,4) DEFAULT 0.0000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(game_id, date)
);

-- Indexes
CREATE INDEX idx_games_category_id ON games(category_id);
CREATE INDEX idx_games_game_type ON games(game_type);
CREATE INDEX idx_games_is_active ON games(is_active);
CREATE INDEX idx_games_is_featured ON games(is_featured);

CREATE INDEX idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX idx_game_sessions_status ON game_sessions(status);
CREATE INDEX idx_game_sessions_started_at ON game_sessions(started_at);

CREATE INDEX idx_game_rounds_session_id ON game_rounds(session_id);
CREATE INDEX idx_game_rounds_user_id ON game_rounds(user_id);
CREATE INDEX idx_game_rounds_game_id ON game_rounds(game_id);
CREATE INDEX idx_game_rounds_created_at ON game_rounds(created_at);

CREATE INDEX idx_tournaments_status ON tournaments(status);
CREATE INDEX idx_tournaments_start_time ON tournaments(start_time);
CREATE INDEX idx_tournaments_game_id ON tournaments(game_id);

CREATE INDEX idx_tournament_participants_tournament_id ON tournament_participants(tournament_id);
CREATE INDEX idx_tournament_participants_user_id ON tournament_participants(user_id);

CREATE INDEX idx_jackpots_game_id ON jackpots(game_id);
CREATE INDEX idx_jackpots_is_active ON jackpots(is_active);

CREATE INDEX idx_jackpot_wins_jackpot_id ON jackpot_wins(jackpot_id);
CREATE INDEX idx_jackpot_wins_user_id ON jackpot_wins(user_id);

CREATE INDEX idx_game_statistics_game_id ON game_statistics(game_id);
CREATE INDEX idx_game_statistics_date ON game_statistics(date);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_games_updated_at 
    BEFORE UPDATE ON games 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tournaments_updated_at 
    BEFORE UPDATE ON tournaments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_jackpots_updated_at 
    BEFORE UPDATE ON jackpots 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();