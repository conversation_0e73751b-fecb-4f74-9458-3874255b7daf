-- Create Custom Betting Schema for BetBet Platform
-- ===============================================

-- Create custom_betting schema
CREATE SCHEMA IF NOT EXISTS custom_betting;

-- Custom betting markets (user-created markets)
CREATE TABLE custom_betting.custom_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_user_id UUID NOT NULL,
    title VARCHAR(300) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'other',
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'active', 'settling', 'settled', 'cancelled', 'disputed')),
    settlement_type VARCHAR(50) DEFAULT 'manual' CHECK (settlement_type IN ('manual', 'oracle', 'consensus')),
    min_stake DECIMAL(10,2) DEFAULT 1.00,
    max_stake DECIMAL(10,2) DEFAULT 1000.00,
    total_pool DECIMAL(15,2) DEFAULT 0.00,
    participant_count INTEGER DEFAULT 0,
    commission_rate DECIMAL(5,4) DEFAULT 0.0500,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    resolution_date TIMESTAMP WITH TIME ZONE NOT NULL,
    settlement_time TIMESTAMP WITH TIME ZONE,
    winning_outcome VARCHAR(500),
    settlement_notes TEXT,
    evidence_urls TEXT[],
    tags VARCHAR(50)[],
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Custom bet outcomes
CREATE TABLE custom_betting.bet_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id) ON DELETE CASCADE,
    outcome_text VARCHAR(300) NOT NULL,
    description TEXT,
    current_odds DECIMAL(8,2) DEFAULT 2.00,
    total_stakes DECIMAL(15,2) DEFAULT 0.00,
    participant_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Bet participants
CREATE TABLE custom_betting.bet_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id) ON DELETE CASCADE,
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    stake_amount DECIMAL(10,2) NOT NULL,
    potential_payout DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'refunded', 'cancelled')),
    payout_amount DECIMAL(15,2) DEFAULT 0.00,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Escrow transactions for custom bets
CREATE TABLE custom_betting.escrow_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('lock', 'unlock', 'settle', 'refund')),
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
    reference_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Bet matching queue for P2P betting
CREATE TABLE custom_betting.bet_matching_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL,
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    stake_amount DECIMAL(10,2) NOT NULL,
    requested_odds DECIMAL(8,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'matched', 'cancelled', 'expired')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Bet matches (when two opposing bets are matched)
CREATE TABLE custom_betting.bet_matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    back_user_id UUID NOT NULL,
    lay_user_id UUID NOT NULL,
    back_outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    matched_amount DECIMAL(10,2) NOT NULL,
    matched_odds DECIMAL(8,2) NOT NULL,
    back_potential_win DECIMAL(15,2) NOT NULL,
    lay_liability DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'settled', 'voided')),
    settlement_amount DECIMAL(15,2) DEFAULT 0.00,
    settled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Settlement disputes
CREATE TABLE custom_betting.settlement_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    disputer_user_id UUID NOT NULL,
    dispute_reason TEXT NOT NULL,
    evidence_urls TEXT[],
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'rejected')),
    resolution_notes TEXT,
    resolved_by UUID,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Market analytics for custom betting
CREATE TABLE custom_betting.market_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    date DATE NOT NULL,
    total_volume DECIMAL(15,2) DEFAULT 0.00,
    unique_participants INTEGER DEFAULT 0,
    average_stake DECIMAL(10,2) DEFAULT 0.00,
    outcome_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(bet_id, date)
);

-- Indexes
CREATE INDEX idx_custom_bets_creator_user_id ON custom_betting.custom_bets(creator_user_id);
CREATE INDEX idx_custom_bets_status ON custom_betting.custom_bets(status);
CREATE INDEX idx_custom_bets_category ON custom_betting.custom_bets(category);
CREATE INDEX idx_custom_bets_deadline ON custom_betting.custom_bets(deadline);
CREATE INDEX idx_custom_bets_created_at ON custom_betting.custom_bets(created_at);

CREATE INDEX idx_bet_outcomes_bet_id ON custom_betting.bet_outcomes(bet_id);

CREATE INDEX idx_bet_participants_bet_id ON custom_betting.bet_participants(bet_id);
CREATE INDEX idx_bet_participants_user_id ON custom_betting.bet_participants(user_id);
CREATE INDEX idx_bet_participants_outcome_id ON custom_betting.bet_participants(outcome_id);
CREATE INDEX idx_bet_participants_status ON custom_betting.bet_participants(status);

CREATE INDEX idx_escrow_transactions_bet_id ON custom_betting.escrow_transactions(bet_id);
CREATE INDEX idx_escrow_transactions_user_id ON custom_betting.escrow_transactions(user_id);
CREATE INDEX idx_escrow_transactions_status ON custom_betting.escrow_transactions(status);

CREATE INDEX idx_bet_matching_queue_bet_id ON custom_betting.bet_matching_queue(bet_id);
CREATE INDEX idx_bet_matching_queue_user_id ON custom_betting.bet_matching_queue(user_id);
CREATE INDEX idx_bet_matching_queue_status ON custom_betting.bet_matching_queue(status);

CREATE INDEX idx_bet_matches_bet_id ON custom_betting.bet_matches(bet_id);
CREATE INDEX idx_bet_matches_back_user_id ON custom_betting.bet_matches(back_user_id);
CREATE INDEX idx_bet_matches_lay_user_id ON custom_betting.bet_matches(lay_user_id);
CREATE INDEX idx_bet_matches_status ON custom_betting.bet_matches(status);

CREATE INDEX idx_settlement_disputes_bet_id ON custom_betting.settlement_disputes(bet_id);
CREATE INDEX idx_settlement_disputes_disputer_user_id ON custom_betting.settlement_disputes(disputer_user_id);
CREATE INDEX idx_settlement_disputes_status ON custom_betting.settlement_disputes(status);

CREATE INDEX idx_market_analytics_bet_id ON custom_betting.market_analytics(bet_id);
CREATE INDEX idx_market_analytics_date ON custom_betting.market_analytics(date);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_custom_bets_updated_at 
    BEFORE UPDATE ON custom_betting.custom_bets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();