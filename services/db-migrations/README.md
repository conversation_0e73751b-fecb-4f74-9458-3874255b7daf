# BetBet Platform - Centralized Database Migration System

## Overview

This directory contains the centralized database migration system for the BetBet platform. All database schema changes and migrations are managed here to ensure consistency and prevent conflicts between services.

## Migration Structure

```
db-migrations/
├── Dockerfile                  # Migration runner container
├── run-migrations.sh          # Main migration script
├── README.md                  # This documentation
├── service-schemas/           # Consolidated service migrations
│   ├── 001_create_custom_betting_schema.sql
│   ├── 001_add_betting_tables.sql
│   ├── 001_create_expert_analyst_schema.sql
│   ├── 001_create_leaderboards_schema.sql
│   ├── 002_create_expert_analyst_indexes.sql
│   └── 003_create_expert_analyst_security.sql
├── schemas/                   # Legacy schema files (deprecated)
│   ├── gaming_engine.sql
│   ├── odds_exchange.sql
│   └── sports_analysis.sql
└── versions/                  # Version-based migrations
    ├── 001_create_gaming_engine.sql
    └── 004_create_sports_analysis.sql
```

## Migration Process

### Automatic Execution

Migrations run automatically when starting the platform:

**Development:**
```bash
docker-compose up -d
# db-migrations container runs and exits automatically
```

**Production:**
```bash
docker-compose -f docker-compose.prod.yml up -d
# db-migrations container runs and exits automatically
```

### Manual Execution

To run migrations manually:

```bash
# Using Docker Compose
docker-compose run --rm db-migrations

# Or directly with Docker
docker run --rm \
  --network betbet-network \
  -e POSTGRES_HOST=postgres \
  -e POSTGRES_DB=betbet_platform \
  -e POSTGRES_USER=betbet_user \
  -e POSTGRES_PASSWORD=your_password \
  betbet/db-migrations
```

## Migration Order

1. **Database Initialization** (`/migrations/init/*.sql`)
   - Basic database setup and extensions

2. **Versioned Migrations** (`/migrations/versions/*.sql`)
   - Core schema changes in chronological order
   - Sorted by filename (001_, 002_, etc.)

3. **Service Schemas** (`/migrations/service-schemas/*.sql`)
   - Consolidated service-specific schemas
   - Includes all individual service migrations

4. **Legacy Schemas** (`/migrations/schemas/*.sql`)
   - Legacy support (will be deprecated)
   - Only applied if service-schemas doesn't exist

## Adding New Migrations

### Step 1: Create Migration File

Create a new SQL file in the appropriate directory:

```bash
# For version-based migrations (core platform changes)
echo "-- New migration" > services/db-migrations/versions/$(date +%Y%m%d_%H%M%S)_description.sql

# For service-specific migrations (add to service-schemas)
echo "-- Service migration" > services/db-migrations/service-schemas/$(printf "%03d" $NEXT_NUMBER)_service_description.sql
```

### Step 2: Write Migration SQL

```sql
-- Migration: 001_add_new_feature
-- Description: Add new feature to the platform
-- Date: 2024-01-01

BEGIN;

-- Your migration code here
CREATE TABLE new_feature (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_new_feature_name ON new_feature(name);

COMMIT;
```

### Step 3: Test Migration

Test your migration in development:

```bash
# Start fresh database
docker-compose down -v
docker-compose up -d postgres redis

# Run migrations
docker-compose up db-migrations

# Verify migration applied
docker exec betbet-postgres psql -U betbet_user -d betbet_platform \
  -c "SELECT * FROM schema_migrations ORDER BY applied_at;"
```

## Migration Tracking

All migrations are tracked in the `schema_migrations` table:

```sql
CREATE TABLE schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);
```

### Viewing Migration Status

```bash
# Check which migrations have been applied
docker exec betbet-postgres psql -U betbet_user -d betbet_platform \
  -c "SELECT version, applied_at, description FROM schema_migrations ORDER BY applied_at;"
```

## Migration Safety

### Transaction Safety
- Each migration runs in a transaction
- Failed migrations are rolled back automatically
- No partial migrations are applied

### Idempotent Operations
- Migrations can be run multiple times safely
- Already applied migrations are skipped
- Use `IF NOT EXISTS` and `IF EXISTS` appropriately

### Example Safe Migration

```sql
-- Safe migration example
BEGIN;

-- Create table only if it doesn't exist
CREATE TABLE IF NOT EXISTS users_new_feature (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    feature_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add column only if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'new_field'
    ) THEN
        ALTER TABLE users ADD COLUMN new_field VARCHAR(100);
    END IF;
END
$$;

-- Create index only if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_users_new_field ON users(new_field);

COMMIT;
```

## Rollback Procedures

### Manual Rollback

1. **Identify problematic migration:**
   ```sql
   SELECT * FROM schema_migrations ORDER BY applied_at DESC LIMIT 5;
   ```

2. **Create rollback migration:**
   ```sql
   -- rollback_YYYYMMDD_description.sql
   BEGIN;
   
   -- Undo the changes from the problematic migration
   DROP TABLE IF EXISTS problematic_table;
   ALTER TABLE existing_table DROP COLUMN IF EXISTS problematic_column;
   
   COMMIT;
   ```

3. **Apply rollback:**
   ```bash
   docker exec -i betbet-postgres psql -U betbet_user -d betbet_platform < rollback.sql
   ```

### Emergency Rollback

For critical issues, restore from backup:

```bash
# Stop all services
docker-compose down

# Restore database from backup
docker run --rm -v postgres_data:/var/lib/postgresql/data \
  -v ./backups:/backups postgres:15 \
  pg_restore -U betbet_user -d betbet_platform /backups/latest.dump

# Restart services
docker-compose up -d
```

## Best Practices

### 1. Naming Conventions
- Use descriptive names: `001_add_user_authentication.sql`
- Include date for major changes: `20240101_major_schema_update.sql`
- Prefix with numbers for ordering: `001_`, `002_`, etc.

### 2. Migration Content
- Always use transactions (`BEGIN` / `COMMIT`)
- Include rollback instructions in comments
- Use `IF EXISTS` / `IF NOT EXISTS` for safety
- Test migrations on development data first

### 3. Documentation
- Include description in migration header
- Document any manual steps required
- Note dependencies on other migrations

### 4. Performance
- Consider impact on large tables
- Use `CONCURRENTLY` for index creation on large tables
- Plan maintenance windows for major changes

## Troubleshooting

### Common Issues

**Migration fails with "relation already exists":**
```sql
-- Use IF NOT EXISTS
CREATE TABLE IF NOT EXISTS my_table (...);
```

**Migration fails with "column does not exist":**
```sql
-- Check column existence first
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'my_table' AND column_name = 'my_column') THEN
        ALTER TABLE my_table DROP COLUMN my_column;
    END IF;
END
$$;
```

**Database connection timeout:**
- Check if PostgreSQL is healthy: `docker-compose ps`
- Verify environment variables are set correctly
- Check network connectivity between containers

### Debugging

Enable verbose logging:

```bash
# Set debug mode in migration script
export DEBUG=true
docker-compose up db-migrations
```

Check migration logs:

```bash
docker-compose logs db-migrations
```

## Security Considerations

- Never include sensitive data in migration files
- Use environment variables for configuration
- Restrict database user permissions appropriately
- Review all migrations before applying to production

## Legacy Service Migrations

**DEPRECATED:** Individual service migration scripts have been removed to prevent conflicts. All migrations now go through this centralized system.

Previously removed:
- `services/custom-betting/app/database/migrations/run_migrations.py`
- `services/gaming-engine/app/database/migrations/run_migrations.py`
- `services/leaderboards/app/database/migrations/run_migrations.py`
- `services/expert-analysts/app/database/migrations/run_migrations.py`

If you need to add service-specific migrations, add them to the `service-schemas/` directory in this centralized system.