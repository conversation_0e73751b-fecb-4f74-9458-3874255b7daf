-- Create Expert Analysis Schema for BetBet Platform
-- =================================================

-- Create expert_analysis schema
CREATE SCHEMA IF NOT EXISTS expert_analysis;

-- Expert profiles
CREATE TABLE expert_analysis.experts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    bio TEXT,
    specialization VARCHAR(100),
    experience_years INTEGER DEFAULT 0,
    verification_status VARCHAR(50) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
    tier VARCHAR(20) DEFAULT 'bronze' CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
    total_picks INTEGER DEFAULT 0,
    winning_picks INTEGER DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0.0000,
    average_odds DECIMAL(8,2) DEFAULT 0.00,
    roi DECIMAL(8,4) DEFAULT 0.0000,
    followers_count INTEGER DEFAULT 0,
    subscription_price DECIMAL(10,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.1000,
    is_active BOOLEAN DEFAULT true,
    avatar_url TEXT,
    social_links JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert picks/predictions
CREATE TABLE expert_analysis.picks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    sport VARCHAR(50),
    event_date TIMESTAMP WITH TIME ZONE,
    pick_type VARCHAR(50) NOT NULL CHECK (pick_type IN ('single', 'accumulator', 'system')),
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 10),
    recommended_stake DECIMAL(10,2),
    odds DECIMAL(8,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'void', 'pending')),
    result_notes TEXT,
    is_premium BOOLEAN DEFAULT false,
    price DECIMAL(10,2) DEFAULT 0.00,
    units INTEGER DEFAULT 1,
    profit_loss DECIMAL(10,2) DEFAULT 0.00,
    subscribers_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    settled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert subscriptions
CREATE TABLE expert_analysis.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    subscription_type VARCHAR(50) DEFAULT 'monthly' CHECK (subscription_type IN ('daily', 'weekly', 'monthly', 'yearly', 'lifetime')),
    price_paid DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'paused')),
    auto_renew BOOLEAN DEFAULT true,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, expert_id)
);

-- Pick purchases (one-time purchases)
CREATE TABLE expert_analysis.pick_purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    pick_id UUID NOT NULL REFERENCES expert_analysis.picks(id),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    price_paid DECIMAL(10,2) NOT NULL,
    purchase_method VARCHAR(50) DEFAULT 'credits',
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert performance tracking
CREATE TABLE expert_analysis.performance_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_picks INTEGER DEFAULT 0,
    winning_picks INTEGER DEFAULT 0,
    losing_picks INTEGER DEFAULT 0,
    void_picks INTEGER DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0.0000,
    average_odds DECIMAL(8,2) DEFAULT 0.00,
    roi DECIMAL(8,4) DEFAULT 0.0000,
    profit_loss DECIMAL(15,2) DEFAULT 0.00,
    subscribers_gained INTEGER DEFAULT 0,
    subscribers_lost INTEGER DEFAULT 0,
    revenue_earned DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(expert_id, period_type, period_start)
);

-- Pick comments and ratings
CREATE TABLE expert_analysis.pick_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pick_id UUID NOT NULL REFERENCES expert_analysis.picks(id),
    user_id UUID NOT NULL,
    comment TEXT NOT NULL,
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    is_verified_purchase BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert followers
CREATE TABLE expert_analysis.expert_followers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    follower_user_id UUID NOT NULL,
    notifications_enabled BOOLEAN DEFAULT true,
    followed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(expert_id, follower_user_id)
);

-- Expert rankings
CREATE TABLE expert_analysis.expert_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    ranking_type VARCHAR(50) NOT NULL CHECK (ranking_type IN ('overall', 'category', 'sport', 'monthly', 'yearly')),
    ranking_category VARCHAR(100),
    rank_position INTEGER NOT NULL,
    score DECIMAL(10,4) NOT NULL,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(expert_id, ranking_type, ranking_category)
);

-- Indexes
CREATE INDEX idx_experts_user_id ON expert_analysis.experts(user_id);
CREATE INDEX idx_experts_verification_status ON expert_analysis.experts(verification_status);
CREATE INDEX idx_experts_tier ON expert_analysis.experts(tier);
CREATE INDEX idx_experts_win_rate ON expert_analysis.experts(win_rate);
CREATE INDEX idx_experts_is_active ON expert_analysis.experts(is_active);

CREATE INDEX idx_picks_expert_id ON expert_analysis.picks(expert_id);
CREATE INDEX idx_picks_category ON expert_analysis.picks(category);
CREATE INDEX idx_picks_sport ON expert_analysis.picks(sport);
CREATE INDEX idx_picks_status ON expert_analysis.picks(status);
CREATE INDEX idx_picks_is_premium ON expert_analysis.picks(is_premium);
CREATE INDEX idx_picks_event_date ON expert_analysis.picks(event_date);
CREATE INDEX idx_picks_created_at ON expert_analysis.picks(created_at);

CREATE INDEX idx_subscriptions_user_id ON expert_analysis.subscriptions(user_id);
CREATE INDEX idx_subscriptions_expert_id ON expert_analysis.subscriptions(expert_id);
CREATE INDEX idx_subscriptions_status ON expert_analysis.subscriptions(status);
CREATE INDEX idx_subscriptions_expires_at ON expert_analysis.subscriptions(expires_at);

CREATE INDEX idx_pick_purchases_user_id ON expert_analysis.pick_purchases(user_id);
CREATE INDEX idx_pick_purchases_pick_id ON expert_analysis.pick_purchases(pick_id);
CREATE INDEX idx_pick_purchases_expert_id ON expert_analysis.pick_purchases(expert_id);

CREATE INDEX idx_performance_stats_expert_id ON expert_analysis.performance_stats(expert_id);
CREATE INDEX idx_performance_stats_period ON expert_analysis.performance_stats(period_type, period_start);

CREATE INDEX idx_pick_comments_pick_id ON expert_analysis.pick_comments(pick_id);
CREATE INDEX idx_pick_comments_user_id ON expert_analysis.pick_comments(user_id);

CREATE INDEX idx_expert_followers_expert_id ON expert_analysis.expert_followers(expert_id);
CREATE INDEX idx_expert_followers_follower_user_id ON expert_analysis.expert_followers(follower_user_id);

CREATE INDEX idx_expert_rankings_expert_id ON expert_analysis.expert_rankings(expert_id);
CREATE INDEX idx_expert_rankings_type ON expert_analysis.expert_rankings(ranking_type, ranking_category);
CREATE INDEX idx_expert_rankings_position ON expert_analysis.expert_rankings(rank_position);

-- Update triggers
CREATE TRIGGER update_experts_updated_at 
    BEFORE UPDATE ON expert_analysis.experts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_picks_updated_at 
    BEFORE UPDATE ON expert_analysis.picks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON expert_analysis.subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();