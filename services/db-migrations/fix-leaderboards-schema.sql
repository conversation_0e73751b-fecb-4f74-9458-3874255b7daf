-- Fix Leaderboards Schema to Match Service Models
-- =================================================

-- Drop existing schema and recreate with correct structure
DROP SCHEMA IF EXISTS leaderboards CASCADE;
CREATE SCHEMA leaderboards;

-- UserRanking table (exact match to service model)
CREATE TABLE leaderboards.user_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    leaderboard_type VARCHAR(50) NOT NULL,
    points INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    win_rate NUMERIC(5,4) DEFAULT 0,
    total_winnings NUMERIC(15,2) DEFAULT 0,
    rank INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Achievement table (exact match to service model)
CREATE TABLE leaderboards.achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    achievement_type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    points_awarded INTEGER DEFAULT 0,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_user_rankings_user_id ON leaderboards.user_rankings(user_id);
CREATE INDEX idx_user_rankings_leaderboard_type ON leaderboards.user_rankings(leaderboard_type);
CREATE INDEX idx_user_rankings_points ON leaderboards.user_rankings(points);
CREATE INDEX idx_user_rankings_rank ON leaderboards.user_rankings(rank);
CREATE INDEX idx_user_rankings_win_rate ON leaderboards.user_rankings(win_rate);

CREATE INDEX idx_achievements_user_id ON leaderboards.achievements(user_id);
CREATE INDEX idx_achievements_achievement_type ON leaderboards.achievements(achievement_type);
CREATE INDEX idx_achievements_unlocked_at ON leaderboards.achievements(unlocked_at);

-- Composite indexes for common queries
CREATE INDEX idx_user_rankings_type_rank ON leaderboards.user_rankings(leaderboard_type, rank);
CREATE INDEX idx_user_rankings_type_points ON leaderboards.user_rankings(leaderboard_type, points DESC);