-- BetBet Platform - Odds Exchange & Trading Database Schema
-- ============================================================
-- Module 5: Financial-Grade Sports Trading Platform with Ultra-Low Latency
-- Template Compliance: Gaming Engine enterprise patterns with financial enhancements
-- Integration: Gaming Engine, Custom Betting, Expert Analysts, Sports Analysis modules
-- Author: Claude-DB
-- Date: 2025-01-21
-- Version: 1.0.0

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- Trigram search for text matching
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- Optimized GIN indexes
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"; -- Performance monitoring

-- Create odds exchange schema
CREATE SCHEMA IF NOT EXISTS odds_exchange;

-- ================================================================================
-- STANDARD BETBET TEMPLATE TABLES (REQUIRED FOR ALL MODULES)
-- ================================================================================

-- Standard User Table (shared across modules) - ensure it exists
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    avatar_url TEXT,
    balance DECIMAL(12,2) DEFAULT 0.00,
    
    -- Status and permissions
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    roles TEXT[] DEFAULT ARRAY['user'],
    permissions TEXT[] DEFAULT ARRAY[],
    
    -- Security fields
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    mfa_secret VARCHAR(255),
    mfa_enabled BOOLEAN DEFAULT false,
    
    -- Audit fields (REQUIRED for all tables)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1,
    
    -- Soft delete (REQUIRED for all tables)
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID
);

-- Standard Audit Log Table (REQUIRED for all modules)
CREATE TABLE odds_exchange.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID REFERENCES public.users(id),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Standard Financial Transaction Table (REQUIRED for financial modules)
CREATE TABLE odds_exchange.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_type VARCHAR(50) NOT NULL,
    user_id UUID NOT NULL REFERENCES public.users(id),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT NOT NULL,
    reference_id UUID,
    
    -- Double-entry bookkeeping fields
    debit_account VARCHAR(100) NOT NULL,
    credit_account VARCHAR(100) NOT NULL,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed')),
    
    -- External references
    external_transaction_id VARCHAR(255),
    payment_method VARCHAR(50),
    
    -- Metadata
    metadata JSONB,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Standard Real-time Event Table (REQUIRED for real-time modules)
CREATE TABLE odds_exchange.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    event_source VARCHAR(50) NOT NULL,
    event_target VARCHAR(50),
    user_id UUID REFERENCES public.users(id),
    session_id VARCHAR(255),
    
    -- Event data
    event_data JSONB NOT NULL,
    
    -- Processing status
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id)
);

-- Standard Performance Metrics Table (REQUIRED for all modules)
CREATE TABLE odds_exchange.performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(20),
    metric_tags JSONB,
    user_id UUID REFERENCES public.users(id),
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================================
-- MARKET & INSTRUMENT MANAGEMENT TABLES
-- ================================================================================

-- Trading markets (sports events, outcomes)
CREATE TABLE odds_exchange.markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market identification
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('sports', 'politics', 'entertainment', 'economics', 'weather', 'custom')),
    subcategory VARCHAR(50),
    
    -- Sports integration (optional)
    sports_event_id UUID, -- Reference to sports_analysis.fixtures.id
    event_name VARCHAR(200),
    event_date TIMESTAMP WITH TIME ZONE,
    
    -- Market configuration
    market_type VARCHAR(30) NOT NULL CHECK (market_type IN ('binary', 'decimal', 'american', 'fractional', 'over_under', 'handicap', 'outright')),
    settlement_type VARCHAR(20) DEFAULT 'automatic' CHECK (settlement_type IN ('automatic', 'manual', 'oracle', 'consensus')),
    
    -- Market parameters
    min_stake DECIMAL(12,4) DEFAULT 1.00 CHECK (min_stake > 0),
    max_stake DECIMAL(12,4) CHECK (max_stake IS NULL OR max_stake >= min_stake),
    tick_size DECIMAL(8,6) DEFAULT 0.01 CHECK (tick_size > 0), -- Minimum price increment
    
    -- Market timing
    opens_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closes_at TIMESTAMP WITH TIME ZONE,
    settlement_time TIMESTAMP WITH TIME ZONE,
    
    -- Market state
    status VARCHAR(20) DEFAULT 'pre_open' CHECK (status IN ('pre_open', 'open', 'in_play', 'suspended', 'closed', 'settled', 'cancelled', 'voided')),
    in_play_enabled BOOLEAN DEFAULT false,
    
    -- Liquidity and statistics
    total_matched DECIMAL(15,2) DEFAULT 0 CHECK (total_matched >= 0),
    total_unmatched DECIMAL(15,2) DEFAULT 0 CHECK (total_unmatched >= 0),
    active_orders INTEGER DEFAULT 0 CHECK (active_orders >= 0),
    
    -- Settlement
    settlement_data JSONB,
    settlement_source VARCHAR(100),
    settlement_evidence TEXT,
    
    -- Market configuration
    market_config JSONB DEFAULT '{}',
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Tradeable instruments (market contracts)
CREATE TABLE odds_exchange.instruments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market reference
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id) ON DELETE CASCADE,
    
    -- Instrument identification
    name VARCHAR(200) NOT NULL,
    code VARCHAR(50) NOT NULL, -- Trading symbol/code
    description TEXT,
    
    -- Outcome definition
    outcome_key VARCHAR(100) NOT NULL, -- Machine-readable outcome identifier
    outcome_description TEXT NOT NULL,
    
    -- Trading parameters
    is_tradeable BOOLEAN DEFAULT true,
    instrument_type VARCHAR(20) DEFAULT 'selection' CHECK (instrument_type IN ('selection', 'lay', 'back', 'spread')),
    
    -- Price bounds
    min_price DECIMAL(8,4) DEFAULT 1.01 CHECK (min_price > 0),
    max_price DECIMAL(8,4) DEFAULT 1000.00 CHECK (max_price >= min_price),
    
    -- Current market state
    last_traded_price DECIMAL(8,4),
    best_back_price DECIMAL(8,4), -- Best price to back (buy)
    best_lay_price DECIMAL(8,4),  -- Best price to lay (sell)
    total_matched DECIMAL(15,2) DEFAULT 0 CHECK (total_matched >= 0),
    
    -- Market depth
    available_back DECIMAL(15,2) DEFAULT 0 CHECK (available_back >= 0),
    available_lay DECIMAL(15,2) DEFAULT 0 CHECK (available_lay >= 0),
    
    -- Settlement
    settlement_price DECIMAL(8,4) CHECK (settlement_price IS NULL OR settlement_price >= 0),
    is_winner BOOLEAN,
    
    -- Display and ordering
    display_order INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    UNIQUE(market_id, code),
    UNIQUE(market_id, outcome_key)
);

-- Trading session management (open/close times)
CREATE TABLE odds_exchange.market_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market reference
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    
    -- Session identification
    session_name VARCHAR(100) NOT NULL,
    session_type VARCHAR(20) DEFAULT 'regular' CHECK (session_type IN ('regular', 'extended', 'pre_market', 'post_market', 'maintenance')),
    
    -- Session timing
    opens_at TIMESTAMP WITH TIME ZONE NOT NULL,
    closes_at TIMESTAMP WITH TIME ZONE NOT NULL CHECK (closes_at > opens_at),
    
    -- Session state
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'open', 'paused', 'closed', 'cancelled')),
    
    -- Trading parameters for this session
    trading_enabled BOOLEAN DEFAULT true,
    order_modification_enabled BOOLEAN DEFAULT true,
    order_cancellation_enabled BOOLEAN DEFAULT true,
    
    -- Session statistics
    orders_placed INTEGER DEFAULT 0,
    trades_executed INTEGER DEFAULT 0,
    volume_traded DECIMAL(15,2) DEFAULT 0,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Market state tracking (pre-open, open, suspended, closed)
CREATE TABLE odds_exchange.market_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market reference
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    
    -- Status information
    status VARCHAR(20) NOT NULL CHECK (status IN ('pre_open', 'open', 'in_play', 'suspended', 'closed', 'settled', 'cancelled', 'voided')),
    previous_status VARCHAR(20),
    
    -- Status reason and details
    reason VARCHAR(100),
    description TEXT,
    automatic_change BOOLEAN DEFAULT false,
    
    -- Effective timing
    effective_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    effective_until TIMESTAMP WITH TIME ZONE,
    
    -- Trading impact
    suspend_trading BOOLEAN DEFAULT false,
    suspend_cancellations BOOLEAN DEFAULT false,
    suspend_modifications BOOLEAN DEFAULT false,
    
    -- Audit fields (simplified for high-frequency updates)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    
    -- Index for fast lookups
    INDEX (market_id, effective_from DESC)
);

-- Contract specifications and parameters
CREATE TABLE odds_exchange.contract_specs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Instrument reference
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    
    -- Contract specification
    contract_size DECIMAL(10,4) DEFAULT 1.0 CHECK (contract_size > 0),
    currency VARCHAR(3) DEFAULT 'USD',
    settlement_currency VARCHAR(3) DEFAULT 'USD',
    
    -- Price specifications
    price_format VARCHAR(20) DEFAULT 'decimal' CHECK (price_format IN ('decimal', 'fractional', 'american', 'percentage')),
    price_precision INTEGER DEFAULT 4 CHECK (price_precision BETWEEN 2 AND 8),
    
    -- Trading specifications
    min_order_size DECIMAL(10,4) DEFAULT 1.0 CHECK (min_order_size > 0),
    max_order_size DECIMAL(10,4) CHECK (max_order_size IS NULL OR max_order_size >= min_order_size),
    lot_size DECIMAL(10,4) DEFAULT 1.0 CHECK (lot_size > 0),
    
    -- Risk parameters
    initial_margin_rate DECIMAL(5,4) DEFAULT 0.05 CHECK (initial_margin_rate BETWEEN 0 AND 1),
    maintenance_margin_rate DECIMAL(5,4) DEFAULT 0.03 CHECK (maintenance_margin_rate BETWEEN 0 AND initial_margin_rate),
    
    -- Settlement specifications
    settlement_method VARCHAR(20) DEFAULT 'cash' CHECK (settlement_method IN ('cash', 'physical', 'binary')),
    settlement_multiplier DECIMAL(10,4) DEFAULT 1.0,
    
    -- Contract metadata
    specifications JSONB DEFAULT '{}',
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- ================================================================================
-- ORDER MANAGEMENT SYSTEM
-- ================================================================================

-- User orders (buy/sell, price, quantity)
CREATE TABLE odds_exchange.orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User and instrument references
    user_id UUID NOT NULL REFERENCES public.users(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    
    -- Order identification
    client_order_id VARCHAR(50), -- Client-provided order ID
    order_reference VARCHAR(100), -- Human-readable reference
    
    -- Order specification
    side VARCHAR(4) NOT NULL CHECK (side IN ('back', 'lay')), -- back = buy, lay = sell
    order_type VARCHAR(20) DEFAULT 'limit' CHECK (order_type IN ('market', 'limit', 'stop', 'stop_limit', 'fill_or_kill', 'immediate_or_cancel')),
    
    -- Order sizing
    size DECIMAL(15,4) NOT NULL CHECK (size > 0), -- Stake amount
    price DECIMAL(8,4) CHECK (price IS NULL OR price > 0), -- NULL for market orders
    
    -- Execution tracking
    filled_size DECIMAL(15,4) DEFAULT 0 CHECK (filled_size >= 0 AND filled_size <= size),
    remaining_size DECIMAL(15,4) GENERATED ALWAYS AS (size - filled_size) STORED,
    average_price DECIMAL(8,4),
    
    -- Order lifecycle
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'open', 'partially_filled', 'filled', 'cancelled', 'rejected', 'expired')),
    
    -- Timing
    time_in_force VARCHAR(20) DEFAULT 'good_till_cancelled' CHECK (time_in_force IN ('good_till_cancelled', 'immediate_or_cancel', 'fill_or_kill', 'good_till_date')),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Order flags
    is_post_only BOOLEAN DEFAULT false, -- Only add liquidity, don't take
    is_reduce_only BOOLEAN DEFAULT false, -- Only reduce position
    is_hidden BOOLEAN DEFAULT false, -- Hidden from order book display
    
    -- Financial calculations
    total_consideration DECIMAL(15,2) GENERATED ALWAYS AS (size * COALESCE(price, 1.0)) STORED,
    fees_charged DECIMAL(15,2) DEFAULT 0,
    commission_rate DECIMAL(6,4) DEFAULT 0.005, -- 0.5% default commission
    
    -- Risk and validation
    risk_check_passed BOOLEAN DEFAULT false,
    margin_requirement DECIMAL(15,2),
    risk_data JSONB,
    
    -- Processing timestamps (for ultra-low latency tracking)
    received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    validated_at TIMESTAMP WITH TIME ZONE,
    matched_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Performance tracking
    processing_time_ms DECIMAL(8,3), -- Time from received to validated
    matching_time_ms DECIMAL(8,3),   -- Time from validated to matched
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete (orders are never physically deleted for audit)
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    CHECK (order_type != 'market' OR price IS NULL),
    CHECK (order_type = 'market' OR price IS NOT NULL)
);

-- Real-time order book state (optimized for ultra-fast access)
CREATE TABLE odds_exchange.order_book (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    
    -- Order book entry
    side VARCHAR(4) NOT NULL CHECK (side IN ('back', 'lay')),
    price DECIMAL(8,4) NOT NULL CHECK (price > 0),
    size DECIMAL(15,4) NOT NULL CHECK (size > 0),
    
    -- Priority and ordering
    price_level INTEGER NOT NULL, -- Price level rank (1 = best price)
    time_priority BIGINT NOT NULL, -- Nanosecond timestamp for time priority
    
    -- Book state
    is_active BOOLEAN DEFAULT true,
    sequence_number BIGINT, -- For order book sequencing
    
    -- Fast access fields
    market_id UUID NOT NULL, -- Denormalized for faster queries
    user_id UUID NOT NULL,   -- Denormalized for faster queries
    
    -- Timestamps (minimal for performance)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraints
    UNIQUE(instrument_id, order_id),
    
    -- Partitioning key for performance
    INDEX (instrument_id, side, price_level, time_priority)
) PARTITION BY HASH (instrument_id);

-- Create partitions for order book (distribute across 16 partitions for performance)
CREATE TABLE odds_exchange.order_book_p0 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 0);
CREATE TABLE odds_exchange.order_book_p1 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 1);
CREATE TABLE odds_exchange.order_book_p2 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 2);
CREATE TABLE odds_exchange.order_book_p3 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 3);
CREATE TABLE odds_exchange.order_book_p4 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 4);
CREATE TABLE odds_exchange.order_book_p5 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 5);
CREATE TABLE odds_exchange.order_book_p6 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 6);
CREATE TABLE odds_exchange.order_book_p7 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 7);
CREATE TABLE odds_exchange.order_book_p8 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 8);
CREATE TABLE odds_exchange.order_book_p9 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 9);
CREATE TABLE odds_exchange.order_book_p10 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 10);
CREATE TABLE odds_exchange.order_book_p11 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 11);
CREATE TABLE odds_exchange.order_book_p12 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 12);
CREATE TABLE odds_exchange.order_book_p13 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 13);
CREATE TABLE odds_exchange.order_book_p14 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 14);
CREATE TABLE odds_exchange.order_book_p15 PARTITION OF odds_exchange.order_book FOR VALUES WITH (MODULUS 16, REMAINDER 15);

-- Historical order states and modifications
CREATE TABLE odds_exchange.order_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Order reference
    order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    
    -- State change information
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN ('created', 'modified', 'partially_filled', 'filled', 'cancelled', 'rejected', 'expired')),
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    
    -- Change details
    size_change DECIMAL(15,4) DEFAULT 0,
    price_change DECIMAL(8,4) DEFAULT 0,
    filled_amount DECIMAL(15,4) DEFAULT 0,
    
    -- Reason and context
    change_reason VARCHAR(100),
    change_source VARCHAR(50), -- 'user', 'system', 'matching_engine', 'risk_management'
    
    -- Related entities
    related_trade_id UUID,
    related_order_id UUID,
    
    -- Processing metrics
    processing_time_ms DECIMAL(8,3),
    
    -- Event data
    event_data JSONB,
    
    -- Audit fields (simplified for performance)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    
    -- Fast access index
    INDEX (order_id, created_at DESC)
);

-- Partial and complete order fills
CREATE TABLE odds_exchange.order_fills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Order references
    order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    user_id UUID NOT NULL REFERENCES public.users(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    
    -- Fill details
    fill_price DECIMAL(8,4) NOT NULL CHECK (fill_price > 0),
    fill_size DECIMAL(15,4) NOT NULL CHECK (fill_size > 0),
    fill_amount DECIMAL(15,2) GENERATED ALWAYS AS (fill_size * fill_price) STORED,
    
    -- Trade reference
    trade_id UUID NOT NULL, -- References trades table
    
    -- Fill type and context
    fill_type VARCHAR(20) DEFAULT 'normal' CHECK (fill_type IN ('normal', 'improvement', 'crossing')),
    aggressor_side VARCHAR(4) NOT NULL CHECK (aggressor_side IN ('back', 'lay')),
    is_self_trade BOOLEAN DEFAULT false,
    
    -- Commission and fees
    commission_amount DECIMAL(15,2) DEFAULT 0 CHECK (commission_amount >= 0),
    commission_rate DECIMAL(6,4) DEFAULT 0,
    fees_breakdown JSONB,
    
    -- Market impact
    market_impact_bps INTEGER, -- Basis points of market impact
    
    -- Liquidity tracking
    liquidity_flag VARCHAR(10) CHECK (liquidity_flag IN ('maker', 'taker', 'both')),
    
    -- Performance metrics
    execution_time_ms DECIMAL(8,3),
    
    -- Audit fields (simplified for high-frequency data)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Fast access indexes
    INDEX (order_id, created_at DESC),
    INDEX (user_id, created_at DESC),
    INDEX (trade_id)
);

-- Order cancellation tracking
CREATE TABLE odds_exchange.order_cancellations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Order reference
    order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    user_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Cancellation details
    cancellation_reason VARCHAR(50) NOT NULL CHECK (cancellation_reason IN ('user_request', 'system', 'risk_management', 'expiry', 'market_closure', 'insufficient_margin')),
    
    -- Order state at cancellation
    remaining_size DECIMAL(15,4) NOT NULL CHECK (remaining_size > 0),
    filled_size DECIMAL(15,4) DEFAULT 0 CHECK (filled_size >= 0),
    
    -- Cancellation context
    initiated_by VARCHAR(20) DEFAULT 'user' CHECK (initiated_by IN ('user', 'system', 'admin', 'risk_engine')),
    cancellation_source VARCHAR(100), -- Additional context
    
    -- Processing metrics
    cancellation_time_ms DECIMAL(8,3),
    
    -- Audit fields (simplified)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    
    -- Fast access index
    INDEX (order_id),
    INDEX (user_id, created_at DESC)
);

-- ================================================================================
-- TRADING ENGINE
-- ================================================================================

-- Executed trades with settlement details
CREATE TABLE odds_exchange.trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Trade identification
    trade_reference VARCHAR(100) UNIQUE NOT NULL,
    external_trade_id VARCHAR(100), -- For external system integration
    
    -- Market and instrument
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    
    -- Trade participants
    back_order_id UUID NOT NULL REFERENCES odds_exchange.orders(id), -- Buyer order
    lay_order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),  -- Seller order
    back_user_id UUID NOT NULL REFERENCES public.users(id),
    lay_user_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Trade execution details
    price DECIMAL(8,4) NOT NULL CHECK (price > 0),
    size DECIMAL(15,4) NOT NULL CHECK (size > 0),
    value DECIMAL(15,2) GENERATED ALWAYS AS (price * size) STORED,
    
    -- Trade classification
    trade_type VARCHAR(20) DEFAULT 'normal' CHECK (trade_type IN ('normal', 'crossing', 'block', 'dark_pool', 'market_making')),
    aggressor_side VARCHAR(4) NOT NULL CHECK (aggressor_side IN ('back', 'lay')),
    
    -- Execution context
    matching_engine_id VARCHAR(50),
    execution_venue VARCHAR(50) DEFAULT 'primary',
    
    -- Commission and fees
    back_commission DECIMAL(15,2) DEFAULT 0 CHECK (back_commission >= 0),
    lay_commission DECIMAL(15,2) DEFAULT 0 CHECK (lay_commission >= 0),
    total_fees DECIMAL(15,2) GENERATED ALWAYS AS (back_commission + lay_commission) STORED,
    
    -- Settlement tracking
    settlement_status VARCHAR(20) DEFAULT 'pending' CHECK (settlement_status IN ('pending', 'settling', 'settled', 'failed', 'reversed')),
    settlement_date DATE,
    
    -- Market impact and statistics
    market_impact_bps INTEGER, -- Market impact in basis points
    is_block_trade BOOLEAN DEFAULT false,
    is_dark_pool_trade BOOLEAN DEFAULT false,
    
    -- Performance metrics
    matching_latency_ms DECIMAL(8,3), -- Time to match orders
    settlement_latency_ms DECIMAL(8,3), -- Time to settle trade
    
    -- Trade metadata
    trade_data JSONB DEFAULT '{}',
    
    -- Regulatory fields
    trade_reporting_required BOOLEAN DEFAULT true,
    reported_to_regulator BOOLEAN DEFAULT false,
    reporting_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (back_user_id != lay_user_id), -- Prevent self-trading
    CHECK (back_order_id != lay_order_id)
);

-- Trade clearing and settlement status
CREATE TABLE odds_exchange.trade_settlements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Trade reference
    trade_id UUID NOT NULL REFERENCES odds_exchange.trades(id),
    
    -- Settlement details
    settlement_type VARCHAR(20) DEFAULT 'cash' CHECK (settlement_type IN ('cash', 'physical', 'netting', 'dvp')),
    settlement_method VARCHAR(20) DEFAULT 'automatic' CHECK (settlement_method IN ('automatic', 'manual', 'batch')),
    
    -- Settlement amounts (can differ from trade amounts due to adjustments)
    gross_settlement_amount DECIMAL(15,2) NOT NULL,
    net_settlement_amount DECIMAL(15,2) NOT NULL,
    adjustment_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Settlement timing
    trade_date DATE NOT NULL,
    settlement_date DATE NOT NULL,
    value_date DATE,
    
    -- Settlement status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'instructed', 'matched', 'settled', 'failed', 'cancelled')),
    
    -- Settlement participants
    back_settlement_account VARCHAR(100),
    lay_settlement_account VARCHAR(100),
    clearing_member VARCHAR(100),
    
    -- Risk and margin
    margin_requirement DECIMAL(15,2),
    collateral_requirement DECIMAL(15,2),
    
    -- Settlement instructions
    settlement_instructions JSONB,
    
    -- Error handling
    settlement_errors TEXT[],
    retry_count INTEGER DEFAULT 0,
    
    -- External references
    clearing_reference VARCHAR(100),
    settlement_reference VARCHAR(100),
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (settlement_date >= trade_date)
);

-- Tick-by-tick price and volume data
CREATE TABLE odds_exchange.price_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Instrument reference
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    market_id UUID NOT NULL, -- Denormalized for performance
    
    -- Price data
    price DECIMAL(8,4) NOT NULL CHECK (price > 0),
    size DECIMAL(15,4) NOT NULL CHECK (size > 0),
    side VARCHAR(4) NOT NULL CHECK (side IN ('back', 'lay')),
    
    -- Trade context
    trade_id UUID, -- NULL for non-trade price updates
    is_trade BOOLEAN DEFAULT false,
    
    -- Market depth at time of update
    best_back_price DECIMAL(8,4),
    best_lay_price DECIMAL(8,4),
    spread_bps INTEGER, -- Spread in basis points
    
    -- Volume metrics
    cumulative_volume DECIMAL(15,4),
    session_volume DECIMAL(15,4),
    
    -- Price movement
    price_change DECIMAL(8,4),
    price_change_percent DECIMAL(8,6),
    
    -- Sequence and timing
    sequence_number BIGINT NOT NULL,
    tick_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Market conditions
    volatility_indicator DECIMAL(8,4),
    liquidity_score DECIMAL(6,3),
    
    -- Data source
    data_source VARCHAR(50) DEFAULT 'trading_engine',
    
    -- Minimal audit (performance optimized)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    INDEX (instrument_id, tick_timestamp DESC),
    INDEX (instrument_id, sequence_number DESC)
) PARTITION BY RANGE (tick_timestamp);

-- Create monthly partitions for price history (last 12 months + future)
CREATE TABLE odds_exchange.price_history_2024_01 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE odds_exchange.price_history_2024_02 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
CREATE TABLE odds_exchange.price_history_2024_03 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-03-01') TO ('2024-04-01');
CREATE TABLE odds_exchange.price_history_2024_04 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-04-01') TO ('2024-05-01');
CREATE TABLE odds_exchange.price_history_2024_05 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-05-01') TO ('2024-06-01');
CREATE TABLE odds_exchange.price_history_2024_06 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-06-01') TO ('2024-07-01');
CREATE TABLE odds_exchange.price_history_2024_07 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01');
CREATE TABLE odds_exchange.price_history_2024_08 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');
CREATE TABLE odds_exchange.price_history_2024_09 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');
CREATE TABLE odds_exchange.price_history_2024_10 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');
CREATE TABLE odds_exchange.price_history_2024_11 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');
CREATE TABLE odds_exchange.price_history_2024_12 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');
CREATE TABLE odds_exchange.price_history_2025_01 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE odds_exchange.price_history_2025_02 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE odds_exchange.price_history_2025_03 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE odds_exchange.price_history_2025_04 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE odds_exchange.price_history_2025_05 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE odds_exchange.price_history_2025_06 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE odds_exchange.price_history_2025_07 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE odds_exchange.price_history_2025_08 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE odds_exchange.price_history_2025_09 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE odds_exchange.price_history_2025_10 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE odds_exchange.price_history_2025_11 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE odds_exchange.price_history_2025_12 PARTITION OF odds_exchange.price_history 
    FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- Real-time market data snapshots
CREATE TABLE odds_exchange.market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market reference
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    market_id UUID NOT NULL,
    
    -- Price data
    last_traded_price DECIMAL(8,4),
    best_back_price DECIMAL(8,4),
    best_lay_price DECIMAL(8,4),
    
    -- Market depth (top 5 levels)
    back_prices DECIMAL(8,4)[5], -- Array of back prices
    back_sizes DECIMAL(15,4)[5], -- Array of back sizes
    lay_prices DECIMAL(8,4)[5],  -- Array of lay prices
    lay_sizes DECIMAL(15,4)[5],  -- Array of lay sizes
    
    -- Volume and turnover
    total_matched DECIMAL(15,2) DEFAULT 0,
    volume_24h DECIMAL(15,4) DEFAULT 0,
    turnover_24h DECIMAL(15,2) DEFAULT 0,
    
    -- Price movement
    price_change_24h DECIMAL(8,4),
    price_change_percent_24h DECIMAL(8,6),
    high_24h DECIMAL(8,4),
    low_24h DECIMAL(8,4),
    
    -- Market statistics
    spread_bps INTEGER, -- Current spread in basis points
    liquidity_score DECIMAL(6,3), -- Liquidity quality score
    volatility_index DECIMAL(8,4), -- Volatility measure
    
    -- Market state
    trading_status VARCHAR(20),
    last_trade_time TIMESTAMP WITH TIME ZONE,
    
    -- Data freshness
    data_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    stale_threshold_seconds INTEGER DEFAULT 5,
    is_stale BOOLEAN GENERATED ALWAYS AS (EXTRACT(EPOCH FROM (NOW() - data_timestamp)) > stale_threshold_seconds) STORED,
    
    -- Update tracking
    sequence_number BIGINT,
    update_source VARCHAR(50),
    
    -- Performance optimized timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Complete audit trail for compliance
CREATE TABLE odds_exchange.trade_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Trade reference
    trade_id UUID REFERENCES odds_exchange.trades(id),
    order_id UUID REFERENCES odds_exchange.orders(id),
    
    -- Audit event details
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN (
        'order_received', 'order_validated', 'order_matched', 'trade_executed',
        'settlement_initiated', 'settlement_completed', 'risk_check', 'compliance_check',
        'position_updated', 'margin_called', 'system_event', 'regulatory_report'
    )),
    
    -- Event context
    event_source VARCHAR(50) NOT NULL, -- 'matching_engine', 'risk_system', 'settlement', etc.
    event_description TEXT NOT NULL,
    
    -- Involved entities
    user_id UUID REFERENCES public.users(id),
    instrument_id UUID REFERENCES odds_exchange.instruments(id),
    market_id UUID REFERENCES odds_exchange.markets(id),
    
    -- Event data
    event_data JSONB NOT NULL,
    previous_state JSONB,
    new_state JSONB,
    
    -- Financial impact
    financial_impact DECIMAL(15,2), -- Amount affected by this event
    
    -- Regulatory compliance
    regulatory_significant BOOLEAN DEFAULT false,
    compliance_flags TEXT[],
    
    -- Risk assessment
    risk_score DECIMAL(6,3),
    risk_factors TEXT[],
    
    -- Processing metrics
    processing_time_ms DECIMAL(8,3),
    system_latency_ms DECIMAL(8,3),
    
    -- Correlation and tracing
    correlation_id VARCHAR(100), -- For tracing related events
    parent_event_id UUID REFERENCES odds_exchange.trade_audit(id),
    
    -- External references
    external_reference VARCHAR(100),
    regulatory_reference VARCHAR(100),
    
    -- Immutable timestamp
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================================
-- RISK MANAGEMENT
-- ================================================================================

-- User positions and exposures
CREATE TABLE odds_exchange.positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Position ownership
    user_id UUID NOT NULL REFERENCES public.users(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    
    -- Position details
    position_side VARCHAR(4) NOT NULL CHECK (position_side IN ('long', 'short', 'flat')),
    quantity DECIMAL(15,4) NOT NULL DEFAULT 0,
    average_price DECIMAL(8,4),
    
    -- Financial metrics
    unrealized_pnl DECIMAL(15,2) DEFAULT 0,
    realized_pnl DECIMAL(15,2) DEFAULT 0,
    total_pnl DECIMAL(15,2) GENERATED ALWAYS AS (unrealized_pnl + realized_pnl) STORED,
    
    -- Cost basis and valuation
    cost_basis DECIMAL(15,2) NOT NULL DEFAULT 0,
    market_value DECIMAL(15,2) DEFAULT 0,
    mark_to_market_price DECIMAL(8,4),
    
    -- Risk metrics
    value_at_risk_95 DECIMAL(15,2), -- 95% VaR
    maximum_drawdown DECIMAL(15,2),
    beta DECIMAL(8,4), -- Sensitivity to market movements
    
    -- Margin requirements
    initial_margin DECIMAL(15,2) DEFAULT 0 CHECK (initial_margin >= 0),
    maintenance_margin DECIMAL(15,2) DEFAULT 0 CHECK (maintenance_margin >= 0),
    variation_margin DECIMAL(15,2) DEFAULT 0,
    
    -- Position limits
    position_limit DECIMAL(15,4), -- Maximum allowed position size
    risk_limit DECIMAL(15,2),     -- Maximum allowed risk exposure
    
    -- Timestamps and lifecycle
    first_trade_date DATE,
    last_trade_date DATE,
    position_age_days INTEGER GENERATED ALWAYS AS (CURRENT_DATE - first_trade_date) STORED,
    
    -- Position flags
    is_hedged BOOLEAN DEFAULT false,
    is_high_risk BOOLEAN DEFAULT false,
    requires_margin BOOLEAN DEFAULT true,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Unique position per user/instrument
    UNIQUE(user_id, instrument_id)
);

-- Individual and aggregate risk limits
CREATE TABLE odds_exchange.risk_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Limit scope
    user_id UUID REFERENCES public.users(id), -- NULL for global limits
    instrument_id UUID REFERENCES odds_exchange.instruments(id), -- NULL for user-wide limits
    market_id UUID REFERENCES odds_exchange.markets(id), -- NULL for instrument-wide limits
    
    -- Limit classification
    limit_type VARCHAR(30) NOT NULL CHECK (limit_type IN (
        'position_limit', 'order_size_limit', 'daily_loss_limit', 'concentration_limit',
        'leverage_limit', 'margin_limit', 'exposure_limit', 'velocity_limit'
    )),
    limit_scope VARCHAR(20) DEFAULT 'user' CHECK (limit_scope IN ('user', 'instrument', 'market', 'global')),
    
    -- Limit values
    limit_value DECIMAL(15,2) NOT NULL CHECK (limit_value > 0),
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Current usage against limit
    current_usage DECIMAL(15,2) DEFAULT 0 CHECK (current_usage >= 0),
    utilization_percent DECIMAL(5,2) GENERATED ALWAYS AS ((current_usage / limit_value) * 100) STORED,
    
    -- Limit enforcement
    is_hard_limit BOOLEAN DEFAULT true, -- Hard limit blocks trades, soft limit warns
    enforcement_action VARCHAR(20) DEFAULT 'block' CHECK (enforcement_action IN ('block', 'warn', 'reduce', 'close')),
    
    -- Limit timing
    effective_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    effective_until TIMESTAMP WITH TIME ZONE,
    reset_frequency VARCHAR(20) CHECK (reset_frequency IN ('never', 'daily', 'weekly', 'monthly')),
    last_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Breach handling
    breach_count INTEGER DEFAULT 0,
    last_breach TIMESTAMP WITH TIME ZONE,
    consecutive_breaches INTEGER DEFAULT 0,
    
    -- Limit metadata
    description TEXT,
    justification TEXT,
    limit_data JSONB DEFAULT '{}',
    
    -- Approval workflow
    approved_by UUID REFERENCES public.users(id),
    approval_date TIMESTAMP WITH TIME ZONE,
    requires_approval BOOLEAN DEFAULT true,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Constraints
    CHECK (user_id IS NOT NULL OR limit_scope != 'user'),
    CHECK (instrument_id IS NOT NULL OR limit_scope != 'instrument'),
    CHECK (market_id IS NOT NULL OR limit_scope != 'market')
);

-- Collateral and margin calculations
CREATE TABLE odds_exchange.margin_requirements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Margin ownership
    user_id UUID NOT NULL REFERENCES public.users(id),
    position_id UUID REFERENCES odds_exchange.positions(id), -- NULL for portfolio-level margin
    instrument_id UUID REFERENCES odds_exchange.instruments(id),
    
    -- Margin calculation
    margin_type VARCHAR(30) NOT NULL CHECK (margin_type IN (
        'initial_margin', 'maintenance_margin', 'variation_margin', 'additional_margin',
        'concentration_margin', 'liquidity_margin', 'stress_margin'
    )),
    
    -- Margin amounts
    required_margin DECIMAL(15,2) NOT NULL CHECK (required_margin >= 0),
    posted_margin DECIMAL(15,2) DEFAULT 0 CHECK (posted_margin >= 0),
    margin_deficit DECIMAL(15,2) GENERATED ALWAYS AS (
        GREATEST(required_margin - posted_margin, 0)
    ) STORED,
    
    -- Margin calculation details
    calculation_method VARCHAR(50) NOT NULL,
    risk_factor DECIMAL(8,4) DEFAULT 1.0,
    volatility_factor DECIMAL(8,4) DEFAULT 1.0,
    concentration_factor DECIMAL(8,4) DEFAULT 1.0,
    
    -- Collateral information
    collateral_type VARCHAR(50) DEFAULT 'cash',
    collateral_value DECIMAL(15,2) DEFAULT 0,
    haircut_percentage DECIMAL(5,4) DEFAULT 0, -- Collateral discount
    effective_collateral DECIMAL(15,2) GENERATED ALWAYS AS (
        collateral_value * (1 - haircut_percentage)
    ) STORED,
    
    -- Margin call information
    margin_call_triggered BOOLEAN DEFAULT false,
    margin_call_amount DECIMAL(15,2),
    margin_call_deadline TIMESTAMP WITH TIME ZONE,
    
    -- Calculation timestamp and validity
    calculation_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    is_current BOOLEAN DEFAULT true,
    
    -- Risk parameters used in calculation
    confidence_level DECIMAL(5,4) DEFAULT 0.99, -- 99% confidence for VaR
    holding_period_days INTEGER DEFAULT 1,
    calculation_parameters JSONB,
    
    -- External references
    collateral_transaction_id UUID, -- Reference to collateral deposit transaction
    margin_call_id UUID,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Real-time risk monitoring and alerts
CREATE TABLE odds_exchange.risk_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Alert target
    user_id UUID REFERENCES public.users(id),
    position_id UUID REFERENCES odds_exchange.positions(id),
    order_id UUID REFERENCES odds_exchange.orders(id),
    
    -- Alert classification
    alert_type VARCHAR(30) NOT NULL CHECK (alert_type IN (
        'margin_call', 'position_limit_breach', 'loss_limit_breach', 'concentration_risk',
        'liquidity_risk', 'market_risk', 'credit_risk', 'operational_risk'
    )),
    severity VARCHAR(10) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Alert details
    alert_message TEXT NOT NULL,
    risk_description TEXT NOT NULL,
    recommended_action TEXT,
    
    -- Risk metrics
    risk_score DECIMAL(6,3) NOT NULL CHECK (risk_score >= 0),
    impact_amount DECIMAL(15,2),
    probability DECIMAL(5,4) CHECK (probability BETWEEN 0 AND 1),
    
    -- Alert status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'suppressed')),
    escalated BOOLEAN DEFAULT false,
    escalation_level INTEGER DEFAULT 1,
    
    -- Response tracking
    acknowledged_by UUID REFERENCES public.users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES public.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    
    -- Alert context
    triggering_event JSONB,
    market_conditions JSONB,
    user_context JSONB,
    
    -- Auto-resolution
    auto_resolve BOOLEAN DEFAULT false,
    auto_resolve_condition TEXT,
    
    -- Notification tracking
    notifications_sent INTEGER DEFAULT 0,
    last_notification TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields (simplified for performance)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    INDEX (user_id, status, created_at DESC),
    INDEX (severity, status, created_at DESC)
);

-- Pre-trade risk validation results
CREATE TABLE odds_exchange.credit_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Check context
    user_id UUID NOT NULL REFERENCES public.users(id),
    order_id UUID REFERENCES odds_exchange.orders(id), -- NULL for portfolio checks
    instrument_id UUID REFERENCES odds_exchange.instruments(id),
    
    -- Check type and scope
    check_type VARCHAR(30) NOT NULL CHECK (check_type IN (
        'pre_trade', 'position_check', 'margin_check', 'limit_check',
        'credit_check', 'compliance_check', 'portfolio_check'
    )),
    check_scope VARCHAR(20) DEFAULT 'order' CHECK (check_scope IN ('order', 'position', 'portfolio', 'user')),
    
    -- Check results
    check_result VARCHAR(20) NOT NULL CHECK (check_result IN ('pass', 'fail', 'warning', 'conditional')),
    risk_score DECIMAL(6,3) CHECK (risk_score >= 0),
    
    -- Check details
    checks_performed TEXT[] NOT NULL,
    failed_checks TEXT[],
    warnings TEXT[],
    
    -- Financial analysis
    available_balance DECIMAL(15,2),
    required_margin DECIMAL(15,2),
    position_limit_usage DECIMAL(5,2),
    credit_limit_usage DECIMAL(5,2),
    
    -- Check parameters
    check_parameters JSONB,
    market_conditions JSONB,
    
    -- Performance metrics
    check_duration_ms DECIMAL(8,3) NOT NULL,
    
    -- Override information
    override_applied BOOLEAN DEFAULT false,
    override_reason TEXT,
    override_by UUID REFERENCES public.users(id),
    
    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================================
-- Schema creation completion part 2
-- ================================================================================

-- ================================================================================
-- MARKET MAKING & LIQUIDITY PROVISION
-- ================================================================================

-- Market maker registration and management
CREATE TABLE odds_exchange.market_makers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market maker identification
    user_id UUID NOT NULL REFERENCES public.users(id),
    firm_name VARCHAR(200) NOT NULL,
    registration_number VARCHAR(100),
    market_maker_code VARCHAR(20) UNIQUE NOT NULL,
    
    -- Authorization and status
    authorization_status VARCHAR(20) DEFAULT 'pending' CHECK (authorization_status IN ('pending', 'approved', 'suspended', 'revoked')),
    authorized_markets TEXT[], -- Array of market categories they can make
    authorization_date DATE,
    authorized_by UUID REFERENCES public.users(id),
    
    -- Capital and risk requirements
    minimum_capital_requirement DECIMAL(15,2) NOT NULL CHECK (minimum_capital_requirement > 0),
    current_capital DECIMAL(15,2) DEFAULT 0 CHECK (current_capital >= 0),
    risk_limit DECIMAL(15,2) NOT NULL CHECK (risk_limit > 0),
    
    -- Performance requirements
    minimum_uptime_percent DECIMAL(5,2) DEFAULT 95.0 CHECK (minimum_uptime_percent BETWEEN 0 AND 100),
    minimum_spread_bps INTEGER DEFAULT 50 CHECK (minimum_spread_bps > 0), -- Minimum spread in basis points
    maximum_quote_time_ms INTEGER DEFAULT 100 CHECK (maximum_quote_time_ms > 0),
    
    -- Market making obligations
    continuous_quoting_required BOOLEAN DEFAULT true,
    minimum_size_obligation DECIMAL(15,4) DEFAULT 100.0,
    maximum_spread_obligation DECIMAL(8,4) DEFAULT 0.05, -- Maximum allowed spread
    
    -- Fee structure
    maker_fee_rate DECIMAL(6,4) DEFAULT 0.001, -- 0.1% maker fee
    rebate_rate DECIMAL(6,4) DEFAULT 0.0005,   -- 0.05% rebate for providing liquidity
    
    -- Performance tracking
    total_volume_provided DECIMAL(15,2) DEFAULT 0,
    average_spread_bps INTEGER DEFAULT 0,
    uptime_percent DECIMAL(5,2) DEFAULT 100.0,
    quote_response_time_ms DECIMAL(8,3) DEFAULT 0,
    
    -- Contact and compliance
    contact_email VARCHAR(255) NOT NULL,
    compliance_officer VARCHAR(200),
    regulatory_identifiers JSONB,
    
    -- Status flags
    is_active BOOLEAN DEFAULT true,
    accepts_retail_flow BOOLEAN DEFAULT true,
    accepts_institutional_flow BOOLEAN DEFAULT true,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Market making strategies and configurations
CREATE TABLE odds_exchange.mm_strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Strategy ownership
    market_maker_id UUID NOT NULL REFERENCES odds_exchange.market_makers(id),
    instrument_id UUID REFERENCES odds_exchange.instruments(id), -- NULL for universal strategies
    market_id UUID REFERENCES odds_exchange.markets(id),
    
    -- Strategy identification
    strategy_name VARCHAR(100) NOT NULL,
    strategy_type VARCHAR(30) NOT NULL CHECK (strategy_type IN (
        'delta_neutral', 'directional', 'arbitrage', 'statistical', 
        'momentum', 'mean_reversion', 'volatility', 'correlation'
    )),
    
    -- Strategy parameters
    target_spread_bps INTEGER NOT NULL CHECK (target_spread_bps > 0),
    max_position_size DECIMAL(15,4) NOT NULL CHECK (max_position_size > 0),
    max_inventory_risk DECIMAL(15,2) NOT NULL CHECK (max_inventory_risk > 0),
    
    -- Pricing parameters
    fair_value_model VARCHAR(50) DEFAULT 'mid_price',
    volatility_adjustment DECIMAL(8,4) DEFAULT 1.0,
    inventory_skew_factor DECIMAL(8,4) DEFAULT 0.1,
    adverse_selection_adjustment DECIMAL(8,4) DEFAULT 0.05,
    
    -- Risk controls
    max_daily_loss DECIMAL(15,2),
    position_limit DECIMAL(15,4),
    concentration_limit DECIMAL(5,4), -- Maximum % of total market position
    
    -- Timing and frequency
    quote_frequency_ms INTEGER DEFAULT 1000 CHECK (quote_frequency_ms >= 100),
    min_time_between_quotes_ms INTEGER DEFAULT 50,
    max_quote_age_seconds INTEGER DEFAULT 5,
    
    -- Strategy state
    is_active BOOLEAN DEFAULT true,
    is_auto_trading BOOLEAN DEFAULT false,
    requires_manual_approval BOOLEAN DEFAULT false,
    
    -- Performance tracking
    total_quotes_sent INTEGER DEFAULT 0,
    successful_quotes INTEGER DEFAULT 0,
    total_fills INTEGER DEFAULT 0,
    pnl_today DECIMAL(15,2) DEFAULT 0,
    pnl_total DECIMAL(15,2) DEFAULT 0,
    
    -- Configuration
    strategy_config JSONB DEFAULT '{}',
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Real-time market maker quotes (optimized for ultra-fast updates)
CREATE TABLE odds_exchange.mm_quotes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Quote context
    market_maker_id UUID NOT NULL REFERENCES odds_exchange.market_makers(id),
    strategy_id UUID REFERENCES odds_exchange.mm_strategies(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    
    -- Quote prices and sizes
    bid_price DECIMAL(8,4) NOT NULL CHECK (bid_price > 0),
    ask_price DECIMAL(8,4) NOT NULL CHECK (ask_price >= bid_price),
    bid_size DECIMAL(15,4) NOT NULL CHECK (bid_size > 0),
    ask_size DECIMAL(15,4) NOT NULL CHECK (ask_size > 0),
    
    -- Quote metadata
    quote_sequence BIGINT NOT NULL, -- Monotonically increasing sequence
    quote_status VARCHAR(20) DEFAULT 'active' CHECK (quote_status IN ('active', 'withdrawn', 'executed', 'expired')),
    
    -- Pricing context
    fair_value DECIMAL(8,4) NOT NULL,
    spread_bps INTEGER GENERATED ALWAYS AS (
        ROUND(((ask_price - bid_price) / ((bid_price + ask_price) / 2)) * 10000)
    ) STORED,
    
    -- Risk and inventory context
    current_inventory DECIMAL(15,4) DEFAULT 0,
    inventory_risk DECIMAL(15,2) DEFAULT 0,
    max_fill_size DECIMAL(15,4) NOT NULL,
    
    -- Market conditions
    market_volatility DECIMAL(8,4),
    liquidity_score DECIMAL(6,3),
    market_pressure VARCHAR(10) CHECK (market_pressure IN ('buying', 'selling', 'neutral')),
    
    -- Timing and lifecycle
    quote_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance tracking
    quote_latency_ms DECIMAL(8,3), -- Time to generate quote
    market_impact DECIMAL(8,4),    -- Expected market impact
    
    -- External integration
    external_quote_id VARCHAR(100),
    
    -- Minimal audit for performance
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY HASH (instrument_id);

-- Create partitions for market maker quotes (8 partitions for performance)
CREATE TABLE odds_exchange.mm_quotes_p0 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 0);
CREATE TABLE odds_exchange.mm_quotes_p1 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 1);
CREATE TABLE odds_exchange.mm_quotes_p2 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 2);
CREATE TABLE odds_exchange.mm_quotes_p3 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 3);
CREATE TABLE odds_exchange.mm_quotes_p4 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 4);
CREATE TABLE odds_exchange.mm_quotes_p5 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 5);
CREATE TABLE odds_exchange.mm_quotes_p6 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 6);
CREATE TABLE odds_exchange.mm_quotes_p7 PARTITION OF odds_exchange.mm_quotes FOR VALUES WITH (MODULUS 8, REMAINDER 7);

-- Market maker performance tracking
CREATE TABLE odds_exchange.mm_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Performance scope
    market_maker_id UUID NOT NULL REFERENCES odds_exchange.market_makers(id),
    instrument_id UUID REFERENCES odds_exchange.instruments(id), -- NULL for overall performance
    
    -- Time period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('real_time', 'hourly', 'daily', 'weekly', 'monthly')),
    
    -- Volume and activity metrics
    quotes_sent INTEGER DEFAULT 0 CHECK (quotes_sent >= 0),
    quotes_filled INTEGER DEFAULT 0 CHECK (quotes_filled >= 0),
    fill_rate DECIMAL(5,4) GENERATED ALWAYS AS (
        CASE WHEN quotes_sent > 0 THEN quotes_filled::DECIMAL / quotes_sent ELSE 0 END
    ) STORED,
    
    -- Volume metrics
    total_volume DECIMAL(15,2) DEFAULT 0 CHECK (total_volume >= 0),
    buy_volume DECIMAL(15,2) DEFAULT 0 CHECK (buy_volume >= 0),
    sell_volume DECIMAL(15,2) DEFAULT 0 CHECK (sell_volume >= 0),
    
    -- Spread and pricing metrics
    average_spread_bps DECIMAL(8,2) DEFAULT 0,
    weighted_spread_bps DECIMAL(8,2) DEFAULT 0,
    time_at_nbbo_percent DECIMAL(5,2) DEFAULT 0, -- Time at National Best Bid Offer
    
    -- Uptime and availability
    uptime_seconds INTEGER DEFAULT 0,
    total_seconds INTEGER DEFAULT 0,
    uptime_percent DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE WHEN total_seconds > 0 THEN (uptime_seconds::DECIMAL / total_seconds) * 100 ELSE 0 END
    ) STORED,
    
    -- Response time metrics
    avg_quote_response_time_ms DECIMAL(8,3) DEFAULT 0,
    p95_quote_response_time_ms DECIMAL(8,3) DEFAULT 0,
    p99_quote_response_time_ms DECIMAL(8,3) DEFAULT 0,
    
    -- Financial performance
    gross_pnl DECIMAL(15,2) DEFAULT 0,
    net_pnl DECIMAL(15,2) DEFAULT 0,
    fees_earned DECIMAL(15,2) DEFAULT 0,
    rebates_received DECIMAL(15,2) DEFAULT 0,
    
    -- Risk metrics
    max_inventory DECIMAL(15,4) DEFAULT 0,
    avg_inventory DECIMAL(15,4) DEFAULT 0,
    inventory_turnover DECIMAL(8,2) DEFAULT 0,
    var_95 DECIMAL(15,2), -- 95% Value at Risk
    
    -- Quality metrics
    adverse_selection_cost DECIMAL(15,2) DEFAULT 0,
    market_impact_cost DECIMAL(15,2) DEFAULT 0,
    opportunity_cost DECIMAL(15,2) DEFAULT 0,
    
    -- Compliance metrics
    obligation_breaches INTEGER DEFAULT 0,
    regulatory_violations INTEGER DEFAULT 0,
    
    -- Performance flags
    meets_obligations BOOLEAN DEFAULT true,
    exceeds_benchmarks BOOLEAN DEFAULT false,
    
    -- Minimal audit for performance
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================================
-- PORTFOLIO MANAGEMENT & ANALYTICS
-- ================================================================================

-- User portfolio summaries and analytics
CREATE TABLE odds_exchange.portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Portfolio ownership
    user_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Portfolio identification
    portfolio_name VARCHAR(100) NOT NULL,
    portfolio_type VARCHAR(20) DEFAULT 'trading' CHECK (portfolio_type IN ('trading', 'investment', 'hedging', 'arbitrage')),
    base_currency VARCHAR(3) DEFAULT 'USD',
    
    -- Portfolio valuation
    total_value DECIMAL(15,2) DEFAULT 0 CHECK (total_value >= 0),
    cash_balance DECIMAL(15,2) DEFAULT 0,
    invested_value DECIMAL(15,2) DEFAULT 0,
    unrealized_pnl DECIMAL(15,2) DEFAULT 0,
    realized_pnl DECIMAL(15,2) DEFAULT 0,
    
    -- Risk metrics
    portfolio_beta DECIMAL(8,4) DEFAULT 1.0,
    sharpe_ratio DECIMAL(8,4),
    sortino_ratio DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    volatility DECIMAL(8,4),
    
    -- Performance metrics
    total_return DECIMAL(8,4),
    annualized_return DECIMAL(8,4),
    win_rate DECIMAL(5,4),
    profit_factor DECIMAL(8,4),
    
    -- Allocation and diversification
    position_count INTEGER DEFAULT 0,
    market_concentration DECIMAL(5,4), -- Largest position as % of portfolio
    sector_concentration DECIMAL(5,4),
    
    -- Portfolio limits and controls
    max_portfolio_value DECIMAL(15,2),
    max_position_size DECIMAL(15,2),
    max_daily_loss DECIMAL(15,2),
    risk_tolerance VARCHAR(20) DEFAULT 'moderate' CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive', 'high_risk')),
    
    -- Portfolio status
    is_active BOOLEAN DEFAULT true,
    is_managed BOOLEAN DEFAULT false, -- Professional management
    manager_id UUID REFERENCES public.users(id),
    
    -- Benchmarking
    benchmark_index VARCHAR(50),
    benchmark_return DECIMAL(8,4),
    tracking_error DECIMAL(8,4),
    information_ratio DECIMAL(8,4),
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id),
    
    -- Unique portfolio per user
    UNIQUE(user_id, portfolio_name)
);

-- Portfolio position allocations
CREATE TABLE odds_exchange.portfolio_allocations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Allocation context
    portfolio_id UUID NOT NULL REFERENCES odds_exchange.portfolios(id),
    position_id UUID NOT NULL REFERENCES odds_exchange.positions(id),
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    
    -- Allocation details
    allocation_percent DECIMAL(5,4) NOT NULL CHECK (allocation_percent BETWEEN 0 AND 1),
    target_allocation DECIMAL(5,4) NOT NULL CHECK (target_allocation BETWEEN 0 AND 1),
    allocation_drift DECIMAL(5,4) GENERATED ALWAYS AS (allocation_percent - target_allocation) STORED,
    
    -- Position sizing
    position_value DECIMAL(15,2) NOT NULL CHECK (position_value >= 0),
    position_weight DECIMAL(5,4) NOT NULL,
    
    -- Risk contribution
    risk_contribution DECIMAL(8,4), -- Contribution to portfolio risk
    beta_contribution DECIMAL(8,4),
    var_contribution DECIMAL(15,2),
    
    -- Rebalancing
    last_rebalanced TIMESTAMP WITH TIME ZONE,
    rebalance_threshold DECIMAL(5,4) DEFAULT 0.05, -- 5% drift threshold
    needs_rebalancing BOOLEAN GENERATED ALWAYS AS (
        ABS(allocation_drift) > rebalance_threshold
    ) STORED,
    
    -- Performance attribution
    contribution_to_return DECIMAL(8,4),
    active_return DECIMAL(8,4),
    
    -- Allocation constraints
    min_allocation DECIMAL(5,4) DEFAULT 0,
    max_allocation DECIMAL(5,4) DEFAULT 1,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Unique allocation per portfolio/position
    UNIQUE(portfolio_id, position_id)
);

-- Technical analysis indicators and signals
CREATE TABLE odds_exchange.technical_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Indicator context
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    indicator_name VARCHAR(50) NOT NULL,
    indicator_type VARCHAR(30) NOT NULL CHECK (indicator_type IN (
        'trend', 'momentum', 'volatility', 'volume', 'support_resistance', 'oscillator'
    )),
    
    -- Time frame
    timeframe VARCHAR(20) NOT NULL CHECK (timeframe IN ('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w')),
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Indicator values
    indicator_value DECIMAL(12,6) NOT NULL,
    previous_value DECIMAL(12,6),
    change_percent DECIMAL(8,4),
    
    -- Signal information
    signal_type VARCHAR(20) CHECK (signal_type IN ('buy', 'sell', 'hold', 'neutral', 'strong_buy', 'strong_sell')),
    signal_strength DECIMAL(5,4) CHECK (signal_strength BETWEEN 0 AND 1),
    confidence_level DECIMAL(5,4) CHECK (confidence_level BETWEEN 0 AND 1),
    
    -- Technical levels
    support_level DECIMAL(8,4),
    resistance_level DECIMAL(8,4),
    pivot_point DECIMAL(8,4),
    
    -- Indicator parameters
    calculation_parameters JSONB,
    lookback_periods INTEGER,
    
    -- Market context
    market_price DECIMAL(8,4) NOT NULL,
    volume DECIMAL(15,4),
    volatility DECIMAL(8,4),
    
    -- Alert triggers
    alert_triggered BOOLEAN DEFAULT false,
    alert_condition TEXT,
    
    -- Data quality
    data_quality_score DECIMAL(3,2) CHECK (data_quality_score BETWEEN 0 AND 1),
    calculation_accuracy DECIMAL(5,4),
    
    -- Performance optimized timestamps
    calculation_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (calculation_time);

-- Create monthly partitions for technical indicators (12 months)
CREATE TABLE odds_exchange.technical_indicators_2025_01 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE odds_exchange.technical_indicators_2025_02 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE odds_exchange.technical_indicators_2025_03 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE odds_exchange.technical_indicators_2025_04 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE odds_exchange.technical_indicators_2025_05 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE odds_exchange.technical_indicators_2025_06 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE odds_exchange.technical_indicators_2025_07 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE odds_exchange.technical_indicators_2025_08 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE odds_exchange.technical_indicators_2025_09 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE odds_exchange.technical_indicators_2025_10 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE odds_exchange.technical_indicators_2025_11 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE odds_exchange.technical_indicators_2025_12 PARTITION OF odds_exchange.technical_indicators 
    FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- Chart data aggregation for various timeframes
CREATE TABLE odds_exchange.chart_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Chart context
    instrument_id UUID NOT NULL REFERENCES odds_exchange.instruments(id),
    timeframe VARCHAR(20) NOT NULL CHECK (timeframe IN ('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M')),
    
    -- Time period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- OHLCV data
    open_price DECIMAL(8,4) NOT NULL CHECK (open_price > 0),
    high_price DECIMAL(8,4) NOT NULL CHECK (high_price >= open_price),
    low_price DECIMAL(8,4) NOT NULL CHECK (low_price <= open_price),
    close_price DECIMAL(8,4) NOT NULL CHECK (close_price > 0),
    volume DECIMAL(15,4) NOT NULL CHECK (volume >= 0),
    
    -- Additional metrics
    vwap DECIMAL(8,4), -- Volume Weighted Average Price
    number_of_trades INTEGER DEFAULT 0,
    turnover DECIMAL(15,2) DEFAULT 0,
    
    -- Price movement
    price_change DECIMAL(8,4) GENERATED ALWAYS AS (close_price - open_price) STORED,
    price_change_percent DECIMAL(8,6) GENERATED ALWAYS AS (
        CASE WHEN open_price > 0 THEN ((close_price - open_price) / open_price) * 100 ELSE 0 END
    ) STORED,
    
    -- Volatility measures
    true_range DECIMAL(8,4),
    volatility DECIMAL(8,4),
    
    -- Market microstructure
    bid_ask_spread_avg DECIMAL(8,4),
    trade_imbalance DECIMAL(8,4), -- Buy volume - Sell volume
    
    -- Data quality
    data_completeness DECIMAL(3,2) DEFAULT 1.0 CHECK (data_completeness BETWEEN 0 AND 1),
    tick_count INTEGER DEFAULT 0,
    
    -- Performance optimized
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for period
    UNIQUE(instrument_id, timeframe, period_start)
) PARTITION BY RANGE (period_start);

-- Create quarterly partitions for chart data (8 quarters for 2 years)
CREATE TABLE odds_exchange.chart_data_2024_q1 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');
CREATE TABLE odds_exchange.chart_data_2024_q2 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2024-04-01') TO ('2024-07-01');
CREATE TABLE odds_exchange.chart_data_2024_q3 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2024-07-01') TO ('2024-10-01');
CREATE TABLE odds_exchange.chart_data_2024_q4 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2024-10-01') TO ('2025-01-01');
CREATE TABLE odds_exchange.chart_data_2025_q1 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');
CREATE TABLE odds_exchange.chart_data_2025_q2 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2025-04-01') TO ('2025-07-01');
CREATE TABLE odds_exchange.chart_data_2025_q3 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2025-07-01') TO ('2025-10-01');
CREate TABLE odds_exchange.chart_data_2025_q4 PARTITION OF odds_exchange.chart_data 
    FOR VALUES FROM ('2025-10-01') TO ('2026-01-01');

DO $$
BEGIN
    RAISE NOTICE 'Odds Exchange & Trading Schema - COMPLETE';
    RAISE NOTICE '✅ Core trading engine with ultra-low latency order matching';
    RAISE NOTICE '✅ Risk management with real-time position tracking and margin calls';
    RAISE NOTICE '✅ Market making framework with performance obligations';
    RAISE NOTICE '✅ Portfolio management with advanced analytics';
    RAISE NOTICE '✅ Technical analysis with partitioned indicator storage';
    RAISE NOTICE '✅ Partitioned data architecture for maximum performance';
    RAISE NOTICE '';
    RAISE NOTICE 'Next Steps: Create performance indexes and stored procedures';
END;
$$;