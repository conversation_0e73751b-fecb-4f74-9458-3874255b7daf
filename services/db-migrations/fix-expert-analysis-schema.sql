-- Fix Expert Analysis Schema to Match Service Models
-- =================================================

-- Drop existing schema and recreate with correct structure
DROP SCHEMA IF EXISTS expert_analysis CASCADE;
CREATE SCHEMA expert_analysis;

-- Expert table (exact match to service model)
CREATE TABLE expert_analysis.experts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    bio TEXT,
    expertise_areas VARCHAR(50)[], -- Array of expertise areas
    rating NUMERIC(3,2) DEFAULT 0,
    total_picks INTEGER DEFAULT 0,
    successful_picks INTEGER DEFAULT 0,
    roi_percentage NUMERIC(6,2) DEFAULT 0,
    subscription_price NUMERIC(10,2),
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Pick table (exact match to service model)
CREATE TABLE expert_analysis.picks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES expert_analysis.experts(id),
    title VARCHAR(300) NOT NULL,
    description TEXT,
    sport VARCHAR(50),
    confidence_level INTEGER, -- 1-5
    odds NUMERIC(6,3),
    stake_recommendation NUMERIC(5,2),
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_experts_user_id ON expert_analysis.experts(user_id);
CREATE INDEX idx_experts_is_verified ON expert_analysis.experts(is_verified);
CREATE INDEX idx_experts_roi_percentage ON expert_analysis.experts(roi_percentage);
CREATE INDEX idx_experts_rating ON expert_analysis.experts(rating);
CREATE INDEX idx_experts_expertise_areas ON expert_analysis.experts USING GIN(expertise_areas);

CREATE INDEX idx_picks_expert_id ON expert_analysis.picks(expert_id);
CREATE INDEX idx_picks_sport ON expert_analysis.picks(sport);
CREATE INDEX idx_picks_status ON expert_analysis.picks(status);
CREATE INDEX idx_picks_created_at ON expert_analysis.picks(created_at);
CREATE INDEX idx_picks_confidence_level ON expert_analysis.picks(confidence_level);