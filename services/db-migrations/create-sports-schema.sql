-- Create Sports Analysis Schema for BetBet Platform
-- =================================================

-- Create sports_analysis schema
CREATE SCHEMA IF NOT EXISTS sports_analysis;

-- Sports events and data
CREATE TABLE sports_analysis.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    external_id VARCHAR(100) UNIQUE,
    sport VARCHAR(50) NOT NULL,
    league VARCHAR(100),
    home_team VARCHAR(200) NOT NULL,
    away_team VARCHAR(200) NOT NULL,
    event_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'finished', 'postponed', 'cancelled')),
    home_score INTEGER,
    away_score INTEGER,
    venue VARCHAR(200),
    weather_conditions JSONB,
    referee VARCHAR(100),
    attendance INTEGER,
    live_stats JSONB DEFAULT '{}',
    final_stats JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI predictions for events
CREATE TABLE sports_analysis.predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES sports_analysis.events(id),
    prediction_type VARCHAR(50) NOT NULL CHECK (prediction_type IN ('match_result', 'total_goals', 'handicap', 'both_teams_score')),
    predicted_outcome VARCHAR(200) NOT NULL,
    confidence_score DECIMAL(5,4) NOT NULL CHECK (confidence_score BETWEEN 0 AND 1),
    suggested_odds DECIMAL(8,2),
    model_version VARCHAR(50),
    input_features JSONB DEFAULT '{}',
    actual_outcome VARCHAR(200),
    is_correct BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Analysis reports
CREATE TABLE sports_analysis.analysis_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID REFERENCES sports_analysis.events(id),
    report_type VARCHAR(50) NOT NULL CHECK (report_type IN ('pre_match', 'live', 'post_match')),
    title VARCHAR(200) NOT NULL,
    summary TEXT NOT NULL,
    detailed_analysis TEXT,
    key_insights TEXT[],
    recommended_bets JSONB DEFAULT '[]',
    data_sources TEXT[],
    analyst_notes TEXT,
    confidence_rating INTEGER CHECK (confidence_rating BETWEEN 1 AND 10),
    is_premium BOOLEAN DEFAULT false,
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Real-time streaming content
CREATE TABLE sports_analysis.streaming_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID REFERENCES sports_analysis.events(id),
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('live_commentary', 'stats_update', 'highlight', 'injury_news')),
    title VARCHAR(200),
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 5),
    tags VARCHAR(50)[],
    metadata JSONB DEFAULT '{}'
);

-- Chat completion history
CREATE TABLE sports_analysis.chat_completions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    query TEXT NOT NULL,
    response TEXT NOT NULL,
    context JSONB DEFAULT '{}',
    response_time_ms INTEGER,
    tokens_used INTEGER,
    model_used VARCHAR(50),
    session_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Team and player statistics
CREATE TABLE sports_analysis.team_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_name VARCHAR(200) NOT NULL,
    sport VARCHAR(50) NOT NULL,
    league VARCHAR(100),
    season VARCHAR(20) NOT NULL,
    games_played INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    draws INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    goals_for INTEGER DEFAULT 0,
    goals_against INTEGER DEFAULT 0,
    points INTEGER DEFAULT 0,
    home_record JSONB DEFAULT '{}',
    away_record JSONB DEFAULT '{}',
    recent_form VARCHAR(20),
    detailed_stats JSONB DEFAULT '{}',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(team_name, sport, league, season)
);

-- Player statistics
CREATE TABLE sports_analysis.player_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_name VARCHAR(200) NOT NULL,
    team_name VARCHAR(200) NOT NULL,
    sport VARCHAR(50) NOT NULL,
    position VARCHAR(50),
    season VARCHAR(20) NOT NULL,
    games_played INTEGER DEFAULT 0,
    goals INTEGER DEFAULT 0,
    assists INTEGER DEFAULT 0,
    yellow_cards INTEGER DEFAULT 0,
    red_cards INTEGER DEFAULT 0,
    detailed_stats JSONB DEFAULT '{}',
    market_value DECIMAL(15,2),
    injury_status VARCHAR(100),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(player_name, team_name, sport, season)
);

-- Model performance tracking
CREATE TABLE sports_analysis.model_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    prediction_type VARCHAR(50) NOT NULL,
    test_date DATE NOT NULL,
    total_predictions INTEGER NOT NULL,
    correct_predictions INTEGER NOT NULL,
    accuracy DECIMAL(5,4) NOT NULL,
    precision_score DECIMAL(5,4),
    recall_score DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    roc_auc DECIMAL(5,4),
    profit_loss DECIMAL(15,2),
    roi DECIMAL(8,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(model_name, model_version, prediction_type, test_date)
);

-- User preferences for sports analysis
CREATE TABLE sports_analysis.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    favorite_sports VARCHAR(50)[],
    favorite_teams VARCHAR(200)[],
    preferred_leagues VARCHAR(100)[],
    notification_settings JSONB DEFAULT '{}',
    analysis_level VARCHAR(20) DEFAULT 'standard' CHECK (analysis_level IN ('basic', 'standard', 'advanced', 'expert')),
    auto_follow_predictions BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_events_sport ON sports_analysis.events(sport);
CREATE INDEX idx_events_league ON sports_analysis.events(league);
CREATE INDEX idx_events_event_date ON sports_analysis.events(event_date);
CREATE INDEX idx_events_status ON sports_analysis.events(status);
CREATE INDEX idx_events_external_id ON sports_analysis.events(external_id);

CREATE INDEX idx_predictions_event_id ON sports_analysis.predictions(event_id);
CREATE INDEX idx_predictions_prediction_type ON sports_analysis.predictions(prediction_type);
CREATE INDEX idx_predictions_confidence_score ON sports_analysis.predictions(confidence_score);
CREATE INDEX idx_predictions_created_at ON sports_analysis.predictions(created_at);

CREATE INDEX idx_analysis_reports_event_id ON sports_analysis.analysis_reports(event_id);
CREATE INDEX idx_analysis_reports_report_type ON sports_analysis.analysis_reports(report_type);
CREATE INDEX idx_analysis_reports_is_premium ON sports_analysis.analysis_reports(is_premium);
CREATE INDEX idx_analysis_reports_created_at ON sports_analysis.analysis_reports(created_at);

CREATE INDEX idx_streaming_content_event_id ON sports_analysis.streaming_content(event_id);
CREATE INDEX idx_streaming_content_content_type ON sports_analysis.streaming_content(content_type);
CREATE INDEX idx_streaming_content_timestamp ON sports_analysis.streaming_content(timestamp);
CREATE INDEX idx_streaming_content_priority ON sports_analysis.streaming_content(priority);

CREATE INDEX idx_chat_completions_user_id ON sports_analysis.chat_completions(user_id);
CREATE INDEX idx_chat_completions_session_id ON sports_analysis.chat_completions(session_id);
CREATE INDEX idx_chat_completions_created_at ON sports_analysis.chat_completions(created_at);

CREATE INDEX idx_team_stats_team_name ON sports_analysis.team_stats(team_name);
CREATE INDEX idx_team_stats_sport_league ON sports_analysis.team_stats(sport, league);
CREATE INDEX idx_team_stats_season ON sports_analysis.team_stats(season);

CREATE INDEX idx_player_stats_player_name ON sports_analysis.player_stats(player_name);
CREATE INDEX idx_player_stats_team_name ON sports_analysis.player_stats(team_name);
CREATE INDEX idx_player_stats_sport ON sports_analysis.player_stats(sport);
CREATE INDEX idx_player_stats_season ON sports_analysis.player_stats(season);

CREATE INDEX idx_model_performance_model_name ON sports_analysis.model_performance(model_name);
CREATE INDEX idx_model_performance_prediction_type ON sports_analysis.model_performance(prediction_type);
CREATE INDEX idx_model_performance_test_date ON sports_analysis.model_performance(test_date);

CREATE INDEX idx_user_preferences_user_id ON sports_analysis.user_preferences(user_id);

-- Update triggers
CREATE TRIGGER update_events_updated_at 
    BEFORE UPDATE ON sports_analysis.events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_predictions_updated_at 
    BEFORE UPDATE ON sports_analysis.predictions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analysis_reports_updated_at 
    BEFORE UPDATE ON sports_analysis.analysis_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON sports_analysis.user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();