-- BetBet Expert Analysis Database Schema
-- ======================================

\c betbet_experts;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Expert profiles
CREATE TABLE experts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE, -- References users.id from main DB
    display_name VARCHAR(100) NOT NULL,
    bio TEXT,
    avatar_url TEXT,
    specialties TEXT[], -- Array of sports/categories they specialize in
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    verification_status VARCHAR(20) DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'rejected')),
    tier VARCHAR(20) DEFAULT 'amateur' CHECK (tier IN ('amateur', 'semi_pro', 'professional', 'elite')),
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0.00 AND rating <= 5.00),
    total_predictions INTEGER DEFAULT 0,
    correct_predictions INTEGER DEFAULT 0,
    accuracy_rate DECIMAL(5,4) DEFAULT 0.0000,
    roi DECIMAL(8,4) DEFAULT 0.0000, -- Return on Investment
    streak_current INTEGER DEFAULT 0,
    streak_best INTEGER DEFAULT 0,
    follower_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    total_tips_received DECIMAL(15,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.1000, -- 10% default commission
    subscription_price DECIMAL(10,2) DEFAULT 0.00,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert predictions/tips
CREATE TABLE predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES experts(id),
    title VARCHAR(300) NOT NULL,
    description TEXT,
    prediction_text TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    sport_id UUID, -- References sport from betting DB
    event_id UUID, -- References event from betting DB
    market_type VARCHAR(100),
    confidence_level INTEGER CHECK (confidence_level >= 1 AND confidence_level <= 5),
    recommended_stake DECIMAL(5,2), -- Percentage of bankroll
    recommended_odds DECIMAL(8,2),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'settled', 'void', 'cancelled')),
    outcome VARCHAR(50), -- 'win', 'loss', 'push', 'void'
    actual_odds DECIMAL(8,2),
    profit_loss DECIMAL(8,4), -- In units (positive for profit, negative for loss)
    is_premium BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    price DECIMAL(8,2) DEFAULT 0.00, -- Price to access this tip
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    follow_count INTEGER DEFAULT 0, -- Users following this prediction
    tags VARCHAR(50)[],
    expires_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Prediction comments/discussions
CREATE TABLE prediction_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prediction_id UUID NOT NULL REFERENCES predictions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id from main DB
    parent_comment_id UUID REFERENCES prediction_comments(id),
    content TEXT NOT NULL,
    like_count INTEGER DEFAULT 0,
    is_expert_reply BOOLEAN DEFAULT false,
    is_edited BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User subscriptions to experts
CREATE TABLE expert_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    expert_id UUID NOT NULL REFERENCES experts(id),
    subscription_type VARCHAR(50) DEFAULT 'monthly' CHECK (subscription_type IN ('monthly', 'quarterly', 'yearly', 'lifetime')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'paused')),
    price_paid DECIMAL(10,2) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    auto_renew BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, expert_id)
);

-- User follows (free following)
CREATE TABLE expert_follows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    expert_id UUID NOT NULL REFERENCES experts(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, expert_id)
);

-- Tips/donations to experts
CREATE TABLE expert_tips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID NOT NULL, -- References users.id from main DB
    expert_id UUID NOT NULL REFERENCES experts(id),
    prediction_id UUID REFERENCES predictions(id),
    amount DECIMAL(10,2) NOT NULL,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Expert performance tracking (daily aggregates)
CREATE TABLE expert_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES experts(id),
    date DATE NOT NULL,
    predictions_made INTEGER DEFAULT 0,
    predictions_won INTEGER DEFAULT 0,
    predictions_lost INTEGER DEFAULT 0,
    predictions_void INTEGER DEFAULT 0,
    total_roi DECIMAL(8,4) DEFAULT 0.0000,
    profit_loss DECIMAL(15,2) DEFAULT 0.00,
    new_followers INTEGER DEFAULT 0,
    tips_received DECIMAL(15,2) DEFAULT 0.00,
    subscription_revenue DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(expert_id, date)
);

-- Leaderboards for experts
CREATE TABLE expert_leaderboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES experts(id),
    category VARCHAR(100) NOT NULL,
    time_period VARCHAR(50) NOT NULL CHECK (time_period IN ('daily', 'weekly', 'monthly', 'yearly', 'all_time')),
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('accuracy', 'roi', 'profit', 'followers', 'tips')),
    metric_value DECIMAL(15,4) NOT NULL,
    rank_position INTEGER NOT NULL,
    total_participants INTEGER NOT NULL,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, time_period, metric_type, expert_id)
);

-- Content/analysis posts by experts
CREATE TABLE expert_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES experts(id),
    title VARCHAR(300) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'article' CHECK (content_type IN ('article', 'video', 'podcast', 'infographic')),
    category VARCHAR(100) DEFAULT 'general',
    tags VARCHAR(50)[],
    is_premium BOOLEAN DEFAULT false,
    price DECIMAL(8,2) DEFAULT 0.00,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    featured_image_url TEXT,
    video_url TEXT,
    audio_url TEXT,
    published_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User interactions with predictions (likes, follows, etc.)
CREATE TABLE prediction_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    prediction_id UUID NOT NULL REFERENCES predictions(id),
    interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('like', 'follow', 'report', 'share')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, prediction_id, interaction_type)
);

-- AI-powered analysis and insights
CREATE TABLE ai_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID, -- References event from betting DB
    market_id UUID, -- References market from betting DB
    insight_type VARCHAR(50) NOT NULL CHECK (insight_type IN ('prediction', 'trend', 'anomaly', 'value_bet')),
    title VARCHAR(300) NOT NULL,
    description TEXT NOT NULL,
    confidence_score DECIMAL(5,4) NOT NULL CHECK (confidence_score >= 0.0000 AND confidence_score <= 1.0000),
    supporting_data JSONB,
    model_version VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_experts_user_id ON experts(user_id);
CREATE INDEX idx_experts_status ON experts(status);
CREATE INDEX idx_experts_tier ON experts(tier);
CREATE INDEX idx_experts_rating ON experts(rating);
CREATE INDEX idx_experts_accuracy_rate ON experts(accuracy_rate);

CREATE INDEX idx_predictions_expert_id ON predictions(expert_id);
CREATE INDEX idx_predictions_category ON predictions(category);
CREATE INDEX idx_predictions_sport_id ON predictions(sport_id);
CREATE INDEX idx_predictions_event_id ON predictions(event_id);
CREATE INDEX idx_predictions_status ON predictions(status);
CREATE INDEX idx_predictions_is_premium ON predictions(is_premium);
CREATE INDEX idx_predictions_created_at ON predictions(created_at);

CREATE INDEX idx_prediction_comments_prediction_id ON prediction_comments(prediction_id);
CREATE INDEX idx_prediction_comments_user_id ON prediction_comments(user_id);
CREATE INDEX idx_prediction_comments_parent_comment_id ON prediction_comments(parent_comment_id);

CREATE INDEX idx_expert_subscriptions_user_id ON expert_subscriptions(user_id);
CREATE INDEX idx_expert_subscriptions_expert_id ON expert_subscriptions(expert_id);
CREATE INDEX idx_expert_subscriptions_status ON expert_subscriptions(status);

CREATE INDEX idx_expert_follows_user_id ON expert_follows(user_id);
CREATE INDEX idx_expert_follows_expert_id ON expert_follows(expert_id);

CREATE INDEX idx_expert_tips_from_user_id ON expert_tips(from_user_id);
CREATE INDEX idx_expert_tips_expert_id ON expert_tips(expert_id);
CREATE INDEX idx_expert_tips_prediction_id ON expert_tips(prediction_id);

CREATE INDEX idx_expert_performance_expert_id ON expert_performance(expert_id);
CREATE INDEX idx_expert_performance_date ON expert_performance(date);

CREATE INDEX idx_expert_leaderboards_category ON expert_leaderboards(category);
CREATE INDEX idx_expert_leaderboards_time_period ON expert_leaderboards(time_period);
CREATE INDEX idx_expert_leaderboards_metric_type ON expert_leaderboards(metric_type);
CREATE INDEX idx_expert_leaderboards_rank_position ON expert_leaderboards(rank_position);

CREATE INDEX idx_expert_content_expert_id ON expert_content(expert_id);
CREATE INDEX idx_expert_content_category ON expert_content(category);
CREATE INDEX idx_expert_content_is_premium ON expert_content(is_premium);
CREATE INDEX idx_expert_content_published_at ON expert_content(published_at);

CREATE INDEX idx_prediction_interactions_user_id ON prediction_interactions(user_id);
CREATE INDEX idx_prediction_interactions_prediction_id ON prediction_interactions(prediction_id);

CREATE INDEX idx_ai_insights_event_id ON ai_insights(event_id);
CREATE INDEX idx_ai_insights_market_id ON ai_insights(market_id);
CREATE INDEX idx_ai_insights_insight_type ON ai_insights(insight_type);
CREATE INDEX idx_ai_insights_confidence_score ON ai_insights(confidence_score);
CREATE INDEX idx_ai_insights_is_active ON ai_insights(is_active);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_experts_updated_at 
    BEFORE UPDATE ON experts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_predictions_updated_at 
    BEFORE UPDATE ON predictions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prediction_comments_updated_at 
    BEFORE UPDATE ON prediction_comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_expert_subscriptions_updated_at 
    BEFORE UPDATE ON expert_subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_expert_content_updated_at 
    BEFORE UPDATE ON expert_content 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();