-- Create Odds Exchange Trading Schema for BetBet Platform
-- =======================================================

-- Create odds_exchange schema
CREATE SCHEMA IF NOT EXISTS odds_exchange;

-- Trading markets
CREATE TABLE odds_exchange.markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    market_type VARCHAR(50) NOT NULL CHECK (market_type IN ('binary', 'decimal', 'fractional')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'settled', 'cancelled')),
    settlement_date TIMESTAMP WITH TIME ZONE,
    total_volume DECIMAL(20,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.0500,
    min_stake DECIMAL(10,2) DEFAULT 1.00,
    max_stake DECIMAL(15,2) DEFAULT 10000.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Order book for trading
CREATE TABLE odds_exchange.orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    user_id UUID NOT NULL,
    order_type VARCHAR(20) NOT NULL CHECK (order_type IN ('back', 'lay')),
    price DECIMAL(8,2) NOT NULL,
    size DECIMAL(15,2) NOT NULL,
    remaining_size DECIMAL(15,2) NOT NULL,
    matched_size DECIMAL(15,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'matched', 'partially_matched', 'cancelled', 'expired')),
    time_in_force VARCHAR(20) DEFAULT 'GTC' CHECK (time_in_force IN ('GTC', 'IOC', 'FOK')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trade executions
CREATE TABLE odds_exchange.trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    back_order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    lay_order_id UUID NOT NULL REFERENCES odds_exchange.orders(id),
    back_user_id UUID NOT NULL,
    lay_user_id UUID NOT NULL,
    price DECIMAL(8,2) NOT NULL,
    size DECIMAL(15,2) NOT NULL,
    commission DECIMAL(15,2) DEFAULT 0.00,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User positions
CREATE TABLE odds_exchange.positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    position_type VARCHAR(20) NOT NULL CHECK (position_type IN ('back', 'lay')),
    total_matched DECIMAL(15,2) DEFAULT 0.00,
    average_price DECIMAL(8,2) DEFAULT 0.00,
    profit_loss DECIMAL(15,2) DEFAULT 0.00,
    liability DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, market_id, position_type)
);

-- Market data for price history
CREATE TABLE odds_exchange.market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    best_back_price DECIMAL(8,2),
    best_lay_price DECIMAL(8,2),
    back_volume DECIMAL(15,2) DEFAULT 0.00,
    lay_volume DECIMAL(15,2) DEFAULT 0.00,
    last_traded_price DECIMAL(8,2),
    total_matched DECIMAL(20,2) DEFAULT 0.00
);

-- User trading accounts
CREATE TABLE odds_exchange.trading_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    exposure DECIMAL(15,2) DEFAULT 0.00,
    total_profit_loss DECIMAL(15,2) DEFAULT 0.00,
    commission_paid DECIMAL(15,2) DEFAULT 0.00,
    total_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0.0000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Risk management settings
CREATE TABLE odds_exchange.risk_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    max_exposure DECIMAL(15,2) DEFAULT 1000.00,
    max_stake_per_bet DECIMAL(10,2) DEFAULT 100.00,
    daily_loss_limit DECIMAL(15,2) DEFAULT 500.00,
    stop_loss_percentage DECIMAL(5,2) DEFAULT 10.00,
    auto_hedge BOOLEAN DEFAULT false,
    notifications_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_markets_status ON odds_exchange.markets(status);
CREATE INDEX idx_markets_category ON odds_exchange.markets(category);
CREATE INDEX idx_markets_created_at ON odds_exchange.markets(created_at);

CREATE INDEX idx_orders_market_id ON odds_exchange.orders(market_id);
CREATE INDEX idx_orders_user_id ON odds_exchange.orders(user_id);
CREATE INDEX idx_orders_status ON odds_exchange.orders(status);
CREATE INDEX idx_orders_order_type ON odds_exchange.orders(order_type);
CREATE INDEX idx_orders_price ON odds_exchange.orders(price);
CREATE INDEX idx_orders_created_at ON odds_exchange.orders(created_at);

CREATE INDEX idx_trades_market_id ON odds_exchange.trades(market_id);
CREATE INDEX idx_trades_back_user_id ON odds_exchange.trades(back_user_id);
CREATE INDEX idx_trades_lay_user_id ON odds_exchange.trades(lay_user_id);
CREATE INDEX idx_trades_executed_at ON odds_exchange.trades(executed_at);

CREATE INDEX idx_positions_user_id ON odds_exchange.positions(user_id);
CREATE INDEX idx_positions_market_id ON odds_exchange.positions(market_id);

CREATE INDEX idx_market_data_market_id ON odds_exchange.market_data(market_id);
CREATE INDEX idx_market_data_timestamp ON odds_exchange.market_data(timestamp);

CREATE INDEX idx_trading_accounts_user_id ON odds_exchange.trading_accounts(user_id);

CREATE INDEX idx_risk_settings_user_id ON odds_exchange.risk_settings(user_id);

-- Update triggers
CREATE TRIGGER update_markets_updated_at 
    BEFORE UPDATE ON odds_exchange.markets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON odds_exchange.orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at 
    BEFORE UPDATE ON odds_exchange.positions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trading_accounts_updated_at 
    BEFORE UPDATE ON odds_exchange.trading_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_risk_settings_updated_at 
    BEFORE UPDATE ON odds_exchange.risk_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();