-- Fix Sports Analysis Schema to Match Service Models
-- =================================================

-- Drop existing schema and recreate with correct structure
DROP SCHEMA IF EXISTS sports_analysis CASCADE;
CREATE SCHEMA sports_analysis;

-- SportsEvent table (exact match to service model)
CREATE TABLE sports_analysis.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    external_id VARCHAR(100) UNIQUE,
    sport VARCHAR(50) NOT NULL,
    league VARCHAR(100),
    home_team VARCHAR(100) NOT NULL,
    away_team VARCHAR(100) NOT NULL,
    event_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'scheduled',
    home_score INTEGER,
    away_score INTEGER,
    event_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Prediction table (exact match to service model)
CREATE TABLE sports_analysis.predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES sports_analysis.events(id),
    prediction_type VARCHAR(50) NOT NULL,
    predicted_outcome VARCHAR(100),
    confidence_score NUMERIC(3,2),
    model_version VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_events_external_id ON sports_analysis.events(external_id);
CREATE INDEX idx_events_sport ON sports_analysis.events(sport);
CREATE INDEX idx_events_league ON sports_analysis.events(league);
CREATE INDEX idx_events_event_date ON sports_analysis.events(event_date);
CREATE INDEX idx_events_status ON sports_analysis.events(status);

CREATE INDEX idx_predictions_event_id ON sports_analysis.predictions(event_id);
CREATE INDEX idx_predictions_prediction_type ON sports_analysis.predictions(prediction_type);
CREATE INDEX idx_predictions_confidence_score ON sports_analysis.predictions(confidence_score);
CREATE INDEX idx_predictions_created_at ON sports_analysis.predictions(created_at);