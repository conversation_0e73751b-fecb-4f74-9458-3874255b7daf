-- Fix Odds Exchange Schema to Match Service Models
-- =================================================

-- Drop existing schema and recreate with correct structure
DROP SCHEMA IF EXISTS odds_exchange CASCADE;
CREATE SCHEMA odds_exchange;

-- Market table (exact match to service model)
CREATE TABLE odds_exchange.markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id VARCHAR(100) NOT NULL,
    market_type VARCHAR(50) NOT NULL,
    title VARCHAR(500) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    total_matched NUMERIC(15,2) DEFAULT 0,
    total_volume NUMERIC(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    closes_at TIMESTAMP WITH TIME ZONE
);

-- Order table (exact match to service model)
CREATE TABLE odds_exchange.orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID NOT NULL REFERENCES odds_exchange.markets(id),
    user_id UUID NOT NULL,
    side VARCHAR(10) NOT NULL, -- back or lay
    odds NUMERIC(6,3) NOT NULL,
    stake NUMERIC(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    matched_amount NUMERIC(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_markets_event_id ON odds_exchange.markets(event_id);
CREATE INDEX idx_markets_market_type ON odds_exchange.markets(market_type);
CREATE INDEX idx_markets_status ON odds_exchange.markets(status);
CREATE INDEX idx_markets_total_volume ON odds_exchange.markets(total_volume);
CREATE INDEX idx_markets_created_at ON odds_exchange.markets(created_at);
CREATE INDEX idx_markets_closes_at ON odds_exchange.markets(closes_at);

CREATE INDEX idx_orders_market_id ON odds_exchange.orders(market_id);
CREATE INDEX idx_orders_user_id ON odds_exchange.orders(user_id);
CREATE INDEX idx_orders_side ON odds_exchange.orders(side);
CREATE INDEX idx_orders_status ON odds_exchange.orders(status);
CREATE INDEX idx_orders_odds ON odds_exchange.orders(odds);
CREATE INDEX idx_orders_created_at ON odds_exchange.orders(created_at);

-- Composite indexes for common queries
CREATE INDEX idx_orders_market_side_status ON odds_exchange.orders(market_id, side, status);
CREATE INDEX idx_markets_status_volume ON odds_exchange.markets(status, total_volume DESC);