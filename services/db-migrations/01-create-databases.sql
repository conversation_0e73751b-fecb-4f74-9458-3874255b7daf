-- BetBet Platform Database Initialization Script
-- ===============================================
-- Creates all databases for the microservices architecture

-- Create databases for each service
CREATE DATABASE betbet_gaming;
CREATE DATABASE betbet_betting;  
CREATE DATABASE betbet_trading;
CREATE DATABASE betbet_experts;
CREATE DATABASE betbet_sports;
CREATE DATABASE betbet_leaderboards;

-- Grant privileges to the main user
GRANT ALL PRIVILEGES ON DATABASE betbet_platform TO betbet_user;
GRANT ALL PRIVILEGES ON DATABASE betbet_gaming TO betbet_user;
GRANT ALL PRIVILEGES ON DATABASE betbet_betting TO betbet_user;
GRANT ALL PRIVILEGES ON DATABASE betbet_trading TO betbet_user;
GRANT ALL PRIVILEGES ON DATABASE betbet_experts TO betbet_user;
GRANT ALL PRIVILEGES ON DATABASE betbet_sports TO betbet_user;
GRANT ALL PRIVILEGES ON DAT<PERSON>ASE betbet_leaderboards TO betbet_user;