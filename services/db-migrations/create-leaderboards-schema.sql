-- Create Leaderboards Schema for BetBet Platform
-- ==============================================

-- Create leaderboards schema
CREATE SCHEMA IF NOT EXISTS leaderboards;

-- User points and rankings
CREATE TABLE leaderboards.user_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    points_type VARCHAR(50) NOT NULL CHECK (points_type IN ('gaming', 'betting', 'trading', 'expert', 'social', 'total')),
    current_points INTEGER NOT NULL DEFAULT 0,
    lifetime_points INTEGER NOT NULL DEFAULT 0,
    points_this_week INTEGER DEFAULT 0,
    points_this_month INTEGER DEFAULT 0,
    level_id UUID,
    rank_position INTEGER,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, points_type)
);

-- Leaderboard levels and tiers
CREATE TABLE leaderboards.levels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level_name VARCHAR(100) NOT NULL UNIQUE,
    level_number INTEGER NOT NULL UNIQUE,
    points_required INTEGER NOT NULL,
    tier VARCHAR(50) NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
    perks JSONB DEFAULT '{}',
    badge_icon_url TEXT,
    color_scheme VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Achievements system
CREATE TABLE leaderboards.achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    achievement_type VARCHAR(50) NOT NULL CHECK (achievement_type IN ('milestone', 'streak', 'special', 'seasonal')),
    criteria JSONB NOT NULL,
    points_reward INTEGER DEFAULT 0,
    badge_icon_url TEXT,
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User achievements (unlocked)
CREATE TABLE leaderboards.user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    achievement_id UUID NOT NULL REFERENCES leaderboards.achievements(id),
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    progress_data JSONB DEFAULT '{}',
    UNIQUE(user_id, achievement_id)
);

-- Points transactions/history
CREATE TABLE leaderboards.points_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    points_type VARCHAR(50) NOT NULL,
    points_change INTEGER NOT NULL,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('earned', 'spent', 'bonus', 'penalty', 'adjustment')),
    source_type VARCHAR(50) NOT NULL,
    source_id UUID,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leaderboard snapshots (historical rankings)
CREATE TABLE leaderboards.ranking_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    snapshot_date DATE NOT NULL,
    points_type VARCHAR(50) NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly', 'all_time')),
    rankings JSONB NOT NULL, -- Array of {user_id, points, rank}
    total_participants INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(snapshot_date, points_type, period_type)
);

-- Competitions and tournaments
CREATE TABLE leaderboards.competitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    competition_type VARCHAR(50) NOT NULL CHECK (competition_type IN ('points_race', 'achievement_hunt', 'streak_challenge', 'custom')),
    category VARCHAR(100) NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'active', 'completed', 'cancelled')),
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    rules JSONB NOT NULL,
    prizes JSONB DEFAULT '{}',
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Competition participants
CREATE TABLE leaderboards.competition_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    competition_id UUID NOT NULL REFERENCES leaderboards.competitions(id),
    user_id UUID NOT NULL,
    entry_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    current_score INTEGER DEFAULT 0,
    current_rank INTEGER,
    final_rank INTEGER,
    prize_amount DECIMAL(10,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'disqualified')),
    progress_data JSONB DEFAULT '{}',
    UNIQUE(competition_id, user_id)
);

-- Affiliation competitions (team-based)
CREATE TABLE leaderboards.affiliation_competitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'active', 'completed', 'cancelled')),
    team_size_min INTEGER DEFAULT 1,
    team_size_max INTEGER DEFAULT 10,
    scoring_method VARCHAR(50) DEFAULT 'total_points' CHECK (scoring_method IN ('total_points', 'average_points', 'best_performers')),
    prize_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Teams for affiliation competitions
CREATE TABLE leaderboards.competition_teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    affiliation_competition_id UUID NOT NULL REFERENCES leaderboards.affiliation_competitions(id),
    team_name VARCHAR(200) NOT NULL,
    team_captain_user_id UUID NOT NULL,
    current_score INTEGER DEFAULT 0,
    current_rank INTEGER,
    final_rank INTEGER,
    prize_amount DECIMAL(15,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'disbanded')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Team members
CREATE TABLE leaderboards.team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID NOT NULL REFERENCES leaderboards.competition_teams(id),
    user_id UUID NOT NULL,
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('captain', 'member')),
    contribution_score INTEGER DEFAULT 0,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(team_id, user_id)
);

-- Indexes
CREATE INDEX idx_user_points_user_id ON leaderboards.user_points(user_id);
CREATE INDEX idx_user_points_points_type ON leaderboards.user_points(points_type);
CREATE INDEX idx_user_points_current_points ON leaderboards.user_points(current_points);
CREATE INDEX idx_user_points_rank_position ON leaderboards.user_points(rank_position);

CREATE INDEX idx_levels_level_number ON leaderboards.levels(level_number);
CREATE INDEX idx_levels_points_required ON leaderboards.levels(points_required);

CREATE INDEX idx_achievements_category ON leaderboards.achievements(category);
CREATE INDEX idx_achievements_achievement_type ON leaderboards.achievements(achievement_type);
CREATE INDEX idx_achievements_is_active ON leaderboards.achievements(is_active);

CREATE INDEX idx_user_achievements_user_id ON leaderboards.user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON leaderboards.user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_unlocked_at ON leaderboards.user_achievements(unlocked_at);

CREATE INDEX idx_points_transactions_user_id ON leaderboards.points_transactions(user_id);
CREATE INDEX idx_points_transactions_points_type ON leaderboards.points_transactions(points_type);
CREATE INDEX idx_points_transactions_source ON leaderboards.points_transactions(source_type, source_id);
CREATE INDEX idx_points_transactions_created_at ON leaderboards.points_transactions(created_at);

CREATE INDEX idx_ranking_snapshots_date ON leaderboards.ranking_snapshots(snapshot_date);
CREATE INDEX idx_ranking_snapshots_type_period ON leaderboards.ranking_snapshots(points_type, period_type);

CREATE INDEX idx_competitions_status ON leaderboards.competitions(status);
CREATE INDEX idx_competitions_category ON leaderboards.competitions(category);
CREATE INDEX idx_competitions_start_date ON leaderboards.competitions(start_date);
CREATE INDEX idx_competitions_is_featured ON leaderboards.competitions(is_featured);

CREATE INDEX idx_competition_participants_competition_id ON leaderboards.competition_participants(competition_id);
CREATE INDEX idx_competition_participants_user_id ON leaderboards.competition_participants(user_id);
CREATE INDEX idx_competition_participants_current_rank ON leaderboards.competition_participants(current_rank);

CREATE INDEX idx_affiliation_competitions_status ON leaderboards.affiliation_competitions(status);
CREATE INDEX idx_affiliation_competitions_start_date ON leaderboards.affiliation_competitions(start_date);

CREATE INDEX idx_competition_teams_affiliation_competition_id ON leaderboards.competition_teams(affiliation_competition_id);
CREATE INDEX idx_competition_teams_team_captain_user_id ON leaderboards.competition_teams(team_captain_user_id);
CREATE INDEX idx_competition_teams_current_rank ON leaderboards.competition_teams(current_rank);

CREATE INDEX idx_team_members_team_id ON leaderboards.team_members(team_id);
CREATE INDEX idx_team_members_user_id ON leaderboards.team_members(user_id);

-- Update triggers
CREATE TRIGGER update_user_points_updated_at 
    BEFORE UPDATE ON leaderboards.user_points 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_competitions_updated_at 
    BEFORE UPDATE ON leaderboards.competitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_affiliation_competitions_updated_at 
    BEFORE UPDATE ON leaderboards.affiliation_competitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();