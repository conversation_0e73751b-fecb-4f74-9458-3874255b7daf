-- BetBet Leaderboards Database Schema
-- ===================================

\c betbet_leaderboards;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Leaderboard categories and types
CREATE TABLE leaderboard_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category_type VARCHAR(50) NOT NULL CHECK (category_type IN ('betting', 'gaming', 'trading', 'expert', 'general')),
    icon_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leaderboard definitions
CREATE TABLE leaderboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES leaderboard_categories(id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    metric_type VARCHAR(100) NOT NULL, -- 'total_winnings', 'win_rate', 'streak', 'volume', 'roi'
    time_period VARCHAR(50) NOT NULL CHECK (time_period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'all_time')),
    calculation_method VARCHAR(50) DEFAULT 'sum' CHECK (calculation_method IN ('sum', 'average', 'max', 'count', 'percentage')),
    min_qualifying_value DECIMAL(15,4) DEFAULT 0.0000,
    min_activity_threshold INTEGER DEFAULT 1, -- Minimum number of activities to qualify
    max_participants INTEGER DEFAULT 1000,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    prize_structure JSONB DEFAULT '{}', -- {1: 1000, 2: 500, 3: 250, ...}
    reset_frequency VARCHAR(50) DEFAULT 'never' CHECK (reset_frequency IN ('never', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    last_reset TIMESTAMP WITH TIME ZONE,
    next_reset TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User leaderboard entries/rankings
CREATE TABLE leaderboard_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    leaderboard_id UUID NOT NULL REFERENCES leaderboards(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id from main DB
    rank_position INTEGER NOT NULL,
    metric_value DECIMAL(20,4) NOT NULL,
    secondary_metric DECIMAL(20,4), -- For tiebreakers
    activity_count INTEGER DEFAULT 0,
    qualifying_period_start TIMESTAMP WITH TIME ZONE,
    qualifying_period_end TIMESTAMP WITH TIME ZONE,
    prize_won DECIMAL(15,2) DEFAULT 0.00,
    badge_earned VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(leaderboard_id, user_id)
);

-- Achievement system
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    type VARCHAR(50) NOT NULL CHECK (type IN ('milestone', 'streak', 'performance', 'participation', 'special')),
    criteria JSONB NOT NULL, -- Conditions to unlock achievement
    reward_type VARCHAR(50) DEFAULT 'badge' CHECK (reward_type IN ('badge', 'points', 'money', 'title')),
    reward_value DECIMAL(15,2) DEFAULT 0.00,
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    icon_url TEXT,
    badge_url TEXT,
    points_value INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_hidden BOOLEAN DEFAULT false, -- Hidden achievements
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User achievements (unlocked achievements)
CREATE TABLE user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    achievement_id UUID NOT NULL REFERENCES achievements(id),
    progress_value DECIMAL(15,4) DEFAULT 0.0000, -- Current progress towards achievement
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    reward_claimed BOOLEAN DEFAULT false,
    claimed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, achievement_id)
);

-- User statistics aggregated for leaderboards
CREATE TABLE user_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    stat_category VARCHAR(100) NOT NULL, -- 'betting', 'gaming', 'trading', 'expert'
    stat_type VARCHAR(100) NOT NULL, -- 'total_bets', 'win_rate', 'total_winnings', etc.
    time_period VARCHAR(50) NOT NULL,
    stat_value DECIMAL(20,4) NOT NULL,
    rank_position INTEGER,
    percentile DECIMAL(5,4), -- 0-1 scale
    calculation_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, stat_category, stat_type, time_period, calculation_date)
);

-- Seasonal competitions and tournaments
CREATE TABLE seasonal_competitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    season_start TIMESTAMP WITH TIME ZONE NOT NULL,
    season_end TIMESTAMP WITH TIME ZONE NOT NULL,
    registration_start TIMESTAMP WITH TIME ZONE,
    registration_end TIMESTAMP WITH TIME ZONE,
    competition_type VARCHAR(50) NOT NULL CHECK (competition_type IN ('tournament', 'league', 'bracket', 'ladder')),
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'registration', 'active', 'finished', 'cancelled')),
    rules JSONB DEFAULT '{}',
    prize_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Competition participants
CREATE TABLE competition_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    competition_id UUID NOT NULL REFERENCES seasonal_competitions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id from main DB
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    current_score DECIMAL(15,4) DEFAULT 0.0000,
    current_rank INTEGER,
    best_rank INTEGER,
    activities_count INTEGER DEFAULT 0,
    last_activity TIMESTAMP WITH TIME ZONE,
    is_eliminated BOOLEAN DEFAULT false,
    elimination_date TIMESTAMP WITH TIME ZONE,
    final_prize DECIMAL(15,2) DEFAULT 0.00,
    UNIQUE(competition_id, user_id)
);

-- Daily/weekly/monthly snapshots for historical tracking
CREATE TABLE leaderboard_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    leaderboard_id UUID NOT NULL REFERENCES leaderboards(id),
    snapshot_date DATE NOT NULL,
    snapshot_type VARCHAR(20) NOT NULL CHECK (snapshot_type IN ('daily', 'weekly', 'monthly')),
    top_performers JSONB NOT NULL, -- Stored JSON of top N users and their stats
    total_participants INTEGER NOT NULL,
    prize_pool_distributed DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(leaderboard_id, snapshot_date, snapshot_type)
);

-- User titles and ranks
CREATE TABLE user_titles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    title_name VARCHAR(100) NOT NULL,
    title_description TEXT,
    title_type VARCHAR(50) DEFAULT 'achievement' CHECK (title_type IN ('achievement', 'leaderboard', 'special', 'seasonal')),
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    earned_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    is_displayed BOOLEAN DEFAULT false, -- Whether user displays this title
    source_id UUID, -- Achievement or leaderboard that granted this title
    metadata JSONB DEFAULT '{}'
);

-- Points and rewards system
CREATE TABLE user_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    total_points INTEGER DEFAULT 0,
    lifetime_points INTEGER DEFAULT 0,
    points_spent INTEGER DEFAULT 0,
    current_tier VARCHAR(50) DEFAULT 'bronze' CHECK (current_tier IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
    tier_progress INTEGER DEFAULT 0,
    next_tier_threshold INTEGER DEFAULT 1000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Point transactions
CREATE TABLE point_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from main DB
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('earned', 'spent', 'bonus', 'penalty', 'refund')),
    points_amount INTEGER NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    source_type VARCHAR(50), -- 'achievement', 'leaderboard', 'purchase', 'activity'
    source_id UUID,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_leaderboards_category_id ON leaderboards(category_id);
CREATE INDEX idx_leaderboards_time_period ON leaderboards(time_period);
CREATE INDEX idx_leaderboards_metric_type ON leaderboards(metric_type);
CREATE INDEX idx_leaderboards_is_active ON leaderboards(is_active);
CREATE INDEX idx_leaderboards_is_featured ON leaderboards(is_featured);

CREATE INDEX idx_leaderboard_entries_leaderboard_id ON leaderboard_entries(leaderboard_id);
CREATE INDEX idx_leaderboard_entries_user_id ON leaderboard_entries(user_id);
CREATE INDEX idx_leaderboard_entries_rank_position ON leaderboard_entries(rank_position);
CREATE INDEX idx_leaderboard_entries_metric_value ON leaderboard_entries(metric_value);

CREATE INDEX idx_achievements_category ON achievements(category);
CREATE INDEX idx_achievements_type ON achievements(type);
CREATE INDEX idx_achievements_rarity ON achievements(rarity);
CREATE INDEX idx_achievements_is_active ON achievements(is_active);

CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_is_completed ON user_achievements(is_completed);
CREATE INDEX idx_user_achievements_completed_at ON user_achievements(completed_at);

CREATE INDEX idx_user_statistics_user_id ON user_statistics(user_id);
CREATE INDEX idx_user_statistics_stat_category ON user_statistics(stat_category);
CREATE INDEX idx_user_statistics_stat_type ON user_statistics(stat_type);
CREATE INDEX idx_user_statistics_time_period ON user_statistics(time_period);
CREATE INDEX idx_user_statistics_calculation_date ON user_statistics(calculation_date);

CREATE INDEX idx_seasonal_competitions_status ON seasonal_competitions(status);
CREATE INDEX idx_seasonal_competitions_season_start ON seasonal_competitions(season_start);
CREATE INDEX idx_seasonal_competitions_season_end ON seasonal_competitions(season_end);

CREATE INDEX idx_competition_participants_competition_id ON competition_participants(competition_id);
CREATE INDEX idx_competition_participants_user_id ON competition_participants(user_id);
CREATE INDEX idx_competition_participants_current_rank ON competition_participants(current_rank);

CREATE INDEX idx_leaderboard_snapshots_leaderboard_id ON leaderboard_snapshots(leaderboard_id);
CREATE INDEX idx_leaderboard_snapshots_snapshot_date ON leaderboard_snapshots(snapshot_date);
CREATE INDEX idx_leaderboard_snapshots_snapshot_type ON leaderboard_snapshots(snapshot_type);

CREATE INDEX idx_user_titles_user_id ON user_titles(user_id);
CREATE INDEX idx_user_titles_title_type ON user_titles(title_type);
CREATE INDEX idx_user_titles_is_displayed ON user_titles(is_displayed);

CREATE INDEX idx_user_points_user_id ON user_points(user_id);
CREATE INDEX idx_user_points_current_tier ON user_points(current_tier);

CREATE INDEX idx_point_transactions_user_id ON point_transactions(user_id);
CREATE INDEX idx_point_transactions_transaction_type ON point_transactions(transaction_type);
CREATE INDEX idx_point_transactions_source_type ON point_transactions(source_type);
CREATE INDEX idx_point_transactions_created_at ON point_transactions(created_at);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_leaderboards_updated_at 
    BEFORE UPDATE ON leaderboards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leaderboard_entries_updated_at 
    BEFORE UPDATE ON leaderboard_entries 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_seasonal_competitions_updated_at 
    BEFORE UPDATE ON seasonal_competitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_points_updated_at 
    BEFORE UPDATE ON user_points 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();