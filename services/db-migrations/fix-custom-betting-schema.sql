-- Fix Custom Betting Schema to Match Service Models
-- =================================================

-- Drop existing schema and recreate with correct structure
DROP SCHEMA IF EXISTS custom_betting CASCADE;
CREATE SCHEMA custom_betting;

-- CustomBet table (exact match to service model)
CREATE TABLE custom_betting.custom_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Creator and identification
    creator_user_id UUID NOT NULL,
    title VARCHAR(300) NOT NULL,
    description TEXT,
    slug VARCHAR(400) UNIQUE,
    
    -- Event and verification criteria  
    event_criteria TEXT NOT NULL,
    verification_source VARCHAR(200),
    verification_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Bet configuration
    bet_type VARCHAR(50) NOT NULL,
    minimum_stake NUMERIC(15,2) NOT NULL,
    maximum_stake NUMERIC(15,2),
    participant_limit INTEGER,
    
    -- Financial tracking
    total_stakes NUMERIC(15,2) DEFAULT 0,
    total_participants INTEGER DEFAULT 0,
    escrow_balance NUMERIC(15,2) DEFAULT 0,
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'open',
    resolution_status VARCHAR(20),
    
    -- Timing
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    settlement_deadline TIMESTAMP WITH TIME ZONE,
    opens_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Market discovery and categorization
    category VARCHAR(50) NOT NULL,
    tags VARCHAR(100)[],
    is_featured BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT true,
    
    -- Market configuration
    bet_config JSONB DEFAULT '{}',
    settlement_criteria JSONB,
    outcome_definitions JSONB NOT NULL,
    
    -- Dispute and resolution
    dispute_resolution_method VARCHAR(50) DEFAULT 'community_vote',
    arbitrator_id UUID,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- BetOutcome table (exact match to service model)
CREATE TABLE custom_betting.bet_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    
    -- Outcome details
    outcome_text VARCHAR(500) NOT NULL,
    outcome_key VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Probability and odds (market-driven)
    current_odds NUMERIC(8,3),
    implied_probability NUMERIC(5,2),
    
    -- Betting statistics
    total_backing_amount NUMERIC(15,2) DEFAULT 0,
    backer_count INTEGER DEFAULT 0,
    
    -- Resolution
    is_winning_outcome BOOLEAN,
    settlement_evidence TEXT,
    settlement_confidence NUMERIC(3,2),
    
    -- Status and ordering
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- BetParticipant table (exact match to service model)
CREATE TABLE custom_betting.bet_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL,
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    
    -- Position details
    stake_amount NUMERIC(15,2) NOT NULL,
    potential_payout NUMERIC(15,2) NOT NULL,
    actual_payout NUMERIC(15,2),
    
    -- Position type
    position_type VARCHAR(20) NOT NULL,
    
    -- Matching details
    matched_amount NUMERIC(15,2) DEFAULT 0,
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'active',
    
    -- Timing
    entry_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    exit_timestamp TIMESTAMP WITH TIME ZONE,
    settlement_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Risk management
    ip_address INET,
    user_agent TEXT,
    
    -- Settlement details
    settlement_reason TEXT,
    settlement_data JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- EscrowTransaction table (exact match to service model)
CREATE TABLE custom_betting.escrow_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL,
    participant_id UUID REFERENCES custom_betting.bet_participants(id),
    
    -- Transaction details
    transaction_type VARCHAR(30) NOT NULL,
    amount NUMERIC(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    
    -- Financial references
    reference_transaction_id VARCHAR(100),
    transaction_hash VARCHAR(100),
    
    -- Escrow status
    escrow_status VARCHAR(20) DEFAULT 'held',
    
    -- Release conditions
    release_conditions JSONB,
    auto_release_at TIMESTAMP WITH TIME ZONE,
    
    -- Dispute handling
    dispute_reason TEXT,
    dispute_initiated_at TIMESTAMP WITH TIME ZONE,
    dispute_resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Processing
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- BetMatchingQueue table (exact match to service model)
CREATE TABLE custom_betting.bet_matching_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL,
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    
    -- Matching preferences
    desired_stake NUMERIC(15,2) NOT NULL,
    position_type VARCHAR(20) NOT NULL,
    acceptable_odds_min NUMERIC(8,3),
    acceptable_odds_max NUMERIC(8,3),
    
    -- Queue management
    queue_priority INTEGER DEFAULT 0,
    queue_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Matching options
    partial_match_allowed BOOLEAN DEFAULT true,
    minimum_match_amount NUMERIC(15,2),
    auto_accept BOOLEAN DEFAULT false,
    
    -- Status
    status VARCHAR(20) DEFAULT 'queued',
    matched_amount NUMERIC(15,2) DEFAULT 0,
    
    -- Matching history
    matching_attempts INTEGER DEFAULT 0,
    last_matching_attempt TIMESTAMP WITH TIME ZONE,
    matching_notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- BetMatch table (exact match to service model)
CREATE TABLE custom_betting.bet_matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    backing_participant_id UUID NOT NULL REFERENCES custom_betting.bet_participants(id),
    laying_participant_id UUID NOT NULL REFERENCES custom_betting.bet_participants(id),
    
    -- Match details
    matched_amount NUMERIC(15,2) NOT NULL,
    agreed_odds NUMERIC(8,3) NOT NULL,
    
    -- Timing
    matched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Match metadata
    matching_algorithm VARCHAR(50) DEFAULT 'fifo',
    match_quality_score NUMERIC(3,2),
    
    -- Settlement
    settlement_status VARCHAR(20) DEFAULT 'active',
    settled_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    version INTEGER DEFAULT 1
);

-- SettlementDispute table (exact match to service model)
CREATE TABLE custom_betting.settlement_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    disputing_user_id UUID NOT NULL,
    
    -- Dispute details
    dispute_type VARCHAR(50) NOT NULL,
    dispute_reason TEXT NOT NULL,
    evidence_submitted TEXT,
    evidence_urls TEXT[],
    
    -- Resolution process
    arbitrator_assigned UUID,
    arbitrator_decision TEXT,
    community_votes_for INTEGER DEFAULT 0,
    community_votes_against INTEGER DEFAULT 0,
    
    -- Status and timeline
    status VARCHAR(20) DEFAULT 'open',
    priority VARCHAR(10) DEFAULT 'normal',
    
    -- Resolution
    resolution_timestamp TIMESTAMP WITH TIME ZONE,
    resolution_details TEXT,
    final_outcome VARCHAR(100),
    
    -- Financial impact
    dispute_fee NUMERIC(15,2) DEFAULT 0,
    resolution_fee NUMERIC(15,2) DEFAULT 0,
    refund_amount NUMERIC(15,2) DEFAULT 0,
    
    -- Timing
    dispute_deadline TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1
);

-- MarketAnalytics table (exact match to service model)
CREATE TABLE custom_betting.market_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    
    -- Time period
    analysis_date TIMESTAMP WITH TIME ZONE NOT NULL,
    analysis_period VARCHAR(20) NOT NULL,
    
    -- Volume metrics
    total_volume NUMERIC(15,2) DEFAULT 0,
    participant_count INTEGER DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    
    -- Market dynamics
    liquidity_score NUMERIC(5,2),
    volatility_index NUMERIC(5,2),
    spread_percentage NUMERIC(5,4),
    
    -- Outcome distribution
    outcome_probabilities JSONB,
    odds_history JSONB,
    
    -- Performance indicators
    time_to_match_avg NUMERIC(10,2),
    match_success_rate NUMERIC(5,4),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_custom_bets_creator_user_id ON custom_betting.custom_bets(creator_user_id);
CREATE INDEX idx_custom_bets_status ON custom_betting.custom_bets(status);
CREATE INDEX idx_custom_bets_category ON custom_betting.custom_bets(category);
CREATE INDEX idx_custom_bets_deadline ON custom_betting.custom_bets(deadline);
CREATE INDEX idx_custom_bets_created_at ON custom_betting.custom_bets(created_at);
CREATE INDEX idx_custom_bets_slug ON custom_betting.custom_bets(slug);

CREATE INDEX idx_bet_outcomes_custom_bet_id ON custom_betting.bet_outcomes(custom_bet_id);
CREATE INDEX idx_bet_outcomes_outcome_key ON custom_betting.bet_outcomes(outcome_key);

CREATE INDEX idx_bet_participants_custom_bet_id ON custom_betting.bet_participants(custom_bet_id);
CREATE INDEX idx_bet_participants_user_id ON custom_betting.bet_participants(user_id);
CREATE INDEX idx_bet_participants_outcome_id ON custom_betting.bet_participants(outcome_id);
CREATE INDEX idx_bet_participants_status ON custom_betting.bet_participants(status);

CREATE INDEX idx_escrow_transactions_custom_bet_id ON custom_betting.escrow_transactions(custom_bet_id);
CREATE INDEX idx_escrow_transactions_user_id ON custom_betting.escrow_transactions(user_id);
CREATE INDEX idx_escrow_transactions_status ON custom_betting.escrow_transactions(escrow_status);

CREATE INDEX idx_bet_matching_queue_custom_bet_id ON custom_betting.bet_matching_queue(custom_bet_id);
CREATE INDEX idx_bet_matching_queue_user_id ON custom_betting.bet_matching_queue(user_id);
CREATE INDEX idx_bet_matching_queue_status ON custom_betting.bet_matching_queue(status);

CREATE INDEX idx_bet_matches_custom_bet_id ON custom_betting.bet_matches(custom_bet_id);
CREATE INDEX idx_bet_matches_backing_participant_id ON custom_betting.bet_matches(backing_participant_id);
CREATE INDEX idx_bet_matches_laying_participant_id ON custom_betting.bet_matches(laying_participant_id);

CREATE INDEX idx_settlement_disputes_custom_bet_id ON custom_betting.settlement_disputes(custom_bet_id);
CREATE INDEX idx_settlement_disputes_disputing_user_id ON custom_betting.settlement_disputes(disputing_user_id);
CREATE INDEX idx_settlement_disputes_status ON custom_betting.settlement_disputes(status);

CREATE INDEX idx_market_analytics_custom_bet_id ON custom_betting.market_analytics(custom_bet_id);
CREATE INDEX idx_market_analytics_analysis_date ON custom_betting.market_analytics(analysis_date);

-- Create update triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_custom_bets_updated_at 
    BEFORE UPDATE ON custom_betting.custom_bets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bet_outcomes_updated_at 
    BEFORE UPDATE ON custom_betting.bet_outcomes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bet_participants_updated_at 
    BEFORE UPDATE ON custom_betting.bet_participants 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_escrow_transactions_updated_at 
    BEFORE UPDATE ON custom_betting.escrow_transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bet_matching_queue_updated_at 
    BEFORE UPDATE ON custom_betting.bet_matching_queue 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settlement_disputes_updated_at 
    BEFORE UPDATE ON custom_betting.settlement_disputes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();