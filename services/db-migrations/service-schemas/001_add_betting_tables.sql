-- BetBet Gaming Engine - Betting Tables Migration
-- =================================================
-- 
-- Migration: 001_add_betting_tables
-- Description: Add betting markets, outcomes, user bets, and odds history tables
-- Author: Claude-API
-- Date: 2025-01-20

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- BETTING MARKETS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS gaming_engine.betting_markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    session_id UUID REFERENCES gaming_engine.game_sessions(id),
    tournament_id UUID REFERENCES gaming_engine.tournaments(id),
    
    -- Market details
    name VARCHAR(200) NOT NULL,
    description TEXT,
    market_type VARCHAR(50) NOT NULL CHECK (market_type IN ('match_winner', 'tournament_winner', 'player_performance', 'special_events')),
    
    -- Market status and configuration
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'closed', 'settled', 'deleted')),
    total_pool DECIMAL(15,2) DEFAULT 0 CHECK (total_pool >= 0),
    total_bets INTEGER DEFAULT 0 CHECK (total_bets >= 0),
    
    -- Timing
    opens_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closes_at TIMESTAMP WITH TIME ZONE,
    settlement_time TIMESTAMP WITH TIME ZONE,
    
    -- Market configuration
    market_config JSONB DEFAULT '{}',
    settlement_criteria JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (session_id IS NOT NULL OR tournament_id IS NOT NULL),
    CHECK (closes_at IS NULL OR closes_at > opens_at),
    CHECK (settlement_time IS NULL OR settlement_time >= opens_at)
);

-- =====================================================
-- BETTING OUTCOMES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS gaming_engine.betting_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    market_id UUID NOT NULL REFERENCES gaming_engine.betting_markets(id) ON DELETE CASCADE,
    
    -- Outcome details
    name VARCHAR(200) NOT NULL,
    description TEXT,
    outcome_type VARCHAR(50) CHECK (outcome_type IN ('player', 'team', 'event', 'performance', 'custom')),
    
    -- Odds and probability
    odds DECIMAL(8,3) NOT NULL CHECK (odds BETWEEN -99999 AND 99999),  -- American odds format
    implied_probability DECIMAL(5,2) CHECK (implied_probability BETWEEN 0 AND 100),  -- Percentage
    
    -- Betting statistics
    total_bet_amount DECIMAL(15,2) DEFAULT 0 CHECK (total_bet_amount >= 0),
    bet_count INTEGER DEFAULT 0 CHECK (bet_count >= 0),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_winning_outcome BOOLEAN DEFAULT NULL,
    
    -- External references (for player/team outcomes)
    external_id VARCHAR(100),  -- Can reference user_id, team_id, etc.
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- =====================================================
-- USER BETS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS gaming_engine.user_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    user_id UUID NOT NULL REFERENCES public.users(id),
    market_id UUID NOT NULL REFERENCES gaming_engine.betting_markets(id),
    outcome_id UUID NOT NULL REFERENCES gaming_engine.betting_outcomes(id),
    
    -- Bet details
    bet_amount DECIMAL(15,2) NOT NULL CHECK (bet_amount > 0),
    odds DECIMAL(8,3) NOT NULL,  -- Odds at time of bet placement
    potential_return DECIMAL(15,2) NOT NULL CHECK (potential_return >= bet_amount),
    actual_return DECIMAL(15,2) CHECK (actual_return IS NULL OR actual_return >= 0),
    
    -- Status and settlement
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'won', 'lost', 'void', 'cancelled')),
    placed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    settled_at TIMESTAMP WITH TIME ZONE,
    
    -- Risk management
    ip_address INET,
    user_agent TEXT,
    
    -- Settlement details
    settlement_reason TEXT,
    settlement_data JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (settled_at IS NULL OR settled_at >= placed_at),
    CHECK (status != 'pending' OR settled_at IS NULL),
    CHECK (status = 'pending' OR settled_at IS NOT NULL)
);

-- =====================================================
-- BETTING MARKET ODDS HISTORY TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS gaming_engine.betting_market_odds_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    outcome_id UUID NOT NULL REFERENCES gaming_engine.betting_outcomes(id) ON DELETE CASCADE,
    
    -- Odds change tracking
    old_odds DECIMAL(8,3),
    new_odds DECIMAL(8,3) NOT NULL,
    old_implied_probability DECIMAL(5,2),
    new_implied_probability DECIMAL(5,2),
    
    -- Change metadata
    change_reason VARCHAR(100) CHECK (change_reason IN ('bet_placement', 'manual_adjustment', 'system_rebalance', 'market_opening', 'market_closing')),
    change_amount DECIMAL(15,2),  -- bet amount that triggered change
    
    -- Timestamps
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Betting Markets indexes
CREATE INDEX IF NOT EXISTS idx_betting_markets_session_id ON gaming_engine.betting_markets(session_id) WHERE session_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_betting_markets_tournament_id ON gaming_engine.betting_markets(tournament_id) WHERE tournament_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_betting_markets_status ON gaming_engine.betting_markets(status);
CREATE INDEX IF NOT EXISTS idx_betting_markets_market_type ON gaming_engine.betting_markets(market_type);
CREATE INDEX IF NOT EXISTS idx_betting_markets_created_at ON gaming_engine.betting_markets(created_at);

-- Betting Outcomes indexes
CREATE INDEX IF NOT EXISTS idx_betting_outcomes_market_id ON gaming_engine.betting_outcomes(market_id);
CREATE INDEX IF NOT EXISTS idx_betting_outcomes_is_active ON gaming_engine.betting_outcomes(is_active);
CREATE INDEX IF NOT EXISTS idx_betting_outcomes_external_id ON gaming_engine.betting_outcomes(external_id) WHERE external_id IS NOT NULL;

-- User Bets indexes
CREATE INDEX IF NOT EXISTS idx_user_bets_user_id ON gaming_engine.user_bets(user_id);
CREATE INDEX IF NOT EXISTS idx_user_bets_market_id ON gaming_engine.user_bets(market_id);
CREATE INDEX IF NOT EXISTS idx_user_bets_outcome_id ON gaming_engine.user_bets(outcome_id);
CREATE INDEX IF NOT EXISTS idx_user_bets_status ON gaming_engine.user_bets(status);
CREATE INDEX IF NOT EXISTS idx_user_bets_placed_at ON gaming_engine.user_bets(placed_at);
CREATE INDEX IF NOT EXISTS idx_user_bets_user_status ON gaming_engine.user_bets(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_bets_user_placed_at ON gaming_engine.user_bets(user_id, placed_at DESC);

-- Odds History indexes
CREATE INDEX IF NOT EXISTS idx_odds_history_outcome_id ON gaming_engine.betting_market_odds_history(outcome_id);
CREATE INDEX IF NOT EXISTS idx_odds_history_timestamp ON gaming_engine.betting_market_odds_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_odds_history_outcome_timestamp ON gaming_engine.betting_market_odds_history(outcome_id, timestamp DESC);

-- =====================================================
-- UPDATE EXISTING TABLES TO SUPPORT BETTING
-- =====================================================

-- Add betting_markets relationship to game_sessions
-- Note: The foreign key relationship is established through the betting_markets table

-- Add betting_markets relationship to tournaments  
-- Note: The foreign key relationship is established through the betting_markets table

-- =====================================================
-- TRIGGER FUNCTIONS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update betting market statistics when bets are placed
CREATE OR REPLACE FUNCTION update_betting_market_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update market stats
        UPDATE gaming_engine.betting_markets 
        SET 
            total_pool = total_pool + NEW.bet_amount,
            total_bets = total_bets + 1,
            updated_at = NOW()
        WHERE id = NEW.market_id;
        
        -- Update outcome stats
        UPDATE gaming_engine.betting_outcomes
        SET 
            total_bet_amount = total_bet_amount + NEW.bet_amount,
            bet_count = bet_count + 1,
            updated_at = NOW()
        WHERE id = NEW.outcome_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Update market stats (for cancelled bets)
        UPDATE gaming_engine.betting_markets 
        SET 
            total_pool = total_pool - OLD.bet_amount,
            total_bets = total_bets - 1,
            updated_at = NOW()
        WHERE id = OLD.market_id;
        
        -- Update outcome stats
        UPDATE gaming_engine.betting_outcomes
        SET 
            total_bet_amount = total_bet_amount - OLD.bet_amount,
            bet_count = bet_count - 1,
            updated_at = NOW()
        WHERE id = OLD.outcome_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_update_betting_stats ON gaming_engine.user_bets;
CREATE TRIGGER trigger_update_betting_stats
    AFTER INSERT OR DELETE ON gaming_engine.user_bets
    FOR EACH ROW EXECUTE FUNCTION update_betting_market_stats();

-- Function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS trigger_betting_markets_updated_at ON gaming_engine.betting_markets;
CREATE TRIGGER trigger_betting_markets_updated_at
    BEFORE UPDATE ON gaming_engine.betting_markets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_betting_outcomes_updated_at ON gaming_engine.betting_outcomes;
CREATE TRIGGER trigger_betting_outcomes_updated_at
    BEFORE UPDATE ON gaming_engine.betting_outcomes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_user_bets_updated_at ON gaming_engine.user_bets;
CREATE TRIGGER trigger_user_bets_updated_at
    BEFORE UPDATE ON gaming_engine.user_bets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE gaming_engine.betting_markets IS 'Betting markets for sessions and tournaments';
COMMENT ON TABLE gaming_engine.betting_outcomes IS 'Possible outcomes for betting markets';
COMMENT ON TABLE gaming_engine.user_bets IS 'Individual user bets on market outcomes';
COMMENT ON TABLE gaming_engine.betting_market_odds_history IS 'Historical record of odds changes';

COMMENT ON COLUMN gaming_engine.betting_markets.market_type IS 'Type of betting market: match_winner, tournament_winner, player_performance, special_events';
COMMENT ON COLUMN gaming_engine.betting_markets.status IS 'Market status: active, suspended, closed, settled, deleted';
COMMENT ON COLUMN gaming_engine.betting_outcomes.odds IS 'American odds format (positive = underdog, negative = favorite)';
COMMENT ON COLUMN gaming_engine.betting_outcomes.implied_probability IS 'Implied probability as percentage (0-100)';
COMMENT ON COLUMN gaming_engine.user_bets.status IS 'Bet status: pending, won, lost, void, cancelled';

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant appropriate permissions to application user
-- (Adjust role name based on your database setup)
GRANT SELECT, INSERT, UPDATE, DELETE ON gaming_engine.betting_markets TO gaming_engine_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON gaming_engine.betting_outcomes TO gaming_engine_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON gaming_engine.user_bets TO gaming_engine_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON gaming_engine.betting_market_odds_history TO gaming_engine_app;

-- Grant sequence usage (for UUID generation)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA gaming_engine TO gaming_engine_app;

-- Migration completed successfully
-- Tables: betting_markets, betting_outcomes, user_bets, betting_market_odds_history
-- Indexes: Performance optimized for common query patterns
-- Triggers: Automatic stats updates and timestamp management
-- Permissions: Granted to application user