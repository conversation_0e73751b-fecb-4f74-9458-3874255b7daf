-- Custom Betting Platform - Core Schema Migration
-- =================================================
-- 
-- Migration: 001_create_custom_betting_schema
-- Description: Create custom betting platform schema with P2P betting, matching engine, and escrow system
-- Author: Claude-DB
-- Date: 2025-07-21
-- Template Base: Gaming Engine betting patterns

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CREATE CUSTOM BETTING SCHEMA
-- =====================================================
CREATE SCHEMA IF NOT EXISTS custom_betting;

-- =====================================================
-- CUSTOM BETS TABLE (Core P2P Betting)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.custom_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Creator and identification
    creator_user_id UUID NOT NULL REFERENCES public.users(id),
    title VARCHAR(300) NOT NULL,
    description TEXT,
    slug VARCHAR(400) UNIQUE, -- For shareable URLs
    
    -- Event and verification criteria
    event_criteria TEXT NOT NULL,
    verification_source VARCHAR(200), -- URL or description of verification method
    verification_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Bet configuration
    bet_type VARCHAR(50) NOT NULL CHECK (bet_type IN ('binary', 'multiple_choice', 'numeric_range')),
    minimum_stake DECIMAL(15,2) NOT NULL CHECK (minimum_stake > 0),
    maximum_stake DECIMAL(15,2) CHECK (maximum_stake IS NULL OR maximum_stake >= minimum_stake),
    participant_limit INTEGER CHECK (participant_limit IS NULL OR participant_limit >= 2),
    
    -- Financial tracking
    total_stakes DECIMAL(15,2) DEFAULT 0 CHECK (total_stakes >= 0),
    total_participants INTEGER DEFAULT 0 CHECK (total_participants >= 0),
    escrow_balance DECIMAL(15,2) DEFAULT 0 CHECK (escrow_balance >= 0),
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'matching', 'active', 'settling', 'settled', 'cancelled', 'disputed')),
    resolution_status VARCHAR(20) CHECK (resolution_status IN ('pending', 'resolved', 'disputed', 'cancelled')),
    
    -- Timing
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    settlement_deadline TIMESTAMP WITH TIME ZONE,
    opens_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Market discovery and categorization
    category VARCHAR(50) NOT NULL,
    tags VARCHAR(100)[], -- Array of tags for search
    is_featured BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT true,
    
    -- Market configuration
    bet_config JSONB DEFAULT '{}',
    settlement_criteria JSONB,
    outcome_definitions JSONB NOT NULL, -- Define possible outcomes
    
    -- Dispute and resolution
    dispute_resolution_method VARCHAR(50) DEFAULT 'community_vote',
    arbitrator_id UUID REFERENCES public.users(id),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (deadline > opens_at),
    CHECK (verification_deadline >= deadline),
    CHECK (settlement_deadline IS NULL OR settlement_deadline >= deadline)
);

-- =====================================================
-- BET OUTCOMES TABLE (Flexible Outcome Definitions)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.bet_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id) ON DELETE CASCADE,
    
    -- Outcome details
    outcome_text VARCHAR(500) NOT NULL,
    outcome_key VARCHAR(100) NOT NULL, -- Machine-readable key (e.g., 'yes', 'no', 'team_a')
    description TEXT,
    
    -- Probability and odds (market-driven)
    current_odds DECIMAL(8,3), -- American odds format
    implied_probability DECIMAL(5,2) CHECK (implied_probability BETWEEN 0 AND 100),
    
    -- Betting statistics
    total_backing_amount DECIMAL(15,2) DEFAULT 0 CHECK (total_backing_amount >= 0),
    backer_count INTEGER DEFAULT 0 CHECK (backer_count >= 0),
    
    -- Resolution
    is_winning_outcome BOOLEAN DEFAULT NULL,
    settlement_evidence TEXT,
    settlement_confidence DECIMAL(3,2) CHECK (settlement_confidence BETWEEN 0 AND 1),
    
    -- Status and ordering
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Unique constraint for outcome keys within a bet
    UNIQUE(custom_bet_id, outcome_key)
);

-- =====================================================
-- BET PARTICIPANTS TABLE (Multi-party Position Tracking)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.bet_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id),
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    
    -- Position details
    stake_amount DECIMAL(15,2) NOT NULL CHECK (stake_amount > 0),
    potential_payout DECIMAL(15,2) NOT NULL CHECK (potential_payout >= stake_amount),
    actual_payout DECIMAL(15,2) CHECK (actual_payout IS NULL OR actual_payout >= 0),
    
    -- Position type
    position_type VARCHAR(20) NOT NULL CHECK (position_type IN ('backing', 'laying')), -- backing = betting for, laying = betting against
    
    -- Matching details
    matched_amount DECIMAL(15,2) DEFAULT 0 CHECK (matched_amount >= 0 AND matched_amount <= stake_amount),
    unmatched_amount DECIMAL(15,2) GENERATED ALWAYS AS (stake_amount - matched_amount) STORED,
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'matched', 'partially_matched', 'settled', 'withdrawn', 'cancelled')),
    
    -- Timing
    entry_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    exit_timestamp TIMESTAMP WITH TIME ZONE,
    settlement_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Risk management
    ip_address INET,
    user_agent TEXT,
    
    -- Settlement details
    settlement_reason TEXT,
    settlement_data JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (matched_amount <= stake_amount),
    UNIQUE(custom_bet_id, user_id, outcome_id) -- One position per user per outcome per bet
);

-- =====================================================
-- ESCROW TRANSACTIONS TABLE (Financial Security)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.escrow_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    user_id UUID NOT NULL REFERENCES public.users(id),
    participant_id UUID REFERENCES custom_betting.bet_participants(id),
    
    -- Transaction details
    transaction_type VARCHAR(30) NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal', 'payout', 'refund', 'fee_deduction')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(10) DEFAULT 'USD',
    
    -- Financial references
    reference_transaction_id VARCHAR(100), -- External payment system reference
    transaction_hash VARCHAR(100), -- Blockchain or payment hash if applicable
    
    -- Escrow status
    escrow_status VARCHAR(20) DEFAULT 'held' CHECK (escrow_status IN ('held', 'released', 'disputed', 'cancelled', 'failed')),
    
    -- Release conditions
    release_conditions JSONB,
    auto_release_at TIMESTAMP WITH TIME ZONE,
    
    -- Dispute handling
    dispute_reason TEXT,
    dispute_initiated_at TIMESTAMP WITH TIME ZONE,
    dispute_resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Processing
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES public.users(id),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- =====================================================
-- BET MATCHING QUEUE TABLE (Automated Matching Engine)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.bet_matching_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id),
    outcome_id UUID NOT NULL REFERENCES custom_betting.bet_outcomes(id),
    
    -- Matching preferences
    desired_stake DECIMAL(15,2) NOT NULL CHECK (desired_stake > 0),
    position_type VARCHAR(20) NOT NULL CHECK (position_type IN ('backing', 'laying')),
    acceptable_odds_min DECIMAL(8,3),
    acceptable_odds_max DECIMAL(8,3),
    
    -- Queue management
    queue_priority INTEGER DEFAULT 0, -- Higher numbers = higher priority
    queue_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Matching options
    partial_match_allowed BOOLEAN DEFAULT true,
    minimum_match_amount DECIMAL(15,2) CHECK (minimum_match_amount IS NULL OR minimum_match_amount <= desired_stake),
    auto_accept BOOLEAN DEFAULT false,
    
    -- Status
    status VARCHAR(20) DEFAULT 'queued' CHECK (status IN ('queued', 'matched', 'partially_matched', 'expired', 'cancelled', 'failed')),
    matched_amount DECIMAL(15,2) DEFAULT 0 CHECK (matched_amount >= 0 AND matched_amount <= desired_stake),
    
    -- Matching history
    matching_attempts INTEGER DEFAULT 0,
    last_matching_attempt TIMESTAMP WITH TIME ZONE,
    matching_notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- =====================================================
-- BET MATCHES TABLE (Completed Matches)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.bet_matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    backing_participant_id UUID NOT NULL REFERENCES custom_betting.bet_participants(id),
    laying_participant_id UUID NOT NULL REFERENCES custom_betting.bet_participants(id),
    
    -- Match details
    matched_amount DECIMAL(15,2) NOT NULL CHECK (matched_amount > 0),
    agreed_odds DECIMAL(8,3) NOT NULL,
    
    -- Timing
    matched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Match metadata
    matching_algorithm VARCHAR(50) DEFAULT 'fifo', -- first-in-first-out, price-time-priority, etc.
    match_quality_score DECIMAL(3,2) CHECK (match_quality_score BETWEEN 0 AND 1),
    
    -- Settlement
    settlement_status VARCHAR(20) DEFAULT 'active' CHECK (settlement_status IN ('active', 'settled', 'disputed')),
    settled_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Constraints
    CHECK (backing_participant_id != laying_participant_id)
);

-- =====================================================
-- SETTLEMENT DISPUTES TABLE (Dispute Resolution)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.settlement_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    disputing_user_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Dispute details
    dispute_type VARCHAR(50) NOT NULL CHECK (dispute_type IN ('outcome_disagreement', 'verification_dispute', 'technical_issue', 'fraud_claim')),
    dispute_reason TEXT NOT NULL,
    evidence_submitted TEXT,
    evidence_urls TEXT[], -- Array of evidence URLs
    
    -- Resolution process
    arbitrator_assigned UUID REFERENCES public.users(id),
    arbitrator_decision TEXT,
    community_votes_for INTEGER DEFAULT 0,
    community_votes_against INTEGER DEFAULT 0,
    
    -- Status and timeline
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'escalated', 'closed')),
    priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- Resolution
    resolution_timestamp TIMESTAMP WITH TIME ZONE,
    resolution_details TEXT,
    final_outcome VARCHAR(100),
    
    -- Financial impact
    dispute_fee DECIMAL(15,2) DEFAULT 0,
    resolution_fee DECIMAL(15,2) DEFAULT 0,
    refund_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Timing
    dispute_deadline TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- =====================================================
-- MARKET ANALYTICS TABLE (Performance Tracking)
-- =====================================================
CREATE TABLE IF NOT EXISTS custom_betting.market_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    custom_bet_id UUID NOT NULL REFERENCES custom_betting.custom_bets(id),
    
    -- Time period
    analysis_date DATE NOT NULL,
    analysis_period VARCHAR(20) NOT NULL CHECK (analysis_period IN ('hourly', 'daily', 'weekly')),
    
    -- Volume metrics
    total_volume DECIMAL(15,2) DEFAULT 0,
    participant_count INTEGER DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    
    -- Market dynamics
    liquidity_score DECIMAL(5,2) CHECK (liquidity_score BETWEEN 0 AND 100),
    volatility_index DECIMAL(5,2),
    spread_percentage DECIMAL(5,4),
    
    -- Outcome distribution
    outcome_probabilities JSONB, -- Probability distribution across outcomes
    odds_history JSONB, -- Historical odds data
    
    -- Performance indicators
    time_to_match_avg DECIMAL(10,2), -- Average time to match bets in seconds
    match_success_rate DECIMAL(5,4) CHECK (match_success_rate BETWEEN 0 AND 1),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for time periods
    UNIQUE(custom_bet_id, analysis_date, analysis_period)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Custom Bets indexes
CREATE INDEX IF NOT EXISTS idx_custom_bets_creator ON custom_betting.custom_bets(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_custom_bets_status ON custom_betting.custom_bets(status);
CREATE INDEX IF NOT EXISTS idx_custom_bets_category ON custom_betting.custom_bets(category);
CREATE INDEX IF NOT EXISTS idx_custom_bets_deadline ON custom_betting.custom_bets(deadline);
CREATE INDEX IF NOT EXISTS idx_custom_bets_created_at ON custom_betting.custom_bets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_custom_bets_featured ON custom_betting.custom_bets(is_featured, status) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_custom_bets_public ON custom_betting.custom_bets(is_public, status) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_custom_bets_slug ON custom_betting.custom_bets(slug) WHERE slug IS NOT NULL;

-- Bet Outcomes indexes
CREATE INDEX IF NOT EXISTS idx_bet_outcomes_custom_bet ON custom_betting.bet_outcomes(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_bet_outcomes_active ON custom_betting.bet_outcomes(custom_bet_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_bet_outcomes_outcome_key ON custom_betting.bet_outcomes(custom_bet_id, outcome_key);

-- Bet Participants indexes
CREATE INDEX IF NOT EXISTS idx_bet_participants_user ON custom_betting.bet_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_bet_participants_bet ON custom_betting.bet_participants(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_bet_participants_outcome ON custom_betting.bet_participants(outcome_id);
CREATE INDEX IF NOT EXISTS idx_bet_participants_status ON custom_betting.bet_participants(status);
CREATE INDEX IF NOT EXISTS idx_bet_participants_user_status ON custom_betting.bet_participants(user_id, status);
CREATE INDEX IF NOT EXISTS idx_bet_participants_entry_time ON custom_betting.bet_participants(entry_timestamp DESC);

-- Escrow Transactions indexes
CREATE INDEX IF NOT EXISTS idx_escrow_transactions_bet ON custom_betting.escrow_transactions(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_escrow_transactions_user ON custom_betting.escrow_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_escrow_transactions_status ON custom_betting.escrow_transactions(escrow_status);
CREATE INDEX IF NOT EXISTS idx_escrow_transactions_type ON custom_betting.escrow_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_escrow_transactions_created ON custom_betting.escrow_transactions(created_at DESC);

-- Bet Matching Queue indexes
CREATE INDEX IF NOT EXISTS idx_matching_queue_bet ON custom_betting.bet_matching_queue(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_matching_queue_status ON custom_betting.bet_matching_queue(status);
CREATE INDEX IF NOT EXISTS idx_matching_queue_priority ON custom_betting.bet_matching_queue(queue_priority DESC, queue_timestamp ASC);
CREATE INDEX IF NOT EXISTS idx_matching_queue_user ON custom_betting.bet_matching_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_matching_queue_outcome ON custom_betting.bet_matching_queue(outcome_id);

-- Bet Matches indexes
CREATE INDEX IF NOT EXISTS idx_bet_matches_bet ON custom_betting.bet_matches(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_bet_matches_backing ON custom_betting.bet_matches(backing_participant_id);
CREATE INDEX IF NOT EXISTS idx_bet_matches_laying ON custom_betting.bet_matches(laying_participant_id);
CREATE INDEX IF NOT EXISTS idx_bet_matches_timestamp ON custom_betting.bet_matches(matched_at DESC);

-- Settlement Disputes indexes
CREATE INDEX IF NOT EXISTS idx_settlement_disputes_bet ON custom_betting.settlement_disputes(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_settlement_disputes_user ON custom_betting.settlement_disputes(disputing_user_id);
CREATE INDEX IF NOT EXISTS idx_settlement_disputes_status ON custom_betting.settlement_disputes(status);
CREATE INDEX IF NOT EXISTS idx_settlement_disputes_arbitrator ON custom_betting.settlement_disputes(arbitrator_assigned);

-- Market Analytics indexes
CREATE INDEX IF NOT EXISTS idx_market_analytics_bet ON custom_betting.market_analytics(custom_bet_id);
CREATE INDEX IF NOT EXISTS idx_market_analytics_date ON custom_betting.market_analytics(analysis_date DESC);
CREATE INDEX IF NOT EXISTS idx_market_analytics_period ON custom_betting.market_analytics(analysis_period, analysis_date DESC);

-- =====================================================
-- TRIGGER FUNCTIONS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update custom bet statistics when participants join/leave
CREATE OR REPLACE FUNCTION update_custom_bet_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update bet stats when participant joins
        UPDATE custom_betting.custom_bets 
        SET 
            total_stakes = total_stakes + NEW.stake_amount,
            total_participants = total_participants + 1,
            updated_at = NOW()
        WHERE id = NEW.custom_bet_id;
        
        -- Update outcome stats
        UPDATE custom_betting.bet_outcomes
        SET 
            total_backing_amount = total_backing_amount + NEW.stake_amount,
            backer_count = backer_count + 1,
            updated_at = NOW()
        WHERE id = NEW.outcome_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Update bet stats when participant leaves (cancelled/withdrawn)
        UPDATE custom_betting.custom_bets 
        SET 
            total_stakes = total_stakes - OLD.stake_amount,
            total_participants = total_participants - 1,
            updated_at = NOW()
        WHERE id = OLD.custom_bet_id;
        
        -- Update outcome stats
        UPDATE custom_betting.bet_outcomes
        SET 
            total_backing_amount = total_backing_amount - OLD.stake_amount,
            backer_count = backer_count - 1,
            updated_at = NOW()
        WHERE id = OLD.outcome_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for custom bet statistics
DROP TRIGGER IF EXISTS trigger_update_custom_bet_stats ON custom_betting.bet_participants;
CREATE TRIGGER trigger_update_custom_bet_stats
    AFTER INSERT OR DELETE ON custom_betting.bet_participants
    FOR EACH ROW EXECUTE FUNCTION update_custom_bet_stats();

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS trigger_custom_bets_updated_at ON custom_betting.custom_bets;
CREATE TRIGGER trigger_custom_bets_updated_at
    BEFORE UPDATE ON custom_betting.custom_bets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_bet_outcomes_updated_at ON custom_betting.bet_outcomes;
CREATE TRIGGER trigger_bet_outcomes_updated_at
    BEFORE UPDATE ON custom_betting.bet_outcomes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_bet_participants_updated_at ON custom_betting.bet_participants;
CREATE TRIGGER trigger_bet_participants_updated_at
    BEFORE UPDATE ON custom_betting.bet_participants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_escrow_transactions_updated_at ON custom_betting.escrow_transactions;
CREATE TRIGGER trigger_escrow_transactions_updated_at
    BEFORE UPDATE ON custom_betting.escrow_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_matching_queue_updated_at ON custom_betting.bet_matching_queue;
CREATE TRIGGER trigger_matching_queue_updated_at
    BEFORE UPDATE ON custom_betting.bet_matching_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_settlement_disputes_updated_at ON custom_betting.settlement_disputes;
CREATE TRIGGER trigger_settlement_disputes_updated_at
    BEFORE UPDATE ON custom_betting.settlement_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON SCHEMA custom_betting IS 'Custom Betting Platform - Polymarket-style P2P betting system';

COMMENT ON TABLE custom_betting.custom_bets IS 'Core P2P custom betting markets created by users';
COMMENT ON TABLE custom_betting.bet_outcomes IS 'Possible outcomes for custom betting markets';
COMMENT ON TABLE custom_betting.bet_participants IS 'User positions in custom betting markets';
COMMENT ON TABLE custom_betting.escrow_transactions IS 'Financial escrow management for bet security';
COMMENT ON TABLE custom_betting.bet_matching_queue IS 'Queue for automated bet matching engine';
COMMENT ON TABLE custom_betting.bet_matches IS 'Completed matches between backing and laying participants';
COMMENT ON TABLE custom_betting.settlement_disputes IS 'Dispute resolution for bet settlements';
COMMENT ON TABLE custom_betting.market_analytics IS 'Analytics and performance tracking for markets';

COMMENT ON COLUMN custom_betting.custom_bets.bet_type IS 'Type of bet: binary (yes/no), multiple_choice, numeric_range';
COMMENT ON COLUMN custom_betting.custom_bets.status IS 'Bet lifecycle: open, matching, active, settling, settled, cancelled, disputed';
COMMENT ON COLUMN custom_betting.bet_participants.position_type IS 'Position type: backing (betting for), laying (betting against)';
COMMENT ON COLUMN custom_betting.escrow_transactions.transaction_type IS 'Transaction type: deposit, withdrawal, payout, refund, fee_deduction';

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant appropriate permissions to application user
-- (Adjust role name based on your database setup)
GRANT USAGE ON SCHEMA custom_betting TO gaming_engine_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA custom_betting TO gaming_engine_app;

-- Grant sequence usage (for UUID generation)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA custom_betting TO gaming_engine_app;

-- Migration completed successfully
-- Schema: custom_betting with 8 primary tables
-- Features: P2P betting, automated matching, escrow system, dispute resolution
-- Indexes: Performance optimized for high-volume operations
-- Triggers: Automatic statistics updates and timestamp management
-- Permissions: Granted to application user