import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Trophy, 
  Zap, 
  Users, 
  Medal,
  Shield,
  ChevronRight,
  Play,
  Star,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Globe,
  ChevronLeft,
  Eye,
  Crown
} from 'lucide-react';

// Enhanced Testimonials Carousel Component
const TestimonialsCarousel = ({ testimonials }: { testimonials: any[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    autoPlayRef.current = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [currentIndex]);

  return (
    <div className="relative">
      <div className="flex items-center justify-center">
        <button
          onClick={prevSlide}
          className="absolute left-0 z-10 p-3 glass rounded-full hover:bg-slate-700/80 transition-all duration-300 hover:scale-110"
          aria-label="Previous testimonial"
        >
          <ChevronLeft className="h-6 w-6 text-white" />
        </button>

        <div className="overflow-hidden mx-16">
          <div 
            className="flex transition-transform duration-700 ease-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {testimonials.map((testimonial, index) => (
              <div key={index} className="w-full flex-shrink-0 px-6">
                <div className="glass border border-white/10 max-w-3xl mx-auto rounded-2xl hover:scale-102 transition-transform duration-500">
                  <div className="p-10">
                    <div className="flex items-center space-x-6 mb-8">
                      <div className="h-20 w-20 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center text-4xl shadow-glow">
                        {testimonial.avatar}
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-white mb-1">{testimonial.name}</p>
                        <p className="text-slate-400 text-lg">{testimonial.role}</p>
                      </div>
                    </div>
                    <div className="flex mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-6 w-6 text-yellow-500 fill-yellow-500" />
                      ))}
                    </div>
                    <p className="text-xl text-slate-300 text-center italic leading-relaxed">"{testimonial.content}"</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={nextSlide}
          className="absolute right-0 z-10 p-3 glass rounded-full hover:bg-slate-700/80 transition-all duration-300 hover:scale-110"
          aria-label="Next testimonial"
        >
          <ChevronRight className="h-6 w-6 text-white" />
        </button>
      </div>

      {/* Enhanced dots indicator */}
      <div className="flex justify-center space-x-3 mt-8">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`h-3 w-3 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 w-12 shadow-glow'
                : 'bg-slate-600 hover:bg-slate-500'
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export const LandingPage = () => {
  const [email, setEmail] = useState('');
  const [jackpotAmount, setJackpotAmount] = useState(125000);
  const [userCount, setUserCount] = useState(15234);
  const navigate = useNavigate();

  // Enhanced counter animation
  useEffect(() => {
    const interval = setInterval(() => {
      setJackpotAmount(prev => prev + Math.floor(Math.random() * 100));
      setUserCount(prev => prev + Math.floor(Math.random() * 5));
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    navigate('/register', { state: { email } });
  };

  const features = [
    {
      icon: <Users className="h-7 w-7" />,
      title: "True P2P Gaming",
      description: "No house edge. Compete directly with other players for better odds and fair competition.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Zap className="h-7 w-7" />,
      title: "Instant Payouts",
      description: "Quick 30-second to 5-minute games with immediate rewards and seamless transactions.",
      gradient: "from-yellow-500 to-orange-500"
    },
    {
      icon: <Medal className="h-7 w-7" />,
      title: "Skill-Based Rewards",
      description: "Higher skills unlock better odds, exclusive tournaments, and premium gaming experiences.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: <Shield className="h-7 w-7" />,
      title: "Secure & Fair",
      description: "Transparent odds, secure transactions, and responsible gaming with enterprise-grade security.",
      gradient: "from-green-500 to-emerald-500"
    }
  ];

  const gameCategories = [
    { name: "Trivia Battles", icon: "🧠", players: "2.3k playing", description: "Test your knowledge" },
    { name: "Word Games", icon: "📝", players: "1.8k playing", description: "Linguistic challenges" },
    { name: "Logic Puzzles", icon: "🧩", players: "1.5k playing", description: "Mind-bending puzzles" },
    { name: "Sports Trivia", icon: "⚽", players: "3.1k playing", description: "Sports knowledge" },
    { name: "Quick Reaction", icon: "⚡", players: "2.7k playing", description: "Lightning-fast games" },
    { name: "Visual Games", icon: "👁️", players: "1.9k playing", description: "Perception challenges" }
  ];

  const testimonials = [
    {
      name: "Alex Chen",
      avatar: "🎯",
      role: "Pro Gamer & Streamer",
      content: "Finally, a platform where my skills actually matter! The P2P system is revolutionary. Won $2,500 last month alone.",
      rating: 5
    },
    {
      name: "Sarah Johnson",
      avatar: "🏆",
      role: "Sports Betting Expert",
      content: "Best odds I've found anywhere. The combination of games and sports betting is genius. Love the transparency!",
      rating: 5
    },
    {
      name: "Mike Williams",
      avatar: "🎮",
      role: "Weekend Warrior",
      content: "Quick games fit perfectly into my schedule. The skill-based matching is fair and I'm already up $800!",
      rating: 5
    }
  ];

  const stats = [
    { value: "$10M+", label: "Monthly Payouts", icon: <DollarSign className="w-6 h-6" /> },
    { value: "50K+", label: "Active Players", icon: <Users className="w-6 h-6" /> },
    { value: "95%", label: "Payout Rate", icon: <Trophy className="w-6 h-6" /> },
    { value: "24/7", label: "Support", icon: <Shield className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950">
      {/* Enhanced Navigation */}
      <nav className="fixed top-0 w-full z-50 glass border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 group">
              <div className="h-12 w-12 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-glow">
                <Trophy className="h-7 w-7 text-white" />
              </div>
              <h1 className="text-3xl font-black text-white">BetBet</h1>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              {['How it Works', 'Games', 'Sports', 'Leaderboards'].map((item) => (
                <button 
                  key={item}
                  className="text-slate-300 hover:text-white transition-colors duration-300 font-medium relative group py-2"
                >
                  {item}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full" />
                </button>
              ))}
            </div>
            
            <Link to="/login">
              <button className="px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-glow">
                Get Started
              </button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Enhanced Hero Section - Restored Original Design */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Enhanced Background System */}
        <div className="absolute inset-0 z-0">
          <img
            src="/wheel.png"
            alt="Gaming Background"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-black/60" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-950/50 to-slate-950" />
          
          {/* Animated particles overlay */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-yellow-500 rounded-full animate-float" />
            <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-purple-500 rounded-full animate-float" style={{animationDelay: '1s'}} />
            <div className="absolute top-1/2 left-1/2 w-1.5 h-1.5 bg-red-500 rounded-full animate-float" style={{animationDelay: '2s'}} />
          </div>
        </div>

        <div className="container mx-auto max-w-7xl relative z-10 px-6">
          <div className="text-center">
            {/* Enhanced live stats badge */}
            <div className="inline-flex items-center mb-8 glass rounded-full px-8 py-4 animate-slide-in-down border border-white/10 hover:scale-105 transition-transform duration-300">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-ping mr-3" />
              <Sparkles className="h-5 w-5 mr-3 text-green-400" />
              <span className="text-white font-semibold text-lg">
                Join {userCount.toLocaleString()} players winning daily
              </span>
            </div>
            
            {/* Enhanced main title */}
            <h1 className="text-6xl md:text-8xl lg:text-9xl font-black text-white mb-8 leading-none animate-slide-in-up">
              Where Skill Meets
              <span className="block bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 bg-clip-text text-transparent animate-gradient-shift bg-[length:200%_200%]">
                Opportunity
              </span>
            </h1>
            
            {/* Enhanced subtitle */}
            <p className="text-xl md:text-2xl text-slate-300 mb-16 max-w-4xl mx-auto leading-relaxed animate-fade-in" style={{animationDelay: '0.3s'}}>
              The world's first P2P gaming platform combining skill-based games with live sports betting. 
              <span className="bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent font-semibold"> No house edge, just pure competition.</span>
            </p>

            {/* Enhanced CTA form */}
            <div className="animate-scale-in mb-20" style={{animationDelay: '0.6s'}}>
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
                <div className="relative group">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-80 h-16 px-6 glass text-white placeholder:text-slate-400 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300 text-lg border border-white/10 hover:border-white/20"
                    required
                  />
                </div>
                <button 
                  type="submit"
                  className="h-16 px-10 bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 text-white rounded-2xl font-bold text-lg transition-all duration-300 flex items-center hover:scale-105 hover:shadow-glow-lg group"
                >
                  Start Playing Free
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform duration-300" />
                </button>
              </form>

              {/* Enhanced live stats display */}
              <div className="flex items-center justify-center space-x-16">
                <div className="text-center group cursor-pointer">
                  <div className="relative">
                    <p className="text-5xl md:text-6xl font-black text-green-500 mb-3 group-hover:scale-110 transition-transform duration-300">
                      ${jackpotAmount.toLocaleString()}
                    </p>
                    <div className="absolute inset-0 bg-green-500/20 blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                  </div>
                  <p className="text-slate-400 font-semibold text-lg">Current Jackpot</p>
                </div>
                
                <div className="h-20 w-px bg-gradient-to-b from-transparent via-slate-600 to-transparent" />
                
                <div className="text-center group cursor-pointer">
                  <div className="relative">
                    <p className="text-5xl md:text-6xl font-black text-purple-500 mb-3 group-hover:scale-110 transition-transform duration-300">
                      {userCount.toLocaleString()}
                    </p>
                    <div className="absolute inset-0 bg-purple-500/20 blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                  </div>
                  <p className="text-slate-400 font-semibold text-lg">Players Online</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-8 h-12 border-2 border-slate-500 rounded-full flex justify-center hover:border-purple-500 transition-colors duration-300">
            <div className="w-1.5 h-4 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full mt-3 animate-pulse" />
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-purple-900/10 via-pink-900/10 to-purple-900/10 border-y border-white/5">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group cursor-pointer">
                <div className="mb-4 mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 flex items-center justify-center text-purple-400 group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <p className="text-4xl md:text-5xl font-black text-white mb-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300">
                  {stat.value}
                </p>
                <p className="text-slate-400 font-medium text-lg">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-24 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-black text-white mb-6">Why Players Choose BetBet</h2>
            <p className="text-2xl text-slate-400">Experience the future of online gaming</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="glass border border-white/10 hover:border-white/20 transition-all duration-500 rounded-2xl group hover:scale-102">
                <div className="p-10">
                  <div className={`mb-6 h-16 w-16 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300 shadow-glow`}>
                    {feature.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-slate-400 text-lg leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Game Categories */}
      <section className="py-24 px-6 bg-slate-900/30">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-black text-white mb-6">Choose Your Challenge</h2>
            <p className="text-2xl text-slate-400">6 game categories, 100+ games, unlimited fun</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {gameCategories.map((category, index) => (
              <div key={index} className="group cursor-pointer">
                <div className="glass border border-white/10 rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-500 hover:scale-105 hover:shadow-glow">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <span className="text-4xl group-hover:scale-110 transition-transform duration-300">{category.icon}</span>
                      <div>
                        <h3 className="text-xl font-bold text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300">
                          {category.name}
                        </h3>
                        <p className="text-slate-400 text-sm">{category.description}</p>
                      </div>
                    </div>
                    <ChevronRight className="h-6 w-6 text-slate-400 group-hover:text-purple-500 group-hover:translate-x-1 transition-all duration-300" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-green-500 bg-green-500/10 px-3 py-1 rounded-full">
                      {category.players}
                    </span>
                    <div className="flex -space-x-2">
                      {[1,2,3].map((i) => (
                        <div key={i} className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 border-2 border-slate-800 group-hover:scale-110 transition-transform duration-300 shadow-glow" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Live Sports Section */}
      <section className="py-24 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="inline-flex items-center mb-6 bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 rounded-full text-lg font-semibold shadow-glow">
                <div className="w-3 h-3 bg-white rounded-full animate-ping mr-3" />
                <Clock className="h-4 w-4 mr-2" />
                LIVE NOW
              </div>
              <h2 className="text-5xl md:text-6xl font-black text-white mb-8">
                Bet on Live Sports
                <span className="block bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent">
                  With Real Players
                </span>
              </h2>
              <p className="text-xl text-slate-400 mb-10 leading-relaxed">
                Get the best odds on soccer, basketball, tennis, cricket, and more. 
                No bookmaker margins, just pure P2P betting with transparent, fair odds.
              </p>
              <div className="space-y-6 mb-10">
                {[
                  "Live odds updated every second",
                  "Bet against real players, not the house", 
                  "Cash out anytime during the match"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-4 group">
                    <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    </div>
                    <span className="text-slate-300 text-lg font-medium">{feature}</span>
                  </div>
                ))}
              </div>
              <button className="px-10 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-xl text-lg font-semibold transition-all duration-300 flex items-center hover:scale-105 hover:shadow-glow group">
                Explore Live Matches
                <Play className="ml-3 h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              {[
                { sport: "⚽", team1: "Barcelona", team2: "Real Madrid", score1: "2", score2: "1", time: "78' min", status: "LIVE", statusColor: "bg-red-500" },
                { sport: "🏀", team1: "Lakers", team2: "Warriors", score1: "89", score2: "92", time: "Q3 5:23", status: "LIVE", statusColor: "bg-red-500" },
                { sport: "🎾", team1: "Djokovic", team2: "Alcaraz", score1: "1.95", score2: "1.85", time: "Starting in 30 min", status: "SOON", statusColor: "bg-orange-500" },
                { sport: "🏏", team1: "India", team2: "Australia", score1: "245", score2: "6", time: "42.3 overs", status: "LIVE", statusColor: "bg-red-500" }
              ].map((match, index) => (
                <div key={index} className="glass border border-white/10 rounded-2xl p-6 hover:border-purple-500/50 transition-all duration-300 hover:scale-105 group">
                  <div className="flex items-center space-x-3 mb-4">
                    <span className="text-3xl group-hover:scale-110 transition-transform duration-300">{match.sport}</span>
                    <span className={`${match.statusColor} text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse`}>
                      {match.status}
                    </span>
                  </div>
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between">
                      <p className="text-white font-semibold">{match.team1}</p>
                      <p className="text-2xl font-black text-white">{match.score1}</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-white font-semibold">{match.team2}</p>
                      <p className="text-2xl font-black text-white">{match.score2}</p>
                    </div>
                  </div>
                  <p className="text-slate-400 text-sm">{match.time}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Testimonials Carousel */}
      <section className="py-24 px-6 bg-slate-900/30">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-black text-white mb-6">Players Love BetBet</h2>
            <p className="text-2xl text-slate-400">Join thousands of satisfied winners worldwide</p>
          </div>
          
          <TestimonialsCarousel testimonials={testimonials} />
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="py-24 px-6">
        <div className="container mx-auto max-w-5xl">
          <div className="glass border border-white/10 rounded-3xl hover:border-purple-500/30 transition-all duration-500">
            <div className="p-16 text-center">
              <div className="mb-8">
                <Crown className="w-16 h-16 text-yellow-500 mx-auto mb-6 animate-float" />
                <h2 className="text-5xl md:text-6xl font-black text-white mb-6">
                  Ready to Start Winning?
                </h2>
                <p className="text-2xl text-slate-300 leading-relaxed">
                  Sign up now and get <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent font-bold">$50 FREE</span> to start playing. No deposit required!
                </p>
              </div>
              
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-8">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full sm:w-80 h-16 px-6 glass text-white placeholder:text-slate-400 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500/50 text-lg border border-white/10 hover:border-white/20 transition-all duration-300"
                  required
                />
                <button 
                  type="submit"
                  className="h-16 px-10 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-2xl font-bold text-lg transition-all duration-300 flex items-center hover:scale-105 hover:shadow-glow group"
                >
                  Claim Your $50
                  <DollarSign className="ml-3 h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                </button>
              </form>
              
              <p className="text-slate-400 text-lg">
                No credit card required • Instant access • Start winning in minutes
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Footer */}
      <footer className="py-20 px-6 border-t border-white/10">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10">
            <div>
              <div className="flex items-center space-x-4 mb-6 group">
                <div className="h-12 w-12 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-glow">
                  <Trophy className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-2xl font-black text-white">BetBet</h3>
              </div>
              <p className="text-slate-400 text-lg leading-relaxed">The future of P2P gaming and sports betting.</p>
            </div>
            
            {[
              { title: "Games", items: ["Trivia", "Word Games", "Logic Puzzles", "Sports Trivia"] },
              { title: "Sports", items: ["Soccer", "Basketball", "Tennis", "Cricket"] },
              { title: "Support & Legal", items: ["How It Works", "Help Center", "Terms of Service", "Privacy Policy", "Responsible Gaming"] }
            ].map((section, index) => (
              <div key={index}>
                <h4 className="text-white font-bold mb-6 text-xl">{section.title}</h4>
                <ul className="space-y-3">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex}>
                      <a href="#" className="text-slate-400 hover:text-white transition-colors duration-300 text-lg hover:translate-x-1 inline-block">
                        {item}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          
          <div className="mt-16 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between">
            <p className="text-slate-400 text-lg">© {new Date().getFullYear()} BetBet. All rights reserved.</p>
            <div className="flex items-center space-x-6 mt-6 md:mt-0">
              {[
                { icon: <Shield className="h-4 w-4" />, text: "SSL Secured" },
                { icon: <Globe className="h-4 w-4" />, text: "Licensed & Regulated" }
              ].map((badge, index) => (
                <span key={index} className="glass border border-white/10 text-slate-400 px-4 py-2 rounded-full flex items-center text-sm font-medium hover:text-white hover:border-white/20 transition-all duration-300">
                  {badge.icon}
                  <span className="ml-2">{badge.text}</span>
                </span>
              ))}
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};