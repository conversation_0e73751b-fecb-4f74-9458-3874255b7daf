import React, { useState, useEffect } from 'react';
import { 
  Menu, 
  X, 
  Search, 
  Bell, 
  Wallet, 
  User, 
  ChevronDown,
  Home,
  Gamepad2,
  TrendingUp,
  Users,
  BarChart3,
  Trophy,
  MessageCircle,
  Settings,
  Play,
  Star,
  Clock,
  Zap,
  Activity,
  DollarSign,
  Target,
  Award,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

const BetBetDashboard = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [user] = useState({
    name: '<PERSON>',
    balance: 2547.80,
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  });

  // Mock real-time data
  const [liveStats, setLiveStats] = useState({
    onlineUsers: 1247,
    activeGames: 892,
    totalBets: 15420,
    jackpot: 125000
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setLiveStats(prev => ({
        onlineUsers: prev.onlineUsers + Math.floor(Math.random() * 10 - 5),
        activeGames: prev.activeGames + Math.floor(Math.random() * 6 - 3),
        totalBets: prev.totalBets + Math.floor(Math.random() * 20),
        jackpot: prev.jackpot + Math.floor(Math.random() * 1000 - 500)
      }));
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const services = [
    { id: 'gaming', name: 'Gaming Engine', icon: Gamepad2, status: 'online', users: 543 },
    { id: 'betting', name: 'Sports Betting', icon: Target, status: 'online', users: 324 },
    { id: 'trading', name: 'Odds Exchange', icon: TrendingUp, status: 'online', users: 189 },
    { id: 'experts', name: 'Expert Analysis', icon: Star, status: 'online', users: 76 },
    { id: 'analytics', name: 'Sports Analytics', icon: BarChart3, status: 'online', users: 234 },
    { id: 'leaderboards', name: 'Leaderboards', icon: Trophy, status: 'online', users: 156 },
    { id: 'wallet', name: 'Wallet Service', icon: Wallet, status: 'online', users: 89 }
  ];

  const navItems = [
    { id: 'overview', name: 'Overview', icon: Home },
    { id: 'gaming', name: 'Gaming', icon: Gamepad2, badge: '12' },
    { id: 'betting', name: 'Sports Betting', icon: Target },
    { id: 'trading', name: 'Trading', icon: TrendingUp },
    { id: 'community', name: 'Community', icon: Users, badge: '3' },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'leaderboards', name: 'Leaderboards', icon: Trophy }
  ];

  const recentGames = [
    { id: 1, name: 'Poker Championship', status: 'live', players: 234, prize: '$15,000' },
    { id: 2, name: 'Blackjack Tournament', status: 'starting', players: 89, prize: '$8,500' },
    { id: 3, name: 'Slots Frenzy', status: 'live', players: 456, prize: '$25,000' }
  ];

  const StatusBadge = ({ status }) => (
    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
      status === 'online' 
        ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
        : 'bg-red-500/20 text-red-400 border border-red-500/30'
    }`}>
      <div className={`w-1.5 h-1.5 rounded-full ${
        status === 'online' ? 'bg-green-400' : 'bg-red-400'
      } animate-pulse`} />
      {status}
    </div>
  );

  const ServiceCard = ({ service }) => (
    <div className="group relative overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:bg-slate-700/50 transition-all duration-300 cursor-pointer">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg">
            <service.icon className="w-6 h-6 text-blue-400" />
          </div>
          <StatusBadge status={service.status} />
        </div>
        
        <h3 className="font-semibold text-white mb-2">{service.name}</h3>
        <div className="flex items-center gap-2 text-sm text-slate-400">
          <Users className="w-4 h-4" />
          <span>{service.users} active users</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors"
            >
              {isSidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
            
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-lg flex items-center justify-center">
                <Trophy className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
                BetBet
              </span>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search games, events..."
                className="pl-10 pr-4 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all"
              />
            </div>
            
            <button className="relative p-2 hover:bg-slate-700/50 rounded-lg transition-colors">
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center">3</span>
            </button>
            
            <div className="flex items-center gap-3 px-3 py-2 bg-slate-700/50 rounded-lg border border-slate-600/50">
              <Wallet className="w-4 h-4 text-green-400" />
              <span className="font-semibold">${user.balance.toLocaleString()}</span>
            </div>
            
            <div className="flex items-center gap-2 cursor-pointer hover:bg-slate-700/50 rounded-lg px-3 py-2 transition-colors">
              <img src={user.avatar} alt="User" className="w-8 h-8 rounded-full" />
              <span className="font-medium">{user.name}</span>
              <ChevronDown className="w-4 h-4" />
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`${
          isSidebarOpen ? 'w-64' : 'w-0'
        } transition-all duration-300 overflow-hidden bg-slate-800/50 backdrop-blur-sm border-r border-slate-700/50`}>
          <nav className="p-4 space-y-2">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                    : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                }`}
              >
                <item.icon className="w-5 h-5" />
                <span className="font-medium">{item.name}</span>
                {item.badge && (
                  <span className="ml-auto px-2 py-0.5 bg-red-500 text-white text-xs rounded-full">
                    {item.badge}
                  </span>
                )}
              </button>
            ))}
          </nav>
          
          <div className="p-4 mt-8">
            <div className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg p-4 border border-slate-600/50">
              <h4 className="font-semibold mb-2 text-slate-200">Platform Status</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Online Users</span>
                  <span className="text-green-400 font-medium">{liveStats.onlineUsers.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Active Games</span>
                  <span className="text-blue-400 font-medium">{liveStats.activeGames}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Total Bets</span>
                  <span className="text-purple-400 font-medium">{liveStats.totalBets.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Page Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">Platform Overview</h1>
                  <p className="text-slate-400">Complete ecosystem overview - All services operational</p>
                </div>
                <div className="flex gap-3">
                  <button className="px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 border border-slate-600/50 rounded-lg transition-colors">
                    Service Status
                  </button>
                  <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 rounded-lg transition-all">
                    Refresh Data
                  </button>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-500/20 rounded-lg">
                      <Activity className="w-6 h-6 text-green-400" />
                    </div>
                    <ArrowUp className="w-5 h-5 text-green-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-1">{liveStats.onlineUsers.toLocaleString()}</h3>
                  <p className="text-green-400 text-sm">Online Users</p>
                </div>

                <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-blue-500/20 rounded-lg">
                      <Play className="w-6 h-6 text-blue-400" />
                    </div>
                    <ArrowUp className="w-5 h-5 text-blue-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-1">{liveStats.activeGames}</h3>
                  <p className="text-blue-400 text-sm">Active Games</p>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-purple-500/20 rounded-lg">
                      <Target className="w-6 h-6 text-purple-400" />
                    </div>
                    <ArrowUp className="w-5 h-5 text-purple-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-1">{liveStats.totalBets.toLocaleString()}</h3>
                  <p className="text-purple-400 text-sm">Total Bets Today</p>
                </div>

                <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-yellow-500/20 rounded-lg">
                      <Award className="w-6 h-6 text-yellow-400" />
                    </div>
                    <ArrowUp className="w-5 h-5 text-yellow-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-1">${liveStats.jackpot.toLocaleString()}</h3>
                  <p className="text-yellow-400 text-sm">Progressive Jackpot</p>
                </div>
              </div>

              {/* Services Grid */}
              <div>
                <h2 className="text-xl font-semibold text-white mb-4">Platform Services</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {services.map((service) => (
                    <ServiceCard key={service.id} service={service} />
                  ))}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Live Tournaments</h3>
                  <div className="space-y-4">
                    {recentGames.map((game) => (
                      <div key={game.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                        <div>
                          <h4 className="font-medium text-white">{game.name}</h4>
                          <p className="text-sm text-slate-400">{game.players} players • {game.prize}</p>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                          game.status === 'live' 
                            ? 'bg-red-500/20 text-red-400 border border-red-500/30' 
                            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                        }`}>
                          {game.status === 'live' ? 'LIVE' : 'Starting Soon'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <button className="p-4 bg-gradient-to-br from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30 border border-blue-500/30 rounded-lg transition-all text-left">
                      <Gamepad2 className="w-8 h-8 text-blue-400 mb-2" />
                      <p className="font-medium text-white">Join Game</p>
                      <p className="text-xs text-slate-400">Quick match</p>
                    </button>
                    <button className="p-4 bg-gradient-to-br from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 border border-green-500/30 rounded-lg transition-all text-left">
                      <Target className="w-8 h-8 text-green-400 mb-2" />
                      <p className="font-medium text-white">Place Bet</p>
                      <p className="text-xs text-slate-400">Live events</p>
                    </button>
                    <button className="p-4 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 hover:from-yellow-500/30 hover:to-orange-500/30 border border-yellow-500/30 rounded-lg transition-all text-left">
                      <TrendingUp className="w-8 h-8 text-yellow-400 mb-2" />
                      <p className="font-medium text-white">Trade</p>
                      <p className="text-xs text-slate-400">Odds exchange</p>
                    </button>
                    <button className="p-4 bg-gradient-to-br from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 border border-purple-500/30 rounded-lg transition-all text-left">
                      <Users className="w-8 h-8 text-purple-400 mb-2" />
                      <p className="font-medium text-white">Community</p>
                      <p className="text-xs text-slate-400">Join chat</p>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab !== 'overview' && (
            <div className="text-center py-12">
              <h2 className="text-2xl font-semibold text-white mb-4">
                {navItems.find(item => item.id === activeTab)?.name} Section
              </h2>
              <p className="text-slate-400">This section will contain the detailed interface for {activeTab}.</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default BetBetDashboard;