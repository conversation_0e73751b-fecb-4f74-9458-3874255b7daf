/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom color system preserving your original palette
        primary: {
          50: '#fef7ee',
          100: '#fdedd3',
          200: '#fbd7a5',
          300: '#f8bb6d',
          400: '#f59e0b', // Your original yellow
          500: '#d97706',
          600: '#b45309',
          700: '#92400e',
          800: '#78350f',
          900: '#451a03',
        },
        secondary: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7', // Your original purple
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        accent: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#10b981', // Your original green
          600: '#059669',
          700: '#047857',
          800: '#065f46',
          900: '#064e3b',
        },
        // Enhanced slate palette for dark theme
        slate: {
          750: '#293548',
          850: '#1a202c',
          925: '#0d1117',
          950: '#0f172a', // Your primary background
        },
        // Gaming-specific colors
        gaming: {
          'live': '#ef4444',
          'win': '#10b981',
          'lose': '#ef4444',
          'pending': '#f59e0b',
          'jackpot': '#a855f7',
        }
      },
      backgroundImage: {
        // Your signature gradients
        'gradient-primary': 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)',
        'gradient-accent': 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
        'gradient-dark': 'linear-gradient(145deg, #1e293b, #334155)',
        'gradient-glass': 'linear-gradient(145deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.6))',
        // Animated gradients
        'gradient-shift': 'linear-gradient(-45deg, #ef4444, #f59e0b, #a855f7, #ec4899)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        gaming: ['Orbitron', 'monospace'], // For gaming elements
      },
      fontSize: {
        'display': ['clamp(2.5rem, 5vw, 4.5rem)', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        '5xl': ['3rem', { lineHeight: '1.2' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        '4xl': '2rem',
        '5xl': '3rem',
      },
      boxShadow: {
        'glow': '0 0 20px rgba(139, 92, 246, 0.4)',
        'glow-lg': '0 0 30px rgba(139, 92, 246, 0.6)',
        'gaming': '0 20px 40px rgba(0, 0, 0, 0.3)',
        'inner-lg': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.3)',
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
        'gradient-shift': 'gradient-shift 3s ease infinite',
        'slide-in-up': 'slideInUp 0.6s ease-out',
        'slide-in-down': 'slideInDown 0.6s ease-out',
        'fade-in': 'fadeIn 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'shimmer': 'shimmer 2s linear infinite',
      },
      keyframes: {
        'pulse-glow': {
          '0%, 100%': { 
            opacity: '1',
            transform: 'scale(1)',
            boxShadow: '0 0 20px rgba(139, 92, 246, 0.4)'
          },
          '50%': { 
            opacity: '0.8',
            transform: 'scale(1.05)',
            boxShadow: '0 0 30px rgba(139, 92, 246, 0.6)'
          },
        },
        'gradient-shift': {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
        'slideInUp': {
          'from': {
            transform: 'translateY(30px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        'slideInDown': {
          'from': {
            transform: 'translateY(-30px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        'fadeIn': {
          'from': { opacity: '0' },
          'to': { opacity: '1' },
        },
        'scaleIn': {
          'from': {
            transform: 'scale(0.9)',
            opacity: '0',
          },
          'to': {
            transform: 'scale(1)',
            opacity: '1',
          },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'shimmer': {
          'from': { backgroundPosition: '200% 0' },
          'to': { backgroundPosition: '-200% 0' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      transitionDuration: {
        '400': '400ms',
        '600': '600ms',
      },
      scale: {
        '102': '1.02',
        '103': '1.03',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [
    // Custom utilities for gaming components
    function({ addUtilities, theme }) {
      const newUtilities = {
        // Glass morphism utility
        '.glass': {
          backgroundColor: 'rgba(30, 41, 59, 0.8)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-hover': {
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: 'rgba(139, 92, 246, 0.5)',
        },
        // Gaming card utilities
        '.card-gaming': {
          background: 'linear-gradient(145deg, #1e293b, #334155)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s ease',
        },
        '.card-gaming:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          borderColor: 'rgba(139, 92, 246, 0.3)',
        },
        // Text gradient utilities
        '.text-gradient-primary': {
          background: 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
        '.text-gradient-secondary': {
          background: 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
        // Skeleton loading utility
        '.skeleton': {
          background: 'linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%)',
          backgroundSize: '200% 100%',
          animation: 'shimmer 1.5s infinite',
        },
        // Live indicator utility
        '.live-indicator': {
          position: 'relative',
        },
        '.live-indicator::before': {
          content: '""',
          position: 'absolute',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          background: 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
          borderRadius: 'inherit',
          opacity: '0.1',
          animation: 'pulse-glow 2s ease-in-out infinite',
        },
        // Button hover effects
        '.btn-hover-lift': {
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        },
        '.btn-hover-lift:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)',
        },
        // Leaderboard rank utilities
        '.rank-1': {
          borderColor: '#f59e0b',
          boxShadow: '0 0 30px rgba(245, 158, 11, 0.2)',
        },
        '.rank-2': {
          borderColor: '#64748b',
          boxShadow: '0 0 20px rgba(100, 116, 139, 0.2)',
        },
        '.rank-3': {
          borderColor: '#92400e',
          boxShadow: '0 0 20px rgba(146, 64, 14, 0.2)',
        },
      }
      addUtilities(newUtilities)
    },
  ],
} '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        accent: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#10b981', // Your original green
          600: '#059669',
          700: '#047857',
          800: '#065f46',
          900: '#064e3b',
        },
        // Enhanced slate palette for dark theme
        slate: {
          750: '#293548',
          850: '#1a202c',
          925: '#0d1117',
          950: '#0f172a', // Your primary background
        },
        // Gaming-specific colors
        gaming: {
          'live': '#ef4444',
          'win': '#10b981',
          'lose': '#ef4444',
          'pending': '#f59e0b',
          'jackpot': '#a855f7',
        }
      },
      backgroundImage: {
        // Your signature gradients
        'gradient-primary': 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)',
        'gradient-accent': 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
        'gradient-dark': 'linear-gradient(145deg, #1e293b, #334155)',
        'gradient-glass': 'linear-gradient(145deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.6))',
        // Animated gradients
        'gradient-shift': 'linear-gradient(-45deg, #ef4444, #f59e0b, #a855f7, #ec4899)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        gaming: ['Orbitron', 'monospace'], // For gaming elements
      },
      fontSize: {
        'display': ['clamp(2.5rem, 5vw, 4.5rem)', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        '5xl': ['3rem', { lineHeight: '1.2' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        '4xl': '2rem',
        '5xl': '3rem',
      },
      boxShadow: {
        'glow': '0 0 20px rgba(139, 92, 246, 0.4)',
        'glow-lg': '0 0 30px rgba(139, 92, 246, 0.6)',
        'gaming': '0 20px 40px rgba(0, 0, 0, 0.3)',
        'inner-lg': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.3)',
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
        'gradient-shift': 'gradient-shift 3s ease infinite',
        'slide-in-up': 'slideInUp 0.6s ease-out',
        'slide-in-down': 'slideInDown 0.6s ease-out',
        'fade-in': 'fadeIn 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'shimmer': 'shimmer 2s linear infinite',
      },
      keyframes: {
        'pulse-glow': {
          '0%, 100%': { 
            opacity: '1',
            transform: 'scale(1)',
            boxShadow: '0 0 20px rgba(139, 92, 246, 0.4)'
          },
          '50%': { 
            opacity: '0.8',
            transform: 'scale(1.05)',
            boxShadow: '0 0 30px rgba(139, 92, 246, 0.6)'
          },
        },
        'gradient-shift': {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
        'slideInUp': {
          'from': {
            transform: 'translateY(30px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        'slideInDown': {
          'from': {
            transform: 'translateY(-30px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        'fadeIn': {
          'from': { opacity: '0' },
          'to': { opacity: '1' },
        },
        'scaleIn': {
          'from': {
            transform: 'scale(0.9)',
            opacity: '0',
          },
          'to': {
            transform: 'scale(1)',
            opacity: '1',
          },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'shimmer': {
          'from': { backgroundPosition: '200% 0' },
          'to': { backgroundPosition: '-200% 0' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      transitionDuration: {
        '400': '400ms',
        '600': '600ms',
      },
      scale: {
        '102': '1.02',
        '103': '1.03',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [
    // Custom utilities for gaming components
    function({ addUtilities, theme }) {
      const newUtilities = {
        // Glass morphism utility
        '.glass': {
          backgroundColor: 'rgba(30, 41, 59, 0.8)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-hover': {
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: 'rgba(139, 92, 246, 0.5)',
        },
        // Gaming card utilities
        '.card-gaming': {
          background: 'linear-gradient(145deg, #1e293b, #334155)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s ease',
        },
        '.card-gaming:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          borderColor: 'rgba(139, 92, 246, 0.3)',
        },
        // Text gradient utilities
        '.text-gradient-primary': {
          background: 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
        '.text-gradient-secondary': {
          background: 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
        // Skeleton loading utility
        '.skeleton': {
          background: 'linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%)',
          backgroundSize: '200% 100%',
          animation: 'shimmer 1.5s infinite',
        },
        // Live indicator utility
        '.live-indicator': {
          position: 'relative',
        },
        '.live-indicator::before': {
          content: '""',
          position: 'absolute',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          background: 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #a855f7 100%)',
          borderRadius: 'inherit',
          opacity: '0.1',
          animation: 'pulse-glow 2s ease-in-out infinite',
        },
        // Button hover effects
        '.btn-hover-lift': {
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        },
        '.btn-hover-lift:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)',
        },
        // Leaderboard rank utilities
        '.rank-1': {
          borderColor: '#f59e0b',
          boxShadow: '0 0 30px rgba(245, 158, 11, 0.2)',
        },
        '.rank-2': {
          borderColor: '#64748b',
          boxShadow: '0 0 20px rgba(100, 116, 139, 0.2)',
        },
        '.rank-3': {
          borderColor: '#92400e',
          boxShadow: '0 0 20px rgba(146, 64, 14, 0.2)',
        },
      }
      addUtilities(newUtilities)
    },
  ],
}