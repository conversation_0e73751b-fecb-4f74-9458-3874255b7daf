# 🎨 **<PERSON>-<PERSON>end Detailed API & UI Specifications**
**Cross-Platform Leaderboards & Social Competition System**

**Date**: January 22, 2025  
**From**: <PERSON>-<PERSON> + <PERSON>-Master  
**To**: <PERSON>-<PERSON>end  
**Module**: Advanced Leaderboards (Complete Technical Specifications)  
**Priority**: HIGH - Final Platform Component

---

## 🔌 **Complete API Specifications**

### **Base Configuration**
```typescript
const LEADERBOARDS_API_BASE = process.env.NEXT_PUBLIC_LEADERBOARDS_API || 'http://localhost:8006';
const WEBSOCKET_BASE = process.env.NEXT_PUBLIC_LEADERBOARDS_WS || 'ws://localhost:8006';

// API Client Configuration
const leaderboardsApi = axios.create({
  baseURL: `${LEADERBOARDS_API_BASE}/api/v1/leaderboards`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});
```

### **1. Unified Leaderboards API**

#### **GET /api/v1/leaderboards/unified-leaderboards**
```typescript
interface UnifiedLeaderboardRequest {
  view?: 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';
  affiliation_filter?: string; // UUID
  time_range?: 'daily' | 'weekly' | 'monthly' | 'all_time';
  limit?: number; // 1-100, default 50
  offset?: number; // default 0
}

interface UnifiedLeaderboardResponse {
  success: boolean;
  data: {
    leaderboard: LeaderboardEntry[];
    total_count: number;
    cache_info: {
      cached: boolean;
      cache_age_seconds: number;
      expires_in_seconds: number;
    };
    metadata: {
      last_updated: string;
      calculation_time_ms: number;
      view_type: string;
      time_range: string;
    };
  };
}

interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar_url?: string;
  affiliation?: {
    id: string;
    name: string;
    logo_url?: string;
  };
  
  // Unified Performance
  unified_score: number; // 0-10000
  platform_tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
  tier_progress: number; // 0.0-1.0
  
  // Module-specific scores
  gaming_score: number;
  gaming_change: number; // +/- change from previous period
  betting_score: number;
  betting_change: number;
  trading_score: number;
  trading_change: number;
  analyst_score: number;
  analyst_change: number;
  
  // Cross-platform metrics
  cross_platform_consistency: number; // 0.0-1.0
  active_modules: string[]; // ['gaming', 'betting', 'trading']
  
  // Recent achievements (last 3)
  recent_achievements: Achievement[];
  
  // Performance trends
  score_history: {
    timestamp: string;
    score: number;
  }[];
}

// Usage Example:
const { data } = await leaderboardsApi.get('/unified-leaderboards', {
  params: {
    view: 'unified',
    time_range: 'weekly',
    limit: 50,
    offset: 0
  }
});
```

#### **GET /api/v1/leaderboards/personal-ranking/{user_id}**
```typescript
interface PersonalRankingResponse {
  success: boolean;
  data: {
    user_id: string;
    global_ranking: {
      unified_rank: number;
      unified_score: number;
      percentile: number; // 0-100
      total_users: number;
    };
    
    module_rankings: {
      gaming: ModuleRanking;
      betting: ModuleRanking;
      trading: ModuleRanking;
      analyst: ModuleRanking;
    };
    
    platform_tier: {
      current: string;
      next: string;
      progress: number; // 0.0-1.0
      points_to_next: number;
    };
    
    performance_trends: {
      daily_scores: ScoreTrend[];
      weekly_scores: ScoreTrend[];
      monthly_scores: ScoreTrend[];
    };
    
    affiliation_ranking?: {
      rank_within_affiliation: number;
      affiliation_total_members: number;
      affiliation_percentile: number;
    };
    
    achievement_summary: {
      total_unlocked: number;
      total_available: number;
      recent_unlocks: Achievement[];
      progress_towards_next: AchievementProgress[];
    };
  };
}

interface ModuleRanking {
  rank: number;
  score: number;
  percentile: number;
  change_24h: number;
  change_7d: number;
  best_rank_ever: number;
  best_score_ever: number;
}

interface ScoreTrend {
  timestamp: string;
  unified_score: number;
  gaming_score: number;
  betting_score: number;
  trading_score: number;
  analyst_score: number;
}
```

#### **GET /api/v1/leaderboards/global-stats**
```typescript
interface GlobalStatsResponse {
  success: boolean;
  data: {
    platform_overview: {
      total_active_users: number;
      total_sessions_today: number;
      total_achievements_unlocked: number;
      active_competitions: number;
    };
    
    tier_distribution: {
      Bronze: { count: number; percentage: number; };
      Silver: { count: number; percentage: number; };
      Gold: { count: number; percentage: number; };
      Platinum: { count: number; percentage: number; };
      Diamond: { count: number; percentage: number; };
      Master: { count: number; percentage: number; };
    };
    
    module_popularity: {
      gaming: { active_users: number; sessions_today: number; };
      betting: { active_users: number; bets_today: number; };
      trading: { active_users: number; trades_today: number; };
      analyst: { active_users: number; picks_today: number; };
    };
    
    top_achievements_this_week: {
      achievement_code: string;
      unlock_count: number;
      achievement_name: string;
    }[];
    
    leaderboard_highlights: {
      biggest_climber: LeaderboardEntry;
      newcomer_of_week: LeaderboardEntry;
      most_consistent: LeaderboardEntry;
      achievement_hunter: LeaderboardEntry;
    };
  };
}
```

### **2. Achievement System API**

#### **GET /api/v1/leaderboards/achievements**
```typescript
interface AchievementListRequest {
  category?: 'multi_module' | 'specialist' | 'social_leadership' | 'consistency';
  modules?: string[]; // ['gaming', 'betting']
  tier?: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond';
  rarity?: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  user_id?: string; // Filter to user's achievements only
  unlocked_only?: boolean;
}

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  modules_required: string[];
  tier: string;
  rarity: string;
  points: number;
  icon_url: string;
  
  // Unlock requirements
  unlock_criteria: {
    conditions: AchievementCondition[];
    require_all: boolean; // AND vs OR logic
  };
  
  // User-specific data (if user_id provided)
  user_progress?: {
    unlocked: boolean;
    unlocked_at?: string;
    progress: number; // 0.0-1.0
    current_values: Record<string, any>;
    next_milestone: string;
  };
  
  // Global statistics
  unlock_statistics: {
    total_unlocked: number;
    unlock_percentage: number;
    first_unlocked_by?: string;
    first_unlocked_at?: string;
    recent_unlocks_count: number;
  };
}

interface AchievementCondition {
  type: 'score_threshold' | 'module_consistency' | 'streak' | 'comparison' | 'time_based';
  module?: string;
  metric: string;
  operator: 'gte' | 'lte' | 'eq' | 'between';
  value: number | number[];
  description: string;
}
```

#### **GET /api/v1/leaderboards/users/{user_id}/achievements**
```typescript
interface UserAchievementsResponse {
  success: boolean;
  data: {
    user_id: string;
    achievement_summary: {
      total_unlocked: number;
      total_available: number;
      completion_percentage: number;
      total_points_earned: number;
      
      by_category: {
        multi_module: { unlocked: number; total: number; };
        specialist: { unlocked: number; total: number; };
        social_leadership: { unlocked: number; total: number; };
        consistency: { unlocked: number; total: number; };
      };
      
      by_tier: {
        Bronze: { unlocked: number; total: number; };
        Silver: { unlocked: number; total: number; };
        Gold: { unlocked: number; total: number; };
        Platinum: { unlocked: number; total: number; };
        Diamond: { unlocked: number; total: number; };
      };
      
      by_rarity: {
        Common: { unlocked: number; total: number; };
        Rare: { unlocked: number; total: number; };
        Epic: { unlocked: number; total: number; };
        Legendary: { unlocked: number; total: number; };
      };
    };
    
    recent_unlocks: Achievement[]; // Last 10 unlocked
    in_progress: AchievementProgress[]; // Close to unlocking
    suggested_next: Achievement[]; // Recommended to pursue
    
    showcase_achievements: Achievement[]; // User's selected showcase
  };
}

interface AchievementProgress {
  achievement: Achievement;
  progress_percentage: number;
  current_values: Record<string, any>;
  missing_requirements: AchievementCondition[];
  estimated_completion: 'hours' | 'days' | 'weeks' | 'months';
}
```

### **3. Affiliation Competition API**

#### **GET /api/v1/leaderboards/affiliation-dominance**
```typescript
interface AffiliationDominanceResponse {
  success: boolean;
  data: {
    rankings: AffiliationRanking[];
    competition_period: {
      start_date: string;
      end_date: string;
      period_type: 'monthly' | 'quarterly' | 'seasonal';
    };
    next_update: string;
  };
}

interface AffiliationRanking {
  rank: number;
  affiliation: {
    id: string;
    name: string;
    logo_url?: string;
    description?: string;
    member_count: number;
  };
  
  dominance_metrics: {
    overall_dominance_score: number;
    rank_change: number; // +/- from previous period
    
    // Module-specific dominance
    gaming_dominance: number;
    betting_dominance: number;
    trading_dominance: number;
    analyst_dominance: number;
    
    // Team performance metrics
    average_member_score: number;
    top_performers_count: number; // Members in top 10%
    cross_platform_masters: number;
    total_achievements_unlocked: number;
  };
  
  strengths: string[]; // ['gaming', 'community_engagement']
  growth_opportunities: string[]; // ['trading', 'betting_accuracy']
  
  recent_highlights: {
    new_members_this_period: number;
    major_achievements_unlocked: Achievement[];
    top_individual_performances: {
      user_id: string;
      username: string;
      achievement_type: string;
      description: string;
    }[];
  };
}
```

### **4. Platform Championships API**

#### **GET /api/v1/leaderboards/platform-championships**
```typescript
interface PlatformChampionshipsResponse {
  success: boolean;
  data: {
    active_championships: Championship[];
    upcoming_championships: Championship[];
    completed_championships: Championship[];
  };
}

interface Championship {
  id: string;
  name: string;
  description: string;
  championship_type: 'ultimate' | 'seasonal' | 'themed' | 'affiliation_vs_affiliation';
  
  status: 'registration' | 'qualifying' | 'active' | 'finals' | 'completed';
  
  schedule: {
    registration_opens: string;
    registration_closes: string;
    start_date: string;
    end_date: string;
    finals_date?: string;
  };
  
  participation: {
    total_participants: number;
    max_participants?: number;
    participating_affiliations: string[];
    qualification_criteria: QualificationCriteria[];
  };
  
  events: ChampionshipEvent[];
  
  prize_structure: {
    total_prize_pool: number;
    currency: string;
    distribution: {
      position: number;
      prize_amount: number;
      percentage: number;
    }[];
  };
  
  current_standings?: {
    individual_leaders: LeaderboardEntry[];
    affiliation_leaders: AffiliationRanking[];
    phase_winners: {
      phase_name: string;
      winners: LeaderboardEntry[];
    }[];
  };
  
  user_participation?: {
    is_registered: boolean;
    current_rank?: number;
    points_earned: number;
    completed_events: string[];
    next_event?: ChampionshipEvent;
  };
}

interface ChampionshipEvent {
  id: string;
  name: string;
  event_type: 'gaming_tournament' | 'betting_challenge' | 'trading_competition' | 'cross_platform_challenge';
  modules_involved: string[];
  
  schedule: {
    start_time: string;
    end_time: string;
    duration_hours: number;
  };
  
  rules: {
    scoring_method: string;
    qualification_requirements?: string[];
    special_rules?: string[];
  };
  
  status: 'upcoming' | 'active' | 'completed';
  participants_count: number;
  
  results?: {
    winners: LeaderboardEntry[];
    final_standings: LeaderboardEntry[];
    statistics: Record<string, any>;
  };
}
```

### **5. Social Competition API**

#### **GET /api/v1/leaderboards/social-competition/challenges**
```typescript
interface ChallengesResponse {
  success: boolean;
  data: {
    user_challenges: {
      sent_challenges: Challenge[];
      received_challenges: Challenge[];
      active_challenges: Challenge[];
      completed_challenges: Challenge[];
    };
    
    public_challenges: Challenge[]; // Open for anyone to join
    trending_challenges: Challenge[]; // Popular this week
    
    challenge_statistics: {
      total_sent: number;
      total_received: number;
      win_rate: number;
      favorite_challenge_type: string;
    };
  };
}

interface Challenge {
  id: string;
  challenge_type: 'direct_duel' | 'accuracy_contest' | 'consistency_challenge' | 'cross_module_showdown';
  
  participants: {
    challenger: {
      user_id: string;
      username: string;
      avatar_url?: string;
      current_stats: Record<string, any>;
    };
    challenged?: {
      user_id: string;
      username: string;
      avatar_url?: string;
      current_stats: Record<string, any>;
    };
    spectators: string[]; // user_ids watching
  };
  
  challenge_config: {
    modules_involved: string[];
    duration: number; // hours
    success_criteria: ChallengeCriteria[];
    wager_amount?: number;
    prize_distribution?: number[]; // [winner_percentage, loser_percentage]
  };
  
  status: 'pending' | 'accepted' | 'active' | 'completed' | 'declined' | 'expired';
  
  timeline: {
    created_at: string;
    accepted_at?: string;
    started_at?: string;
    completed_at?: string;
    expires_at: string;
  };
  
  current_progress?: {
    challenger_progress: ChallengeProgress;
    challenged_progress?: ChallengeProgress;
    live_updates: ChallengeUpdate[];
  };
  
  results?: {
    winner_id?: string;
    final_scores: Record<string, number>;
    completion_summary: string;
    spectator_reactions: SpectatorReaction[];
  };
  
  community_engagement: {
    spectator_count: number;
    community_bets: CommunityChallengebet[];
    social_buzz_score: number;
  };
}

interface ChallengeCriteria {
  metric: string; // 'gaming_rating_increase', 'betting_accuracy', 'trading_roi'
  target_value?: number;
  comparison_type: 'absolute' | 'relative' | 'head_to_head';
  module: string;
  weight: number; // for multi-criteria challenges
}

interface ChallengeProgress {
  current_metrics: Record<string, number>;
  progress_percentage: number;
  milestones_achieved: string[];
  time_remaining_hours: number;
}
```

---

## 🔌 **WebSocket API Specifications**

### **Connection Endpoints**

#### **Global Leaderboard Updates**
```typescript
// Connection: ws://host/ws/leaderboards/global
interface GlobalLeaderboardMessage {
  type: 'RANKING_UPDATE' | 'GLOBAL_STATS_UPDATE' | 'TIER_DISTRIBUTION_CHANGE';
  timestamp: string;
  data: {
    // For RANKING_UPDATE
    affected_users?: {
      user_id: string;
      old_rank: number;
      new_rank: number;
      score_change: number;
    }[];
    
    // For GLOBAL_STATS_UPDATE
    updated_stats?: {
      total_active_users: number;
      active_competitions: number;
      achievements_unlocked_today: number;
    };
    
    // For TIER_DISTRIBUTION_CHANGE
    new_distribution?: Record<string, { count: number; percentage: number; }>;
  };
}
```

#### **Personal Updates**
```typescript
// Connection: ws://host/ws/leaderboards/personal/{user_id}
interface PersonalLeaderboardMessage {
  type: 'PERSONAL_RANKING_UPDATE' | 'ACHIEVEMENT_UNLOCK' | 'TIER_PROMOTION' | 'CHALLENGE_UPDATE';
  user_id: string;
  timestamp: string;
  data: {
    // For PERSONAL_RANKING_UPDATE
    ranking_changes?: {
      module: string;
      old_rank: number;
      new_rank: number;
      score_change: number;
      percentile_change: number;
    };
    
    // For ACHIEVEMENT_UNLOCK
    achievement?: {
      achievement: Achievement;
      points_earned: number;
      celebration_type: 'standard' | 'rare' | 'epic' | 'legendary';
    };
    
    // For TIER_PROMOTION
    tier_change?: {
      old_tier: string;
      new_tier: string;
      benefits_unlocked: string[];
    };
    
    // For CHALLENGE_UPDATE
    challenge_update?: {
      challenge_id: string;
      update_type: 'progress' | 'completion' | 'new_challenge';
      details: any;
    };
  };
}
```

#### **Championship Events**
```typescript
// Connection: ws://host/ws/leaderboards/championships/{championship_id}
interface ChampionshipMessage {
  type: 'STANDINGS_UPDATE' | 'EVENT_START' | 'EVENT_COMPLETE' | 'PARTICIPANT_UPDATE';
  championship_id: string;
  timestamp: string;
  data: {
    // For STANDINGS_UPDATE
    updated_standings?: {
      individual_leaders: LeaderboardEntry[];
      affiliation_leaders: AffiliationRanking[];
      significant_changes: {
        user_id: string;
        rank_change: number;
        points_gained: number;
      }[];
    };
    
    // For EVENT_START/COMPLETE
    event_update?: {
      event_id: string;
      event_name: string;
      status: string;
      participants_count: number;
      next_event?: ChampionshipEvent;
    };
  };
}
```

---

## 🎨 **Detailed UI Component Specifications**

### **1. Main Leaderboard Dashboard**

#### **Layout Structure**
```typescript
// Main container with responsive grid
const LeaderboardDashboard = () => (
  <div className="leaderboard-dashboard max-w-7xl mx-auto px-4 py-6">
    {/* Header Section */}
    <div className="mb-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-2">Platform Leaderboards</h1>
      <p className="text-gray-600">Compete across all BetBet gaming and betting activities</p>
    </div>
    
    {/* Personal Performance Overview */}
    <div className="mb-8">
      <UnifiedPerformanceCard />
    </div>
    
    {/* Main Leaderboard Section */}
    <div className="mb-8">
      <LeaderboardTabs />
    </div>
    
    {/* Social Competition Grid */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <AffiliationCompetitionPanel />
      <ActiveChallengesPanel />
    </div>
    
    {/* Achievement Gallery */}
    <div className="mb-8">
      <AchievementGalleryPanel />
    </div>
    
    {/* Platform Championships */}
    <div>
      <PlatformChampionshipsPanel />
    </div>
  </div>
);
```

#### **Responsive Breakpoints**
```css
/* Mobile First - Base styles for mobile */
.leaderboard-dashboard {
  @apply px-4 py-4 space-y-6;
}

.performance-card {
  @apply p-4;
}

.leaderboard-table {
  @apply text-sm;
}

/* Tablet - md breakpoint (768px+) */
@media (min-width: 768px) {
  .leaderboard-dashboard {
    @apply px-6 py-6 space-y-8;
  }
  
  .performance-card {
    @apply p-6;
  }
  
  .leaderboard-grid {
    @apply grid-cols-2;
  }
  
  .achievement-grid {
    @apply grid-cols-4;
  }
}

/* Desktop - lg breakpoint (1024px+) */
@media (min-width: 1024px) {
  .leaderboard-dashboard {
    @apply px-8 py-8;
  }
  
  .leaderboard-grid {
    @apply grid-cols-3;
  }
  
  .achievement-grid {
    @apply grid-cols-6;
  }
  
  .social-competition-grid {
    @apply grid-cols-2;
  }
}

/* Large Desktop - xl breakpoint (1280px+) */
@media (min-width: 1280px) {
  .leaderboard-dashboard {
    @apply max-w-7xl mx-auto;
  }
  
  .achievement-grid {
    @apply grid-cols-8;
  }
}
```

### **2. Unified Performance Card - Complete Spec**

```typescript
interface UnifiedPerformanceCardProps {
  userPerformance: PersonalRankingResponse['data'];
  isLoading?: boolean;
  showDetailed?: boolean;
  onNavigateToModule?: (module: string) => void;
}

const UnifiedPerformanceCard: React.FC<UnifiedPerformanceCardProps> = ({
  userPerformance,
  isLoading = false,
  showDetailed = true,
  onNavigateToModule
}) => {
  return (
    <Card className="unified-performance-card relative overflow-hidden">
      {/* Background Gradient based on tier */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-br opacity-10",
        getTierGradient(userPerformance.platform_tier.current)
      )} />
      
      <CardHeader className="relative z-10">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
              Platform Mastery
            </h2>
            <p className="text-gray-600">
              Your unified performance across all BetBet activities
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-right">
              <TierBadge 
                tier={userPerformance.platform_tier.current} 
                size="lg"
                showProgress={userPerformance.platform_tier.progress}
              />
              <p className="text-sm text-gray-500 mt-1">
                Global Rank #{userPerformance.global_ranking.unified_rank.toLocaleString()}
              </p>
              <p className="text-xs text-gray-400">
                Top {userPerformance.global_ranking.percentile.toFixed(1)}%
              </p>
            </div>
            
            {/* Quick Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVerticalIcon className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => window.open('/profile/performance', '_blank')}>
                  <TrendingUpIcon className="w-4 h-4 mr-2" />
                  Detailed Analytics
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <ShareIcon className="w-4 h-4 mr-2" />
                  Share Performance
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <TargetIcon className="w-4 h-4 mr-2" />
                  Set Goals
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="relative z-10 space-y-6">
        {/* Main Score Display */}
        <div className="text-center py-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <div className="text-4xl md:text-5xl font-bold text-indigo-600">
              {userPerformance.global_ranking.unified_score.toFixed(1)}
            </div>
            <div className="text-sm text-gray-500">
              / 10000
            </div>
          </div>
          
          {/* Tier Progress Bar */}
          <div className="max-w-md mx-auto">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>{userPerformance.platform_tier.current}</span>
              <span>{userPerformance.platform_tier.next}</span>
            </div>
            <Progress 
              value={userPerformance.platform_tier.progress * 100} 
              className="h-3 bg-gray-200"
              indicatorClassName={getTierProgressColor(userPerformance.platform_tier.current)}
            />
            <p className="text-xs text-gray-500 mt-1 text-center">
              {userPerformance.platform_tier.points_to_next} points to {userPerformance.platform_tier.next}
            </p>
          </div>
        </div>
        
        {/* Module Performance Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(userPerformance.module_rankings).map(([module, ranking]) => (
            <PerformanceMetricCard
              key={module}
              module={module}
              ranking={ranking}
              onClick={() => onNavigateToModule?.(module)}
              className="cursor-pointer hover:bg-gray-50 transition-colors"
            />
          ))}
        </div>
        
        {showDetailed && (
          <>
            {/* Cross-Platform Consistency */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-gray-900">Cross-Platform Consistency</h4>
                <Tooltip content="How consistently you perform across all platform modules">
                  <InfoIcon className="w-4 h-4 text-gray-400" />
                </Tooltip>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Progress 
                    value={userPerformance.cross_platform_consistency * 100} 
                    className="h-2"
                  />
                </div>
                <div className="text-sm font-medium text-gray-700">
                  {(userPerformance.cross_platform_consistency * 100).toFixed(0)}%
                </div>
              </div>
              
              <div className="flex flex-wrap gap-1 mt-2">
                {userPerformance.active_modules.map(module => (
                  <Badge key={module} variant="secondary" className="text-xs">
                    {module}
                  </Badge>
                ))}
              </div>
            </div>
            
            {/* Recent Achievements Preview */}
            {userPerformance.achievement_summary.recent_unlocks.length > 0 && (
              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">Recent Achievements</h4>
                  <Button variant="ghost" size="sm" onClick={() => {}}>
                    View All
                    <ChevronRightIcon className="w-4 h-4 ml-1" />
                  </Button>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {userPerformance.achievement_summary.recent_unlocks.slice(0, 4).map(achievement => (
                    <AchievementBadge 
                      key={achievement.id} 
                      achievement={achievement} 
                      size="sm"
                      showTooltip 
                    />
                  ))}
                  {userPerformance.achievement_summary.recent_unlocks.length > 4 && (
                    <div className="flex items-center text-sm text-gray-500 px-2 py-1 bg-gray-100 rounded">
                      +{userPerformance.achievement_summary.recent_unlocks.length - 4} more
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Performance Trend Chart */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-gray-900">Performance Trend</h4>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm" onClick={() => {}}>7D</Button>
                  <Button variant="ghost" size="sm" onClick={() => {}}>30D</Button>
                  <Button variant="ghost" size="sm" onClick={() => {}}>90D</Button>
                </div>
              </div>
              
              <div className="h-24">
                <MiniPerformanceChart 
                  data={userPerformance.performance_trends.daily_scores.slice(-7)}
                  height={96}
                />
              </div>
            </div>
          </>
        )}
      </CardContent>
      
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-20">
          <Loader2Icon className="w-6 h-6 animate-spin text-indigo-600" />
        </div>
      )}
    </Card>
  );
};
```

### **3. Leaderboard Table Component**

```typescript
interface LeaderboardTableProps {
  data: LeaderboardEntry[];
  view: 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';
  isLoading?: boolean;
  currentUserId?: string;
  onUserClick?: (userId: string) => void;
  showAffiliation?: boolean;
  showTrends?: boolean;
}

const LeaderboardTable: React.FC<LeaderboardTableProps> = ({
  data,
  view,
  isLoading = false,
  currentUserId,
  onUserClick,
  showAffiliation = true,
  showTrends = true
}) => {
  return (
    <div className="leaderboard-table-container">
      <Table>
        <TableHeader>
          <TableRow className="border-b-2 border-gray-200">
            <TableHead className="w-16 text-center">Rank</TableHead>
            <TableHead className="min-w-48">Player</TableHead>
            {view === 'unified' && (
              <>
                <TableHead className="text-center">Tier</TableHead>
                <TableHead className="text-right">Unified Score</TableHead>
                <TableHead className="text-right w-24">Gaming</TableHead>
                <TableHead className="text-right w-24">Betting</TableHead>
                <TableHead className="text-right w-24">Trading</TableHead>
                <TableHead className="text-right w-24">Analysis</TableHead>
              </>
            )}
            {view === 'gaming' && (
              <>
                <TableHead className="text-right">Gaming Score</TableHead>
                <TableHead className="text-right">Win Rate</TableHead>
                <TableHead className="text-right">Sessions</TableHead>
              </>
            )}
            {/* Similar for other views */}
            
            {showTrends && (
              <TableHead className="text-center w-32">Trend</TableHead>
            )}
            <TableHead className="w-40">Achievements</TableHead>
          </TableRow>
        </TableHeader>
        
        <TableBody>
          {data.map((entry, index) => (
            <TableRow 
              key={entry.user_id}
              className={cn(
                "hover:bg-gray-50 transition-colors cursor-pointer group",
                entry.user_id === currentUserId && "bg-indigo-50 hover:bg-indigo-100"
              )}
              onClick={() => onUserClick?.(entry.user_id)}
            >
              <TableCell className="text-center">
                <RankBadge 
                  rank={entry.rank} 
                  showChange={entry.rank_change}
                  size="sm"
                />
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10 ring-2 ring-white shadow-md">
                    <AvatarImage src={entry.avatar_url} alt={entry.username} />
                    <AvatarFallback className="bg-gradient-to-br from-indigo-400 to-purple-400 text-white font-semibold">
                      {entry.username.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="min-w-0 flex-1">
                    <p className="font-semibold text-gray-900 truncate group-hover:text-indigo-600 transition-colors">
                      {entry.username}
                      {entry.user_id === currentUserId && (
                        <Badge variant="secondary" className="ml-2 text-xs">You</Badge>
                      )}
                    </p>
                    
                    {showAffiliation && entry.affiliation && (
                      <div className="flex items-center gap-1 mt-1">
                        {entry.affiliation.logo_url && (
                          <img 
                            src={entry.affiliation.logo_url} 
                            alt=""
                            className="w-4 h-4 rounded-sm"
                          />
                        )}
                        <p className="text-sm text-gray-500 truncate">
                          {entry.affiliation.name}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </TableCell>
              
              {view === 'unified' && (
                <>
                  <TableCell className="text-center">
                    <TierBadge tier={entry.platform_tier} size="sm" />
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <div className="font-mono font-bold text-lg">
                      {entry.unified_score.toFixed(1)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {entry.tier_progress && (
                        <>{(entry.tier_progress * 100).toFixed(0)}% to next</>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <ScoreDisplay 
                      value={entry.gaming_score} 
                      change={entry.gaming_change}
                      format="decimal"
                    />
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <ScoreDisplay 
                      value={entry.betting_score} 
                      change={entry.betting_change}
                      format="decimal"
                    />
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <ScoreDisplay 
                      value={entry.trading_score} 
                      change={entry.trading_change}
                      format="decimal"
                    />
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <ScoreDisplay 
                      value={entry.analyst_score} 
                      change={entry.analyst_change}
                      format="decimal"
                    />
                  </TableCell>
                </>
              )}
              
              {showTrends && entry.score_history && (
                <TableCell className="text-center">
                  <MiniTrendChart 
                    data={entry.score_history.slice(-7)}
                    height={32}
                    width={80}
                    showChange
                  />
                </TableCell>
              )}
              
              <TableCell>
                <div className="flex items-center gap-1 flex-wrap">
                  {entry.recent_achievements.slice(0, 3).map(achievement => (
                    <AchievementBadge 
                      key={achievement.id} 
                      achievement={achievement} 
                      size="xs"
                      showTooltip
                    />
                  ))}
                  {entry.recent_achievements.length > 3 && (
                    <Tooltip content={`${entry.recent_achievements.length - 3} more achievements`}>
                      <div className="text-xs text-gray-500 bg-gray-100 rounded px-1 py-0.5">
                        +{entry.recent_achievements.length - 3}
                      </div>
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2Icon className="w-5 h-5 animate-spin" />
            <span>Loading leaderboard...</span>
          </div>
        </div>
      )}
      
      {/* Empty State */}
      {!isLoading && data.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <TrophyIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-lg font-medium mb-1">No rankings available</p>
          <p className="text-sm">Check back soon for updated leaderboards</p>
        </div>
      )}
    </div>
  );
};
```

---

This provides comprehensive API specifications and detailed UI component requirements for Claude-Frontend to implement the complete leaderboards interface.
