# 🗄️ **<PERSON>-DB Database Design Handoff**
**Cross-Platform Leaderboards & Social Competition System**

**Date**: January 22, 2025  
**From**: <PERSON>-<PERSON>  
**To**: <PERSON><PERSON><PERSON>  
**Module**: Advanced Leaderboards (Final Module - 1.8% remaining)  
**Priority**: HIGH - Platform Completion

---

## 🎯 **Database Design Mandate**

Design the database schema for BetBet's **Cross-Platform Leaderboards & Social Competition System** - the final module completing our 98.2% finished platform. This is not just a gaming leaderboard but a **unified social competition framework** spanning all six platform modules.

---

## 📋 **Core Requirements**

### **1. Unified Performance Tracking**
Every user action across all platform modules must feed into their social standing:

```sql
-- Primary unified performance table
CREATE TABLE unified_user_performance (
    user_id UUID REFERENCES users(id),
    affiliation_id UUID REFERENCES affiliations(id),
    
    -- P2P Gaming Performance (Module 1)
    gaming_stats JSONB DEFAULT '{}',  -- {chess: {rating: 1800, wins: 45}, poker: {...}}
    tournament_achievements JSONB DEFAULT '{}',  -- tournament wins, placements
    spectator_betting_accuracy DECIMAL(5,4),  -- accuracy betting on others' games
    
    -- Custom Betting Performance (Module 2)
    betting_stats JSONB DEFAULT '{}',  -- {total_bets: 156, win_rate: 0.67, roi: 0.15}
    bet_creation_success_rate DECIMAL(5,4),  -- how often their created bets attract participants
    dispute_resolution_rating DECIMAL(3,2),  -- community trust score
    
    -- Expert Analyst Performance (Module 3)
    analyst_tier VARCHAR(20),  -- bronze, silver, gold, diamond
    pick_accuracy DECIMAL(5,4),  -- overall prediction accuracy
    subscriber_satisfaction DECIMAL(3,2),  -- subscriber ratings
    revenue_generated INTEGER DEFAULT 0,  -- total subscription revenue
    
    -- Sports Analysis & Trading (Modules 4 & 5)
    trading_performance JSONB DEFAULT '{}',  -- {roi: 0.23, sharpe_ratio: 1.8, max_drawdown: 0.12}
    ai_chat_contributions INTEGER DEFAULT 0,  -- valuable insights shared
    prediction_model_accuracy DECIMAL(5,4),  -- AI model performance
    
    -- Meta-Platform Achievements
    cross_platform_consistency_score DECIMAL(5,4),  -- performance across multiple modules
    community_reputation INTEGER DEFAULT 0,  -- peer recognition score
    platform_loyalty_score DECIMAL(5,4),  -- engagement across all modules
    
    -- Composite Scores
    unified_platform_score DECIMAL(8,4),  -- overall platform mastery score
    platform_tier VARCHAR(20),  -- Bronze, Silver, Gold, Platinum, Diamond, Master
    tier_progress DECIMAL(3,2),  -- progress to next tier (0.0-1.0)
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP,
    
    PRIMARY KEY (user_id),
    CONSTRAINT valid_accuracy_scores CHECK (
        spectator_betting_accuracy BETWEEN 0 AND 1 AND
        bet_creation_success_rate BETWEEN 0 AND 1 AND
        pick_accuracy BETWEEN 0 AND 1 AND
        prediction_model_accuracy BETWEEN 0 AND 1 AND
        cross_platform_consistency_score BETWEEN 0 AND 1 AND
        platform_loyalty_score BETWEEN 0 AND 1
    ),
    CONSTRAINT valid_ratings CHECK (
        dispute_resolution_rating BETWEEN 1.0 AND 5.0 AND
        subscriber_satisfaction BETWEEN 1.0 AND 5.0
    ),
    CONSTRAINT valid_tier_progress CHECK (tier_progress BETWEEN 0.0 AND 1.0)
);

-- Indexes for performance
CREATE INDEX idx_unified_performance_platform_score ON unified_user_performance(unified_platform_score DESC);
CREATE INDEX idx_unified_performance_platform_tier ON unified_user_performance(platform_tier);
CREATE INDEX idx_unified_performance_affiliation ON unified_user_performance(affiliation_id);
CREATE INDEX idx_unified_performance_last_activity ON unified_user_performance(last_activity_at DESC);
```

### **2. Cross-Platform Achievement System**

```sql
-- Achievement definitions spanning multiple modules
CREATE TABLE cross_platform_achievements (
    achievement_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    achievement_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(30) NOT NULL, -- MULTI_MODULE, SPECIALIST, SOCIAL_LEADERSHIP, CONSISTENCY
    modules_required TEXT[] NOT NULL, -- array of modules involved
    tier VARCHAR(20) NOT NULL, -- Bronze, Silver, Gold, Platinum, Diamond
    points INTEGER NOT NULL,
    icon_url VARCHAR(255),
    rarity VARCHAR(20) DEFAULT 'Common', -- Common, Rare, Epic, Legendary
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User achievement tracking
CREATE TABLE user_cross_platform_achievements (
    user_id UUID REFERENCES users(id),
    achievement_id UUID REFERENCES cross_platform_achievements(achievement_id),
    achieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    achievement_data JSONB DEFAULT '{}', -- specific metrics that unlocked achievement
    celebration_shown BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (user_id, achievement_id)
);

-- Pre-populate key achievements
INSERT INTO cross_platform_achievements (achievement_code, name, description, category, modules_required, tier, points) VALUES
('TRIPLE_THREAT', 'Triple Threat', 'Excel in gaming, betting, and trading simultaneously', 'MULTI_MODULE', ARRAY['gaming', 'betting', 'trading'], 'Gold', 500),
('PLATFORM_DOMINATOR', 'Platform Dominator', 'Top 10% in at least 4 different modules', 'MULTI_MODULE', ARRAY['gaming', 'betting', 'trading', 'analysts'], 'Platinum', 1000),
('RENAISSANCE_PLAYER', 'Renaissance Player', 'Active and successful across all 6 modules', 'MULTI_MODULE', ARRAY['gaming', 'betting', 'analysts', 'sports', 'trading', 'leaderboards'], 'Diamond', 2000),
('CHESS_BETTING_MASTER', 'Chess Betting Master', 'High chess rating + high betting accuracy on chess games', 'SPECIALIST', ARRAY['gaming', 'betting'], 'Silver', 300),
('ANALYST_TRADER_COMBO', 'Analyst Trader Combo', 'Successful analyst + profitable trader', 'SPECIALIST', ARRAY['analysts', 'trading'], 'Gold', 750),
('AFFILIATION_AMBASSADOR', 'Affiliation Ambassador', 'Top performer representing multiple affiliations', 'SOCIAL_LEADERSHIP', ARRAY['gaming', 'betting'], 'Silver', 400),
('MONTHLY_CONSISTENCY', 'Monthly Consistency', 'Top performer across modules for 30 consecutive days', 'CONSISTENCY', ARRAY['gaming', 'betting', 'trading'], 'Gold', 600);
```

### **3. Affiliation Competition System**

```sql
-- Affiliation overall dominance tracking
CREATE TABLE affiliation_dominance (
    affiliation_id UUID REFERENCES affiliations(id) PRIMARY KEY,
    
    -- Gaming dominance metrics
    gaming_champions_count INTEGER DEFAULT 0,
    tournament_wins_total INTEGER DEFAULT 0,
    average_gaming_rating DECIMAL(6,2),
    
    -- Betting expertise metrics
    betting_masters_count INTEGER DEFAULT 0,
    successful_bet_creators_count INTEGER DEFAULT 0,
    betting_volume_leaders_count INTEGER DEFAULT 0,
    
    -- Trading excellence metrics
    profitable_traders_count INTEGER DEFAULT 0,
    total_trading_volume BIGINT DEFAULT 0,
    average_risk_management_score DECIMAL(5,4),
    
    -- Analyst expertise metrics
    verified_analysts_count INTEGER DEFAULT 0,
    total_analyst_revenue INTEGER DEFAULT 0,
    average_prediction_accuracy DECIMAL(5,4),
    
    -- Community engagement metrics
    community_leaders_count INTEGER DEFAULT 0,
    cross_platform_masters_count INTEGER DEFAULT 0,
    average_platform_loyalty DECIMAL(5,4),
    
    -- Overall dominance calculations
    overall_dominance_score DECIMAL(10,4),
    dominance_ranking INTEGER,
    strengths TEXT[], -- array of strength areas
    growth_opportunities TEXT[], -- array of improvement areas
    
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calculation_date DATE DEFAULT CURRENT_DATE
);

-- Affiliation championship participation
CREATE TABLE affiliation_championships (
    championship_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    championship_type VARCHAR(50) NOT NULL, -- ULTIMATE, SEASONAL, THEMED
    participating_affiliations UUID[] NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'UPCOMING', -- UPCOMING, ACTIVE, COMPLETED, CANCELLED
    
    -- Event structure
    events JSONB NOT NULL, -- detailed event configuration
    scoring_system JSONB NOT NULL,
    prize_distribution JSONB NOT NULL,
    
    -- Results
    final_rankings JSONB, -- filled when completed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **4. Real-Time Leaderboard Performance Tables**

```sql
-- Cached leaderboard rankings for fast retrieval
CREATE TABLE cached_leaderboard_rankings (
    ranking_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    leaderboard_type VARCHAR(50) NOT NULL, -- UNIFIED, GAMING, BETTING, TRADING, ANALYST
    affiliation_filter UUID, -- null for global rankings
    time_range VARCHAR(20) NOT NULL, -- DAILY, WEEKLY, MONTHLY, ALL_TIME
    
    -- Top performers data
    rankings JSONB NOT NULL, -- array of ranked users with scores
    total_participants INTEGER NOT NULL,
    
    -- Cache metadata
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    
    UNIQUE(leaderboard_type, affiliation_filter, time_range)
);

-- Performance tracking for real-time updates
CREATE TABLE performance_change_events (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    affiliation_id UUID REFERENCES affiliations(id),
    
    module VARCHAR(30) NOT NULL, -- source module of the performance change
    event_type VARCHAR(50) NOT NULL, -- GAMING_WIN, BET_SUCCESS, TRADE_PROFIT, etc.
    
    -- Performance impact
    score_changes JSONB NOT NULL, -- specific score changes
    ranking_impact JSONB, -- ranking changes across different leaderboards
    achievement_unlocked UUID, -- reference to achievement if any
    
    -- Context
    event_data JSONB, -- additional context about what caused the change
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE -- for batch processing
);

-- Indexes for real-time performance
CREATE INDEX idx_performance_events_user_time ON performance_change_events(user_id, timestamp DESC);
CREATE INDEX idx_performance_events_unprocessed ON performance_change_events(processed, timestamp) WHERE NOT processed;
CREATE INDEX idx_cached_rankings_type_filter ON cached_leaderboard_rankings(leaderboard_type, affiliation_filter);
```

### **5. Social Competition Features**

```sql
-- Direct challenges between users across modules
CREATE TABLE cross_module_challenges (
    challenge_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    challenger_id UUID REFERENCES users(id),
    challenged_id UUID REFERENCES users(id),
    
    challenge_type VARCHAR(50) NOT NULL, -- GAMING_DUEL, BETTING_ACCURACY, TRADING_SHOWDOWN
    modules_involved TEXT[] NOT NULL,
    
    -- Challenge parameters
    challenge_config JSONB NOT NULL, -- specific rules and parameters
    wager_amount INTEGER, -- optional betting component
    duration INTERVAL, -- how long challenge lasts
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, ACCEPTED, ACTIVE, COMPLETED, DECLINED
    result JSONB, -- challenge outcome when completed
    winner_id UUID REFERENCES users(id),
    
    -- Social aspects
    spectators UUID[], -- users watching this challenge
    community_bets JSONB, -- bets placed by spectators
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    
    CONSTRAINT no_self_challenge CHECK (challenger_id != challenged_id)
);

-- Community engagement and recognition
CREATE TABLE community_recognition (
    recognition_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    recognition_type VARCHAR(50) NOT NULL, -- HELPFUL_INSIGHT, GREAT_SPORTSMAN, COMMUNITY_BUILDER
    
    -- Recognition details
    given_by_user_id UUID REFERENCES users(id),
    module_context VARCHAR(30), -- where this recognition occurred
    description TEXT,
    points_awarded INTEGER DEFAULT 0,
    
    -- Validation
    validated BOOLEAN DEFAULT FALSE,
    validation_count INTEGER DEFAULT 0, -- how many users validated this recognition
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT no_self_recognition CHECK (user_id != given_by_user_id)
);
```

---

## 🚀 **Performance Requirements**

### **Query Performance Targets**
- Leaderboard retrieval: **<10ms** (cached)
- Performance score calculation: **<50ms**
- Real-time ranking updates: **<5ms** 
- Achievement checking: **<20ms**
- Affiliation dominance calculation: **<100ms**

### **Critical Indexes Needed**

```sql
-- Performance-critical indexes
CREATE INDEX CONCURRENTLY idx_unified_performance_composite ON unified_user_performance(unified_platform_score DESC, platform_tier, affiliation_id);
CREATE INDEX CONCURRENTLY idx_achievements_user_recent ON user_cross_platform_achievements(user_id, achieved_at DESC);
CREATE INDEX CONCURRENTLY idx_affiliation_dominance_ranking ON affiliation_dominance(dominance_ranking, overall_dominance_score DESC);
CREATE INDEX CONCURRENTLY idx_challenges_active ON cross_module_challenges(status, created_at DESC) WHERE status IN ('PENDING', 'ACCEPTED', 'ACTIVE');

-- Real-time update optimization
CREATE INDEX CONCURRENTLY idx_performance_events_realtime ON performance_change_events(timestamp DESC, processed) WHERE NOT processed;
```

---

## 📊 **Integration Points**

### **Module Integration Requirements**
The leaderboards system must integrate with existing module tables:

1. **Gaming Engine** (`gaming_engine` schema)
   - `game_sessions`, `tournaments`, `spectator_bets`
   - Trigger updates on game completions, tournament results

2. **Custom Betting** (`custom_betting` schema) 
   - `custom_bets`, `bet_participants`, `settlements`
   - Trigger updates on bet outcomes, creation success

3. **Expert Analysts** (`expert_analysts` schema)
   - `expert_profiles`, `picks`, `subscriptions`
   - Trigger updates on pick accuracy, subscriber metrics

4. **Sports Analysis** & **Odds Exchange** 
   - Trading performance, AI contributions, predictions
   - Trigger updates on trade completions, model accuracy

### **Required Database Triggers**

```sql
-- Example trigger for gaming performance updates
CREATE OR REPLACE FUNCTION update_gaming_performance()
RETURNS TRIGGER AS $$
BEGIN
    -- Update unified performance when game session completes
    INSERT INTO performance_change_events (user_id, module, event_type, score_changes)
    VALUES (NEW.winner_id, 'gaming', 'GAME_WIN', 
            jsonb_build_object('rating_change', NEW.rating_change, 'game_type', NEW.game_type));
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER gaming_session_complete
    AFTER UPDATE OF status ON gaming_engine.game_sessions
    FOR EACH ROW
    WHEN (NEW.status = 'COMPLETED' AND OLD.status != 'COMPLETED')
    EXECUTE FUNCTION update_gaming_performance();
```

---

## 🔄 **Real-Time Update Architecture**

### **Event Processing Pipeline**
1. Module activities generate `performance_change_events`
2. Background processor calculates unified score changes
3. WebSocket broadcasts real-time updates
4. Cached leaderboard rankings refreshed

### **Caching Strategy**
- **L1 Cache**: Redis for top 100 rankings (TTL: 5 minutes)
- **L2 Cache**: Database cached rankings (TTL: 15 minutes)  
- **L3 Cache**: Pre-computed aggregations (TTL: 1 hour)

---

## ✅ **Deliverables Expected**

1. **Complete database schema** with all tables, indexes, and constraints
2. **Database migration files** ready for deployment
3. **Performance optimization** queries and indexes
4. **Integration triggers** for all existing modules
5. **Data validation** functions and constraints
6. **Caching schema** for real-time performance

---

## 🎯 **Success Criteria**

- ✅ Sub-10ms leaderboard queries (cached)
- ✅ Real-time performance updates <5ms
- ✅ Supports 100K+ concurrent users
- ✅ Cross-module integration without breaking existing functionality
- ✅ Unified performance tracking across all 6 modules
- ✅ Social competition features ready for immediate frontend integration

---

**This completes BetBet's database architecture - make this final 1.8% count! 🚀**