-- BetBet Platform - Database Schema Template
-- ============================================
-- This template establishes the standard patterns for all module database schemas
-- All modules must follow these exact patterns for consistency and maintainability

-- Schema Organization Pattern
CREATE SCHEMA IF NOT EXISTS {module_name};

-- UUID Extension (required for all modules)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Standard User Table (shared across modules)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    avatar_url TEXT,
    balance DECIMAL(12,2) DEFAULT 0.00,
    
    -- Status and permissions
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    roles TEXT[] DEFAULT ARRAY['user'],
    permissions TEXT[] DEFAULT ARRAY[],
    
    -- Security fields
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    mfa_secret VARCHAR(255),
    mfa_enabled BOOLEAN DEFAULT false,
    
    -- Audit fields (REQUIRED for all tables)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    version INTEGER DEFAULT 1,
    
    -- Soft delete (REQUIRED for all tables)
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID
);

-- Standard Audit Log Table Pattern (REQUIRED for all modules)
CREATE TABLE {module_name}.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID REFERENCES public.users(id),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Standard Financial Transaction Table Pattern (REQUIRED for financial modules)
CREATE TABLE {module_name}.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_type VARCHAR(50) NOT NULL,
    user_id UUID NOT NULL REFERENCES public.users(id),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT NOT NULL,
    reference_id UUID,
    
    -- Double-entry bookkeeping fields
    debit_account VARCHAR(100) NOT NULL,
    credit_account VARCHAR(100) NOT NULL,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed')),
    
    -- External references
    external_transaction_id VARCHAR(255),
    payment_method VARCHAR(50),
    
    -- Metadata
    metadata JSONB,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- Standard Real-time Event Table Pattern (REQUIRED for real-time modules)
CREATE TABLE {module_name}.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    event_source VARCHAR(50) NOT NULL,
    event_target VARCHAR(50),
    user_id UUID REFERENCES public.users(id),
    session_id VARCHAR(255),
    
    -- Event data
    event_data JSONB NOT NULL,
    
    -- Processing status
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    
    -- Audit fields (REQUIRED)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id)
);

-- Standard Performance Metrics Table Pattern (REQUIRED for all modules)
CREATE TABLE {module_name}.performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(20),
    metric_tags JSONB,
    user_id UUID REFERENCES public.users(id),
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- EXAMPLE: Module-specific table following the pattern
-- Replace {entity_name} with actual entity name
CREATE TABLE {module_name}.{entity_name}s (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Entity-specific fields go here
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Status fields
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'cancelled')),
    is_public BOOLEAN DEFAULT true,
    
    -- Ownership
    owner_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Configuration (use JSONB for flexible configs)
    config JSONB DEFAULT '{}',
    
    -- Business logic fields
    -- Add specific fields here based on module requirements
    
    -- Relationship fields (always use UUIDs and foreign keys)
    parent_id UUID REFERENCES {module_name}.{entity_name}s(id),
    related_entity_id UUID, -- Add foreign key constraint separately
    
    -- Audit fields (REQUIRED for all tables)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    
    -- Soft delete (REQUIRED for all tables)
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES public.users(id)
);

-- REQUIRED: Update trigger for updated_at field (apply to all tables)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update trigger to all tables
CREATE TRIGGER update_{entity_name}s_updated_at BEFORE UPDATE ON {module_name}.{entity_name}s 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- REQUIRED: Audit trigger function (apply to all tables with sensitive data)
CREATE OR REPLACE FUNCTION {module_name}.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO {module_name}.audit_logs(table_name, record_id, action, new_values, user_id)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW), NEW.created_by);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO {module_name}.audit_logs(table_name, record_id, action, old_values, new_values, user_id)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW), NEW.updated_by);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO {module_name}.audit_logs(table_name, record_id, action, old_values, user_id)
        VALUES (TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD), OLD.deleted_by);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger to sensitive tables
CREATE TRIGGER audit_{entity_name}s AFTER INSERT OR UPDATE OR DELETE ON {module_name}.{entity_name}s
    FOR EACH ROW EXECUTE FUNCTION {module_name}.audit_trigger_function();

-- REQUIRED: Performance indexes (optimize for expected query patterns)
-- Primary key indexes are automatic, add these for common queries

-- User lookup indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_owner_id ON {module_name}.{entity_name}s(owner_id) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_status ON {module_name}.{entity_name}s(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_created_at ON {module_name}.{entity_name}s(created_at) WHERE deleted_at IS NULL;

-- Soft delete index (REQUIRED for all tables)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_deleted_at ON {module_name}.{entity_name}s(deleted_at);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_owner_status ON {module_name}.{entity_name}s(owner_id, status) WHERE deleted_at IS NULL;

-- JSONB indexes for configuration and metadata
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_config_gin ON {module_name}.{entity_name}s USING GIN(config) WHERE deleted_at IS NULL;

-- Full-text search index (if needed)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_{entity_name}s_search ON {module_name}.{entity_name}s USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, ''))) WHERE deleted_at IS NULL;

-- REQUIRED: Row Level Security (RLS) for multi-tenant isolation
ALTER TABLE {module_name}.{entity_name}s ENABLE ROW LEVEL SECURITY;

-- Basic RLS policy (customize based on module requirements)
CREATE POLICY {entity_name}s_owner_policy ON {module_name}.{entity_name}s
    FOR ALL TO authenticated_users
    USING (owner_id = current_user_id() OR has_permission('admin'));

-- REQUIRED: Data integrity constraints
-- Add check constraints for business rules
ALTER TABLE {module_name}.{entity_name}s ADD CONSTRAINT check_{entity_name}s_name_length CHECK (length(name) >= 3);

-- Add foreign key constraints for relationships
-- ALTER TABLE {module_name}.{entity_name}s ADD CONSTRAINT fk_{entity_name}s_related_entity 
--     FOREIGN KEY (related_entity_id) REFERENCES other_module.other_entities(id);

-- REQUIRED: Database functions for common operations
-- Soft delete function
CREATE OR REPLACE FUNCTION {module_name}.soft_delete_{entity_name}(entity_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE {module_name}.{entity_name}s 
    SET deleted_at = NOW(), deleted_by = user_id, updated_by = user_id
    WHERE id = entity_id AND deleted_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Restore from soft delete function
CREATE OR REPLACE FUNCTION {module_name}.restore_{entity_name}(entity_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE {module_name}.{entity_name}s 
    SET deleted_at = NULL, deleted_by = NULL, updated_by = user_id
    WHERE id = entity_id AND deleted_at IS NOT NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- REQUIRED: Performance statistics view
CREATE OR REPLACE VIEW {module_name}.{entity_name}s_stats AS
SELECT 
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE deleted_at IS NULL) as active_count,
    COUNT(*) FILTER (WHERE deleted_at IS NOT NULL) as deleted_count,
    COUNT(*) FILTER (WHERE status = 'active') as active_status_count,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_lifetime_seconds,
    MIN(created_at) as first_created,
    MAX(created_at) as last_created
FROM {module_name}.{entity_name}s;

-- REQUIRED: Module-specific helper functions
-- Get active entities for user
CREATE OR REPLACE FUNCTION {module_name}.get_user_{entity_name}s(user_id UUID, limit_count INTEGER DEFAULT 50)
RETURNS TABLE(
    id UUID,
    name VARCHAR(200),
    status VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT e.id, e.name, e.status, e.created_at
    FROM {module_name}.{entity_name}s e
    WHERE e.owner_id = user_id 
      AND e.deleted_at IS NULL
      AND e.status = 'active'
    ORDER BY e.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- REQUIRED: Cleanup function for old audit logs
CREATE OR REPLACE FUNCTION {module_name}.cleanup_old_audit_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM {module_name}.audit_logs 
    WHERE created_at < NOW() - INTERVAL '%d days', days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- REQUIRED: Performance monitoring table for query optimization
CREATE TABLE {module_name}.query_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_name VARCHAR(100) NOT NULL,
    execution_time_ms DECIMAL(10,3) NOT NULL,
    rows_affected INTEGER,
    query_plan_hash VARCHAR(64),
    user_id UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for query performance analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_name_time ON {module_name}.query_performance(query_name, execution_time_ms);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_created_at ON {module_name}.query_performance(created_at);

-- REQUIRED: Data seeding function for development/testing
CREATE OR REPLACE FUNCTION {module_name}.seed_development_data()
RETURNS VOID AS $$
BEGIN
    -- Insert test data for development environment
    -- This function should be idempotent
    
    IF NOT EXISTS (SELECT 1 FROM {module_name}.{entity_name}s WHERE name = 'Test Entity') THEN
        INSERT INTO {module_name}.{entity_name}s (name, description, owner_id, created_by)
        VALUES ('Test Entity', 'Test entity for development', 
                (SELECT id FROM public.users WHERE username = 'testuser' LIMIT 1),
                (SELECT id FROM public.users WHERE username = 'testuser' LIMIT 1));
    END IF;
END;
$$ LANGUAGE plpgsql;

-- REQUIRED: Module health check function
CREATE OR REPLACE FUNCTION {module_name}.health_check()
RETURNS TABLE(
    check_name VARCHAR(50),
    status VARCHAR(20),
    details TEXT
) AS $$
BEGIN
    -- Check table exists and is accessible
    RETURN QUERY
    SELECT 'table_access'::VARCHAR(50), 'healthy'::VARCHAR(20), 
           format('Table {module_name}.{entity_name}s accessible with %s records', 
                  COUNT(*)::TEXT)
    FROM {module_name}.{entity_name}s;
    
    -- Check indexes are being used
    RETURN QUERY
    SELECT 'index_usage'::VARCHAR(50), 'healthy'::VARCHAR(20), 
           'Primary indexes are active'::TEXT;
           
    -- Add more health checks as needed
END;
$$ LANGUAGE plpgsql;

-- REQUIRED: Security and compliance settings
-- Enable connection logging for audit compliance
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_statement = 'all';

-- Set row security policies
SET row_security = on;

-- REQUIRED: Database comments for documentation
COMMENT ON SCHEMA {module_name} IS 'BetBet {module_name} module schema';
COMMENT ON TABLE {module_name}.{entity_name}s IS 'Main entity table for {module_name} module';
COMMENT ON COLUMN {module_name}.{entity_name}s.id IS 'Primary key - UUID format for global uniqueness';
COMMENT ON COLUMN {module_name}.{entity_name}s.created_at IS 'Record creation timestamp - required for all tables';
COMMENT ON COLUMN {module_name}.{entity_name}s.updated_at IS 'Last update timestamp - automatically updated';
COMMENT ON COLUMN {module_name}.{entity_name}s.deleted_at IS 'Soft delete timestamp - NULL means active record';

-- REQUIRED: Grant appropriate permissions
-- Grant permissions to application roles
GRANT USAGE ON SCHEMA {module_name} TO betbet_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA {module_name} TO betbet_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA {module_name} TO betbet_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA {module_name} TO betbet_app;

-- Grant read-only access to reporting role
GRANT USAGE ON SCHEMA {module_name} TO betbet_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA {module_name} TO betbet_readonly;

-- REQUIRED: Module completion verification
DO $$
BEGIN
    RAISE NOTICE 'Schema {module_name} created successfully with all required components:';
    RAISE NOTICE '- Main entity table: {module_name}.{entity_name}s';
    RAISE NOTICE '- Audit logging: {module_name}.audit_logs';
    RAISE NOTICE '- Performance tracking: {module_name}.performance_metrics';
    RAISE NOTICE '- Event handling: {module_name}.events';
    RAISE NOTICE '- All required indexes and constraints';
    RAISE NOTICE '- Row level security enabled';
    RAISE NOTICE '- Helper functions and health checks';
    RAISE NOTICE 'Template compliance: VERIFIED';
END;
$$;