# BetBet Platform

A comprehensive unified betting and gaming platform with microservices architecture.

## 🏗️ Architecture Overview

BetBet is built using a microservices architecture with the following components:

### 🌐 Frontend Applications
- **Web Frontend** (Port 3000) - Main web application
- **Admin Dashboard** (Port 3001) - Admin management interface  
- **Mobile App** - React Native application with Expo

### 🔌 API & Communication
- **API Gateway** (Port 8000) - Unified entry point for all services
- **WebSocket Manager** (Port 8007) - Real-time communication hub

### 🎯 Backend Services
- **Gaming Engine** (Port 8001) - Core gaming functionality
- **Betting Service** (Port 8002) - Sports betting and markets
- **Trading/Odds Exchange** (Port 8003) - Trading platform
- **Expert Analysis** (Port 8004) - Expert predictions and insights
- **Sports Analysis** (Port 8005) - Sports data and analytics
- **Leaderboards** (Port 8006) - User rankings and achievements

### 🗄️ Data Layer
- **PostgreSQL** (Port 5432) - Primary database with separate schemas per service
- **Redis** (Port 6379) - Caching and real-time messaging
- **PgAdmin** (Port 5050) - Database administration interface

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- Node.js 18+ (for development)
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd BetBet

# Copy environment configuration
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 2. Start the Platform

```bash
# Start all services
./scripts/start.sh

# Or build and start
./scripts/start.sh --build-only  # Build only
./scripts/start.sh --no-build    # Start without building
```

### 3. Access Services

- **Web App**: http://localhost:3000
- **Admin Panel**: http://localhost:3001
- **API Gateway**: http://localhost:8000
- **PgAdmin**: http://localhost:5050 (<EMAIL> / admin123)

### 4. Check Status

```bash
./scripts/status.sh              # Full status
./scripts/status.sh --health     # Health checks only
./scripts/status.sh --ports      # Port status only
```

### 5. Stop Services

```bash
./scripts/stop.sh                # Stop all services
./scripts/stop.sh --remove       # Stop and remove containers
./scripts/stop.sh --clean        # Stop and clean system
```

## 🛠️ Development

### Development Environment

```bash
# Install all dependencies
./scripts/dev.sh install

# Start development infrastructure
./scripts/dev.sh start

# Run database migrations
./scripts/dev.sh migrate

# Run tests
./scripts/dev.sh test

# Lint code
./scripts/dev.sh lint
```

### Individual Service Development

Start infrastructure first:
```bash
./scripts/dev.sh start
```

Then start individual services in separate terminals:

```bash
# API Gateway
cd services/api-gateway && npm run dev

# WebSocket Manager  
cd services/websocket-manager && npm run dev

# Web Frontend
cd frontend/web && npm run dev

# Admin Dashboard
cd frontend/admin && npm run dev

# Mobile App
cd frontend/mobile && npm start
```

## 📊 Service Details

### API Gateway (Port 8000)
- Routes requests to appropriate microservices
- Handles authentication and authorization
- Rate limiting and CORS management
- Request/response logging

**Key Endpoints:**
- `/api/gaming/*` → Gaming Engine
- `/api/betting/*` → Betting Service  
- `/api/trading/*` → Trading Service
- `/api/experts/*` → Expert Analysis
- `/api/sports/*` → Sports Analysis
- `/api/leaderboards/*` → Leaderboards
- `/ws` → WebSocket Manager

### Database Schema

Each service has its own database:
- `betbet_gaming` - Games, sessions, tournaments, jackpots
- `betbet_betting` - Sports, markets, bets, odds
- `betbet_trading` - Trading accounts, orders, positions
- `betbet_experts` - Expert profiles, predictions, subscriptions
- `betbet_sports` - Sports statistics, analytics, insights
- `betbet_leaderboards` - Rankings, achievements, points

Shared tables in all databases:
- `users` - User accounts and profiles
- `user_sessions` - Session management
- `user_balances` - Account balances
- `transactions` - Financial transactions
- `notifications` - User notifications

## 🔧 Configuration

### Environment Variables

Key variables in `.env`:

```bash
# Database
POSTGRES_USER=betbet_user
POSTGRES_PASSWORD=betbet_password

# JWT Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# External APIs
ODDS_API_KEY=your-odds-api-key
SPORTS_DATA_API_KEY=your-sports-api-key

# Features
ENABLE_TRADING=true
ENABLE_EXPERT_ANALYSIS=true
```

### Service Configuration

Each service can be configured via environment variables or config files:

- **API Gateway**: `services/api-gateway/.env`
- **WebSocket Manager**: `services/websocket-manager/.env`
- **Frontend Apps**: Next.js configuration files

## 🧪 Testing

```bash
# Run all tests
./scripts/dev.sh test

# Test specific service
./scripts/dev.sh test api-gateway
./scripts/dev.sh test web

# Health checks
./scripts/start.sh --health-check
curl http://localhost:8000/health
```

## 📝 API Documentation

### Authentication

All protected endpoints require JWT tokens:

```bash
# Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Use token
curl -X GET http://localhost:8000/api/user/profile \
  -H "Authorization: Bearer <your-token>"
```

### Key Endpoints

```bash
# Gaming
GET  /api/gaming/games
POST /api/gaming/games/:id/join

# Betting  
GET  /api/betting/events
POST /api/betting/bets

# Trading
GET  /api/trading/instruments
POST /api/trading/orders

# Experts
GET  /api/experts/predictions
POST /api/experts/subscribe/:expertId

# Sports
GET  /api/sports/analytics
GET  /api/sports/statistics

# Leaderboards
GET  /api/leaderboards/global
GET  /api/leaderboards/user/:userId
```

### WebSocket Events

Connect to `ws://localhost:8007/ws`:

```javascript
// Authenticate
ws.send(JSON.stringify({
  type: 'authenticate',
  token: 'your-jwt-token'
}));

// Join room
ws.send(JSON.stringify({
  type: 'join_room',
  room: 'betting_lobby'
}));

// Send message
ws.send(JSON.stringify({
  type: 'send_message',
  room: 'betting_lobby',
  message: 'Hello world!'
}));
```

## 🔒 Security

- JWT-based authentication
- Role-based access control (user, vip, expert, admin, super_admin)
- Rate limiting on all endpoints
- CORS configuration
- Input validation and sanitization
- SQL injection protection
- XSS protection via Content Security Policy

## 📊 Monitoring

### Health Checks
All services expose `/health` endpoints for monitoring:

```bash
curl http://localhost:8000/health  # API Gateway
curl http://localhost:8001/health  # Gaming Engine
curl http://localhost:8007/health  # WebSocket Manager
```

### Logging
- Centralized logging via Winston
- Request/response logging in API Gateway
- Error tracking and alerting
- Performance monitoring

### Metrics
```bash
# WebSocket connection stats
curl http://localhost:8007/stats

# Service status
./scripts/status.sh --resources
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
```bash
cp .env.example .env.production
# Update with production values
```

2. **Build and Deploy**
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

3. **Database Migration**
```bash
# Run production migrations
docker-compose exec postgres psql -U betbet_user -f /docker-entrypoint-initdb.d/
```

### Scaling Services

```bash
# Scale specific services
docker-compose up -d --scale gaming-engine=3
docker-compose up -d --scale betting-service=2
```

## 🗂️ Project Structure

```
BetBet/
├── services/                 # Backend microservices
│   ├── api-gateway/         # API Gateway service
│   ├── websocket-manager/   # WebSocket service  
│   ├── gaming-engine/       # Gaming service
│   ├── betting-service/     # Betting service
│   ├── odds-exchange/       # Trading service
│   ├── expert-analysis/     # Expert service
│   ├── sports-analysis/     # Sports service
│   └── leaderboards-service/# Leaderboards service
├── frontend/                # Frontend applications
│   ├── web/                 # Next.js web app
│   ├── admin/              # Admin dashboard
│   └── mobile/             # React Native app
├── database/               # Database schemas
│   └── init/              # Migration scripts
├── scripts/               # Utility scripts
│   ├── start.sh          # Start platform
│   ├── stop.sh           # Stop platform  
│   ├── status.sh         # Check status
│   └── dev.sh            # Development tools
├── docker-compose.yml     # Docker orchestration
├── .env.example          # Environment template
└── README.md             # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Make changes and test (`./scripts/dev.sh test`)
4. Lint code (`./scripts/dev.sh lint`)
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Check the logs: `docker-compose logs -f [service-name]`
- View status: `./scripts/status.sh`
- Health checks: `curl http://localhost:8000/health`
- Issues: Create an issue in the repository

## 🎯 Roadmap

- [ ] Add comprehensive API documentation (Swagger/OpenAPI)
- [ ] Implement advanced analytics and reporting
- [ ] Add automated testing pipeline (CI/CD)
- [ ] Performance optimization and caching strategies
- [ ] Mobile app enhancements
- [ ] Advanced security features (2FA, OAuth)
- [ ] Multi-language support (i18n)
- [ ] Advanced trading features and algorithms