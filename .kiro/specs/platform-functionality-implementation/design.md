# Design Document

## Overview

This design document outlines the comprehensive implementation of the BetBet platform to transform it from a basic microservices skeleton into a fully functional betting and gaming platform. The design focuses on implementing core business logic, data persistence, real-time communication, and user interactions across all advertised services while maintaining the existing microservices architecture.

The platform will support user authentication, gaming with real mechanics, sports betting with live odds, trading functionality, expert analysis, sports analytics, leaderboards, and comprehensive admin tools. All services will be integrated through the API Gateway with real-time updates via WebSocket connections.

## Architecture

### High-Level Architecture

The platform maintains its microservices architecture with the following enhanced components:

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Web Frontend<br/>Next.js]
        ADMIN[Admin Dashboard<br/>Next.js]
        MOBILE[Mobile App<br/>React Native]
    end
    
    subgraph "API Layer"
        GATEWAY[API Gateway<br/>Express.js]
        WS[WebSocket Manager<br/>Socket.io]
    end
    
    subgraph "Business Services"
        AUTH[Authentication Service]
        GAMING[Gaming Engine<br/>FastAPI]
        BETTING[Betting Service<br/>FastAPI]
        TRADING[Trading Service<br/>FastAPI]
        EXPERTS[Expert Analysis<br/>FastAPI]
        SPORTS[Sports Analysis<br/>FastAPI]
        LEADERBOARDS[Leaderboards<br/>FastAPI]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL<br/>Multi-Database)]
        REDIS[(Redis<br/>Cache & Sessions)]
    end
    
    subgraph "External Services"
        ODDS_API[Odds API]
        SPORTS_API[Sports Data API]
        PAYMENT[Payment Gateway]
        EMAIL[Email Service]
    end
    
    WEB --> GATEWAY
    ADMIN --> GATEWAY
    MOBILE --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> GAMING
    GATEWAY --> BETTING
    GATEWAY --> TRADING
    GATEWAY --> EXPERTS
    GATEWAY --> SPORTS
    GATEWAY --> LEADERBOARDS
    
    WS --> REDIS
    
    AUTH --> POSTGRES
    GAMING --> POSTGRES
    BETTING --> POSTGRES
    TRADING --> POSTGRES
    EXPERTS --> POSTGRES
    SPORTS --> POSTGRES
    LEADERBOARDS --> POSTGRES
    
    BETTING --> ODDS_API
    SPORTS --> SPORTS_API
    AUTH --> EMAIL
    TRADING --> PAYMENT
```

### Service Communication

- **Synchronous**: HTTP/REST APIs through API Gateway
- **Asynchronous**: Redis pub/sub for service-to-service communication
- **Real-time**: WebSocket connections for live updates
- **Data Consistency**: Database transactions and event sourcing patterns

### Security Architecture

- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- API rate limiting and DDoS protection
- Input validation and sanitization
- Encrypted sensitive data storage
- Audit logging for all financial transactions

## Components and Interfaces

### 1. Authentication Service

**Purpose**: Centralized user authentication and authorization

**Key Components**:
- User registration and email verification
- JWT token generation and validation
- Password reset functionality
- Role-based access control
- Session management

**API Endpoints**:
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout
POST /api/auth/forgot-password
POST /api/auth/reset-password
GET  /api/auth/verify-email/:token
GET  /api/auth/profile
PUT  /api/auth/profile
```

### 2. Gaming Engine

**Purpose**: Manage games, sessions, and gameplay mechanics

**Key Components**:
- Game catalog management
- Session state management
- Score calculation and validation
- Tournament management
- Game result processing

**Game Types**:
- Word Scramble: Timed word puzzle game
- Trivia Challenge: Multiple choice questions
- Reaction Time: Speed-based challenges
- Slot Games: RNG-based slot machines
- Card Games: Blackjack, Poker variants

**API Endpoints**:
```
GET  /api/gaming/games
GET  /api/gaming/games/:id
POST /api/gaming/games/:id/join
POST /api/gaming/sessions/:id/action
GET  /api/gaming/sessions/:id/state
POST /api/gaming/sessions/:id/end
GET  /api/gaming/tournaments
POST /api/gaming/tournaments/:id/join
```

### 3. Betting Service

**Purpose**: Sports betting with live odds and market management

**Key Components**:
- Sports and league management
- Event and market creation
- Odds calculation and updates
- Bet placement and validation
- Settlement processing

**Market Types**:
- Match Winner (1X2)
- Over/Under totals
- Handicap betting
- Player props
- Live in-play betting

**API Endpoints**:
```
GET  /api/betting/sports
GET  /api/betting/events
GET  /api/betting/events/:id/markets
POST /api/betting/bets
GET  /api/betting/bets
GET  /api/betting/bets/:id
POST /api/betting/bets/:id/cashout
```

### 3.1. Custom Betting Service

**Purpose**: Peer-to-peer custom betting markets like Polymarket

**Key Components**:
- Custom market creation
- Peer-to-peer bet matching
- Escrow management
- Outcome resolution system
- Dispute handling mechanism

**Custom Bet Categories**:
- Politics (elections, policy outcomes)
- Entertainment (awards, celebrity events)
- Technology (product launches, crypto prices)
- Weather (temperature, natural disasters)
- Economics (market predictions, inflation)
- Sports (custom propositions)
- Social events (viral trends, memes)

**Custom Bet Features**:
- User-defined outcomes
- Binary and multi-outcome markets
- Time-based resolution
- Community-driven validation
- Reputation-based resolution
- Automated oracle integration

**API Endpoints**:
```
POST /api/custom-betting/markets
GET  /api/custom-betting/markets
GET  /api/custom-betting/markets/:id
POST /api/custom-betting/markets/:id/bet
GET  /api/custom-betting/categories
POST /api/custom-betting/resolve/:id
POST /api/custom-betting/dispute/:id
GET  /api/custom-betting/user-markets
```

### 4. Trading Service

**Purpose**: Odds exchange with order matching and position management

**Key Components**:
- Order book management
- Trade matching engine
- Position tracking
- P&L calculation
- Risk management

**Trading Features**:
- Back/Lay betting
- Order types (Market, Limit, Stop)
- Portfolio management
- Margin trading
- Automated trading rules

**API Endpoints**:
```
GET  /api/trading/instruments
GET  /api/trading/orderbook/:instrument
POST /api/trading/orders
GET  /api/trading/orders
DELETE /api/trading/orders/:id
GET  /api/trading/positions
GET  /api/trading/portfolio
```

### 5. Expert Analysis Service

**Purpose**: Expert predictions and subscription management

**Key Components**:
- Expert profile management
- Prediction creation and tracking
- Subscription handling
- Performance analytics
- Content delivery

**Expert Features**:
- Verified expert accounts
- Prediction accuracy tracking
- Subscription tiers
- Premium content access
- Expert rankings

**API Endpoints**:
```
GET  /api/experts/experts
GET  /api/experts/experts/:id
GET  /api/experts/predictions
POST /api/experts/predictions
POST /api/experts/subscribe/:expertId
GET  /api/experts/subscriptions
GET  /api/experts/leaderboard
```

### 6. Sports Analysis Service

**Purpose**: Comprehensive sports data and analytics

**Key Components**:
- Sports data aggregation
- Statistical analysis
- Trend identification
- Performance metrics
- Predictive modeling

**Analytics Features**:
- Team/player statistics
- Historical performance
- Head-to-head records
- Form analysis
- Injury reports

**API Endpoints**:
```
GET  /api/sports/analytics/teams/:id
GET  /api/sports/analytics/players/:id
GET  /api/sports/analytics/matches/:id
GET  /api/sports/statistics
GET  /api/sports/trends
GET  /api/sports/predictions
```

### 7. Leaderboards Service

**Purpose**: User rankings and achievement system

**Key Components**:
- Ranking calculations
- Achievement definitions
- Badge management
- Competition tracking
- Reward distribution

**Leaderboard Types**:
- Gaming high scores
- Betting profitability
- Trading performance
- Expert accuracy
- Overall platform activity

**API Endpoints**:
```
GET  /api/leaderboards/global
GET  /api/leaderboards/gaming
GET  /api/leaderboards/betting
GET  /api/leaderboards/trading
GET  /api/leaderboards/user/:id
POST /api/leaderboards/achievements
```

### 8. WebSocket Manager

**Purpose**: Real-time communication and live updates

**Key Components**:
- Connection management
- Room-based messaging
- Event broadcasting
- Authentication integration
- Reconnection handling

**Real-time Features**:
- Live odds updates
- Game state synchronization
- Chat functionality
- Notification delivery
- Activity feeds

**WebSocket Events**:
```
authenticate
join_room
leave_room
send_message
odds_update
game_update
notification
user_activity
```

## Data Models

### Core Entities

**User Model**:
```typescript
interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'vip' | 'expert' | 'admin' | 'super_admin';
  status: 'active' | 'suspended' | 'banned' | 'pending';
  emailVerified: boolean;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}
```

**Game Session Model**:
```typescript
interface GameSession {
  id: string;
  userId: string;
  gameId: string;
  status: 'active' | 'completed' | 'abandoned';
  startBalance: number;
  currentBalance: number;
  gameState: any;
  score: number;
  startedAt: Date;
  endedAt?: Date;
}
```

**Bet Model**:
```typescript
interface Bet {
  id: string;
  userId: string;
  eventId: string;
  marketId: string;
  selectionId: string;
  stake: number;
  odds: number;
  potentialWin: number;
  status: 'pending' | 'won' | 'lost' | 'void' | 'cashed_out';
  placedAt: Date;
  settledAt?: Date;
}
```

**Custom Market Model**:
```typescript
interface CustomMarket {
  id: string;
  creatorId: string;
  title: string;
  description: string;
  category: string;
  outcomes: string[];
  resolutionCriteria: string;
  resolutionDate: Date;
  status: 'active' | 'resolved' | 'disputed' | 'cancelled';
  totalVolume: number;
  participantCount: number;
  createdAt: Date;
  resolvedAt?: Date;
  winningOutcome?: string;
}
```

**Custom Bet Model**:
```typescript
interface CustomBet {
  id: string;
  marketId: string;
  userId: string;
  outcome: string;
  stake: number;
  odds: number;
  matchedWith?: string; // Other user's bet ID
  status: 'pending' | 'matched' | 'won' | 'lost' | 'disputed';
  placedAt: Date;
  matchedAt?: Date;
  settledAt?: Date;
}
```

**Trade Order Model**:
```typescript
interface TradeOrder {
  id: string;
  userId: string;
  instrumentId: string;
  side: 'back' | 'lay';
  orderType: 'market' | 'limit' | 'stop';
  quantity: number;
  price: number;
  status: 'pending' | 'filled' | 'cancelled' | 'rejected';
  createdAt: Date;
  filledAt?: Date;
}
```

### Database Schema Design

The platform uses separate PostgreSQL databases for each service:

- `betbet_platform`: Core user and session data
- `betbet_gaming`: Games, sessions, tournaments
- `betbet_betting`: Sports, events, bets, markets
- `betbet_custom_betting`: Custom markets, peer-to-peer bets, escrow
- `betbet_trading`: Instruments, orders, positions
- `betbet_experts`: Expert profiles, predictions, subscriptions
- `betbet_sports`: Sports data, analytics, statistics
- `betbet_leaderboards`: Rankings, achievements, badges

## Error Handling

### Error Response Format

All services return consistent error responses:

```typescript
interface ErrorResponse {
  error: string;
  message: string;
  code: string;
  details?: any;
  timestamp: string;
  requestId: string;
}
```

### Error Categories

1. **Authentication Errors** (401, 403)
2. **Validation Errors** (400)
3. **Business Logic Errors** (422)
4. **Service Unavailable** (503)
5. **Internal Server Errors** (500)

### Error Handling Strategy

- Graceful degradation for non-critical features
- Circuit breaker pattern for external API calls
- Retry mechanisms with exponential backoff
- Comprehensive error logging and monitoring
- User-friendly error messages in frontend

## Testing Strategy

### Unit Testing

- **Coverage Target**: 90% code coverage
- **Framework**: Jest for JavaScript/TypeScript, pytest for Python
- **Focus Areas**: Business logic, data validation, calculations

### Integration Testing

- **API Testing**: Automated API endpoint testing
- **Database Testing**: Transaction and data integrity tests
- **Service Communication**: Inter-service communication tests

### End-to-End Testing

- **User Workflows**: Complete user journey testing
- **Cross-Service**: Multi-service interaction testing
- **Real-time Features**: WebSocket and live update testing

### Performance Testing

- **Load Testing**: Concurrent user simulation
- **Stress Testing**: System breaking point identification
- **Database Performance**: Query optimization and indexing

### Security Testing

- **Authentication**: JWT token validation and security
- **Authorization**: Role-based access control testing
- **Input Validation**: SQL injection and XSS prevention
- **Financial Security**: Transaction integrity and fraud prevention

### Testing Implementation

1. **Unit Tests**: Each service maintains comprehensive unit test suites
2. **Integration Tests**: API contract testing between services
3. **E2E Tests**: Playwright tests for complete user workflows
4. **Performance Tests**: Load testing with realistic user scenarios
5. **Security Tests**: Automated security scanning and penetration testing

The testing strategy ensures platform reliability, security, and performance under real-world conditions while maintaining code quality and preventing regressions.