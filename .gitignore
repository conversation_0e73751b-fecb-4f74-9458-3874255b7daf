# CRITICAL SECURITY FILES - NEVER COMMIT THESE
.env
.env.local
.env.production
.env.staging
*.pem
*.key
*.crt
*.p12
*.jks

# Docker secrets
docker-compose.override.yml
docker-compose.prod.yml
secrets/

# Database
*.db
*.sqlite
*.sqlite3
pgdata/
postgres_data/
redis_data/
pgadmin_data/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Build artifacts
dist/
build/
*.tgz
*.tar.gz

# Next.js
.next/
out/
.vercel/

# React Native
.expo/
.expo-shared/

# Testing
coverage/
.coverage
.pytest_cache/
.coverage.*

# Temporary files
*.tmp
*.temp
.cache/

# Backup files
*.bak
*.backup