# BetBet Mobile Application

**React Native Mobile App for BetBet Platform**

Cross-platform mobile application providing full access to the BetBet gaming, betting, trading, and analysis ecosystem with native performance and mobile-optimized user experience.

## 🎯 Features

### Core Functionality
- **Cross-Platform**: Single codebase for iOS and Android
- **Real-Time Updates**: WebSocket integration for live data
- **Offline Support**: Cached data and offline functionality
- **Biometric Authentication**: Touch ID / Face ID security
- **Push Notifications**: Live updates and engagement
- **Native Performance**: Optimized for mobile gaming and trading

### Platform Integration
- **Gaming Engine**: Mobile-optimized gaming interfaces
- **Custom Betting**: Touch-friendly bet creation and participation
- **Expert Analytics**: AI-powered insights and subscriptions
- **Sports Analysis**: Mobile chat and analysis tools
- **Trading Platform**: Professional trading on mobile
- **Leaderboards**: Social competition and achievements

## 🏗️ Architecture

```
BetBet Mobile/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── gaming/          # Gaming-specific components
│   │   ├── betting/         # Betting interfaces
│   │   ├── trading/         # Trading dashboard components
│   │   └── shared/          # Common UI components
│   ├── screens/             # Application screens
│   │   ├── auth/           # Authentication screens
│   │   ├── gaming/         # Gaming module screens
│   │   ├── betting/        # Betting module screens
│   │   ├── trading/        # Trading module screens
│   │   └── profile/        # User profile screens
│   ├── navigation/          # React Navigation setup
│   ├── services/           # API and external services
│   ├── store/              # Redux state management
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   └── types/              # TypeScript type definitions
├── android/                # Android-specific code
├── ios/                   # iOS-specific code
└── assets/                # Images, fonts, resources
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- React Native CLI
- Xcode (for iOS development)
- Android Studio (for Android development)
- CocoaPods (for iOS dependencies)

### 1. Installation

```bash
# Navigate to mobile directory
cd frontend/mobile

# Install dependencies
npm install

# iOS: Install pod dependencies
cd ios && pod install && cd ..
```

### 2. Configuration

Create `.env` file:
```bash
# API Configuration
API_GATEWAY_URL=http://localhost:8000
WEBSOCKET_URL=ws://localhost:8080

# Push Notification Keys
FCM_SENDER_ID=your_fcm_sender_id
APNS_KEY_ID=your_apns_key_id

# Biometric Configuration
BIOMETRIC_ENABLED=true
```

### 3. Development

```bash
# Start Metro bundler
npm start

# Run on iOS
npm run ios

# Run on Android
npm run android

# Run with specific device
npm run ios -- --simulator="iPhone 15 Pro"
npm run android -- --active-arch-only
```

### 4. Building

```bash
# Android Release Build
npm run android:build

# iOS Release Build
npm run ios:build

# Clean build artifacts
npm run clean
```

## 📱 Mobile-Specific Features

### 1. Biometric Authentication

```typescript
import {BiometricService} from '@/services/BiometricService';

// Check if biometrics are available
const available = await BiometricService.isAvailable();

// Authenticate user
const authenticated = await BiometricService.authenticate();

// Enable biometric login
await BiometricService.enableBiometricLogin();
```

### 2. Push Notifications

```typescript
import {NotificationService} from '@/services/NotificationService';

// Initialize notifications
NotificationService.initialize();

// Handle notification types
NotificationService.onNotification((notification) => {
  switch (notification.type) {
    case 'game_invitation':
      navigateToGame(notification.gameId);
      break;
    case 'bet_matched':
      navigateToBetting(notification.betId);
      break;
    case 'trade_executed':
      navigateToTrading(notification.orderId);
      break;
  }
});
```

### 3. Offline Support

```typescript
import {apiClient} from '@/services/ApiClient';

// API client automatically handles offline scenarios
try {
  const data = await apiClient.get('/api/v1/gaming/sessions');
  // Returns cached data if offline
} catch (error) {
  // Handle offline state
}
```

### 4. Real-Time WebSocket

```typescript
import {useWebSocket} from '@/hooks/useWebSocket';

const GameScreen = () => {
  const {connectionStatus, subscribe, sendMessage} = useWebSocket();
  
  useEffect(() => {
    // Subscribe to game events
    subscribe('gaming', ['game_started', 'player_joined']);
  }, []);
  
  return (
    <View>
      <ConnectionStatus connected={connectionStatus.connected} />
      {/* Game interface */}
    </View>
  );
};
```

## 🎮 Module Integration

### Gaming Interface

```typescript
// Gaming-optimized mobile interface
const MobileGameInterface = () => {
  return (
    <View style={styles.gameContainer}>
      <TouchableOpacity 
        style={styles.gameButton}
        onPress={() => onGameAction('play')}
      >
        <Text style={styles.buttonText}>Play Game</Text>
      </TouchableOpacity>
      
      <SpectatorCount count={gameSession.spectators} />
      <MobileBettingPanel gameId={gameSession.id} />
    </View>
  );
};
```

### Trading Dashboard

```typescript
// Mobile trading interface with touch controls
const TradingDashboard = () => {
  const {positions, balance} = useTradingData();
  
  return (
    <ScrollView style={styles.dashboard}>
      <AccountBalance balance={balance} />
      <PositionsList positions={positions} />
      <QuickOrderPanel />
      <MobileTradingChart />
    </ScrollView>
  );
};
```

### Betting Interface

```typescript
// Touch-optimized betting creation
const MobileBettingInterface = () => {
  return (
    <View style={styles.bettingContainer}>
      <SwipeableCardStack bets={availableBets} />
      <QuickBetPanel />
      <LiveOddsDisplay />
    </View>
  );
};
```

## 🔧 Configuration

### Navigation Setup

```typescript
// src/navigation/RootNavigator.tsx
const RootNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{headerShown: false}}>
        <Stack.Screen name="Auth" component={AuthNavigator} />
        <Stack.Screen name="Main" component={TabNavigator} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

### State Management

```typescript
// src/store/index.ts
export const store = configureStore({
  reducer: {
    auth: authReducer,
    gaming: gamingReducer,
    betting: bettingReducer,
    trading: tradingReducer,
    leaderboards: leaderboardsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(websocketMiddleware),
});
```

### Theme System

```typescript
// src/theme/index.ts
export const theme = {
  colors: {
    primary: '#1a1a2e',
    secondary: '#16213e',
    accent: '#e94560',
    background: '#0f0f23',
    surface: '#16213e',
    text: '#ffffff',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
  },
};
```

## 📊 Performance Optimization

### Lazy Loading

```typescript
// Lazy load heavy screens
const TradingScreen = lazy(() => import('@/screens/trading/TradingScreen'));
const GameScreen = lazy(() => import('@/screens/gaming/GameScreen'));

// Use with Suspense
<Suspense fallback={<LoadingScreen />}>
  <TradingScreen />
</Suspense>
```

### Image Optimization

```typescript
import FastImage from 'react-native-fast-image';

// Optimized image loading
<FastImage
  source={{uri: imageUrl, priority: FastImage.priority.normal}}
  style={styles.image}
  resizeMode={FastImage.resizeMode.cover}
/>
```

### Memory Management

```typescript
// Proper cleanup in useEffect
useEffect(() => {
  const subscription = eventEmitter.addListener('event', handler);
  
  return () => {
    subscription.remove();
  };
}, []);
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests (requires Detox setup)
npm run test:e2e
```

### Test Structure

```typescript
// __tests__/components/GameInterface.test.tsx
describe('GameInterface', () => {
  it('should render game controls', () => {
    render(<GameInterface />);
    expect(screen.getByText('Play Game')).toBeTruthy();
  });
  
  it('should handle game actions', async () => {
    const onGameAction = jest.fn();
    render(<GameInterface onGameAction={onGameAction} />);
    
    fireEvent.press(screen.getByText('Play Game'));
    expect(onGameAction).toHaveBeenCalledWith('play');
  });
});
```

## 🚀 Deployment

### Android Deployment

```bash
# Generate signed APK
cd android
./gradlew assembleRelease

# Generate AAB for Play Store
./gradlew bundleRelease
```

### iOS Deployment

```bash
# Archive for App Store
cd ios
xcodebuild -workspace BetBetMobile.xcworkspace \
  -scheme BetBetMobile \
  archive \
  -archivePath build/BetBetMobile.xcarchive
```

### Code Push (Optional)

```bash
# Install CodePush CLI
npm install -g code-push-cli

# Deploy update
code-push release-react BetBet-iOS ios
code-push release-react BetBet-Android android
```

## 📱 Platform-Specific Features

### iOS
- **App Clips**: Quick access to specific features
- **Widgets**: Home screen widgets for quick stats
- **Shortcuts**: Siri shortcuts for common actions
- **Apple Pay**: Integrated payment processing

### Android
- **App Bundles**: Dynamic feature delivery
- **Widgets**: Home screen widgets
- **Actions**: Google Assistant integration
- **Google Pay**: Integrated payment processing

## 🔒 Security

### Authentication Security
- Biometric authentication support
- Token refresh handling
- Secure storage using Keychain/Keystore
- Certificate pinning for API calls

### Data Security
- Encrypted data storage
- Secure communication (TLS 1.3)
- Screen recording prevention
- Jailbreak/root detection

## 🎯 Performance Targets

- **App Launch**: <3 seconds cold start
- **Screen Navigation**: <300ms transitions
- **API Responses**: <2 seconds average
- **Memory Usage**: <200MB average
- **Battery Impact**: Minimal background usage
- **Crash Rate**: <0.1% of sessions

## 📚 Development Guidelines

### Code Style
- TypeScript strict mode
- ESLint + Prettier configuration
- Component-driven architecture
- Functional programming patterns

### File Naming
- PascalCase for components
- camelCase for functions/variables
- kebab-case for assets
- UPPER_CASE for constants

### Component Structure
```typescript
// Standard component structure
interface Props {
  // Props definition
}

const Component: React.FC<Props> = ({prop1, prop2}) => {
  // Hooks
  // Event handlers
  // Render logic
  
  return (
    <View>
      {/* Component JSX */}
    </View>
  );
};

const styles = StyleSheet.create({
  // Component styles
});

export default Component;
```

---

**Status**: ✅ Mobile Application Foundation Complete  
**Next Steps**: Platform integration testing and optimization