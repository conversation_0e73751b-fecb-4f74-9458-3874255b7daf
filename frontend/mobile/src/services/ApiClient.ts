/**
 * BetBet Mobile API Client
 * ========================
 * 
 * Mobile-optimized API client that connects to the unified API Gateway
 * and provides offline capabilities, caching, and mobile-specific features.
 */

import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import Config from 'react-native-config';

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  role: string;
  permissions: string[];
}

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'betbet_access_token',
  REFRESH_TOKEN: 'betbet_refresh_token',
  USER_DATA: 'betbet_user_data',
  TOKEN_EXPIRES_AT: 'betbet_token_expires_at',
  OFFLINE_CACHE: 'betbet_offline_cache',
};

export class MobileApiClient {
  private axiosInstance: AxiosInstance;
  private baseURL: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private isRefreshing: boolean = false;
  private failedQueue: any[] = [];

  constructor() {
    this.baseURL = Config.API_GATEWAY_URL || 'http://localhost:8000';
    
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BetBet-Mobile/1.0.0',
      },
    });

    this.setupInterceptors();
    this.loadTokensFromStorage();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Check network connectivity
        const netState = await NetInfo.fetch();
        if (!netState.isConnected) {
          throw new Error('No internet connection');
        }

        // Add auth token
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }

        // Add request metadata
        config.metadata = { startTime: Date.now() };

        return config;
      },
      (error) => Promise.reject(error),
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // Cache successful responses for offline use
        this.cacheResponse(response);
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle token refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
              this.failedQueue.push({resolve, reject});
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.axiosInstance(originalRequest);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.refreshAccessToken();
            this.processQueue(null, newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            this.handleAuthenticationFailure();
            throw refreshError;
          } finally {
            this.isRefreshing = false;
          }
        }

        // Check for cached response if offline
        if (!error.response && error.message === 'No internet connection') {
          const cachedResponse = await this.getCachedResponse(originalRequest);
          if (cachedResponse) {
            return cachedResponse;
          }
        }

        return Promise.reject(error);
      },
    );
  }

  /**
   * Process failed request queue after token refresh
   */
  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(({resolve, reject}) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  /**
   * Cache response for offline use
   */
  private async cacheResponse(response: AxiosResponse): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(response.config);
      const cacheData = {
        data: response.data,
        timestamp: Date.now(),
        url: response.config.url,
        method: response.config.method,
      };

      const existingCache = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_CACHE);
      const cache = existingCache ? JSON.parse(existingCache) : {};
      cache[cacheKey] = cacheData;

      // Limit cache size (keep last 100 requests)
      const cacheKeys = Object.keys(cache);
      if (cacheKeys.length > 100) {
        const sortedKeys = cacheKeys.sort(
          (a, b) => cache[b].timestamp - cache[a].timestamp,
        );
        sortedKeys.slice(100).forEach(key => delete cache[key]);
      }

      await AsyncStorage.setItem(
        STORAGE_KEYS.OFFLINE_CACHE,
        JSON.stringify(cache),
      );
    } catch (error) {
      console.warn('Failed to cache response:', error);
    }
  }

  /**
   * Get cached response for offline use
   */
  private async getCachedResponse(
    config: AxiosRequestConfig,
  ): Promise<AxiosResponse | null> {
    try {
      const cacheKey = this.getCacheKey(config);
      const existingCache = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_CACHE);
      
      if (!existingCache) return null;
      
      const cache = JSON.parse(existingCache);
      const cachedData = cache[cacheKey];

      if (!cachedData) return null;

      // Check if cache is not too old (max 1 hour for offline use)
      const maxAge = 60 * 60 * 1000; // 1 hour
      if (Date.now() - cachedData.timestamp > maxAge) return null;

      return {
        data: cachedData.data,
        status: 200,
        statusText: 'OK (Cached)',
        headers: {'x-cache': 'HIT'},
        config,
      } as AxiosResponse;
    } catch (error) {
      console.warn('Failed to get cached response:', error);
      return null;
    }
  }

  /**
   * Generate cache key for request
   */
  private getCacheKey(config: AxiosRequestConfig): string {
    const method = config.method?.toUpperCase() || 'GET';
    const url = config.url || '';
    const params = JSON.stringify(config.params || {});
    return `${method}:${url}:${params}`;
  }

  /**
   * Load tokens from AsyncStorage
   */
  private async loadTokensFromStorage(): Promise<void> {
    try {
      const [accessToken, refreshToken] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
        AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
      ]);

      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
    } catch (error) {
      console.warn('Failed to load tokens from storage:', error);
    }
  }

  /**
   * Save tokens to AsyncStorage
   */
  private async saveTokensToStorage(tokens: AuthTokens): Promise<void> {
    try {
      const expiresAt = Date.now() + tokens.expires_in * 1000;

      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.access_token),
        AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh_token),
        AsyncStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_AT, expiresAt.toString()),
      ]);

      this.accessToken = tokens.access_token;
      this.refreshToken = tokens.refresh_token;
    } catch (error) {
      console.error('Failed to save tokens to storage:', error);
    }
  }

  /**
   * Clear tokens from storage
   */
  private async clearTokensFromStorage(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.TOKEN_EXPIRES_AT),
        AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
      ]);

      this.accessToken = null;
      this.refreshToken = null;
    } catch (error) {
      console.error('Failed to clear tokens from storage:', error);
    }
  }

  /**
   * Refresh access token
   */
  private async refreshAccessToken(): Promise<string> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {
        refresh_token: this.refreshToken,
      });

      const tokens: AuthTokens = response.data;
      await this.saveTokensToStorage(tokens);

      return tokens.access_token;
    } catch (error) {
      await this.clearTokensFromStorage();
      throw error;
    }
  }

  /**
   * Handle authentication failure
   */
  private handleAuthenticationFailure(): void {
    this.clearTokensFromStorage();
    // Emit event for app to handle (navigate to login)
    // This would be handled by the auth context in a real app
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  /**
   * Login user
   */
  public async login(credentials: LoginCredentials): Promise<{tokens: AuthTokens; user: User}> {
    try {
      const response = await this.axiosInstance.post('/auth/login', credentials);
      const {access_token, refresh_token, token_type, expires_in, user} = response.data;

      const tokens: AuthTokens = {access_token, refresh_token, token_type, expires_in};
      await this.saveTokensToStorage(tokens);

      // Save user data
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));

      return {tokens, user};
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Logout user
   */
  public async logout(): Promise<void> {
    try {
      if (this.accessToken) {
        await this.axiosInstance.post('/auth/logout');
      }
    } catch (error) {
      // Ignore logout errors
      console.warn('Logout API call failed:', error);
    } finally {
      await this.clearTokensFromStorage();
    }
  }

  /**
   * Get current user
   */
  public async getCurrentUser(): Promise<User> {
    const response = await this.axiosInstance.get('/auth/me');
    return response.data;
  }

  /**
   * Handle API errors
   */
  private handleApiError(error: any): Error {
    if (error.response) {
      const message = error.response.data?.message || error.response.data?.detail || 'An error occurred';
      return new Error(message);
    }

    if (error.request) {
      return new Error('Network error - please check your connection');
    }

    return new Error(error.message || 'Unknown error occurred');
  }

  // HTTP method helpers
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.get(url, config);
    return response.data;
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.post(url, data, config);
    return response.data;
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.put(url, data, config);
    return response.data;
  }

  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.patch(url, data, config);
    return response.data;
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.delete(url, config);
    return response.data;
  }
}

// Service-specific API clients
export class GamingAPI extends MobileApiClient {
  private readonly baseRoute = '/api/v1/gaming';

  async getGameSessions() {
    return this.get(`${this.baseRoute}/sessions`);
  }

  async joinGameSession(sessionId: string) {
    return this.post(`${this.baseRoute}/sessions/${sessionId}/join`);
  }
}

export class BettingAPI extends MobileApiClient {
  private readonly baseRoute = '/api/v1/betting';

  async getBets() {
    return this.get(`${this.baseRoute}/bets`);
  }

  async createBet(betData: any) {
    return this.post(`${this.baseRoute}/bets`, betData);
  }
}

export class TradingAPI extends MobileApiClient {
  private readonly baseRoute = '/api/v1/trading';

  async getAccountBalance() {
    return this.get(`${this.baseRoute}/account/balance`);
  }

  async getPositions() {
    return this.get(`${this.baseRoute}/account/positions`);
  }

  async createOrder(orderData: any) {
    return this.post(`${this.baseRoute}/orders`, orderData);
  }
}

export class LeaderboardsAPI extends MobileApiClient {
  private readonly baseRoute = '/api/v1/leaderboards';

  async getUnifiedLeaderboards() {
    return this.get(`${this.baseRoute}/unified`);
  }

  async getAchievements(userId?: string) {
    const url = userId 
      ? `${this.baseRoute}/achievements?user_id=${userId}`
      : `${this.baseRoute}/achievements`;
    return this.get(url);
  }
}

// Export singleton instances
export const apiClient = new MobileApiClient();
export const gamingApi = new GamingAPI();
export const bettingApi = new BettingAPI();
export const tradingApi = new TradingAPI();
export const leaderboardsApi = new LeaderboardsAPI();

export default apiClient;