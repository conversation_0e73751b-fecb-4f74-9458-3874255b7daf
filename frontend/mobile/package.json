{"name": "BetBetMobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android:build": "cd android && ./gradlew assembleRelease", "ios:build": "cd ios && xcodebuild -workspace BetBetMobile.xcworkspace -scheme BetBetMobile archive", "clean": "react-native clean", "reset-cache": "npx react-native start --reset-cache", "postinstall": "cd ios && pod install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/tab": "^6.0.0", "@reduxjs/toolkit": "^2.0.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-biometrics": "^3.0.1", "react-native-config": "^1.5.1", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.14.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.6.1", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-vector-icons": "^10.0.3", "react-native-webview": "^13.6.4", "react-native-websocket": "^1.0.2", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "axios": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.8.8", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=18"}, "packageManager": "npm@9.0.0"}