/**
 * BetBet Mobile Application
 * ========================
 * 
 * React Native application providing mobile access to the complete
 * BetBet platform including gaming, betting, trading, and analysis.
 */

import React, {useEffect} from 'react';
import {StatusBar, Alert, AppState} from 'react-native';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import PushNotification from 'react-native-push-notification';

import {store, persistor} from '@/store';
import RootNavigator from '@/navigation/RootNavigator';
import LoadingScreen from '@/components/shared/LoadingScreen';
import {AuthProvider} from '@/hooks/useAuth';
import {WebSocketProvider} from '@/hooks/useWebSocket';
import {NotificationService} from '@/services/NotificationService';
import {BiometricService} from '@/services/BiometricService';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize push notifications
    PushNotification.configure({
      onNotification: function(notification) {
        console.log('Notification received:', notification);
        
        // Handle notification tap
        if (notification.userInteraction) {
          // User tapped the notification
          handleNotificationTap(notification);
        }
      },
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: true,
      requestPermissions: true,
    });

    // Initialize biometric authentication
    BiometricService.initialize();

    // Handle app state changes for security
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        // App is going to background - trigger biometric lock if enabled
        BiometricService.lockApp();
      } else if (nextAppState === 'active') {
        // App is becoming active - check if biometric unlock is required
        BiometricService.checkUnlockRequired();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, []);

  const handleNotificationTap = (notification: any) => {
    // Handle different notification types
    switch (notification.data?.type) {
      case 'game_invitation':
        // Navigate to gaming section
        break;
      case 'bet_matched':
        // Navigate to betting section
        break;
      case 'trade_executed':
        // Navigate to trading section
        break;
      case 'achievement_unlocked':
        // Show achievement modal
        break;
      default:
        // Navigate to appropriate section based on notification
        break;
    }
  };

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <SafeAreaProvider>
        <Provider store={store}>
          <PersistGate loading={<LoadingScreen />} persistor={persistor}>
            <AuthProvider>
              <WebSocketProvider>
                <NavigationContainer>
                  <StatusBar
                    barStyle="light-content"
                    backgroundColor="#1a1a2e"
                    translucent={false}
                  />
                  <RootNavigator />
                </NavigationContainer>
              </WebSocketProvider>
            </AuthProvider>
          </PersistGate>
        </Provider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

export default App;