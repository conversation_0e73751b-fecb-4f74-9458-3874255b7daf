import { Notification } from '@/contexts/NotificationContext';

export interface ErrorDetails {
  code?: string;
  message: string;
  field?: string;
  context?: Record<string, any>;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: ErrorDetails[];
  timestamp?: number;
}

export class TradingError extends Error {
  public readonly code: string;
  public readonly details?: ErrorDetails[];
  public readonly retryable: boolean;

  constructor(
    message: string, 
    code: string = 'TRADING_ERROR', 
    details?: ErrorDetails[],
    retryable: boolean = false
  ) {
    super(message);
    this.name = 'TradingError';
    this.code = code;
    this.details = details;
    this.retryable = retryable;
  }
}

export class ValidationError extends Error {
  public readonly field: string;
  public readonly value?: any;

  constructor(message: string, field: string, value?: any) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.value = value;
  }
}

export class NetworkError extends Error {
  public readonly status?: number;
  public readonly retryable: boolean = true;

  constructor(message: string, status?: number) {
    super(message);
    this.name = 'NetworkError';
    this.status = status;
  }
}

export class WebSocketError extends Error {
  public readonly code?: number;
  public readonly reconnectable: boolean;

  constructor(message: string, code?: number, reconnectable: boolean = true) {
    super(message);
    this.name = 'WebSocketError';
    this.code = code;
    this.reconnectable = reconnectable;
  }
}

// Error classification and handling utilities
export class ErrorHandler {
  private static errorHistory: Array<{ error: Error; timestamp: number }> = [];
  private static readonly MAX_HISTORY = 50;

  static handleError(error: unknown, context?: string): ErrorDetails {
    const timestamp = Date.now();
    
    // Log error for debugging
    console.error(`[${context || 'Unknown'}]`, error);
    
    // Store in history
    if (error instanceof Error) {
      this.errorHistory.unshift({ error, timestamp });
      if (this.errorHistory.length > this.MAX_HISTORY) {
        this.errorHistory.pop();
      }
    }

    return this.classifyError(error, context);
  }

  private static classifyError(error: unknown, context?: string): ErrorDetails {
    if (error instanceof ValidationError) {
      return {
        code: 'VALIDATION_ERROR',
        message: error.message,
        field: error.field,
        context: { value: error.value },
      };
    }

    if (error instanceof TradingError) {
      return {
        code: error.code,
        message: error.message,
        context: { 
          retryable: error.retryable,
          details: error.details 
        },
      };
    }

    if (error instanceof NetworkError) {
      return {
        code: 'NETWORK_ERROR',
        message: this.getNetworkErrorMessage(error.status),
        context: { 
          status: error.status,
          retryable: error.retryable 
        },
      };
    }

    if (error instanceof WebSocketError) {
      return {
        code: 'WEBSOCKET_ERROR',
        message: error.message,
        context: { 
          code: error.code,
          reconnectable: error.reconnectable 
        },
      };
    }

    // Handle API errors
    if (this.isApiError(error)) {
      return {
        code: error.code || 'API_ERROR',
        message: error.message,
        context: { 
          status: error.status,
          details: error.details 
        },
      };
    }

    // Generic error
    return {
      code: 'UNKNOWN_ERROR',
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      context: { originalError: String(error), context },
    };
  }

  private static isApiError(error: any): error is ApiError {
    return error && typeof error === 'object' && 'status' in error;
  }

  private static getNetworkErrorMessage(status?: number): string {
    switch (status) {
      case 400:
        return 'Invalid request. Please check your input.';
      case 401:
        return 'Authentication required. Please log in.';
      case 403:
        return 'Access denied. Insufficient permissions.';
      case 404:
        return 'Resource not found.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again.';
      case 502:
        return 'Service temporarily unavailable.';
      case 503:
        return 'Service maintenance in progress.';
      default:
        return 'Network error. Please check your connection.';
    }
  }

  static getErrorHistory(): Array<{ error: Error; timestamp: number }> {
    return [...this.errorHistory];
  }

  static clearErrorHistory(): void {
    this.errorHistory = [];
  }

  // Convert error to notification
  static toNotification(error: ErrorDetails): Omit<Notification, 'id' | 'timestamp'> {
    const isRetryable = error.context?.retryable || error.context?.reconnectable;
    
    let type: Notification['type'] = 'error';
    let duration = 8000;

    // Adjust notification type and duration based on error severity
    switch (error.code) {
      case 'VALIDATION_ERROR':
        type = 'warning';
        duration = 5000;
        break;
      case 'NETWORK_ERROR':
        if (error.context?.status === 429) {
          type = 'warning';
          duration = 6000;
        }
        break;
      case 'WEBSOCKET_ERROR':
        if (error.context?.reconnectable) {
          type = 'warning';
          duration = 4000;
        }
        break;
      case 'INSUFFICIENT_BALANCE':
        type = 'warning';
        duration = 6000;
        break;
    }

    return {
      type,
      title: this.getErrorTitle(error.code),
      message: error.message,
      duration,
      persistent: !isRetryable && type === 'error',
    };
  }

  private static getErrorTitle(code?: string): string {
    switch (code) {
      case 'VALIDATION_ERROR':
        return 'Invalid Input';
      case 'NETWORK_ERROR':
        return 'Connection Error';
      case 'WEBSOCKET_ERROR':
        return 'Real-time Connection Issue';
      case 'TRADING_ERROR':
        return 'Trading Error';
      case 'API_ERROR':
        return 'Service Error';
      case 'INSUFFICIENT_BALANCE':
        return 'Insufficient Balance';
      case 'ORDER_REJECTED':
        return 'Order Rejected';
      case 'MARKET_CLOSED':
        return 'Market Closed';
      case 'RATE_LIMITED':
        return 'Rate Limited';
      default:
        return 'Error';
    }
  }
}

// Utility functions for common error scenarios
export function handleOrderError(error: unknown, orderData?: any): ErrorDetails {
  const details = ErrorHandler.handleError(error, 'ORDER_SUBMISSION');
  
  // Add order-specific context
  if (orderData) {
    details.context = {
      ...details.context,
      orderData: {
        side: orderData.side,
        type: orderData.order_type,
        quantity: orderData.quantity,
        market: orderData.market_id,
      },
    };
  }
  
  return details;
}

export function handleWebSocketError(error: unknown, event?: string): ErrorDetails {
  const details = ErrorHandler.handleError(error, 'WEBSOCKET');
  
  if (event) {
    details.context = {
      ...details.context,
      event,
    };
  }
  
  return details;
}

export function handleApiError(error: unknown, endpoint?: string): ErrorDetails {
  const details = ErrorHandler.handleError(error, 'API');
  
  if (endpoint) {
    details.context = {
      ...details.context,
      endpoint,
    };
  }
  
  return details;
}

export function createValidationError(field: string, value: any, rule: string): ValidationError {
  const message = `${field} ${rule}`;
  return new ValidationError(message, field, value);
}

export function createTradingError(
  message: string, 
  code: string, 
  retryable: boolean = false
): TradingError {
  return new TradingError(message, code, undefined, retryable);
}

// Retry utility with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Check if error is retryable
      if (error instanceof TradingError && !error.retryable) {
        break;
      }
      
      if (error instanceof NetworkError && !error.retryable) {
        break;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1);
      const jitter = Math.random() * 0.1 * delay;
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
    }
  }
  
  throw lastError!;
}