/**
 * Event Service Hook
 * ==================
 * 
 * React hook for managing platform events, notifications, and real-time updates.
 * Provides WebSocket connection for live notifications and API methods for event management.
 */

'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';
import axios from 'axios';

// Event Types
export type EventType = 
  | 'user_registration'
  | 'user_login'
  | 'bet_placed'
  | 'bet_won'
  | 'bet_lost'
  | 'game_started'
  | 'game_completed'
  | 'achievement_unlocked'
  | 'tier_upgraded'
  | 'deposit_completed'
  | 'withdrawal_completed'
  | 'market_created'
  | 'market_resolved'
  | 'expert_pick_published'
  | 'leaderboard_position_changed'
  | 'system_maintenance'
  | 'system_alert';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface PlatformEvent {
  id: string;
  event_type: EventType;
  user_id?: string;
  title: string;
  description: string;
  data: Record<string, any>;
  priority: NotificationPriority;
  channels: string[];
  is_read: boolean;
  created_at: string;
  expires_at?: string;
}

export interface EventNotification {
  type: 'notification' | 'broadcast' | 'connected' | 'heartbeat';
  event_type?: EventType;
  title?: string;
  description?: string;
  priority?: NotificationPriority;
  data?: Record<string, any>;
  timestamp: string;
  user_id?: string;
  message?: string;
}

interface UseEventServiceReturn {
  // Connection status
  isConnected: boolean;
  connectionError: string | null;
  
  // Events and notifications
  events: PlatformEvent[];
  unreadCount: number;
  lastNotification: EventNotification | null;
  
  // Loading states
  loading: boolean;
  error: string | null;
  
  // Methods
  fetchEvents: (limit?: number, offset?: number, unreadOnly?: boolean) => Promise<void>;
  markEventRead: (eventId: string) => Promise<void>;
  markAllRead: () => Promise<void>;
  createEvent: (eventData: Partial<PlatformEvent>) => Promise<void>;
  connect: () => void;
  disconnect: () => void;
  
  // Real-time notifications
  onNotification: (callback: (notification: EventNotification) => void) => void;
}

const EVENT_API_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';

export function useEventService(): UseEventServiceReturn {
  const { user } = useUser();
  
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [events, setEvents] = useState<PlatformEvent[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastNotification, setLastNotification] = useState<EventNotification | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Refs
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const notificationCallbacksRef = useRef<((notification: EventNotification) => void)[]>([]);
  
  // Constants
  const MAX_RECONNECT_ATTEMPTS = 5;
  const RECONNECT_INTERVAL = 3000;
  
  // API client
  const apiClient = axios.create({
    baseURL: `${EVENT_API_URL}/api/events`,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  // WebSocket connection
  const connect = useCallback(() => {
    if (!user?.id || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }
    
    try {
      const wsUrl = `${WEBSOCKET_URL}/ws/events/${user.id}`;
      console.log('Connecting to Event WebSocket:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;
      
      ws.onopen = () => {
        console.log('Event WebSocket connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttemptsRef.current = 0;
      };
      
      ws.onmessage = (event) => {
        try {
          const notification: EventNotification = JSON.parse(event.data);
          setLastNotification(notification);
          
          // Call registered callbacks
          notificationCallbacksRef.current.forEach(callback => {
            try {
              callback(notification);
            } catch (error) {
              console.error('Error in notification callback:', error);
            }
          });
          
          // Update unread count for new notifications
          if (notification.type === 'notification') {
            setUnreadCount(prev => prev + 1);
          }
          
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('Event WebSocket disconnected');
        setIsConnected(false);
        
        // Attempt reconnection
        if (reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS) {
          reconnectAttemptsRef.current += 1;
          console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS})...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, RECONNECT_INTERVAL * reconnectAttemptsRef.current);
        } else {
          setConnectionError('Max reconnection attempts reached');
        }
      };
      
      ws.onerror = (error) => {
        console.error('Event WebSocket error:', error);
        setConnectionError('WebSocket connection failed');
      };
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionError('Failed to create WebSocket connection');
    }
  }, [user?.id]);
  
  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    reconnectAttemptsRef.current = 0;
  }, []);
  
  // Fetch events from API
  const fetchEvents = useCallback(async (limit = 20, offset = 0, unreadOnly = false) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get(`/user/${user.id}`, {
        params: { limit, offset, unread_only: unreadOnly }
      });
      
      setEvents(response.data.events);
      setUnreadCount(response.data.unread_count);
      
    } catch (error) {
      console.error('Failed to fetch events:', error);
      setError('Failed to load events');
    } finally {
      setLoading(false);
    }
  }, [user?.id, apiClient]);
  
  // Mark event as read
  const markEventRead = useCallback(async (eventId: string) => {
    if (!user?.id) return;
    
    try {
      await apiClient.put(`/${eventId}/read`, { user_id: user.id });
      
      // Update local state
      setEvents(prev => prev.map(event => 
        event.id === eventId ? { ...event, is_read: true } : event
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
      
    } catch (error) {
      console.error('Failed to mark event as read:', error);
    }
  }, [user?.id, apiClient]);
  
  // Mark all events as read
  const markAllRead = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      // Mark all unread events as read
      const unreadEvents = events.filter(event => !event.is_read);
      await Promise.all(unreadEvents.map(event => markEventRead(event.id)));
      
    } catch (error) {
      console.error('Failed to mark all events as read:', error);
    }
  }, [user?.id, events, markEventRead]);
  
  // Create new event
  const createEvent = useCallback(async (eventData: Partial<PlatformEvent>) => {
    try {
      await apiClient.post('/', {
        ...eventData,
        user_id: eventData.user_id || user?.id
      });
      
      // Refresh events
      await fetchEvents();
      
    } catch (error) {
      console.error('Failed to create event:', error);
      throw error;
    }
  }, [user?.id, apiClient, fetchEvents]);
  
  // Register notification callback
  const onNotification = useCallback((callback: (notification: EventNotification) => void) => {
    notificationCallbacksRef.current.push(callback);
    
    // Return cleanup function
    return () => {
      const index = notificationCallbacksRef.current.indexOf(callback);
      if (index > -1) {
        notificationCallbacksRef.current.splice(index, 1);
      }
    };
  }, []);
  
  // Auto-connect when user is available
  useEffect(() => {
    if (user?.id) {
      connect();
      fetchEvents();
    }
    
    return () => {
      disconnect();
    };
  }, [user?.id, connect, disconnect, fetchEvents]);
  
  return {
    isConnected,
    connectionError,
    events,
    unreadCount,
    lastNotification,
    loading,
    error,
    fetchEvents,
    markEventRead,
    markAllRead,
    createEvent,
    connect,
    disconnect,
    onNotification,
  };
}
