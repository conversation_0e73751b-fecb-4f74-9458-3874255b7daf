/**
 * BetBet Unified WebSocket Hook
 * =============================
 * 
 * React hook for managing WebSocket connections to the unified WebSocket Manager.
 * Handles connection management, message routing, and event subscriptions.
 */

import { useEffect, useState, useRef, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';

export interface WebSocketMessage {
  type: string;
  module: string;
  data: any;
  timestamp: string;
  user_id?: string;
  session_id?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

export interface WebSocketHookReturn {
  connectionStatus: ConnectionStatus;
  messages: WebSocketMessage[];
  sendMessage: (message: any) => void;
  subscribeToModule: (module: string) => void;
  subscribeToEvents: (eventTypes: string[]) => void;
  clearMessages: () => void;
  getMessagesByType: (type: string) => WebSocketMessage[];
  getMessagesByModule: (module: string) => WebSocketMessage[];
}

const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000; // 3 seconds

/**
 * Unified WebSocket Hook
 */
export const useUnifiedWebSocket = (userId?: string): WebSocketHookReturn => {
  const { user, isSignedIn } = useUser();
  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef<boolean>(true);
  const reconnectAttemptsRef = useRef<number>(0);
  const subscriptionsRef = useRef<{
    modules: Set<string>;
    events: Set<string>;
  }>({
    modules: new Set(),
    events: new Set(),
  });

  const effectiveUserId = userId || user?.id;

  /**
   * Clear reconnect timeout
   */
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  /**
   * Update connection status
   */
  const updateConnectionStatus = useCallback((updates: Partial<ConnectionStatus>) => {
    setConnectionStatus(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Add message to state
   */
  const addMessage = useCallback((message: WebSocketMessage) => {
    setMessages(prev => {
      // Limit message history to prevent memory issues
      const newMessages = [...prev, message];
      return newMessages.slice(-1000); // Keep last 1000 messages
    });
  }, []);

  /**
   * Send message through WebSocket
   */
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected. Message not sent:', message);
    }
  }, []);

  /**
   * Subscribe to module events
   */
  const subscribeToModule = useCallback((module: string) => {
    subscriptionsRef.current.modules.add(module);
    
    sendMessage({
      type: 'subscribe_module',
      module: module,
    });
  }, [sendMessage]);

  /**
   * Subscribe to specific event types
   */
  const subscribeToEvents = useCallback((eventTypes: string[]) => {
    eventTypes.forEach(type => subscriptionsRef.current.events.add(type));
    
    sendMessage({
      type: 'subscribe_events',
      event_types: eventTypes,
    });
  }, [sendMessage]);

  /**
   * Re-establish subscriptions after reconnection
   */
  const reestablishSubscriptions = useCallback(() => {
    // Re-subscribe to modules
    subscriptionsRef.current.modules.forEach(module => {
      sendMessage({
        type: 'subscribe_module',
        module: module,
      });
    });

    // Re-subscribe to events
    if (subscriptionsRef.current.events.size > 0) {
      sendMessage({
        type: 'subscribe_events',
        event_types: Array.from(subscriptionsRef.current.events),
      });
    }
  }, [sendMessage]);

  /**
   * Connect to WebSocket
   */
  const connect = useCallback(() => {
    if (!effectiveUserId || !isSignedIn) {
      console.log('Skipping WebSocket connection - user not authenticated');
      return;
    }

    if (wsRef.current && wsRef.current.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket connection already in progress');
      return;
    }

    clearReconnectTimeout();
    updateConnectionStatus({ connecting: true, error: null });

    try {
      const wsUrl = `${WEBSOCKET_URL}/ws/${effectiveUserId}`;
      console.log('Connecting to WebSocket:', wsUrl);
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        reconnectAttemptsRef.current = 0;
        updateConnectionStatus({
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempts: 0,
        });

        // Re-establish subscriptions after connection
        setTimeout(() => {
          reestablishSubscriptions();
        }, 100);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          addMessage(message);
          
          // Emit custom event for other components to listen
          window.dispatchEvent(new CustomEvent('betbet:websocket:message', {
            detail: message
          }));
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        updateConnectionStatus({ connected: false, connecting: false });

        // Attempt to reconnect if not intentionally closed
        if (shouldReconnectRef.current && reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS) {
          reconnectAttemptsRef.current += 1;
          const attempts = reconnectAttemptsRef.current;
          updateConnectionStatus({ reconnectAttempts: attempts });

          console.log(`Attempting to reconnect (${attempts}/${MAX_RECONNECT_ATTEMPTS})...`);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, RECONNECT_INTERVAL * attempts); // Exponential backoff
        } else if (reconnectAttemptsRef.current >= MAX_RECONNECT_ATTEMPTS) {
          reconnectAttemptsRef.current = 0;
          updateConnectionStatus({
            error: 'Max reconnection attempts reached',
            reconnectAttempts: 0
          });
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateConnectionStatus({ 
          error: 'WebSocket connection error',
          connecting: false 
        });
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      updateConnectionStatus({
        connecting: false,
        error: 'Failed to create WebSocket connection',
      });
    }
  }, [effectiveUserId, isSignedIn, clearReconnectTimeout, updateConnectionStatus, addMessage, reestablishSubscriptions]);

  /**
   * Disconnect WebSocket
   */
  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;
    clearReconnectTimeout();
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Component unmounting');
      wsRef.current = null;
    }
    
    reconnectAttemptsRef.current = 0;
    updateConnectionStatus({
      connected: false,
      connecting: false,
      reconnectAttempts: 0,
    });
  }, [clearReconnectTimeout, updateConnectionStatus]);

  /**
   * Clear messages
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  /**
   * Get messages by type
   */
  const getMessagesByType = useCallback((type: string): WebSocketMessage[] => {
    return messages.filter(message => message.type === type);
  }, [messages]);

  /**
   * Get messages by module
   */
  const getMessagesByModule = useCallback((module: string): WebSocketMessage[] => {
    return messages.filter(message => message.module === module);
  }, [messages]);

  /**
   * Effect to manage WebSocket connection
   */
  useEffect(() => {
    shouldReconnectRef.current = true;
    
    if (isSignedIn && effectiveUserId) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [isSignedIn, effectiveUserId, connect, disconnect]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      shouldReconnectRef.current = false;
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [clearReconnectTimeout]);

  return {
    connectionStatus,
    messages,
    sendMessage,
    subscribeToModule,
    subscribeToEvents,
    clearMessages,
    getMessagesByType,
    getMessagesByModule,
  };
};

/**
 * Hook for specific module WebSocket events
 */
export const useModuleWebSocket = (module: string, userId?: string) => {
  const webSocket = useUnifiedWebSocket(userId);

  useEffect(() => {
    if (webSocket.connectionStatus.connected) {
      webSocket.subscribeToModule(module);
    }
  }, [webSocket.connectionStatus.connected, module, webSocket]);

  const moduleMessages = webSocket.getMessagesByModule(module);

  return {
    ...webSocket,
    moduleMessages,
  };
};

/**
 * Hook for specific event type subscriptions
 */
export const useEventWebSocket = (eventTypes: string[], userId?: string) => {
  const webSocket = useUnifiedWebSocket(userId);

  useEffect(() => {
    if (webSocket.connectionStatus.connected) {
      webSocket.subscribeToEvents(eventTypes);
    }
  }, [webSocket.connectionStatus.connected, eventTypes, webSocket]);

  const eventMessages = eventTypes.reduce((acc, type) => {
    acc[type] = webSocket.getMessagesByType(type);
    return acc;
  }, {} as Record<string, WebSocketMessage[]>);

  return {
    ...webSocket,
    eventMessages,
  };
};

export default useUnifiedWebSocket;