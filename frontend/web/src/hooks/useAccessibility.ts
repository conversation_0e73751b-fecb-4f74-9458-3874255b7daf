'use client';

import { useEffect, useRef, useCallback, useState } from 'react';

// Screen reader announcements
export function useScreenReader() {
  const announcementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create announcement element if it doesn't exist
    if (!announcementRef.current) {
      const element = document.createElement('div');
      element.setAttribute('aria-live', 'polite');
      element.setAttribute('aria-atomic', 'true');
      element.className = 'sr-only';
      element.style.position = 'absolute';
      element.style.left = '-10000px';
      element.style.width = '1px';
      element.style.height = '1px';
      element.style.overflow = 'hidden';
      
      document.body.appendChild(element);
      announcementRef.current = element;
    }

    return () => {
      if (announcementRef.current && document.body.contains(announcementRef.current)) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcementRef.current) {
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.textContent = message;
    }
  }, []);

  const announcePrice = useCallback((price: number, symbol: string, change?: number) => {
    let message = `${symbol} price is ${price.toFixed(2)} dollars`;
    if (change !== undefined) {
      const direction = change >= 0 ? 'up' : 'down';
      message += `, ${direction} ${Math.abs(change).toFixed(2)}`;
    }
    announce(message);
  }, [announce]);

  const announceOrder = useCallback((side: string, quantity: number, symbol: string, status: string) => {
    const message = `${side} order for ${quantity} ${symbol} ${status}`;
    announce(message, 'assertive');
  }, [announce]);

  const announcePosition = useCallback((side: string, quantity: number, symbol: string, pnl: number) => {
    const pnlDirection = pnl >= 0 ? 'profit' : 'loss';
    const message = `${side} position for ${quantity} ${symbol} closed with ${pnlDirection} of ${Math.abs(pnl).toFixed(2)} dollars`;
    announce(message, 'assertive');
  }, [announce]);

  return {
    announce,
    announcePrice,
    announceOrder,
    announcePosition,
  };
}

// Focus management
export function useFocusManagement() {
  const trapRef = useRef<HTMLElement | null>(null);
  const previousFocus = useRef<HTMLElement | null>(null);

  const trapFocus = useCallback((element: HTMLElement) => {
    if (trapRef.current) {
      releaseFocus();
    }

    trapRef.current = element;
    previousFocus.current = document.activeElement as HTMLElement;

    const focusableElements = element.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    element.addEventListener('keydown', handleTabKey);
    firstElement.focus();

    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  const releaseFocus = useCallback(() => {
    if (previousFocus.current) {
      previousFocus.current.focus();
      previousFocus.current = null;
    }
    trapRef.current = null;
  }, []);

  return { trapFocus, releaseFocus };
}

// Keyboard navigation
export function useKeyboardNavigation(items: any[], onSelect: (item: any, index: number) => void) {
  const currentIndexRef = useRef(-1);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        currentIndexRef.current = Math.min(currentIndexRef.current + 1, items.length - 1);
        break;
      case 'ArrowUp':
        e.preventDefault();
        currentIndexRef.current = Math.max(currentIndexRef.current - 1, 0);
        break;
      case 'Home':
        e.preventDefault();
        currentIndexRef.current = 0;
        break;
      case 'End':
        e.preventDefault();
        currentIndexRef.current = items.length - 1;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (currentIndexRef.current >= 0 && currentIndexRef.current < items.length) {
          onSelect(items[currentIndexRef.current], currentIndexRef.current);
        }
        break;
      case 'Escape':
        currentIndexRef.current = -1;
        break;
    }
  }, [items, onSelect]);

  const setCurrentIndex = useCallback((index: number) => {
    currentIndexRef.current = index;
  }, []);

  return {
    currentIndex: currentIndexRef.current,
    setCurrentIndex,
    handleKeyDown,
  };
}

// Color contrast and theme accessibility
export function useAccessibleColors() {
  const getContrastRatio = useCallback((color1: string, color2: string): number => {
    // Simplified contrast ratio calculation
    // In a real implementation, you'd use a proper color library
    const getLuminance = (color: string): number => {
      // This is a simplified version - use a proper color library in production
      const rgb = color.match(/\d+/g);
      if (!rgb) return 0;
      
      const [r, g, b] = rgb.map(c => {
        const channel = parseInt(c) / 255;
        return channel <= 0.03928 
          ? channel / 12.92 
          : Math.pow((channel + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }, []);

  const checkAACompliance = useCallback((foreground: string, background: string): boolean => {
    return getContrastRatio(foreground, background) >= 4.5;
  }, [getContrastRatio]);

  const checkAAACompliance = useCallback((foreground: string, background: string): boolean => {
    return getContrastRatio(foreground, background) >= 7;
  }, [getContrastRatio]);

  return {
    getContrastRatio,
    checkAACompliance,
    checkAAACompliance,
  };
}

// High contrast mode detection
export function useHighContrastMode() {
  const [isHighContrast, setIsHighContrast] = useState(false);

  useEffect(() => {
    const checkHighContrast = () => {
      // Check for Windows high contrast mode
      const isWindows = /Windows/.test(navigator.userAgent);
      if (isWindows) {
        setIsHighContrast(window.matchMedia('(prefers-contrast: high)').matches);
      }
    };

    checkHighContrast();

    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    mediaQuery.addEventListener('change', checkHighContrast);

    return () => mediaQuery.removeEventListener('change', checkHighContrast);
  }, []);

  return isHighContrast;
}

// Reduced motion detection
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = () => setPrefersReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

// ARIA live region management
export function useAriaLiveRegion() {
  const liveRegionRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create live region if it doesn't exist
    if (!liveRegionRef.current) {
      const element = document.createElement('div');
      element.setAttribute('aria-live', 'polite');
      element.setAttribute('aria-atomic', 'false');
      element.className = 'sr-only';
      element.style.position = 'absolute';
      element.style.left = '-10000px';
      
      document.body.appendChild(element);
      liveRegionRef.current = element;
    }

    return () => {
      if (liveRegionRef.current && document.body.contains(liveRegionRef.current)) {
        document.body.removeChild(liveRegionRef.current);
      }
    };
  }, []);

  const updateLiveRegion = useCallback((content: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (liveRegionRef.current) {
      liveRegionRef.current.setAttribute('aria-live', priority);
      liveRegionRef.current.textContent = content;
    }
  }, []);

  return updateLiveRegion;
}