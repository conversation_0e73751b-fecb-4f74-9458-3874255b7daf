/**
 * useLeaderboardWebSocket - Real-time Updates Hook for Leaderboards
 * ===============================================================
 * 
 * Manages WebSocket connections for live leaderboard updates with <5ms processing
 * requirement. Handles ranking changes, achievement unlocks, and social events.
 */

'use client';

import { useEffect, useRef, useCallback, useState } from 'react';

// WebSocket Configuration
const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws';
console.log('WebSocket URL:', WEBSOCKET_URL);
const RECONNECT_INTERVAL = 3000; // 3 seconds
const MAX_RECONNECT_ATTEMPTS = 5;

// Type definitions for WebSocket messages
interface WebSocketMessage {
  type: 'ranking_update' | 'achievement_unlock' | 'challenge_update' | 'affiliation_update' | 'user_activity' | 'welcome' | 'echo';
  timestamp: string;
  data?: any;
  message?: string;
  original_message?: string;
}

interface RankingUpdateData {
  user_id: string;
  username: string;
  old_rank: number;
  new_rank: number;
  score_change: number;
  module: 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';
  tier_change?: {
    old_tier: string;
    new_tier: string;
  };
}

interface AchievementUnlockData {
  user_id: string;
  username: string;
  achievement: {
    id: string;
    name: string;
    description: string;
    rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
    points: number;
    icon_url?: string;
  };
  celebration_eligible: boolean;
}

interface ChallengeUpdateData {
  challenge_id: string;
  type: 'started' | 'progress_update' | 'completed';
  participants: string[];
  progress?: {
    challenger_progress: number;
    challenged_progress?: number;
  };
  winner?: string;
}

interface AffiliationUpdateData {
  affiliation_id: string;
  affiliation_name: string;
  old_rank: number;
  new_rank: number;
  dominance_score_change: number;
}

interface UserActivityData {
  user_id: string;
  username: string;
  activity_type: 'login' | 'major_win' | 'streak_achievement' | 'tier_promotion';
  module: string;
  details: any;
}

// Hook for WebSocket connection and message handling
export const useLeaderboardWebSocket = (userId?: string, enabled: boolean = true) => {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttempts = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [messageCount, setMessageCount] = useState(0);

  // Message handlers
  const [rankingUpdateHandler, setRankingUpdateHandler] = useState<((data: RankingUpdateData) => void) | null>(null);
  const [achievementHandler, setAchievementHandler] = useState<((data: AchievementUnlockData) => void) | null>(null);
  const [challengeHandler, setChallengeHandler] = useState<((data: ChallengeUpdateData) => void) | null>(null);
  const [affiliationHandler, setAffiliationHandler] = useState<((data: AffiliationUpdateData) => void) | null>(null);
  const [activityHandler, setActivityHandler] = useState<((data: UserActivityData) => void) | null>(null);

  const connect = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) return;

    setConnectionStatus('connecting');
    
    const wsUrl = userId ? `${WEBSOCKET_URL}?user_id=${userId}` : WEBSOCKET_URL;
    console.log('Attempting WebSocket connection to:', wsUrl);
    
    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;
      
      ws.onopen = () => {
        console.log('WebSocket connected successfully');
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        // Send test message for test endpoint
        ws.send('WebSocket connection from BetBet frontend');
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);
          setMessageCount(prev => prev + 1);

          // Route message to appropriate handler
          switch (message.type) {
            case 'welcome':
              console.log('WebSocket welcome:', message.message);
              break;
            case 'echo':
              console.log('WebSocket echo:', message.original_message);
              break;
            case 'ranking_update':
              rankingUpdateHandler?.(message.data as RankingUpdateData);
              break;
            case 'achievement_unlock':
              achievementHandler?.(message.data as AchievementUnlockData);
              break;
            case 'challenge_update':
              challengeHandler?.(message.data as ChallengeUpdateData);
              break;
            case 'affiliation_update':
              affiliationHandler?.(message.data as AffiliationUpdateData);
              break;
            case 'user_activity':
              activityHandler?.(message.data as UserActivityData);
              break;
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

      ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect if not a clean close
        if (!event.wasClean && reconnectAttempts.current < MAX_RECONNECT_ATTEMPTS) {
          console.log(`Attempting reconnect ${reconnectAttempts.current + 1}/${MAX_RECONNECT_ATTEMPTS}`);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, RECONNECT_INTERVAL);
        }
      };
      
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setConnectionStatus('error');
    }
  }, [enabled, userId, rankingUpdateHandler, achievementHandler, challengeHandler, affiliationHandler, activityHandler]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setConnectionStatus('disconnected');
  }, []);

  const sendMessage = useCallback((message: object) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    connectionStatus,
    lastMessage,
    messageCount,
    connect,
    disconnect,
    sendMessage,
    // Handler setters
    onRankingUpdate: setRankingUpdateHandler,
    onAchievementUnlock: setAchievementHandler,
    onChallengeUpdate: setChallengeHandler,
    onAffiliationUpdate: setAffiliationHandler,
    onUserActivity: setActivityHandler,
  };
};

// Hook for live leaderboard data with WebSocket updates
export const useLiveLeaderboardData = (
  view: 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst' = 'unified',
  userId?: string
) => {
  const [liveData, setLiveData] = useState<any[]>([]);
  const [pendingUpdates, setPendingUpdates] = useState<RankingUpdateData[]>([]);
  
  const { connectionStatus, onRankingUpdate } = useLeaderboardWebSocket(userId);

  // Handle ranking updates
  useEffect(() => {
    const handleRankingUpdate = (updateData: RankingUpdateData) => {
      if (!updateData) return;
      
      if (updateData.module === view || view === 'unified') {
        setPendingUpdates(prev => [...prev, updateData]);
        
        // Process update after a brief delay to allow for batching
        setTimeout(() => {
          setPendingUpdates(prev => {
            const remaining = prev.filter(update => update !== updateData);
            
            // Apply the update to live data
            setLiveData(currentData => 
              currentData.map(entry => {
                if (entry.user_id === updateData.user_id) {
                  return {
                    ...entry,
                    rank: updateData.new_rank,
                    [`${updateData.module}_score`]: entry[`${updateData.module}_score`] + updateData.score_change,
                    rank_change: updateData.new_rank - updateData.old_rank,
                    platform_tier: updateData.tier_change?.new_tier || entry.platform_tier
                  };
                }
                return entry;
              }).sort((a, b) => a.rank - b.rank)
            );
            
            return remaining;
          });
        }, 100);
      }
    };

    onRankingUpdate(handleRankingUpdate);
    
    // Cleanup function
    return () => {
      onRankingUpdate(null);
    };
  }, [view, onRankingUpdate]);

  return {
    liveData,
    pendingUpdates,
    connectionStatus,
    setLiveData // Allow external data setting from API calls
  };
};

// Hook for achievement celebrations
export const useAchievementCelebration = (userId?: string) => {
  const [celebrationQueue, setCelebrationQueue] = useState<AchievementUnlockData[]>([]);
  const [currentCelebration, setCurrentCelebration] = useState<AchievementUnlockData | null>(null);
  
  const { onAchievementUnlock } = useLeaderboardWebSocket(userId);

  // Handle new achievement unlocks
  useEffect(() => {
    onAchievementUnlock((achievementData: AchievementUnlockData) => {
      if (!achievementData) return;
      
      if (achievementData.celebration_eligible) {
        setCelebrationQueue(prev => [...prev, achievementData]);
      }
    });
  }, [onAchievementUnlock]);

  // Process celebration queue
  useEffect(() => {
    if (!currentCelebration && celebrationQueue.length > 0) {
      const [nextCelebration, ...remaining] = celebrationQueue;
      setCurrentCelebration(nextCelebration);
      setCelebrationQueue(remaining);
    }
  }, [celebrationQueue, currentCelebration]);

  const dismissCelebration = useCallback(() => {
    setCurrentCelebration(null);
  }, []);

  return {
    currentCelebration,
    celebrationQueue: celebrationQueue.length,
    dismissCelebration
  };
};

// Hook for challenge spectator mode
export const useChallengeSpectator = (challengeId?: string, userId?: string) => {
  const [challengeData, setChallengeData] = useState<any>(null);
  const [liveProgress, setLiveProgress] = useState<{ challenger_progress: number; challenged_progress?: number } | null>(null);
  
  const { sendMessage, onChallengeUpdate } = useLeaderboardWebSocket(userId);

  // Subscribe to specific challenge updates
  useEffect(() => {
    if (challengeId) {
      sendMessage({
        type: 'subscribe_challenge',
        challenge_id: challengeId
      });
    }
  }, [challengeId, sendMessage]);

  // Handle challenge updates
  useEffect(() => {
    onChallengeUpdate((updateData: ChallengeUpdateData) => {
      if (!updateData) return;
      
      if (updateData.challenge_id === challengeId) {
        if (updateData.type === 'progress_update' && updateData.progress) {
          setLiveProgress(updateData.progress);
        }
      }
    });
  }, [challengeId, onChallengeUpdate]);

  return {
    challengeData,
    liveProgress,
    setChallengeData
  };
};

export default useLeaderboardWebSocket;