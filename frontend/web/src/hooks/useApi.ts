/**
 * BetBet Platform - Unified API Hook
 * ==================================
 *
 * SINGLE SOURCE OF TRUTH for all API access in the application.
 *
 * ⚠️  IMPORTANT: This is the ONLY API hook that should be used.
 * ⚠️  All API calls must go through the unified client.
 * ⚠️  Do NOT create separate API hooks for individual services.
 */

import { useAuth } from '@clerk/nextjs';
import { useEffect } from 'react';
import api from '@/lib/api-client-unified';

/**
 * Hook that provides unified API access with Clerk authentication
 *
 * @returns Unified API client with all service methods
 */
export function useApi() {
  const { getToken, isSignedIn } = useAuth();

  // Update the unified API client with current auth context
  useEffect(() => {
    const setupAuth = async () => {
      if (isSignedIn && getToken) {
        try {
          const token = await getToken();
          if (token) {
            api.setAuthToken(token);
          }
        } catch (error) {
          console.error('Failed to set auth token:', error);
        }
      }
    };

    setupAuth();
  }, [isSignedIn, getToken]);

  return api;
}

/**
 * @deprecated Use useApi() instead. This alias is for backward compatibility only.
 */
export const useMultiServiceApi = useApi;

/**
 * Hook that provides authentication status and user information
 */
export function useAuthStatus() {
  const { isSignedIn, isLoaded, userId, sessionId } = useAuth();

  return {
    isAuthenticated: isSignedIn,
    isLoading: !isLoaded,
    userId,
    sessionId,
  };
}