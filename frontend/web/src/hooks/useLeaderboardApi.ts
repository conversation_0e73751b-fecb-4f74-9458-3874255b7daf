/**
 * useLeaderboardApi - API Integration Hook for Leaderboards
 * =========================================================
 * 
 * Manages API calls for leaderboard data, achievements, competitions, and real-time updates.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';

// API Base Configuration (via API Gateway)
const LEADERBOARDS_API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Type definitions
type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
type LeaderboardView = 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';
type TimeRange = 'daily' | 'weekly' | 'monthly' | 'all_time';

interface UnifiedLeaderboardRequest {
  view?: LeaderboardView;
  affiliation_filter?: string;
  time_range?: TimeRange;
  limit?: number;
  offset?: number;
}

interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar_url?: string;
  affiliation?: {
    id: string;
    name: string;
    logo_url?: string;
  };
  unified_score: number;
  platform_tier: PlatformTier;
  tier_progress: number;
  gaming_score: number;
  gaming_change: number;
  betting_score: number;
  betting_change: number;
  trading_score: number;
  trading_change: number;
  analyst_score: number;
  analyst_change: number;
  cross_platform_consistency: number;
  active_modules: string[];
  recent_achievements: Achievement[];
  score_history: { timestamp: string; score: number; }[];
  rank_change?: number;
}

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  points: number;
  icon_url?: string;
  unlocked_at?: string;
}

interface UnifiedUserPerformance {
  user_id: string;
  global_ranking: {
    unified_rank: number;
    unified_score: number;
    percentile: number;
    total_users: number;
  };
  platform_tier: {
    current: PlatformTier;
    next: PlatformTier;
    progress: number;
    points_to_next: number;
  };
  module_rankings: {
    gaming: ModuleRanking;
    betting: ModuleRanking;
    trading: ModuleRanking;
    analyst: ModuleRanking;
  };
  cross_platform_consistency: number;
  active_modules: string[];
  performance_trends: {
    daily_scores: ScoreTrend[];
    weekly_scores: ScoreTrend[];
    monthly_scores: ScoreTrend[];
  };
  achievement_summary: {
    total_unlocked: number;
    total_available: number;
    recent_unlocks: Achievement[];
    progress_towards_next: AchievementProgress[];
  };
}

interface ModuleRanking {
  rank: number;
  score: number;
  percentile: number;
  change_24h: number;
  change_7d: number;
  best_rank_ever: number;
  best_score_ever: number;
}

interface ScoreTrend {
  timestamp: string;
  unified_score: number;
  gaming_score: number;
  betting_score: number;
  trading_score: number;
  analyst_score: number;
}

interface AchievementProgress {
  achievement: Achievement;
  progress_percentage: number;
  current_values: Record<string, any>;
  missing_requirements: any[];
  estimated_completion: string;
}

// API Response interfaces
interface LeaderboardResponse {
  success: boolean;
  data: {
    leaderboard: LeaderboardEntry[];
    total_count: number;
    cache_info: {
      cached: boolean;
      cache_age_seconds: number;
      expires_in_seconds: number;
    };
  };
}

interface PersonalRankingResponse {
  success: boolean;
  data: UnifiedUserPerformance;
}

// Custom hook for leaderboard API operations
export const useLeaderboardApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generic API call function
  const apiCall = useCallback(async <T>(endpoint: string, options?: RequestInit): Promise<T> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${LEADERBOARDS_API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'API request failed');
      }

      return data.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown API error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get unified leaderboards
  const getUnifiedLeaderboard = useCallback(async (
    params: UnifiedLeaderboardRequest = {}
  ): Promise<{ leaderboard: LeaderboardEntry[]; total_count: number }> => {
    const queryParams = new URLSearchParams();
    
    if (params.view) queryParams.append('view', params.view);
    if (params.affiliation_filter) queryParams.append('affiliation_filter', params.affiliation_filter);
    if (params.time_range) queryParams.append('time_range', params.time_range);
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());

    const endpoint = `/api/leaderboards/unified-leaderboards?${queryParams.toString()}`;
    return apiCall<{ leaderboard: LeaderboardEntry[]; total_count: number }>(endpoint);
  }, [apiCall]);

  // Get personal ranking
  const getPersonalRanking = useCallback(async (userId: string): Promise<UnifiedUserPerformance> => {
    const endpoint = `/api/leaderboards/personal-ranking/${userId}`;
    return apiCall<UnifiedUserPerformance>(endpoint);
  }, [apiCall]);

  // Get achievements
  const getAchievements = useCallback(async (params: {
    category?: string;
    modules?: string[];
    tier?: PlatformTier;
    rarity?: string;
    user_id?: string;
    unlocked_only?: boolean;
  } = {}): Promise<Achievement[]> => {
    const queryParams = new URLSearchParams();
    
    if (params.category) queryParams.append('category', params.category);
    if (params.modules) params.modules.forEach(module => queryParams.append('modules', module));
    if (params.tier) queryParams.append('tier', params.tier);
    if (params.rarity) queryParams.append('rarity', params.rarity);
    if (params.user_id) queryParams.append('user_id', params.user_id);
    if (params.unlocked_only) queryParams.append('unlocked_only', 'true');

    const endpoint = `/api/leaderboards/achievements?${queryParams.toString()}`;
    return apiCall<Achievement[]>(endpoint);
  }, [apiCall]);

  // Get global stats
  const getGlobalStats = useCallback(async (): Promise<{
    platform_overview: any;
    tier_distribution: any;
    module_popularity: any;
    top_achievements_this_week: any[];
    leaderboard_highlights: any;
  }> => {
    const endpoint = '/api/leaderboards/global-stats';
    return apiCall(endpoint);
  }, [apiCall]);

  // Refresh cache
  const refreshCache = useCallback(async (): Promise<{ success: boolean }> => {
    const endpoint = '/api/leaderboards/refresh-cache';
    return apiCall(endpoint, { method: 'POST' });
  }, [apiCall]);

  return {
    loading,
    error,
    getUnifiedLeaderboard,
    getPersonalRanking,
    getAchievements,
    getGlobalStats,
    refreshCache,
  };
};

// Hook for real-time leaderboard data
export const useLeaderboardData = (
  view: LeaderboardView = 'unified',
  timeRange: TimeRange = 'all_time',
  affiliationFilter?: string
) => {
  const [data, setData] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { getUnifiedLeaderboard } = useLeaderboardApi();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const result = await getUnifiedLeaderboard({
          view,
          time_range: timeRange,
          affiliation_filter: affiliationFilter,
          limit: 50
        });
        
        setData(result.leaderboard);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load leaderboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [view, timeRange, affiliationFilter, getUnifiedLeaderboard]);

  return { data, loading, error, refetch: () => setLoading(true) };
};

// Hook for user performance data
export const useUserPerformance = (userId?: string) => {
  const [data, setData] = useState<UnifiedUserPerformance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { getPersonalRanking } = useLeaderboardApi();

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const result = await getPersonalRanking(userId);
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load user performance');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, getPersonalRanking]);

  return { data, loading, error };
};

export default useLeaderboardApi;