/**
 * BetBet Chess Game Hook
 * ======================
 * 
 * Manages chess game state, WebSocket connection, and real-time updates.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';

interface ChessGameState {
  fen: string;
  moves: string[];
  moveHistory: Array<{
    move_uci: string;
    move_san: string;
    time_taken_ms: number;
    position_after: string;
  }>;
  currentTurn: 'white' | 'black';
  moveNumber: number;
  whiteTimeRemaining: number;
  blackTimeRemaining: number;
  isCheck: boolean;
  isCheckmate: boolean;
  isStalemate: boolean;
  lastMoveTime: string;
}

interface ChessGameInfo {
  id: string;
  whitePlayerId: string;
  blackPlayerId: string;
  whitePlayerName: string;
  blackPlayerName: string;
  variant: string;
  timeControl: string;
  status: 'waiting' | 'active' | 'paused' | 'completed';
  result?: string;
  spectatorCount: number;
}

interface ChessMove {
  from: string;
  to: string;
  promotion?: string;
}

interface UseChessGameOptions {
  gameId: string;
  onGameEnd?: (result: string) => void;
  onError?: (error: string) => void;
  onOpponentDisconnect?: () => void;
  onOpponentReconnect?: () => void;
}

export const useChessGame = (options: UseChessGameOptions) => {
  const { gameId, onGameEnd, onError, onOpponentDisconnect, onOpponentReconnect } = options;
  const { getToken } = useAuth();
  const { user } = useUser();
  
  // State
  const [gameState, setGameState] = useState<ChessGameState | null>(null);
  const [gameInfo, setGameInfo] = useState<ChessGameInfo | null>(null);
  const [playerColor, setPlayerColor] = useState<'white' | 'black' | 'spectator'>('spectator');
  const [legalMoves, setLegalMoves] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Derived state
  const isPlayerTurn = gameState && gameInfo && gameState.currentTurn === playerColor && gameInfo.status === 'active';
  const opponentColor = playerColor === 'white' ? 'black' : 'white';
  
  // Refs
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Interval | null>(null);
  
  // Connect to WebSocket
  const connectWebSocket = useCallback(async () => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Wait for game info to be loaded to get the actual game_id
      if (!gameInfo?.id) {
        console.log('Waiting for game info to load before connecting WebSocket...');
        return;
      }

      // Close existing connection
      if (wsRef.current) {
        wsRef.current.close();
      }

      // Create new WebSocket connection - connect directly to gaming engine
      const wsUrl = `ws://localhost:8000/ws/chess/${gameInfo.id}?token=${token}`;
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;
      
      ws.onopen = () => {
        console.log('Chess WebSocket connected');
        setConnectionStatus('connected');
        setError(null);

        // Authentication happens via token in URL, so immediately request game state
        ws.send(JSON.stringify({ type: 'request_game_state' }));
        ws.send(JSON.stringify({ type: 'request_legal_moves' }));

        // Start heartbeat
        heartbeatIntervalRef.current = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'ping' }));
          }
        }, 30000);
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onclose = (event) => {
        console.log('Chess WebSocket closed:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }
        
        // Attempt to reconnect unless it was a clean close
        if (event.code !== 1000 && event.code !== 1001) {
          scheduleReconnect();
        }
      };
      
      ws.onerror = (error) => {
        console.error('Chess WebSocket error:', error);
        setConnectionStatus('error');
        setError('WebSocket connection error');
        onError?.('WebSocket connection error');
      };
      
    } catch (err) {
      console.error('Failed to connect to chess WebSocket:', err);
      setConnectionStatus('error');
      setError(`Connection failed: ${err}`);
      onError?.(`Connection failed: ${err}`);
    }
  }, [gameInfo?.id, getToken, onError]);
  
  // Schedule reconnection
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('Attempting to reconnect to chess WebSocket...');
      connectWebSocket();
    }, 3000);
  }, [connectWebSocket]);
  
  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((message: any) => {
    console.log('Chess WebSocket message:', message);
    
    switch (message.type) {
      case 'connection_established':
        // Connection acknowledged by server
        console.log('Chess WebSocket connection established');
        break;

      case 'game_state_update':
        setGameState(message.payload);
        setIsLoading(false);
        break;
        
      case 'legal_moves':
        setLegalMoves(message.payload.legal_moves || []);
        break;
        
      case 'chess_move':
        // Move was made, request updated state
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ type: 'request_game_state' }));
          wsRef.current.send(JSON.stringify({ type: 'request_legal_moves' }));
        }
        break;
        
      case 'game_ended':
        onGameEnd?.(message.payload.result);
        break;
        
      case 'player_connected':
        if (message.payload.user_id !== playerColor) {
          onOpponentReconnect?.();
        }
        break;
        
      case 'player_disconnected':
        if (message.payload.user_id !== playerColor) {
          onOpponentDisconnect?.();
        }
        break;
        
      case 'error':
        setError(message.payload.message);
        onError?.(message.payload.message);
        break;
        
      case 'pong':
        // Heartbeat response
        break;
        
      default:
        console.log('Unhandled chess message type:', message.type);
    }
  }, [playerColor, onGameEnd, onOpponentDisconnect, onOpponentReconnect, onError]);
  
  // Make a move
  const makeMove = useCallback(async (move: ChessMove) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      setError('WebSocket not connected');
      return false;
    }
    
    if (!isPlayerTurn) {
      setError('It is not your turn');
      return false;
    }
    
    try {
      const message = {
        type: 'chess_move',
        payload: {
          move_uci: move.from + move.to + (move.promotion || ''),
          offer_draw: false
        }
      };
      
      wsRef.current.send(JSON.stringify(message));
      return true;
    } catch (err) {
      setError(`Failed to make move: ${err}`);
      return false;
    }
  }, [isPlayerTurn]);
  
  // Resign game
  const resignGame = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const message = {
      type: 'game_action',
      payload: {
        action: 'resign'
      }
    };
    
    wsRef.current.send(JSON.stringify(message));
  }, []);
  
  // Offer draw
  const offerDraw = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const message = {
      type: 'draw_offer',
      payload: {}
    };
    
    wsRef.current.send(JSON.stringify(message));
  }, []);
  
  // Respond to draw offer
  const respondToDraw = useCallback((accept: boolean) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const message = {
      type: 'draw_response',
      payload: {
        accepted: accept
      }
    };
    
    wsRef.current.send(JSON.stringify(message));
  }, []);
  
  // Send chat message
  const sendChatMessage = useCallback((message: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const chatMessage = {
      type: 'chat_message',
      payload: {
        message: message.trim()
      }
    };
    
    wsRef.current.send(JSON.stringify(chatMessage));
  }, []);
  
  // Initialize connection
  useEffect(() => {
    // connectWebSocket(); // This is now handled by the effect below
    
    return () => {
      // Cleanup
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting');
      }
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
    };
  }, [connectWebSocket]);
  
  // Fetch initial session and game info
  useEffect(() => {
    const fetchGameInfo = async () => {
      try {
        const token = await getToken();

        // For testing: Use hardcoded game data if the gameId matches our test game
        if (gameId === "550e8400-e29b-41d4-a716-446655441001") {
          const testGameInfo = {
            id: "550e8400-e29b-41d4-a716-446655441001",
            whitePlayerId: "550e8400-e29b-41d4-a716-446655440001",
            blackPlayerId: null,
            whitePlayerName: "ChessMaster2024",
            blackPlayerName: "Waiting...",
            variant: "standard",
            timeControl: "blitz",
            status: "waiting",
            result: null,
            spectatorCount: 0
          };

          setGameInfo(testGameInfo);

          // Determine player color based on user ID
          const userId = user?.id;
          if (userId === testGameInfo.whitePlayerId) {
            setPlayerColor('white');
          } else if (userId === testGameInfo.blackPlayerId) {
            setPlayerColor('black');
          } else {
            setPlayerColor('spectator');
          }

          // Don't wait for WebSocket - mark as loaded
          setIsLoading(false);
          return;
        }

        // First try to load as a chess game
        let response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/gaming/chess/games`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const chessGamesData = await response.json();
          const chessGame = chessGamesData.games?.find((game: any) => game.id === gameId);
          
          if (chessGame) {
            // Convert chess game to session format for compatibility
            const gameInfo = {
              id: chessGame.id,
              whitePlayerId: chessGame.white_player?.id,
              blackPlayerId: chessGame.black_player?.id,
              whitePlayerName: chessGame.white_player?.username || 'Waiting...',
              blackPlayerName: chessGame.black_player?.username || 'Waiting...',
              variant: chessGame.variant,
              timeControl: chessGame.time_control,
              status: chessGame.status,
              result: chessGame.result,
              spectatorCount: 0 // Will be updated via WebSocket
            };
            
            setGameInfo(gameInfo);

            // Determine player color based on user ID
            const userId = user?.id;
            if (userId === chessGame.white_player?.id) {
              setPlayerColor('white');
            } else if (userId === chessGame.black_player?.id) {
              setPlayerColor('black');
            } else {
              setPlayerColor('spectator');
            }
            
            // Don't wait for WebSocket - mark as loaded
            setIsLoading(false);
            return;
          }
        }

        // If not found as chess game, try to load as session
        response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/gaming/chess/testgame`, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch game info: ${response.statusText}`);
        }

        const chessGameResponse = await response.json();
        const chessGameData = chessGameResponse.game;

        if (chessGameData) {
          // Convert chess game to session format
          const sessionData = {
            id: chessGameData.id,
            slug: chessGameData.id,
            game_id: chessGameData.id,
            game_type: 'chess',
            status: chessGameData.status,
            white_player: chessGameData.white_player,
            black_player: chessGameData.black_player,
            current_fen: chessGameData.current_fen,
            move_count: chessGameData.move_count,
            time_control: chessGameData.time_control,
            initial_time_seconds: chessGameData.initial_time_seconds,
            increment_seconds: chessGameData.increment_seconds,
            white_time_remaining: chessGameData.white_time_remaining,
            black_time_remaining: chessGameData.black_time_remaining,
            variant: chessGameData.variant,
            is_rated: chessGameData.is_rated,
            wager_amount: chessGameData.wager_amount,
            description: chessGameData.description
          };

          setGameInfo(sessionData);
        } else {
          throw new Error('Chess game not found');
        }

        // For sessions, set default player color
        const userId = user?.id;
        setPlayerColor('white'); // Default for now, will be determined by game state
        
        // Don't wait for WebSocket - mark as loaded
        setIsLoading(false);
        
      } catch (err) {
        console.error('Failed to fetch game info:', err);
        setError(`Failed to load game: ${err}`);
      }
    };
    
    fetchGameInfo();
  }, [gameId, getToken, user]);

  // Connect WebSocket after game info is loaded
  useEffect(() => {
    if (gameInfo?.id) {
      connectWebSocket();
    }
  }, [gameInfo?.id, connectWebSocket]);

  return {
    // Game state
    gameState,
    gameInfo,
    playerColor,
    legalMoves,
    isPlayerTurn,
    opponentColor,
    
    // Connection state
    connectionStatus,
    isLoading,
    error,
    
    // Actions
    makeMove,
    resignGame,
    offerDraw,
    respondToDraw,
    sendChatMessage,
    
    // Connection management
    reconnect: connectWebSocket
  };
};