'use client';

import { useEffect, useRef, useCallback, useState } from 'react';

// Virtual scrolling hook for large lists
export function useVirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const totalHeight = items.length * itemHeight;
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(items.length - 1, startIndex + visibleCount + overscan * 2);
  
  const visibleItems = items.slice(startIndex, endIndex).map((item, index) => ({
    item,
    index: startIndex + index,
  }));

  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
}

// Debounced value hook
export function useDebounced<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Throttled callback hook
export function useThrottled<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

// Performance monitoring hook
export function usePerformanceMonitor(
  componentName: string,
  enabled: boolean = process.env.NODE_ENV === 'development'
) {
  const renderCountRef = useRef(0);
  const mountTimeRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>(0);

  useEffect(() => {
    if (!enabled) return;

    renderCountRef.current++;
    const now = performance.now();

    if (mountTimeRef.current === 0) {
      mountTimeRef.current = now;
      console.log(`[Performance] ${componentName} mounted`);
    }

    const renderTime = now - lastRenderTimeRef.current;
    lastRenderTimeRef.current = now;

    if (renderCountRef.current > 1 && renderTime > 16) { // > 16ms = < 60fps
      console.warn(
        `[Performance] ${componentName} slow render #${renderCountRef.current}: ${renderTime.toFixed(2)}ms`
      );
    }

    return () => {
      if (renderCountRef.current === 1) {
        const totalTime = performance.now() - mountTimeRef.current;
        console.log(
          `[Performance] ${componentName} unmounted after ${totalTime.toFixed(2)}ms, ${renderCountRef.current} renders`
        );
      }
    };
  });

  return {
    renderCount: renderCountRef.current,
    mountTime: mountTimeRef.current,
  };
}

// Intersection observer hook for lazy loading
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
) {
  const [element, setElement] = useState<Element | null>(null);

  useEffect(() => {
    if (!element) return;

    const observer = new IntersectionObserver(callback, options);
    observer.observe(element);

    return () => observer.disconnect();
  }, [element, callback, options]);

  return setElement;
}

// Memory usage monitoring
export function useMemoryMonitor(intervalMs: number = 5000) {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } | null>(null);

  useEffect(() => {
    if (!('memory' in performance)) return;

    const updateMemoryInfo = () => {
      const memory = (performance as any).memory;
      setMemoryInfo({
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      });
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, intervalMs);

    return () => clearInterval(interval);
  }, [intervalMs]);

  return memoryInfo;
}

// Request animation frame hook for smooth animations
export function useAnimationFrame(callback: (deltaTime: number) => void, deps: any[] = []) {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();

  const animate = useCallback((time: number) => {
    if (previousTimeRef.current !== undefined) {
      const deltaTime = time - previousTimeRef.current;
      callback(deltaTime);
    }
    previousTimeRef.current = time;
    requestRef.current = requestAnimationFrame(animate);
  }, deps);

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [animate]);
}

// Batch updates hook for reducing re-renders
export function useBatchedUpdates<T>(
  initialValue: T,
  batchDelay: number = 16 // ~60fps
): [T, (newValue: T) => void, () => void] {
  const [value, setValue] = useState<T>(initialValue);
  const pendingValueRef = useRef<T>(initialValue);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchedSetValue = useCallback((newValue: T) => {
    pendingValueRef.current = newValue;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setValue(pendingValueRef.current);
    }, batchDelay);
  }, [batchDelay]);

  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      setValue(pendingValueRef.current);
    }
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [value, batchedSetValue, flushUpdates];
}

// WebWorker hook for heavy computations
export function useWebWorker<T, R>(
  workerFunction: (data: T) => R,
  dependencies: any[] = []
) {
  const [worker, setWorker] = useState<Worker | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<R | null>(null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Create worker from function
    const workerBlob = new Blob([`
      self.onmessage = function(e) {
        try {
          const result = (${workerFunction.toString()})(e.data);
          self.postMessage({ success: true, result });
        } catch (error) {
          self.postMessage({ success: false, error: error.message });
        }
      }
    `], { type: 'application/javascript' });

    const workerUrl = URL.createObjectURL(workerBlob);
    const newWorker = new Worker(workerUrl);

    newWorker.onmessage = (e) => {
      const { success, result, error } = e.data;
      setIsLoading(false);
      
      if (success) {
        setResult(result);
        setError(null);
      } else {
        setError(new Error(error));
        setResult(null);
      }
    };

    setWorker(newWorker);

    return () => {
      newWorker.terminate();
      URL.revokeObjectURL(workerUrl);
    };
  }, dependencies);

  const execute = useCallback((data: T) => {
    if (!worker) return;
    
    setIsLoading(true);
    setError(null);
    worker.postMessage(data);
  }, [worker]);

  return { execute, isLoading, result, error };
}

// Canvas optimization hook
export function useCanvasOptimizations(canvasRef: React.RefObject<HTMLCanvasElement>) {
  const [dpr, setDpr] = useState(1);
  const ctxRef = useRef<CanvasRenderingContext2D | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set device pixel ratio for crisp rendering
    const devicePixelRatio = window.devicePixelRatio || 1;
    setDpr(devicePixelRatio);

    // Optimize context settings
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    ctxRef.current = ctx;

    // Handle resize
    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      const { width, height } = entry.contentRect;
      
      canvas.width = width * devicePixelRatio;
      canvas.height = height * devicePixelRatio;
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;
      
      ctx.scale(devicePixelRatio, devicePixelRatio);
    });

    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, [canvasRef]);

  return { dpr, ctx: ctxRef.current };
}