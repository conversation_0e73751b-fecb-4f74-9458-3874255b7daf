'use client';

import { useEffect, useCallback, useRef } from 'react';

interface ShortcutHandler {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  handler: () => void;
  description: string;
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  preventDefault?: boolean;
}

export function useKeyboardShortcuts(
  shortcuts: ShortcutHandler[],
  options: UseKeyboardShortcutsOptions = {}
) {
  const { enabled = true, preventDefault = true } = options;
  const shortcutsRef = useRef(shortcuts);
  
  // Update shortcuts ref when they change
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when typing in input fields
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return;
    }

    const { key, ctrl<PERSON>ey, shiftKey, altKey, metaKey } = event;
    
    // Check each shortcut
    for (const shortcut of shortcutsRef.current) {
      const matchesKey = key.toLowerCase() === shortcut.key.toLowerCase();
      const matchesCtrl = (shortcut.ctrl || false) === (ctrlKey || metaKey);
      const matchesShift = (shortcut.shift || false) === shiftKey;
      const matchesAlt = (shortcut.alt || false) === altKey;

      if (matchesKey && matchesCtrl && matchesShift && matchesAlt) {
        if (preventDefault) {
          event.preventDefault();
          event.stopPropagation();
        }
        shortcut.handler();
        break;
      }
    }
  }, [enabled, preventDefault]);

  useEffect(() => {
    if (!enabled) return;

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [enabled, handleKeyDown]);

  return shortcuts;
}

// Trading-specific keyboard shortcuts hook
export function useTradingKeyboardShortcuts({
  onBuyMarket,
  onSellMarket,
  onBuyLimit,
  onSellLimit,
  onCancelAllOrders,
  onCloseAllPositions,
  onToggleOrderBook,
  onToggleChart,
  onFocusQuantity,
  onFocusPrice,
  onSwitchMarket,
  onToggleFullscreen,
  enabled = true
}: {
  onBuyMarket?: () => void;
  onSellMarket?: () => void;
  onBuyLimit?: () => void;
  onSellLimit?: () => void;
  onCancelAllOrders?: () => void;
  onCloseAllPositions?: () => void;
  onToggleOrderBook?: () => void;
  onToggleChart?: () => void;
  onFocusQuantity?: () => void;
  onFocusPrice?: () => void;
  onSwitchMarket?: (direction: 'next' | 'prev') => void;
  onToggleFullscreen?: () => void;
  enabled?: boolean;
}) {
  const shortcuts: ShortcutHandler[] = [
    // Trading Actions
    {
      key: 'b',
      description: 'Buy Market Order',
      handler: () => onBuyMarket?.()
    },
    {
      key: 's',
      description: 'Sell Market Order',
      handler: () => onSellMarket?.()
    },
    {
      key: 'b',
      shift: true,
      description: 'Buy Limit Order',
      handler: () => onBuyLimit?.()
    },
    {
      key: 's',
      shift: true,
      description: 'Sell Limit Order',
      handler: () => onSellLimit?.()
    },
    
    // Order Management
    {
      key: 'c',
      ctrl: true,
      description: 'Cancel All Orders',
      handler: () => onCancelAllOrders?.()
    },
    {
      key: 'x',
      ctrl: true,
      description: 'Close All Positions',
      handler: () => onCloseAllPositions?.()
    },
    
    // Navigation
    {
      key: 'o',
      description: 'Toggle Order Book',
      handler: () => onToggleOrderBook?.()
    },
    {
      key: 'k',
      description: 'Toggle Chart',
      handler: () => onToggleChart?.()
    },
    {
      key: 'q',
      description: 'Focus Quantity Input',
      handler: () => onFocusQuantity?.()
    },
    {
      key: 'p',
      description: 'Focus Price Input',
      handler: () => onFocusPrice?.()
    },
    {
      key: 'ArrowUp',
      alt: true,
      description: 'Previous Market',
      handler: () => onSwitchMarket?.('prev')
    },
    {
      key: 'ArrowDown',
      alt: true,
      description: 'Next Market',
      handler: () => onSwitchMarket?.('next')
    },
    {
      key: 'f',
      alt: true,
      description: 'Toggle Fullscreen',
      handler: () => onToggleFullscreen?.()
    }
  ].filter(shortcut => shortcut.handler !== undefined);

  return useKeyboardShortcuts(shortcuts, { enabled });
}

// Hook to display available shortcuts
export function useShortcutHelp() {
  const shortcuts = [
    { keys: 'B', description: 'Buy Market Order' },
    { keys: 'S', description: 'Sell Market Order' },
    { keys: 'Shift + B', description: 'Buy Limit Order' },
    { keys: 'Shift + S', description: 'Sell Limit Order' },
    { keys: 'Ctrl + C', description: 'Cancel All Orders' },
    { keys: 'Ctrl + X', description: 'Close All Positions' },
    { keys: 'O', description: 'Toggle Order Book' },
    { keys: 'K', description: 'Toggle Chart' },
    { keys: 'Q', description: 'Focus Quantity Input' },
    { keys: 'P', description: 'Focus Price Input' },
    { keys: 'Alt + ↑', description: 'Previous Market' },
    { keys: 'Alt + ↓', description: 'Next Market' },
    { keys: 'Alt + F', description: 'Toggle Fullscreen' },
    { keys: '?', description: 'Show Keyboard Shortcuts' }
  ];

  return shortcuts;
}