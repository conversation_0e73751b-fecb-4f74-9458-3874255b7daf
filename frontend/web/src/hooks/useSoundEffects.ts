/**
 * BetBet Chess Sound Effects Hook
 * ===============================
 * 
 * Manages chess game sound effects with volume control and muting.
 */

import { useCallback, useRef, useEffect, useState } from 'react';

interface SoundEffectsOptions {
  volume?: number; // 0.0 to 1.0
  muted?: boolean;
  preload?: boolean;
}

export const useSoundEffects = (options: SoundEffectsOptions = {}) => {
  const {
    volume = 0.7,
    muted = false,
    preload = true
  } = options;
  
  // Audio refs
  const moveAudioRef = useRef<HTMLAudioElement | null>(null);
  const captureAudioRef = useRef<HTMLAudioElement | null>(null);
  const checkAudioRef = useRef<HTMLAudioElement | null>(null);
  
  // State
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  
  // Initialize audio objects
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        // Create audio objects
        moveAudioRef.current = new Audio('/assets/chess/sounds/move.mp3');
        captureAudioRef.current = new Audio('/assets/chess/sounds/capture.mp3');
        checkAudioRef.current = new Audio('/assets/chess/sounds/notify.mp3'); // Using notify for check sound
        
        // Set volume for all audio objects
        const audioElements = [moveAudioRef.current, captureAudioRef.current, checkAudioRef.current];
        audioElements.forEach(audio => {
          if (audio) {
            audio.volume = volume;
            audio.preload = preload ? 'auto' : 'none';
          }
        });
        
        // Preload audio if requested
        if (preload) {
          await Promise.all(
            audioElements.map(audio => {
              return new Promise<void>((resolve, reject) => {
                if (!audio) {
                  resolve();
                  return;
                }
                
                const handleCanPlay = () => {
                  audio.removeEventListener('canplaythrough', handleCanPlay);
                  audio.removeEventListener('error', handleError);
                  resolve();
                };
                
                const handleError = (e: Event) => {
                  audio.removeEventListener('canplaythrough', handleCanPlay);
                  audio.removeEventListener('error', handleError);
                  console.warn('Failed to preload audio:', audio.src, e);
                  resolve(); // Don't reject, just continue without preloading
                };
                
                if (audio.readyState >= 4) {
                  resolve();
                } else {
                  audio.addEventListener('canplaythrough', handleCanPlay);
                  audio.addEventListener('error', handleError);
                  audio.load();
                }
              });
            })
          );
        }
        
        setIsLoaded(true);
      } catch (error) {
        setLoadError(`Failed to initialize audio: ${error}`);
        console.error('Audio initialization error:', error);
      }
    };
    
    initializeAudio();
    
    // Cleanup
    return () => {
      [moveAudioRef, captureAudioRef, checkAudioRef].forEach(ref => {
        if (ref.current) {
          ref.current.pause();
          ref.current.src = '';
        }
      });
    };
  }, [volume, preload]);
  
  // Update volume when it changes
  useEffect(() => {
    const audioElements = [moveAudioRef.current, captureAudioRef.current, checkAudioRef.current];
    audioElements.forEach(audio => {
      if (audio) {
        audio.volume = muted ? 0 : volume;
      }
    });
  }, [volume, muted]);
  
  // Play sound with error handling
  const playSound = useCallback(async (audioRef: React.RefObject<HTMLAudioElement>) => {
    if (!audioRef.current || muted || !isLoaded) {
      return;
    }
    
    try {
      // Reset audio to beginning
      audioRef.current.currentTime = 0;
      
      // Play the sound
      await audioRef.current.play();
    } catch (error) {
      // Ignore play errors (often due to autoplay policies)
      console.debug('Audio play failed:', error);
    }
  }, [muted, isLoaded]);
  
  // Sound effect functions
  const playMoveSound = useCallback(() => {
    playSound(moveAudioRef);
  }, [playSound]);
  
  const playCaptureSound = useCallback(() => {
    playSound(captureAudioRef);
  }, [playSound]);
  
  const playCheckSound = useCallback(() => {
    playSound(checkAudioRef);
  }, [playSound]);
  
  // Play specific sound by name
  const playNamedSound = useCallback((soundName: 'move' | 'capture' | 'check') => {
    switch (soundName) {
      case 'move':
        playMoveSound();
        break;
      case 'capture':
        playCaptureSound();
        break;
      case 'check':
        playCheckSound();
        break;
    }
  }, [playMoveSound, playCaptureSound, playCheckSound]);
  
  // Test all sounds
  const testSounds = useCallback(async () => {
    const sounds = [
      { name: 'move', fn: playMoveSound },
      { name: 'capture', fn: playCaptureSound },
      { name: 'check', fn: playCheckSound }
    ];
    
    for (const sound of sounds) {
      console.log(`Testing ${sound.name} sound...`);
      sound.fn();
      await new Promise(resolve => setTimeout(resolve, 500)); // Wait between sounds
    }
  }, [playMoveSound, playCaptureSound, playCheckSound]);
  
  return {
    // Sound functions
    playMoveSound,
    playCaptureSound,
    playCheckSound,
    playNamedSound,
    testSounds,
    
    // State
    isLoaded,
    loadError,
    
    // Audio refs (for advanced usage)
    audioRefs: {
      move: moveAudioRef,
      capture: captureAudioRef,
      check: checkAudioRef
    }
  };
};