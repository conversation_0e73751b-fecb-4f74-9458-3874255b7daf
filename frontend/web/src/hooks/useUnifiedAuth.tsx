/**
 * BetBet Unified Authentication Hook
 * ==================================
 * 
 * React hook for managing authentication state and operations
 * across the entire BetBet platform using the unified API client.
 */

import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { unifiedApiClient, LoginCredentials, User, AuthTokens } from '@/lib/unified-api-client';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
}

// Create auth context
const AuthContext = createContext<AuthContextType | null>(null);

/**
 * Auth Provider Component
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  /**
   * Set loading state
   */
  const setLoading = useCallback((loading: boolean) => {
    setAuthState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  /**
   * Set error state
   */
  const setError = useCallback((error: string | null) => {
    setAuthState(prev => ({ ...prev, error }));
  }, []);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  /**
   * Set authenticated user
   */
  const setUser = useCallback((user: User | null) => {
    setAuthState(prev => ({
      ...prev,
      user,
      isAuthenticated: !!user,
      error: null,
    }));
  }, []);

  /**
   * Login function
   */
  const login = useCallback(async (credentials: LoginCredentials) => {
    setLoading(true);
    setError(null);

    try {
      const { user } = await unifiedApiClient.login(credentials);
      setUser(user);
    } catch (error: any) {
      setError(error.message || 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError, setUser]);

  /**
   * Logout function
   */
  const logout = useCallback(async () => {
    setLoading(true);

    try {
      await unifiedApiClient.logout();
    } catch (error: any) {
      console.warn('Logout error:', error);
    } finally {
      setUser(null);
      setLoading(false);
    }
  }, [setLoading, setUser]);

  /**
   * Refresh authentication state
   */
  const refreshAuth = useCallback(async () => {
    if (!unifiedApiClient.isAuthenticated()) {
      setUser(null);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      const user = await unifiedApiClient.getCurrentUser();
      setUser(user);
    } catch (error: any) {
      console.warn('Auth refresh failed:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [setLoading, setUser]);

  /**
   * Handle authentication failure events
   */
  const handleAuthFailure = useCallback(() => {
    setUser(null);
    setError('Your session has expired. Please log in again.');
  }, [setUser, setError]);

  /**
   * Initialize auth state on mount
   */
  useEffect(() => {
    refreshAuth();
  }, [refreshAuth]);

  /**
   * Listen for authentication failure events
   */
  useEffect(() => {
    const handleAuthFailureEvent = () => handleAuthFailure();
    
    if (typeof window !== 'undefined') {
      window.addEventListener('betbet:auth:failure', handleAuthFailureEvent);
      
      return () => {
        window.removeEventListener('betbet:auth:failure', handleAuthFailureEvent);
      };
    }
  }, [handleAuthFailure]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    refreshAuth,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use authentication context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Hook for checking specific permissions
 */
export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = useCallback((permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  }, [user]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  const hasRole = useCallback((role: string): boolean => {
    return user?.role === role;
  }, [user]);

  const hasAnyRole = useCallback((roles: string[]): boolean => {
    return roles.includes(user?.role || '');
  }, [user]);

  return {
    hasPermission,
    hasAnyPermission,
    hasRole,
    hasAnyRole,
    permissions: user?.permissions || [],
    role: user?.role || null,
  };
};

/**
 * Permission-based component wrapper
 */
interface RequirePermissionProps {
  permission: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const RequirePermission: React.FC<RequirePermissionProps> = ({
  permission,
  fallback = null,
  children,
}) => {
  const { hasPermission } = usePermissions();

  if (!hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Role-based component wrapper
 */
interface RequireRoleProps {
  role: string | string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const RequireRole: React.FC<RequireRoleProps> = ({
  role,
  fallback = null,
  children,
}) => {
  const { hasRole, hasAnyRole } = usePermissions();

  const hasRequiredRole = Array.isArray(role) 
    ? hasAnyRole(role)
    : hasRole(role);

  if (!hasRequiredRole) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Authentication guard component
 */
interface AuthGuardProps {
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  fallback = <div>Please log in to continue.</div>,
  children,
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Export default hook
export default useAuth;