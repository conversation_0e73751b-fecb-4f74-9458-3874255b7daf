/**
 * Custom Betting Platform - WebSocket Hook
 * ========================================
 * 
 * React hook for managing WebSocket connections to the Custom Betting Platform
 * for real-time market updates, betting activity, and live notifications.
 */

'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';

export interface WebSocketMessage {
  type: 'market_update' | 'bet_placed' | 'bet_matched' | 'participant_joined' | 'odds_change' | 'market_status_change';
  data: any;
  timestamp: string;
  market_id?: string;
}

export interface MarketUpdateData {
  market_id: string;
  total_stakes: number;
  total_participants: number;
  status: string;
  outcomes: Array<{
    id: string;
    current_odds: number;
    implied_probability: number;
    total_backing: number;
    total_laying: number;
  }>;
}

export interface BetActivityData {
  market_id: string;
  user_id: string;
  outcome_id: string;
  position_type: 'backing' | 'laying';
  stake_amount: number;
  desired_odds: number;
  activity_type: 'placed' | 'matched' | 'cancelled';
}

interface UseCustomBettingWebSocketOptions {
  marketId?: string;
  enabled?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export function useCustomBettingWebSocket(options: UseCustomBettingWebSocketOptions = {}) {
  const {
    marketId,
    enabled = true,
    reconnectAttempts = 5,
    reconnectDelay = 3000
  } = options;

  const { getToken } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const messageHandlersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map());

  // Message handler registration
  const subscribe = useCallback((messageType: string, handler: (data: any) => void) => {
    if (!messageHandlersRef.current.has(messageType)) {
      messageHandlersRef.current.set(messageType, new Set());
    }
    messageHandlersRef.current.get(messageType)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = messageHandlersRef.current.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          messageHandlersRef.current.delete(messageType);
        }
      }
    };
  }, []);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const token = await getToken();
      if (!token) {
        setConnectionError('Authentication token not available');
        return;
      }

      // Build WebSocket URL
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsHost = process.env.NEXT_PUBLIC_API_URL?.replace(/^https?:\/\//, '') || 'localhost:8000';
      let wsUrl = `${wsProtocol}//${wsHost}/ws/custom-betting`;
      
      // Add market-specific connection if marketId is provided
      if (marketId) {
        wsUrl += `/market/${marketId}`;
      }

      // Add authentication token as query parameter
      wsUrl += `?token=${encodeURIComponent(token)}`;

      console.log('Connecting to Custom Betting WebSocket:', wsUrl);

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Custom Betting WebSocket connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectCountRef.current = 0;

        // Send initial subscription message
        ws.send(JSON.stringify({
          type: 'subscribe',
          data: {
            market_id: marketId,
            events: ['market_update', 'bet_activity', 'odds_change']
          }
        }));
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);

          // Call registered handlers for this message type
          const handlers = messageHandlersRef.current.get(message.type);
          if (handlers) {
            handlers.forEach(handler => {
              try {
                handler(message.data);
              } catch (error) {
                console.error('Error in WebSocket message handler:', error);
              }
            });
          }

          console.log('Custom Betting WebSocket message:', message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('Custom Betting WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        wsRef.current = null;

        // Attempt reconnection if not manually closed
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          const delay = reconnectDelay * Math.pow(2, reconnectCountRef.current); // Exponential backoff
          console.log(`Attempting to reconnect in ${delay}ms... (attempt ${reconnectCountRef.current + 1}/${reconnectAttempts})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectCountRef.current++;
            connect();
          }, delay);
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          setConnectionError('Maximum reconnection attempts reached');
        }
      };

      ws.onerror = (error) => {
        console.error('Custom Betting WebSocket error:', error);
        setConnectionError('WebSocket connection error');
      };

    } catch (error) {
      console.error('Failed to connect to Custom Betting WebSocket:', error);
      setConnectionError('Failed to establish connection');
    }
  }, [enabled, marketId, getToken, reconnectAttempts, reconnectDelay]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionError(null);
    reconnectCountRef.current = 0;
  }, []);

  // Send message through WebSocket
  const sendMessage = useCallback((type: string, data: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type, data }));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, []);

  // Effect to manage connection
  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, marketId, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
      messageHandlersRef.current.clear();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionError,
    lastMessage,
    subscribe,
    sendMessage,
    connect,
    disconnect
  };
}

// Convenience hooks for specific message types
export function useMarketUpdates(marketId: string) {
  const [marketData, setMarketData] = useState<MarketUpdateData | null>(null);
  const { subscribe } = useCustomBettingWebSocket({ marketId });

  useEffect(() => {
    const unsubscribe = subscribe('market_update', (data: MarketUpdateData) => {
      if (data.market_id === marketId) {
        setMarketData(data);
      }
    });

    return unsubscribe;
  }, [marketId, subscribe]);

  return marketData;
}

export function useBetActivity(marketId: string) {
  const [recentActivity, setRecentActivity] = useState<BetActivityData[]>([]);
  const { subscribe } = useCustomBettingWebSocket({ marketId });

  useEffect(() => {
    const unsubscribe = subscribe('bet_placed', (data: BetActivityData) => {
      if (data.market_id === marketId) {
        setRecentActivity(prev => [data, ...prev.slice(0, 9)]); // Keep last 10 activities
      }
    });

    return unsubscribe;
  }, [marketId, subscribe]);

  return recentActivity;
}

export function useOddsChanges(marketId: string) {
  const [oddsChanges, setOddsChanges] = useState<Map<string, number>>(new Map());
  const { subscribe } = useCustomBettingWebSocket({ marketId });

  useEffect(() => {
    const unsubscribe = subscribe('odds_change', (data: any) => {
      if (data.market_id === marketId) {
        setOddsChanges(prev => new Map(prev.set(data.outcome_id, data.new_odds)));
      }
    });

    return unsubscribe;
  }, [marketId, subscribe]);

  return oddsChanges;
}