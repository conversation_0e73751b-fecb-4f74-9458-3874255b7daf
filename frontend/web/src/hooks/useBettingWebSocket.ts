/**
 * BetBet Gaming Engine - Betting WebSocket Hook
 * =============================================
 * 
 * Real-time WebSocket connection for betting market updates.
 * Handles bet placements, odds changes, and market status updates.
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';

interface BettingWebSocketMessage {
  type: 'bet_placed' | 'odds_updated' | 'market_closed' | 'market_opened' | 'connection' | 'error';
  data: any;
  timestamp?: string;
  market_id?: string;
}

interface BetPlacedData {
  bet_id: string;
  user_id: string;
  market_id: string;
  outcome_id: string;
  bet_amount: number;
  odds: number;
  potential_return: number;
}

interface OddsUpdatedData {
  market_id: string;
  outcome_id: string;
  old_odds: number;
  new_odds: number;
  implied_probability: number;
}

interface MarketStatusData {
  market_id: string;
  status: 'active' | 'suspended' | 'closed' | 'settled';
  total_pool: number;
  total_bets: number;
}

interface UseBettingWebSocketOptions {
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onBetPlaced?: (data: BetPlacedData) => void;
  onOddsUpdated?: (data: OddsUpdatedData) => void;
  onMarketStatusChanged?: (data: MarketStatusData) => void;
  onError?: (error: string) => void;
}

export const useBettingWebSocket = (
  marketId: string | null, 
  options: UseBettingWebSocketOptions = {}
) => {
  const {
    autoConnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
    onBetPlaced,
    onOddsUpdated,
    onMarketStatusChanged,
    onError,
  } = options;

  const { getToken } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastMessage, setLastMessage] = useState<BettingWebSocketMessage | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const connect = useCallback(async () => {
    if (!marketId || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      // Get auth token for WebSocket authentication
      const token = await getToken();
      
      // Construct WebSocket URL for betting (via WebSocket Manager)
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws';
      const url = `${wsUrl}/betting/${marketId}${token ? `?token=${token}` : ''}`;
      
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Betting WebSocket connected:', marketId);
        setIsConnected(true);
        setIsConnecting(false);
        reconnectAttemptsRef.current = 0;
        
        // Send subscription message
        const subscribeMessage = {
          type: 'subscribe',
          market_id: marketId,
          timestamp: new Date().toISOString()
        };
        ws.send(JSON.stringify(subscribeMessage));
      };

      ws.onclose = () => {
        console.log('Betting WebSocket disconnected:', marketId);
        setIsConnected(false);
        setIsConnecting(false);
        wsRef.current = null;

        // Attempt reconnection if within limits
        if (
          autoConnect &&
          reconnectAttemptsRef.current < maxReconnectAttempts
        ) {
          reconnectAttemptsRef.current += 1;
          console.log(`Reconnecting betting WebSocket... Attempt ${reconnectAttemptsRef.current}`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      ws.onerror = (event) => {
        console.error('Betting WebSocket error:', event);
        const errorMessage = 'Betting WebSocket connection error';
        setError(errorMessage);
        onError?.(errorMessage);
      };

      ws.onmessage = (event) => {
        try {
          const message: BettingWebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);
          
          // Handle different message types
          switch (message.type) {
            case 'bet_placed':
              onBetPlaced?.(message.data as BetPlacedData);
              break;
            case 'odds_updated':
              onOddsUpdated?.(message.data as OddsUpdatedData);
              break;
            case 'market_closed':
            case 'market_opened':
              onMarketStatusChanged?.(message.data as MarketStatusData);
              break;
            case 'error':
              const errorMsg = message.data?.message || 'Betting WebSocket error';
              setError(errorMsg);
              onError?.(errorMsg);
              break;
            case 'connection':
              console.log('Betting WebSocket connection confirmed:', message.data);
              break;
            default:
              console.log('Unknown betting WebSocket message type:', message.type);
          }
        } catch (err) {
          console.error('Failed to parse betting WebSocket message:', err);
        }
      };
    } catch (err) {
      console.error('Failed to connect betting WebSocket:', err);
      const errorMessage = 'Failed to establish betting connection';
      setError(errorMessage);
      setIsConnecting(false);
      onError?.(errorMessage);
    }
  }, [marketId, getToken, autoConnect, reconnectInterval, maxReconnectAttempts, onBetPlaced, onOddsUpdated, onMarketStatusChanged, onError]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      // Send unsubscribe message before closing
      if (wsRef.current.readyState === WebSocket.OPEN) {
        const unsubscribeMessage = {
          type: 'unsubscribe',
          market_id: marketId,
          timestamp: new Date().toISOString()
        };
        wsRef.current.send(JSON.stringify(unsubscribeMessage));
      }
      
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
  }, [marketId]);

  const sendMessage = useCallback((type: string, data: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const message: BettingWebSocketMessage = {
        type: type as any,
        data,
        timestamp: new Date().toISOString(),
        market_id: marketId || undefined,
      };
      
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    
    console.warn('Betting WebSocket not connected, cannot send message');
    return false;
  }, [marketId]);

  // Auto-connect when marketId is available
  useEffect(() => {
    if (marketId && autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [marketId, autoConnect, connect, disconnect]);

  return {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
  };
};

// Export type definitions for use in components
export type { BettingWebSocketMessage, BetPlacedData, OddsUpdatedData, MarketStatusData };