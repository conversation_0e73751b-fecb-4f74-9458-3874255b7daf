import { useEffect, useRef, useCallback, useState } from 'react';
import { useTradingStore } from '@/store/tradingStore';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface OrderSubmission {
  market_id: string;
  side: 'buy' | 'sell';
  order_type: 'market' | 'limit' | 'stop_loss' | 'take_profit';
  quantity: string;
  price?: string;
  time_in_force?: string;
}

const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/trading';
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const RECONNECT_DELAY = 3000; // 3 seconds

export function useTradingWebSocket(userId: string) {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const subscribedMarketsRef = useRef<Set<string>>(new Set());
  
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const {
    setConnected: setStoreConnected,
    updateOrderBook,
    updateOrders,
    updatePositions,
    addOrder,
    updateOrder,
    updateMarketTickers
  } = useTradingStore();

  // Handle incoming WebSocket messages
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'welcome':
          console.log('Trading WebSocket connected:', message.message);
          break;
          
        case 'orderbook_snapshot':
          updateOrderBook({
            marketId: message.market_id,
            bids: message.bids?.map(([price, qty]: [string, string]) => ({
              price: parseFloat(price),
              quantity: parseFloat(qty),
              orderCount: 1 // Simplified
            })) || [],
            asks: message.asks?.map(([price, qty]: [string, string]) => ({
              price: parseFloat(price),
              quantity: parseFloat(qty),
              orderCount: 1 // Simplified
            })) || [],
            bestBid: message.bids?.[0] ? parseFloat(message.bids[0][0]) : null,
            bestAsk: message.asks?.[0] ? parseFloat(message.asks[0][0]) : null
          });
          break;
          
        case 'orderbook_update':
          // Handle incremental order book updates
          if (message.market_id) {
            updateOrderBook({
              marketId: message.market_id
            });
          }
          break;
          
        case 'market_trade':
          // Update last price and other market data
          updateMarketTickers([{
            marketId: message.market_id,
            symbol: `MARKET_${message.market_id.slice(0, 8)}`,
            lastPrice: parseFloat(message.price),
            priceChange24h: null,
            priceChangePercentage24h: null,
            volume24h: parseFloat(message.quantity)
          }]);
          break;
          
        case 'trade_execution':
          console.log('Trade executed:', message);
          // Handle user's trade execution
          break;
          
        case 'order_update':
          updateOrder(message.order_id, {
            status: message.status,
            filledQuantity: parseFloat(message.filled_quantity),
            remainingQuantity: parseFloat(message.remaining_quantity)
          });
          break;
          
        case 'market_summary':
          // Update market summary data
          break;
          
        case 'pong':
          // Heartbeat response
          break;
          
        case 'subscription_confirmed':
          console.log('Market subscription confirmed:', message.market_id);
          break;
          
        case 'error':
          console.error('WebSocket error:', message.message);
          setError(message.message);
          break;
          
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }, [updateOrderBook, updateOrder, updateMarketTickers]);

  // Send message to WebSocket
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message:', message);
    }
  }, []);

  // Start heartbeat
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    
    heartbeatIntervalRef.current = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, HEARTBEAT_INTERVAL);
  }, [sendMessage]);

  // Stop heartbeat
  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (connecting || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    // Skip actual WebSocket connection in test environment
    if (process.env.NODE_ENV === 'test' || typeof window === 'undefined') {
      console.log('Skipping WebSocket connection in test environment');
      setConnected(true);
      setStoreConnected(true);
      setConnecting(false);
      setError(null);
      return;
    }

    setConnecting(true);
    setError(null);

    const wsUrl = `${WS_URL}/${userId}`;
    console.log('Connecting to trading WebSocket:', wsUrl);

    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

    ws.onopen = () => {
      console.log('Trading WebSocket connected');
      setConnected(true);
      setStoreConnected(true);
      setConnecting(false);
      setError(null);
      startHeartbeat();
      
      // Resubscribe to markets
      subscribedMarketsRef.current.forEach(marketId => {
        sendMessage({
          type: 'subscribe_market',
          market_id: marketId
        });
      });
    };

    ws.onmessage = handleMessage;

    ws.onerror = (error) => {
      console.error('Trading WebSocket error:', error);
      setError('Connection error');
    };

      ws.onclose = (event) => {
        console.log('Trading WebSocket closed:', event.code, event.reason);
        setConnected(false);
        setStoreConnected(false);
        setConnecting(false);
        stopHeartbeat();
        
        // Attempt to reconnect unless it was a clean close
        if (event.code !== 1000) {
          console.log(`Reconnecting in ${RECONNECT_DELAY}ms...`);
          reconnectTimeoutRef.current = setTimeout(connect, RECONNECT_DELAY);
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setError('Failed to establish connection');
      setConnecting(false);
    }
  }, [userId, connecting, handleMessage, sendMessage, startHeartbeat, stopHeartbeat, setStoreConnected]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    stopHeartbeat();
    
    if (wsRef.current) {
      wsRef.current.close(1000);
      wsRef.current = null;
    }
    
    setConnected(false);
    setStoreConnected(false);
    setConnecting(false);
  }, [stopHeartbeat, setStoreConnected]);

  // Subscribe to market updates
  const subscribeToMarket = useCallback((marketId: string) => {
    subscribedMarketsRef.current.add(marketId);
    sendMessage({
      type: 'subscribe_market',
      market_id: marketId
    });
  }, [sendMessage]);

  // Unsubscribe from market updates
  const unsubscribeFromMarket = useCallback((marketId: string) => {
    subscribedMarketsRef.current.delete(marketId);
    sendMessage({
      type: 'unsubscribe_market',
      market_id: marketId
    });
  }, [sendMessage]);

  // Submit order through API (not WebSocket)
  const submitOrder = useCallback(async (orderData: OrderSubmission) => {
    // In test environment, return mock response
    if (process.env.NODE_ENV === 'test') {
      const mockOrder = {
        id: `order-${Date.now()}`,
        market_id: orderData.market_id,
        side: orderData.side,
        order_type: orderData.order_type,
        quantity: orderData.quantity,
        price: orderData.price || null,
        filled_quantity: '0',
        remaining_quantity: orderData.quantity,
        status: 'pending',
        timestamp: new Date().toISOString()
      };
      
      // Simulate the order addition to store
      addOrder({
        id: mockOrder.id,
        marketId: mockOrder.market_id,
        marketName: `Market ${mockOrder.market_id.slice(0, 8)}`,
        side: mockOrder.side,
        orderType: mockOrder.order_type,
        quantity: parseFloat(mockOrder.quantity),
        price: mockOrder.price ? parseFloat(mockOrder.price) : null,
        filledQuantity: parseFloat(mockOrder.filled_quantity),
        remainingQuantity: parseFloat(mockOrder.remaining_quantity),
        status: mockOrder.status,
        timestamp: mockOrder.timestamp
      });
      
      return mockOrder;
    }

    try {
      const response = await fetch('/api/trading/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // TODO: Add authentication header when auth is implemented
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to submit order');
      }

      const order = await response.json();
      
      // Add order to store (will be updated via WebSocket)
      addOrder({
        id: order.id,
        marketId: order.market_id,
        marketName: `Market ${order.market_id.slice(0, 8)}`,
        side: order.side,
        orderType: order.order_type,
        quantity: parseFloat(order.quantity),
        price: order.price ? parseFloat(order.price) : null,
        filledQuantity: parseFloat(order.filled_quantity),
        remainingQuantity: parseFloat(order.remaining_quantity),
        status: order.status,
        timestamp: order.timestamp
      });

      return order;
    } catch (error) {
      console.error('Failed to submit order:', error);
      throw error;
    }
  }, [addOrder]);

  // Cancel order
  const cancelOrder = useCallback(async (orderId: string) => {
    try {
      const response = await fetch(`/api/trading/orders/${orderId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${await getAuthToken()}` // TODO: Implement auth token
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to cancel order');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Failed to cancel order:', error);
      throw error;
    }
  }, []);

  // Initialize connection
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connected,
    connecting,
    error,
    subscribeToMarket,
    unsubscribeFromMarket,
    submitOrder,
    cancelOrder,
    sendMessage
  };
}

// TODO: Implement proper auth token retrieval
async function getAuthToken(): Promise<string> {
  // This should integrate with your auth system (Clerk)
  return 'mock-token';
}