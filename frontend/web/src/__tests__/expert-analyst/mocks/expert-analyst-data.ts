/**
 * Expert Analyst Mock Data
 * ========================
 * 
 * Centralized mock data for Expert Analyst module testing.
 * Provides realistic test data for various testing scenarios.
 */

import type {
  Expert,
  Pick,
  Subscription,
  SubscriptionTier,
  Review,
  ExpertPerformance,
  ExpertAnalytics,
  PaymentIntent
} from '@/lib/expert-analyst-api';

// Expert profiles with various specializations and performance levels
export const mockExperts: Record<string, Expert> = {
  topPerformer: {
    id: 'expert-1',
    name: '<PERSON>',
    bio: 'Former ESPN analyst with 15+ years of sports betting experience. Specializing in NFL and college football with a proven track record.',
    avatar_url: '/avatars/expert1.jpg',
    specializations: ['NFL', 'College Football', 'NBA'],
    verification_status: 'verified',
    overall_rating: 4.9,
    total_subscribers: 3450,
    total_picks: 2150,
    win_rate: 0.72,
    roi: 0.185,
    streak_type: 'win',
    current_streak: 12,
    subscription_tiers: [
      {
        id: 'tier-1-basic',
        name: 'Basic',
        price: 49.99,
        description: 'Get started with weekly NFL picks',
        features: [
          '5-8 NFL picks per week',
          'Email notifications',
          'Basic performance tracking',
          'Access to pick history'
        ]
      },
      {
        id: 'tier-1-premium',
        name: 'Premium',
        price: 149.99,
        description: 'Full access to all sports picks and analysis',
        features: [
          'All sports picks (NFL, NCAAF, NBA)',
          'Real-time notifications',
          'Live chat during games',
          'Detailed pre-game analysis',
          'Exclusive Discord access',
          'Priority support'
        ]
      },
      {
        id: 'tier-1-vip',
        name: 'VIP',
        price: 299.99,
        description: 'Elite access with personal consultations',
        features: [
          'Everything in Premium',
          'Weekly 1-on-1 video calls',
          'Custom betting strategies',
          'Bankroll management advice',
          'Early access to all picks',
          'Personal WhatsApp access'
        ]
      }
    ],
    created_at: '2023-08-15T00:00:00Z',
    updated_at: '2024-01-25T12:00:00Z'
  },
  
  cryptoExpert: {
    id: 'expert-2',
    name: 'Sarah Chen',
    bio: 'Blockchain developer turned crypto trading expert. Specializing in DeFi strategies and altcoin analysis.',
    avatar_url: '/avatars/expert2.jpg',
    specializations: ['Crypto', 'DeFi', 'Trading', 'NFTs'],
    verification_status: 'verified',
    overall_rating: 4.7,
    total_subscribers: 2850,
    total_picks: 890,
    win_rate: 0.69,
    roi: 0.225,
    streak_type: 'win',
    current_streak: 8,
    subscription_tiers: [
      {
        id: 'tier-2-starter',
        name: 'Starter',
        price: 79.99,
        description: 'Essential crypto trading signals',
        features: [
          'Daily trading signals',
          'Market analysis reports',
          'Risk management tips',
          'Email alerts'
        ]
      },
      {
        id: 'tier-2-pro',
        name: 'Professional',
        price: 249.99,
        description: 'Advanced trading with real-time support',
        features: [
          'All Starter features',
          'Real-time trade alerts',
          'Technical analysis training',
          'Private Telegram group',
          'Weekly market webinars',
          'DeFi yield strategies'
        ]
      }
    ],
    created_at: '2023-11-01T00:00:00Z',
    updated_at: '2024-01-24T08:00:00Z'
  },
  
  newExpert: {
    id: 'expert-3',
    name: 'David Martinez',
    bio: 'Rising star in sports analytics. Building a following with consistent NBA and MLB picks.',
    avatar_url: '/avatars/expert3.jpg',
    specializations: ['NBA', 'MLB', 'Analytics'],
    verification_status: 'verified',
    overall_rating: 4.3,
    total_subscribers: 450,
    total_picks: 180,
    win_rate: 0.64,
    roi: 0.098,
    streak_type: 'loss',
    current_streak: 2,
    subscription_tiers: [
      {
        id: 'tier-3-basic',
        name: 'Starter Pack',
        price: 29.99,
        description: 'Affordable entry to premium picks',
        features: [
          '3-5 picks per week',
          'Focus on NBA games',
          'Email notifications',
          'Monthly performance reports'
        ]
      }
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-25T15:00:00Z'
  },
  
  strugglingExpert: {
    id: 'expert-4',
    name: 'Robert Wilson',
    bio: 'Experienced handicapper working to improve recent performance.',
    specializations: ['NFL', 'Soccer'],
    verification_status: 'verified',
    overall_rating: 3.8,
    total_subscribers: 120,
    total_picks: 450,
    win_rate: 0.48,
    roi: -0.05,
    streak_type: 'loss',
    current_streak: 5,
    subscription_tiers: [
      {
        id: 'tier-4-basic',
        name: 'Basic',
        price: 19.99,
        description: 'Budget-friendly picks',
        features: ['Weekly picks', 'Email support']
      }
    ],
    created_at: '2023-06-01T00:00:00Z',
    updated_at: '2024-01-25T10:00:00Z'
  }
};

// Sample picks with various statuses and types
export const mockPicks: Pick[] = [
  // Pending picks
  {
    id: 'pick-1',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    expert_avatar: '/avatars/expert1.jpg',
    title: 'Chiefs -3.5 vs Raiders',
    description: 'Chiefs coming off a bye week at home. Raiders have lost 5 straight on the road. Take the Chiefs to cover.',
    category: 'NFL',
    subcategory: 'Spread',
    confidence_level: 88,
    pick_type: 'spread',
    odds: -110,
    stake_recommendation: 4,
    status: 'pending',
    created_at: '2024-01-25T14:00:00Z',
    event_date: '2024-01-28T13:00:00Z',
    tier_required: 'tier-1-basic'
  },
  {
    id: 'pick-2',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    title: 'Lakers/Celtics Over 228.5',
    description: 'Both teams averaging 120+ in last 5 games. Expect high-scoring affair.',
    category: 'NBA',
    subcategory: 'Totals',
    confidence_level: 75,
    pick_type: 'total',
    odds: -105,
    stake_recommendation: 3,
    status: 'pending',
    created_at: '2024-01-25T12:00:00Z',
    event_date: '2024-01-26T19:30:00Z',
    tier_required: 'tier-1-premium'
  },
  {
    id: 'pick-3',
    expert_id: 'expert-2',
    expert_name: 'Sarah Chen',
    expert_avatar: '/avatars/expert2.jpg',
    title: 'BTC Long Position',
    description: 'Bitcoin showing strong support at 42k. RSI oversold on 4H chart. Target: 45k, Stop: 41k.',
    category: 'Crypto',
    subcategory: 'Spot Trading',
    confidence_level: 82,
    pick_type: 'position',
    status: 'pending',
    created_at: '2024-01-25T10:00:00Z',
    tier_required: 'tier-2-starter'
  },
  
  // Won picks
  {
    id: 'pick-4',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    title: 'Bills ML vs Dolphins',
    description: 'Bills need this win for playoffs. Dolphins struggling in cold weather.',
    category: 'NFL',
    subcategory: 'Moneyline',
    confidence_level: 91,
    pick_type: 'moneyline',
    odds: -150,
    stake_recommendation: 5,
    status: 'won',
    outcome: 'Bills won 31-21. Dominated throughout, never trailed.',
    created_at: '2024-01-20T10:00:00Z',
    event_date: '2024-01-21T13:00:00Z',
    tier_required: 'tier-1-basic'
  },
  {
    id: 'pick-5',
    expert_id: 'expert-2',
    expert_name: 'Sarah Chen',
    title: 'ETH/BTC Ratio Long',
    description: 'Ethereum showing strength vs Bitcoin. Ratio breaking out of consolidation.',
    category: 'Crypto',
    subcategory: 'Pairs Trading',
    confidence_level: 79,
    pick_type: 'position',
    status: 'won',
    outcome: 'Target hit at 0.058. +12% gain in 3 days.',
    created_at: '2024-01-18T08:00:00Z',
    tier_required: 'tier-2-pro'
  },
  
  // Lost picks
  {
    id: 'pick-6',
    expert_id: 'expert-3',
    expert_name: 'David Martinez',
    title: 'Warriors -5.5 vs Nuggets',
    description: 'Warriors at home after rest. Nuggets on back-to-back.',
    category: 'NBA',
    subcategory: 'Spread',
    confidence_level: 72,
    pick_type: 'spread',
    odds: -110,
    stake_recommendation: 3,
    status: 'lost',
    outcome: 'Nuggets won 122-117. Jokic triple-double, Warriors cold from 3.',
    created_at: '2024-01-19T15:00:00Z',
    event_date: '2024-01-19T22:00:00Z',
    tier_required: 'tier-3-basic'
  }
];

// Subscription data
export const mockSubscriptions: Subscription[] = [
  {
    id: 'sub-1',
    user_id: 'user-123',
    expert_id: 'expert-1',
    tier_id: 'tier-1-premium',
    status: 'active',
    current_period_start: '2024-01-01T00:00:00Z',
    current_period_end: '2024-02-01T00:00:00Z',
    trial_end: null,
    price: 149.99,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'sub-2',
    user_id: 'user-123',
    expert_id: 'expert-2',
    tier_id: 'tier-2-starter',
    status: 'active',
    current_period_start: '2024-01-15T00:00:00Z',
    current_period_end: '2024-02-15T00:00:00Z',
    trial_end: '2024-01-22T00:00:00Z',
    price: 79.99,
    created_at: '2024-01-15T00:00:00Z'
  },
  {
    id: 'sub-3',
    user_id: 'user-456',
    expert_id: 'expert-1',
    tier_id: 'tier-1-vip',
    status: 'active',
    current_period_start: '2023-12-01T00:00:00Z',
    current_period_end: '2024-02-01T00:00:00Z',
    price: 299.99,
    created_at: '2023-12-01T00:00:00Z'
  }
];

// Review data
export const mockReviews: Review[] = [
  {
    id: 'review-1',
    user_id: 'user-789',
    user_name: 'John Smith',
    expert_id: 'expert-1',
    rating: 5,
    comment: 'Michael\'s picks have been incredible! Up 45 units in 3 months following his NFL picks. Worth every penny.',
    created_at: '2024-01-20T10:00:00Z'
  },
  {
    id: 'review-2',
    user_id: 'user-101',
    user_name: 'Emily Davis',
    expert_id: 'expert-1',
    rating: 4,
    comment: 'Great analysis and consistent wins. Would love to see more NBA content.',
    created_at: '2024-01-18T14:00:00Z'
  },
  {
    id: 'review-3',
    user_id: 'user-202',
    user_name: 'Alex Thompson',
    expert_id: 'expert-2',
    rating: 5,
    comment: 'Sarah\'s crypto calls are on point. The technical analysis training alone is worth the subscription.',
    created_at: '2024-01-22T09:00:00Z'
  },
  {
    id: 'review-4',
    user_id: 'user-303',
    user_name: 'Mike Brown',
    expert_id: 'expert-3',
    rating: 3,
    comment: 'Decent picks but inconsistent lately. Hope to see improvement.',
    created_at: '2024-01-19T16:00:00Z'
  }
];

// Performance data
export const mockPerformance: Record<string, ExpertPerformance> = {
  'expert-1': {
    total_picks: 2150,
    winning_picks: 1548,
    losing_picks: 602,
    win_rate: 0.72,
    roi: 0.185,
    avg_odds: -108,
    profit_loss: 39775.50,
    best_category: 'NFL',
    worst_category: 'NBA',
    monthly_performance: [
      { month: '2024-01', wins: 52, losses: 20, roi: 0.21 },
      { month: '2023-12', wins: 48, losses: 22, roi: 0.17 },
      { month: '2023-11', wins: 45, losses: 25, roi: 0.14 },
      { month: '2023-10', wins: 50, losses: 20, roi: 0.22 },
      { month: '2023-09', wins: 38, losses: 32, roi: 0.08 }
    ]
  },
  'expert-2': {
    total_picks: 890,
    winning_picks: 614,
    losing_picks: 276,
    win_rate: 0.69,
    roi: 0.225,
    avg_odds: null,
    profit_loss: 20025.00,
    best_category: 'Crypto',
    worst_category: 'NFTs',
    monthly_performance: [
      { month: '2024-01', wins: 28, losses: 12, roi: 0.25 },
      { month: '2023-12', wins: 25, losses: 15, roi: 0.18 },
      { month: '2023-11', wins: 30, losses: 10, roi: 0.32 }
    ]
  }
};

// Analytics data
export const mockAnalytics: Record<string, ExpertAnalytics> = {
  'expert-1': {
    total_revenue: 425750.50,
    monthly_revenue: 35479.20,
    subscriber_growth: 18.5,
    pick_performance: {
      total_picks: 2150,
      successful_picks: 1548,
      win_rate: 0.72,
      roi: 0.185
    },
    top_categories: [
      { category: 'NFL', performance: 0.75 },
      { category: 'College Football', performance: 0.71 },
      { category: 'NBA', performance: 0.68 }
    ]
  },
  'expert-2': {
    total_revenue: 185000.00,
    monthly_revenue: 22500.00,
    subscriber_growth: 25.0,
    pick_performance: {
      total_picks: 890,
      successful_picks: 614,
      win_rate: 0.69,
      roi: 0.225
    },
    top_categories: [
      { category: 'Crypto', performance: 0.72 },
      { category: 'DeFi', performance: 0.68 },
      { category: 'Trading', performance: 0.65 }
    ]
  }
};

// Payment intent mock
export const mockPaymentIntent: PaymentIntent = {
  client_secret: 'pi_test_secret_xxx',
  payment_intent_id: 'pi_test_123',
  amount: 14999,
  currency: 'usd'
};

// User dashboard data
export const mockUserDashboard = {
  subscriptions: mockSubscriptions.filter(s => s.user_id === 'user-123'),
  recent_picks: mockPicks.slice(0, 5),
  performance_summary: {
    total_invested: 10000,
    total_profit: 1850,
    roi: 0.185,
    win_rate: 0.68
  }
};

// Leaderboard data
export const mockLeaderboard = [
  mockExperts.topPerformer,
  mockExperts.cryptoExpert,
  mockExperts.newExpert
].sort((a, b) => b.roi - a.roi);

// Categories
export const mockCategories = [
  { id: 'all', name: 'All Categories', count: 325 },
  { id: 'sports', name: 'Sports', count: 189 },
  { id: 'nfl', name: 'NFL', count: 78 },
  { id: 'nba', name: 'NBA', count: 45 },
  { id: 'mlb', name: 'MLB', count: 32 },
  { id: 'crypto', name: 'Cryptocurrency', count: 68 },
  { id: 'stocks', name: 'Stock Market', count: 42 },
  { id: 'forex', name: 'Forex', count: 26 }
];

// Notification data
export const mockNotifications = [
  {
    id: 'notif-1',
    type: 'pick',
    title: 'New Pick from Michael Johnson',
    message: 'Chiefs -3.5 vs Raiders - 88% confidence',
    timestamp: '2024-01-25T14:00:00Z',
    read: false
  },
  {
    id: 'notif-2',
    type: 'subscription',
    title: 'Subscription Renewal',
    message: 'Your Premium subscription to Michael Johnson renews in 7 days',
    timestamp: '2024-01-25T10:00:00Z',
    read: false
  },
  {
    id: 'notif-3',
    type: 'pick',
    title: 'Pick Result: WON',
    message: 'Bills ML vs Dolphins hit! +$150 profit',
    timestamp: '2024-01-21T16:00:00Z',
    read: true
  }
];

// Helper functions for generating test data
export function generateMockExpert(overrides: Partial<Expert> = {}): Expert {
  const id = `expert-${Date.now()}`;
  return {
    id,
    name: 'Test Expert',
    bio: 'Test bio',
    specializations: ['Test'],
    verification_status: 'verified',
    overall_rating: 4.5,
    total_subscribers: 100,
    total_picks: 50,
    win_rate: 0.65,
    roi: 0.12,
    streak_type: 'win',
    current_streak: 3,
    subscription_tiers: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  };
}

export function generateMockPick(overrides: Partial<Pick> = {}): Pick {
  const id = `pick-${Date.now()}`;
  return {
    id,
    expert_id: 'expert-1',
    expert_name: 'Test Expert',
    title: 'Test Pick',
    description: 'Test description',
    category: 'Test',
    confidence_level: 80,
    pick_type: 'test',
    status: 'pending',
    created_at: new Date().toISOString(),
    ...overrides
  };
}

export function generateMockSubscription(overrides: Partial<Subscription> = {}): Subscription {
  const id = `sub-${Date.now()}`;
  return {
    id,
    user_id: 'user-test',
    expert_id: 'expert-1',
    tier_id: 'tier-1',
    status: 'active',
    current_period_start: new Date().toISOString(),
    current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    price: 49.99,
    created_at: new Date().toISOString(),
    ...overrides
  };
}

// Dummy test to prevent Jest from treating this as a test file
if (process.env.NODE_ENV === 'test') {
  describe('Mock Data Validation', () => {
    it('should provide valid mock data structures', () => {
      expect(mockExperts.topPerformer).toBeDefined();
      expect(mockPicks).toBeInstanceOf(Array);
      expect(mockSubscriptions).toBeInstanceOf(Array);
    });
  });
}