/**
 * Expert Analyst API Client Unit Tests
 * ====================================
 * 
 * Comprehensive tests for the Expert Analyst API client.
 * Tests all API methods, error handling, and authentication.
 */

import axios from 'axios';
import type {
  Expert,
  Pick,
  Subscription,
  Review,
  ExpertPerformance,
  PaymentIntent
} from '@/lib/expert-analyst-api';

// Mock axios before importing the module
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Set up axios mock before import
const mockAxiosInstance = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  interceptors: {
    request: {
      use: jest.fn().mockImplementation((fn) => {
        mockAxiosInstance._requestInterceptor = fn;
      })
    },
    response: {
      use: jest.fn().mockImplementation((successFn, errorFn) => {
        mockAxiosInstance._responseSuccessInterceptor = successFn;
        mockAxiosInstance._responseErrorInterceptor = errorFn;
      })
    }
  },
  _requestInterceptor: null,
  _responseSuccessInterceptor: null,
  _responseErrorInterceptor: null
};

mockedAxios.create.mockReturnValue(mockAxiosInstance);

// Now import the module
import { expertAnalystApi, ExpertAnalystApiClient } from '@/lib/expert-analyst-api';

describe('ExpertAnalystApiClient', () => {
  let apiClient: ExpertAnalystApiClient;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Reset mock axios instance methods
    mockAxiosInstance.get.mockReset();
    mockAxiosInstance.post.mockReset();
    mockAxiosInstance.put.mockReset();
    mockAxiosInstance.delete.mockReset();
    
    // Create new instance
    apiClient = new ExpertAnalystApiClient();
  });

  describe('Configuration', () => {
    it('should create axios instance with correct config', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8003',
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });

    it('should setup request interceptor for auth', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      
      const interceptor = mockAxiosInstance._requestInterceptor;
      const config = { headers: {} };
      
      // Test without token
      const result1 = interceptor(config);
      expect(result1.headers.Authorization).toBeUndefined();
      
      // Test with token
      apiClient.setAuthToken('test-token');
      const result2 = interceptor(config);
      expect(result2.headers.Authorization).toBe('Bearer test-token');
    });

    it('should setup response interceptor for error handling', () => {
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
      
      const successHandler = mockAxiosInstance._responseSuccessInterceptor;
      const errorHandler = mockAxiosInstance._responseErrorInterceptor;
      
      // Test success handler
      const response = { data: 'test' };
      expect(successHandler(response)).toBe(response);
      
      // Test error handler
      const error = {
        response: {
          data: { message: 'API Error' },
          status: 400
        },
        config: {
          url: '/api/test',
          method: 'get'
        }
      };
      
      return expect(errorHandler(error)).rejects.toEqual(error);
    });
  });

  describe('Health Check', () => {
    it('should check API health', async () => {
      const mockResponse = { status: 'healthy', version: '1.0.0' };
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });
      
      const result = await apiClient.health();
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Expert Management', () => {
    const mockExpert: Expert = {
      id: '1',
      name: 'Test Expert',
      bio: 'Test bio',
      specializations: ['NFL'],
      verification_status: 'verified',
      overall_rating: 4.5,
      total_subscribers: 100,
      total_picks: 50,
      win_rate: 0.65,
      roi: 0.12,
      streak_type: 'win',
      current_streak: 5,
      subscription_tiers: [],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    it('should get experts list with filters', async () => {
      const mockResponse = {
        experts: [mockExpert],
        total_count: 1,
        page: 1,
        limit: 20,
        has_next: false,
        has_prev: false
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });
      
      const params = {
        page: 1,
        limit: 20,
        category: 'sports',
        sort_by: 'rating' as const,
        min_rating: 4.0,
        verified_only: true
      };
      
      const result = await apiClient.getExperts(params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/experts', { params });
      expect(result).toEqual(mockResponse);
    });

    it('should get single expert by ID', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: mockExpert });
      
      const result = await apiClient.getExpert('1');
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/experts/1');
      expect(result).toEqual(mockExpert);
    });

    it('should get expert performance stats', async () => {
      const mockPerformance: ExpertPerformance = {
        total_picks: 100,
        winning_picks: 65,
        losing_picks: 35,
        win_rate: 0.65,
        roi: 0.12,
        avg_odds: 1.95,
        profit_loss: 1200,
        best_category: 'NFL',
        worst_category: 'NBA',
        monthly_performance: []
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockPerformance });
      
      const params = {
        date_from: '2024-01-01',
        date_to: '2024-01-31'
      };
      
      const result = await apiClient.getExpertPerformance('1', params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/analytics/experts/1/stats',
        { params }
      );
      expect(result).toEqual(mockPerformance);
    });

    it('should create new expert profile', async () => {
      const expertData = {
        name: 'New Expert',
        bio: 'Expert bio',
        specializations: ['NFL', 'NBA'],
        subscription_tiers: [
          {
            name: 'Basic',
            price: 29.99,
            description: 'Basic tier',
            features: ['Weekly picks']
          }
        ]
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockExpert });
      
      const result = await apiClient.createExpert(expertData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/v1/experts', expertData);
      expect(result).toEqual(mockExpert);
    });

    it('should update expert profile', async () => {
      const updateData = { bio: 'Updated bio' };
      mockAxiosInstance.put.mockResolvedValue({ data: { ...mockExpert, ...updateData } });
      
      const result = await apiClient.updateExpert('1', updateData);
      
      expect(mockAxiosInstance.put).toHaveBeenCalledWith('/api/v1/experts/1', updateData);
      expect(result.bio).toBe('Updated bio');
    });
  });

  describe('Subscription Management', () => {
    const mockSubscription: Subscription = {
      id: 'sub-1',
      user_id: 'user-1',
      expert_id: 'expert-1',
      tier_id: 'tier-1',
      status: 'active',
      current_period_start: '2024-01-01T00:00:00Z',
      current_period_end: '2024-02-01T00:00:00Z',
      price: 29.99,
      created_at: '2024-01-01T00:00:00Z'
    };

    it('should get subscription tiers', async () => {
      const mockTiers = [
        { id: 'tier-1', name: 'Basic', price: 29.99, description: 'Basic tier', features: [] }
      ];
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockTiers });
      
      const result = await apiClient.getSubscriptionTiers('expert-1');
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/subscription-tiers', {
        params: { expert_id: 'expert-1' }
      });
      expect(result).toEqual(mockTiers);
    });

    it('should get user subscriptions', async () => {
      const mockResponse = {
        subscriptions: [mockSubscription],
        total_count: 1
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });
      
      const result = await apiClient.getUserSubscriptions();
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/subscriptions');
      expect(result).toEqual(mockResponse);
    });

    it('should create new subscription', async () => {
      const subscriptionData = {
        expert_id: 'expert-1',
        tier_id: 'tier-1',
        payment_method_id: 'pm_test'
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockSubscription });
      
      const result = await apiClient.createSubscription(subscriptionData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/subscriptions',
        subscriptionData
      );
      expect(result).toEqual(mockSubscription);
    });

    it('should cancel subscription', async () => {
      mockAxiosInstance.delete.mockResolvedValue({ data: {} });
      
      await apiClient.cancelSubscription('sub-1', 'Too expensive');
      
      expect(mockAxiosInstance.delete).toHaveBeenCalledWith(
        '/api/v1/subscriptions/sub-1',
        { data: { reason: 'Too expensive' } }
      );
    });

    it('should update subscription tier', async () => {
      const updateData = { tier_id: 'tier-2' };
      const updatedSubscription = { ...mockSubscription, tier_id: 'tier-2' };
      
      mockAxiosInstance.put.mockResolvedValue({ data: updatedSubscription });
      
      const result = await apiClient.updateSubscription('sub-1', updateData);
      
      expect(mockAxiosInstance.put).toHaveBeenCalledWith(
        '/api/v1/subscriptions/sub-1',
        updateData
      );
      expect(result).toEqual(updatedSubscription);
    });
  });

  describe('Pick Management', () => {
    const mockPick: Pick = {
      id: 'pick-1',
      expert_id: 'expert-1',
      expert_name: 'Test Expert',
      title: 'Test Pick',
      description: 'Pick description',
      category: 'NFL',
      confidence_level: 85,
      pick_type: 'spread',
      status: 'pending',
      created_at: '2024-01-01T00:00:00Z'
    };

    it('should get picks with filters', async () => {
      const mockResponse = {
        picks: [mockPick],
        total_count: 1,
        page: 1,
        limit: 20,
        has_next: false,
        has_prev: false
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });
      
      const params = {
        page: 1,
        limit: 20,
        expert_id: 'expert-1',
        category: 'NFL',
        status: 'pending' as const,
        confidence_min: 80,
        subscribed_only: true
      };
      
      const result = await apiClient.getPicks(params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/picks', { params });
      expect(result).toEqual(mockResponse);
    });

    it('should get single pick', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: mockPick });
      
      const result = await apiClient.getPick('pick-1');
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/picks/pick-1');
      expect(result).toEqual(mockPick);
    });

    it('should create new pick', async () => {
      const pickData = {
        title: 'New Pick',
        description: 'Description',
        category: 'NFL',
        confidence_level: 90,
        pick_type: 'moneyline',
        odds: -150,
        stake_recommendation: 3
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockPick });
      
      const result = await apiClient.createPick(pickData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/v1/picks', pickData);
      expect(result).toEqual(mockPick);
    });

    it('should update pick outcome', async () => {
      const outcomeData = {
        status: 'won' as const,
        outcome: 'Team won 24-17'
      };
      
      const updatedPick = { ...mockPick, ...outcomeData };
      mockAxiosInstance.post.mockResolvedValue({ data: updatedPick });
      
      const result = await apiClient.updatePickOutcome('pick-1', outcomeData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/analytics/picks/pick-1/outcome',
        outcomeData
      );
      expect(result).toEqual(updatedPick);
    });
  });

  describe('Analytics', () => {
    it('should get leaderboards', async () => {
      const mockLeaderboard = [mockExpert];
      mockAxiosInstance.get.mockResolvedValue({ data: mockLeaderboard });
      
      const params = {
        category: 'NFL',
        time_period: 'month' as const,
        limit: 10
      };
      
      const result = await apiClient.getLeaderboards(params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/analytics/leaderboards',
        { params }
      );
      expect(result).toEqual(mockLeaderboard);
    });

    it('should get user dashboard', async () => {
      const mockDashboard = {
        subscriptions: [],
        recent_picks: [],
        performance_summary: {
          total_invested: 1000,
          total_profit: 150,
          roi: 0.15,
          win_rate: 0.65
        }
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockDashboard });
      
      const result = await apiClient.getUserDashboard();
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/analytics/dashboard');
      expect(result).toEqual(mockDashboard);
    });

    it('should get expert analytics', async () => {
      const mockAnalytics = {
        total_revenue: 50000,
        monthly_revenue: 5000,
        subscriber_growth: 15,
        pick_performance: {
          total_picks: 100,
          successful_picks: 65,
          win_rate: 0.65,
          roi: 0.15
        },
        top_categories: []
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockAnalytics });
      
      const result = await apiClient.getExpertAnalytics('expert-1');
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/revenue/earnings/expert-1');
      expect(result).toEqual(mockAnalytics);
    });
  });

  describe('Community Features', () => {
    const mockReview: Review = {
      id: 'review-1',
      user_id: 'user-1',
      user_name: 'Test User',
      expert_id: 'expert-1',
      rating: 5,
      comment: 'Great picks!',
      created_at: '2024-01-01T00:00:00Z'
    };

    it('should get expert reviews', async () => {
      const mockResponse = {
        reviews: [mockReview],
        total_count: 1,
        average_rating: 5.0
      };
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });
      
      const params = { page: 1, limit: 10 };
      const result = await apiClient.getExpertReviews('expert-1', params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/community/experts/expert-1/reviews',
        { params }
      );
      expect(result).toEqual(mockResponse);
    });

    it('should create review', async () => {
      const reviewData = {
        expert_id: 'expert-1',
        rating: 5,
        comment: 'Excellent service!'
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockReview });
      
      const result = await apiClient.createReview(reviewData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/community/reviews',
        reviewData
      );
      expect(result).toEqual(mockReview);
    });

    it('should follow expert', async () => {
      mockAxiosInstance.post.mockResolvedValue({ data: {} });
      
      await apiClient.followExpert('expert-1');
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/community/follows',
        { expert_id: 'expert-1' }
      );
    });

    it('should unfollow expert', async () => {
      mockAxiosInstance.delete.mockResolvedValue({ data: {} });
      
      await apiClient.unfollowExpert('expert-1');
      
      expect(mockAxiosInstance.delete).toHaveBeenCalledWith(
        '/api/v1/community/follows/expert-1'
      );
    });

    it('should get notifications', async () => {
      const mockNotifications = [
        { id: '1', type: 'pick', message: 'New pick available' }
      ];
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockNotifications });
      
      const params = { type: 'pick' as const, limit: 20 };
      const result = await apiClient.getNotifications(params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/community/notifications',
        { params }
      );
      expect(result).toEqual(mockNotifications);
    });
  });

  describe('Payment Features', () => {
    it('should create payment intent', async () => {
      const paymentData = {
        expert_id: 'expert-1',
        tier_id: 'tier-1',
        amount: 2999
      };
      
      const mockIntent: PaymentIntent = {
        client_secret: 'pi_test_secret',
        payment_intent_id: 'pi_test',
        amount: 2999,
        currency: 'usd'
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockIntent });
      
      const result = await apiClient.createPaymentIntent(paymentData);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/payments/create-intent',
        paymentData
      );
      expect(result).toEqual(mockIntent);
    });

    it('should confirm payment', async () => {
      const mockConfirmation = {
        subscription: mockSubscription,
        status: 'succeeded'
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockConfirmation });
      
      const result = await apiClient.confirmPayment('pi_test');
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/payments/confirm/pi_test'
      );
      expect(result).toEqual(mockConfirmation);
    });

    it('should get payment history', async () => {
      const mockHistory = [
        { id: 'pay-1', amount: 2999, date: '2024-01-01' }
      ];
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockHistory });
      
      const params = { page: 1, limit: 20 };
      const result = await apiClient.getPaymentHistory(params);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/revenue/history',
        { params }
      );
      expect(result).toEqual(mockHistory);
    });

    it('should request payout', async () => {
      const mockPayout = {
        payout_id: 'po_test',
        amount: 5000,
        status: 'pending'
      };
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockPayout });
      
      const result = await apiClient.requestPayout(5000);
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/revenue/payout',
        { amount: 5000 }
      );
      expect(result).toEqual(mockPayout);
    });
  });

  describe('Search and Categories', () => {
    it('should search experts', async () => {
      const mockResults = [mockExpert];
      mockAxiosInstance.get.mockResolvedValue({ data: mockResults });
      
      const filters = {
        category: 'sports',
        min_rating: 4.0,
        price_range: [10, 100] as [number, number]
      };
      
      const result = await apiClient.searchExperts('NFL expert', filters);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/api/v1/experts/search',
        { params: { q: 'NFL expert', ...filters } }
      );
      expect(result).toEqual(mockResults);
    });

    it('should get categories', async () => {
      const mockCategories = [
        { id: 'sports', name: 'Sports', count: 100 }
      ];
      
      mockAxiosInstance.get.mockResolvedValue({ data: mockCategories });
      
      const result = await apiClient.getCategories();
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/categories');
      expect(result).toEqual(mockCategories);
    });
  });

  describe('WebSocket Helper', () => {
    it('should generate WebSocket URL for HTTP', () => {
      const url = apiClient.getWebSocketUrl('/test');
      expect(url).toBe('ws://localhost:8003/ws/test');
    });

    it('should generate WebSocket URL for HTTPS', () => {
      // Create client with HTTPS URL
      process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL = 'https://api.example.com';
      const httpsClient = new ExpertAnalystApiClient();
      
      const url = httpsClient.getWebSocketUrl('/test');
      expect(url).toBe('wss://api.example.com/ws/test');
      
      // Reset env
      delete process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL;
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      mockAxiosInstance.get.mockRejectedValue(networkError);
      
      await expect(apiClient.health()).rejects.toThrow('Network Error');
    });

    it('should handle API errors with response', async () => {
      const apiError = {
        response: {
          status: 400,
          data: { message: 'Bad Request' }
        }
      };
      
      mockAxiosInstance.get.mockRejectedValue(apiError);
      
      await expect(apiClient.health()).rejects.toMatchObject(apiError);
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('timeout of 15000ms exceeded');
      mockAxiosInstance.get.mockRejectedValue(timeoutError);
      
      await expect(apiClient.health()).rejects.toThrow('timeout');
    });
  });

  describe('Development Mode Features', () => {
    const originalEnv = process.env.NODE_ENV;
    
    beforeEach(() => {
      process.env.NODE_ENV = 'development';
      global.window = {} as any;
      global.window.dispatchEvent = jest.fn();
    });
    
    afterEach(() => {
      process.env.NODE_ENV = originalEnv;
      delete (global as any).window;
    });
    
    it('should emit debug events in development', async () => {
      const apiError = {
        response: {
          status: 400,
          data: { message: 'Bad Request' }
        },
        config: {
          url: '/api/test',
          method: 'get'
        }
      };
      
      mockAxiosInstance.get.mockRejectedValue(apiError);
      
      try {
        await apiClient.health();
      } catch (e) {
        // Expected to throw
      }
      
      expect(global.window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expert-api-error',
          detail: expect.objectContaining({
            endpoint: '/health',
            method: 'GET'
          })
        })
      );
    });
  });

  describe('Additional Coverage Enhancement', () => {
    it('should handle setAuthToken with null value', () => {
      apiClient.setAuthToken('test-token');
      apiClient.setAuthToken(null);
      
      // Test request without token
      const interceptor = mockAxiosInstance._requestInterceptor;
      const config = { headers: {} };
      const result = interceptor(config);
      
      expect(result.headers.Authorization).toBeUndefined();
    });

    it('should handle environment variable configuration', () => {
      const originalEnv = process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL;
      
      process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL = 'https://custom-api.example.com';
      
      // Create new instance with custom env
      const customClient = new ExpertAnalystApiClient();
      
      expect(mockedAxios.create).toHaveBeenLastCalledWith({
        baseURL: 'https://custom-api.example.com',
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Reset env
      process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL = originalEnv;
    });

    it('should handle WebSocket URL generation edge cases', () => {
      // Test with existing HTTPS configuration
      process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL = 'https://api.example.com:8080';
      const httpsClient = new ExpertAnalystApiClient();
      
      const wsUrl = httpsClient.getWebSocketUrl('/notifications');
      expect(wsUrl).toBe('wss://api.example.com:8080/ws/notifications');
      
      // Test with HTTP and port
      process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL = 'http://localhost:3001';
      const httpClient = new ExpertAnalystApiClient();
      
      const httpWsUrl = httpClient.getWebSocketUrl('/live-updates');
      expect(httpWsUrl).toBe('ws://localhost:3001/ws/live-updates');
      
      // Clean up
      delete process.env.NEXT_PUBLIC_EXPERT_API_BASE_URL;
    });

    it('should handle response interceptor success path', () => {
      const successHandler = mockAxiosInstance._responseSuccessInterceptor;
      const response = { data: 'test', status: 200 };
      
      const result = successHandler(response);
      expect(result).toBe(response);
    });

    it('should handle error without response object', async () => {
      const errorHandler = mockAxiosInstance._responseErrorInterceptor;
      const networkError = new Error('Network unavailable');
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      try {
        await errorHandler(networkError);
      } catch (e) {
        expect(e).toBe(networkError);
      }
      
      expect(consoleSpy).toHaveBeenCalledWith('Expert Analyst API Error:', 'Network unavailable');
      consoleSpy.mockRestore();
    });

    it('should handle error without config object', async () => {
      const errorHandler = mockAxiosInstance._responseErrorInterceptor;
      const error = {
        response: { data: 'Error', status: 500 },
        // No config object
      };
      
      try {
        await errorHandler(error);
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('should handle undefined environment in error handler', async () => {
      const originalEnv = process.env.NODE_ENV;
      const originalWindow = global.window;
      
      process.env.NODE_ENV = 'production';
      delete (global as any).window;
      
      const errorHandler = mockAxiosInstance._responseErrorInterceptor;
      const error = {
        response: { data: 'Error', status: 500 },
        config: { url: '/test', method: 'get' }
      };
      
      try {
        await errorHandler(error);
      } catch (e) {
        // Should not throw additional errors in production without window
        expect(e).toBe(error);
      }
      
      process.env.NODE_ENV = originalEnv;
      global.window = originalWindow;
    });

    it('should handle optional parameters in API methods', async () => {
      // Test methods without optional parameters
      mockAxiosInstance.get.mockResolvedValue({ data: [] });
      
      await apiClient.getSubscriptionTiers();
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/subscription-tiers', { params: {} });
      
      await apiClient.getExpertPerformance('expert-1');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/analytics/experts/expert-1/stats', { params: undefined });
      
      await apiClient.getExpertReviews('expert-1');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/community/experts/expert-1/reviews', { params: undefined });
    });

    it('should handle different error response formats', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Error with string response
      const stringError = {
        response: { data: 'Simple error message' },
        config: { url: '/test' }
      };
      
      const errorHandler = mockAxiosInstance._responseErrorInterceptor;
      
      try {
        await errorHandler(stringError);
      } catch (e) {
        expect(e).toBe(stringError);
      }
      
      expect(consoleSpy).toHaveBeenCalledWith('Expert Analyst API Error:', 'Simple error message');
      consoleSpy.mockRestore();
    });

    it('should handle request cancellation', async () => {
      const cancelError = {
        message: 'Request canceled',
        code: 'ECONNABORTED'
      };
      
      mockAxiosInstance.get.mockRejectedValue(cancelError);
      
      await expect(apiClient.health()).rejects.toMatchObject(cancelError);
    });

    it('should handle expert analytics without expertId parameter', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: { total_revenue: 0 }
      });
      
      await apiClient.getExpertAnalytics();
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/revenue/earnings');
    });

    it('should handle all subscription tier edge cases', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: [] });
      
      // With expert ID
      await apiClient.getSubscriptionTiers('expert-123');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/subscription-tiers', {
        params: { expert_id: 'expert-123' }
      });
      
      // Without expert ID
      await apiClient.getSubscriptionTiers();
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/subscription-tiers', {
        params: {}
      });
    });

    it('should handle complex search parameters', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: [] });
      
      await apiClient.searchExperts('test query', {
        category: 'sports',
        min_rating: 4.5,
        price_range: [0, 100]
      });
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/experts/search', {
        params: {
          q: 'test query',
          category: 'sports',
          min_rating: 4.5,
          price_range: [0, 100]
        }
      });
    });
  });

  describe('useExpertAnalystApi Hook Coverage', () => {
    beforeEach(() => {
      jest.doMock('@clerk/nextjs', () => ({
        useAuth: jest.fn()
      }));
    });

    it('should handle token retrieval success', async () => {
      const { useExpertAnalystApi } = await import('@/lib/expert-analyst-api');
      const { useAuth } = await import('@clerk/nextjs');
      
      const mockGetToken = jest.fn().mockResolvedValue('valid-token');
      (useAuth as jest.Mock).mockReturnValue({ getToken: mockGetToken });
      
      const { renderHook } = await import('@testing-library/react');
      
      const { result } = renderHook(() => useExpertAnalystApi());
      
      await new Promise(resolve => setTimeout(resolve, 0)); // Wait for useEffect
      
      expect(mockGetToken).toHaveBeenCalled();
      expect(result.current).toBeDefined();
    });

    it('should handle token retrieval failure', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const { useExpertAnalystApi } = await import('@/lib/expert-analyst-api');
      const { useAuth } = await import('@clerk/nextjs');
      
      const mockGetToken = jest.fn().mockRejectedValue(new Error('Token error'));
      (useAuth as jest.Mock).mockReturnValue({ getToken: mockGetToken });
      
      const { renderHook } = await import('@testing-library/react');
      
      renderHook(() => useExpertAnalystApi());
      
      await new Promise(resolve => setTimeout(resolve, 0)); // Wait for useEffect
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to get auth token:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });
});