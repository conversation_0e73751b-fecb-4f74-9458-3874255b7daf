/**
 * Expert Creator Flow Integration Tests
 * =====================================
 * 
 * Tests the complete flow for experts creating and managing their
 * profiles, publishing picks, and tracking revenue.
 */

import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Expert, Pick, ExpertAnalytics } from '@/lib/expert-analyst-api';

// Components under test
import ExpertPortalPage from '@/app/expert-portal/page';
import ExpertsMarketplace from '@/app/experts/page';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

describe('Expert Creator Flow Integration', () => {
  const mockGetToken = jest.fn().mockResolvedValue('test-token');
  const mockPush = jest.fn();
  
  const mockNewExpert: Expert = {
    id: 'new-expert-id',
    name: '<PERSON> <PERSON>',
    bio: 'Professional sports analyst with 10+ years experience',
    specializations: ['NFL', 'NBA'],
    verification_status: 'pending',
    overall_rating: 0,
    total_subscribers: 0,
    total_picks: 0,
    win_rate: 0,
    roi: 0,
    streak_type: 'win',
    current_streak: 0,
    subscription_tiers: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'test-user-id',
      isSignedIn: true
    });
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
  });

  describe('Expert Application Process', () => {
    it('should complete expert application flow', async () => {
      const user = userEvent.setup();
      
      // Start with no expert profile
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue(new Error('Not found'));
      
      // Render expert portal - should redirect to application
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/experts/apply');
      });
      
      // Mock application page render
      const { rerender } = render(
        <div data-testid="expert-application">
          <h1>Become an Expert Analyst</h1>
          <form>
            <input name="name" placeholder="Your Name" />
            <textarea name="bio" placeholder="Your Bio" />
            <input name="specializations" placeholder="Specializations (comma-separated)" />
            <button type="submit">Apply</button>
          </form>
        </div>
      );
      
      // Fill application form
      await user.type(screen.getByPlaceholderText('Your Name'), 'John Smith');
      await user.type(
        screen.getByPlaceholderText('Your Bio'),
        'Professional sports analyst with 10+ years experience'
      );
      await user.type(screen.getByPlaceholderText('Specializations (comma-separated)'), 'NFL, NBA');
      
      // Mock successful expert creation
      (expertAnalystApi.createExpert as jest.Mock).mockResolvedValue(mockNewExpert);
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockNewExpert);
      
      // Submit application
      const submitButton = screen.getByText('Apply');
      await user.click(submitButton);
      
      // Should redirect to expert portal
      await waitFor(() => {
        expect(expertAnalystApi.createExpert).toHaveBeenCalledWith({
          name: 'John Smith',
          bio: 'Professional sports analyst with 10+ years experience',
          specializations: ['NFL', 'NBA'],
          subscription_tiers: []
        });
        expect(mockPush).toHaveBeenCalledWith('/expert-portal');
      });
      
      // Render expert portal with new expert
      rerender(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Welcome, John Smith!')).toBeInTheDocument();
        expect(screen.getByText('Verification Status: Pending')).toBeInTheDocument();
        expect(screen.getByText('Complete your profile to get verified')).toBeInTheDocument();
      });
    });

    it('should guide expert through profile completion', async () => {
      const user = userEvent.setup();
      
      // Expert with incomplete profile
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockNewExpert);
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 0,
        monthly_revenue: 0,
        subscriber_growth: 0,
        pick_performance: {
          total_picks: 0,
          successful_picks: 0,
          win_rate: 0,
          roi: 0
        },
        top_categories: []
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Profile Completion: 40%')).toBeInTheDocument();
        expect(screen.getByText('Add subscription tiers')).toBeInTheDocument();
        expect(screen.getByText('Upload profile photo')).toBeInTheDocument();
        expect(screen.getByText('Add payment information')).toBeInTheDocument();
      });
      
      // Add subscription tier
      const addTierButton = screen.getByText('Add Subscription Tier');
      await user.click(addTierButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByLabelText('Tier Name')).toBeInTheDocument();
      });
      
      // Fill tier details
      await user.type(screen.getByLabelText('Tier Name'), 'Basic');
      await user.type(screen.getByLabelText('Price'), '49.99');
      await user.type(screen.getByLabelText('Description'), 'Weekly NFL and NBA picks');
      
      // Add features
      const addFeatureButton = screen.getByText('Add Feature');
      await user.click(addFeatureButton);
      await user.type(screen.getByPlaceholderText('Feature description'), '5-10 picks per week');
      
      // Mock tier creation
      const updatedExpert = {
        ...mockNewExpert,
        subscription_tiers: [{
          id: 'tier-1',
          name: 'Basic',
          price: 49.99,
          description: 'Weekly NFL and NBA picks',
          features: ['5-10 picks per week']
        }]
      };
      
      (expertAnalystApi.updateExpert as jest.Mock).mockResolvedValue(updatedExpert);
      
      // Save tier
      const saveTierButton = screen.getByText('Create Tier');
      await user.click(saveTierButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.updateExpert).toHaveBeenCalled();
        expect(screen.getByText('Profile Completion: 70%')).toBeInTheDocument();
      });
    });
  });

  describe('Pick Creation and Publishing', () => {
    beforeEach(() => {
      const verifiedExpert = {
        ...mockNewExpert,
        verification_status: 'verified',
        subscription_tiers: [{
          id: 'tier-1',
          name: 'Basic',
          price: 49.99,
          description: 'Weekly picks',
          features: ['5-10 picks per week']
        }]
      };
      
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(verifiedExpert);
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 0,
        monthly_revenue: 0,
        subscriber_growth: 0,
        pick_performance: {
          total_picks: 0,
          successful_picks: 0,
          win_rate: 0,
          roi: 0
        },
        top_categories: []
      });
      
      (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
        picks: [],
        total_count: 0
      });
    });

    it('should create and publish first pick', async () => {
      const user = userEvent.setup();
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Create Your First Pick')).toBeInTheDocument();
        expect(screen.getByText('Start building your track record')).toBeInTheDocument();
      });
      
      // Click create pick
      const createButton = screen.getByText('Create New Pick');
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Create Pick')).toBeInTheDocument();
      });
      
      // Fill pick details
      await user.type(screen.getByLabelText('Title'), 'Chiefs -3.5 vs Raiders');
      await user.type(
        screen.getByLabelText('Description'),
        'Chiefs coming off a bye week, Raiders struggling on the road. Take the home favorite.'
      );
      await user.selectOptions(screen.getByLabelText('Category'), 'NFL');
      await user.selectOptions(screen.getByLabelText('Pick Type'), 'spread');
      
      // Set confidence level
      const confidenceSlider = screen.getByLabelText('Confidence Level');
      fireEvent.change(confidenceSlider, { target: { value: '85' } });
      
      // Add odds
      await user.type(screen.getByLabelText('Odds'), '-110');
      
      // Set stake recommendation
      const stakeSelect = screen.getByLabelText('Recommended Stake');
      await user.selectOptions(stakeSelect, '3');
      
      // Set event date
      await user.type(screen.getByLabelText('Event Date'), '2024-01-28T13:00');
      
      // Mock pick creation
      const newPick: Pick = {
        id: 'pick-1',
        expert_id: 'new-expert-id',
        expert_name: 'John Smith',
        title: 'Chiefs -3.5 vs Raiders',
        description: 'Chiefs coming off a bye week, Raiders struggling on the road. Take the home favorite.',
        category: 'NFL',
        subcategory: 'Spread',
        confidence_level: 85,
        pick_type: 'spread',
        odds: -110,
        stake_recommendation: 3,
        status: 'pending',
        created_at: new Date().toISOString(),
        event_date: '2024-01-28T13:00:00Z',
        tier_required: 'tier-1'
      };
      
      (expertAnalystApi.createPick as jest.Mock).mockResolvedValue(newPick);
      
      // Publish pick
      const publishButton = screen.getByText('Publish Pick');
      await user.click(publishButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.createPick).toHaveBeenCalledWith({
          title: 'Chiefs -3.5 vs Raiders',
          description: 'Chiefs coming off a bye week, Raiders struggling on the road. Take the home favorite.',
          category: 'NFL',
          pick_type: 'spread',
          confidence_level: 85,
          odds: -110,
          stake_recommendation: 3,
          event_date: '2024-01-28T13:00:00Z',
          tier_required: 'tier-1'
        });
        
        expect(screen.getByText('Pick published successfully!')).toBeInTheDocument();
        expect(screen.getByText('Your subscribers have been notified')).toBeInTheDocument();
      });
      
      // Should show in recent picks
      await waitFor(() => {
        expect(screen.getByText('Chiefs -3.5 vs Raiders')).toBeInTheDocument();
        expect(screen.getByText('85% confidence')).toBeInTheDocument();
        expect(screen.getByText('PENDING')).toBeInTheDocument();
      });
    });

    it('should track pick performance after resolution', async () => {
      const user = userEvent.setup();
      
      // Expert with existing picks
      const existingPicks: Pick[] = [
        {
          id: 'pick-1',
          expert_id: 'new-expert-id',
          expert_name: 'John Smith',
          title: 'Chiefs -3.5',
          description: 'Chiefs cover',
          category: 'NFL',
          confidence_level: 85,
          pick_type: 'spread',
          odds: -110,
          status: 'pending',
          created_at: '2024-01-25T10:00:00Z',
          event_date: '2024-01-26T13:00:00Z'
        }
      ];
      
      (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
        picks: existingPicks,
        total_count: 1
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Chiefs -3.5')).toBeInTheDocument();
        expect(screen.getByText('Set Outcome')).toBeInTheDocument();
      });
      
      // Update pick outcome
      const outcomeButton = screen.getByText('Set Outcome');
      await user.click(outcomeButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Update Pick Outcome')).toBeInTheDocument();
      });
      
      // Mark as won
      const wonButton = screen.getByLabelText('Won');
      await user.click(wonButton);
      
      await user.type(
        screen.getByLabelText('Outcome Details'),
        'Chiefs won 31-17, easily covering the spread'
      );
      
      // Mock outcome update
      const updatedPick = {
        ...existingPicks[0],
        status: 'won' as const,
        outcome: 'Chiefs won 31-17, easily covering the spread'
      };
      
      (expertAnalystApi.updatePickOutcome as jest.Mock).mockResolvedValue(updatedPick);
      
      // Update analytics
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 0,
        monthly_revenue: 0,
        subscriber_growth: 0,
        pick_performance: {
          total_picks: 1,
          successful_picks: 1,
          win_rate: 1.0,
          roi: 0.909 // Based on -110 odds
        },
        top_categories: [{ category: 'NFL', performance: 1.0 }]
      });
      
      const saveButton = screen.getByText('Update Outcome');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.updatePickOutcome).toHaveBeenCalledWith('pick-1', {
          status: 'won',
          outcome: 'Chiefs won 31-17, easily covering the spread'
        });
        
        // Updated stats should show
        expect(screen.getByText('Win Rate: 100%')).toBeInTheDocument();
        expect(screen.getByText('ROI: 90.9%')).toBeInTheDocument();
      });
    });
  });

  describe('Revenue and Subscriber Management', () => {
    beforeEach(() => {
      const establishedExpert = {
        ...mockNewExpert,
        verification_status: 'verified',
        total_subscribers: 150,
        total_picks: 45,
        win_rate: 0.67,
        roi: 0.145,
        subscription_tiers: [{
          id: 'tier-1',
          name: 'Basic',
          price: 49.99,
          description: 'Weekly picks',
          features: ['5-10 picks per week']
        }]
      };
      
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(establishedExpert);
      
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 22495.50,
        monthly_revenue: 7498.50,
        subscriber_growth: 12.5,
        pick_performance: {
          total_picks: 45,
          successful_picks: 30,
          win_rate: 0.67,
          roi: 0.145
        },
        top_categories: [
          { category: 'NFL', performance: 0.71 },
          { category: 'NBA', performance: 0.62 }
        ]
      });
    });

    it('should display revenue dashboard', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Revenue Overview')).toBeInTheDocument();
        expect(screen.getByText('$22,495.50')).toBeInTheDocument(); // Total revenue
        expect(screen.getByText('$7,498.50')).toBeInTheDocument(); // Monthly revenue
        expect(screen.getByText('150 subscribers')).toBeInTheDocument();
        expect(screen.getByText('+12.5% growth')).toBeInTheDocument();
      });
      
      // Revenue breakdown
      expect(screen.getByText('Revenue per Subscriber')).toBeInTheDocument();
      expect(screen.getByText('$49.99')).toBeInTheDocument();
      
      // Performance metrics affecting revenue
      expect(screen.getByText('67% win rate')).toBeInTheDocument();
      expect(screen.getByText('14.5% ROI')).toBeInTheDocument();
    });

    it('should request payout', async () => {
      const user = userEvent.setup();
      
      // Set available balance
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 22495.50,
        monthly_revenue: 7498.50,
        available_balance: 5000,
        subscriber_growth: 12.5,
        pick_performance: {
          total_picks: 45,
          successful_picks: 30,
          win_rate: 0.67,
          roi: 0.145
        },
        top_categories: []
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Available Balance')).toBeInTheDocument();
        expect(screen.getByText('$5,000.00')).toBeInTheDocument();
        expect(screen.getByText('Request Payout')).toBeInTheDocument();
      });
      
      // Request payout
      const payoutButton = screen.getByText('Request Payout');
      await user.click(payoutButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Request Payout')).toBeInTheDocument();
        expect(screen.getByText('Available: $5,000.00')).toBeInTheDocument();
      });
      
      // Enter payout amount
      const amountInput = screen.getByLabelText('Payout Amount');
      await user.type(amountInput, '3000');
      
      // Select payout method
      const methodSelect = screen.getByLabelText('Payout Method');
      await user.selectOptions(methodSelect, 'bank_transfer');
      
      // Mock payout request
      (expertAnalystApi.requestPayout as jest.Mock).mockResolvedValue({
        payout_id: 'po_123',
        amount: 3000,
        status: 'pending',
        estimated_arrival: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      });
      
      const confirmButton = screen.getByText('Confirm Payout');
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.requestPayout).toHaveBeenCalledWith(3000);
        expect(screen.getByText('Payout requested successfully')).toBeInTheDocument();
        expect(screen.getByText('Estimated arrival: 3 business days')).toBeInTheDocument();
      });
    });

    it('should communicate with subscribers', async () => {
      const user = userEvent.setup();
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Subscriber Communication')).toBeInTheDocument();
        expect(screen.getByText('Send Update')).toBeInTheDocument();
      });
      
      // Send update to subscribers
      const sendUpdateButton = screen.getByText('Send Update');
      await user.click(sendUpdateButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Send Subscriber Update')).toBeInTheDocument();
      });
      
      // Compose message
      await user.type(
        screen.getByLabelText('Subject'),
        'Big slate coming this weekend!'
      );
      
      await user.type(
        screen.getByLabelText('Message'),
        'Get ready for a loaded weekend of picks. I have 8 high-confidence plays across NFL and NBA.'
      );
      
      // Mock notification send
      (expertAnalystApi.sendSubscriberUpdate as jest.Mock).mockResolvedValue({
        sent_count: 150,
        status: 'sent'
      });
      
      const sendButton = screen.getByText('Send to All Subscribers');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.sendSubscriberUpdate).toHaveBeenCalledWith({
          subject: 'Big slate coming this weekend!',
          message: 'Get ready for a loaded weekend of picks. I have 8 high-confidence plays across NFL and NBA.',
          recipient_tier: 'all'
        });
        
        expect(screen.getByText('Update sent to 150 subscribers')).toBeInTheDocument();
      });
    });
  });

  describe('Performance Tracking', () => {
    it('should show detailed performance analytics', async () => {
      const analyticsData = {
        total_revenue: 45000,
        monthly_revenue: 8500,
        subscriber_growth: 15,
        pick_performance: {
          total_picks: 250,
          successful_picks: 175,
          win_rate: 0.70,
          roi: 0.18
        },
        top_categories: [
          { category: 'NFL', performance: 0.75 },
          { category: 'NBA', performance: 0.68 },
          { category: 'MLB', performance: 0.65 }
        ],
        subscriber_retention: 0.92,
        average_subscriber_lifetime: 4.5,
        pick_distribution: {
          spread: 120,
          moneyline: 80,
          totals: 50
        }
      };
      
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue(analyticsData);
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        // Performance summary
        expect(screen.getByText('70% Overall Win Rate')).toBeInTheDocument();
        expect(screen.getByText('18% Average ROI')).toBeInTheDocument();
        
        // Category breakdown
        expect(screen.getByText('Best Performing: NFL (75%)')).toBeInTheDocument();
        
        // Subscriber metrics
        expect(screen.getByText('92% Retention Rate')).toBeInTheDocument();
        expect(screen.getByText('4.5 Month Avg Lifetime')).toBeInTheDocument();
        
        // Revenue insights
        expect(screen.getByText('$56.67 per subscriber/month')).toBeInTheDocument();
      });
    });

    it('should provide improvement suggestions', async () => {
      // Expert with room for improvement
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 5000,
        monthly_revenue: 1000,
        subscriber_growth: -5,
        pick_performance: {
          total_picks: 30,
          successful_picks: 15,
          win_rate: 0.50,
          roi: -0.05
        },
        top_categories: [{ category: 'NFL', performance: 0.50 }],
        improvement_suggestions: [
          'Your win rate is below average. Consider being more selective with picks.',
          'Subscriber growth is negative. Engage more with your community.',
          'ROI is negative. Review your staking recommendations.'
        ]
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Areas for Improvement')).toBeInTheDocument();
        expect(screen.getByText(/win rate is below average/)).toBeInTheDocument();
        expect(screen.getByText(/Subscriber growth is negative/)).toBeInTheDocument();
        expect(screen.getByText(/ROI is negative/)).toBeInTheDocument();
      });
    });
  });
});