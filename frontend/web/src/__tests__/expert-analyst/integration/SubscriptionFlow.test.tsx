/**
 * Subscription Flow Integration Tests
 * ===================================
 * 
 * End-to-end integration tests for the critical revenue-generating
 * subscription flow from expert discovery to payment completion.
 */

import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter, usePathname } from 'next/navigation';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Expert, SubscriptionTier, PaymentIntent, Subscription } from '@/lib/expert-analyst-api';

// Mock components (we'll test the flow through multiple pages)
import ExpertsMarketplace from '@/app/experts/page';
import ExpertDetailPage from '@/app/experts/[id]/page';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

// Mock Stripe Elements
jest.mock('@stripe/react-stripe-js', () => ({
  Elements: ({ children }: any) => <div data-testid="stripe-elements">{children}</div>,
  CardElement: () => <div data-testid="card-element">Card Input</div>,
  useStripe: () => ({
    confirmCardPayment: jest.fn().mockResolvedValue({
      paymentIntent: { status: 'succeeded' }
    })
  }),
  useElements: () => ({
    getElement: jest.fn().mockReturnValue({})
  })
}));

describe('Expert Subscription Flow Integration', () => {
  const mockGetToken = jest.fn().mockResolvedValue('test-token');
  const mockPush = jest.fn();
  const mockPathname = jest.fn();
  
  const mockExpert: Expert = {
    id: 'expert-1',
    name: 'Michael Johnson',
    bio: 'NFL expert with proven track record',
    avatar_url: '/avatars/expert1.jpg',
    specializations: ['NFL', 'College Football'],
    verification_status: 'verified',
    overall_rating: 4.8,
    total_subscribers: 2450,
    total_picks: 1250,
    win_rate: 0.68,
    roi: 0.15,
    streak_type: 'win',
    current_streak: 8,
    subscription_tiers: [
      {
        id: 'tier-basic',
        name: 'Basic',
        price: 29.99,
        description: 'Get started with weekly NFL picks',
        features: [
          'Weekly NFL game picks',
          'Email notifications',
          'Basic performance tracking'
        ]
      },
      {
        id: 'tier-premium',
        name: 'Premium',
        price: 99.99,
        description: 'Full access to all sports picks',
        features: [
          'All sports picks',
          'Real-time notifications',
          'Live chat access',
          'Detailed analysis'
        ]
      }
    ],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'test-user-id',
      isSignedIn: true
    });
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    (usePathname as jest.Mock).mockReturnValue('/experts');
    
    // Default API mocks
    (expertAnalystApi.getExperts as jest.Mock).mockResolvedValue({
      experts: [mockExpert],
      total_count: 1
    });
    
    (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExpert);
    
    (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
      subscriptions: [],
      total_count: 0
    });
  });

  describe('Complete Subscription Journey', () => {
    it('should complete full subscription flow from discovery to payment', async () => {
      const user = userEvent.setup();
      
      // Step 1: Expert Discovery
      const { rerender } = render(<ExpertsMarketplace />);
      
      // Wait for experts to load
      await waitFor(() => {
        expect(screen.getByText('Expert Analysts Marketplace')).toBeInTheDocument();
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
      });
      
      // Verify expert card shows key metrics
      const expertCard = screen.getByText('Michael Johnson').closest('[class*="card"]');
      expect(expertCard).toBeInTheDocument();
      within(expertCard!).getByText('68%'); // Win rate
      within(expertCard!).getByText('15%'); // ROI
      within(expertCard!).getByText('$29.99/month'); // Starting price
      
      // Step 2: Click to view expert details
      const expertLink = screen.getByRole('link', { name: /experts\/expert-1/ });
      await user.click(expertLink);
      
      // Simulate navigation to expert detail page
      (usePathname as jest.Mock).mockReturnValue('/experts/expert-1');
      rerender(<ExpertDetailPage />);
      
      // Step 3: Expert Profile Review
      await waitFor(() => {
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
        expect(screen.getByText(/NFL expert with proven track record/)).toBeInTheDocument();
      });
      
      // Verify subscription tiers are displayed
      expect(screen.getByText('Basic')).toBeInTheDocument();
      expect(screen.getByText('$29.99/month')).toBeInTheDocument();
      expect(screen.getByText('Premium')).toBeInTheDocument();
      expect(screen.getByText('$99.99/month')).toBeInTheDocument();
      
      // Step 4: Select Premium Tier
      const premiumTier = screen.getByText('Premium').closest('[data-testid="subscription-tier"]');
      const subscribeButton = within(premiumTier!).getByText('Subscribe');
      
      // Mock payment intent creation
      (expertAnalystApi.createPaymentIntent as jest.Mock).mockResolvedValue({
        client_secret: 'pi_test_secret',
        payment_intent_id: 'pi_test',
        amount: 9999,
        currency: 'usd'
      });
      
      await user.click(subscribeButton);
      
      // Step 5: Payment Modal
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Complete Subscription')).toBeInTheDocument();
        expect(screen.getByText('Premium - $99.99/month')).toBeInTheDocument();
      });
      
      // Verify payment form elements
      expect(screen.getByTestId('card-element')).toBeInTheDocument();
      expect(screen.getByLabelText('Cardholder Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      
      // Fill payment details
      await user.type(screen.getByLabelText('Cardholder Name'), 'Test User');
      await user.type(screen.getByLabelText('Email'), '<EMAIL>');
      
      // Mock successful subscription creation
      const mockSubscription: Subscription = {
        id: 'sub-new',
        user_id: 'test-user-id',
        expert_id: 'expert-1',
        tier_id: 'tier-premium',
        status: 'active',
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        price: 99.99,
        created_at: new Date().toISOString()
      };
      
      (expertAnalystApi.confirmPayment as jest.Mock).mockResolvedValue({
        subscription: mockSubscription,
        status: 'succeeded'
      });
      
      // Submit payment
      const payButton = screen.getByText('Pay $99.99');
      await user.click(payButton);
      
      // Step 6: Success Confirmation
      await waitFor(() => {
        expect(screen.getByText('Subscription Successful!')).toBeInTheDocument();
        expect(screen.getByText(/Welcome to Premium/)).toBeInTheDocument();
      });
      
      // Verify navigation to picks feed
      const viewPicksButton = screen.getByText('View Expert Picks');
      await user.click(viewPicksButton);
      
      expect(mockPush).toHaveBeenCalledWith('/picks');
    });

    it('should handle authentication requirement', async () => {
      const user = userEvent.setup();
      
      // Set up as unauthenticated user
      (useAuth as jest.Mock).mockReturnValue({
        getToken: jest.fn(),
        userId: null,
        isSignedIn: false
      });
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
      });
      
      // Try to subscribe
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(subscribeButton);
      
      // Should redirect to sign-in
      expect(mockPush).toHaveBeenCalledWith('/sign-in?redirect=/experts/expert-1');
    });

    it('should prevent duplicate subscriptions', async () => {
      // User already has active subscription
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [{
          id: 'existing-sub',
          expert_id: 'expert-1',
          tier_id: 'tier-basic',
          status: 'active'
        }],
        total_count: 1
      });
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Active Subscription')).toBeInTheDocument();
        expect(screen.getByText('Basic Plan')).toBeInTheDocument();
      });
      
      // Should show upgrade option instead of subscribe
      expect(screen.getByText('Upgrade to Premium')).toBeInTheDocument();
      expect(screen.queryByText('Subscribe')).not.toBeInTheDocument();
    });

    it('should handle payment failures gracefully', async () => {
      const user = userEvent.setup();
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
      });
      
      // Click subscribe
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(subscribeButton);
      
      // Fill payment form
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
      
      await user.type(screen.getByLabelText('Cardholder Name'), 'Test User');
      await user.type(screen.getByLabelText('Email'), '<EMAIL>');
      
      // Mock payment failure
      const mockStripe = {
        confirmCardPayment: jest.fn().mockResolvedValue({
          error: { message: 'Card declined' }
        })
      };
      
      (require('@stripe/react-stripe-js').useStripe as jest.Mock).mockReturnValue(mockStripe);
      
      const payButton = screen.getByText('Pay $29.99');
      await user.click(payButton);
      
      // Should show error message
      await waitFor(() => {
        expect(screen.getByText('Payment failed: Card declined')).toBeInTheDocument();
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
    });

    it('should track conversion metrics', async () => {
      const user = userEvent.setup();
      
      // Mock analytics tracking
      const trackEvent = jest.fn();
      (window as any).gtag = trackEvent;
      
      render(<ExpertsMarketplace />);
      
      // Track marketplace view
      await waitFor(() => {
        expect(trackEvent).toHaveBeenCalledWith('event', 'page_view', {
          page_title: 'Expert Marketplace',
          page_location: '/experts'
        });
      });
      
      // Click expert
      const expertLink = await screen.findByRole('link', { name: /experts\/expert-1/ });
      await user.click(expertLink);
      
      expect(trackEvent).toHaveBeenCalledWith('event', 'select_item', {
        item_list_name: 'Expert Marketplace',
        items: [{
          item_id: 'expert-1',
          item_name: 'Michael Johnson',
          item_category: 'Expert',
          price: 29.99
        }]
      });
    });
  });

  describe('Subscription Tier Selection', () => {
    it('should highlight value proposition for each tier', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const basicTier = screen.getByText('Basic').closest('[data-testid="subscription-tier"]');
        expect(within(basicTier!).getByText('Weekly NFL game picks')).toBeInTheDocument();
        expect(within(basicTier!).getByText('Email notifications')).toBeInTheDocument();
        
        const premiumTier = screen.getByText('Premium').closest('[data-testid="subscription-tier"]');
        expect(within(premiumTier!).getByText('All sports picks')).toBeInTheDocument();
        expect(within(premiumTier!).getByText('Live chat access')).toBeInTheDocument();
      });
    });

    it('should show savings for higher tiers', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const premiumTier = screen.getByText('Premium').closest('[data-testid="subscription-tier"]');
        expect(within(premiumTier!).getByText('Most Popular')).toBeInTheDocument();
        expect(within(premiumTier!).getByText('Best Value')).toBeInTheDocument();
      });
    });
  });

  describe('Post-Subscription Experience', () => {
    beforeEach(() => {
      // User has active subscription
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [{
          id: 'active-sub',
          expert_id: 'expert-1',
          tier_id: 'tier-premium',
          status: 'active',
          current_period_end: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString()
        }],
        total_count: 1
      });
    });

    it('should show subscription management options', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Active Subscription')).toBeInTheDocument();
        expect(screen.getByText('Premium Plan')).toBeInTheDocument();
        expect(screen.getByText('Manage Subscription')).toBeInTheDocument();
        expect(screen.getByText('Renews in 20 days')).toBeInTheDocument();
      });
    });

    it('should provide quick access to picks', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('View Latest Picks')).toBeInTheDocument();
        expect(screen.getByRole('link', { name: 'View Latest Picks' }))
          .toHaveAttribute('href', '/picks?expert=expert-1');
      });
    });

    it('should enable pick notifications', async () => {
      const user = userEvent.setup();
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByLabelText('Email notifications for new picks')).toBeInTheDocument();
        expect(screen.getByLabelText('Push notifications')).toBeInTheDocument();
      });
      
      // Enable notifications
      const emailToggle = screen.getByLabelText('Email notifications for new picks');
      await user.click(emailToggle);
      
      expect(expertAnalystApi.updateSubscription).toHaveBeenCalledWith(
        'active-sub',
        expect.objectContaining({
          email_notifications: true
        })
      );
    });
  });

  describe('Revenue Optimization', () => {
    it('should show social proof during subscription', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('2,450 active subscribers')).toBeInTheDocument();
        expect(screen.getByText('68% win rate')).toBeInTheDocument();
        expect(screen.getByText('15% average ROI')).toBeInTheDocument();
      });
    });

    it('should display limited-time offers when available', async () => {
      // Mock expert with promotional pricing
      const expertWithPromo = {
        ...mockExpert,
        subscription_tiers: [
          {
            ...mockExpert.subscription_tiers[0],
            promotional_price: 19.99,
            promotion_ends: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
          }
        ]
      };
      
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(expertWithPromo);
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Limited Time: $19.99')).toBeInTheDocument();
        expect(screen.getByText(/Regular price: \$29.99/)).toBeInTheDocument();
        expect(screen.getByText(/Offer ends in/)).toBeInTheDocument();
      });
    });

    it('should track abandoned subscriptions', async () => {
      const user = userEvent.setup();
      const trackEvent = jest.fn();
      (window as any).gtag = trackEvent;
      
      render(<ExpertDetailPage />);
      
      // Start subscription flow
      const subscribeButton = await screen.findByText('Subscribe');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
      
      // Close modal without completing
      const closeButton = screen.getByLabelText('Close');
      await user.click(closeButton);
      
      expect(trackEvent).toHaveBeenCalledWith('event', 'begin_checkout', {
        value: 29.99,
        currency: 'USD',
        items: [{
          item_id: 'tier-basic',
          item_name: 'Basic Subscription',
          item_category: 'Subscription',
          price: 29.99
        }]
      });
      
      expect(trackEvent).toHaveBeenCalledWith('event', 'checkout_abandoned', {
        value: 29.99,
        currency: 'USD'
      });
    });
  });
});