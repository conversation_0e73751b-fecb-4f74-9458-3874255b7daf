/**
 * Expert Marketplace Page Unit Tests
 * ==================================
 * 
 * Comprehensive unit tests for the Expert Discovery & Marketplace page.
 * Tests search, filtering, sorting, and expert card display functionality.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import ExpertsMarketplace from '@/app/experts/page';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Expert, ExpertCategory } from '@/lib/expert-analyst-api';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('@/lib/expert-analyst-api');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));
jest.mock('next/link', () => {
  return React.forwardRef(({ children, href, ...props }: any, ref: any) => {
    return (
      <a href={href} ref={ref} {...props}>
        {children}
      </a>
    );
  });
});

// Mock expert data
const mockExperts: Expert[] = [
  {
    id: '1',
    name: 'Michael <PERSON>',
    bio: 'NFL betting expert with 8+ years experience.',
    avatar_url: '/avatars/expert1.jpg',
    specializations: ['NFL', 'College Football'],
    verification_status: 'verified',
    overall_rating: 4.8,
    total_subscribers: 2450,
    total_picks: 1250,
    win_rate: 0.68,
    roi: 0.15,
    streak_type: 'win',
    current_streak: 8,
    subscription_tiers: [
      { 
        id: '1', 
        name: 'Basic', 
        price: 29.99, 
        description: 'Weekly picks', 
        features: ['Weekly NFL picks', 'Email alerts'] 
      }
    ],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z'
  },
  {
    id: '2',
    name: 'Sarah Chen',
    bio: 'Crypto trading strategist.',
    avatar_url: '/avatars/expert2.jpg',
    specializations: ['Crypto', 'DeFi', 'Trading'],
    verification_status: 'verified',
    overall_rating: 4.9,
    total_subscribers: 3200,
    total_picks: 890,
    win_rate: 0.74,
    roi: 0.22,
    streak_type: 'win',
    current_streak: 12,
    subscription_tiers: [
      { 
        id: '3', 
        name: 'Standard', 
        price: 49.99, 
        description: 'Weekly signals', 
        features: ['Daily crypto signals', 'Discord access'] 
      }
    ],
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-21T00:00:00Z'
  },
  {
    id: '3',
    name: 'David Martinez',
    bio: 'Stock market analyst.',
    avatar_url: '/avatars/expert3.jpg',
    specializations: ['Stocks', 'Options'],
    verification_status: 'verified',
    overall_rating: 4.5,
    total_subscribers: 1800,
    total_picks: 650,
    win_rate: 0.62,
    roi: 0.11,
    streak_type: 'loss',
    current_streak: 2,
    subscription_tiers: [
      { 
        id: '5', 
        name: 'Premium', 
        price: 79.99, 
        description: 'Daily stock picks', 
        features: ['Daily stock picks', 'Live chat support'] 
      }
    ],
    created_at: '2024-01-20T00:00:00Z',
    updated_at: '2024-01-25T00:00:00Z'
  }
];

const mockCategories: ExpertCategory[] = [
  { id: 'all', name: 'All Categories', count: 156 },
  { id: 'sports', name: 'Sports', count: 89 },
  { id: 'crypto', name: 'Cryptocurrency', count: 34 },
  { id: 'stocks', name: 'Stock Market', count: 23 }
];

describe('ExpertsMarketplace', () => {
  const mockGetToken = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken
    });
    
    // Default API mocks
    (expertAnalystApi.getExperts as jest.Mock).mockResolvedValue({
      experts: mockExperts,
      total_count: mockExperts.length,
      page: 1,
      limit: 20,
      has_next: false,
      has_prev: false
    });
    
    (expertAnalystApi.getCategories as jest.Mock).mockResolvedValue(mockCategories);
  });

  describe('Rendering and Layout', () => {
    it('should render the marketplace page with all sections', () => {
      render(<ExpertsMarketplace />);
      
      expect(screen.getByText('Expert Analysts Marketplace')).toBeInTheDocument();
      expect(screen.getByText(/Discover top-performing analysts/)).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search experts, specializations...')).toBeInTheDocument();
      expect(screen.getByText('Sort by')).toBeInTheDocument();
    });

    it('should display expert cards with correct information', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Check first expert card
        const michaelCard = screen.getByText('Michael Johnson').closest('[class*="card"]');
        expect(michaelCard).toBeInTheDocument();
        
        within(michaelCard!).getByText('4.8'); // Rating
        within(michaelCard!).getByText('2,450'); // Subscribers
        within(michaelCard!).getByText('68%'); // Win rate
        within(michaelCard!).getByText('15%'); // ROI
        within(michaelCard!).getByText('8 win streak');
        within(michaelCard!).getByText('$29.99/month');
      });
    });

    it('should show verified badge for verified experts', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        const expertCards = screen.getAllByRole('link', { name: /experts\// });
        expect(expertCards.length).toBeGreaterThan(0);
        
        // All mock experts are verified, so all should have badges
        expertCards.forEach(card => {
          const badge = within(card).getByRole('img', { hidden: true });
          expect(badge).toBeInTheDocument();
        });
      });
    });

    it('should display specialization badges', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByText('NFL')).toBeInTheDocument();
        expect(screen.getByText('Crypto')).toBeInTheDocument();
        expect(screen.getByText('Stocks')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    it('should filter experts by name search', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      const searchInput = screen.getByPlaceholderText('Search experts, specializations...');
      
      await user.type(searchInput, 'Sarah');
      
      await waitFor(() => {
        expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
        expect(screen.queryByText('Michael Johnson')).not.toBeInTheDocument();
        expect(screen.queryByText('David Martinez')).not.toBeInTheDocument();
      });
    });

    it('should filter experts by specialization search', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      const searchInput = screen.getByPlaceholderText('Search experts, specializations...');
      
      await user.type(searchInput, 'crypto');
      
      await waitFor(() => {
        expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
        expect(screen.queryByText('Michael Johnson')).not.toBeInTheDocument();
      });
    });

    it('should show no results message when search yields no matches', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      const searchInput = screen.getByPlaceholderText('Search experts, specializations...');
      
      await user.type(searchInput, 'nonexistent');
      
      await waitFor(() => {
        expect(screen.getByText('No experts found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search or filter criteria.')).toBeInTheDocument();
      });
    });
  });

  describe('Category Filtering', () => {
    it('should filter experts by category selection', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      // Click on Sports category tab
      const sportsTab = screen.getByRole('tab', { name: 'Sports' });
      await user.click(sportsTab);
      
      await waitFor(() => {
        expect(expertAnalystApi.getExperts).toHaveBeenCalledWith(
          expect.objectContaining({
            category: 'sports'
          })
        );
      });
    });

    it('should update category count display', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByText('Sports (89)')).toBeInTheDocument();
        expect(screen.getByText('Cryptocurrency (34)')).toBeInTheDocument();
        expect(screen.getByText('Stock Market (23)')).toBeInTheDocument();
      });
    });
  });

  describe('Sorting Functionality', () => {
    it('should sort experts by different criteria', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      // Open sort dropdown
      const sortTrigger = screen.getByText('Sort by');
      await user.click(sortTrigger);
      
      // Select ROI sort
      const roiOption = screen.getByText('Best ROI');
      await user.click(roiOption);
      
      await waitFor(() => {
        expect(expertAnalystApi.getExperts).toHaveBeenCalledWith(
          expect.objectContaining({
            sort_by: 'roi'
          })
        );
      });
    });

    it('should support all sort options', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      const sortOptions = ['rating', 'roi', 'subscribers', 'win_rate', 'recent'];
      
      for (const option of sortOptions) {
        const sortTrigger = screen.getByText('Sort by');
        await user.click(sortTrigger);
        
        const optionText = option === 'rating' ? 'Highest Rated' :
                          option === 'roi' ? 'Best ROI' :
                          option === 'subscribers' ? 'Most Popular' :
                          option === 'win_rate' ? 'Win Rate' :
                          'Recently Joined';
        
        const sortOption = screen.getByText(optionText);
        await user.click(sortOption);
        
        await waitFor(() => {
          expect(expertAnalystApi.getExperts).toHaveBeenCalledWith(
            expect.objectContaining({
              sort_by: option
            })
          );
        });
      }
    });
  });

  describe('Performance Indicators', () => {
    it('should color-code win rate based on performance', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Sarah Chen with 74% win rate should be green
        const sarahCard = screen.getByText('Sarah Chen').closest('[class*="card"]');
        const sarahWinRate = within(sarahCard!).getByText('74%');
        expect(sarahWinRate).toHaveClass('text-green-600');
        
        // Michael Johnson with 68% win rate should be yellow
        const michaelCard = screen.getByText('Michael Johnson').closest('[class*="card"]');
        const michaelWinRate = within(michaelCard!).getByText('68%');
        expect(michaelWinRate).toHaveClass('text-yellow-600');
        
        // David Martinez with 62% win rate should be yellow
        const davidCard = screen.getByText('David Martinez').closest('[class*="card"]');
        const davidWinRate = within(davidCard!).getByText('62%');
        expect(davidWinRate).toHaveClass('text-yellow-600');
      });
    });

    it('should color-code ROI based on performance', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Sarah Chen with 22% ROI should be green
        const sarahCard = screen.getByText('Sarah Chen').closest('[class*="card"]');
        const sarahROI = within(sarahCard!).getByText('22%');
        expect(sarahROI).toHaveClass('text-green-600');
        
        // Michael Johnson with 15% ROI should be green
        const michaelCard = screen.getByText('Michael Johnson').closest('[class*="card"]');
        const michaelROI = within(michaelCard!).getByText('15%');
        expect(michaelROI).toHaveClass('text-green-600');
      });
    });

    it('should display streak indicators correctly', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Win streaks
        expect(screen.getByText('8 win streak')).toBeInTheDocument();
        expect(screen.getByText('12 win streak')).toBeInTheDocument();
        
        // Loss streak
        expect(screen.getByText('2 loss streak')).toBeInTheDocument();
      });
    });
  });

  describe('Call to Action', () => {
    it('should display expert application CTA', () => {
      render(<ExpertsMarketplace />);
      
      expect(screen.getByText('Want to Become an Expert?')).toBeInTheDocument();
      expect(screen.getByText(/Share your expertise and earn from your knowledge/)).toBeInTheDocument();
      
      const applyButton = screen.getByRole('link', { name: /Apply to Become an Expert/ });
      expect(applyButton).toHaveAttribute('href', '/expert-portal');
    });
  });

  describe('Loading States', () => {
    it('should show loading skeletons while fetching experts', async () => {
      (expertAnalystApi.getExperts as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );
      
      render(<ExpertsMarketplace />);
      
      // Check for loading skeletons
      const skeletons = screen.getAllByRole('article');
      expect(skeletons.length).toBeGreaterThanOrEqual(6);
      
      skeletons.forEach(skeleton => {
        expect(skeleton).toHaveClass('animate-pulse');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      (expertAnalystApi.getExperts as jest.Mock).mockRejectedValue(new Error('API Error'));
      
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to load experts:', expect.any(Error));
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Navigation', () => {
    it('should link to individual expert profiles', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        const expertLinks = screen.getAllByRole('link', { name: /experts\// });
        
        expect(expertLinks[0]).toHaveAttribute('href', '/experts/1');
        expect(expertLinks[1]).toHaveAttribute('href', '/experts/2');
        expect(expertLinks[2]).toHaveAttribute('href', '/experts/3');
      });
    });
  });

  describe('Responsive Design', () => {
    it('should render grid layout with appropriate columns', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        const grid = screen.getByText('Michael Johnson').closest('.grid');
        expect(grid).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
      });
    });
  });
});