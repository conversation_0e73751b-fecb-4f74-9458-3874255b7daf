/**
 * Picks Feed Page Unit Tests
 * ==========================
 * 
 * Tests for the picks feed where subscribed users access expert picks,
 * track performance, and manage their pick consumption.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import PicksFeedPage from '@/app/picks/page';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Pick, Subscription, Expert } from '@/lib/expert-analyst-api';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

// Mock data
const mockSubscriptions: Subscription[] = [
  {
    id: 'sub-1',
    user_id: 'user-1',
    expert_id: 'expert-1',
    tier_id: 'tier-premium',
    status: 'active',
    current_period_start: '2024-01-01T00:00:00Z',
    current_period_end: '2024-02-01T00:00:00Z',
    price: 99.99,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'sub-2',
    user_id: 'user-1',
    expert_id: 'expert-2',
    tier_id: 'tier-basic',
    status: 'active',
    current_period_start: '2024-01-05T00:00:00Z',
    current_period_end: '2024-02-05T00:00:00Z',
    price: 29.99,
    created_at: '2024-01-05T00:00:00Z'
  }
];

const mockPicks: Pick[] = [
  {
    id: 'pick-1',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    expert_avatar: '/avatars/expert1.jpg',
    title: 'Chiefs -3.5 vs Raiders',
    description: 'Chiefs dominate at home after extra rest. Take the spread.',
    category: 'NFL',
    subcategory: 'Spread',
    confidence_level: 85,
    pick_type: 'spread',
    odds: -110,
    stake_recommendation: 3,
    status: 'pending',
    created_at: '2024-01-25T10:00:00Z',
    event_date: '2024-01-26T20:00:00Z',
    tier_required: 'tier-basic'
  },
  {
    id: 'pick-2',
    expert_id: 'expert-2',
    expert_name: 'Sarah Chen',
    expert_avatar: '/avatars/expert2.jpg',
    title: 'BTC Long Position',
    description: 'Bitcoin showing bullish signals. Entry at 42k, target 45k.',
    category: 'Crypto',
    subcategory: 'Trading',
    confidence_level: 78,
    pick_type: 'position',
    status: 'pending',
    created_at: '2024-01-25T09:00:00Z',
    tier_required: 'tier-basic'
  },
  {
    id: 'pick-3',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    title: 'Lakers ML vs Celtics',
    description: 'Lakers at home with LeBron back. Take the moneyline.',
    category: 'NBA',
    subcategory: 'Moneyline',
    confidence_level: 92,
    pick_type: 'moneyline',
    odds: +120,
    stake_recommendation: 5,
    status: 'won',
    outcome: 'Lakers won 117-113',
    created_at: '2024-01-24T14:00:00Z',
    event_date: '2024-01-24T22:00:00Z',
    tier_required: 'tier-premium'
  }
];

const mockExperts: Record<string, Expert> = {
  'expert-1': {
    id: 'expert-1',
    name: 'Michael Johnson',
    specializations: ['NFL', 'NBA'],
    verification_status: 'verified',
    overall_rating: 4.8,
    total_subscribers: 2450,
    total_picks: 1250,
    win_rate: 0.68,
    roi: 0.15,
    streak_type: 'win',
    current_streak: 8,
    subscription_tiers: [],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z'
  },
  'expert-2': {
    id: 'expert-2',
    name: 'Sarah Chen',
    specializations: ['Crypto', 'Trading'],
    verification_status: 'verified',
    overall_rating: 4.9,
    total_subscribers: 3200,
    total_picks: 890,
    win_rate: 0.74,
    roi: 0.22,
    streak_type: 'win',
    current_streak: 12,
    subscription_tiers: [],
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-21T00:00:00Z'
  }
};

describe('PicksFeedPage', () => {
  const mockGetToken = jest.fn();
  const mockPush = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'user-1',
      isSignedIn: true
    });
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    // API mocks
    (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
      subscriptions: mockSubscriptions,
      total_count: mockSubscriptions.length
    });
    
    (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
      picks: mockPicks,
      total_count: mockPicks.length,
      page: 1,
      limit: 20,
      has_next: false,
      has_prev: false
    });
    
    (expertAnalystApi.getExpert as jest.Mock).mockImplementation((id) => 
      Promise.resolve(mockExperts[id])
    );
  });

  describe('Feed Display', () => {
    it('should display picks feed header', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Picks Feed')).toBeInTheDocument();
        expect(screen.getByText(/Access picks from your subscribed experts/)).toBeInTheDocument();
      });
    });

    it('should show all subscribed expert picks', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Chiefs -3.5 vs Raiders')).toBeInTheDocument();
        expect(screen.getByText('BTC Long Position')).toBeInTheDocument();
        expect(screen.getByText('Lakers ML vs Celtics')).toBeInTheDocument();
      });
    });

    it('should display expert information for each pick', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getAllByText('Michael Johnson')).toHaveLength(2);
        expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
      });
    });

    it('should show pick details', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Check confidence levels
        expect(screen.getByText('85% confidence')).toBeInTheDocument();
        expect(screen.getByText('78% confidence')).toBeInTheDocument();
        expect(screen.getByText('92% confidence')).toBeInTheDocument();
        
        // Check categories
        expect(screen.getByText('NFL')).toBeInTheDocument();
        expect(screen.getByText('Crypto')).toBeInTheDocument();
        expect(screen.getByText('NBA')).toBeInTheDocument();
      });
    });

    it('should indicate pick status', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const pendingBadges = screen.getAllByText('PENDING');
        expect(pendingBadges).toHaveLength(2);
        
        expect(screen.getByText('WON')).toBeInTheDocument();
        expect(screen.getByText('Lakers won 117-113')).toBeInTheDocument();
      });
    });
  });

  describe('Filtering and Sorting', () => {
    it('should filter picks by expert', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Filter by Expert')).toBeInTheDocument();
      });
      
      const expertFilter = screen.getByLabelText('Filter by Expert');
      await user.selectOptions(expertFilter, 'expert-1');
      
      await waitFor(() => {
        expect(expertAnalystApi.getPicks).toHaveBeenCalledWith(
          expect.objectContaining({
            expert_id: 'expert-1',
            subscribed_only: true
          })
        );
      });
    });

    it('should filter picks by category', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const categoryFilter = await screen.findByLabelText('Filter by Category');
      await user.selectOptions(categoryFilter, 'NFL');
      
      await waitFor(() => {
        expect(expertAnalystApi.getPicks).toHaveBeenCalledWith(
          expect.objectContaining({
            category: 'NFL',
            subscribed_only: true
          })
        );
      });
    });

    it('should filter picks by status', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const statusFilter = await screen.findByLabelText('Filter by Status');
      await user.selectOptions(statusFilter, 'pending');
      
      await waitFor(() => {
        expect(expertAnalystApi.getPicks).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'pending',
            subscribed_only: true
          })
        );
      });
    });

    it('should sort picks by date', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const sortSelect = await screen.findByLabelText('Sort by');
      await user.selectOptions(sortSelect, 'oldest');
      
      await waitFor(() => {
        expect(expertAnalystApi.getPicks).toHaveBeenCalledWith(
          expect.objectContaining({
            sort_by: 'oldest',
            subscribed_only: true
          })
        );
      });
    });

    it('should filter by confidence level', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const confidenceSlider = await screen.findByLabelText('Minimum Confidence');
      fireEvent.change(confidenceSlider, { target: { value: '80' } });
      
      await waitFor(() => {
        expect(expertAnalystApi.getPicks).toHaveBeenCalledWith(
          expect.objectContaining({
            confidence_min: 80,
            subscribed_only: true
          })
        );
      });
    });
  });

  describe('Pick Details Modal', () => {
    it('should open pick details when clicked', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const pickCard = await screen.findByText('Chiefs -3.5 vs Raiders');
      await user.click(pickCard);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Pick Details')).toBeInTheDocument();
        expect(screen.getByText(/Chiefs dominate at home/)).toBeInTheDocument();
      });
    });

    it('should display stake recommendation', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const pickCard = await screen.findByText('Chiefs -3.5 vs Raiders');
      await user.click(pickCard);
      
      await waitFor(() => {
        expect(screen.getByText('Recommended Stake: 3 units')).toBeInTheDocument();
        expect(screen.getByText('Odds: -110')).toBeInTheDocument();
      });
    });

    it('should show event date for upcoming picks', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      const pickCard = await screen.findByText('Chiefs -3.5 vs Raiders');
      await user.click(pickCard);
      
      await waitFor(() => {
        expect(screen.getByText(/Event Date:/)).toBeInTheDocument();
        expect(screen.getByText(/Jan 26, 2024/)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Tracking', () => {
    it('should display performance summary', async () => {
      (expertAnalystApi.getUserDashboard as jest.Mock).mockResolvedValue({
        subscriptions: mockSubscriptions,
        recent_picks: mockPicks,
        performance_summary: {
          total_invested: 10000,
          total_profit: 1500,
          roi: 0.15,
          win_rate: 0.65
        }
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Performance Summary')).toBeInTheDocument();
        expect(screen.getByText('ROI: 15%')).toBeInTheDocument();
        expect(screen.getByText('Win Rate: 65%')).toBeInTheDocument();
        expect(screen.getByText('Profit: $1,500')).toBeInTheDocument();
      });
    });

    it('should track individual pick performance', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const wonPick = screen.getByText('Lakers ML vs Celtics').closest('[data-testid="pick-card"]');
        expect(within(wonPick!).getByText('+120')).toBeInTheDocument();
        expect(within(wonPick!).getByText('WON')).toHaveClass('text-green-600');
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should connect to WebSocket for live updates', async () => {
      const mockWebSocket = {
        on: jest.fn(),
        emit: jest.fn(),
        disconnect: jest.fn()
      };
      
      (window as any).WebSocket = jest.fn(() => mockWebSocket);
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(window.WebSocket).toHaveBeenCalledWith(
          expect.stringContaining('/ws/picks')
        );
      });
    });

    it('should update picks on WebSocket events', async () => {
      const mockWebSocket = {
        on: jest.fn(),
        emit: jest.fn(),
        disconnect: jest.fn()
      };
      
      (window as any).WebSocket = jest.fn(() => mockWebSocket);
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(mockWebSocket.on).toHaveBeenCalledWith('pick_update', expect.any(Function));
        expect(mockWebSocket.on).toHaveBeenCalledWith('new_pick', expect.any(Function));
      });
    });
  });

  describe('Subscription Management', () => {
    it('should show active subscriptions', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Active Subscriptions (2)')).toBeInTheDocument();
        expect(screen.getByText('Michael Johnson - Premium')).toBeInTheDocument();
        expect(screen.getByText('Sarah Chen - Basic')).toBeInTheDocument();
      });
    });

    it('should link to expert profiles', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const expertLinks = screen.getAllByRole('link', { name: /View Profile/ });
        expect(expertLinks[0]).toHaveAttribute('href', '/experts/expert-1');
        expect(expertLinks[1]).toHaveAttribute('href', '/experts/expert-2');
      });
    });

    it('should show subscription expiry dates', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText(/Expires: Feb 1, 2024/)).toBeInTheDocument();
        expect(screen.getByText(/Expires: Feb 5, 2024/)).toBeInTheDocument();
      });
    });
  });

  describe('Empty States', () => {
    it('should show no subscriptions message', async () => {
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [],
        total_count: 0
      });
      
      (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
        picks: [],
        total_count: 0
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('No Active Subscriptions')).toBeInTheDocument();
        expect(screen.getByText(/Subscribe to experts to see their picks/)).toBeInTheDocument();
        expect(screen.getByText('Browse Experts')).toBeInTheDocument();
      });
    });

    it('should show no picks message when subscribed but no picks', async () => {
      (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
        picks: [],
        total_count: 0
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('No Picks Available')).toBeInTheDocument();
        expect(screen.getByText(/Your subscribed experts haven't posted any picks yet/)).toBeInTheDocument();
      });
    });
  });

  describe('Pick Actions', () => {
    it('should allow saving picks to favorites', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const favoriteButtons = screen.getAllByTestId('favorite-button');
        expect(favoriteButtons).toHaveLength(3);
      });
      
      const firstFavoriteButton = screen.getAllByTestId('favorite-button')[0];
      await user.click(firstFavoriteButton);
      
      await waitFor(() => {
        expect(firstFavoriteButton).toHaveAttribute('aria-pressed', 'true');
      });
    });

    it('should allow sharing picks', async () => {
      const user = userEvent.setup();
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const shareButtons = screen.getAllByTestId('share-button');
        expect(shareButtons).toHaveLength(3);
      });
      
      const shareButton = screen.getAllByTestId('share-button')[0];
      await user.click(shareButton);
      
      await waitFor(() => {
        expect(screen.getByText('Share Pick')).toBeInTheDocument();
        expect(screen.getByText('Copy Link')).toBeInTheDocument();
      });
    });
  });

  describe('Authentication', () => {
    it('should redirect to sign in if not authenticated', async () => {
      (useAuth as jest.Mock).mockReturnValue({
        getToken: mockGetToken,
        userId: null,
        isSignedIn: false
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/sign-in?redirect=/picks');
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading skeleton while fetching data', () => {
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );
      
      render(<PicksFeedPage />);
      
      expect(screen.getByTestId('picks-feed-skeleton')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error message on API failure', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      (expertAnalystApi.getPicks as jest.Mock).mockRejectedValue(new Error('API Error'));
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to load picks')).toBeInTheDocument();
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
      
      consoleSpy.mockRestore();
    });

    it('should retry on error', async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      (expertAnalystApi.getPicks as jest.Mock)
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValueOnce({
          picks: mockPicks,
          total_count: mockPicks.length
        });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
      
      const retryButton = screen.getByText('Try Again');
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(screen.getByText('Chiefs -3.5 vs Raiders')).toBeInTheDocument();
      });
      
      consoleSpy.mockRestore();
    });
  });
});