/**
 * Expert Portal Unit Tests
 * ========================
 * 
 * Tests for the expert creator portal where experts manage their profiles,
 * create picks, track performance, and manage subscribers.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import ExpertPortalPage from '@/app/expert-portal/page';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Expert, Pick, ExpertAnalytics } from '@/lib/expert-analyst-api';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

// Mock data
const mockExpertProfile: Expert = {
  id: 'expert-1',
  name: '<PERSON>',
  bio: 'NFL betting expert with 8+ years experience.',
  avatar_url: '/avatars/expert1.jpg',
  specializations: ['NFL', 'College Football'],
  verification_status: 'verified',
  overall_rating: 4.8,
  total_subscribers: 2450,
  total_picks: 1250,
  win_rate: 0.68,
  roi: 0.15,
  streak_type: 'win',
  current_streak: 8,
  subscription_tiers: [
    {
      id: 'tier-1',
      name: 'Basic',
      price: 29.99,
      description: 'Weekly picks',
      features: ['Weekly NFL picks']
    }
  ],
  created_at: '2024-01-15T00:00:00Z',
  updated_at: '2024-01-20T00:00:00Z'
};

const mockAnalytics: ExpertAnalytics = {
  total_revenue: 125000,
  monthly_revenue: 8500,
  subscriber_growth: 15.5,
  pick_performance: {
    total_picks: 1250,
    successful_picks: 850,
    win_rate: 0.68,
    roi: 0.15
  },
  top_categories: [
    { category: 'NFL', performance: 0.72 },
    { category: 'College Football', performance: 0.65 }
  ]
};

const mockRecentPicks: Pick[] = [
  {
    id: 'pick-1',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    title: 'Chiefs -3.5',
    description: 'Chiefs at home',
    category: 'NFL',
    confidence_level: 85,
    pick_type: 'spread',
    odds: -110,
    status: 'pending',
    created_at: '2024-01-25T10:00:00Z',
    tier_required: 'tier-1'
  },
  {
    id: 'pick-2',
    expert_id: 'expert-1',
    expert_name: 'Michael Johnson',
    title: 'Bills ML',
    description: 'Bills must win',
    category: 'NFL',
    confidence_level: 90,
    pick_type: 'moneyline',
    odds: -150,
    status: 'won',
    outcome: 'Bills 31-21',
    created_at: '2024-01-20T10:00:00Z',
    tier_required: 'tier-1'
  }
];

describe('ExpertPortalPage', () => {
  const mockGetToken = jest.fn();
  const mockPush = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'test-user-id',
      isSignedIn: true
    });
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    // API mocks
    (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExpertProfile);
    (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue(mockAnalytics);
    (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
      picks: mockRecentPicks,
      total_count: mockRecentPicks.length
    });
  });

  describe('Dashboard Overview', () => {
    it('should display expert dashboard with key metrics', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Welcome back, Michael Johnson')).toBeInTheDocument();
      });
    });

    it('should show revenue metrics', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Total Revenue')).toBeInTheDocument();
        expect(screen.getByText('$125,000')).toBeInTheDocument();
        expect(screen.getByText('Monthly Revenue')).toBeInTheDocument();
        expect(screen.getByText('$8,500')).toBeInTheDocument();
      });
    });

    it('should display subscriber metrics', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Total Subscribers')).toBeInTheDocument();
        expect(screen.getByText('2,450')).toBeInTheDocument();
        expect(screen.getByText('Growth Rate')).toBeInTheDocument();
        expect(screen.getByText('+15.5%')).toBeInTheDocument();
      });
    });

    it('should show pick performance summary', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Win Rate')).toBeInTheDocument();
        expect(screen.getByText('68%')).toBeInTheDocument();
        expect(screen.getByText('ROI')).toBeInTheDocument();
        expect(screen.getByText('15%')).toBeInTheDocument();
        expect(screen.getByText('Current Streak')).toBeInTheDocument();
        expect(screen.getByText('8 wins')).toBeInTheDocument();
      });
    });
  });

  describe('Pick Creation', () => {
    it('should display create pick button', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Create New Pick')).toBeInTheDocument();
      });
    });

    it('should open pick creation modal', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.getByText('Create Pick')).toBeInTheDocument();
        expect(screen.getByLabelText('Title')).toBeInTheDocument();
        expect(screen.getByLabelText('Description')).toBeInTheDocument();
        expect(screen.getByLabelText('Category')).toBeInTheDocument();
        expect(screen.getByLabelText('Confidence Level')).toBeInTheDocument();
      });
    });

    it('should validate pick form fields', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      // Try to submit empty form
      const submitButton = screen.getByText('Publish Pick');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('Title is required')).toBeInTheDocument();
        expect(screen.getByText('Description is required')).toBeInTheDocument();
        expect(screen.getByText('Category is required')).toBeInTheDocument();
      });
    });

    it('should submit valid pick', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.createPick as jest.Mock).mockResolvedValue({
        id: 'new-pick',
        title: 'Test Pick',
        status: 'pending'
      });
      
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      // Fill form
      await user.type(screen.getByLabelText('Title'), 'Patriots +7.5');
      await user.type(screen.getByLabelText('Description'), 'Patriots as underdogs at home');
      await user.selectOptions(screen.getByLabelText('Category'), 'NFL');
      
      const confidenceSlider = screen.getByLabelText('Confidence Level');
      fireEvent.change(confidenceSlider, { target: { value: '75' } });
      
      const submitButton = screen.getByText('Publish Pick');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.createPick).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Patriots +7.5',
            description: 'Patriots as underdogs at home',
            category: 'NFL',
            confidence_level: 75
          })
        );
      });
    });

    it('should show success message after pick creation', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.createPick as jest.Mock).mockResolvedValue({
        id: 'new-pick',
        title: 'Test Pick',
        status: 'pending'
      });
      
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      await user.type(screen.getByLabelText('Title'), 'Test Pick');
      await user.type(screen.getByLabelText('Description'), 'Test description');
      await user.selectOptions(screen.getByLabelText('Category'), 'NFL');
      
      const submitButton = screen.getByText('Publish Pick');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('Pick published successfully!')).toBeInTheDocument();
      });
    });
  });

  describe('Recent Picks Management', () => {
    it('should display recent picks list', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Recent Picks')).toBeInTheDocument();
        expect(screen.getByText('Chiefs -3.5')).toBeInTheDocument();
        expect(screen.getByText('Bills ML')).toBeInTheDocument();
      });
    });

    it('should show pick status indicators', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('PENDING')).toBeInTheDocument();
        expect(screen.getByText('WON')).toBeInTheDocument();
      });
    });

    it('should allow editing pending picks', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        const pendingPick = screen.getByText('Chiefs -3.5').closest('tr');
        const editButton = within(pendingPick!).getByText('Edit');
        expect(editButton).toBeInTheDocument();
      });
      
      const editButton = screen.getAllByText('Edit')[0];
      await user.click(editButton);
      
      await waitFor(() => {
        expect(screen.getByText('Edit Pick')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Chiefs -3.5')).toBeInTheDocument();
      });
    });

    it('should not allow editing resolved picks', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        const wonPick = screen.getByText('Bills ML').closest('tr');
        const editButton = within(wonPick!).queryByText('Edit');
        expect(editButton).not.toBeInTheDocument();
      });
    });

    it('should allow updating pick outcomes', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.updatePickOutcome as jest.Mock).mockResolvedValue({
        id: 'pick-1',
        status: 'won'
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        const pendingPick = screen.getByText('Chiefs -3.5').closest('tr');
        const outcomeButton = within(pendingPick!).getByText('Set Outcome');
        expect(outcomeButton).toBeInTheDocument();
      });
      
      const outcomeButton = screen.getByText('Set Outcome');
      await user.click(outcomeButton);
      
      // Select outcome
      const wonButton = screen.getByText('Won');
      await user.click(wonButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.updatePickOutcome).toHaveBeenCalledWith('pick-1', {
          status: 'won',
          outcome: expect.any(String)
        });
      });
    });
  });

  describe('Profile Management', () => {
    it('should display profile edit section', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Profile Settings')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Michael Johnson')).toBeInTheDocument();
        expect(screen.getByDisplayValue(/NFL betting expert/)).toBeInTheDocument();
      });
    });

    it('should allow updating profile information', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.updateExpert as jest.Mock).mockResolvedValue({
        ...mockExpertProfile,
        bio: 'Updated bio'
      });
      
      render(<ExpertPortalPage />);
      
      const bioInput = await screen.findByDisplayValue(/NFL betting expert/);
      await user.clear(bioInput);
      await user.type(bioInput, 'Updated bio with more experience');
      
      const saveButton = screen.getByText('Save Profile');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.updateExpert).toHaveBeenCalledWith('expert-1', {
          bio: 'Updated bio with more experience'
        });
      });
    });

    it('should manage specializations', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('NFL')).toBeInTheDocument();
        expect(screen.getByText('College Football')).toBeInTheDocument();
      });
      
      // Add new specialization
      const addButton = screen.getByText('Add Specialization');
      await user.click(addButton);
      
      const input = screen.getByPlaceholderText('Enter specialization');
      await user.type(input, 'NBA');
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(screen.getByText('NBA')).toBeInTheDocument();
      });
    });
  });

  describe('Subscription Tier Management', () => {
    it('should display current subscription tiers', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Subscription Tiers')).toBeInTheDocument();
        expect(screen.getByText('Basic')).toBeInTheDocument();
        expect(screen.getByText('$29.99/month')).toBeInTheDocument();
      });
    });

    it('should allow adding new tiers', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const addTierButton = await screen.findByText('Add Tier');
      await user.click(addTierButton);
      
      await waitFor(() => {
        expect(screen.getByText('Create Subscription Tier')).toBeInTheDocument();
        expect(screen.getByLabelText('Tier Name')).toBeInTheDocument();
        expect(screen.getByLabelText('Price')).toBeInTheDocument();
        expect(screen.getByLabelText('Description')).toBeInTheDocument();
      });
    });

    it('should validate tier pricing', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const addTierButton = await screen.findByText('Add Tier');
      await user.click(addTierButton);
      
      const priceInput = screen.getByLabelText('Price');
      await user.type(priceInput, '-10');
      
      const createButton = screen.getByText('Create Tier');
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.getByText('Price must be greater than 0')).toBeInTheDocument();
      });
    });
  });

  describe('Analytics Dashboard', () => {
    it('should display revenue chart', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('revenue-chart')).toBeInTheDocument();
        expect(screen.getByText('Revenue Trend')).toBeInTheDocument();
      });
    });

    it('should show category performance breakdown', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Category Performance')).toBeInTheDocument();
        expect(screen.getByText('NFL: 72%')).toBeInTheDocument();
        expect(screen.getByText('College Football: 65%')).toBeInTheDocument();
      });
    });

    it('should display subscriber growth metrics', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('subscriber-growth-chart')).toBeInTheDocument();
        expect(screen.getByText('+15.5% this month')).toBeInTheDocument();
      });
    });
  });

  describe('Payout Management', () => {
    it('should display payout section', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Earnings & Payouts')).toBeInTheDocument();
        expect(screen.getByText('Available Balance')).toBeInTheDocument();
        expect(screen.getByText('$8,500')).toBeInTheDocument();
      });
    });

    it('should allow requesting payouts', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.requestPayout as jest.Mock).mockResolvedValue({
        payout_id: 'payout-1',
        amount: 5000,
        status: 'pending'
      });
      
      render(<ExpertPortalPage />);
      
      const requestButton = await screen.findByText('Request Payout');
      await user.click(requestButton);
      
      const amountInput = screen.getByLabelText('Payout Amount');
      await user.type(amountInput, '5000');
      
      const confirmButton = screen.getByText('Confirm Payout');
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.requestPayout).toHaveBeenCalledWith(5000);
        expect(screen.getByText('Payout requested successfully')).toBeInTheDocument();
      });
    });

    it('should validate payout amounts', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const requestButton = await screen.findByText('Request Payout');
      await user.click(requestButton);
      
      const amountInput = screen.getByLabelText('Payout Amount');
      await user.type(amountInput, '10000'); // More than available
      
      const confirmButton = screen.getByText('Confirm Payout');
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(screen.getByText('Amount exceeds available balance')).toBeInTheDocument();
      });
    });
  });

  describe('Notifications', () => {
    it('should display notification preferences', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Notification Settings')).toBeInTheDocument();
        expect(screen.getByLabelText('New Subscriber Alerts')).toBeInTheDocument();
        expect(screen.getByLabelText('Pick Performance Updates')).toBeInTheDocument();
        expect(screen.getByLabelText('Payment Notifications')).toBeInTheDocument();
      });
    });
  });

  describe('Expert Verification', () => {
    it('should show verification status', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Verified Expert')).toBeInTheDocument();
        expect(screen.getByTestId('verified-badge')).toBeInTheDocument();
      });
    });

    it('should show pending verification for unverified experts', async () => {
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue({
        ...mockExpertProfile,
        verification_status: 'pending'
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Verification Pending')).toBeInTheDocument();
        expect(screen.getByText('Your expert status is under review')).toBeInTheDocument();
      });
    });
  });

  describe('Access Control', () => {
    it('should redirect non-experts to application page', async () => {
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue(
        new Error('Expert not found')
      );
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/experts/apply');
      });
    });

    it('should require authentication', async () => {
      (useAuth as jest.Mock).mockReturnValue({
        getToken: mockGetToken,
        userId: null,
        isSignedIn: false
      });
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/sign-in?redirect=/expert-portal');
      });
    });
  });
});