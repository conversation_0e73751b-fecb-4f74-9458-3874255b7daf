/**
 * Expert Detail Page Unit Tests
 * =============================
 * 
 * Comprehensive tests for individual expert profile pages.
 * Tests subscription flows, pick viewing, performance metrics, and reviews.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter, useParams } from 'next/navigation';
import ExpertDetailPage from '@/app/experts/[id]/page';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import type { Expert, Pick, Review, ExpertPerformance } from '@/lib/expert-analyst-api';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

// Mock data
const mockExpert: Expert = {
  id: '1',
  name: '<PERSON>',
  bio: 'NFL betting expert with 8+ years of experience analyzing games and providing winning picks.',
  avatar_url: '/avatars/expert1.jpg',
  specializations: ['NFL', 'College Football', 'NBA'],
  verification_status: 'verified',
  overall_rating: 4.8,
  total_subscribers: 2450,
  total_picks: 1250,
  win_rate: 0.68,
  roi: 0.15,
  streak_type: 'win',
  current_streak: 8,
  subscription_tiers: [
    {
      id: 'tier-1',
      name: 'Basic',
      price: 29.99,
      description: 'Get started with weekly NFL picks',
      features: [
        'Weekly NFL game picks',
        'Email notifications',
        'Basic performance tracking'
      ]
    },
    {
      id: 'tier-2',
      name: 'Premium',
      price: 99.99,
      description: 'Full access to all sports picks and analysis',
      features: [
        'All sports picks (NFL, NBA, NCAA)',
        'Real-time notifications',
        'Live chat access',
        'Detailed analysis reports',
        'Priority support'
      ]
    },
    {
      id: 'tier-3',
      name: 'VIP',
      price: 249.99,
      description: 'Exclusive access with personal consultations',
      features: [
        'Everything in Premium',
        'Monthly 1-on-1 consultation',
        'Custom betting strategies',
        'VIP Discord channel',
        'Early access to picks'
      ]
    }
  ],
  created_at: '2024-01-15T00:00:00Z',
  updated_at: '2024-01-20T00:00:00Z'
};

const mockPerformance: ExpertPerformance = {
  total_picks: 1250,
  winning_picks: 850,
  losing_picks: 400,
  win_rate: 0.68,
  roi: 0.15,
  avg_odds: 1.95,
  profit_loss: 1875.50,
  best_category: 'NFL',
  worst_category: 'NBA',
  monthly_performance: [
    { month: '2024-01', wins: 45, losses: 20, roi: 0.18 },
    { month: '2023-12', wins: 38, losses: 22, roi: 0.12 },
    { month: '2023-11', wins: 42, losses: 18, roi: 0.20 }
  ]
};

const mockPicks: Pick[] = [
  {
    id: 'pick-1',
    expert_id: '1',
    expert_name: 'Michael Johnson',
    title: 'Chiefs -3.5 vs Raiders',
    description: 'Chiefs should dominate at home after extra rest.',
    category: 'NFL',
    subcategory: 'Spread',
    confidence_level: 85,
    pick_type: 'spread',
    odds: -110,
    stake_recommendation: 3,
    status: 'pending',
    created_at: '2024-01-25T10:00:00Z',
    event_date: '2024-01-26T20:00:00Z',
    tier_required: 'tier-1'
  },
  {
    id: 'pick-2',
    expert_id: '1',
    expert_name: 'Michael Johnson',
    title: 'Bills ML vs Dolphins',
    description: 'Bills in must-win situation for playoffs.',
    category: 'NFL',
    subcategory: 'Moneyline',
    confidence_level: 92,
    pick_type: 'moneyline',
    odds: -150,
    stake_recommendation: 5,
    status: 'won',
    outcome: 'Bills won 31-21',
    created_at: '2024-01-20T10:00:00Z',
    event_date: '2024-01-21T13:00:00Z',
    tier_required: 'tier-2'
  }
];

const mockReviews: Review[] = [
  {
    id: 'review-1',
    user_id: 'user-1',
    user_name: 'John Doe',
    expert_id: '1',
    rating: 5,
    comment: 'Amazing picks! Been following for 3 months and up 25 units.',
    created_at: '2024-01-20T00:00:00Z'
  },
  {
    id: 'review-2',
    user_id: 'user-2',
    user_name: 'Jane Smith',
    expert_id: '1',
    rating: 4,
    comment: 'Good analysis but wish there were more NBA picks.',
    created_at: '2024-01-18T00:00:00Z'
  }
];

describe('ExpertDetailPage', () => {
  const mockGetToken = jest.fn();
  const mockPush = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'test-user-id',
      isSignedIn: true
    });
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    (useParams as jest.Mock).mockReturnValue({
      id: '1'
    });
    
    // API mocks
    (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExpert);
    (expertAnalystApi.getExpertPerformance as jest.Mock).mockResolvedValue(mockPerformance);
    (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
      picks: mockPicks,
      total_count: mockPicks.length
    });
    (expertAnalystApi.getExpertReviews as jest.Mock).mockResolvedValue({
      reviews: mockReviews,
      total_count: mockReviews.length,
      average_rating: 4.5
    });
    (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
      subscriptions: [],
      total_count: 0
    });
  });

  describe('Expert Profile Display', () => {
    it('should display expert information correctly', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
        expect(screen.getByText(/NFL betting expert with 8\+ years/)).toBeInTheDocument();
        expect(screen.getByText('4.8')).toBeInTheDocument(); // Rating
        expect(screen.getByText('2,450')).toBeInTheDocument(); // Subscribers
      });
    });

    it('should show verification badge for verified experts', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const verifiedBadge = screen.getByTestId('verified-badge');
        expect(verifiedBadge).toBeInTheDocument();
      });
    });

    it('should display specializations', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('NFL')).toBeInTheDocument();
        expect(screen.getByText('College Football')).toBeInTheDocument();
        expect(screen.getByText('NBA')).toBeInTheDocument();
      });
    });

    it('should show current streak', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('8 win streak')).toBeInTheDocument();
        expect(screen.getByTestId('streak-icon-win')).toBeInTheDocument();
      });
    });
  });

  describe('Performance Metrics', () => {
    it('should display overall performance stats', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('68%')).toBeInTheDocument(); // Win rate
        expect(screen.getByText('15%')).toBeInTheDocument(); // ROI
        expect(screen.getByText('1,250')).toBeInTheDocument(); // Total picks
        expect(screen.getByText('$1,875.50')).toBeInTheDocument(); // Profit/Loss
      });
    });

    it('should show monthly performance chart', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('performance-chart')).toBeInTheDocument();
        expect(screen.getByText('January 2024')).toBeInTheDocument();
        expect(screen.getByText('45W - 20L')).toBeInTheDocument();
      });
    });

    it('should display best and worst categories', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Best: NFL')).toBeInTheDocument();
        expect(screen.getByText('Worst: NBA')).toBeInTheDocument();
      });
    });
  });

  describe('Subscription Tiers', () => {
    it('should display all subscription tiers', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Basic tier
        expect(screen.getByText('Basic')).toBeInTheDocument();
        expect(screen.getByText('$29.99/month')).toBeInTheDocument();
        expect(screen.getByText('Get started with weekly NFL picks')).toBeInTheDocument();
        
        // Premium tier
        expect(screen.getByText('Premium')).toBeInTheDocument();
        expect(screen.getByText('$99.99/month')).toBeInTheDocument();
        
        // VIP tier
        expect(screen.getByText('VIP')).toBeInTheDocument();
        expect(screen.getByText('$249.99/month')).toBeInTheDocument();
      });
    });

    it('should show tier features', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Weekly NFL game picks')).toBeInTheDocument();
        expect(screen.getByText('Live chat access')).toBeInTheDocument();
        expect(screen.getByText('Monthly 1-on-1 consultation')).toBeInTheDocument();
      });
    });

    it('should handle subscription button clicks', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.createPaymentIntent as jest.Mock).mockResolvedValue({
        client_secret: 'test-secret',
        payment_intent_id: 'pi_test',
        amount: 2999,
        currency: 'usd'
      });
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const subscribeButtons = screen.getAllByText('Subscribe');
        expect(subscribeButtons).toHaveLength(3);
      });
      
      const basicSubscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(basicSubscribeButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.createPaymentIntent).toHaveBeenCalledWith({
          expert_id: '1',
          tier_id: 'tier-1',
          amount: 2999
        });
      });
    });

    it('should show active subscription status', async () => {
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [{
          id: 'sub-1',
          user_id: 'test-user-id',
          expert_id: '1',
          tier_id: 'tier-2',
          status: 'active',
          current_period_start: '2024-01-01T00:00:00Z',
          current_period_end: '2024-02-01T00:00:00Z',
          price: 99.99,
          created_at: '2024-01-01T00:00:00Z'
        }],
        total_count: 1
      });
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Active Subscription')).toBeInTheDocument();
        expect(screen.getByText('Premium Plan')).toBeInTheDocument();
        expect(screen.getByText('Manage Subscription')).toBeInTheDocument();
      });
    });
  });

  describe('Recent Picks', () => {
    it('should display recent picks list', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Chiefs -3.5 vs Raiders')).toBeInTheDocument();
        expect(screen.getByText('Bills ML vs Dolphins')).toBeInTheDocument();
      });
    });

    it('should show pick confidence levels', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('85% confidence')).toBeInTheDocument();
        expect(screen.getByText('92% confidence')).toBeInTheDocument();
      });
    });

    it('should indicate pick status', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('PENDING')).toBeInTheDocument();
        expect(screen.getByText('WON')).toBeInTheDocument();
      });
    });

    it('should show tier requirements for picks', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const basicPick = screen.getByText('Chiefs -3.5 vs Raiders').closest('div');
        expect(within(basicPick!).getByText('Basic')).toBeInTheDocument();
        
        const premiumPick = screen.getByText('Bills ML vs Dolphins').closest('div');
        expect(within(premiumPick!).getByText('Premium')).toBeInTheDocument();
      });
    });

    it('should blur picks for non-subscribers', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const picks = screen.getAllByTestId('pick-card');
        picks.forEach(pick => {
          expect(pick).toHaveClass('blur-sm');
        });
      });
    });
  });

  describe('Reviews Section', () => {
    it('should display expert reviews', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText(/Amazing picks!/)).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText(/Good analysis/)).toBeInTheDocument();
      });
    });

    it('should show average rating', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('4.5 out of 5')).toBeInTheDocument();
        expect(screen.getByText('Based on 2 reviews')).toBeInTheDocument();
      });
    });

    it('should allow authenticated users to write reviews', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Write a Review')).toBeInTheDocument();
      });
    });

    it('should handle review submission', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.createReview as jest.Mock).mockResolvedValue({
        id: 'review-3',
        user_id: 'test-user-id',
        user_name: 'Test User',
        expert_id: '1',
        rating: 5,
        comment: 'Great picks!',
        created_at: new Date().toISOString()
      });
      
      render(<ExpertDetailPage />);
      
      const writeReviewButton = await screen.findByText('Write a Review');
      await user.click(writeReviewButton);
      
      // Fill review form
      const ratingStars = screen.getAllByTestId('rating-star');
      await user.click(ratingStars[4]); // 5 stars
      
      const commentInput = screen.getByPlaceholderText('Share your experience...');
      await user.type(commentInput, 'Great picks!');
      
      const submitButton = screen.getByText('Submit Review');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.createReview).toHaveBeenCalledWith({
          expert_id: '1',
          rating: 5,
          comment: 'Great picks!'
        });
      });
    });
  });

  describe('Follow/Unfollow Functionality', () => {
    it('should show follow button for non-followers', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Follow Expert')).toBeInTheDocument();
      });
    });

    it('should handle follow action', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.followExpert as jest.Mock).mockResolvedValue(undefined);
      
      render(<ExpertDetailPage />);
      
      const followButton = await screen.findByText('Follow Expert');
      await user.click(followButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.followExpert).toHaveBeenCalledWith('1');
        expect(screen.getByText('Following')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading skeleton while fetching data', () => {
      (expertAnalystApi.getExpert as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );
      
      render(<ExpertDetailPage />);
      
      expect(screen.getByTestId('expert-detail-skeleton')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error message on API failure', async () => {
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue(new Error('Not found'));
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert not found')).toBeInTheDocument();
        expect(screen.getByText('Return to Experts')).toBeInTheDocument();
      });
    });

    it('should redirect to experts list on error', async () => {
      const user = userEvent.setup();
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue(new Error('Not found'));
      
      render(<ExpertDetailPage />);
      
      const returnButton = await screen.findByText('Return to Experts');
      await user.click(returnButton);
      
      expect(mockPush).toHaveBeenCalledWith('/experts');
    });
  });

  describe('Authentication', () => {
    it('should prompt sign in for subscription actions when not authenticated', async () => {
      (useAuth as jest.Mock).mockReturnValue({
        getToken: mockGetToken,
        userId: null,
        isSignedIn: false
      });
      
      const user = userEvent.setup();
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const subscribeButtons = screen.getAllByText('Subscribe');
        expect(subscribeButtons).toHaveLength(3);
      });
      
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(subscribeButton);
      
      expect(mockPush).toHaveBeenCalledWith('/sign-in?redirect=/experts/1');
    });
  });
});