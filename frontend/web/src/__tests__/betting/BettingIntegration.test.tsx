/**
 * BetBet Gaming Engine - Betting Integration Tests
 * ==============================================
 * Comprehensive testing of betting functionality implemented by Claude-Frontend
 */

import { render, screen, fireEvent, waitFor } from '../utils/simple-test-utils';
import { BettingMarkets } from '@/components/betting/BettingMarkets';
import { BettingMarketCard } from '@/components/betting/BettingMarketCard';
import { BetPlacementModal } from '@/components/betting/BetPlacementModal';
import { AdminBettingMarketManager } from '@/components/betting/AdminBettingMarketManager';
import { useGameStore } from '@/store/gameStore';
import { apiClient } from '@/lib/api';

// Mock the API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    getSessionBettingMarkets: jest.fn(),
    getTournamentBettingMarkets: jest.fn(),
    getBettingMarket: jest.fn(),
    getBettingMarketStats: jest.fn(),
    createBettingMarket: jest.fn(),
    updateBettingMarket: jest.fn(),
    deleteBettingMarket: jest.fn(),
    placeBet: jest.fn(),
    getUserBettingHistory: jest.fn(),
    getUserBettingStats: jest.fn(),
  }
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock Clerk authentication
jest.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    getToken: jest.fn().mockResolvedValue('mock-token'),
    isSignedIn: true,
    userId: 'user-123'
  }),
  useUser: () => ({
    user: {
      id: 'user-123',
      publicMetadata: { role: 'user' }
    }
  })
}));

// Mock the betting WebSocket hook
jest.mock('@/hooks/useBettingWebSocket', () => ({
  useBettingWebSocket: () => ({
    isConnected: true,
    lastMessage: null,
    sendMessage: jest.fn()
  })
}));

describe('Betting Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useGameStore.getState().reset();
  });

  const mockBettingMarket = {
    id: 'market-1',
    title: 'Who will win the chess match?',
    description: 'Betting market for chess match outcome',
    status: 'active' as const,
    session_id: 'session-123',
    created_at: '2025-01-20T10:00:00Z',
    outcomes: [
      {
        id: 'outcome-1',
        title: 'Player One Wins',
        odds: 2.5,
        total_amount: 1000
      },
      {
        id: 'outcome-2',
        title: 'Player Two Wins', 
        odds: 1.8,
        total_amount: 1500
      }
    ],
    total_pool: 2500
  };

  const mockSession = {
    id: 'session-123',
    game_id: 'game-1',
    status: 'active' as const,
    players: [
      { id: 'player-1', username: 'alice', status: 'active' },
      { id: 'player-2', username: 'bob', status: 'active' }
    ],
    betting_enabled: true,
    slug: 'chess-match-alice-vs-bob'
  };

  describe('BettingMarkets Component', () => {
    it('renders betting markets for a session', async () => {
      mockApiClient.getSessionBettingMarkets.mockResolvedValue([mockBettingMarket]);

      render(<BettingMarkets sessionId="session-123" showLiveUpdates={true} />);

      await waitFor(() => {
        expect(screen.getByText('Who will win the chess match?')).toBeInTheDocument();
      });

      expect(mockApiClient.getSessionBettingMarkets).toHaveBeenCalledWith('session-123');
    });

    it('handles loading state properly', () => {
      mockApiClient.getSessionBettingMarkets.mockReturnValue(new Promise(() => {}));

      render(<BettingMarkets sessionId="session-123" showLiveUpdates={false} />);

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('displays error state when API fails', async () => {
      mockApiClient.getSessionBettingMarkets.mockRejectedValue(new Error('API Error'));

      render(<BettingMarkets sessionId="session-123" showLiveUpdates={false} />);

      await waitFor(() => {
        expect(screen.getByText(/error loading betting markets/i)).toBeInTheDocument();
      });
    });
  });

  describe('BettingMarketCard Component', () => {
    it('displays market information correctly', () => {
      render(<BettingMarketCard market={mockBettingMarket} onOutcomeSelect={jest.fn()} />);

      expect(screen.getByText('Who will win the chess match?')).toBeInTheDocument();
      expect(screen.getByText('Player One Wins')).toBeInTheDocument();
      expect(screen.getByText('Player Two Wins')).toBeInTheDocument();
      expect(screen.getByText('2.5x')).toBeInTheDocument(); // Decimal odds
      expect(screen.getByText('1.8x')).toBeInTheDocument();
    });

    it('handles outcome selection', () => {
      const mockOnOutcomeSelect = jest.fn();
      render(<BettingMarketCard market={mockBettingMarket} onOutcomeSelect={mockOnOutcomeSelect} />);

      fireEvent.click(screen.getByText('Player One Wins'));

      expect(mockOnOutcomeSelect).toHaveBeenCalledWith(
        mockBettingMarket,
        mockBettingMarket.outcomes[0]
      );
    });

    it('shows market status indicators', () => {
      const closedMarket = { ...mockBettingMarket, status: 'closed' as const };
      render(<BettingMarketCard market={closedMarket} onOutcomeSelect={jest.fn()} />);

      expect(screen.getByText(/closed/i)).toBeInTheDocument();
    });

    it('displays pool distribution visualization', () => {
      render(<BettingMarketCard market={mockBettingMarket} onOutcomeSelect={jest.fn()} />);

      // Check for pool amounts
      expect(screen.getByText('$1,000')).toBeInTheDocument(); // Player One pool
      expect(screen.getByText('$1,500')).toBeInTheDocument(); // Player Two pool
    });
  });

  describe('BetPlacementModal Component', () => {
    const mockOutcome = mockBettingMarket.outcomes[0];

    it('calculates potential returns correctly', () => {
      render(
        <BetPlacementModal
          isOpen={true}
          market={mockBettingMarket}
          outcome={mockOutcome}
          onClose={jest.fn()}
          onBetPlaced={jest.fn()}
        />
      );

      const amountInput = screen.getByLabelText(/bet amount/i);
      fireEvent.change(amountInput, { target: { value: '100' } });

      // Potential return = bet amount * odds = 100 * 2.5 = 250
      expect(screen.getByText('$250.00')).toBeInTheDocument(); // Potential return
      expect(screen.getByText('$150.00')).toBeInTheDocument(); // Profit
    });

    it('validates bet amounts', async () => {
      render(
        <BetPlacementModal
          isOpen={true}
          market={mockBettingMarket}
          outcome={mockOutcome}
          onClose={jest.fn()}
          onBetPlaced={jest.fn()}
        />
      );

      const amountInput = screen.getByLabelText(/bet amount/i);
      const submitButton = screen.getByText(/place bet/i);

      // Test minimum amount validation
      fireEvent.change(amountInput, { target: { value: '0.5' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/minimum bet amount/i)).toBeInTheDocument();
      });
    });

    it('handles bet placement successfully', async () => {
      const mockOnBetPlaced = jest.fn();
      mockApiClient.placeBet.mockResolvedValue({ success: true, bet_id: 'bet-123' });

      render(
        <BetPlacementModal
          isOpen={true}
          market={mockBettingMarket}
          outcome={mockOutcome}
          onClose={jest.fn()}
          onBetPlaced={mockOnBetPlaced}
        />
      );

      const amountInput = screen.getByLabelText(/bet amount/i);
      const submitButton = screen.getByText(/place bet/i);

      fireEvent.change(amountInput, { target: { value: '50' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockApiClient.placeBet).toHaveBeenCalledWith({
          market_id: 'market-1',
          outcome_id: 'outcome-1',
          bet_amount: 50
        });
      });

      await waitFor(() => {
        expect(mockOnBetPlaced).toHaveBeenCalled();
      });
    });

    it('provides quick amount buttons', () => {
      render(
        <BetPlacementModal
          isOpen={true}
          market={mockBettingMarket}
          outcome={mockOutcome}
          onClose={jest.fn()}
          onBetPlaced={jest.fn()}
        />
      );

      const quickAmountButtons = ['$5', '$10', '$25', '$50'];
      quickAmountButtons.forEach(amount => {
        expect(screen.getByText(amount)).toBeInTheDocument();
      });

      // Test quick amount selection
      fireEvent.click(screen.getByText('$25'));
      expect(screen.getByDisplayValue('25')).toBeInTheDocument();
    });
  });

  describe('AdminBettingMarketManager Component', () => {
    beforeEach(() => {
      // Mock admin user
      jest.mocked(require('@clerk/nextjs').useUser).mockReturnValue({
        user: {
          id: 'admin-123',
          publicMetadata: { role: 'admin' }
        }
      });
    });

    it('shows admin interface for admin users', () => {
      render(<AdminBettingMarketManager sessionId="session-123" onMarketCreated={jest.fn()} />);

      expect(screen.getByText(/create betting market/i)).toBeInTheDocument();
    });

    it('allows market creation', async () => {
      const mockOnMarketCreated = jest.fn();
      mockApiClient.createBettingMarket.mockResolvedValue(mockBettingMarket);

      render(<AdminBettingMarketManager sessionId="session-123" onMarketCreated={mockOnMarketCreated} />);

      // Fill in market creation form
      fireEvent.change(screen.getByLabelText(/market title/i), {
        target: { value: 'Test Market' }
      });
      fireEvent.change(screen.getByLabelText(/description/i), {
        target: { value: 'Test Description' }
      });

      // Add outcomes
      fireEvent.click(screen.getByText(/add outcome/i));
      fireEvent.change(screen.getByLabelText(/outcome title/i), {
        target: { value: 'Test Outcome' }
      });
      fireEvent.change(screen.getByLabelText(/initial odds/i), {
        target: { value: '2.0' }
      });

      // Submit form
      fireEvent.click(screen.getByText(/create market/i));

      await waitFor(() => {
        expect(mockApiClient.createBettingMarket).toHaveBeenCalledWith({
          title: 'Test Market',
          description: 'Test Description',
          session_id: 'session-123',
          outcomes: [{ title: 'Test Outcome', odds: 2.0 }]
        });
      });

      await waitFor(() => {
        expect(mockOnMarketCreated).toHaveBeenCalledWith(mockBettingMarket);
      });
    });

    it('prevents access for non-admin users', () => {
      // Mock regular user
      jest.mocked(require('@clerk/nextjs').useUser).mockReturnValue({
        user: {
          id: 'user-123',
          publicMetadata: { role: 'user' }
        }
      });

      render(<AdminBettingMarketManager sessionId="session-123" onMarketCreated={jest.fn()} />);

      expect(screen.getByText(/admin access required/i)).toBeInTheDocument();
    });
  });

  describe('Betting WebSocket Integration', () => {
    it('handles real-time betting updates', async () => {
      const mockWebSocket = {
        isConnected: true,
        lastMessage: {
          type: 'bet_placed',
          data: {
            market_id: 'market-1',
            outcome_id: 'outcome-1',
            amount: 100,
            new_odds: 2.3
          }
        },
        sendMessage: jest.fn()
      };

      jest.mocked(require('@/hooks/useBettingWebSocket').useBettingWebSocket)
        .mockReturnValue(mockWebSocket);

      render(<BettingMarkets sessionId="session-123" showLiveUpdates={true} />);

      // Verify that real-time updates would trigger market refresh
      // This tests the WebSocket integration point
      expect(screen.queryByText(/real.*time/i)).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      mockApiClient.getSessionBettingMarkets.mockRejectedValue(
        new Error('Network error')
      );

      render(<BettingMarkets sessionId="session-123" showLiveUpdates={false} />);

      await waitFor(() => {
        expect(screen.getByText(/error loading betting markets/i)).toBeInTheDocument();
      });
    });

    it('handles authentication errors', async () => {
      mockApiClient.placeBet.mockRejectedValue(
        new Error('Unauthorized')
      );

      render(
        <BetPlacementModal
          isOpen={true}
          market={mockBettingMarket}
          outcome={mockBettingMarket.outcomes[0]}
          onClose={jest.fn()}
          onBetPlaced={jest.fn()}
        />
      );

      const amountInput = screen.getByLabelText(/bet amount/i);
      const submitButton = screen.getByText(/place bet/i);

      fireEvent.change(amountInput, { target: { value: '50' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please sign in/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Testing', () => {
    it('renders large numbers of betting markets efficiently', () => {
      const manyMarkets = Array.from({ length: 100 }, (_, i) => ({
        ...mockBettingMarket,
        id: `market-${i}`,
        title: `Market ${i}`
      }));

      mockApiClient.getSessionBettingMarkets.mockResolvedValue(manyMarkets);

      const startTime = performance.now();
      render(<BettingMarkets sessionId="session-123" showLiveUpdates={false} />);
      const endTime = performance.now();

      // Should render within reasonable time (< 100ms)
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});