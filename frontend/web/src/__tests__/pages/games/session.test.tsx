/**
 * BetBet Gaming Engine - Gaming Session Tests
 * ===========================================
 */

import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import GameSessionPage from '@/app/games/[gameId]/session/[sessionId]/page';
import { mockStore, updateMockStore, resetMockStore, createMockSession, createMockUser, createMockParticipant } from '../../utils/test-utils';

// Mock the useParams hook
jest.mock('next/navigation', () => ({
  ...jest.requireActual('next/navigation'),
  useParams: () => ({
    gameId: 'test-game',
    sessionId: 'test-session'
  }),
}));

// Mock API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    getSession: jest.fn(),
    joinSession: jest.fn(),
    leaveSession: jest.fn(),
  },
}));

describe('Game Session Page', () => {
  beforeEach(() => {
    resetMockStore();
  });

  it('renders session loading state', () => {
    updateMockStore({
      sessionLoading: true,
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/loading session/i)).toBeInTheDocument();
  });

  it('renders session not found', () => {
    updateMockStore({
      sessionLoading: false,
      currentSession: null,
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/session not found/i)).toBeInTheDocument();
  });

  it('renders session details when loaded', () => {
    const mockSession = createMockSession({
      session_name: 'Friday Night Trivia',
      status: 'waiting',
      current_participants: 3,
      max_participants: 8,
      entry_fee: 10,
      total_prize_pool: 80,
    });

    updateMockStore({
      sessionLoading: false,
      currentSession: mockSession,
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText('Friday Night Trivia')).toBeInTheDocument();
    expect(screen.getByText('3/8 players')).toBeInTheDocument();
    expect(screen.getByText('$10')).toBeInTheDocument();
    expect(screen.getByText('$80')).toBeInTheDocument();
  });

  it('shows join button for non-participants', () => {
    const mockSession = createMockSession({ status: 'waiting' });

    updateMockStore({
      currentSession: mockSession,
      isAuthenticated: true,
      user: createMockUser(),
      participants: [],
    });

    render(<GameSessionPage />);
    
    expect(screen.getByRole('button', { name: /join session/i })).toBeInTheDocument();
  });

  it('shows leave button for participants', () => {
    const mockUser = createMockUser();
    const mockSession = createMockSession({ status: 'waiting' });
    const mockParticipant = createMockParticipant({ user_id: mockUser.id });

    updateMockStore({
      currentSession: mockSession,
      isAuthenticated: true,
      user: mockUser,
      participants: [mockParticipant],
    });

    render(<GameSessionPage />);
    
    expect(screen.getByRole('button', { name: /leave session/i })).toBeInTheDocument();
  });

  it('shows ready button for participants in waiting session', () => {
    const mockUser = createMockUser();
    const mockSession = createMockSession({ status: 'waiting' });
    const mockParticipant = createMockParticipant({ 
      user_id: mockUser.id, 
      is_ready: false 
    });

    updateMockStore({
      currentSession: mockSession,
      isAuthenticated: true,
      user: mockUser,
      participants: [mockParticipant],
    });

    render(<GameSessionPage />);
    
    expect(screen.getByRole('button', { name: /ready/i })).toBeInTheDocument();
  });

  it('shows participants list', () => {
    const mockSession = createMockSession();
    const mockParticipants = [
      createMockParticipant({ user_id: 'user-1' }),
      createMockParticipant({ user_id: 'user-2', is_ready: true }),
    ];

    updateMockStore({
      currentSession: mockSession,
      participants: mockParticipants,
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/participants/i)).toBeInTheDocument();
    // Should show participant indicators
    const readyIndicators = screen.getAllByText(/ready/i);
    expect(readyIndicators.length).toBeGreaterThan(0);
  });

  it('shows game in progress when session is active', () => {
    const mockSession = createMockSession({ status: 'active' });
    const mockGameState = {
      currentRound: 1,
      totalRounds: 5,
      timeRemaining: 30,
      question: 'What is 2 + 2?',
      options: ['3', '4', '5', '6'],
    };

    updateMockStore({
      currentSession: mockSession,
      currentGameState: mockGameState,
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText('Round 1 of 5')).toBeInTheDocument();
    expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
    expect(screen.getByText('30s')).toBeInTheDocument();
  });

  it('shows spectator mode when not participating', () => {
    const mockSession = createMockSession({ status: 'active' });

    updateMockStore({
      currentSession: mockSession,
      isSpectatorMode: true,
      participants: [],
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/spectator mode/i)).toBeInTheDocument();
  });

  it('handles join session action', async () => {
    const { apiClient } = require('@/lib/api');
    apiClient.joinSession.mockResolvedValue({});

    const mockSession = createMockSession({ status: 'waiting' });

    updateMockStore({
      currentSession: mockSession,
      isAuthenticated: true,
      user: createMockUser(),
      participants: [],
    });

    render(<GameSessionPage />);
    
    const joinButton = screen.getByRole('button', { name: /join session/i });
    fireEvent.click(joinButton);

    await waitFor(() => {
      expect(apiClient.joinSession).toHaveBeenCalledWith(mockSession.id);
    });
  });

  it('shows connection status', () => {
    const mockSession = createMockSession({ status: 'active' });

    updateMockStore({
      currentSession: mockSession,
      isConnected: true,
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/connected/i)).toBeInTheDocument();
  });

  it('shows connection error', () => {
    const mockSession = createMockSession({ status: 'active' });

    updateMockStore({
      currentSession: mockSession,
      isConnected: false,
      connectionError: 'Connection lost',
    });

    render(<GameSessionPage />);
    
    expect(screen.getByText(/connection lost/i)).toBeInTheDocument();
  });

  it('shows chat when enabled', () => {
    const mockSession = createMockSession({ 
      status: 'active',
      spectator_chat_enabled: true 
    });

    updateMockStore({
      currentSession: mockSession,
      isChatOpen: true,
    });

    render(<GameSessionPage />);
    
    expect(screen.getByPlaceholderText(/type a message/i)).toBeInTheDocument();
  });
});