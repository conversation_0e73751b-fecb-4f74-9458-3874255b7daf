/**
 * BetBet Gaming Engine - Tournament Bracket Tests
 * ===============================================
 */

import { render, screen, fireEvent } from '../../utils/test-utils';
import TournamentBracketPage from '@/app/tournaments/[id]/bracket/page';
import { resetMockStore } from '../../utils/test-utils';

// Mock useParams
jest.mock('next/navigation', () => ({
  ...jest.requireActual('next/navigation'),
  useParams: () => ({
    id: 'tournament-1'
  }),
}));

// Mock API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    getTournament: jest.fn(),
    getTournamentBracket: jest.fn(),
  },
}));

const mockTournament = {
  id: 'tournament-1',
  name: 'Friday Night Championship',
  game_id: 'game-1',
  format: 'single_elimination',
  status: 'active',
  entry_fee: 25,
  prize_pool: 1000,
  max_participants: 16,
  current_participants: 16,
  start_time: new Date(Date.now() + 3600000).toISOString(),
  estimated_duration_minutes: 120,
};

const mockBracket = {
  rounds: [
    {
      round_number: 1,
      matches: [
        {
          id: 'match-1',
          participant1_id: 'player-1',
          participant1_name: 'Player One',
          participant2_id: 'player-2',
          participant2_name: 'Player Two',
          participant1_score: 10,
          participant2_score: 8,
          status: 'completed',
          winner_id: 'player-1',
        },
        {
          id: 'match-2',
          participant1_id: 'player-3',
          participant1_name: 'Player Three',
          participant2_id: 'player-4',
          participant2_name: 'Player Four',
          participant1_score: 0,
          participant2_score: 0,
          status: 'in_progress',
          winner_id: null,
        }
      ]
    },
    {
      round_number: 2,
      matches: [
        {
          id: 'match-3',
          participant1_id: 'player-1',
          participant1_name: 'Player One',
          participant2_id: null,
          participant2_name: 'TBD',
          participant1_score: 0,
          participant2_score: 0,
          status: 'waiting',
          winner_id: null,
        }
      ]
    }
  ]
};

describe('Tournament Bracket Page', () => {
  beforeEach(() => {
    resetMockStore();
    const { apiClient } = require('@/lib/api');
    apiClient.getTournament.mockResolvedValue(mockTournament);
    apiClient.getTournamentBracket.mockResolvedValue(mockBracket);
  });

  it('renders tournament information', async () => {
    render(<TournamentBracketPage />);
    
    expect(screen.getByText('Tournament Bracket')).toBeInTheDocument();
    await screen.findByText('Friday Night Championship');
    expect(screen.getByText('Single Elimination')).toBeInTheDocument();
    expect(screen.getByText('$1,000')).toBeInTheDocument();
  });

  it('displays bracket rounds', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Round 1');
    expect(screen.getByText('Round 2')).toBeInTheDocument();
  });

  it('shows match details', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Player One');
    expect(screen.getByText('Player Two')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // Score
    expect(screen.getByText('8')).toBeInTheDocument(); // Score
  });

  it('indicates match status', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Player One');
    // Should show completed match
    expect(screen.getByText('Completed')).toBeInTheDocument();
    // Should show in progress match
    expect(screen.getByText('Live')).toBeInTheDocument();
    // Should show waiting match
    expect(screen.getByText('Waiting')).toBeInTheDocument();
  });

  it('highlights winners', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Player One');
    
    // Winner should have special styling
    const winnerCard = screen.getByText('Player One').closest('.match-participant');
    expect(winnerCard).toHaveClass('winner');
  });

  it('shows TBD for unknown participants', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('TBD');
  });

  it('allows clicking on matches for details', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Player One');
    
    const matchCard = screen.getByText('Player One').closest('.match-card');
    fireEvent.click(matchCard!);
    
    // Should open match details modal or navigate
    expect(screen.getByText('Match Details')).toBeInTheDocument();
  });

  it('shows tournament progress', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Friday Night Championship');
    
    expect(screen.getByText('Round 1 of 2')).toBeInTheDocument();
    expect(screen.getByText('50% Complete')).toBeInTheDocument();
  });

  it('displays prize distribution', async () => {
    render(<TournamentBracketPage />);
    
    await screen.findByText('Prize Distribution');
    expect(screen.getByText('1st Place: $500')).toBeInTheDocument();
    expect(screen.getByText('2nd Place: $300')).toBeInTheDocument();
    expect(screen.getByText('3rd Place: $200')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    const { apiClient } = require('@/lib/api');
    apiClient.getTournament.mockImplementation(() => new Promise(() => {}));
    
    render(<TournamentBracketPage />);
    
    expect(screen.getByText('Loading tournament...')).toBeInTheDocument();
  });

  it('handles tournament not found', async () => {
    const { apiClient } = require('@/lib/api');
    apiClient.getTournament.mockRejectedValue(new Error('Tournament not found'));
    
    render(<TournamentBracketPage />);
    
    await screen.findByText('Tournament not found');
  });

  it('refreshes bracket data automatically', async () => {
    render(<TournamentBracketPage />);
    
    const { apiClient } = require('@/lib/api');
    
    // Should call API on mount
    expect(apiClient.getTournamentBracket).toHaveBeenCalledTimes(1);
    
    // Should refresh for active tournaments
    await new Promise(resolve => setTimeout(resolve, 5100)); // Wait for auto-refresh
    
    expect(apiClient.getTournamentBracket).toHaveBeenCalledTimes(2);
  });
});