/**
 * BetBet Gaming Engine - Live Spectator Tests
 * ===========================================
 */

import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import LiveSpectatorPage from '@/app/spectate/[id]/live/page';
import { mockStore, updateMockStore, resetMockStore, createMockSession, createMockUser } from '../../utils/test-utils';

// Mock useParams
jest.mock('next/navigation', () => ({
  ...jest.requireActual('next/navigation'),
  useParams: () => ({
    id: 'session-1'
  }),
}));

// Mock API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    getSpectatorSession: jest.fn(),
    placeBet: jest.fn(),
    getSpectatorChat: jest.fn(),
    sendSpectatorMessage: jest.fn(),
  },
}));

const mockSpectatorSession = {
  ...createMockSession({
    session_name: 'Epic Trivia Battle',
    status: 'active',
    current_participants: 6,
    max_participants: 8,
  }),
  betting_enabled: true,
  total_bets: 45,
  betting_pool: 2250,
  viewer_count: 127,
  participants: [
    { id: 'p1', name: 'Player One', score: 150, betting_odds: 2.5 },
    { id: 'p2', name: 'Player Two', score: 120, betting_odds: 3.2 },
    { id: 'p3', name: 'Player Three', score: 180, betting_odds: 1.8 },
  ]
};

describe('Live Spectator Page', () => {
  beforeEach(() => {
    resetMockStore();
    const { apiClient } = require('@/lib/api');
    apiClient.getSpectatorSession.mockResolvedValue(mockSpectatorSession);
    apiClient.getSpectatorChat.mockResolvedValue([]);
  });

  it('renders session information', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('Epic Trivia Battle');
    expect(screen.getByText('127 viewers')).toBeInTheDocument();
    expect(screen.getByText('$2,250 betting pool')).toBeInTheDocument();
  });

  it('displays participant list with scores', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('Player One');
    expect(screen.getByText('Player Two')).toBeInTheDocument();
    expect(screen.getByText('Player Three')).toBeInTheDocument();
    expect(screen.getByText('150')).toBeInTheDocument(); // Score
    expect(screen.getByText('180')).toBeInTheDocument(); // Score
  });

  it('shows betting odds for participants', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('2.5x');
    expect(screen.getByText('3.2x')).toBeInTheDocument();
    expect(screen.getByText('1.8x')).toBeInTheDocument();
  });

  it('allows placing bets when authenticated', async () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser({ balance: 100 }),
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Player One');
    
    // Should show betting interface
    expect(screen.getByText('Place Bet')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Bet amount')).toBeInTheDocument();
  });

  it('handles bet placement', async () => {
    const { apiClient } = require('@/lib/api');
    apiClient.placeBet.mockResolvedValue({ success: true });

    updateMockStore({
      isAuthenticated: true,
      user: createMockUser({ balance: 100 }),
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Player One');
    
    // Select participant and enter bet amount
    const playerCard = screen.getByText('Player One').closest('.participant-card');
    fireEvent.click(playerCard!);
    
    const betInput = screen.getByPlaceholderText('Bet amount');
    fireEvent.change(betInput, { target: { value: '25' } });
    
    const placeBetButton = screen.getByRole('button', { name: /place bet/i });
    fireEvent.click(placeBetButton);
    
    await waitFor(() => {
      expect(apiClient.placeBet).toHaveBeenCalledWith({
        session_id: 'session-1',
        participant_id: 'p1',
        amount: 25,
      });
    });
  });

  it('prevents betting without authentication', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('Player One');
    
    // Should show sign in prompt instead of betting interface
    expect(screen.getByText('Sign in to place bets')).toBeInTheDocument();
    expect(screen.queryByText('Place Bet')).not.toBeInTheDocument();
  });

  it('prevents betting with insufficient balance', async () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser({ balance: 10 }),
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Player One');
    
    const betInput = screen.getByPlaceholderText('Bet amount');
    fireEvent.change(betInput, { target: { value: '25' } });
    
    expect(screen.getByText('Insufficient balance')).toBeInTheDocument();
    
    const placeBetButton = screen.getByRole('button', { name: /place bet/i });
    expect(placeBetButton).toBeDisabled();
  });

  it('shows live game state when available', async () => {
    const gameState = {
      currentRound: 3,
      totalRounds: 5,
      timeRemaining: 15,
      question: 'What is the capital of France?',
      currentLeader: 'Player Three',
    };

    updateMockStore({
      currentGameState: gameState,
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Epic Trivia Battle');
    
    expect(screen.getByText('Round 3 of 5')).toBeInTheDocument();
    expect(screen.getByText('15s remaining')).toBeInTheDocument();
    expect(screen.getByText('What is the capital of France?')).toBeInTheDocument();
    expect(screen.getByText('Leading: Player Three')).toBeInTheDocument();
  });

  it('displays live chat when enabled', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('Epic Trivia Battle');
    
    expect(screen.getByText('Live Chat')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
  });

  it('sends chat messages', async () => {
    const { apiClient } = require('@/lib/api');
    apiClient.sendSpectatorMessage.mockResolvedValue({ success: true });

    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Epic Trivia Battle');
    
    const chatInput = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(chatInput, { target: { value: 'Great game!' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(apiClient.sendSpectatorMessage).toHaveBeenCalledWith({
        session_id: 'session-1',
        message: 'Great game!',
      });
    });
  });

  it('updates odds in real-time', async () => {
    render(<LiveSpectatorPage />);
    
    await screen.findByText('2.5x');
    
    // Simulate real-time odds update
    const updatedSession = {
      ...mockSpectatorSession,
      participants: [
        ...mockSpectatorSession.participants.slice(0, 1),
        { ...mockSpectatorSession.participants[0], betting_odds: 2.1 }
      ]
    };
    
    // This would typically come through WebSocket
    // For testing, we can simulate the store update
    updateMockStore({
      currentSession: updatedSession,
    });
    
    await waitFor(() => {
      expect(screen.getByText('2.1x')).toBeInTheDocument();
    });
  });

  it('shows session ended state', async () => {
    const endedSession = {
      ...mockSpectatorSession,
      status: 'completed',
      winner: 'Player Three',
    };

    const { apiClient } = require('@/lib/api');
    apiClient.getSpectatorSession.mockResolvedValue(endedSession);

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Session Ended');
    expect(screen.getByText('Winner: Player Three')).toBeInTheDocument();
    expect(screen.getByText('View Results')).toBeInTheDocument();
  });

  it('handles connection issues gracefully', async () => {
    updateMockStore({
      isConnected: false,
      connectionError: 'Connection lost',
    });

    render(<LiveSpectatorPage />);
    
    await screen.findByText('Epic Trivia Battle');
    
    expect(screen.getByText('Connection lost')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reconnect/i })).toBeInTheDocument();
  });
});