/**
 * Custom Betting Platform - Performance Testing Utilities
 * =======================================================
 * 
 * Utilities for performance testing and monitoring.
 */

import { act, render, RenderResult } from '@testing-library/react';
import { performance, PerformanceObserver } from 'perf_hooks';

// Performance metrics collection
export class PerformanceMetrics {
  private metrics: Map<string, number[]> = new Map();
  private observer: PerformanceObserver | null = null;
  
  start() {
    if (typeof window !== 'undefined' && window.performance) {
      // Browser environment
      this.setupWebPerformanceObserver();
    } else {
      // Node.js environment
      this.setupNodePerformanceObserver();
    }
  }
  
  stop() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
  
  private setupWebPerformanceObserver() {
    if (!window.PerformanceObserver) return;
    
    this.observer = new window.PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric(entry.name, entry.duration);
      }
    });
    
    this.observer.observe({ entryTypes: ['measure', 'paint', 'layout-shift'] });
  }
  
  private setupNodePerformanceObserver() {
    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric(entry.name, entry.duration);
      }
    });
    
    this.observer.observe({ entryTypes: ['measure'] });
  }
  
  mark(name: string) {
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark(name);
    } else {
      performance.mark(name);
    }
  }
  
  measure(name: string, startMark: string, endMark?: string) {
    try {
      if (typeof window !== 'undefined' && window.performance) {
        window.performance.measure(name, startMark, endMark);
      } else {
        performance.measure(name, startMark, endMark);
      }
    } catch (error) {
      console.warn(`Failed to measure ${name}:`, error);
    }
  }
  
  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }
  
  getMetrics(name: string): {
    count: number;
    min: number;
    max: number;
    avg: number;
    median: number;
    p95: number;
  } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return null;
    
    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    const min = sorted[0];
    const max = sorted[count - 1];
    const avg = values.reduce((sum, val) => sum + val, 0) / count;
    const median = sorted[Math.floor(count / 2)];
    const p95 = sorted[Math.floor(count * 0.95)];
    
    return { count, min, max, avg, median, p95 };
  }
  
  getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [name] of this.metrics) {
      result[name] = this.getMetrics(name);
    }
    return result;
  }
  
  clear() {
    this.metrics.clear();
  }
}

// Component render performance testing
export async function measureRenderPerformance<T extends React.ComponentType<any>>(
  Component: T,
  props: React.ComponentProps<T>,
  iterations = 10
): Promise<{
  renderTime: { avg: number; min: number; max: number };
  mountTime: { avg: number; min: number; max: number };
  memoryUsage?: { before: number; after: number; delta: number };
}> {
  const renderTimes: number[] = [];
  const mountTimes: number[] = [];
  let memoryBefore = 0;
  let memoryAfter = 0;
  
  // Measure initial memory if available
  if (typeof window !== 'undefined' && (window as any).performance?.memory) {
    memoryBefore = (window as any).performance.memory.usedJSHeapSize;
  }
  
  for (let i = 0; i < iterations; i++) {
    const renderStart = performance.now();
    
    let component: RenderResult;
    await act(async () => {
      component = render(<Component {...props} />);
    });
    
    const renderEnd = performance.now();
    renderTimes.push(renderEnd - renderStart);
    
    // Measure mount time with a brief delay for DOM updates
    const mountStart = performance.now();
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });
    const mountEnd = performance.now();
    mountTimes.push(mountEnd - mountStart);
    
    // Cleanup
    component!.unmount();
    
    // Force garbage collection if available (for memory testing)
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  }
  
  // Measure final memory if available
  if (typeof window !== 'undefined' && (window as any).performance?.memory) {
    memoryAfter = (window as any).performance.memory.usedJSHeapSize;
  }
  
  const calculateStats = (times: number[]) => ({
    avg: times.reduce((sum, time) => sum + time, 0) / times.length,
    min: Math.min(...times),
    max: Math.max(...times)
  });
  
  const result: any = {
    renderTime: calculateStats(renderTimes),
    mountTime: calculateStats(mountTimes)
  };
  
  if (memoryAfter > 0) {
    result.memoryUsage = {
      before: memoryBefore,
      after: memoryAfter,
      delta: memoryAfter - memoryBefore
    };
  }
  
  return result;
}

// Large dataset performance testing
export async function measureLargeDatasetPerformance<T extends React.ComponentType<any>>(
  Component: T,
  baseProps: Partial<React.ComponentProps<T>>,
  dataGenerator: (size: number) => any,
  dataSizes: number[] = [10, 50, 100, 500, 1000]
): Promise<Array<{
  dataSize: number;
  renderTime: number;
  updateTime: number;
  memoryUsage?: number;
}>> {
  const results = [];
  
  for (const size of dataSizes) {
    const data = dataGenerator(size);
    const props = { ...baseProps, data } as React.ComponentProps<T>;
    
    let memoryBefore = 0;
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      memoryBefore = (window as any).performance.memory.usedJSHeapSize;
    }
    
    // Initial render
    const renderStart = performance.now();
    let component: RenderResult;
    await act(async () => {
      component = render(<Component {...props} />);
    });
    const renderEnd = performance.now();
    
    // Update with new data
    const newData = dataGenerator(size);
    const newProps = { ...baseProps, data: newData } as React.ComponentProps<T>;
    
    const updateStart = performance.now();
    await act(async () => {
      component!.rerender(<Component {...newProps} />);
    });
    const updateEnd = performance.now();
    
    let memoryAfter = 0;
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      memoryAfter = (window as any).performance.memory.usedJSHeapSize;
    }
    
    results.push({
      dataSize: size,
      renderTime: renderEnd - renderStart,
      updateTime: updateEnd - updateStart,
      memoryUsage: memoryAfter > 0 ? memoryAfter - memoryBefore : undefined
    });
    
    component!.unmount();
  }
  
  return results;
}

// Animation and interaction performance testing
export async function measureInteractionPerformance(
  component: RenderResult,
  interactions: Array<{
    name: string;
    action: () => Promise<void>;
  }>
): Promise<Record<string, { duration: number; fps?: number }>> {
  const results: Record<string, { duration: number; fps?: number }> = {};
  
  for (const interaction of interactions) {
    const startTime = performance.now();
    
    // Track animation frames if available
    let frameCount = 0;
    let animationId: number;
    
    if (typeof window !== 'undefined' && window.requestAnimationFrame) {
      const countFrames = () => {
        frameCount++;
        animationId = window.requestAnimationFrame(countFrames);
      };
      animationId = window.requestAnimationFrame(countFrames);
    }
    
    await act(async () => {
      await interaction.action();
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (typeof window !== 'undefined' && animationId!) {
      window.cancelAnimationFrame(animationId);
    }
    
    results[interaction.name] = {
      duration,
      fps: frameCount > 0 ? (frameCount / duration) * 1000 : undefined
    };
  }
  
  return results;
}

// Bundle size and loading performance
export class BundleAnalyzer {
  static async analyzeComponentBundle(componentPath: string): Promise<{
    size: number;
    gzipSize: number;
    dependencies: string[];
  }> {
    // This is a mock implementation for testing
    // In a real scenario, this would use webpack-bundle-analyzer or similar
    return {
      size: Math.floor(Math.random() * 100000) + 10000, // 10KB - 110KB
      gzipSize: Math.floor(Math.random() * 30000) + 3000, // 3KB - 33KB
      dependencies: ['react', 'react-dom', '@radix-ui/react-select', 'clsx']
    };
  }
  
  static async measureLoadingPerformance(): Promise<{
    domContentLoaded: number;
    fullyLoaded: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
  }> {
    const navigationTiming = typeof window !== 'undefined' ? window.performance?.getEntriesByType('navigation')[0] as PerformanceNavigationTiming : null;
    
    if (!navigationTiming) {
      return {
        domContentLoaded: 0,
        fullyLoaded: 0
      };
    }
    
    return {
      domContentLoaded: navigationTiming.domContentLoadedEventEnd - navigationTiming.navigationStart,
      fullyLoaded: navigationTiming.loadEventEnd - navigationTiming.navigationStart,
      firstContentfulPaint: this.getPaintMetric('first-contentful-paint'),
      largestContentfulPaint: this.getPaintMetric('largest-contentful-paint')
    };
  }
  
  private static getPaintMetric(name: string): number | undefined {
    if (typeof window === 'undefined' || !window.performance) return undefined;
    
    const paintEntries = window.performance.getEntriesByType('paint');
    const entry = paintEntries.find(e => e.name === name);
    return entry?.startTime;
  }
}

// Performance budget enforcement
export class PerformanceBudget {
  private budgets: Map<string, number> = new Map();
  
  setBudget(metric: string, budget: number) {
    this.budgets.set(metric, budget);
  }
  
  checkBudgets(metrics: Record<string, any>): {
    passed: boolean;
    violations: Array<{ metric: string; actual: number; budget: number }>;
  } {
    const violations = [];
    
    for (const [metric, budget] of this.budgets) {
      const actual = this.getMetricValue(metrics, metric);
      if (actual !== null && actual > budget) {
        violations.push({ metric, actual, budget });
      }
    }
    
    return {
      passed: violations.length === 0,
      violations
    };
  }
  
  private getMetricValue(metrics: Record<string, any>, path: string): number | null {
    const parts = path.split('.');
    let value = metrics;
    
    for (const part of parts) {
      value = value[part];
      if (value === undefined) return null;
    }
    
    return typeof value === 'number' ? value : null;
  }
}

// Web Vitals simulation for testing
export function simulateWebVitals(): {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
} {
  return {
    fcp: Math.random() * 2000 + 500, // 500-2500ms
    lcp: Math.random() * 3000 + 1000, // 1-4s
    fid: Math.random() * 100 + 10, // 10-110ms
    cls: Math.random() * 0.1, // 0-0.1
    ttfb: Math.random() * 800 + 200 // 200-1000ms
  };
}

// Performance test assertions
export const PerformanceAssertions = {
  renderTimeLessThan(actual: number, expected: number) {
    expect(actual).toBeLessThan(expected);
  },
  
  memoryUsageLessThan(actual: number, expected: number) {
    expect(actual).toBeLessThan(expected);
  },
  
  fpsGreaterThan(actual: number, expected: number) {
    expect(actual).toBeGreaterThan(expected);
  },
  
  webVitalsWithinBudget(vitals: ReturnType<typeof simulateWebVitals>) {
    expect(vitals.fcp).toBeLessThan(1800); // Good FCP < 1.8s
    expect(vitals.lcp).toBeLessThan(2500); // Good LCP < 2.5s
    expect(vitals.fid).toBeLessThan(100); // Good FID < 100ms
    expect(vitals.cls).toBeLessThan(0.1); // Good CLS < 0.1
    expect(vitals.ttfb).toBeLessThan(800); // Good TTFB < 800ms
  }
};

// Utility to create performance test suite
export function createPerformanceTestSuite(
  componentName: string,
  Component: React.ComponentType<any>,
  testProps: any,
  budgets?: Record<string, number>
) {
  return {
    [`${componentName} performance`]: async () => {
      const metrics = new PerformanceMetrics();
      metrics.start();
      
      const renderPerf = await measureRenderPerformance(Component, testProps, 5);
      
      metrics.stop();
      
      // Default performance budgets
      const budget = new PerformanceBudget();
      budget.setBudget('renderTime.avg', budgets?.renderTimeAvg || 50);
      budget.setBudget('mountTime.avg', budgets?.mountTimeAvg || 10);
      
      const budgetCheck = budget.checkBudgets(renderPerf);
      
      expect(budgetCheck.passed).toBe(true);
      if (!budgetCheck.passed) {
        console.warn(`Performance budget violations for ${componentName}:`, budgetCheck.violations);
      }
      
      return renderPerf;
    }
  };
}