/**
 * Expert Analyst Performance Tests
 * =================================
 * 
 * Performance tests to validate that the Expert Analyst module meets
 * the strict performance requirements:
 * - API responses: <50ms
 * - Page loads: <2 seconds
 * - WebSocket latency: <10ms
 * - Lighthouse scores: >90%
 */

import { performance } from 'perf_hooks';
import { renderHook, waitFor } from '@testing-library/react';
import { expertAnalystApi, useExpertAnalystApi } from '@/lib/expert-analyst-api';
import { mockExperts, mockPicks, mockAnalytics } from '../expert-analyst/mocks/expert-analyst-data';

// Mock axios for controlled performance testing
jest.mock('axios');

describe('Expert Analyst Performance Tests', () => {
  let mockAxiosInstance: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock axios instance with timing controls
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      }
    };
    
    require('axios').create.mockReturnValue(mockAxiosInstance);
  });

  describe('API Response Time Requirements', () => {
    it('should get experts list in under 50ms', async () => {
      // Mock fast response
      mockAxiosInstance.get.mockImplementation(() => 
        Promise.resolve({
          data: {
            experts: Object.values(mockExperts),
            total_count: Object.values(mockExperts).length
          }
        })
      );
      
      const startTime = performance.now();
      
      await expertAnalystApi.getExperts();
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/v1/experts', { params: undefined });
    });

    it('should get expert details in under 50ms', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: mockExperts.topPerformer
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.getExpert('expert-1');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });

    it('should get picks feed in under 50ms', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          picks: mockPicks,
          total_count: mockPicks.length
        }
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.getPicks();
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });

    it('should handle concurrent API calls efficiently', async () => {
      // Mock responses for all endpoints
      mockAxiosInstance.get.mockImplementation((url: string) => {
        if (url.includes('experts')) {
          return Promise.resolve({ data: { experts: Object.values(mockExperts) }});
        } else if (url.includes('picks')) {
          return Promise.resolve({ data: { picks: mockPicks }});
        } else if (url.includes('subscriptions')) {
          return Promise.resolve({ data: { subscriptions: [] }});
        }
        return Promise.resolve({ data: {} });
      });
      
      const startTime = performance.now();
      
      // Simulate concurrent dashboard load
      await Promise.all([
        expertAnalystApi.getExperts(),
        expertAnalystApi.getPicks(),
        expertAnalystApi.getUserSubscriptions(),
        expertAnalystApi.getUserDashboard()
      ]);
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      // Should complete all calls in under 200ms total
      expect(totalTime).toBeLessThan(200);
    });

    it('should maintain performance with large datasets', async () => {
      // Generate large dataset
      const largeExpertsList = Array.from({ length: 1000 }, (_, i) => ({
        ...mockExperts.topPerformer,
        id: `expert-${i}`,
        name: `Expert ${i}`
      }));
      
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          experts: largeExpertsList,
          total_count: largeExpertsList.length
        }
      });
      
      const startTime = performance.now();
      
      const response = await expertAnalystApi.getExperts({ limit: 1000 });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(100); // Allow slightly more time for large datasets
      expect(response.experts).toHaveLength(1000);
    });
  });

  describe('Payment Flow Performance', () => {
    it('should create payment intent quickly', async () => {
      mockAxiosInstance.post.mockResolvedValue({
        data: {
          client_secret: 'pi_test_secret',
          payment_intent_id: 'pi_test',
          amount: 14999,
          currency: 'usd'
        }
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.createPaymentIntent({
        expert_id: 'expert-1',
        tier_id: 'tier-1',
        amount: 14999
      });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });

    it('should confirm payment within performance threshold', async () => {
      mockAxiosInstance.post.mockResolvedValue({
        data: {
          subscription: {
            id: 'sub-1',
            status: 'active'
          },
          status: 'succeeded'
        }
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.confirmPayment('pi_test');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });
  });

  describe('Search and Filtering Performance', () => {
    it('should perform expert search quickly', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: Object.values(mockExperts).filter(e => 
          e.name.toLowerCase().includes('michael')
        )
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.searchExperts('Michael');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });

    it('should handle complex filter combinations efficiently', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          experts: [mockExperts.topPerformer],
          total_count: 1
        }
      });
      
      const complexFilters = {
        category: 'sports',
        specialization: 'NFL',
        sort_by: 'roi' as const,
        search: 'expert',
        min_rating: 4.5,
        verified_only: true
      };
      
      const startTime = performance.now();
      
      await expertAnalystApi.getExperts(complexFilters);
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(75); // Allow slightly more for complex queries
    });
  });

  describe('Analytics Performance', () => {
    it('should load expert analytics quickly', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: mockAnalytics['expert-1']
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.getExpertAnalytics('expert-1');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });

    it('should handle dashboard aggregations efficiently', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          subscriptions: [],
          recent_picks: mockPicks.slice(0, 5),
          performance_summary: {
            total_invested: 10000,
            total_profit: 1500,
            roi: 0.15,
            win_rate: 0.65
          }
        }
      });
      
      const startTime = performance.now();
      
      await expertAnalystApi.getUserDashboard();
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(50);
    });
  });

  describe('React Hook Performance', () => {
    it('should initialize useExpertAnalystApi hook quickly', async () => {
      const mockGetToken = jest.fn().mockResolvedValue('test-token');
      
      // Mock useAuth
      jest.doMock('@clerk/nextjs', () => ({
        useAuth: () => ({ getToken: mockGetToken })
      }));
      
      const startTime = performance.now();
      
      const { result } = renderHook(() => useExpertAnalystApi());
      
      await waitFor(() => {
        expect(result.current).toBeDefined();
      });
      
      const endTime = performance.now();
      const initTime = endTime - startTime;
      
      expect(initTime).toBeLessThan(100);
      expect(mockGetToken).toHaveBeenCalled();
    });
  });

  describe('Memory Performance', () => {
    it('should not cause memory leaks with repeated API calls', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: { experts: [mockExperts.topPerformer] }
      });
      
      // Get initial memory usage
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple API calls
      for (let i = 0; i < 100; i++) {
        await expertAnalystApi.getExperts();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should handle large pick datasets without excessive memory usage', async () => {
      // Generate large picks dataset
      const largePicks = Array.from({ length: 10000 }, (_, i) => ({
        ...mockPicks[0],
        id: `pick-${i}`,
        title: `Pick ${i}`
      }));
      
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          picks: largePicks,
          total_count: largePicks.length
        }
      });
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      const response = await expertAnalystApi.getPicks({ limit: 10000 });
      
      const afterLoadMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = afterLoadMemory - initialMemory;
      
      expect(response.picks).toHaveLength(10000);
      // Should not use more than 50MB for 10k picks
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Error Handling Performance', () => {
    it('should handle API errors quickly without hanging', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'));
      
      const startTime = performance.now();
      
      try {
        await expertAnalystApi.getExperts();
      } catch (error) {
        // Expected to throw
      }
      
      const endTime = performance.now();
      const errorTime = endTime - startTime;
      
      // Error handling should be fast
      expect(errorTime).toBeLessThan(100);
    });

    it('should timeout appropriately for hung requests', async () => {
      // Mock a request that never resolves
      mockAxiosInstance.get.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      );
      
      const startTime = performance.now();
      
      try {
        await expertAnalystApi.getExperts();
      } catch (error) {
        // Expected to timeout
      }
      
      const endTime = performance.now();
      const timeoutDuration = endTime - startTime;
      
      // Should timeout around 15 seconds (configured timeout)
      expect(timeoutDuration).toBeGreaterThan(14000);
      expect(timeoutDuration).toBeLessThan(16000);
    });
  });

  describe('Bundle Size Impact', () => {
    it('should have reasonable bundle size for expert analyst module', () => {
      // This test would be enhanced with actual bundle analysis
      // For now, we verify that the module exports are tree-shakeable
      
      const { expertAnalystApi, useExpertAnalystApi } = require('@/lib/expert-analyst-api');
      
      expect(expertAnalystApi).toBeDefined();
      expect(useExpertAnalystApi).toBeDefined();
      
      // Verify that unused methods don't get imported
      const moduleKeys = Object.keys(expertAnalystApi);
      expect(moduleKeys.length).toBeGreaterThan(20); // Has all methods
      expect(moduleKeys.length).toBeLessThan(50); // Not bloated
    });
  });

  describe('Network Efficiency', () => {
    it('should minimize redundant API calls', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: { experts: [mockExperts.topPerformer] }
      });
      
      // Multiple calls to the same endpoint
      await Promise.all([
        expertAnalystApi.getExpert('expert-1'),
        expertAnalystApi.getExpert('expert-1'),
        expertAnalystApi.getExpert('expert-1')
      ]);
      
      // Should only make one actual API call (if caching is implemented)
      // This test documents expected behavior for future caching implementation
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(3);
    });

    it('should batch similar requests when possible', async () => {
      // This test documents expected behavior for future request batching
      mockAxiosInstance.get.mockResolvedValue({
        data: { experts: Object.values(mockExperts) }
      });
      
      const requests = [
        expertAnalystApi.getExpert('expert-1'),
        expertAnalystApi.getExpert('expert-2'),
        expertAnalystApi.getExpert('expert-3')
      ];
      
      await Promise.all(requests);
      
      // Currently makes separate calls - future optimization opportunity
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(3);
    });
  });
});