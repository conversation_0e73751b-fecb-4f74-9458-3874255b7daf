/**
 * Custom Betting Platform - Component Performance Tests
 * =====================================================
 * 
 * Performance tests for Custom Betting Platform components.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  PerformanceMetrics,
  measureRenderPerformance,
  measureLargeDatasetPerformance,
  measureInteractionPerformance,
  BundleAnalyzer,
  PerformanceBudget,
  PerformanceAssertions,
  simulateWebVitals
} from './performance-utils';

// Import components to test
import { BettingInterface } from '@/components/custom-betting/BettingInterface';
import { MarketBrowser } from '@/components/custom-betting/MarketBrowser';
import { CreateBetWizard } from '@/components/custom-betting/CreateBetWizard';
import { MarketOutcomes } from '@/components/custom-betting/MarketOutcomes';
import { UserBetManager } from '@/components/custom-betting/UserBetManager';

import { 
  mockBinaryMarket, 
  mockMultiChoiceMarket, 
  mockUserParticipations,
  generateMockMarket,
  generateMockParticipation 
} from '../mocks/custom-betting-data';

describe('Component Performance Tests', () => {
  let performanceMetrics: PerformanceMetrics;
  let performanceBudget: PerformanceBudget;

  beforeEach(() => {
    performanceMetrics = new PerformanceMetrics();
    performanceBudget = new PerformanceBudget();
    
    // Set performance budgets
    performanceBudget.setBudget('renderTime.avg', 50); // 50ms average render time
    performanceBudget.setBudget('mountTime.avg', 10); // 10ms average mount time
    performanceBudget.setBudget('memoryUsage.delta', 5 * 1024 * 1024); // 5MB memory increase
  });

  describe('BettingInterface Performance', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      userParticipation: null,
      onBetPlaced: jest.fn()
    };

    it('renders within performance budget', async () => {
      const renderPerf = await measureRenderPerformance(BettingInterface, defaultProps, 10);
      
      const budgetCheck = performanceBudget.checkBudgets(renderPerf);
      expect(budgetCheck.passed).toBe(true);
      
      if (!budgetCheck.passed) {
        console.warn('BettingInterface performance budget violations:', budgetCheck.violations);
      }
      
      // Specific assertions
      PerformanceAssertions.renderTimeLessThan(renderPerf.renderTime.avg, 50);
      expect(renderPerf.renderTime.max).toBeLessThan(100);
      expect(renderPerf.mountTime.avg).toBeLessThan(15);
    });

    it('handles rapid form updates efficiently', async () => {
      const component = render(<BettingInterface {...defaultProps} />);
      
      const interactions = [
        {
          name: 'stake_input_rapid_updates',
          action: async () => {
            const user = userEvent.setup();
            const stakeInput = screen.getByPlaceholderText('0.00');
            
            // Rapid typing simulation
            for (let i = 0; i < 10; i++) {
              await user.type(stakeInput, '1');
              await user.keyboard('{Backspace}');
            }
          }
        },
        {
          name: 'odds_slider_updates',
          action: async () => {
            const user = userEvent.setup();
            const oddsInput = screen.getByPlaceholderText('2.00');
            
            // Rapid odds changes
            for (let i = 0; i < 5; i++) {
              await user.clear(oddsInput);
              await user.type(oddsInput, (2.0 + i * 0.1).toString());
            }
          }
        }
      ];

      const interactionPerf = await measureInteractionPerformance(component, interactions);
      
      // Each interaction should complete within reasonable time
      expect(interactionPerf.stake_input_rapid_updates.duration).toBeLessThan(500);
      expect(interactionPerf.odds_slider_updates.duration).toBeLessThan(300);
      
      component.unmount();
    });

    it('maintains performance with complex calculations', async () => {
      const complexMarket = {
        ...mockMultiChoiceMarket,
        outcomes: Array.from({ length: 20 }, (_, i) => ({
          id: `outcome-${i}`,
          outcome_text: `Option ${i + 1}`,
          current_odds: 2.0 + Math.random(),
          implied_probability: 1 / 20,
          total_backing: Math.floor(Math.random() * 10000),
          total_laying: Math.floor(Math.random() * 10000),
          is_winning_outcome: false
        }))
      };

      const renderPerf = await measureRenderPerformance(
        BettingInterface, 
        { ...defaultProps, market: complexMarket }, 
        5
      );
      
      // Should still render efficiently even with complex market
      expect(renderPerf.renderTime.avg).toBeLessThan(100);
      expect(renderPerf.renderTime.max).toBeLessThan(200);
    });
  });

  describe('MarketBrowser Performance', () => {
    it('renders within performance budget', async () => {
      const renderPerf = await measureRenderPerformance(MarketBrowser, {}, 5);
      
      PerformanceAssertions.renderTimeLessThan(renderPerf.renderTime.avg, 80);
      expect(renderPerf.renderTime.max).toBeLessThan(150);
    });

    it('handles large market datasets efficiently', async () => {
      const dataGenerator = (size: number) => 
        Array.from({ length: size }, (_, i) => generateMockMarket({
          id: `market-${i}`,
          title: `Market ${i}`,
          total_stakes: Math.floor(Math.random() * 100000),
          total_participants: Math.floor(Math.random() * 1000)
        }));

      const datasetPerf = await measureLargeDatasetPerformance(
        MarketBrowser,
        {},
        dataGenerator,
        [10, 50, 100, 250]
      );

      // Performance should scale reasonably with data size
      const smallDataset = datasetPerf.find(r => r.dataSize === 10);
      const largeDataset = datasetPerf.find(r => r.dataSize === 250);

      if (smallDataset && largeDataset) {
        // Large dataset shouldn't be more than 5x slower than small dataset
        expect(largeDataset.renderTime).toBeLessThan(smallDataset.renderTime * 5);
        expect(largeDataset.updateTime).toBeLessThan(smallDataset.updateTime * 3);
      }

      // Absolute limits
      datasetPerf.forEach(result => {
        expect(result.renderTime).toBeLessThan(500); // 500ms max render time
        expect(result.updateTime).toBeLessThan(200); // 200ms max update time
      });
    });

    it('handles search and filtering efficiently', async () => {
      const component = render(<MarketBrowser />);
      
      const interactions = [
        {
          name: 'search_typing',
          action: async () => {
            const user = userEvent.setup();
            const searchInput = screen.getByPlaceholderText('Search markets...');
            await user.type(searchInput, 'bitcoin ethereum crypto');
          }
        },
        {
          name: 'category_filtering',
          action: async () => {
            const user = userEvent.setup();
            const categoryFilter = screen.getByText('All Categories');
            await user.click(categoryFilter);
            
            // Try to click different categories
            const cryptoOption = screen.queryByText('Crypto');
            if (cryptoOption) {
              await user.click(cryptoOption);
            }
          }
        },
        {
          name: 'view_mode_switch',
          action: async () => {
            const user = userEvent.setup();
            const listViewButton = screen.queryByRole('button', { name: /list view/i });
            const gridViewButton = screen.queryByRole('button', { name: /grid view/i });
            
            if (listViewButton) await user.click(listViewButton);
            if (gridViewButton) await user.click(gridViewButton);
          }
        }
      ];

      const interactionPerf = await measureInteractionPerformance(component, interactions);
      
      // All interactions should be responsive
      Object.values(interactionPerf).forEach(perf => {
        expect(perf.duration).toBeLessThan(300);
      });
      
      component.unmount();
    });

    it('virtualizes large lists efficiently', async () => {
      // Test with very large dataset to ensure virtualization is working
      const largeDataset = Array.from({ length: 10000 }, (_, i) => generateMockMarket({
        id: `market-${i}`,
        title: `Market ${i}`,
        description: `Description for market ${i}`
      }));

      const renderStart = performance.now();
      const component = render(<MarketBrowser markets={largeDataset} />);
      const renderEnd = performance.now();

      // Should render quickly even with massive dataset due to virtualization
      expect(renderEnd - renderStart).toBeLessThan(200);
      
      // DOM should not contain all 10000 items
      const marketCards = component.container.querySelectorAll('[data-testid*="market-card"]');
      expect(marketCards.length).toBeLessThan(100); // Should only render visible items
      
      component.unmount();
    });
  });

  describe('CreateBetWizard Performance', () => {
    const defaultProps = {
      onComplete: jest.fn(),
      onCancel: jest.fn()
    };

    it('renders within performance budget', async () => {
      const renderPerf = await measureRenderPerformance(CreateBetWizard, defaultProps, 5);
      
      PerformanceAssertions.renderTimeLessThan(renderPerf.renderTime.avg, 100);
      expect(renderPerf.renderTime.max).toBeLessThan(200);
    });

    it('handles wizard navigation efficiently', async () => {
      const component = render(<CreateBetWizard {...defaultProps} />);
      
      const interactions = [
        {
          name: 'form_filling_step1',
          action: async () => {
            const user = userEvent.setup();
            await user.type(screen.getByLabelText('Market Title'), 'Performance Test Market');
            await user.type(screen.getByLabelText('Description'), 'Testing wizard performance with form filling and validation');
          }
        },
        {
          name: 'step_navigation',
          action: async () => {
            const user = userEvent.setup();
            // Complete step 1
            const categorySelect = screen.getByText('Select Category');
            await user.click(categorySelect);
            await user.click(screen.getByText('Other'));
            
            await user.click(screen.getByRole('button', { name: /continue/i }));
          }
        }
      ];

      const interactionPerf = await measureInteractionPerformance(component, interactions);
      
      // Form interactions should be responsive
      expect(interactionPerf.form_filling_step1.duration).toBeLessThan(400);
      expect(interactionPerf.step_navigation.duration).toBeLessThan(200);
      
      component.unmount();
    });

    it('handles dynamic outcome management efficiently', async () => {
      const component = render(<CreateBetWizard {...defaultProps} />);
      
      // Navigate to step 2
      const user = userEvent.setup();
      await user.type(screen.getByLabelText('Market Title'), 'Multi Choice Test');
      await user.type(screen.getByLabelText('Description'), 'Testing outcome management performance');
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Sports'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Select multiple choice
      await user.click(screen.getByRole('button', { name: /multiple choice/i }));
      
      const interaction = {
        name: 'outcome_management',
        action: async () => {
          // Add many outcomes rapidly
          for (let i = 0; i < 10; i++) {
            const addButton = screen.getByRole('button', { name: /add outcome/i });
            await user.click(addButton);
            
            const newInputs = screen.getAllByPlaceholderText('Enter outcome option');
            const lastInput = newInputs[newInputs.length - 1];
            await user.type(lastInput, `Outcome ${i + 4}`);
          }
          
          // Remove some outcomes
          const removeButtons = screen.getAllByRole('button', { name: /remove outcome/i });
          for (let i = 0; i < 3 && i < removeButtons.length; i++) {
            await user.click(removeButtons[i]);
          }
        }
      };

      const interactionPerf = await measureInteractionPerformance(component, [interaction]);
      
      // Dynamic outcome management should be efficient
      expect(interactionPerf.outcome_management.duration).toBeLessThan(1000);
      
      component.unmount();
    });
  });

  describe('MarketOutcomes Performance', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      onOutcomeSelect: jest.fn(),
      userParticipation: null
    };

    it('renders within performance budget', async () => {
      const renderPerf = await measureRenderPerformance(MarketOutcomes, defaultProps, 10);
      
      PerformanceAssertions.renderTimeLessThan(renderPerf.renderTime.avg, 40);
      expect(renderPerf.renderTime.max).toBeLessThan(80);
    });

    it('handles real-time odds updates efficiently', async () => {
      let updateCount = 0;
      const component = render(<MarketOutcomes {...defaultProps} />);
      
      const interaction = {
        name: 'odds_updates',
        action: async () => {
          // Simulate rapid odds updates
          for (let i = 0; i < 20; i++) {
            updateCount++;
            const updatedMarket = {
              ...mockBinaryMarket,
              outcomes: mockBinaryMarket.outcomes.map(outcome => ({
                ...outcome,
                current_odds: outcome.current_odds + (Math.random() - 0.5) * 0.1,
                implied_probability: Math.random() * 0.1 + 0.45
              }))
            };
            
            component.rerender(<MarketOutcomes {...defaultProps} market={updatedMarket} />);
            
            // Small delay to simulate real-time updates
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      };

      const interactionPerf = await measureInteractionPerformance(component, [interaction]);
      
      // Should handle rapid updates efficiently
      expect(interactionPerf.odds_updates.duration).toBeLessThan(600);
      
      // FPS should be maintained during updates
      if (interactionPerf.odds_updates.fps) {
        expect(interactionPerf.odds_updates.fps).toBeGreaterThan(30);
      }
      
      component.unmount();
    });

    it('scales with number of outcomes', async () => {
      const dataGenerator = (size: number) => ({
        ...mockMultiChoiceMarket,
        outcomes: Array.from({ length: size }, (_, i) => ({
          id: `outcome-${i}`,
          outcome_text: `Outcome ${i + 1}`,
          current_odds: 2.0 + Math.random(),
          implied_probability: 1 / size,
          total_backing: Math.floor(Math.random() * 10000),
          total_laying: Math.floor(Math.random() * 5000),
          is_winning_outcome: false
        }))
      });

      const datasetPerf = await measureLargeDatasetPerformance(
        MarketOutcomes,
        defaultProps,
        dataGenerator,
        [5, 10, 20, 50]
      );

      // Performance should scale reasonably with number of outcomes
      datasetPerf.forEach(result => {
        expect(result.renderTime).toBeLessThan(150);
        expect(result.updateTime).toBeLessThan(100);
      });
    });
  });

  describe('UserBetManager Performance', () => {
    it('renders within performance budget', async () => {
      const props = {
        userBets: mockUserParticipations.map(p => ({
          ...p,
          market_title: 'Test Market'
        }))
      };

      const renderPerf = await measureRenderPerformance(UserBetManager, props, 5);
      
      PerformanceAssertions.renderTimeLessThan(renderPerf.renderTime.avg, 60);
      expect(renderPerf.renderTime.max).toBeLessThan(120);
    });

    it('handles large bet portfolios efficiently', async () => {
      const dataGenerator = (size: number) => 
        Array.from({ length: size }, (_, i) => ({
          ...generateMockParticipation({
            id: `participation-${i}`,
            bet_id: `market-${i}`,
            stake_amount: Math.floor(Math.random() * 1000) + 10
          }),
          market_title: `Market ${i}`
        }));

      const datasetPerf = await measureLargeDatasetPerformance(
        UserBetManager,
        {},
        dataGenerator,
        [10, 50, 100, 200]
      );

      // Should handle large portfolios efficiently
      datasetPerf.forEach(result => {
        expect(result.renderTime).toBeLessThan(300);
        expect(result.updateTime).toBeLessThan(150);
      });

      // Performance should scale sub-linearly
      const small = datasetPerf.find(r => r.dataSize === 10);
      const large = datasetPerf.find(r => r.dataSize === 200);
      
      if (small && large) {
        expect(large.renderTime).toBeLessThan(small.renderTime * 10); // Should be better than linear scaling
      }
    });

    it('handles real-time bet updates efficiently', async () => {
      const initialBets = Array.from({ length: 20 }, (_, i) => ({
        ...generateMockParticipation({
          id: `participation-${i}`,
          status: Math.random() > 0.5 ? 'pending' : 'active'
        }),
        market_title: `Market ${i}`
      }));

      const component = render(<UserBetManager userBets={initialBets} />);
      
      const interaction = {
        name: 'bet_status_updates',
        action: async () => {
          // Simulate status updates for multiple bets
          for (let update = 0; update < 10; update++) {
            const updatedBets = initialBets.map(bet => ({
              ...bet,
              status: Math.random() > 0.5 ? 'active' : 'pending',
              matched_amount: Math.floor(Math.random() * bet.stake_amount)
            }));
            
            component.rerender(<UserBetManager userBets={updatedBets as any} />);
            await new Promise(resolve => setTimeout(resolve, 20));
          }
        }
      };

      const interactionPerf = await measureInteractionPerformance(component, [interaction]);
      
      expect(interactionPerf.bet_status_updates.duration).toBeLessThan(400);
      
      component.unmount();
    });
  });

  describe('Bundle Size and Loading Performance', () => {
    it('has acceptable bundle sizes', async () => {
      const components = [
        'BettingInterface',
        'MarketBrowser',
        'CreateBetWizard',
        'MarketOutcomes',
        'UserBetManager'
      ];

      for (const componentName of components) {
        const analysis = await BundleAnalyzer.analyzeComponentBundle(componentName);
        
        // Bundle size budgets
        expect(analysis.size).toBeLessThan(100 * 1024); // 100KB uncompressed
        expect(analysis.gzipSize).toBeLessThan(30 * 1024); // 30KB gzipped
        
        // Should not have excessive dependencies
        expect(analysis.dependencies.length).toBeLessThan(20);
      }
    });

    it('meets loading performance budgets', async () => {
      const loadingPerf = await BundleAnalyzer.measureLoadingPerformance();
      
      // Loading time budgets
      expect(loadingPerf.domContentLoaded).toBeLessThan(2000); // 2s
      expect(loadingPerf.fullyLoaded).toBeLessThan(4000); // 4s
      
      if (loadingPerf.firstContentfulPaint) {
        expect(loadingPerf.firstContentfulPaint).toBeLessThan(1500); // 1.5s
      }
      
      if (loadingPerf.largestContentfulPaint) {
        expect(loadingPerf.largestContentfulPaint).toBeLessThan(2500); // 2.5s
      }
    });
  });

  describe('Web Vitals Performance', () => {
    it('meets Core Web Vitals benchmarks', async () => {
      // Simulate Web Vitals measurements
      const vitals = simulateWebVitals();
      
      // Use performance assertions to check Web Vitals
      PerformanceAssertions.webVitalsWithinBudget(vitals);
      
      // Additional custom checks
      expect(vitals.fcp).toBeGreaterThan(0);
      expect(vitals.lcp).toBeGreaterThan(vitals.fcp);
      expect(vitals.fid).toBeGreaterThan(0);
      expect(vitals.cls).toBeGreaterThanOrEqual(0);
      expect(vitals.ttfb).toBeGreaterThan(0);
    });
  });

  describe('Memory Leak Detection', () => {
    it('does not leak memory during component lifecycle', async () => {
      // This test would be more meaningful in a browser environment
      // with actual memory measurement capabilities
      
      const components = [
        { Component: BettingInterface, props: { market: mockBinaryMarket, onBetPlaced: jest.fn() } },
        { Component: MarketBrowser, props: {} },
        { Component: CreateBetWizard, props: { onComplete: jest.fn(), onCancel: jest.fn() } }
      ];

      for (const { Component, props } of components) {
        let memoryBefore = 0;
        let memoryAfter = 0;
        
        if (typeof window !== 'undefined' && (window as any).performance?.memory) {
          memoryBefore = (window as any).performance.memory.usedJSHeapSize;
        }

        // Mount and unmount multiple times
        for (let i = 0; i < 10; i++) {
          const component = render(<Component {...props} />);
          component.unmount();
          
          // Force garbage collection if available
          if (typeof window !== 'undefined' && (window as any).gc) {
            (window as any).gc();
          }
        }

        if (typeof window !== 'undefined' && (window as any).performance?.memory) {
          memoryAfter = (window as any).performance.memory.usedJSHeapSize;
          const memoryGrowth = memoryAfter - memoryBefore;
          
          // Memory growth should be minimal after cleanup
          expect(memoryGrowth).toBeLessThan(1024 * 1024); // Less than 1MB growth
        }
      }
    });
  });
});