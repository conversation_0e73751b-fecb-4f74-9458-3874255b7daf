/**
 * BetBet Gaming Engine - Simple Navigation Component Tests
 * ========================================================
 */

import { render, screen } from '../utils/simple-test-utils';
import Navigation from '@/components/layout/Navigation';

// Mock the store
jest.mock('@/store/gameStore');

describe('Navigation Component', () => {
  it('renders the BetBet brand logo', () => {
    render(<Navigation />);
    
    expect(screen.getByText('BetBet')).toBeInTheDocument();
    expect(screen.getByText('Gaming Engine')).toBeInTheDocument();
  });

  it('shows navigation links', () => {
    render(<Navigation />);
    
    expect(screen.getByRole('link', { name: /games/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sessions/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /tournaments/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /leaderboard/i })).toBeInTheDocument();
  });

  it('shows search bar', () => {
    render(<Navigation />);
    
    const searchInput = screen.getByPlaceholderText(/search games, players/i);
    expect(searchInput).toBeInTheDocument();
  });

  it('shows sign in/up buttons', () => {
    render(<Navigation />);
    
    expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();
  });
});