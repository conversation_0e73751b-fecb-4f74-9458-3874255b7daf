/**
 * BetPlacementModal Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BetPlacementModal } from '@/components/betting/BetPlacementModal';
import { useApi } from '@/hooks/useApi';
import { useAuth } from '@clerk/nextjs';

// Mock hooks
jest.mock('@/hooks/useApi');
jest.mock('@clerk/nextjs');

const mockApi = {
  getProfile: jest.fn(),
  placeBet: jest.fn(),
};

const mockAuth = {
  user: { id: 'user-123' },
  isSignedIn: true,
  getToken: jest.fn(),
};

describe('BetPlacementModal', () => {
  const mockMarket = {
    id: 'market-1',
    name: 'Match Winner',
    description: 'Who will win the match?',
    market_type: 'match_winner',
    status: 'open',
    total_pool: 1000,
    total_bets: 10,
    outcomes: [],
    created_at: new Date().toISOString(),
  };

  const mockOutcome = {
    id: 'outcome-1',
    name: 'Player 1',
    description: 'Player 1 wins',
    odds: 2.5,
    implied_probability: 40,
    total_bet_amount: 500,
    bet_count: 5,
    is_active: true,
    outcome_type: 'win',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useApi as jest.Mock).mockReturnValue(mockApi);
    (useAuth as jest.Mock).mockReturnValue(mockAuth);
  });

  it('should not render when market or outcome is null', () => {
    const { container } = render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={null}
        outcome={null}
      />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render bet placement form when open', () => {
    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    expect(screen.getByText('Place Bet')).toBeInTheDocument();
    expect(screen.getByText(/Place your bet on "Player 1"/)).toBeInTheDocument();
    expect(screen.getByLabelText('Bet Amount ($)')).toBeInTheDocument();
  });

  it('should load user balance on mount', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    await waitFor(() => {
      expect(mockApi.getProfile).toHaveBeenCalled();
      expect(screen.getByText('$1,000')).toBeInTheDocument();
    });
  });

  it('should show error state when balance fails to load', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockApi.getProfile.mockRejectedValue(new Error('API Error'));

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('$0')).toBeInTheDocument();
    });

    // In development, should show debug tip
    if (process.env.NODE_ENV === 'development') {
      expect(screen.getByText(/Dev tip: Balance is 0 due to API error/)).toBeInTheDocument();
    }

    consoleErrorSpy.mockRestore();
  });

  it('should update bet amount and calculate payout', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    const input = screen.getByLabelText('Bet Amount ($)');
    fireEvent.change(input, { target: { value: '100' } });

    await waitFor(() => {
      expect(screen.getByText('$250.00')).toBeInTheDocument(); // Payout
      expect(screen.getByText('$150.00')).toBeInTheDocument(); // Profit
    });
  });

  it('should use preset amount buttons', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    await waitFor(() => {
      const button25 = screen.getByRole('button', { name: '$25' });
      fireEvent.click(button25);
    });

    const input = screen.getByLabelText('Bet Amount ($)');
    expect(input).toHaveValue(25);
  });

  it('should validate bet amount', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 100 });

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    const input = screen.getByLabelText('Bet Amount ($)');
    const placeButton = screen.getByRole('button', { name: /place bet/i });

    // Test insufficient balance
    fireEvent.change(input, { target: { value: '200' } });
    fireEvent.click(placeButton);

    await waitFor(() => {
      expect(screen.getByText('Insufficient balance')).toBeInTheDocument();
    });

    // Test minimum bet
    fireEvent.change(input, { target: { value: '0.5' } });
    fireEvent.click(placeButton);

    await waitFor(() => {
      expect(screen.getByText('Minimum bet amount is $1')).toBeInTheDocument();
    });

    // Test maximum bet
    fireEvent.change(input, { target: { value: '1500' } });
    fireEvent.click(placeButton);

    await waitFor(() => {
      expect(screen.getByText('Maximum bet amount is $1000')).toBeInTheDocument();
    });
  });

  it('should place bet successfully', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });
    mockApi.placeBet.mockResolvedValue({ id: 'bet-1', status: 'accepted' });

    const onBetPlaced = jest.fn();
    const onClose = jest.fn();

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={onClose}
        market={mockMarket}
        outcome={mockOutcome}
        onBetPlaced={onBetPlaced}
      />
    );

    await waitFor(() => {
      const input = screen.getByLabelText('Bet Amount ($)');
      fireEvent.change(input, { target: { value: '50' } });
    });

    const placeButton = screen.getByRole('button', { name: /place bet.*50/i });
    fireEvent.click(placeButton);

    await waitFor(() => {
      expect(mockApi.placeBet).toHaveBeenCalledWith({
        market_id: 'market-1',
        outcome_id: 'outcome-1',
        bet_amount: 50,
      });
      expect(onBetPlaced).toHaveBeenCalledWith({ id: 'bet-1', status: 'accepted' });
      expect(screen.getByText('Bet Placed Successfully!')).toBeInTheDocument();
    });

    // Should close after delay
    await waitFor(() => {
      expect(onClose).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('should handle bet placement errors', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });
    mockApi.placeBet.mockRejectedValue(new Error('Betting is closed'));

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    await waitFor(() => {
      const input = screen.getByLabelText('Bet Amount ($)');
      fireEvent.change(input, { target: { value: '50' } });
    });

    const placeButton = screen.getByRole('button', { name: /place bet.*50/i });
    fireEvent.click(placeButton);

    await waitFor(() => {
      expect(screen.getByText('Betting is closed')).toBeInTheDocument();
    });

    consoleErrorSpy.mockRestore();
  });

  it('should display market closure warning', () => {
    const futureDate = new Date(Date.now() + 3600000).toISOString(); // 1 hour from now
    const marketWithCloseTime = { ...mockMarket, closes_at: futureDate };

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={marketWithCloseTime}
        outcome={mockOutcome}
      />
    );

    expect(screen.getByText(/Betting closes at/)).toBeInTheDocument();
  });

  it('should disable buttons when placing bet', async () => {
    mockApi.getProfile.mockResolvedValue({ balance: 1000 });
    mockApi.placeBet.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(
      <BetPlacementModal
        isOpen={true}
        onClose={jest.fn()}
        market={mockMarket}
        outcome={mockOutcome}
      />
    );

    await waitFor(() => {
      const input = screen.getByLabelText('Bet Amount ($)');
      fireEvent.change(input, { target: { value: '50' } });
    });

    const placeButton = screen.getByRole('button', { name: /place bet.*50/i });
    const cancelButton = screen.getByText('Cancel');

    fireEvent.click(placeButton);

    expect(screen.getByText('Placing Bet...')).toBeInTheDocument();
    expect(placeButton).toBeDisabled();
    expect(cancelButton).toBeDisabled();
  });
});