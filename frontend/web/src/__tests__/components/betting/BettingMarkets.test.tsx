/**
 * BettingMarkets Component Tests
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BettingMarkets } from '@/components/betting/BettingMarkets';
import { useApi } from '@/hooks/useApi';
import { useBettingWebSocket } from '@/hooks/useBettingWebSocket';

// Mock hooks
jest.mock('@/hooks/useApi');
jest.mock('@/hooks/useBettingWebSocket');
jest.mock('@clerk/nextjs', () => ({
  useAuth: () => ({ isSignedIn: true, user: { id: 'user-123' } }),
}));

const mockApi = {
  getBettingMarkets: jest.fn(),
  placeBet: jest.fn(),
};

const mockWebSocket = {
  isConnected: true,
  markets: [],
  subscribeToMarket: jest.fn(),
  unsubscribeFromMarket: jest.fn(),
};

describe('BettingMarkets Component', () => {
  const mockMarkets = [
    {
      id: 'market-1',
      name: 'Match Winner',
      description: 'Who will win this match?',
      market_type: 'match_winner',
      status: 'open',
      total_pool: 500,
      total_bets: 10,
      outcomes: [
        {
          id: 'outcome-1',
          name: 'Team A',
          odds: 1.8,
          implied_probability: 55.56,
          total_bet_amount: 300,
          bet_count: 6,
          is_active: true,
          outcome_type: 'win',
        },
        {
          id: 'outcome-2',
          name: 'Team B',
          odds: 2.2,
          implied_probability: 45.45,
          total_bet_amount: 200,
          bet_count: 4,
          is_active: true,
          outcome_type: 'win',
        },
      ],
      created_at: new Date().toISOString(),
      session_id: 'session-123',
    },
    {
      id: 'market-2',
      name: 'First to Score',
      description: 'Which player will score first?',
      market_type: 'player_performance',
      status: 'open',
      total_pool: 300,
      total_bets: 15,
      outcomes: [
        {
          id: 'outcome-3',
          name: 'Player 1',
          odds: 3.5,
          implied_probability: 28.57,
          total_bet_amount: 100,
          bet_count: 5,
          is_active: true,
          outcome_type: 'player',
        },
        {
          id: 'outcome-4',
          name: 'Player 2',
          odds: 2.8,
          implied_probability: 35.71,
          total_bet_amount: 120,
          bet_count: 6,
          is_active: true,
          outcome_type: 'player',
        },
        {
          id: 'outcome-5',
          name: 'Player 3',
          odds: 4.0,
          implied_probability: 25.00,
          total_bet_amount: 80,
          bet_count: 4,
          is_active: true,
          outcome_type: 'player',
        },
      ],
      created_at: new Date().toISOString(),
      session_id: 'session-123',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useApi as jest.Mock).mockReturnValue(mockApi);
    (useBettingWebSocket as jest.Mock).mockReturnValue(mockWebSocket);
    mockApi.getBettingMarkets.mockResolvedValue(mockMarkets);
  });

  it('should render betting markets for a session', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('Betting Markets')).toBeInTheDocument();
      expect(screen.getByText('Match Winner')).toBeInTheDocument();
      expect(screen.getByText('First to Score')).toBeInTheDocument();
    });
  });

  it('should display market statistics', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      // Total pool
      expect(screen.getByText('$500')).toBeInTheDocument();
      expect(screen.getByText('$300')).toBeInTheDocument();
      
      // Total bets
      expect(screen.getByText('10 bets')).toBeInTheDocument();
      expect(screen.getByText('15 bets')).toBeInTheDocument();
    });
  });

  it('should show outcomes with odds', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      // Team outcomes
      expect(screen.getByText('Team A')).toBeInTheDocument();
      expect(screen.getByText('Team B')).toBeInTheDocument();
      expect(screen.getByText('1.80x')).toBeInTheDocument();
      expect(screen.getByText('2.20x')).toBeInTheDocument();
      
      // Player outcomes
      expect(screen.getByText('Player 1')).toBeInTheDocument();
      expect(screen.getByText('3.50x')).toBeInTheDocument();
    });
  });

  it('should subscribe to WebSocket updates on mount', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(mockWebSocket.subscribeToMarket).toHaveBeenCalledWith('session-123');
    });
  });

  it('should unsubscribe from WebSocket on unmount', async () => {
    const { unmount } = render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(mockWebSocket.subscribeToMarket).toHaveBeenCalled();
    });

    unmount();

    expect(mockWebSocket.unsubscribeFromMarket).toHaveBeenCalledWith('session-123');
  });

  it('should update odds in real-time', async () => {
    const { rerender } = render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('1.80x')).toBeInTheDocument();
    });

    // Simulate WebSocket update
    const updatedMarkets = [
      {
        ...mockMarkets[0],
        outcomes: [
          { ...mockMarkets[0].outcomes[0], odds: 1.9 },
          { ...mockMarkets[0].outcomes[1], odds: 2.1 },
        ],
      },
    ];

    (useBettingWebSocket as jest.Mock).mockReturnValue({
      ...mockWebSocket,
      markets: updatedMarkets,
    });

    rerender(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('1.90x')).toBeInTheDocument();
      expect(screen.getByText('2.10x')).toBeInTheDocument();
    });
  });

  it('should handle closed markets', async () => {
    const closedMarket = {
      ...mockMarkets[0],
      status: 'closed',
    };

    mockApi.getBettingMarkets.mockResolvedValue([closedMarket]);

    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('Market Closed')).toBeInTheDocument();
    });
  });

  it('should show loading state', () => {
    mockApi.getBettingMarkets.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockMarkets), 100))
    );

    render(<BettingMarkets sessionId="session-123" />);

    expect(screen.getByText('Loading betting markets...')).toBeInTheDocument();
  });

  it('should handle error states', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockApi.getBettingMarkets.mockRejectedValue(new Error('API Error'));

    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText(/unable to load betting markets/i)).toBeInTheDocument();
    });

    consoleErrorSpy.mockRestore();
  });

  it('should show empty state when no markets', async () => {
    mockApi.getBettingMarkets.mockResolvedValue([]);

    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText(/no betting markets available/i)).toBeInTheDocument();
    });
  });

  it('should display market types correctly', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('Match Winner')).toBeInTheDocument();
      expect(screen.getByText('Player Performance')).toBeInTheDocument();
    });
  });

  it('should show implied probability', async () => {
    render(<BettingMarkets sessionId="session-123" showProbability />);

    await waitFor(() => {
      expect(screen.getByText('55.6%')).toBeInTheDocument();
      expect(screen.getByText('45.5%')).toBeInTheDocument();
    });
  });

  it('should handle suspended markets', async () => {
    const suspendedMarket = {
      ...mockMarkets[0],
      status: 'suspended',
    };

    mockApi.getBettingMarkets.mockResolvedValue([suspendedMarket]);

    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText('Market Suspended')).toBeInTheDocument();
    });
  });

  it('should filter markets by type when filter provided', async () => {
    render(<BettingMarkets sessionId="session-123" marketType="match_winner" />);

    await waitFor(() => {
      expect(screen.getByText('Match Winner')).toBeInTheDocument();
      expect(screen.queryByText('First to Score')).not.toBeInTheDocument();
    });
  });

  it('should trigger onMarketSelect when market is clicked', async () => {
    const onMarketSelect = jest.fn();
    
    render(
      <BettingMarkets 
        sessionId="session-123" 
        onMarketSelect={onMarketSelect}
      />
    );

    await waitFor(() => {
      const marketCard = screen.getByText('Match Winner').closest('[data-testid="market-card"]');
      if (marketCard) {
        marketCard.click();
        expect(onMarketSelect).toHaveBeenCalledWith(mockMarkets[0]);
      }
    });
  });

  it('should show market closing time if provided', async () => {
    const marketWithCloseTime = {
      ...mockMarkets[0],
      closes_at: new Date(Date.now() + 3600000).toISOString(),
    };

    mockApi.getBettingMarkets.mockResolvedValue([marketWithCloseTime]);

    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      expect(screen.getByText(/closes in/i)).toBeInTheDocument();
    });
  });

  it('should be accessible with keyboard navigation', async () => {
    render(<BettingMarkets sessionId="session-123" />);

    await waitFor(() => {
      const marketCards = screen.getAllByTestId('market-card');
      marketCards[0].focus();
      expect(document.activeElement).toBe(marketCards[0]);
    });
  });
});