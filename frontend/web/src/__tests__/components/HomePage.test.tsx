/**
 * BetBet Gaming Engine - Home Page Tests
 * =====================================
 */

import { render, screen, waitFor } from '../utils/test-utils';
import HomePage from '@/app/page';
import { mockStore, updateMockStore, resetMockStore, createMockUser, createMockGame, createMockSession } from '../utils/test-utils';

// Mock the API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    getFeaturedGames: jest.fn().mockResolvedValue([]),
    getSessions: jest.fn().mockResolvedValue({ sessions: [] }),
  },
}));

describe('Home Page', () => {
  beforeEach(() => {
    resetMockStore();
  });

  it('renders the hero section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('BetBet Gaming Engine')).toBeInTheDocument();
    expect(screen.getByText(/real-time multiplayer gaming/i)).toBeInTheDocument();
  });

  it('shows get started buttons when not authenticated', () => {
    render(<HomePage />);
    
    expect(screen.getByRole('link', { name: /get started/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /browse games/i })).toBeInTheDocument();
  });

  it('shows gaming action buttons when authenticated', () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<HomePage />);
    
    expect(screen.getByRole('link', { name: /create session/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /join session/i })).toBeInTheDocument();
  });

  it('displays stats overview cards', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Active Players')).toBeInTheDocument();
    expect(screen.getByText('Live Sessions')).toBeInTheDocument();
    expect(screen.getByText('Prize Pool')).toBeInTheDocument();
    expect(screen.getByText('Tournaments')).toBeInTheDocument();
  });

  it('shows featured games section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Featured Games')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view all/i })).toBeInTheDocument();
  });

  it('displays featured games when loaded', async () => {
    const mockGames = [
      createMockGame({ name: 'Test Game 1', is_featured: true }),
      createMockGame({ name: 'Test Game 2', is_featured: true }),
    ];

    updateMockStore({
      featuredGames: mockGames,
      gamesLoading: false,
    });

    render(<HomePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Game 1')).toBeInTheDocument();
      expect(screen.getByText('Test Game 2')).toBeInTheDocument();
    });
  });

  it('shows loading state for games', () => {
    updateMockStore({
      gamesLoading: true,
    });

    render(<HomePage />);
    
    // Should show skeleton loading cards
    const skeletonCards = document.querySelectorAll('.animate-pulse');
    expect(skeletonCards.length).toBeGreaterThan(0);
  });

  it('displays active sessions section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Active Sessions')).toBeInTheDocument();
  });

  it('shows active sessions when loaded', async () => {
    const mockSessions = [
      createMockSession({ session_name: 'Test Session 1', status: 'active' }),
      createMockSession({ session_name: 'Test Session 2', status: 'active' }),
    ];

    updateMockStore({
      sessions: mockSessions,
      sessionsLoading: false,
    });

    render(<HomePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Session 1')).toBeInTheDocument();
      expect(screen.getByText('Test Session 2')).toBeInTheDocument();
    });
  });

  it('displays quick actions section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Create Session')).toBeInTheDocument();
    expect(screen.getByText('Join Tournament')).toBeInTheDocument();
    expect(screen.getByText('Spectator Betting')).toBeInTheDocument();
  });

  it('shows game details in featured games', async () => {
    const mockGame = createMockGame({
      name: 'Trivia Master',
      description: 'Test your knowledge',
      min_players: 2,
      max_players: 8,
      estimated_duration_minutes: 15,
      total_sessions_played: 150,
    });

    updateMockStore({
      featuredGames: [mockGame],
      gamesLoading: false,
    });

    render(<HomePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Trivia Master')).toBeInTheDocument();
      expect(screen.getByText('Test your knowledge')).toBeInTheDocument();
      expect(screen.getByText('2-8 players')).toBeInTheDocument();
      expect(screen.getByText('15 min')).toBeInTheDocument();
      expect(screen.getByText('150 sessions played')).toBeInTheDocument();
    });
  });

  it('shows session details in active sessions', async () => {
    const mockSession = createMockSession({
      session_name: 'Friday Night Trivia',
      current_participants: 4,
      max_participants: 8,
      entry_fee: 10,
      total_prize_pool: 80,
      status: 'active',
    });

    updateMockStore({
      sessions: [mockSession],
      sessionsLoading: false,
    });

    render(<HomePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Friday Night Trivia')).toBeInTheDocument();
      expect(screen.getByText('4/8 players')).toBeInTheDocument();
      expect(screen.getByText('$10')).toBeInTheDocument();
      expect(screen.getByText('$80')).toBeInTheDocument();
    });
  });
});