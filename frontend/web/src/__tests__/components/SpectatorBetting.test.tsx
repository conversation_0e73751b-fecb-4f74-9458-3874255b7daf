/**
 * BetBet Gaming Engine - Spectator Betting Tests
 * ==============================================
 */

import { render, screen, fireEvent, waitFor } from '../utils/simple-test-utils';
import { createMockUser, createMockSession } from '../utils/simple-test-utils';

// Mock spectator betting component
const MockSpectatorBetting = ({ sessionId }: { sessionId: string }) => {
  const [selectedParticipant, setSelectedParticipant] = React.useState<string | null>(null);
  const [betAmount, setBetAmount] = React.useState<string>('');
  const [userBalance] = React.useState<number>(100);
  const [isAuthenticated] = React.useState<boolean>(true);

  const session = {
    id: sessionId,
    session_name: 'Epic Trivia Battle',
    status: 'active',
    betting_enabled: true,
    total_bets: 45,
    betting_pool: 2250,
    viewer_count: 127,
    participants: [
      { id: 'p1', name: 'Player One', score: 150, betting_odds: 2.5 },
      { id: 'p2', name: 'Player Two', score: 120, betting_odds: 3.2 },
      { id: 'p3', name: 'Player Three', score: 180, betting_odds: 1.8 },
    ]
  };

  const handlePlaceBet = () => {
    if (!selectedParticipant || !betAmount || !isAuthenticated) return;
    console.log('Placing bet:', { selectedParticipant, betAmount });
  };

  const isInsufficientBalance = parseFloat(betAmount) > userBalance;
  const isBetValid = selectedParticipant && betAmount && !isInsufficientBalance;

  return (
    <div data-testid="spectator-betting">
      <div data-testid="session-info">
        <h2>{session.session_name}</h2>
        <div>Viewers: {session.viewer_count}</div>
        <div>Betting Pool: ${session.betting_pool}</div>
        <div>Total Bets: {session.total_bets}</div>
      </div>

      <div data-testid="participants-list">
        {session.participants.map((participant) => (
          <div 
            key={participant.id} 
            data-testid={`participant-${participant.id}`}
            className={`participant-card ${selectedParticipant === participant.id ? 'selected' : ''}`}
            onClick={() => setSelectedParticipant(participant.id)}
          >
            <div>{participant.name}</div>
            <div>Score: {participant.score}</div>
            <div data-testid={`odds-${participant.id}`}>Odds: {participant.betting_odds}x</div>
          </div>
        ))}
      </div>

      {isAuthenticated ? (
        <div data-testid="betting-interface">
          <div data-testid="user-balance">Balance: ${userBalance}</div>
          
          <div data-testid="bet-form">
            <input
              data-testid="bet-amount-input"
              type="number"
              placeholder="Bet amount"
              value={betAmount}
              onChange={(e) => setBetAmount(e.target.value)}
            />
            
            {isInsufficientBalance && (
              <div data-testid="insufficient-balance-error">Insufficient balance</div>
            )}
            
            <button 
              data-testid="place-bet-button"
              onClick={handlePlaceBet}
              disabled={!isBetValid}
            >
              Place Bet
            </button>
          </div>

          {selectedParticipant && betAmount && (
            <div data-testid="bet-preview">
              <div>Betting on: {session.participants.find(p => p.id === selectedParticipant)?.name}</div>
              <div>Amount: ${betAmount}</div>
              <div>Potential Win: ${(parseFloat(betAmount) * (session.participants.find(p => p.id === selectedParticipant)?.betting_odds || 1)).toFixed(2)}</div>
            </div>
          )}
        </div>
      ) : (
        <div data-testid="auth-prompt">
          <div>Sign in to place bets</div>
          <button data-testid="sign-in-button">Sign In</button>
        </div>
      )}

      <div data-testid="live-chat">
        <h3>Live Chat</h3>
        <div data-testid="chat-messages">
          <div>User1: Great game!</div>
          <div>User2: Go Player Three!</div>
        </div>
        <input data-testid="chat-input" placeholder="Type a message..." />
        <button data-testid="send-chat">Send</button>
      </div>

      <div data-testid="betting-history">
        <h3>Recent Bets</h3>
        <div>User123 bet $25 on Player One</div>
        <div>User456 bet $50 on Player Three</div>
      </div>
    </div>
  );
};

// Mock React import
const React = { useState: jest.fn() };

describe('Spectator Betting Component', () => {
  beforeEach(() => {
    // Reset React.useState mocks
    React.useState
      .mockReturnValueOnce([null, jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['', jest.fn()]) // betAmount
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([true, jest.fn()]); // isAuthenticated
  });

  it('renders session information correctly', () => {
    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByText('Epic Trivia Battle')).toBeInTheDocument();
    expect(screen.getByText('Viewers: 127')).toBeInTheDocument();
    expect(screen.getByText('Betting Pool: $2250')).toBeInTheDocument();
    expect(screen.getByText('Total Bets: 45')).toBeInTheDocument();
  });

  it('displays all participants with scores and odds', () => {
    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('participant-p1')).toBeInTheDocument();
    expect(screen.getByTestId('participant-p2')).toBeInTheDocument();
    expect(screen.getByTestId('participant-p3')).toBeInTheDocument();
    
    expect(screen.getByText('Player One')).toBeInTheDocument();
    expect(screen.getByText('Score: 150')).toBeInTheDocument();
    expect(screen.getByTestId('odds-p1')).toHaveTextContent('Odds: 2.5x');
  });

  it('shows betting interface when authenticated', () => {
    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('betting-interface')).toBeInTheDocument();
    expect(screen.getByTestId('user-balance')).toHaveTextContent('Balance: $100');
    expect(screen.getByTestId('bet-amount-input')).toBeInTheDocument();
    expect(screen.getByTestId('place-bet-button')).toBeInTheDocument();
  });

  it('shows sign in prompt when not authenticated', () => {
    // Mock unauthenticated state
    React.useState
      .mockReturnValueOnce([null, jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['', jest.fn()]) // betAmount
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([false, jest.fn()]); // isAuthenticated = false

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('auth-prompt')).toBeInTheDocument();
    expect(screen.getByText('Sign in to place bets')).toBeInTheDocument();
    expect(screen.getByTestId('sign-in-button')).toBeInTheDocument();
    expect(screen.queryByTestId('betting-interface')).not.toBeInTheDocument();
  });

  it('handles participant selection', () => {
    const mockSetSelectedParticipant = jest.fn();
    React.useState
      .mockReturnValueOnce([null, mockSetSelectedParticipant])
      .mockReturnValueOnce(['', jest.fn()])
      .mockReturnValueOnce([100, jest.fn()])
      .mockReturnValueOnce([true, jest.fn()]);

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    const participantCard = screen.getByTestId('participant-p1');
    fireEvent.click(participantCard);
    
    expect(mockSetSelectedParticipant).toHaveBeenCalledWith('p1');
  });

  it('handles bet amount input', () => {
    const mockSetBetAmount = jest.fn();
    React.useState
      .mockReturnValueOnce([null, jest.fn()])
      .mockReturnValueOnce(['', mockSetBetAmount])
      .mockReturnValueOnce([100, jest.fn()])
      .mockReturnValueOnce([true, jest.fn()]);

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    const betInput = screen.getByTestId('bet-amount-input');
    fireEvent.change(betInput, { target: { value: '25' } });
    
    expect(mockSetBetAmount).toHaveBeenCalledWith('25');
  });

  it('shows insufficient balance error when bet exceeds balance', () => {
    React.useState
      .mockReturnValueOnce(['p1', jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['150', jest.fn()]) // betAmount > balance
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([true, jest.fn()]); // isAuthenticated

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('insufficient-balance-error')).toBeInTheDocument();
    expect(screen.getByTestId('place-bet-button')).toBeDisabled();
  });

  it('shows bet preview when participant and amount selected', () => {
    React.useState
      .mockReturnValueOnce(['p1', jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['25', jest.fn()]) // betAmount
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([true, jest.fn()]); // isAuthenticated

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('bet-preview')).toBeInTheDocument();
    expect(screen.getByText('Betting on: Player One')).toBeInTheDocument();
    expect(screen.getByText('Amount: $25')).toBeInTheDocument();
    expect(screen.getByText('Potential Win: $62.50')).toBeInTheDocument(); // 25 * 2.5
  });

  it('enables place bet button when valid bet is ready', () => {
    React.useState
      .mockReturnValueOnce(['p1', jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['25', jest.fn()]) // betAmount
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([true, jest.fn()]); // isAuthenticated

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    const placeBetButton = screen.getByTestId('place-bet-button');
    expect(placeBetButton).not.toBeDisabled();
  });

  it('handles place bet action', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    React.useState
      .mockReturnValueOnce(['p1', jest.fn()]) // selectedParticipant
      .mockReturnValueOnce(['25', jest.fn()]) // betAmount
      .mockReturnValueOnce([100, jest.fn()]) // userBalance
      .mockReturnValueOnce([true, jest.fn()]); // isAuthenticated

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    const placeBetButton = screen.getByTestId('place-bet-button');
    fireEvent.click(placeBetButton);
    
    expect(consoleSpy).toHaveBeenCalledWith('Placing bet:', { 
      selectedParticipant: 'p1', 
      betAmount: '25' 
    });
    
    consoleSpy.mockRestore();
  });

  it('displays live chat functionality', () => {
    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('live-chat')).toBeInTheDocument();
    expect(screen.getByText('Live Chat')).toBeInTheDocument();
    expect(screen.getByTestId('chat-messages')).toBeInTheDocument();
    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
    expect(screen.getByTestId('send-chat')).toBeInTheDocument();
  });

  it('shows betting history', () => {
    render(<MockSpectatorBetting sessionId="session-1" />);
    
    expect(screen.getByTestId('betting-history')).toBeInTheDocument();
    expect(screen.getByText('Recent Bets')).toBeInTheDocument();
    expect(screen.getByText('User123 bet $25 on Player One')).toBeInTheDocument();
  });

  it('highlights selected participant', () => {
    React.useState
      .mockReturnValueOnce(['p2', jest.fn()]) // selectedParticipant = p2
      .mockReturnValueOnce(['', jest.fn()])
      .mockReturnValueOnce([100, jest.fn()])
      .mockReturnValueOnce([true, jest.fn()]);

    render(<MockSpectatorBetting sessionId="session-1" />);
    
    const selectedCard = screen.getByTestId('participant-p2');
    expect(selectedCard).toHaveClass('selected');
  });
});