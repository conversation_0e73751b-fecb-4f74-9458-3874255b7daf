/**
 * Custom Betting Platform - SettlementInterface Component Tests
 * ============================================================
 * 
 * Comprehensive tests for the admin settlement interface component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SettlementInterface } from '@/components/custom-betting/SettlementInterface';
import { mockBinaryMarket, mockMultiChoiceMarket, mockSettledMarket } from '../../mocks/custom-betting-data';

// Mock the API module
jest.mock('@/lib/custom-betting-api', () => ({
  __esModule: true,
  default: {
    settleMarket: jest.fn(),
    suspendMarket: jest.fn(),
    cancelMarket: jest.fn()
  }
}));

import customBettingAPI from '@/lib/custom-betting-api';
const mockSettleMarket = customBettingAPI.settleMarket as jest.MockedFunction<typeof customBettingAPI.settleMarket>;
const mockSuspendMarket = customBettingAPI.suspendMarket as jest.MockedFunction<typeof customBettingAPI.suspendMarket>;
const mockCancelMarket = customBettingAPI.cancelMarket as jest.MockedFunction<typeof customBettingAPI.cancelMarket>;

const mockOnSettlement = jest.fn();
const mockOnSuspend = jest.fn();
const mockOnCancel = jest.fn();

describe('SettlementInterface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    market: mockBinaryMarket,
    onSettlement: mockOnSettlement,
    onSuspend: mockOnSuspend,
    onCancel: mockOnCancel
  };

  describe('Component Rendering', () => {
    it('renders settlement interface for active market', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText('Market Settlement')).toBeInTheDocument();
      expect(screen.getByText(mockBinaryMarket.title)).toBeInTheDocument();
      expect(screen.getByText('Select Winning Outcome')).toBeInTheDocument();
      expect(screen.getByText('Settlement Actions')).toBeInTheDocument();
    });

    it('displays market information correctly', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText(`Total Stakes: $${mockBinaryMarket.total_stakes.toLocaleString()}`)).toBeInTheDocument();
      expect(screen.getByText(`Participants: ${mockBinaryMarket.total_participants}`)).toBeInTheDocument();
      expect(screen.getByText(`Status: ${mockBinaryMarket.status.charAt(0).toUpperCase() + mockBinaryMarket.status.slice(1)}`)).toBeInTheDocument();
    });

    it('shows all market outcomes as options', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        expect(screen.getByText(outcome.outcome_text)).toBeInTheDocument();
        expect(screen.getByText(`${outcome.current_odds}x odds`)).toBeInTheDocument();
        expect(screen.getByText(`$${outcome.total_backing.toLocaleString()} backing`)).toBeInTheDocument();
      });
    });

    it('disables settlement for already settled market', () => {
      render(<SettlementInterface {...defaultProps} market={mockSettledMarket} />);
      
      expect(screen.getByText('This market has already been settled')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /settle market/i })).toBeDisabled();
      
      // Should show winning outcome
      expect(screen.getByText('Winning Outcome:')).toBeInTheDocument();
      const winningOutcome = mockSettledMarket.outcomes.find(o => o.is_winning_outcome);
      expect(screen.getByText(winningOutcome!.outcome_text)).toBeInTheDocument();
    });
  });

  describe('Outcome Selection', () => {
    it('allows selecting binary outcomes', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      
      expect(firstOutcome).toBeChecked();
      
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      expect(settleButton).toBeEnabled();
    });

    it('allows selecting multiple choice outcomes', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} market={mockMultiChoiceMarket} />);
      
      const secondOutcome = screen.getByRole('radio', { name: mockMultiChoiceMarket.outcomes[1].outcome_text });
      await user.click(secondOutcome);
      
      expect(secondOutcome).toBeChecked();
      
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      expect(settleButton).toBeEnabled();
    });

    it('shows impact summary when outcome is selected', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      
      expect(screen.getByText('Settlement Impact')).toBeInTheDocument();
      expect(screen.getByText('Winning Positions:')).toBeInTheDocument();
      expect(screen.getByText('Losing Positions:')).toBeInTheDocument();
    });

    it('calculates payout amounts correctly', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      
      // Should show estimated payouts
      expect(screen.getByText(/Estimated Payouts:/)).toBeInTheDocument();
      expect(screen.getByText(/Total Payout:/)).toBeInTheDocument();
    });
  });

  describe('Settlement Actions', () => {
    it('settles market with selected outcome', async () => {
      const user = userEvent.setup();
      mockSettleMarket.mockResolvedValue({ success: true });
      
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      
      // Add settlement notes
      const notesInput = screen.getByLabelText('Settlement Notes');
      await user.type(notesInput, 'Market settled based on official results');
      
      // Settle market
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      expect(screen.getByText('Settling market...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(mockSettleMarket).toHaveBeenCalledWith(
          mockBinaryMarket.id,
          mockBinaryMarket.outcomes[0].id,
          'Market settled based on official results'
        );
        expect(mockOnSettlement).toHaveBeenCalled();
      });
    });

    it('requires outcome selection for settlement', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      expect(settleButton).toBeDisabled();
      
      // Try to click disabled button
      await user.click(settleButton);
      
      expect(mockSettleMarket).not.toHaveBeenCalled();
    });

    it('requires confirmation before settlement', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      
      // Click settle button
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      // Should show confirmation dialog
      expect(screen.getByText('Confirm Settlement')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to settle this market?')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /confirm settlement/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    it('cancels settlement confirmation', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome and trigger confirmation
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      // Cancel confirmation
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);
      
      expect(screen.queryByText('Confirm Settlement')).not.toBeInTheDocument();
      expect(mockSettleMarket).not.toHaveBeenCalled();
    });

    it('handles settlement errors', async () => {
      const user = userEvent.setup();
      mockSettleMarket.mockRejectedValue(new Error('Settlement failed'));
      
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome and settle
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      // Confirm settlement
      const confirmButton = screen.getByRole('button', { name: /confirm settlement/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(screen.getByText('Settlement failed')).toBeInTheDocument();
        expect(screen.getByText('Please try again or contact support')).toBeInTheDocument();
      });
    });
  });

  describe('Market Management Actions', () => {
    it('suspends market', async () => {
      const user = userEvent.setup();
      mockSuspendMarket.mockResolvedValue({ success: true });
      
      render(<SettlementInterface {...defaultProps} />);
      
      const suspendButton = screen.getByRole('button', { name: /suspend market/i });
      await user.click(suspendButton);
      
      // Should require confirmation
      expect(screen.getByText('Confirm Suspension')).toBeInTheDocument();
      
      const confirmButton = screen.getByRole('button', { name: /confirm suspension/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(mockSuspendMarket).toHaveBeenCalledWith(mockBinaryMarket.id);
        expect(mockOnSuspend).toHaveBeenCalled();
      });
    });

    it('cancels market', async () => {
      const user = userEvent.setup();
      mockCancelMarket.mockResolvedValue({ success: true });
      
      render(<SettlementInterface {...defaultProps} />);
      
      const cancelButton = screen.getByRole('button', { name: /cancel market/i });
      await user.click(cancelButton);
      
      // Should require confirmation and reason
      expect(screen.getByText('Confirm Market Cancellation')).toBeInTheDocument();
      expect(screen.getByLabelText('Cancellation Reason')).toBeInTheDocument();
      
      const reasonInput = screen.getByLabelText('Cancellation Reason');
      await user.type(reasonInput, 'Market conditions changed');
      
      const confirmButton = screen.getByRole('button', { name: /confirm cancellation/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(mockCancelMarket).toHaveBeenCalledWith(mockBinaryMarket.id, 'Market conditions changed');
        expect(mockOnCancel).toHaveBeenCalled();
      });
    });

    it('requires reason for market cancellation', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const cancelButton = screen.getByRole('button', { name: /cancel market/i });
      await user.click(cancelButton);
      
      // Try to confirm without reason
      const confirmButton = screen.getByRole('button', { name: /confirm cancellation/i });
      expect(confirmButton).toBeDisabled();
      
      // Add reason
      const reasonInput = screen.getByLabelText('Cancellation Reason');
      await user.type(reasonInput, 'Valid reason');
      
      expect(confirmButton).toBeEnabled();
    });
  });

  describe('Market Status Display', () => {
    it('shows different interface for suspended market', () => {
      const suspendedMarket = { ...mockBinaryMarket, status: 'suspended' as const };
      render(<SettlementInterface {...defaultProps} market={suspendedMarket} />);
      
      expect(screen.getByText('This market is currently suspended')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reactivate market/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /settle market/i })).toBeEnabled();
    });

    it('shows different interface for cancelled market', () => {
      const cancelledMarket = { ...mockBinaryMarket, status: 'cancelled' as const };
      render(<SettlementInterface {...defaultProps} market={cancelledMarket} />);
      
      expect(screen.getByText('This market has been cancelled')).toBeInTheDocument();
      expect(screen.getByText('All stakes have been returned to participants')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /settle market/i })).toBeDisabled();
    });

    it('shows time information correctly', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText('Market Deadline:')).toBeInTheDocument();
      expect(screen.getByText('Verification Deadline:')).toBeInTheDocument();
      
      // Should show formatted dates
      const deadline = new Date(mockBinaryMarket.deadline).toLocaleDateString();
      const verificationDeadline = new Date(mockBinaryMarket.verification_deadline).toLocaleDateString();
      
      expect(screen.getByText(deadline)).toBeInTheDocument();
      expect(screen.getByText(verificationDeadline)).toBeInTheDocument();
    });
  });

  describe('Admin Authorization', () => {
    it('displays admin-only warning', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText('Admin Access Only')).toBeInTheDocument();
      expect(screen.getByText('Settlement actions are irreversible')).toBeInTheDocument();
    });

    it('shows audit trail section', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText('Audit Trail')).toBeInTheDocument();
      expect(screen.getByText('All actions are logged for compliance')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('shows live participant count', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByText(`${mockBinaryMarket.total_participants} active participants`)).toBeInTheDocument();
    });

    it('updates market statistics in real-time', () => {
      const { rerender } = render(<SettlementInterface {...defaultProps} />);
      
      // Update market with new stats
      const updatedMarket = {
        ...mockBinaryMarket,
        total_stakes: 60000,
        total_participants: 35
      };
      
      rerender(<SettlementInterface {...defaultProps} market={updatedMarket} />);
      
      expect(screen.getByText('Total Stakes: $60,000')).toBeInTheDocument();
      expect(screen.getByText('Participants: 35')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<SettlementInterface {...defaultProps} />);
      
      expect(screen.getByRole('radiogroup')).toBeInTheDocument();
      expect(screen.getByLabelText('Settlement Notes')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /settle market/i })).toHaveAttribute('aria-disabled');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      // Tab to first outcome
      await user.tab();
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      expect(firstOutcome).toHaveFocus();
      
      // Arrow key to second outcome
      await user.keyboard('{ArrowDown}');
      const secondOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[1].outcome_text });
      expect(secondOutcome).toHaveFocus();
    });

    it('announces settlement actions to screen readers', async () => {
      const user = userEvent.setup();
      mockSettleMarket.mockResolvedValue({ success: true });
      
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome and settle
      const firstOutcome = screen.getByRole('radio', { name: mockBinaryMarket.outcomes[0].outcome_text });
      await user.click(firstOutcome);
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      const confirmButton = screen.getByRole('button', { name: /confirm settlement/i });
      await user.click(confirmButton);
      
      const statusElement = screen.getByText('Settling market...');
      expect(statusElement).toHaveAttribute('aria-live', 'polite');
    });
  });

  describe('Data Validation', () => {
    it('validates settlement notes length', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const notesInput = screen.getByLabelText('Settlement Notes');
      const longText = 'a'.repeat(501); // Over 500 character limit
      await user.type(notesInput, longText);
      
      expect(screen.getByText('Notes must be under 500 characters')).toBeInTheDocument();
    });

    it('shows character count for settlement notes', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      const notesInput = screen.getByLabelText('Settlement Notes');
      await user.type(notesInput, 'Test notes');
      
      expect(screen.getByText('10 / 500')).toBeInTheDocument();
    });
  });
});