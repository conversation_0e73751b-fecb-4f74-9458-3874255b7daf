/**
 * Custom Betting Platform - BettingInterface Component Tests
 * =========================================================
 * 
 * Comprehensive tests for the betting interface component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BettingInterface } from '@/components/custom-betting/BettingInterface';
import { mockBinaryMarket, mockUserParticipations } from '../../mocks/custom-betting-data';

// Mock the API
const mockOnBetPlaced = jest.fn();

describe('BettingInterface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    market: mockBinaryMarket,
    userParticipation: null,
    onBetPlaced: mockOnBetPlaced
  };

  describe('Component Rendering', () => {
    it('renders betting interface correctly', () => {
      render(<BettingInterface {...defaultProps} />);
      
      expect(screen.getByText('Place Your Bet')).toBeInTheDocument();
      expect(screen.getByText('Select Outcome')).toBeInTheDocument();
      expect(screen.getByText('Position Type')).toBeInTheDocument();
      expect(screen.getByText('Stake Amount')).toBeInTheDocument();
      expect(screen.getByText('Desired Odds')).toBeInTheDocument();
    });

    it('shows user participation alert when user has existing position', () => {
      render(
        <BettingInterface 
          {...defaultProps} 
          userParticipation={mockUserParticipations[0]} 
        />
      );
      
      expect(screen.getByText('You\'re already participating')).toBeInTheDocument();
      expect(screen.getByText(/You have a backing position/)).toBeInTheDocument();
    });

    it('displays all market outcomes in dropdown', () => {
      render(<BettingInterface {...defaultProps} />);
      
      const outcomeSelect = screen.getByText('Choose an outcome...');
      fireEvent.click(outcomeSelect);
      
      expect(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text)).toBeInTheDocument();
      expect(screen.getByText(mockBinaryMarket.outcomes[1].outcome_text)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('validates stake amount within market limits', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Set minimum values to enable form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Test below minimum
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.clear(stakeInput);
      await user.type(stakeInput, '1'); // Below minimum of 5
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeDisabled();
      
      // Test above maximum
      await user.clear(stakeInput);
      await user.type(stakeInput, '15000'); // Above maximum of 10000
      
      expect(placeBetButton).toBeDisabled();
      
      // Test valid amount
      await user.clear(stakeInput);
      await user.type(stakeInput, '100');
      
      expect(placeBetButton).toBeEnabled();
    });

    it('validates odds input (minimum 1.01)', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Set outcome and valid stake
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      // Test invalid odds (below 1.01)
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '1.0');
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeDisabled();
      
      // Test valid odds
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      expect(placeBetButton).toBeEnabled();
    });

    it('requires all fields to be filled', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeDisabled();
      
      // Only stake - still disabled
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      expect(placeBetButton).toBeDisabled();
      
      // Add odds - still disabled
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.type(oddsInput, '2.5');
      expect(placeBetButton).toBeDisabled();
      
      // Add outcome - now enabled
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      expect(placeBetButton).toBeEnabled();
    });
  });

  describe('Position Type Selection', () => {
    it('defaults to backing position', () => {
      render(<BettingInterface {...defaultProps} />);
      
      const backingButton = screen.getByRole('button', { name: /back \(for\)/i });
      const layingButton = screen.getByRole('button', { name: /lay \(against\)/i });
      
      // Backing should have default variant, laying should have outline
      expect(backingButton).toHaveClass('bg-primary'); // default variant
      expect(layingButton).not.toHaveClass('bg-primary'); // outline variant
    });

    it('switches between backing and laying positions', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      const layingButton = screen.getByRole('button', { name: /lay \(against\)/i });
      await user.click(layingButton);
      
      expect(screen.getByText('You win if this outcome does NOT happen')).toBeInTheDocument();
      
      const backingButton = screen.getByRole('button', { name: /back \(for\)/i });
      await user.click(backingButton);
      
      expect(screen.getByText('You win if this outcome happens')).toBeInTheDocument();
    });

    it('updates bet summary calculations when position type changes', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Fill form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Check backing calculation (stake * odds)
      await waitFor(() => {
        expect(screen.getByText('$250.00')).toBeInTheDocument(); // Payout
        expect(screen.getByText('$150.00')).toBeInTheDocument(); // Profit
      });
      
      // Switch to laying
      const layingButton = screen.getByRole('button', { name: /lay \(against\)/i });
      await user.click(layingButton);
      
      // Check laying calculation (stake is what you win)
      await waitFor(() => {
        expect(screen.getByText('$100.00')).toBeInTheDocument(); // Payout (stake amount)
      });
    });
  });

  describe('Quick Odds Selection', () => {
    it('displays quick odds when outcome is selected', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Select outcome
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      // Should show quick odds based on current odds (2.5)
      await waitFor(() => {
        expect(screen.getByText('Quick odds:')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: '2.38x' })).toBeInTheDocument(); // 2.5 * 0.95
        expect(screen.getByRole('button', { name: '2.5x' })).toBeInTheDocument(); // Base odds
        expect(screen.getByRole('button', { name: '2.63x' })).toBeInTheDocument(); // 2.5 * 1.05
        expect(screen.getByRole('button', { name: '2.75x' })).toBeInTheDocument(); // 2.5 * 1.1
      });
    });

    it('sets odds when quick odds button is clicked', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Select outcome
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      // Click quick odds
      await waitFor(() => {
        const quickOddsButton = screen.getByRole('button', { name: '2.63x' });
        user.click(quickOddsButton);
      });
      
      const oddsInput = screen.getByDisplayValue('2.63');
      expect(oddsInput).toBeInTheDocument();
    });
  });

  describe('Bet Placement', () => {
    it('calls onBetPlaced when bet is successfully placed', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Fill out form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Place bet
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      // Should show loading state
      expect(screen.getByText('Placing Bet...')).toBeInTheDocument();
      
      // Wait for completion
      await waitFor(() => {
        expect(mockOnBetPlaced).toHaveBeenCalled();
      }, { timeout: 3000 });
    });

    it('resets form after successful bet placement', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Fill out form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Place bet
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      // Wait for form reset
      await waitFor(() => {
        expect(screen.getByText('Choose an outcome...')).toBeInTheDocument();
        expect(screen.getByDisplayValue('')).toBeInTheDocument(); // Empty stake input
      }, { timeout: 3000 });
    });

    it('handles bet placement errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock console.log to simulate API error
      const originalConsoleLog = console.log;
      console.log = jest.fn();
      
      render(<BettingInterface {...defaultProps} />);
      
      // Fill out form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Place bet
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      // Should complete without throwing (mock implementation always succeeds)
      await waitFor(() => {
        expect(mockOnBetPlaced).toHaveBeenCalled();
      }, { timeout: 3000 });
      
      console.log = originalConsoleLog;
    });
  });

  describe('Calculation Accuracy', () => {
    it('calculates backing payout correctly (stake * odds)', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Fill form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '3.0');
      
      // Check calculations
      await waitFor(() => {
        expect(screen.getByText('$300.00')).toBeInTheDocument(); // Potential Payout (100 * 3.0)
        expect(screen.getByText('$200.00')).toBeInTheDocument(); // Potential Profit (300 - 100)
      });
    });

    it('calculates laying payout correctly (stake amount)', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Switch to laying position
      const layingButton = screen.getByRole('button', { name: /lay \(against\)/i });
      await user.click(layingButton);
      
      // Fill form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '3.0');
      
      // Check calculations (for laying, payout is the stake, profit is stake)
      await waitFor(() => {
        expect(screen.getByText('$100.00')).toBeInTheDocument(); // Potential Payout (stake)
      });
    });

    it('updates calculations when inputs change', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Fill initial form
      const outcomeSelect = screen.getByText('Choose an outcome...');
      await user.click(outcomeSelect);
      await user.click(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text));
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.0');
      
      // Initial calculation
      await waitFor(() => {
        expect(screen.getByText('$200.00')).toBeInTheDocument(); // 100 * 2.0
      });
      
      // Change stake
      await user.clear(stakeInput);
      await user.type(stakeInput, '200');
      
      // Updated calculation
      await waitFor(() => {
        expect(screen.getByText('$400.00')).toBeInTheDocument(); // 200 * 2.0
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<BettingInterface {...defaultProps} />);
      
      expect(screen.getByLabelText('Select Outcome')).toBeInTheDocument();
      expect(screen.getByLabelText('Stake Amount')).toBeInTheDocument();
      expect(screen.getByLabelText('Desired Odds')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /place back bet/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Tab navigation should work
      await user.tab();
      expect(screen.getByText('Choose an outcome...')).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /back \(for\)/i })).toHaveFocus();
    });
  });
});