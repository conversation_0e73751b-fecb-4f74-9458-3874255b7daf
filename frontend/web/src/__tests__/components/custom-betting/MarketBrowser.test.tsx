/**
 * Custom Betting Platform - MarketBrowser Component Tests
 * =======================================================
 * 
 * Comprehensive tests for the market browser component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MarketBrowser } from '@/components/custom-betting/MarketBrowser';
import { mockMarkets } from '../../mocks/custom-betting-data';

// Mock the API module
jest.mock('@/lib/custom-betting-api', () => ({
  __esModule: true,
  default: {
    getBets: jest.fn()
  }
}));

import customBettingAPI from '@/lib/custom-betting-api';
const mockGetBets = customBettingAPI.getBets as jest.MockedFunction<typeof customBettingAPI.getBets>;

describe('MarketBrowser', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetBets.mockResolvedValue(mockMarkets);
  });

  describe('Component Rendering', () => {
    it('renders market browser with default view', async () => {
      render(<MarketBrowser />);
      
      expect(screen.getByText('Browse Markets')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search markets...')).toBeInTheDocument();
      expect(screen.getByText('All Categories')).toBeInTheDocument();
      
      // Should show loading initially, then markets
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
    });

    it('displays category filter options', () => {
      render(<MarketBrowser />);
      
      const categoryFilter = screen.getByText('All Categories');
      fireEvent.click(categoryFilter);
      
      expect(screen.getByText('Crypto')).toBeInTheDocument();
      expect(screen.getByText('Politics')).toBeInTheDocument();
      expect(screen.getByText('Sports')).toBeInTheDocument();
      expect(screen.getByText('Business')).toBeInTheDocument();
      expect(screen.getByText('Entertainment')).toBeInTheDocument();
      expect(screen.getByText('Science')).toBeInTheDocument();
    });

    it('displays status filter options', () => {
      render(<MarketBrowser />);
      
      const statusFilter = screen.getByText('All Status');
      fireEvent.click(statusFilter);
      
      expect(screen.getByText('Open')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('Settled')).toBeInTheDocument();
      expect(screen.getByText('Settling')).toBeInTheDocument();
    });

    it('shows view mode toggle buttons', () => {
      render(<MarketBrowser />);
      
      expect(screen.getByRole('button', { name: /grid view/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /list view/i })).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('filters markets by search term', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search markets...');
      await user.type(searchInput, 'Bitcoin');
      
      // Should filter API call with search term
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith(
          expect.objectContaining({
            search: 'Bitcoin'
          })
        );
      });
    });

    it('debounces search input', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      const searchInput = screen.getByPlaceholderText('Search markets...');
      
      // Type multiple characters quickly
      await user.type(searchInput, 'Bitcoin');
      
      // Should only call API once after debounce
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledTimes(2); // Initial load + debounced search
      }, { timeout: 1000 });
    });

    it('shows empty state when no search results', async () => {
      const user = userEvent.setup();
      mockGetBets.mockResolvedValue([]); // Empty results
      
      render(<MarketBrowser />);
      
      const searchInput = screen.getByPlaceholderText('Search markets...');
      await user.type(searchInput, 'nonexistent market');
      
      await waitFor(() => {
        expect(screen.getByText('No markets found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument();
      });
    });
  });

  describe('Filtering', () => {
    it('filters markets by category', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      const categoryFilter = screen.getByText('All Categories');
      await user.click(categoryFilter);
      await user.click(screen.getByText('Crypto'));
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith(
          expect.objectContaining({
            category: 'crypto'
          })
        );
      });
    });

    it('filters markets by status', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      const statusFilter = screen.getByText('All Status');
      await user.click(statusFilter);
      await user.click(screen.getByText('Active'));
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'active'
          })
        );
      });
    });

    it('applies multiple filters simultaneously', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Apply category filter
      const categoryFilter = screen.getByText('All Categories');
      await user.click(categoryFilter);
      await user.click(screen.getByText('Politics'));
      
      // Apply status filter
      const statusFilter = screen.getByText('All Status');
      await user.click(statusFilter);
      await user.click(screen.getByText('Open'));
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith(
          expect.objectContaining({
            category: 'politics',
            status: 'open'
          })
        );
      });
    });

    it('resets filters when clear button is clicked', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Apply some filters first
      const categoryFilter = screen.getByText('All Categories');
      await user.click(categoryFilter);
      await user.click(screen.getByText('Crypto'));
      
      const searchInput = screen.getByPlaceholderText('Search markets...');
      await user.type(searchInput, 'Bitcoin');
      
      // Click clear filters
      const clearButton = screen.getByRole('button', { name: /clear filters/i });
      await user.click(clearButton);
      
      // Should reset to default state
      expect(screen.getByDisplayValue('')).toBeInTheDocument(); // Empty search
      expect(screen.getByText('All Categories')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith({});
      });
    });
  });

  describe('View Mode Switching', () => {
    it('switches between grid and list views', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
      
      // Default should be grid view
      expect(screen.getByRole('button', { name: /grid view/i })).toHaveClass('bg-primary');
      
      // Switch to list view
      const listViewButton = screen.getByRole('button', { name: /list view/i });
      await user.click(listViewButton);
      
      expect(listViewButton).toHaveClass('bg-primary');
      
      // Switch back to grid view
      const gridViewButton = screen.getByRole('button', { name: /grid view/i });
      await user.click(gridViewButton);
      
      expect(gridViewButton).toHaveClass('bg-primary');
    });
  });

  describe('Sorting', () => {
    it('displays sort options', () => {
      render(<MarketBrowser />);
      
      const sortDropdown = screen.getByText('Latest');
      fireEvent.click(sortDropdown);
      
      expect(screen.getByText('Highest Volume')).toBeInTheDocument();
      expect(screen.getByText('Most Popular')).toBeInTheDocument();
      expect(screen.getByText('Ending Soon')).toBeInTheDocument();
    });

    it('applies sorting to market requests', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      const sortDropdown = screen.getByText('Latest');
      await user.click(sortDropdown);
      await user.click(screen.getByText('Highest Volume'));
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledWith(
          expect.objectContaining({
            sort: 'volume',
            order: 'desc'
          })
        );
      });
    });
  });

  describe('Pagination', () => {
    it('loads more markets when Load More button is clicked', async () => {
      const user = userEvent.setup();
      
      // Mock paginated response
      mockGetBets
        .mockResolvedValueOnce(mockMarkets.slice(0, 2)) // First page
        .mockResolvedValueOnce(mockMarkets.slice(2, 4)); // Second page
      
      render(<MarketBrowser />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
      
      // Click Load More
      const loadMoreButton = screen.getByRole('button', { name: /load more markets/i });
      await user.click(loadMoreButton);
      
      await waitFor(() => {
        expect(mockGetBets).toHaveBeenCalledTimes(2);
        expect(mockGetBets).toHaveBeenLastCalledWith(
          expect.objectContaining({
            offset: expect.any(Number)
          })
        );
      });
    });

    it('hides Load More button when no more markets', async () => {
      // Mock response with has_more: false
      mockGetBets.mockResolvedValue(mockMarkets);
      
      render(<MarketBrowser hasMore={false} />);
      
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
      
      expect(screen.queryByRole('button', { name: /load more markets/i })).not.toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loading spinner during initial load', () => {
      // Mock delayed response
      mockGetBets.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockMarkets), 1000))
      );
      
      render(<MarketBrowser />);
      
      expect(screen.getByText('Loading markets...')).toBeInTheDocument();
    });

    it('shows loading state during search', async () => {
      const user = userEvent.setup();
      
      // Mock delayed search response
      mockGetBets.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockMarkets), 500))
      );
      
      render(<MarketBrowser />);
      
      const searchInput = screen.getByPlaceholderText('Search markets...');
      await user.type(searchInput, 'Bitcoin');
      
      expect(screen.getByText('Searching...')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('displays error message when API fails', async () => {
      mockGetBets.mockRejectedValue(new Error('API Error'));
      
      render(<MarketBrowser />);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to load markets')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      });
    });

    it('retries loading when Try Again is clicked', async () => {
      const user = userEvent.setup();
      
      // First call fails, second succeeds
      mockGetBets
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValueOnce(mockMarkets);
      
      render(<MarketBrowser />);
      
      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText('Failed to load markets')).toBeInTheDocument();
      });
      
      // Click retry
      const tryAgainButton = screen.getByRole('button', { name: /try again/i });
      await user.click(tryAgainButton);
      
      // Should retry and show markets
      await waitFor(() => {
        expect(screen.getByText(mockMarkets[0].title)).toBeInTheDocument();
      });
      
      expect(mockGetBets).toHaveBeenCalledTimes(2);
    });
  });

  describe('Market Display', () => {
    it('displays market cards with correct information', async () => {
      render(<MarketBrowser />);
      
      await waitFor(() => {
        const firstMarket = mockMarkets[0];
        expect(screen.getByText(firstMarket.title)).toBeInTheDocument();
        expect(screen.getByText(firstMarket.category)).toBeInTheDocument();
        expect(screen.getByText(`$${firstMarket.total_stakes.toLocaleString()}`)).toBeInTheDocument();
        expect(screen.getByText(`${firstMarket.total_participants} participants`)).toBeInTheDocument();
      });
    });

    it('shows correct market status badges', async () => {
      render(<MarketBrowser />);
      
      await waitFor(() => {
        mockMarkets.forEach(market => {
          const statusBadge = screen.getByText(
            market.status.charAt(0).toUpperCase() + market.status.slice(1)
          );
          expect(statusBadge).toBeInTheDocument();
        });
      });
    });

    it('displays outcome odds correctly', async () => {
      render(<MarketBrowser />);
      
      await waitFor(() => {
        const firstMarket = mockMarkets[0];
        firstMarket.outcomes.forEach(outcome => {
          expect(screen.getByText(`${outcome.current_odds.toFixed(2)}x`)).toBeInTheDocument();
        });
      });
    });
  });

  describe('Responsive Design', () => {
    it('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<MarketBrowser />);
      
      // Mobile-specific elements should be visible
      expect(screen.getByRole('button', { name: /grid view/i })).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      render(<MarketBrowser />);
      
      expect(screen.getByRole('searchbox')).toBeInTheDocument();
      expect(screen.getByLabelText('Search markets')).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: /category filter/i })).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: /status filter/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Tab through interactive elements
      await user.tab(); // Search input
      expect(screen.getByPlaceholderText('Search markets...')).toHaveFocus();
      
      await user.tab(); // Category filter
      expect(screen.getByText('All Categories')).toHaveFocus();
      
      await user.tab(); // Status filter
      expect(screen.getByText('All Status')).toHaveFocus();
    });

    it('announces loading states to screen readers', () => {
      mockGetBets.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockMarkets), 1000))
      );
      
      render(<MarketBrowser />);
      
      const loadingElement = screen.getByText('Loading markets...');
      expect(loadingElement).toHaveAttribute('aria-live', 'polite');
    });
  });
});