/**
 * Custom Betting Platform - CreateBetWizard Component Tests
 * ========================================================
 * 
 * Comprehensive tests for the 5-step bet creation wizard.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateBetWizard } from '@/components/custom-betting/CreateBetWizard';
import { mockUsers } from '../../mocks/custom-betting-data';

// Mock the API module
jest.mock('@/lib/custom-betting-api', () => ({
  __esModule: true,
  default: {
    createBet: jest.fn()
  }
}));

import customBettingAPI from '@/lib/custom-betting-api';
const mockCreateBet = customBettingAPI.createBet as jest.MockedFunction<typeof customBettingAPI.createBet>;

const mockOnComplete = jest.fn();
const mockOnCancel = jest.fn();

describe('CreateBetWizard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    onComplete: mockOnComplete,
    onCancel: mockOnCancel
  };

  describe('Component Rendering', () => {
    it('renders the wizard with initial step', () => {
      render(<CreateBetWizard {...defaultProps} />);
      
      expect(screen.getByText('Basic Information')).toBeInTheDocument();
      expect(screen.getByText('Step 1 of 5')).toBeInTheDocument();
      expect(screen.getByText('Market Title *')).toBeInTheDocument();
      expect(screen.getByText('Description *')).toBeInTheDocument();
      expect(screen.getByText('Category *')).toBeInTheDocument();
    });

    it('displays progress indicator correctly', () => {
      render(<CreateBetWizard {...defaultProps} />);
      
      // Check current step progress
      expect(screen.getByText('Step 1 of 5')).toBeInTheDocument();
      expect(screen.getByText('20% Complete')).toBeInTheDocument();
      
      // Check that progress bar is present
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('shows cancel button on all steps', () => {
      render(<CreateBetWizard {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });
  });

  describe('Step 1: Basic Information', () => {
    it('validates required fields', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeDisabled();
      
      // Fill out minimum required fields
      const titleInput = screen.getByLabelText('Market Title *');
      await user.type(titleInput, 'Test Market Title');
      
      const descriptionInput = screen.getByLabelText('Description *');
      await user.type(descriptionInput, 'This is a test market description that meets the minimum length requirement.');
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Crypto'));
      
      // Should enable next button
      expect(nextButton).toBeEnabled();
    });

    it('shows character count for title and description', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      const titleInput = screen.getByLabelText('Market Title *');
      await user.type(titleInput, 'Test Title');
      
      expect(screen.getByText('10 / 200')).toBeInTheDocument(); // Character count for title
    });

    it('validates minimum description length', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      const descriptionInput = screen.getByLabelText('Description *');
      await user.type(descriptionInput, 'Too short');
      
      expect(screen.getByText('Description must be at least 50 characters')).toBeInTheDocument();
    });

    it('proceeds to step 2 when valid', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Fill valid data
      await user.type(screen.getByLabelText('Market Title *'), 'Valid Market Title');
      await user.type(screen.getByLabelText('Description *'), 'This is a valid description that meets the minimum length requirement for market creation.');
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Politics'));
      
      // Add tags
      const tagsInput = screen.getByPlaceholderText('Add tags (press Enter)');
      await user.type(tagsInput, 'politics{enter}election{enter}');
      
      // Continue to next step
      const nextButton = screen.getByRole('button', { name: /continue/i });
      await user.click(nextButton);
      
      expect(screen.getByText('Step 2 of 5: Define Outcomes')).toBeInTheDocument();
    });
  });

  describe('Step 2: Define Outcomes', () => {
    it('allows binary market creation', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Binary Market Test');
      await user.type(screen.getByLabelText('Description *'), 'This is a binary market for testing the wizard functionality.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Crypto'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Step 2: Select binary type (should be default)
      expect(screen.getByText('Binary (Yes/No)')).toBeInTheDocument();
      expect(screen.getByText('Multiple Choice')).toBeInTheDocument();
      
      // Binary should be selected by default
      const binaryOption = screen.getByRole('button', { name: /binary \(yes\/no\)/i });
      expect(binaryOption).toHaveClass('border-primary');
      
      // Should show default outcomes
      expect(screen.getByDisplayValue('Yes')).toBeInTheDocument();
      expect(screen.getByDisplayValue('No')).toBeInTheDocument();
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeEnabled();
    });

    it('allows multiple choice market creation', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Multiple Choice Test');
      await user.type(screen.getByLabelText('Description *'), 'This is a multiple choice market for testing the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Sports'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Step 2: Select multiple choice
      const multipleChoiceOption = screen.getByRole('button', { name: /multiple choice/i });
      await user.click(multipleChoiceOption);
      
      expect(multipleChoiceOption).toHaveClass('border-primary');
      
      // Should show default 3 outcomes
      const outcomeInputs = screen.getAllByPlaceholderText('Enter outcome option');
      expect(outcomeInputs).toHaveLength(3);
      
      // Fill outcomes
      await user.type(outcomeInputs[0], 'Option 1');
      await user.type(outcomeInputs[1], 'Option 2');
      await user.type(outcomeInputs[2], 'Option 3');
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeEnabled();
    });

    it('allows adding and removing outcomes in multiple choice', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Add/Remove Outcomes Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing adding and removing outcomes in multiple choice market.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Entertainment'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Select multiple choice
      const multipleChoiceOption = screen.getByRole('button', { name: /multiple choice/i });
      await user.click(multipleChoiceOption);
      
      // Add outcome
      const addOutcomeButton = screen.getByRole('button', { name: /add outcome/i });
      await user.click(addOutcomeButton);
      
      expect(screen.getAllByPlaceholderText('Enter outcome option')).toHaveLength(4);
      
      // Remove outcome
      const removeButtons = screen.getAllByRole('button', { name: /remove/i });
      await user.click(removeButtons[0]);
      
      expect(screen.getAllByPlaceholderText('Enter outcome option')).toHaveLength(3);
    });

    it('validates outcome requirements', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Validation Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing outcome validation in the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Business'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Select multiple choice but leave outcomes empty
      const multipleChoiceOption = screen.getByRole('button', { name: /multiple choice/i });
      await user.click(multipleChoiceOption);
      
      // Clear default outcomes
      const outcomeInputs = screen.getAllByPlaceholderText('Enter outcome option');
      for (const input of outcomeInputs) {
        await user.clear(input);
      }
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeDisabled();
      
      // Show validation message
      expect(screen.getByText('All outcomes must be filled')).toBeInTheDocument();
    });
  });

  describe('Step 3: Market Settings', () => {
    it('displays all configuration options', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete steps 1 and 2
      await user.type(screen.getByLabelText('Market Title *'), 'Settings Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing market settings configuration in the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Science'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i })); // Skip step 2 with defaults
      
      // Step 3: Market Settings
      expect(screen.getByText('Step 3 of 5: Market Settings')).toBeInTheDocument();
      expect(screen.getByText('Stake Limits')).toBeInTheDocument();
      expect(screen.getByText('Minimum Stake')).toBeInTheDocument();
      expect(screen.getByText('Maximum Stake')).toBeInTheDocument();
      expect(screen.getByText('Market Deadline')).toBeInTheDocument();
      expect(screen.getByText('Settlement Method')).toBeInTheDocument();
      expect(screen.getByText('Market Visibility')).toBeInTheDocument();
    });

    it('validates stake limits', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete steps 1 and 2
      await user.type(screen.getByLabelText('Market Title *'), 'Stake Validation Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing stake limit validation in the wizard settings.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Other'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Invalid stakes (min > max)
      const minStakeInput = screen.getByLabelText('Minimum Stake');
      const maxStakeInput = screen.getByLabelText('Maximum Stake');
      
      await user.clear(minStakeInput);
      await user.type(minStakeInput, '100');
      await user.clear(maxStakeInput);
      await user.type(maxStakeInput, '50');
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeDisabled();
      expect(screen.getByText('Maximum stake must be greater than minimum stake')).toBeInTheDocument();
    });

    it('validates deadline is in future', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete steps 1 and 2
      await user.type(screen.getByLabelText('Market Title *'), 'Deadline Validation Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing deadline validation in the wizard settings.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Crypto'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Set past date
      const deadlineInput = screen.getByLabelText('Market Deadline');
      await user.clear(deadlineInput);
      await user.type(deadlineInput, '2020-01-01T12:00');
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeDisabled();
      expect(screen.getByText('Deadline must be in the future')).toBeInTheDocument();
    });
  });

  describe('Step 4: Validation Criteria', () => {
    it('displays validation criteria input', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete steps 1, 2, and 3
      await user.type(screen.getByLabelText('Market Title *'), 'Validation Criteria Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing validation criteria step in the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Politics'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Step 4: Validation Criteria
      expect(screen.getByText('Step 4 of 5: Validation Criteria')).toBeInTheDocument();
      expect(screen.getByText('Event Criteria')).toBeInTheDocument();
      expect(screen.getByLabelText('Event Criteria')).toBeInTheDocument();
    });

    it('validates minimum criteria length', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete steps 1, 2, and 3
      await user.type(screen.getByLabelText('Market Title *'), 'Criteria Length Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing minimum criteria length validation in the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Sports'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Short criteria
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(criteriaInput, 'Too short');
      
      const nextButton = screen.getByRole('button', { name: /continue/i });
      expect(nextButton).toBeDisabled();
      expect(screen.getByText('Event criteria must be at least 100 characters')).toBeInTheDocument();
    });
  });

  describe('Step 5: Review and Submit', () => {
    it('displays market summary', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete all steps
      await user.type(screen.getByLabelText('Market Title *'), 'Complete Market Test');
      await user.type(screen.getByLabelText('Description *'), 'This is a complete market test with all steps filled out properly for submission.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Business'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(criteriaInput, 'This market will be settled based on official announcements from the company website and verified through multiple reliable news sources including Reuters, Bloomberg, and the company press releases.');
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Step 5: Review
      expect(screen.getByText('Step 5 of 5: Review & Submit')).toBeInTheDocument();
      expect(screen.getByText('Market Summary')).toBeInTheDocument();
      expect(screen.getByText('Complete Market Test')).toBeInTheDocument();
      expect(screen.getByText('Business')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create market/i })).toBeInTheDocument();
    });

    it('submits market successfully', async () => {
      const user = userEvent.setup();
      const mockMarket = { id: 'new-market-123', title: 'Complete Market Test' };
      mockCreateBet.mockResolvedValue(mockMarket);
      
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete all steps quickly
      await user.type(screen.getByLabelText('Market Title *'), 'Submission Test');
      await user.type(screen.getByLabelText('Description *'), 'This is a submission test to verify the market creation process works end-to-end.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Crypto'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(criteriaInput, 'This test market will be settled based on predefined test conditions for automated testing purposes and validation.');
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Submit
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      expect(screen.getByText('Creating Market...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(mockCreateBet).toHaveBeenCalled();
        expect(mockOnComplete).toHaveBeenCalledWith(mockMarket);
      });
    });

    it('handles submission errors', async () => {
      const user = userEvent.setup();
      mockCreateBet.mockRejectedValue(new Error('API Error'));
      
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete all steps quickly
      await user.type(screen.getByLabelText('Market Title *'), 'Error Test');
      await user.type(screen.getByLabelText('Description *'), 'This is an error test to verify proper error handling during market creation.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Other'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(criteriaInput, 'This error test market is designed to trigger API errors for testing error handling in the wizard submission.');
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Submit
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to create market')).toBeInTheDocument();
        expect(screen.getByText('Please try again or contact support if the problem persists.')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('allows going back to previous steps', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Navigation Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing navigation between steps in the wizard.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Entertainment'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Should be on step 2
      expect(screen.getByText('Step 2 of 5: Define Outcomes')).toBeInTheDocument();
      
      // Go back
      const backButton = screen.getByRole('button', { name: /back/i });
      await user.click(backButton);
      
      // Should be back on step 1
      expect(screen.getByText('Step 1 of 5: Basic Information')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Navigation Test')).toBeInTheDocument();
    });

    it('handles cancel action', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('preserves form data when navigating', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Fill step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Preservation Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing form data preservation during navigation between wizard steps.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Science'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Go to step 3
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Go back to step 1
      const backButton = screen.getByRole('button', { name: /back/i });
      await user.click(backButton);
      await user.click(backButton);
      
      // Data should be preserved
      expect(screen.getByDisplayValue('Preservation Test')).toBeInTheDocument();
      expect(screen.getByText('Science')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<CreateBetWizard {...defaultProps} />);
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByLabelText('Market Title *')).toBeInTheDocument();
      expect(screen.getByLabelText('Description *')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Tab through form elements
      await user.tab();
      expect(screen.getByLabelText('Market Title *')).toHaveFocus();
      
      await user.tab();
      expect(screen.getByLabelText('Description *')).toHaveFocus();
      
      await user.tab();
      expect(screen.getByText('Select Category')).toHaveFocus();
    });

    it('announces step changes to screen readers', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title *'), 'Accessibility Test');
      await user.type(screen.getByLabelText('Description *'), 'Testing accessibility features in the wizard step transitions.');
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Politics'));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const stepHeading = screen.getByText('Step 2 of 5: Define Outcomes');
      expect(stepHeading).toHaveAttribute('aria-live', 'polite');
    });
  });
});