/**
 * Custom Betting Platform - MarketOutcomes Component Tests
 * ========================================================
 * 
 * Comprehensive tests for the market outcomes display component.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MarketOutcomes } from '@/components/custom-betting/MarketOutcomes';
import { mockBinaryMarket, mockMultiChoiceMarket, mockSettledMarket, mockUsers } from '../../mocks/custom-betting-data';

describe('MarketOutcomes', () => {
  const mockOnOutcomeSelect = jest.fn();

  const defaultProps = {
    market: mockBinaryMarket,
    onOutcomeSelect: mockOnOutcomeSelect,
    userParticipation: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders all market outcomes', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        expect(screen.getByText(outcome.outcome_text)).toBeInTheDocument();
        expect(screen.getByText(`${outcome.current_odds}x`)).toBeInTheDocument();
        expect(screen.getByText(`${(outcome.implied_probability * 100).toFixed(1)}%`)).toBeInTheDocument();
      });
    });

    it('displays outcome statistics correctly', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        expect(screen.getByText(`$${outcome.total_backing.toLocaleString()} backing`)).toBeInTheDocument();
        expect(screen.getByText(`$${outcome.total_laying.toLocaleString()} laying`)).toBeInTheDocument();
      });
    });

    it('shows outcome cards in grid layout', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      const outcomeGrid = screen.getByTestId('outcomes-grid');
      expect(outcomeGrid).toHaveClass('grid', 'gap-4');
      
      // Binary market should have 2 columns
      expect(outcomeGrid).toHaveClass('grid-cols-1', 'md:grid-cols-2');
    });

    it('adapts grid layout for multiple choice markets', () => {
      render(<MarketOutcomes {...defaultProps} market={mockMultiChoiceMarket} />);
      
      const outcomeGrid = screen.getByTestId('outcomes-grid');
      // Multiple choice should use different grid layout
      expect(outcomeGrid).toHaveClass('md:grid-cols-1', 'lg:grid-cols-2');
    });
  });

  describe('Outcome Cards', () => {
    it('displays outcome card with all information', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      const firstOutcome = mockBinaryMarket.outcomes[0];
      
      // Check for outcome text
      expect(screen.getByText(firstOutcome.outcome_text)).toBeInTheDocument();
      
      // Check for odds display
      expect(screen.getByText(`${firstOutcome.current_odds}x`)).toBeInTheDocument();
      
      // Check for probability
      expect(screen.getByText(`${(firstOutcome.implied_probability * 100).toFixed(1)}%`)).toBeInTheDocument();
      
      // Check for volume information
      expect(screen.getByText(`$${firstOutcome.total_backing.toLocaleString()}`)).toBeInTheDocument();
      expect(screen.getByText(`$${firstOutcome.total_laying.toLocaleString()}`)).toBeInTheDocument();
    });

    it('makes outcome cards clickable when callback provided', async () => {
      const user = userEvent.setup();
      render(<MarketOutcomes {...defaultProps} />);
      
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      expect(mockOnOutcomeSelect).toHaveBeenCalledWith(mockBinaryMarket.outcomes[0]);
    });

    it('shows hover effects on interactive cards', async () => {
      const user = userEvent.setup();
      render(<MarketOutcomes {...defaultProps} />);
      
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      
      await user.hover(outcomeCard);
      expect(outcomeCard).toHaveClass('hover:shadow-lg');
    });

    it('disables cards when no callback provided', () => {
      render(<MarketOutcomes {...defaultProps} onOutcomeSelect={undefined} />);
      
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      expect(outcomeCard).not.toHaveClass('cursor-pointer');
      expect(outcomeCard).not.toHaveClass('hover:shadow-lg');
    });
  });

  describe('User Participation Indicator', () => {
    it('highlights outcome where user has position', () => {
      const userParticipation = {
        id: 'participation-1',
        user_id: mockUsers.user1,
        bet_id: mockBinaryMarket.id,
        outcome_id: mockBinaryMarket.outcomes[0].id,
        position_type: 'backing' as const,
        stake_amount: 100,
        desired_odds: 2.5,
        matched_amount: 100,
        status: 'active' as const,
        potential_payout: 250,
        created_at: '2024-07-15T10:30:00Z',
        updated_at: '2024-07-15T10:30:00Z',
        settled_at: null,
        payout_amount: null
      };

      render(<MarketOutcomes {...defaultProps} userParticipation={userParticipation} />);
      
      const userOutcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      expect(userOutcomeCard).toHaveClass('border-primary');
      
      // Should show position indicator
      expect(screen.getByText('Your Position')).toBeInTheDocument();
      expect(screen.getByText('Backing $100')).toBeInTheDocument();
    });

    it('shows different styling for laying positions', () => {
      const layingParticipation = {
        id: 'participation-2',
        user_id: mockUsers.user1,
        bet_id: mockBinaryMarket.id,
        outcome_id: mockBinaryMarket.outcomes[1].id,
        position_type: 'laying' as const,
        stake_amount: 50,
        desired_odds: 1.8,
        matched_amount: 25,
        status: 'pending' as const,
        potential_payout: 50,
        created_at: '2024-07-20T15:45:00Z',
        updated_at: '2024-07-20T15:45:00Z',
        settled_at: null,
        payout_amount: null
      };

      render(<MarketOutcomes {...defaultProps} userParticipation={layingParticipation} />);
      
      expect(screen.getByText('Laying $50')).toBeInTheDocument();
      expect(screen.getByText('$25 matched')).toBeInTheDocument();
    });

    it('shows winning position styling for settled markets', () => {
      const wonParticipation = {
        id: 'participation-3',
        user_id: mockUsers.user1,
        bet_id: mockSettledMarket.id,
        outcome_id: 'outcome-7', // Winning outcome
        position_type: 'backing' as const,
        stake_amount: 200,
        desired_odds: 1.4,
        matched_amount: 200,
        status: 'won' as const,
        potential_payout: 280,
        created_at: '2024-05-10T09:00:00Z',
        updated_at: '2024-07-01T16:45:00Z',
        settled_at: '2024-07-01T16:45:00Z',
        payout_amount: 280
      };

      render(<MarketOutcomes {...defaultProps} market={mockSettledMarket} userParticipation={wonParticipation} />);
      
      const winningCard = screen.getByTestId('outcome-outcome-7');
      expect(winningCard).toHaveClass('border-green-500');
      expect(screen.getByText('Won $280')).toBeInTheDocument();
    });
  });

  describe('Settled Market Display', () => {
    it('highlights winning outcome in settled market', () => {
      render(<MarketOutcomes {...defaultProps} market={mockSettledMarket} />);
      
      const winningOutcome = mockSettledMarket.outcomes.find(o => o.is_winning_outcome);
      const winningCard = screen.getByTestId(`outcome-${winningOutcome!.id}`);
      
      expect(winningCard).toHaveClass('border-green-500', 'bg-green-50');
      expect(screen.getByText('Winner')).toBeInTheDocument();
    });

    it('grays out losing outcomes in settled market', () => {
      render(<MarketOutcomes {...defaultProps} market={mockSettledMarket} />);
      
      const losingOutcome = mockSettledMarket.outcomes.find(o => !o.is_winning_outcome);
      const losingCard = screen.getByTestId(`outcome-${losingOutcome!.id}`);
      
      expect(losingCard).toHaveClass('opacity-60');
      expect(losingCard).not.toHaveClass('cursor-pointer');
    });

    it('disables outcome selection for settled markets', () => {
      render(<MarketOutcomes {...defaultProps} market={mockSettledMarket} />);
      
      const outcomeCards = screen.getAllByTestId(/^outcome-/);
      outcomeCards.forEach(card => {
        expect(card).not.toHaveClass('cursor-pointer');
      });
    });
  });

  describe('Market Status Effects', () => {
    it('shows different styling for suspended market', () => {
      const suspendedMarket = { ...mockBinaryMarket, status: 'suspended' as const };
      render(<MarketOutcomes {...defaultProps} market={suspendedMarket} />);
      
      const outcomeCards = screen.getAllByTestId(/^outcome-/);
      outcomeCards.forEach(card => {
        expect(card).toHaveClass('opacity-75');
      });
      
      expect(screen.getByText('Market Suspended')).toBeInTheDocument();
    });

    it('shows cancelled market styling', () => {
      const cancelledMarket = { ...mockBinaryMarket, status: 'cancelled' as const };
      render(<MarketOutcomes {...defaultProps} market={cancelledMarket} />);
      
      const outcomeCards = screen.getAllByTestId(/^outcome-/);
      outcomeCards.forEach(card => {
        expect(card).toHaveClass('opacity-50');
      });
      
      expect(screen.getByText('Market Cancelled')).toBeInTheDocument();
    });
  });

  describe('Odds Movement', () => {
    it('shows odds change indicators', () => {
      const marketWithOddsHistory = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          odds_change: outcome.id === 'outcome-1' ? 0.1 : -0.05,
          odds_trend: outcome.id === 'outcome-1' ? 'up' as const : 'down' as const
        }))
      };

      render(<MarketOutcomes {...defaultProps} market={marketWithOddsHistory} />);
      
      expect(screen.getByText('↗')).toBeInTheDocument(); // Up arrow
      expect(screen.getByText('↘')).toBeInTheDocument(); // Down arrow
      expect(screen.getByText('+0.1')).toBeInTheDocument();
      expect(screen.getByText('-0.05')).toBeInTheDocument();
    });

    it('shows stable odds without indicators', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      // No odds change indicators should be present
      expect(screen.queryByText('↗')).not.toBeInTheDocument();
      expect(screen.queryByText('↘')).not.toBeInTheDocument();
    });
  });

  describe('Volume and Liquidity', () => {
    it('displays liquidity depth for each outcome', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        const totalVolume = outcome.total_backing + outcome.total_laying;
        expect(screen.getByText(`$${totalVolume.toLocaleString()} volume`)).toBeInTheDocument();
      });
    });

    it('shows low liquidity warning', () => {
      const lowLiquidityMarket = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          total_backing: 50,
          total_laying: 25
        }))
      };

      render(<MarketOutcomes {...defaultProps} market={lowLiquidityMarket} />);
      
      expect(screen.getAllByText('Low Liquidity')).toHaveLength(2);
    });

    it('highlights high volume outcomes', () => {
      const highVolumeOutcome = mockBinaryMarket.outcomes[0];
      const totalVolume = highVolumeOutcome.total_backing + highVolumeOutcome.total_laying;
      
      if (totalVolume > 20000) {
        render(<MarketOutcomes {...defaultProps} />);
        
        const outcomeCard = screen.getByTestId(`outcome-${highVolumeOutcome.id}`);
        expect(outcomeCard).toHaveClass('border-blue-200');
        expect(screen.getByText('High Volume')).toBeInTheDocument();
      }
    });
  });

  describe('Real-time Updates', () => {
    it('updates odds when market data changes', () => {
      const { rerender } = render(<MarketOutcomes {...defaultProps} />);
      
      // Update odds
      const updatedMarket = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          current_odds: outcome.current_odds + 0.1,
          implied_probability: outcome.implied_probability - 0.02
        }))
      };
      
      rerender(<MarketOutcomes {...defaultProps} market={updatedMarket} />);
      
      expect(screen.getByText(`${updatedMarket.outcomes[0].current_odds}x`)).toBeInTheDocument();
    });

    it('shows loading state during odds update', () => {
      render(<MarketOutcomes {...defaultProps} isUpdating={true} />);
      
      expect(screen.getByText('Updating odds...')).toBeInTheDocument();
      expect(screen.getByTestId('odds-loading')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for outcome cards', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        const card = screen.getByTestId(`outcome-${outcome.id}`);
        expect(card).toHaveAttribute('aria-label', expect.stringContaining(outcome.outcome_text));
        expect(card).toHaveAttribute('role', 'button');
      });
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<MarketOutcomes {...defaultProps} />);
      
      const firstOutcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      
      await user.tab();
      expect(firstOutcomeCard).toHaveFocus();
      
      await user.keyboard('{Enter}');
      expect(mockOnOutcomeSelect).toHaveBeenCalledWith(mockBinaryMarket.outcomes[0]);
    });

    it('announces odds changes to screen readers', () => {
      const marketWithOddsHistory = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          odds_change: 0.1,
          odds_trend: 'up' as const
        }))
      };

      render(<MarketOutcomes {...defaultProps} market={marketWithOddsHistory} />);
      
      const oddsElement = screen.getByText('2.5x');
      expect(oddsElement).toHaveAttribute('aria-live', 'polite');
    });

    it('provides context for probabilities', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        const probability = (outcome.implied_probability * 100).toFixed(1);
        const probabilityElement = screen.getByText(`${probability}%`);
        expect(probabilityElement).toHaveAttribute('aria-label', `${probability} percent implied probability`);
      });
    });
  });

  describe('Mobile Responsiveness', () => {
    it('stacks outcome cards on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<MarketOutcomes {...defaultProps} />);
      
      const outcomeGrid = screen.getByTestId('outcomes-grid');
      expect(outcomeGrid).toHaveClass('grid-cols-1');
    });

    it('shows condensed information on small screens', () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      // Check for responsive text sizes
      const outcomeText = screen.getByText(mockBinaryMarket.outcomes[0].outcome_text);
      expect(outcomeText).toHaveClass('text-sm', 'md:text-base');
    });
  });

  describe('Error Handling', () => {
    it('handles missing outcome data gracefully', () => {
      const incompleteMarket = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          current_odds: 0,
          total_backing: 0,
          total_laying: 0
        }))
      };

      render(<MarketOutcomes {...defaultProps} market={incompleteMarket} />);
      
      expect(screen.getAllByText('0x')).toHaveLength(2);
      expect(screen.getAllByText('$0 backing')).toHaveLength(2);
    });

    it('shows placeholder when no outcomes available', () => {
      const emptyMarket = {
        ...mockBinaryMarket,
        outcomes: []
      };

      render(<MarketOutcomes {...defaultProps} market={emptyMarket} />);
      
      expect(screen.getByText('No outcomes available')).toBeInTheDocument();
    });
  });
});