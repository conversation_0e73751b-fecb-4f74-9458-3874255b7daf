/**
 * Custom Betting Platform - MarketHeader Component Tests
 * =====================================================
 * 
 * Comprehensive tests for the market header display component.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MarketHeader } from '@/components/custom-betting/MarketHeader';
import { mockBinaryMarket, mockMultiChoiceMarket, mockSettledMarket, mockUsers } from '../../mocks/custom-betting-data';

describe('MarketHeader', () => {
  const defaultProps = {
    market: mockBinaryMarket,
    userParticipation: null
  };

  describe('Component Rendering', () => {
    it('renders market title and basic information', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText(mockBinaryMarket.title)).toBeInTheDocument();
      expect(screen.getByText(mockBinaryMarket.category.charAt(0).toUpperCase() + mockBinaryMarket.category.slice(1))).toBeInTheDocument();
      expect(screen.getByText(mockBinaryMarket.status.charAt(0).toUpperCase() + mockBinaryMarket.status.slice(1))).toBeInTheDocument();
    });

    it('displays market statistics correctly', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText(`$${mockBinaryMarket.total_stakes.toLocaleString()}`)).toBeInTheDocument();
      expect(screen.getByText(`${mockBinaryMarket.total_participants} participants`)).toBeInTheDocument();
    });

    it('shows market tags', () => {
      render(<MarketHeader {...defaultProps} />);
      
      mockBinaryMarket.tags.forEach(tag => {
        expect(screen.getByText(`#${tag}`)).toBeInTheDocument();
      });
    });

    it('displays creator information', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText('Created by')).toBeInTheDocument();
      expect(screen.getByText(mockBinaryMarket.creator_user_id)).toBeInTheDocument();
    });

    it('shows market deadlines', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText('Market Deadline')).toBeInTheDocument();
      expect(screen.getByText('Verification Deadline')).toBeInTheDocument();
      
      const marketDeadline = new Date(mockBinaryMarket.deadline).toLocaleDateString();
      const verificationDeadline = new Date(mockBinaryMarket.verification_deadline).toLocaleDateString();
      
      expect(screen.getByText(marketDeadline)).toBeInTheDocument();
      expect(screen.getByText(verificationDeadline)).toBeInTheDocument();
    });
  });

  describe('Market Status Display', () => {
    it('shows appropriate status badge for active market', () => {
      render(<MarketHeader {...defaultProps} />);
      
      const statusBadge = screen.getByText('Active');
      expect(statusBadge).toHaveClass('bg-green-100', 'text-green-800');
    });

    it('shows appropriate status badge for settled market', () => {
      render(<MarketHeader {...defaultProps} market={mockSettledMarket} />);
      
      const statusBadge = screen.getByText('Settled');
      expect(statusBadge).toHaveClass('bg-blue-100', 'text-blue-800');
    });

    it('shows suspended status with warning styling', () => {
      const suspendedMarket = { ...mockBinaryMarket, status: 'suspended' as const };
      render(<MarketHeader {...defaultProps} market={suspendedMarket} />);
      
      const statusBadge = screen.getByText('Suspended');
      expect(statusBadge).toHaveClass('bg-yellow-100', 'text-yellow-800');
    });

    it('shows cancelled status with danger styling', () => {
      const cancelledMarket = { ...mockBinaryMarket, status: 'cancelled' as const };
      render(<MarketHeader {...defaultProps} market={cancelledMarket} />);
      
      const statusBadge = screen.getByText('Cancelled');
      expect(statusBadge).toHaveClass('bg-red-100', 'text-red-800');
    });
  });

  describe('User Participation Display', () => {
    it('shows participation alert when user has position', () => {
      const userParticipation = {
        id: 'participation-1',
        user_id: mockUsers.user1,
        bet_id: mockBinaryMarket.id,
        outcome_id: 'outcome-1',
        position_type: 'backing' as const,
        stake_amount: 100,
        desired_odds: 2.5,
        matched_amount: 100,
        status: 'active' as const,
        potential_payout: 250,
        created_at: '2024-07-15T10:30:00Z',
        updated_at: '2024-07-15T10:30:00Z',
        settled_at: null,
        payout_amount: null
      };

      render(<MarketHeader {...defaultProps} userParticipation={userParticipation} />);
      
      expect(screen.getByText('Your Position')).toBeInTheDocument();
      expect(screen.getByText('Backing')).toBeInTheDocument();
      expect(screen.getByText('$100 stake')).toBeInTheDocument();
      expect(screen.getByText('$250 potential payout')).toBeInTheDocument();
    });

    it('shows laying position correctly', () => {
      const layingParticipation = {
        id: 'participation-2',
        user_id: mockUsers.user1,
        bet_id: mockBinaryMarket.id,
        outcome_id: 'outcome-2',
        position_type: 'laying' as const,
        stake_amount: 50,
        desired_odds: 1.8,
        matched_amount: 25,
        status: 'pending' as const,
        potential_payout: 50,
        created_at: '2024-07-20T15:45:00Z',
        updated_at: '2024-07-20T15:45:00Z',
        settled_at: null,
        payout_amount: null
      };

      render(<MarketHeader {...defaultProps} userParticipation={layingParticipation} />);
      
      expect(screen.getByText('Laying')).toBeInTheDocument();
      expect(screen.getByText('$50 stake')).toBeInTheDocument();
      expect(screen.getByText('$25 matched')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
    });

    it('shows won position with payout', () => {
      const wonParticipation = {
        id: 'participation-3',
        user_id: mockUsers.user1,
        bet_id: mockSettledMarket.id,
        outcome_id: 'outcome-7',
        position_type: 'backing' as const,
        stake_amount: 200,
        desired_odds: 1.4,
        matched_amount: 200,
        status: 'won' as const,
        potential_payout: 280,
        created_at: '2024-05-10T09:00:00Z',
        updated_at: '2024-07-01T16:45:00Z',
        settled_at: '2024-07-01T16:45:00Z',
        payout_amount: 280
      };

      render(<MarketHeader {...defaultProps} market={mockSettledMarket} userParticipation={wonParticipation} />);
      
      expect(screen.getByText('Won')).toBeInTheDocument();
      expect(screen.getByText('$280 payout received')).toBeInTheDocument();
      expect(screen.getByText('bg-green-100')).toBeInTheDocument();
    });
  });

  describe('Time Display', () => {
    it('shows time until deadline for active markets', () => {
      // Mock a market with deadline in 2 days
      const futureDeadline = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString();
      const marketWithDeadline = { ...mockBinaryMarket, deadline: futureDeadline };
      
      render(<MarketHeader {...defaultProps} market={marketWithDeadline} />);
      
      expect(screen.getByText(/time left/i)).toBeInTheDocument();
      expect(screen.getByText(/2 days/i)).toBeInTheDocument();
    });

    it('shows expired status for past deadline', () => {
      const pastDeadline = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const expiredMarket = { ...mockBinaryMarket, deadline: pastDeadline };
      
      render(<MarketHeader {...defaultProps} market={expiredMarket} />);
      
      expect(screen.getByText('Deadline Passed')).toBeInTheDocument();
      expect(screen.getByText('text-red-600')).toBeInTheDocument();
    });

    it('shows settlement date for settled markets', () => {
      render(<MarketHeader {...defaultProps} market={mockSettledMarket} />);
      
      expect(screen.getByText('Settled On')).toBeInTheDocument();
      const settledDate = new Date(mockSettledMarket.updated_at).toLocaleDateString();
      expect(screen.getByText(settledDate)).toBeInTheDocument();
    });
  });

  describe('Share and Actions', () => {
    it('shows share button', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /share market/i })).toBeInTheDocument();
    });

    it('handles share action', async () => {
      const user = userEvent.setup();
      
      // Mock navigator.share
      Object.assign(navigator, {
        share: jest.fn().mockResolvedValue(undefined)
      });
      
      render(<MarketHeader {...defaultProps} />);
      
      const shareButton = screen.getByRole('button', { name: /share market/i });
      await user.click(shareButton);
      
      expect(navigator.share).toHaveBeenCalledWith({
        title: mockBinaryMarket.title,
        text: mockBinaryMarket.description,
        url: expect.stringContaining(`/markets/${mockBinaryMarket.id}`)
      });
    });

    it('falls back to copy URL when share not supported', async () => {
      const user = userEvent.setup();
      
      // Mock clipboard
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValue(undefined)
        },
        share: undefined
      });
      
      render(<MarketHeader {...defaultProps} />);
      
      const shareButton = screen.getByRole('button', { name: /share market/i });
      await user.click(shareButton);
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
        expect.stringContaining(`/markets/${mockBinaryMarket.id}`)
      );
      expect(screen.getByText('Market URL copied to clipboard!')).toBeInTheDocument();
    });

    it('shows favorite button for authenticated users', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /add to favorites/i })).toBeInTheDocument();
    });

    it('shows report button', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /report market/i })).toBeInTheDocument();
    });
  });

  describe('Market Type Display', () => {
    it('shows binary market type indicator', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText('Binary')).toBeInTheDocument();
    });

    it('shows multiple choice market type indicator', () => {
      render(<MarketHeader {...defaultProps} market={mockMultiChoiceMarket} />);
      
      expect(screen.getByText('Multiple Choice')).toBeInTheDocument();
      expect(screen.getByText(`${mockMultiChoiceMarket.outcomes.length} outcomes`)).toBeInTheDocument();
    });
  });

  describe('Settlement Information', () => {
    it('shows settlement method for active markets', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText('Settlement: Oracle')).toBeInTheDocument();
    });

    it('shows winning outcome for settled markets', () => {
      render(<MarketHeader {...defaultProps} market={mockSettledMarket} />);
      
      const winningOutcome = mockSettledMarket.outcomes.find(o => o.is_winning_outcome);
      expect(screen.getByText('Winner:')).toBeInTheDocument();
      expect(screen.getByText(winningOutcome!.outcome_text)).toBeInTheDocument();
    });

    it('shows escrow information', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText(`${mockBinaryMarket.escrow_percentage}% Escrowed`)).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('stacks information vertically on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<MarketHeader {...defaultProps} />);
      
      const headerContainer = screen.getByTestId('market-header');
      expect(headerContainer).toHaveClass('flex-col', 'md:flex-row');
    });

    it('shows condensed view on smaller screens', () => {
      render(<MarketHeader {...defaultProps} />);
      
      // Check for responsive classes
      expect(screen.getByText(mockBinaryMarket.title)).toHaveClass('text-lg', 'md:text-2xl');
    });
  });

  describe('Loading and Error States', () => {
    it('handles missing market data gracefully', () => {
      const incompleteMarket = {
        ...mockBinaryMarket,
        total_stakes: 0,
        total_participants: 0,
        tags: []
      };
      
      render(<MarketHeader {...defaultProps} market={incompleteMarket} />);
      
      expect(screen.getByText('$0')).toBeInTheDocument();
      expect(screen.getByText('0 participants')).toBeInTheDocument();
      expect(screen.queryByText(/^#/)).not.toBeInTheDocument();
    });

    it('shows placeholder for missing creator', () => {
      const marketWithoutCreator = {
        ...mockBinaryMarket,
        creator_user_id: ''
      };
      
      render(<MarketHeader {...defaultProps} market={marketWithoutCreator} />);
      
      expect(screen.getByText('Unknown Creator')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(<MarketHeader {...defaultProps} />);
      
      const titleHeading = screen.getByRole('heading', { level: 1 });
      expect(titleHeading).toHaveTextContent(mockBinaryMarket.title);
    });

    it('has proper ARIA labels for actions', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByLabelText('Share this market')).toBeInTheDocument();
      expect(screen.getByLabelText('Add to favorites')).toBeInTheDocument();
      expect(screen.getByLabelText('Report market')).toBeInTheDocument();
    });

    it('provides status information for screen readers', () => {
      render(<MarketHeader {...defaultProps} />);
      
      const statusElement = screen.getByText('Active');
      expect(statusElement).toHaveAttribute('aria-label', 'Market status: Active');
    });

    it('includes semantic time elements', () => {
      render(<MarketHeader {...defaultProps} />);
      
      const deadlineElement = screen.getByTestId('market-deadline');
      expect(deadlineElement).toHaveAttribute('dateTime', mockBinaryMarket.deadline);
    });

    it('supports keyboard navigation for actions', async () => {
      const user = userEvent.setup();
      render(<MarketHeader {...defaultProps} />);
      
      await user.tab();
      expect(screen.getByRole('button', { name: /share market/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /add to favorites/i })).toHaveFocus();
    });
  });

  describe('Visual Indicators', () => {
    it('shows market health indicator', () => {
      render(<MarketHeader {...defaultProps} />);
      
      // Market with good volume should show green indicator
      expect(screen.getByTestId('market-health')).toHaveClass('text-green-500');
    });

    it('shows low volume warning', () => {
      const lowVolumeMarket = {
        ...mockBinaryMarket,
        total_stakes: 100 // Very low volume
      };
      
      render(<MarketHeader {...defaultProps} market={lowVolumeMarket} />);
      
      expect(screen.getByText('Low Volume')).toBeInTheDocument();
      expect(screen.getByTestId('market-health')).toHaveClass('text-yellow-500');
    });

    it('shows verification status indicator', () => {
      render(<MarketHeader {...defaultProps} />);
      
      expect(screen.getByText('Oracle Verified')).toBeInTheDocument();
      expect(screen.getByTestId('verification-badge')).toBeInTheDocument();
    });
  });
});