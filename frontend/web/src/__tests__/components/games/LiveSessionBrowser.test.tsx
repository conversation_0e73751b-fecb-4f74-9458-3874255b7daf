/**
 * LiveSessionBrowser Component Tests
 * Critical component for browsing live game sessions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { LiveSessionBrowser } from '@/components/games/LiveSessionBrowser';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import { GameSession } from '@/lib/api';

// Mock hooks
jest.mock('@/hooks/useApi');
jest.mock('@/hooks/useWebSocket');

const mockApi = {
  getActiveSessions: jest.fn(),
  getSessionPreviews: jest.fn(),
};

const mockWebSocket = {
  isConnected: true,
  lastMessage: null,
  sendMessage: jest.fn(),
  connect: jest.fn(),
  disconnect: jest.fn(),
};

describe('LiveSessionBrowser Component - Real-time Session Discovery', () => {
  const mockSessions: GameSession[] = [
    {
      id: 'session-1',
      game_id: 'game-1',
      session_name: 'Trivia Championship',
      status: 'in_progress',
      entry_fee: 5,
      min_participants: 2,
      max_participants: 10,
      current_participants: 8,
      current_spectators: 25,
      prize_pool: 50,
      scheduled_start_time: new Date().toISOString(),
      estimated_duration_minutes: 30,
      allow_spectators: true,
      allow_practice_mode: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'session-2',
      game_id: 'game-2',
      session_name: 'Speed Chess Blitz',
      status: 'waiting',
      entry_fee: 10,
      min_participants: 2,
      max_participants: 2,
      current_participants: 1,
      current_spectators: 5,
      prize_pool: 20,
      scheduled_start_time: new Date(Date.now() + 600000).toISOString(),
      estimated_duration_minutes: 15,
      allow_spectators: true,
      allow_practice_mode: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'session-3',
      game_id: 'game-3',
      session_name: 'Reaction Time Tournament',
      status: 'in_progress',
      entry_fee: 0,
      min_participants: 4,
      max_participants: 20,
      current_participants: 15,
      current_spectators: 40,
      prize_pool: 0,
      scheduled_start_time: new Date(Date.now() - 300000).toISOString(),
      estimated_duration_minutes: 10,
      allow_spectators: true,
      allow_practice_mode: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useApi as jest.Mock).mockReturnValue(mockApi);
    (useWebSocket as jest.Mock).mockReturnValue(mockWebSocket);
    mockApi.getActiveSessions.mockResolvedValue(mockSessions);
  });

  it('should render and load sessions on mount', async () => {
    render(<LiveSessionBrowser />);

    expect(screen.getByText('Live Game Sessions')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(mockApi.getActiveSessions).toHaveBeenCalled();
      expect(screen.getByText('Trivia Championship')).toBeInTheDocument();
      expect(screen.getByText('Speed Chess Blitz')).toBeInTheDocument();
      expect(screen.getByText('Reaction Time Tournament')).toBeInTheDocument();
    });
  });

  it('should display WebSocket connection status', async () => {
    render(<LiveSessionBrowser />);
    
    await waitFor(() => {
      const connectionIndicator = screen.getByTestId('connection-status');
      expect(connectionIndicator).toBeInTheDocument();
      expect(connectionIndicator).toHaveTextContent('Connected');
      expect(connectionIndicator).toHaveClass('text-green-600');
    });
  });

  it('should filter sessions by category', async () => {
    mockApi.getActiveSessions.mockResolvedValue([
      { ...mockSessions[0], game_category: 'trivia' },
      { ...mockSessions[1], game_category: 'strategy' },
      { ...mockSessions[2], game_category: 'reaction' },
    ]);

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText('Trivia Championship')).toBeInTheDocument();
    });

    // Click on category filter
    const strategyFilter = screen.getByRole('button', { name: /strategy/i });
    fireEvent.click(strategyFilter);

    await waitFor(() => {
      expect(screen.queryByText('Trivia Championship')).not.toBeInTheDocument();
      expect(screen.getByText('Speed Chess Blitz')).toBeInTheDocument();
      expect(screen.queryByText('Reaction Time Tournament')).not.toBeInTheDocument();
    });
  });

  it('should filter sessions by status', async () => {
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getAllByTestId(/game-preview-card/)).toHaveLength(3);
    });

    // Filter by "Can Join" status
    const canJoinFilter = screen.getByRole('checkbox', { name: /can join/i });
    fireEvent.click(canJoinFilter);

    await waitFor(() => {
      // Only waiting session with slots should show
      expect(screen.getByText('Speed Chess Blitz')).toBeInTheDocument();
      expect(screen.queryByText('Trivia Championship')).not.toBeInTheDocument();
    });
  });

  it('should sort sessions by excitement score', async () => {
    mockApi.getSessionPreviews.mockResolvedValue([
      { sessionId: 'session-1', excitementScore: 90 },
      { sessionId: 'session-2', excitementScore: 30 },
      { sessionId: 'session-3', excitementScore: 75 },
    ]);

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText('Trivia Championship')).toBeInTheDocument();
    });

    // Change sort to "Hot Games"
    const sortSelect = screen.getByRole('combobox', { name: /sort by/i });
    fireEvent.change(sortSelect, { target: { value: 'excitement' } });

    await waitFor(() => {
      const cards = screen.getAllByTestId(/game-preview-card/);
      expect(within(cards[0]).getByText('Trivia Championship')).toBeInTheDocument();
      expect(within(cards[1]).getByText('Reaction Time Tournament')).toBeInTheDocument();
      expect(within(cards[2]).getByText('Speed Chess Blitz')).toBeInTheDocument();
    });
  });

  it('should search sessions by name', async () => {
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getAllByTestId(/game-preview-card/)).toHaveLength(3);
    });

    const searchInput = screen.getByPlaceholderText(/search games/i);
    fireEvent.change(searchInput, { target: { value: 'chess' } });

    await waitFor(() => {
      expect(screen.getByText('Speed Chess Blitz')).toBeInTheDocument();
      expect(screen.queryByText('Trivia Championship')).not.toBeInTheDocument();
      expect(screen.queryByText('Reaction Time Tournament')).not.toBeInTheDocument();
    });
  });

  it('should handle real-time updates via WebSocket', async () => {
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText('Trivia Championship')).toBeInTheDocument();
    });

    // Simulate WebSocket message for participant update
    const wsMessage = {
      type: 'participant_update',
      data: {
        sessionId: 'session-1',
        participantCount: 9,
        spectatorCount: 30,
      },
    };

    // Trigger WebSocket update
    const { lastMessage } = (useWebSocket as jest.Mock).mock.results[0].value;
    (useWebSocket as jest.Mock).mockReturnValue({
      ...mockWebSocket,
      lastMessage: wsMessage,
    });

    const { rerender } = render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText(/9.*players/i)).toBeInTheDocument();
      expect(screen.getByText(/30.*watching/i)).toBeInTheDocument();
    });
  });

  it('should auto-refresh sessions periodically', async () => {
    jest.useFakeTimers();
    
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(mockApi.getActiveSessions).toHaveBeenCalledTimes(1);
    });

    // Fast-forward 30 seconds (default refresh interval)
    jest.advanceTimersByTime(30000);

    await waitFor(() => {
      expect(mockApi.getActiveSessions).toHaveBeenCalledTimes(2);
    });

    jest.useRealTimers();
  });

  it('should handle loading states properly', async () => {
    mockApi.getActiveSessions.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockSessions), 100))
    );

    render(<LiveSessionBrowser />);

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      expect(screen.getByText('Trivia Championship')).toBeInTheDocument();
    });
  });

  it('should handle error states gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockApi.getActiveSessions.mockRejectedValue(new Error('Network error'));

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText(/unable to load sessions/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
    });

    consoleErrorSpy.mockRestore();
  });

  it('should handle empty state when no sessions', async () => {
    mockApi.getActiveSessions.mockResolvedValue([]);

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText(/no live sessions/i)).toBeInTheDocument();
      expect(screen.getByText(/check back later/i)).toBeInTheDocument();
    });
  });

  it('should navigate to session details on click', async () => {
    const onSessionClick = jest.fn();
    
    render(<LiveSessionBrowser onSessionClick={onSessionClick} />);

    await waitFor(() => {
      const triviaCard = screen.getByText('Trivia Championship').closest('[data-testid*="game-preview-card"]');
      expect(triviaCard).toBeInTheDocument();
      
      if (triviaCard) {
        fireEvent.click(triviaCard);
        expect(onSessionClick).toHaveBeenCalledWith(mockSessions[0]);
      }
    });
  });

  it('should show high stakes indicator for expensive games', async () => {
    const highStakesSession = {
      ...mockSessions[0],
      entry_fee: 100,
      prize_pool: 1000,
    };
    
    mockApi.getActiveSessions.mockResolvedValue([highStakesSession]);

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getByText(/high stakes/i)).toBeInTheDocument();
      expect(screen.getByText('$100')).toBeInTheDocument();
    });
  });

  it('should indicate free-to-play games', async () => {
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      const freeGame = screen.getByText('Reaction Time Tournament')
        .closest('[data-testid*="game-preview-card"]');
      
      expect(within(freeGame!).getByText(/free/i)).toBeInTheDocument();
    });
  });

  it('should be keyboard navigable', async () => {
    render(<LiveSessionBrowser />);

    await waitFor(() => {
      expect(screen.getAllByTestId(/game-preview-card/)).toHaveLength(3);
    });

    const firstCard = screen.getAllByTestId(/game-preview-card/)[0];
    firstCard.focus();

    expect(document.activeElement).toBe(firstCard);

    // Tab to next card
    fireEvent.keyDown(firstCard, { key: 'Tab' });
    
    const secondCard = screen.getAllByTestId(/game-preview-card/)[1];
    expect(document.activeElement).toBe(secondCard);
  });

  it('should handle virtual scrolling for many sessions', async () => {
    // Create 100 mock sessions
    const manySessions = Array.from({ length: 100 }, (_, i) => ({
      ...mockSessions[0],
      id: `session-${i}`,
      session_name: `Game Session ${i}`,
    }));

    mockApi.getActiveSessions.mockResolvedValue(manySessions);

    render(<LiveSessionBrowser />);

    await waitFor(() => {
      // Should use virtual scrolling - not all 100 items rendered
      const renderedCards = screen.getAllByTestId(/game-preview-card/);
      expect(renderedCards.length).toBeLessThan(100);
      expect(renderedCards.length).toBeGreaterThan(0);
    });
  });
});