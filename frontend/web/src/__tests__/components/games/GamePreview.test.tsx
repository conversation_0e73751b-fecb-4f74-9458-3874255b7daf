/**
 * GamePreview Component Tests
 * Critical component for real-time game preview system
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { GamePreview } from '@/components/games/GamePreview';
import { GameSession, GamePreviewData } from '@/lib/api';

// Mock the useWebSocket hook
jest.mock('@/hooks/useWebSocket', () => ({
  useWebSocket: jest.fn(() => ({
    isConnected: true,
    lastMessage: null,
    sendMessage: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
  })),
}));

describe('GamePreview Component - Primary UX Feature', () => {
  const mockSession: GameSession = {
    id: 'session-123',
    game_id: 'game-456',
    session_name: 'Friday Night Trivia',
    status: 'in_progress',
    entry_fee: 10,
    min_participants: 2,
    max_participants: 8,
    current_participants: 6,
    current_spectators: 12,
    prize_pool: 100,
    scheduled_start_time: new Date().toISOString(),
    estimated_duration_minutes: 30,
    allow_spectators: true,
    allow_practice_mode: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const mockPreviewData: GamePreviewData = {
    sessionId: 'session-123',
    gameName: 'Trivia Master',
    gameCategory: 'trivia',
    participantCount: 6,
    spectatorCount: 12,
    currentLeader: 'Player1',
    recentActions: [
      'Player1 answered correctly',
      'Player2 joined the game',
      'Player3 got bonus points'
    ],
    excitementScore: 85,
    lastUpdate: new Date().toISOString(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render game preview card with all essential information', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);

    // Session name
    expect(screen.getByText('Friday Night Trivia')).toBeInTheDocument();
    
    // Game category
    expect(screen.getByText('trivia')).toBeInTheDocument();
    
    // Participant count
    expect(screen.getByText(/6.*players/i)).toBeInTheDocument();
    
    // Spectator count
    expect(screen.getByText(/12.*watching/i)).toBeInTheDocument();
    
    // Current leader
    expect(screen.getByText(/Player1/)).toBeInTheDocument();
    
    // Prize pool
    expect(screen.getByText(/\$100/)).toBeInTheDocument();
  });

  it('should display live indicator when game is in progress', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    const liveIndicator = screen.getByText('LIVE');
    expect(liveIndicator).toBeInTheDocument();
    expect(liveIndicator).toHaveClass('bg-red-500');
  });

  it('should show recent game actions', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    expect(screen.getByText('Player1 answered correctly')).toBeInTheDocument();
    expect(screen.getByText('Player2 joined the game')).toBeInTheDocument();
    expect(screen.getByText('Player3 got bonus points')).toBeInTheDocument();
  });

  it('should display excitement score as heat indicator', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    // High excitement should show fire icon or hot indicator
    const excitementIndicator = screen.getByTestId('excitement-indicator');
    expect(excitementIndicator).toBeInTheDocument();
    expect(excitementIndicator).toHaveTextContent('85');
  });

  it('should handle missing preview data gracefully', () => {
    render(<GamePreview session={mockSession} previewData={null} />);
    
    // Should still show basic session info
    expect(screen.getByText('Friday Night Trivia')).toBeInTheDocument();
    expect(screen.getByText(/6.*players/i)).toBeInTheDocument();
    
    // Should show loading or placeholder for dynamic content
    expect(screen.queryByText('Player1')).not.toBeInTheDocument();
  });

  it('should update in real-time when preview data changes', async () => {
    const { rerender } = render(
      <GamePreview session={mockSession} previewData={mockPreviewData} />
    );
    
    expect(screen.getByText('Player1')).toBeInTheDocument();
    
    // Update with new leader
    const updatedPreviewData = {
      ...mockPreviewData,
      currentLeader: 'Player2',
      participantCount: 7,
    };
    
    rerender(<GamePreview session={mockSession} previewData={updatedPreviewData} />);
    
    await waitFor(() => {
      expect(screen.getByText('Player2')).toBeInTheDocument();
      expect(screen.getByText(/7.*players/i)).toBeInTheDocument();
    });
  });

  it('should handle different game statuses appropriately', () => {
    const waitingSession = { ...mockSession, status: 'waiting' as const };
    const { rerender } = render(
      <GamePreview session={waitingSession} previewData={mockPreviewData} />
    );
    
    // Waiting status
    expect(screen.getByText('WAITING')).toBeInTheDocument();
    
    // Starting status
    const startingSession = { ...mockSession, status: 'starting' as const };
    rerender(<GamePreview session={startingSession} previewData={mockPreviewData} />);
    expect(screen.getByText('STARTING')).toBeInTheDocument();
    
    // Completed status
    const completedSession = { ...mockSession, status: 'completed' as const };
    rerender(<GamePreview session={completedSession} previewData={mockPreviewData} />);
    expect(screen.getByText('COMPLETED')).toBeInTheDocument();
  });

  it('should be mobile responsive', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    const card = screen.getByTestId('game-preview-card');
    expect(card).toHaveClass('responsive');
  });

  it('should handle click interactions', () => {
    const onClickMock = jest.fn();
    render(
      <GamePreview 
        session={mockSession} 
        previewData={mockPreviewData} 
        onClick={onClickMock}
      />
    );
    
    const card = screen.getByTestId('game-preview-card');
    card.click();
    
    expect(onClickMock).toHaveBeenCalledWith(mockSession);
  });

  it('should show betting availability indicator', () => {
    const sessionWithBetting = {
      ...mockSession,
      has_betting_markets: true,
    };
    
    render(<GamePreview session={sessionWithBetting} previewData={mockPreviewData} />);
    
    expect(screen.getByText(/betting available/i)).toBeInTheDocument();
  });

  it('should display time-based information correctly', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    // Should show estimated duration
    expect(screen.getByText(/30.*min/i)).toBeInTheDocument();
  });

  it('should indicate when slots are available', () => {
    const sessionWithSlots = {
      ...mockSession,
      current_participants: 3,
      max_participants: 8,
    };
    
    render(<GamePreview session={sessionWithSlots} previewData={mockPreviewData} />);
    
    expect(screen.getByText(/5.*slots.*available/i)).toBeInTheDocument();
  });

  it('should show game is full when at capacity', () => {
    const fullSession = {
      ...mockSession,
      current_participants: 8,
      max_participants: 8,
    };
    
    render(<GamePreview session={fullSession} previewData={mockPreviewData} />);
    
    expect(screen.getByText(/full/i)).toBeInTheDocument();
  });

  it('should display game category icon correctly', () => {
    render(<GamePreview session={mockSession} previewData={mockPreviewData} />);
    
    const categoryIcon = screen.getByTestId('category-icon-trivia');
    expect(categoryIcon).toBeInTheDocument();
  });

  it('should handle network errors gracefully', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Simulate error state
    render(
      <GamePreview 
        session={mockSession} 
        previewData={mockPreviewData}
        error="Network error"
      />
    );
    
    expect(screen.getByText(/unable to load/i)).toBeInTheDocument();
    
    consoleErrorSpy.mockRestore();
  });
});