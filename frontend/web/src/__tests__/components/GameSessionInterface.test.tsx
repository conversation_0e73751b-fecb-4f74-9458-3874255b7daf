/**
 * BetBet Gaming Engine - Game Session Interface Tests
 * ====================================================
 */

import { render, screen, fireEvent, waitFor } from '../utils/simple-test-utils';
import { createMockSession, createMockUser, createMockParticipant } from '../utils/simple-test-utils';

// Mock the game session interface component
const MockGameSessionInterface = ({ sessionId }: { sessionId: string }) => {
  const [session] = React.useState(createMockSession({ id: sessionId }));
  const [gameState] = React.useState({
    currentRound: 1,
    totalRounds: 5,
    timeRemaining: 30,
    question: 'What is 2 + 2?',
    options: ['3', '4', '5', '6'],
    scores: { 'user-1': 100, 'user-2': 80 }
  });

  return (
    <div data-testid="game-session-interface">
      <div data-testid="session-info">
        <h2>{session.session_name}</h2>
        <span>Status: {session.status}</span>
        <span>Players: {session.current_participants}/{session.max_participants}</span>
      </div>
      
      {session.status === 'active' && (
        <div data-testid="game-area">
          <div data-testid="game-progress">
            Round {gameState.currentRound} of {gameState.totalRounds}
          </div>
          <div data-testid="timer">
            {gameState.timeRemaining}s remaining
          </div>
          <div data-testid="question">
            {gameState.question}
          </div>
          <div data-testid="options">
            {gameState.options.map((option, index) => (
              <button key={index} data-testid={`option-${index}`}>
                {option}
              </button>
            ))}
          </div>
          <div data-testid="scores">
            {Object.entries(gameState.scores).map(([userId, score]) => (
              <div key={userId} data-testid={`score-${userId}`}>
                {userId}: {score}
              </div>
            ))}
          </div>
        </div>
      )}

      <div data-testid="session-controls">
        <button data-testid="join-button">Join Session</button>
        <button data-testid="leave-button">Leave Session</button>
        <button data-testid="ready-button">Ready</button>
      </div>

      <div data-testid="chat-area">
        <input data-testid="chat-input" placeholder="Type a message..." />
        <button data-testid="send-message">Send</button>
      </div>
    </div>
  );
};

// Mock React import
const React = { useState: jest.fn() };

describe('Game Session Interface', () => {
  beforeEach(() => {
    // Reset React.useState mock
    React.useState
      .mockReturnValueOnce([createMockSession(), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        scores: { 'user-1': 100, 'user-2': 80 }
      }, jest.fn()]);
  });

  it('renders session information correctly', () => {
    render(<MockGameSessionInterface sessionId="test-session" />);
    
    expect(screen.getByTestId('session-info')).toBeInTheDocument();
    expect(screen.getByText('Test Session')).toBeInTheDocument();
    expect(screen.getByText(/Status:/)).toBeInTheDocument();
    expect(screen.getByText(/Players:/)).toBeInTheDocument();
  });

  it('shows game area when session is active', () => {
    // Mock active session
    React.useState
      .mockReturnValueOnce([createMockSession({ status: 'active' }), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        scores: { 'user-1': 100, 'user-2': 80 }
      }, jest.fn()]);

    render(<MockGameSessionInterface sessionId="active-session" />);
    
    expect(screen.getByTestId('game-area')).toBeInTheDocument();
    expect(screen.getByTestId('game-progress')).toBeInTheDocument();
    expect(screen.getByTestId('timer')).toBeInTheDocument();
    expect(screen.getByTestId('question')).toBeInTheDocument();
    expect(screen.getByTestId('options')).toBeInTheDocument();
  });

  it('displays current game question and options', () => {
    React.useState
      .mockReturnValueOnce([createMockSession({ status: 'active' }), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        scores: { 'user-1': 100, 'user-2': 80 }
      }, jest.fn()]);

    render(<MockGameSessionInterface sessionId="active-session" />);
    
    expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
    expect(screen.getByTestId('option-0')).toHaveTextContent('3');
    expect(screen.getByTestId('option-1')).toHaveTextContent('4');
    expect(screen.getByTestId('option-2')).toHaveTextContent('5');
    expect(screen.getByTestId('option-3')).toHaveTextContent('6');
  });

  it('shows current scores for all players', () => {
    React.useState
      .mockReturnValueOnce([createMockSession({ status: 'active' }), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        scores: { 'user-1': 100, 'user-2': 80 }
      }, jest.fn()]);

    render(<MockGameSessionInterface sessionId="active-session" />);
    
    expect(screen.getByTestId('score-user-1')).toHaveTextContent('user-1: 100');
    expect(screen.getByTestId('score-user-2')).toHaveTextContent('user-2: 80');
  });

  it('displays game progress and timer', () => {
    React.useState
      .mockReturnValueOnce([createMockSession({ status: 'active' }), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 3,
        totalRounds: 5,
        timeRemaining: 15,
        question: 'Test question?',
        options: ['A', 'B', 'C', 'D'],
        scores: {}
      }, jest.fn()]);

    render(<MockGameSessionInterface sessionId="active-session" />);
    
    expect(screen.getByText('Round 3 of 5')).toBeInTheDocument();
    expect(screen.getByText('15s remaining')).toBeInTheDocument();
  });

  it('provides session control buttons', () => {
    render(<MockGameSessionInterface sessionId="test-session" />);
    
    expect(screen.getByTestId('join-button')).toBeInTheDocument();
    expect(screen.getByTestId('leave-button')).toBeInTheDocument();
    expect(screen.getByTestId('ready-button')).toBeInTheDocument();
  });

  it('includes chat functionality', () => {
    render(<MockGameSessionInterface sessionId="test-session" />);
    
    expect(screen.getByTestId('chat-area')).toBeInTheDocument();
    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
    expect(screen.getByTestId('send-message')).toBeInTheDocument();
  });

  it('handles user interactions', () => {
    render(<MockGameSessionInterface sessionId="test-session" />);
    
    const joinButton = screen.getByTestId('join-button');
    const chatInput = screen.getByTestId('chat-input');
    const sendButton = screen.getByTestId('send-message');
    
    // Test button clicks
    fireEvent.click(joinButton);
    expect(joinButton).toBeInTheDocument();
    
    // Test chat input
    fireEvent.change(chatInput, { target: { value: 'Hello everyone!' } });
    fireEvent.click(sendButton);
    
    expect(chatInput).toHaveValue('Hello everyone!');
  });

  it('handles option selection during gameplay', () => {
    React.useState
      .mockReturnValueOnce([createMockSession({ status: 'active' }), jest.fn()])
      .mockReturnValueOnce([{
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        scores: {}
      }, jest.fn()]);

    render(<MockGameSessionInterface sessionId="active-session" />);
    
    const correctOption = screen.getByTestId('option-1'); // '4' is correct
    fireEvent.click(correctOption);
    
    expect(correctOption).toBeInTheDocument();
  });
});