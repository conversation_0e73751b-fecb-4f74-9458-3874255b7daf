/**
 * BetBet Gaming Engine - Simple Home Page Tests
 * =============================================
 */

import { render, screen } from '../utils/simple-test-utils';
import HomePage from '@/app/page';

// Mock the API client
jest.mock('@/lib/api');
jest.mock('@/store/gameStore');

describe('Home Page', () => {
  it('renders the hero section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('BetBet Gaming Engine')).toBeInTheDocument();
    expect(screen.getByText(/real-time multiplayer gaming/i)).toBeInTheDocument();
  });

  it('shows get started buttons', () => {
    render(<HomePage />);
    
    expect(screen.getByRole('link', { name: /get started/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /browse games/i })).toBeInTheDocument();
  });

  it('displays stats overview cards', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Active Players')).toBeInTheDocument();
    expect(screen.getByText('Live Sessions')).toBeInTheDocument();
    expect(screen.getByText('Prize Pool')).toBeInTheDocument();
    expect(screen.getByText('Tournaments')).toBeInTheDocument();
  });

  it('shows featured games section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Featured Games')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view all/i })).toBeInTheDocument();
  });

  it('displays active sessions section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Active Sessions')).toBeInTheDocument();
  });

  it('displays quick actions section', () => {
    render(<HomePage />);
    
    expect(screen.getByText('Create Session')).toBeInTheDocument();
    expect(screen.getByText('Join Tournament')).toBeInTheDocument();
    expect(screen.getByText('Spectator Betting')).toBeInTheDocument();
  });
});