/**
 * BetBet Gaming Engine - Tournament Bracket Tests
 * ===============================================
 */

import { render, screen, fireEvent, waitFor } from '../utils/simple-test-utils';
import { createMockUser } from '../utils/simple-test-utils';

// Mock tournament bracket component
const MockTournamentBracket = ({ tournamentId }: { tournamentId: string }) => {
  const tournament = {
    id: tournamentId,
    name: 'Friday Night Championship',
    format: 'single_elimination',
    status: 'active',
    current_participants: 16,
    max_participants: 16,
    prize_pool: 1000,
    entry_fee: 25
  };

  const bracket = {
    rounds: [
      {
        round_number: 1,
        name: 'Round 1',
        matches: [
          {
            id: 'match-1',
            participant1: { id: 'p1', name: 'Player One', score: 10 },
            participant2: { id: 'p2', name: 'Player Two', score: 8 },
            status: 'completed',
            winner_id: 'p1'
          },
          {
            id: 'match-2', 
            participant1: { id: 'p3', name: 'Player Three', score: 0 },
            participant2: { id: 'p4', name: 'Player Four', score: 0 },
            status: 'in_progress',
            winner_id: null
          }
        ]
      },
      {
        round_number: 2,
        name: 'Semifinals',
        matches: [
          {
            id: 'match-3',
            participant1: { id: 'p1', name: 'Player One', score: 0 },
            participant2: null,
            status: 'waiting',
            winner_id: null
          }
        ]
      }
    ]
  };

  return (
    <div data-testid="tournament-bracket">
      <div data-testid="tournament-header">
        <h1>{tournament.name}</h1>
        <div data-testid="tournament-info">
          <span>Format: {tournament.format}</span>
          <span>Prize Pool: ${tournament.prize_pool}</span>
          <span>Participants: {tournament.current_participants}/{tournament.max_participants}</span>
          <span>Status: {tournament.status}</span>
        </div>
      </div>

      <div data-testid="bracket-container">
        {bracket.rounds.map((round) => (
          <div key={round.round_number} data-testid={`round-${round.round_number}`}>
            <h3>{round.name}</h3>
            {round.matches.map((match) => (
              <div key={match.id} data-testid={`match-${match.id}`} className="match-card">
                <div data-testid={`match-status-${match.id}`} className={`status-${match.status}`}>
                  {match.status === 'completed' && 'Completed'}
                  {match.status === 'in_progress' && 'Live'}
                  {match.status === 'waiting' && 'Waiting'}
                </div>
                
                <div className={`participant ${match.winner_id === match.participant1?.id ? 'winner' : ''}`}>
                  <span>{match.participant1?.name || 'TBD'}</span>
                  <span>{match.participant1?.score || 0}</span>
                </div>
                
                <div className={`participant ${match.winner_id === match.participant2?.id ? 'winner' : ''}`}>
                  <span>{match.participant2?.name || 'TBD'}</span>
                  <span>{match.participant2?.score || 0}</span>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <div data-testid="tournament-progress">
        <div>Round 1 of 2</div>
        <div>50% Complete</div>
      </div>

      <div data-testid="prize-distribution">
        <h3>Prize Distribution</h3>
        <div>1st Place: $500</div>
        <div>2nd Place: $300</div>
        <div>3rd Place: $200</div>
      </div>

      <div data-testid="tournament-actions">
        <button data-testid="refresh-bracket">Refresh</button>
        <button data-testid="view-rules">View Rules</button>
      </div>
    </div>
  );
};

describe('Tournament Bracket Component', () => {
  it('renders tournament information correctly', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByText('Friday Night Championship')).toBeInTheDocument();
    expect(screen.getByText('Format: single_elimination')).toBeInTheDocument();
    expect(screen.getByText('Prize Pool: $1000')).toBeInTheDocument();
    expect(screen.getByText('Participants: 16/16')).toBeInTheDocument();
    expect(screen.getByText('Status: active')).toBeInTheDocument();
  });

  it('displays all tournament rounds', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByTestId('round-1')).toBeInTheDocument();
    expect(screen.getByTestId('round-2')).toBeInTheDocument();
    expect(screen.getByText('Round 1')).toBeInTheDocument();
    expect(screen.getByText('Semifinals')).toBeInTheDocument();
  });

  it('shows match details with participants and scores', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    // Check completed match
    expect(screen.getByTestId('match-match-1')).toBeInTheDocument();
    expect(screen.getByText('Player One')).toBeInTheDocument();
    expect(screen.getByText('Player Two')).toBeInTheDocument();
    
    // Check scores are displayed
    const matchCard = screen.getByTestId('match-match-1');
    expect(matchCard).toHaveTextContent('10');
    expect(matchCard).toHaveTextContent('8');
  });

  it('indicates different match statuses correctly', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    // Completed match
    expect(screen.getByTestId('match-status-match-1')).toHaveTextContent('Completed');
    
    // In progress match
    expect(screen.getByTestId('match-status-match-2')).toHaveTextContent('Live');
    
    // Waiting match
    expect(screen.getByTestId('match-status-match-3')).toHaveTextContent('Waiting');
  });

  it('highlights match winners', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    const matchCard = screen.getByTestId('match-match-1');
    const winnerElement = matchCard.querySelector('.winner');
    
    expect(winnerElement).toBeInTheDocument();
    expect(winnerElement).toHaveTextContent('Player One');
  });

  it('shows TBD for unknown participants', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    const semifinalMatch = screen.getByTestId('match-match-3');
    expect(semifinalMatch).toHaveTextContent('TBD');
  });

  it('displays tournament progress information', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByText('Round 1 of 2')).toBeInTheDocument();
    expect(screen.getByText('50% Complete')).toBeInTheDocument();
  });

  it('shows prize distribution details', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByText('Prize Distribution')).toBeInTheDocument();
    expect(screen.getByText('1st Place: $500')).toBeInTheDocument();
    expect(screen.getByText('2nd Place: $300')).toBeInTheDocument();
    expect(screen.getByText('3rd Place: $200')).toBeInTheDocument();
  });

  it('provides interaction buttons', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByTestId('refresh-bracket')).toBeInTheDocument();
    expect(screen.getByTestId('view-rules')).toBeInTheDocument();
  });

  it('handles match card clicks for details', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    const matchCard = screen.getByTestId('match-match-1');
    fireEvent.click(matchCard);
    
    // Match card should be clickable
    expect(matchCard).toBeInTheDocument();
  });

  it('handles refresh bracket action', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    const refreshButton = screen.getByTestId('refresh-bracket');
    fireEvent.click(refreshButton);
    
    expect(refreshButton).toBeInTheDocument();
  });

  it('shows proper CSS classes for match status', () => {
    render(<MockTournamentBracket tournamentId="tournament-1" />);
    
    expect(screen.getByTestId('match-status-match-1')).toHaveClass('status-completed');
    expect(screen.getByTestId('match-status-match-2')).toHaveClass('status-in_progress');
    expect(screen.getByTestId('match-status-match-3')).toHaveClass('status-waiting');
  });

  it('displays empty bracket state appropriately', () => {
    // Test with minimal tournament data
    const EmptyBracket = () => (
      <div data-testid="tournament-bracket">
        <div data-testid="tournament-header">
          <h1>Empty Tournament</h1>
        </div>
        <div data-testid="empty-bracket">No matches scheduled</div>
      </div>
    );

    render(<EmptyBracket />);
    
    expect(screen.getByText('Empty Tournament')).toBeInTheDocument();
    expect(screen.getByText('No matches scheduled')).toBeInTheDocument();
  });
});