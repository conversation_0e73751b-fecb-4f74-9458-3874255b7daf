/**
 * Chess Game Creation Modal Tests
 * ================================
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ChessGameCreationModal } from '@/components/chess/ChessGameCreationModal';
import { useAuth, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation');
jest.mock('@/lib/multi-service-api', () => ({
  multiServiceAPI: {
    updateAuthContext: jest.fn(),
  }
}));

const mockGetToken = jest.fn();
const mockPush = jest.fn();
const mockUser = { id: 'user-123', username: 'testuser' };

describe('ChessGameCreationModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({ getToken: mockGetToken });
    (useUser as jest.Mock).mockReturnValue({ user: mockUser });
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    mockGetToken.mockResolvedValue('test-token');
  });

  it('renders when open', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    expect(screen.getByText('Create Chess Game')).toBeInTheDocument();
    expect(screen.getByText('Step 1 of 4: Game Settings')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <ChessGameCreationModal
        isOpen={false}
        onClose={jest.fn()}
      />
    );

    expect(screen.queryByText('Create Chess Game')).not.toBeInTheDocument();
  });

  it('calls onClose when X button is clicked', () => {
    const onClose = jest.fn();
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={onClose}
      />
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    expect(onClose).toHaveBeenCalled();
  });

  it('navigates through wizard steps', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    // Step 1
    expect(screen.getByText('Game Settings')).toBeInTheDocument();
    
    // Go to Step 2
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);
    expect(screen.getByText('Player Settings')).toBeInTheDocument();
    
    // Go to Step 3
    fireEvent.click(nextButton);
    expect(screen.getByText('Game Features')).toBeInTheDocument();
    
    // Go to Step 4
    fireEvent.click(nextButton);
    expect(screen.getByText('Session Rules')).toBeInTheDocument();
    
    // Final step should show Create Game button
    expect(screen.getByText('Create Game')).toBeInTheDocument();
  });

  it('allows going back to previous steps', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    // Go to step 2
    fireEvent.click(screen.getByText('Next'));
    
    // Go back to step 1
    const prevButton = screen.getByText('Previous');
    fireEvent.click(prevButton);
    
    expect(screen.getByText('Game Settings')).toBeInTheDocument();
  });

  it('auto-generates session name based on settings', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    const sessionNameInput = screen.getByPlaceholderText('e.g., Blitz Chess - $10');
    expect(sessionNameInput).toHaveValue('Blitz (5+3) Chess - $10');
  });

  it('updates time controls when preset is selected', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    // Click on Rapid preset
    const rapidOption = screen.getByText('Rapid (10+10)');
    fireEvent.click(rapidOption.closest('div[role="button"]') || rapidOption);

    // Session name should update
    const sessionNameInput = screen.getByPlaceholderText('e.g., Blitz Chess - $10');
    expect(sessionNameInput).toHaveValue('Rapid (10+10) Chess - $10');
  });

  it('validates required fields', () => {
    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    // Clear session name
    const sessionNameInput = screen.getByPlaceholderText('e.g., Blitz Chess - $10');
    fireEvent.change(sessionNameInput, { target: { value: '' } });

    // Try to go to next step
    fireEvent.click(screen.getByText('Next'));

    // Should show validation error
    expect(screen.getByText('Session name must be at least 3 characters')).toBeInTheDocument();
  });

  it('creates session successfully', async () => {
    const onSuccess = jest.fn();
    global.fetch = jest.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => ({ id: 'session-123' })
    });

    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
        onSuccess={onSuccess}
      />
    );

    // Navigate to last step
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton); // Step 2
    fireEvent.click(nextButton); // Step 3
    fireEvent.click(nextButton); // Step 4

    // Create game
    const createButton = screen.getByText('Create Game');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:8001/api/v1/gaming/sessions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token'
          })
        })
      );
    });

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith('session-123');
    });
  });

  it('handles creation errors', async () => {
    global.fetch = jest.fn().mockResolvedValueOnce({
      ok: false,
      json: async () => ({ detail: 'Test error message' })
    });

    render(
      <ChessGameCreationModal
        isOpen={true}
        onClose={jest.fn()}
      />
    );

    // Navigate to last step
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton); // Step 2
    fireEvent.click(nextButton); // Step 3
    fireEvent.click(nextButton); // Step 4

    // Create game
    const createButton = screen.getByText('Create Game');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });
});