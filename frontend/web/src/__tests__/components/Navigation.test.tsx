/**
 * BetBet Gaming Engine - Navigation Component Tests
 * ================================================
 */

import { render, screen, fireEvent } from '../utils/test-utils';
import Navigation from '@/components/layout/Navigation';
import { mockStore, updateMockStore, resetMockStore, createMockUser } from '../utils/test-utils';

describe('Navigation Component', () => {
  beforeEach(() => {
    resetMockStore();
  });

  it('renders the BetBet brand logo', () => {
    render(<Navigation />);
    
    expect(screen.getByText('BetBet')).toBeInTheDocument();
    expect(screen.getByText('Gaming Engine')).toBeInTheDocument();
  });

  it('shows navigation links', () => {
    render(<Navigation />);
    
    expect(screen.getByRole('link', { name: /games/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sessions/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /tournaments/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /leaderboard/i })).toBeInTheDocument();
  });

  it('shows search bar on desktop', () => {
    render(<Navigation />);
    
    const searchInput = screen.getByPlaceholderText(/search games, players/i);
    expect(searchInput).toBeInTheDocument();
  });

  it('shows sign in/up buttons when not authenticated', () => {
    render(<Navigation />);
    
    expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();
  });

  it('shows user menu when authenticated', () => {
    const mockUser = createMockUser();
    updateMockStore({
      isAuthenticated: true,
      user: mockUser,
    });

    render(<Navigation />);
    
    expect(screen.getByText(mockUser.username)).toBeInTheDocument();
    expect(screen.getByText(mockUser.email)).toBeInTheDocument();
    expect(screen.queryByText('Sign In')).not.toBeInTheDocument();
  });

  it('shows create session button when authenticated', () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<Navigation />);
    
    expect(screen.getByRole('button', { name: /create session/i })).toBeInTheDocument();
  });

  it('shows connection status when in a session', () => {
    updateMockStore({
      currentSession: { id: 'test-session' },
      isConnected: true,
    });

    render(<Navigation />);
    
    expect(screen.getByText(/connected/i)).toBeInTheDocument();
  });

  it('shows disconnected status when connection is lost', () => {
    updateMockStore({
      currentSession: { id: 'test-session' },
      isConnected: false,
    });

    render(<Navigation />);
    
    expect(screen.getByText(/disconnected/i)).toBeInTheDocument();
  });

  it('calls mobile menu handler when mobile menu button is clicked', () => {
    const onMobileMenuClick = jest.fn();
    render(<Navigation onMobileMenuClick={onMobileMenuClick} />);
    
    const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(mobileMenuButton);
    
    expect(onMobileMenuClick).toHaveBeenCalled();
  });

  it('calls logout when logout is clicked', () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<Navigation />);
    
    // Click on user menu to open dropdown
    const userButton = screen.getByRole('button');
    fireEvent.click(userButton);
    
    // Click logout
    const logoutButton = screen.getByRole('menuitem', { name: /log out/i });
    fireEvent.click(logoutButton);
    
    expect(mockStore.logout).toHaveBeenCalled();
  });

  it('shows notification badge', () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<Navigation />);
    
    const notificationBadge = screen.getByText('3');
    expect(notificationBadge).toBeInTheDocument();
  });
});