/**
 * ApiDebugPanel Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ApiDebugPanel, emitApiError } from '@/components/dev/ApiDebugPanel';

// Mock environment variable
const originalEnv = process.env.NODE_ENV;

describe('ApiDebugPanel', () => {
  beforeEach(() => {
    // Clear any previous errors
    window.dispatchEvent(new CustomEvent('api-error', { detail: { endpoint: 'clear', method: 'CLEAR', error: {} } }));
  });

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  it('should not render in production environment', () => {
    process.env.NODE_ENV = 'production';
    const { container } = render(<ApiDebugPanel />);
    expect(container.firstChild).toBeNull();
  });

  it('should render toggle button in development environment', () => {
    process.env.NODE_ENV = 'development';
    render(<ApiDebugPanel />);
    const toggleButton = screen.getByText('API Debug');
    expect(toggleButton).toBeInTheDocument();
  });

  it('should toggle panel visibility when button is clicked', () => {
    process.env.NODE_ENV = 'development';
    render(<ApiDebugPanel />);
    
    const toggleButton = screen.getByText('API Debug');
    
    // Panel should not be visible initially
    expect(screen.queryByText('API Debug Panel')).not.toBeInTheDocument();
    
    // Click to show panel
    fireEvent.click(toggleButton);
    expect(screen.getByText('API Debug Panel')).toBeInTheDocument();
    
    // Click to hide panel
    fireEvent.click(toggleButton);
    expect(screen.queryByText('API Debug Panel')).not.toBeInTheDocument();
  });

  it('should display API errors when emitted', async () => {
    process.env.NODE_ENV = 'development';
    render(<ApiDebugPanel />);
    
    // Open the panel
    fireEvent.click(screen.getByText('API Debug'));
    
    // Emit an API error
    emitApiError('/api/users', 'GET', new Error('Network error'), { status: 500 });
    
    await waitFor(() => {
      expect(screen.getByText('GET /api/users')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });
  });

  it('should show error count badge when errors exist', async () => {
    process.env.NODE_ENV = 'development';
    render(<ApiDebugPanel />);
    
    // Emit multiple API errors
    emitApiError('/api/users', 'GET', new Error('Error 1'));
    emitApiError('/api/posts', 'POST', new Error('Error 2'));
    
    await waitFor(() => {
      const badge = screen.getByText('2');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('ml-2');
    });
  });

  it('should clear errors when clear button is clicked', async () => {
    process.env.NODE_ENV = 'development';
    render(<ApiDebugPanel />);
    
    // Open panel and emit error
    fireEvent.click(screen.getByText('API Debug'));
    emitApiError('/api/test', 'GET', new Error('Test error'));
    
    await waitFor(() => {
      expect(screen.getByText('GET /api/test')).toBeInTheDocument();
    });
    
    // Click clear button (trash icon button)
    const clearButton = screen.getAllByRole('button').find(btn => 
      btn.querySelector('[class*="h-3 w-3"]')
    );
    if (clearButton) fireEvent.click(clearButton);
    
    await waitFor(() => {
      expect(screen.getByText('No API errors detected')).toBeInTheDocument();
    });
  });

  it('should check API status when refresh is clicked', async () => {
    process.env.NODE_ENV = 'development';
    
    // Mock fetch for health check
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      } as Response)
    );
    
    render(<ApiDebugPanel />);
    fireEvent.click(screen.getByText('API Debug'));
    
    // Find and click refresh button
    const refreshButton = screen.getAllByRole('button').find(btn => 
      btn.querySelector('[class*="h-3 w-3"]')
    );
    if (refreshButton) fireEvent.click(refreshButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/health')
      );
    });
  });

  it('should display offline status when API is unreachable', async () => {
    process.env.NODE_ENV = 'development';
    
    // Mock fetch to simulate offline
    global.fetch = jest.fn(() => Promise.reject(new Error('Network error')));
    
    render(<ApiDebugPanel />);
    fireEvent.click(screen.getByText('API Debug'));
    
    await waitFor(() => {
      expect(screen.getByText('offline')).toBeInTheDocument();
    });
  });

  it('should not emit errors in production environment', () => {
    process.env.NODE_ENV = 'production';
    
    const mockDispatchEvent = jest.spyOn(window, 'dispatchEvent');
    
    emitApiError('/api/test', 'GET', new Error('Test error'));
    
    expect(mockDispatchEvent).not.toHaveBeenCalled();
    
    mockDispatchEvent.mockRestore();
  });
});