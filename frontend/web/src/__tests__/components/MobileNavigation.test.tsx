/**
 * BetBet Gaming Engine - Mobile Navigation Tests
 * ==============================================
 */

import { render, screen, fireEvent } from '../utils/test-utils';
import MobileNavigation from '@/components/layout/MobileNavigation';
import { mockStore, updateMockStore, resetMockStore, createMockUser } from '../utils/test-utils';

describe('Mobile Navigation Component', () => {
  beforeEach(() => {
    resetMockStore();
  });

  it('renders all navigation items', () => {
    render(<MobileNavigation />);
    
    expect(screen.getByLabelText('Home')).toBeInTheDocument();
    expect(screen.getByLabelText('Games')).toBeInTheDocument();
    expect(screen.getByLabelText('Sessions')).toBeInTheDocument();
    expect(screen.getByLabelText('Tournaments')).toBeInTheDocument();
    expect(screen.getByLabelText('Leaderboard')).toBeInTheDocument();
    expect(screen.getByLabelText('Profile')).toBeInTheDocument();
  });

  it('highlights active route', () => {
    // Mock current pathname
    const mockPathname = '/games';
    jest.doMock('next/navigation', () => ({
      usePathname: () => mockPathname,
    }));

    render(<MobileNavigation />);
    
    const gamesNavItem = screen.getByLabelText('Games');
    expect(gamesNavItem.closest('a')).toHaveClass('active');
  });

  it('shows profile icon when authenticated', () => {
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });

    render(<MobileNavigation />);
    
    expect(screen.getByLabelText('Profile')).toBeInTheDocument();
  });

  it('shows sign in icon when not authenticated', () => {
    updateMockStore({
      isAuthenticated: false,
      user: null,
    });

    render(<MobileNavigation />);
    
    expect(screen.getByLabelText('Profile')).toBeInTheDocument();
    // Should link to sign in page
    const profileLink = screen.getByLabelText('Profile').closest('a');
    expect(profileLink).toHaveAttribute('href', '/auth/signin');
  });

  it('shows notification badge on games tab when in session', () => {
    updateMockStore({
      currentSession: { id: 'test-session' },
    });

    render(<MobileNavigation />);
    
    const gamesNavItem = screen.getByLabelText('Games');
    const badge = gamesNavItem.parentElement?.querySelector('.notification-badge');
    expect(badge).toBeInTheDocument();
  });

  it('handles navigation clicks', () => {
    render(<MobileNavigation />);
    
    const homeLink = screen.getByLabelText('Home').closest('a');
    const gamesLink = screen.getByLabelText('Games').closest('a');
    
    expect(homeLink).toHaveAttribute('href', '/');
    expect(gamesLink).toHaveAttribute('href', '/games');
  });

  it('is hidden on desktop screens', () => {
    render(<MobileNavigation />);
    
    const mobileNav = document.querySelector('.mobile-nav');
    expect(mobileNav).toHaveClass('sm:hidden');
  });

  it('has proper touch targets for mobile', () => {
    render(<MobileNavigation />);
    
    const navItems = screen.getAllByRole('link');
    
    navItems.forEach(item => {
      expect(item).toHaveClass('touch-target');
    });
  });

  it('shows correct icons for each section', () => {
    render(<MobileNavigation />);
    
    // Each nav item should have an icon
    const navItems = [
      { label: 'Home', icon: 'Home' },
      { label: 'Games', icon: 'Gamepad' },
      { label: 'Sessions', icon: 'Users' },
      { label: 'Tournaments', icon: 'Trophy' },
      { label: 'Leaderboard', icon: 'BarChart' },
      { label: 'Profile', icon: 'User' },
    ];

    navItems.forEach(({ label }) => {
      const navItem = screen.getByLabelText(label);
      const icon = navItem.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });
  });

  it('maintains accessibility standards', () => {
    render(<MobileNavigation />);
    
    const navItems = screen.getAllByRole('link');
    
    navItems.forEach(item => {
      // Should have aria-label
      expect(item).toHaveAttribute('aria-label');
      
      // Should be keyboard accessible
      expect(item).toHaveAttribute('href');
    });
  });

  it('adapts to user state changes', () => {
    const { rerender } = render(<MobileNavigation />);
    
    // Initially not authenticated
    expect(screen.getByLabelText('Profile').closest('a')).toHaveAttribute('href', '/auth/signin');
    
    // Update to authenticated
    updateMockStore({
      isAuthenticated: true,
      user: createMockUser(),
    });
    
    rerender(<MobileNavigation />);
    
    expect(screen.getByLabelText('Profile').closest('a')).toHaveAttribute('href', '/profile');
  });
});