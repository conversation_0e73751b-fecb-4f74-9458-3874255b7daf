/**
 * Expert Analyst Accessibility Tests
 * ===================================
 * 
 * Comprehensive accessibility tests for WCAG 2.1 AA compliance.
 * Tests keyboard navigation, screen reader compatibility, color contrast,
 * focus management, and semantic HTML structure.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { useAuth } from '@clerk/nextjs';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import { mockExperts, mockPicks, mockSubscriptions } from '../expert-analyst/mocks/expert-analyst-data';

// Components under test
import ExpertsMarketplace from '@/app/experts/page';
import ExpertDetailPage from '@/app/experts/[id]/page';
import PicksFeedPage from '@/app/picks/page';
import ExpertPortalPage from '@/app/expert-portal/page';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

describe('Expert Analyst Accessibility Tests', () => {
  const mockGetToken = jest.fn().mockResolvedValue('test-token');
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
      userId: 'test-user-id',
      isSignedIn: true
    });
    
    // Default API mocks
    (expertAnalystApi.getExperts as jest.Mock).mockResolvedValue({
      experts: Object.values(mockExperts),
      total_count: Object.values(mockExperts).length
    });
    
    (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
    (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
      subscriptions: mockSubscriptions,
      total_count: mockSubscriptions.length
    });
    (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
      picks: mockPicks,
      total_count: mockPicks.length
    });
  });

  describe('Experts Marketplace Accessibility', () => {
    it('should have no axe violations', async () => {
      const { container } = render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Analysts Marketplace')).toBeInTheDocument();
      });
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper heading hierarchy', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Main page heading
        const h1 = screen.getByRole('heading', { level: 1 });
        expect(h1).toHaveTextContent('Expert Analysts Marketplace');
        
        // Section headings should be h2
        const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
        expect(sectionHeadings.length).toBeGreaterThan(0);
        
        // Expert card headings should be h3
        const cardHeadings = screen.getAllByRole('heading', { level: 3 });
        expect(cardHeadings.length).toBeGreaterThan(0);
      });
    });

    it('should provide proper labels for form controls', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Search input should have accessible label
        const searchInput = screen.getByRole('textbox', { name: /search experts/i });
        expect(searchInput).toBeInTheDocument();
        
        // Select dropdowns should have labels
        const categorySelect = screen.getByRole('combobox', { name: /category/i });
        expect(categorySelect).toBeInTheDocument();
        
        const sortSelect = screen.getByRole('combobox', { name: /sort/i });
        expect(sortSelect).toBeInTheDocument();
      });
    });

    it('should be fully keyboard navigable', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Analysts Marketplace')).toBeInTheDocument();
      });
      
      // Tab through interactive elements
      await user.keyboard('{Tab}'); // Search input
      expect(screen.getByRole('textbox')).toHaveFocus();
      
      await user.keyboard('{Tab}'); // Category select
      expect(screen.getAllByRole('combobox')[0]).toHaveFocus();
      
      await user.keyboard('{Tab}'); // Sort select
      expect(screen.getAllByRole('combobox')[1]).toHaveFocus();
      
      // Navigate to expert cards
      await user.keyboard('{Tab}');
      const expertLinks = screen.getAllByRole('link');
      expect(expertLinks[0]).toHaveFocus();
      
      // Should be able to activate with Enter
      await user.keyboard('{Enter}');
      // Navigation would occur in real app
    });

    it('should announce loading states to screen readers', async () => {
      const { rerender } = render(<ExpertsMarketplace />);
      
      // Initially should show loading
      expect(screen.getByRole('status')).toHaveAttribute('aria-live', 'polite');
      expect(screen.getByRole('status')).toHaveTextContent(/loading/i);
      
      // After loading completes
      await waitFor(() => {
        expect(screen.queryByRole('status')).not.toBeInTheDocument();
      });
    });

    it('should provide meaningful alternative text for images', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // Expert avatars should have descriptive alt text
        const avatars = screen.getAllByRole('img');
        avatars.forEach(avatar => {
          expect(avatar).toHaveAttribute('alt');
          expect(avatar.getAttribute('alt')).not.toBe('');
          expect(avatar.getAttribute('alt')).toMatch(/expert|profile|avatar/i);
        });
      });
    });

    it('should have proper color contrast for all text', async () => {
      const { container } = render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        // This would be enhanced with actual color contrast testing
        // For now, verify that text elements have appropriate classes
        const textElements = container.querySelectorAll('[class*="text-"]');
        textElements.forEach(element => {
          // Verify no extremely light text on light backgrounds
          expect(element.className).not.toMatch(/text-gray-100|text-white/);
        });
      });
    });

    it('should provide skip navigation links', async () => {
      render(<ExpertsMarketplace />);
      
      // Skip link should be present (even if visually hidden)
      const skipLink = screen.getByRole('link', { name: /skip to main content/i });
      expect(skipLink).toBeInTheDocument();
      expect(skipLink).toHaveAttribute('href', '#main-content');
    });
  });

  describe('Expert Detail Page Accessibility', () => {
    it('should have no axe violations', async () => {
      const { container } = render(<ExpertDetailPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Michael Johnson')).toBeInTheDocument();
      });
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should properly structure subscription tier information', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Each subscription tier should be in a proper region
        const tierSections = screen.getAllByRole('region');
        expect(tierSections.length).toBeGreaterThanOrEqual(3);
        
        tierSections.forEach(section => {
          expect(section).toHaveAttribute('aria-labelledby');
        });
      });
    });

    it('should make subscription buttons accessible', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const subscribeButtons = screen.getAllByRole('button', { name: /subscribe/i });
        
        subscribeButtons.forEach((button, index) => {
          // Should have descriptive text
          expect(button).toHaveAccessibleName();
          
          // Should indicate which tier it's for
          expect(button).toHaveAttribute('aria-describedby');
          
          // Should have proper button role
          expect(button).toHaveAttribute('type', 'button');
        });
      });
    });

    it('should provide accessible performance metrics', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Performance metrics should have proper labels
        const winRateElement = screen.getByText(/win rate/i).closest('[role="group"]');
        expect(winRateElement).toHaveAttribute('aria-labelledby');
        
        const roiElement = screen.getByText(/roi/i).closest('[role="group"]');
        expect(roiElement).toHaveAttribute('aria-labelledby');
        
        // Metrics should be announced properly
        const metrics = screen.getAllByRole('text');
        metrics.forEach(metric => {
          if (metric.textContent?.includes('%')) {
            expect(metric).toHaveAttribute('aria-label');
          }
        });
      });
    });

    it('should handle modal dialogs accessibly', async () => {
      const user = userEvent.setup();
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const subscribeButton = screen.getAllByText('Subscribe')[0];
        expect(subscribeButton).toBeInTheDocument();
      });
      
      // Open subscription modal
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(subscribeButton);
      
      await waitFor(() => {
        const modal = screen.getByRole('dialog');
        expect(modal).toBeInTheDocument();
        expect(modal).toHaveAttribute('aria-modal', 'true');
        expect(modal).toHaveAttribute('aria-labelledby');
        
        // Focus should move to modal
        const firstFocusableElement = modal.querySelector('input, button, [tabindex]');
        expect(firstFocusableElement).toHaveFocus();
        
        // Should be able to close with Escape
        fireEvent.keyDown(modal, { key: 'Escape' });
      });
    });

    it('should provide accessible reviews section', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const reviewsSection = screen.getByRole('region', { name: /reviews/i });
        expect(reviewsSection).toBeInTheDocument();
        
        // Each review should be properly structured
        const reviews = screen.getAllByRole('article');
        reviews.forEach(review => {
          expect(review).toHaveAttribute('aria-labelledby');
          
          // Star ratings should be accessible
          const stars = review.querySelectorAll('[role="img"]');
          stars.forEach(star => {
            expect(star).toHaveAttribute('aria-label');
          });
        });
      });
    });
  });

  describe('Picks Feed Accessibility', () => {
    it('should have no axe violations', async () => {
      const { container } = render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Picks Feed')).toBeInTheDocument();
      });
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should make pick cards accessible', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        const pickCards = screen.getAllByRole('article');
        
        pickCards.forEach(card => {
          // Each pick should have a heading
          const heading = card.querySelector('h3, h4, [role="heading"]');
          expect(heading).toBeInTheDocument();
          
          // Should have descriptive content
          expect(card).toHaveTextContent(/expert|pick|confidence/i);
          
          // Should be focusable if interactive
          if (card.onclick || card.role === 'button') {
            expect(card).toHaveAttribute('tabindex', '0');
          }
        });
      });
    });

    it('should provide accessible filtering controls', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Filter controls should be grouped
        const filtersGroup = screen.getByRole('group', { name: /filters/i });
        expect(filtersGroup).toBeInTheDocument();
        
        // Each filter should have proper labels
        const expertFilter = screen.getByLabelText(/filter by expert/i);
        expect(expertFilter).toBeInTheDocument();
        
        const categoryFilter = screen.getByLabelText(/filter by category/i);
        expect(categoryFilter).toBeInTheDocument();
        
        const statusFilter = screen.getByLabelText(/filter by status/i);
        expect(statusFilter).toBeInTheDocument();
      });
    });

    it('should announce pick updates to screen readers', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Live region for pick updates
        const liveRegion = screen.getByRole('status');
        expect(liveRegion).toHaveAttribute('aria-live', 'polite');
        
        // Would announce new picks or status changes
        expect(liveRegion).toHaveAttribute('aria-atomic', 'false');
      });
    });

    it('should handle real-time updates accessibly', async () => {
      const { rerender } = render(<PicksFeedPage />);
      
      // Simulate new pick arriving
      const updatedPicks = [...mockPicks, {
        ...mockPicks[0],
        id: 'new-pick',
        title: 'New Pick Added',
        created_at: new Date().toISOString()
      }];
      
      (expertAnalystApi.getPicks as jest.Mock).mockResolvedValue({
        picks: updatedPicks,
        total_count: updatedPicks.length
      });
      
      rerender(<PicksFeedPage />);
      
      await waitFor(() => {
        // New pick should be announced
        const liveRegion = screen.getByRole('status');
        expect(liveRegion).toHaveTextContent(/new pick/i);
      });
    });
  });

  describe('Expert Portal Accessibility', () => {
    beforeEach(() => {
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 125000,
        monthly_revenue: 8500,
        subscriber_growth: 15.5,
        pick_performance: {
          total_picks: 1250,
          successful_picks: 850,
          win_rate: 0.68,
          roi: 0.15
        },
        top_categories: []
      });
    });

    it('should have no axe violations', async () => {
      const { container } = render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Dashboard')).toBeInTheDocument();
      });
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should make analytics dashboard accessible', async () => {
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        // Analytics sections should have proper headings
        const analyticsSection = screen.getByRole('region', { name: /analytics|dashboard/i });
        expect(analyticsSection).toBeInTheDocument();
        
        // Charts should have text alternatives
        const charts = screen.getAllByRole('img', { name: /chart|graph/i });
        charts.forEach(chart => {
          expect(chart).toHaveAccessibleName();
          expect(chart).toHaveAccessibleDescription();
        });
        
        // Data tables should be properly structured
        const tables = screen.getAllByRole('table');
        tables.forEach(table => {
          expect(table).toHaveAttribute('aria-label');
          
          const headers = table.querySelectorAll('th');
          headers.forEach(header => {
            expect(header).toHaveAttribute('scope');
          });
        });
      });
    });

    it('should make pick creation form accessible', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        const createButton = screen.getByText('Create New Pick');
        expect(createButton).toBeInTheDocument();
      });
      
      const createButton = screen.getByText('Create New Pick');
      await user.click(createButton);
      
      await waitFor(() => {
        const form = screen.getByRole('form', { name: /create pick/i });
        expect(form).toBeInTheDocument();
        
        // All form fields should have labels
        const titleInput = screen.getByLabelText(/title/i);
        expect(titleInput).toBeRequired();
        
        const descriptionInput = screen.getByLabelText(/description/i);
        expect(descriptionInput).toBeRequired();
        
        const categorySelect = screen.getByLabelText(/category/i);
        expect(categorySelect).toBeRequired();
        
        // Error messages should be associated with fields
        const requiredFields = screen.getAllByAttribute('required');
        requiredFields.forEach(field => {
          const errorId = field.getAttribute('aria-describedby');
          if (errorId) {
            expect(document.getElementById(errorId)).toBeInTheDocument();
          }
        });
      });
    });

    it('should handle form validation accessibly', async () => {
      const user = userEvent.setup();
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      // Try to submit empty form
      const submitButton = screen.getByRole('button', { name: /publish|create/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        // Error messages should be announced
        const errorAlert = screen.getByRole('alert');
        expect(errorAlert).toBeInTheDocument();
        expect(errorAlert).toHaveTextContent(/required|error/i);
        
        // Focus should move to first error field
        const firstErrorField = screen.getByLabelText(/title/i);
        expect(firstErrorField).toHaveFocus();
        expect(firstErrorField).toHaveAttribute('aria-invalid', 'true');
      });
    });
  });

  describe('Keyboard Navigation', () => {
    it('should support tab navigation through all interactive elements', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByText('Expert Analysts Marketplace')).toBeInTheDocument();
      });
      
      // Collect all tabbable elements
      const tabbableElements = screen.getAllByRole(/button|link|textbox|combobox|tab/);
      
      // Tab through each element
      for (const element of tabbableElements) {
        await user.keyboard('{Tab}');
        expect(element).toHaveFocus();
      }
    });

    it('should support arrow key navigation in lists', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        const categoryTabs = screen.getByRole('tablist');
        const firstTab = screen.getAllByRole('tab')[0];
        
        // Focus first tab
        firstTab.focus();
        expect(firstTab).toHaveFocus();
        
        // Arrow right to next tab
        await user.keyboard('{ArrowRight}');
        const secondTab = screen.getAllByRole('tab')[1];
        expect(secondTab).toHaveFocus();
        
        // Arrow left back to first tab
        await user.keyboard('{ArrowLeft}');
        expect(firstTab).toHaveFocus();
      });
    });

    it('should support Space and Enter for activation', async () => {
      const user = userEvent.setup();
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        const subscribeButton = screen.getAllByText('Subscribe')[0];
        subscribeButton.focus();
        expect(subscribeButton).toHaveFocus();
      });
      
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      
      // Should activate with Space
      await user.keyboard(' ');
      // Modal should open (would be tested in integration)
      
      // Should also activate with Enter
      await user.keyboard('{Enter}');
    });
  });

  describe('Screen Reader Compatibility', () => {
    it('should provide proper ARIA landmarks', async () => {
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByRole('banner')).toBeInTheDocument(); // Header
        expect(screen.getByRole('main')).toBeInTheDocument(); // Main content
        expect(screen.getByRole('navigation')).toBeInTheDocument(); // Nav
        expect(screen.getByRole('contentinfo')).toBeInTheDocument(); // Footer
        
        // Sections should have proper landmarks
        const regions = screen.getAllByRole('region');
        regions.forEach(region => {
          expect(region).toHaveAttribute('aria-labelledby');
        });
      });
    });

    it('should announce dynamic content changes', async () => {
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Live regions should be present
        const liveRegions = screen.getAllByAttribute('aria-live');
        expect(liveRegions.length).toBeGreaterThan(0);
        
        liveRegions.forEach(region => {
          expect(region).toHaveAttribute('aria-live', /polite|assertive/);
        });
      });
    });

    it('should provide context for interactive elements', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Buttons should have context
        const buttons = screen.getAllByRole('button');
        buttons.forEach(button => {
          // Should have accessible name
          expect(button).toHaveAccessibleName();
          
          // Complex buttons should have descriptions
          if (button.textContent?.includes('Subscribe')) {
            expect(button).toHaveAccessibleDescription();
          }
        });
        
        // Links should indicate their purpose
        const links = screen.getAllByRole('link');
        links.forEach(link => {
          expect(link).toHaveAccessibleName();
          
          // External links should be indicated
          if (link.getAttribute('target') === '_blank') {
            expect(link).toHaveAttribute('aria-label', expect.stringMatching(/opens in new/i));
          }
        });
      });
    });
  });

  describe('Focus Management', () => {
    it('should manage focus properly in modal dialogs', async () => {
      const user = userEvent.setup();
      render(<ExpertDetailPage />);
      
      const subscribeButton = await screen.findByText('Subscribe');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        const modal = screen.getByRole('dialog');
        
        // Focus should be trapped in modal
        const focusableElements = modal.querySelectorAll(
          'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        expect(focusableElements.length).toBeGreaterThan(0);
        
        // First focusable element should have focus
        expect(focusableElements[0]).toHaveFocus();
      });
    });

    it('should provide visible focus indicators', async () => {
      const { container } = render(<ExpertsMarketplace />);
      
      // Check that focusable elements have focus styles
      const focusableElements = container.querySelectorAll(
        'button, input, select, a, [tabindex]:not([tabindex="-1"])'
      );
      
      focusableElements.forEach(element => {
        // Should have focus styles (verified through CSS classes)
        const classList = Array.from(element.classList);
        const hasFocusStyles = classList.some(cls => 
          cls.includes('focus') || cls.includes('ring')
        );
        
        if (!hasFocusStyles) {
          // Check if parent has focus styles
          const parent = element.closest('[class*="focus"], [class*="ring"]');
          expect(parent).not.toBeNull();
        }
      });
    });

    it('should restore focus after modal closes', async () => {
      const user = userEvent.setup();
      render(<ExpertDetailPage />);
      
      const subscribeButton = screen.getAllByText('Subscribe')[0];
      await user.click(subscribeButton);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
      
      // Close modal with Escape
      await user.keyboard('{Escape}');
      
      await waitFor(() => {
        // Focus should return to the button that opened the modal
        expect(subscribeButton).toHaveFocus();
      });
    });
  });
});