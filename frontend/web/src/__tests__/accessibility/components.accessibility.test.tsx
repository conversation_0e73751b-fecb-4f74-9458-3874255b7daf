/**
 * Custom Betting Platform - Component Accessibility Tests
 * ======================================================
 * 
 * Comprehensive accessibility tests for Custom Betting Platform components.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { runAccessibilityTestSuite, KeyboardNavigationTester, ScreenReaderTester } from './accessibility-utils';
import { formAccessibilityConfig, interactiveAccessibilityConfig } from './axe-config';

// Import components to test
import { BettingInterface } from '@/components/custom-betting/BettingInterface';
import { MarketBrowser } from '@/components/custom-betting/MarketBrowser';
import { CreateBetWizard } from '@/components/custom-betting/CreateBetWizard';
import { MarketHeader } from '@/components/custom-betting/MarketHeader';
import { MarketOutcomes } from '@/components/custom-betting/MarketOutcomes';
import { SettlementInterface } from '@/components/custom-betting/SettlementInterface';
import { UserBetManager } from '@/components/custom-betting/UserBetManager';

import { mockBinaryMarket, mockMultiChoiceMarket, mockUserParticipations } from '../mocks/custom-betting-data';

describe('Component Accessibility Tests', () => {
  describe('BettingInterface Accessibility', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      userParticipation: null,
      onBetPlaced: jest.fn()
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<BettingInterface {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component, {
        axeConfig: interactiveAccessibilityConfig
      });
      
      expect(results.overallSuccess).toBe(true);
      
      if (!results.overallSuccess) {
        console.log(results.summary);
        console.log('Axe violations:', results.axeResults?.violations);
        console.log('Keyboard issues:', results.keyboardResults?.tabOrder.issues);
      }
    });

    it('has proper form labels and descriptions', async () => {
      render(<BettingInterface {...defaultProps} />);
      
      // Check for proper form labeling
      expect(screen.getByLabelText('Select Outcome')).toBeInTheDocument();
      expect(screen.getByLabelText('Stake Amount')).toBeInTheDocument();
      expect(screen.getByLabelText('Desired Odds')).toBeInTheDocument();
      
      // Check for help text associations
      const stakeInput = screen.getByLabelText('Stake Amount');
      const describedBy = stakeInput.getAttribute('aria-describedby');
      if (describedBy) {
        expect(document.getElementById(describedBy)).toBeInTheDocument();
      }
    });

    it('announces betting actions to screen readers', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Select outcome
      const outcomeSelect = screen.getByLabelText('Select Outcome');
      await user.click(outcomeSelect);
      
      // Check for live region announcements
      const liveRegions = document.querySelectorAll('[aria-live]');
      expect(liveRegions.length).toBeGreaterThan(0);
      
      // Check if status updates are announced
      const statusElements = document.querySelectorAll('[role="status"], [aria-live="polite"]');
      expect(statusElements.length).toBeGreaterThan(0);
    });

    it('supports keyboard navigation for all interactive elements', async () => {
      const user = userEvent.setup();
      const component = render(<BettingInterface {...defaultProps} />);
      
      const keyboardTester = new KeyboardNavigationTester();
      const tabOrderResult = await keyboardTester.testTabOrder(component.container);
      
      expect(tabOrderResult.success).toBe(true);
      
      if (!tabOrderResult.success) {
        console.log('Tab order issues:', tabOrderResult.issues);
      }
      
      // Test keyboard activation
      const activationResult = await keyboardTester.testKeyboardActivation(component.container);
      expect(activationResult.success).toBe(true);
    });

    it('provides clear error messages for invalid inputs', async () => {
      const user = userEvent.setup();
      render(<BettingInterface {...defaultProps} />);
      
      // Enter invalid stake amount
      const stakeInput = screen.getByLabelText('Stake Amount');
      await user.type(stakeInput, '1'); // Below minimum
      
      // Check for error message
      await user.tab(); // Trigger validation
      
      const errorMessage = screen.queryByRole('alert');
      if (errorMessage) {
        expect(errorMessage).toHaveTextContent(/minimum/i);
      }
      
      // Check aria-invalid attribute
      expect(stakeInput).toHaveAttribute('aria-invalid', 'true');
    });

    it('has appropriate ARIA roles for position type selection', async () => {
      render(<BettingInterface {...defaultProps} />);
      
      const positionGroup = screen.getByRole('radiogroup');
      expect(positionGroup).toBeInTheDocument();
      
      const backingOption = screen.getByRole('radio', { name: /back.*for/i });
      const layingOption = screen.getByRole('radio', { name: /lay.*against/i });
      
      expect(backingOption).toBeInTheDocument();
      expect(layingOption).toBeInTheDocument();
      
      // Check default selection
      expect(backingOption).toBeChecked();
    });
  });

  describe('MarketBrowser Accessibility', () => {
    it('passes comprehensive accessibility tests', async () => {
      const component = render(<MarketBrowser />);
      
      // Wait for markets to load
      await screen.findByText('Browse Markets');
      
      const results = await runAccessibilityTestSuite(component);
      expect(results.overallSuccess).toBe(true);
    });

    it('has proper search landmark and labeling', async () => {
      render(<MarketBrowser />);
      
      const searchInput = screen.getByRole('searchbox');
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveAccessibleName();
      
      // Check for search landmark
      const searchLandmark = screen.getByRole('search');
      expect(searchLandmark).toBeInTheDocument();
    });

    it('has accessible filter controls', async () => {
      render(<MarketBrowser />);
      
      // Category filter
      const categoryFilter = screen.getByRole('combobox', { name: /category/i });
      expect(categoryFilter).toBeInTheDocument();
      expect(categoryFilter).toHaveAttribute('aria-expanded');
      
      // Status filter
      const statusFilter = screen.getByRole('combobox', { name: /status/i });
      expect(statusFilter).toBeInTheDocument();
      expect(statusFilter).toHaveAttribute('aria-expanded');
    });

    it('announces loading and error states', async () => {
      render(<MarketBrowser />);
      
      // Check for loading announcement
      const loadingElement = screen.queryByText('Loading markets...');
      if (loadingElement) {
        const liveRegion = ScreenReaderTester.hasLiveRegion(loadingElement);
        expect(liveRegion.hasLiveRegion).toBe(true);
        expect(liveRegion.isPolite).toBe(true);
      }
    });

    it('has proper grid navigation for market cards', async () => {
      const user = userEvent.setup();
      render(<MarketBrowser />);
      
      // Wait for markets to load
      await screen.findByText('Browse Markets');
      
      // Check grid role
      const marketsGrid = document.querySelector('[role="grid"], [data-testid="markets-grid"]');
      if (marketsGrid) {
        expect(marketsGrid).toHaveAttribute('role', 'grid');
        
        // Test arrow key navigation
        const firstCard = marketsGrid.querySelector('[role="gridcell"], [data-testid*="market-card"]');
        if (firstCard) {
          (firstCard as HTMLElement).focus();
          await user.keyboard('{ArrowRight}');
          
          // Should move focus to next card
          expect(document.activeElement).not.toBe(firstCard);
        }
      }
    });
  });

  describe('CreateBetWizard Accessibility', () => {
    const defaultProps = {
      onComplete: jest.fn(),
      onCancel: jest.fn()
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<CreateBetWizard {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component, {
        axeConfig: formAccessibilityConfig
      });
      
      expect(results.overallSuccess).toBe(true);
    });

    it('has proper wizard navigation structure', async () => {
      render(<CreateBetWizard {...defaultProps} />);
      
      // Check for progress indicator
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toBeInTheDocument();
      expect(progressbar).toHaveAttribute('aria-valuenow', '1');
      expect(progressbar).toHaveAttribute('aria-valuemax', '5');
      
      // Check for step headings
      const stepHeading = screen.getByRole('heading', { level: 2 });
      expect(stepHeading).toHaveTextContent(/step 1 of 5/i);
    });

    it('announces step changes to screen readers', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Fill required fields
      await user.type(screen.getByLabelText('Market Title'), 'Test Market');
      await user.type(screen.getByLabelText('Description'), 'Test description that meets minimum length requirements');
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Other'));
      
      // Continue to next step
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Check that step change is announced
      const stepHeading = screen.getByRole('heading', { level: 2 });
      const liveRegion = ScreenReaderTester.hasLiveRegion(stepHeading);
      expect(liveRegion.hasLiveRegion).toBe(true);
    });

    it('provides accessible form validation', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Try to continue without filling fields
      const continueButton = screen.getByRole('button', { name: /continue/i });
      expect(continueButton).toBeDisabled();
      
      // Check for proper disabled state announcement
      expect(continueButton).toHaveAttribute('aria-disabled', 'true');
      
      // Add short description
      await user.type(screen.getByLabelText('Description'), 'Too short');
      
      // Should show validation error
      const errorMessage = screen.queryByRole('alert');
      if (errorMessage) {
        expect(errorMessage).toBeInTheDocument();
      }
      
      // Check aria-invalid on field
      const descriptionField = screen.getByLabelText('Description');
      expect(descriptionField).toHaveAttribute('aria-invalid', 'true');
    });

    it('has accessible outcome management in multiple choice', async () => {
      const user = userEvent.setup();
      render(<CreateBetWizard {...defaultProps} />);
      
      // Complete step 1
      await user.type(screen.getByLabelText('Market Title'), 'Multi Choice Test');
      await user.type(screen.getByLabelText('Description'), 'Testing multiple choice accessibility features in wizard');
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Sports'));
      
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Select multiple choice
      const multipleChoiceOption = screen.getByRole('button', { name: /multiple choice/i });
      await user.click(multipleChoiceOption);
      
      // Check outcome inputs have proper labels
      const outcomeInputs = screen.getAllByLabelText(/outcome \d+/i);
      expect(outcomeInputs.length).toBeGreaterThan(0);
      
      // Check add/remove buttons are accessible
      const addButton = screen.getByRole('button', { name: /add outcome/i });
      expect(addButton).toBeInTheDocument();
      expect(addButton).toHaveAccessibleName();
      
      // Add outcome and check remove button
      await user.click(addButton);
      const removeButtons = screen.getAllByRole('button', { name: /remove outcome/i });
      expect(removeButtons.length).toBeGreaterThan(0);
      removeButtons.forEach(button => {
        expect(button).toHaveAccessibleName();
      });
    });
  });

  describe('MarketHeader Accessibility', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      userParticipation: null
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<MarketHeader {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component);
      expect(results.overallSuccess).toBe(true);
    });

    it('has proper heading structure', async () => {
      render(<MarketHeader {...defaultProps} />);
      
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent(mockBinaryMarket.title);
    });

    it('has accessible status information', async () => {
      render(<MarketHeader {...defaultProps} />);
      
      const statusElement = screen.getByText('Active');
      expect(statusElement).toHaveAttribute('aria-label', expect.stringMatching(/market status/i));
    });

    it('has accessible action buttons', async () => {
      render(<MarketHeader {...defaultProps} />);
      
      const shareButton = screen.getByRole('button', { name: /share/i });
      expect(shareButton).toHaveAccessibleName();
      
      const favoriteButton = screen.getByRole('button', { name: /favorite/i });
      expect(favoriteButton).toHaveAccessibleName();
      
      const reportButton = screen.getByRole('button', { name: /report/i });
      expect(reportButton).toHaveAccessibleName();
    });

    it('has semantic time elements', async () => {
      render(<MarketHeader {...defaultProps} />);
      
      const timeElements = document.querySelectorAll('time');
      timeElements.forEach(timeElement => {
        expect(timeElement).toHaveAttribute('dateTime');
      });
    });
  });

  describe('MarketOutcomes Accessibility', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      onOutcomeSelect: jest.fn(),
      userParticipation: null
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<MarketOutcomes {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component);
      expect(results.overallSuccess).toBe(true);
    });

    it('has accessible outcome cards', async () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        const outcomeCard = screen.getByTestId(`outcome-${outcome.id}`);
        expect(outcomeCard).toHaveAttribute('aria-label', expect.stringContaining(outcome.outcome_text));
        expect(outcomeCard).toHaveAttribute('role', 'button');
      });
    });

    it('announces odds changes to screen readers', async () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      const oddsElements = document.querySelectorAll('[data-testid*="odds"]');
      oddsElements.forEach(element => {
        const liveRegion = ScreenReaderTester.hasLiveRegion(element);
        if (liveRegion.hasLiveRegion) {
          expect(liveRegion.isPolite).toBe(true);
        }
      });
    });

    it('provides context for probabilities', async () => {
      render(<MarketOutcomes {...defaultProps} />);
      
      mockBinaryMarket.outcomes.forEach(outcome => {
        const probability = (outcome.implied_probability * 100).toFixed(1);
        const probabilityElement = screen.getByText(`${probability}%`);
        expect(probabilityElement).toHaveAttribute('aria-label', expect.stringContaining('percent'));
      });
    });
  });

  describe('SettlementInterface Accessibility', () => {
    const defaultProps = {
      market: mockBinaryMarket,
      onSettlement: jest.fn(),
      onSuspend: jest.fn(),
      onCancel: jest.fn()
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<SettlementInterface {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component);
      expect(results.overallSuccess).toBe(true);
    });

    it('has proper radiogroup for outcome selection', async () => {
      render(<SettlementInterface {...defaultProps} />);
      
      const radioGroup = screen.getByRole('radiogroup');
      expect(radioGroup).toBeInTheDocument();
      expect(radioGroup).toHaveAccessibleName();
      
      const radioButtons = screen.getAllByRole('radio');
      expect(radioButtons.length).toBe(mockBinaryMarket.outcomes.length);
      
      radioButtons.forEach((radio, index) => {
        expect(radio).toHaveAccessibleName();
        expect(radio).toHaveAttribute('aria-describedby');
      });
    });

    it('announces settlement actions', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome
      const firstOutcome = screen.getAllByRole('radio')[0];
      await user.click(firstOutcome);
      
      // Try to settle
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      // Check for status announcements
      const statusElements = document.querySelectorAll('[aria-live="polite"]');
      expect(statusElements.length).toBeGreaterThan(0);
    });

    it('has accessible confirmation dialogs', async () => {
      const user = userEvent.setup();
      render(<SettlementInterface {...defaultProps} />);
      
      // Select outcome and settle
      const firstOutcome = screen.getAllByRole('radio')[0];
      await user.click(firstOutcome);
      
      const settleButton = screen.getByRole('button', { name: /settle market/i });
      await user.click(settleButton);
      
      // Check dialog accessibility
      const dialog = screen.queryByRole('dialog');
      if (dialog) {
        expect(dialog).toHaveAttribute('aria-labelledby');
        expect(dialog).toHaveAttribute('aria-describedby');
        
        // Check focus trap
        const closeButton = screen.getByRole('button', { name: /close/i });
        expect(document.activeElement).toBe(closeButton);
      }
    });
  });

  describe('UserBetManager Accessibility', () => {
    const defaultProps = {
      userBets: mockUserParticipations.map(p => ({
        ...p,
        market_title: 'Test Market'
      }))
    };

    it('passes comprehensive accessibility tests', async () => {
      const component = render(<UserBetManager {...defaultProps} />);
      
      const results = await runAccessibilityTestSuite(component);
      expect(results.overallSuccess).toBe(true);
    });

    it('has accessible data table structure', async () => {
      render(<UserBetManager {...defaultProps} />);
      
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
      expect(table).toHaveAccessibleName();
      
      // Check column headers
      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders.length).toBeGreaterThan(0);
      
      columnHeaders.forEach(header => {
        expect(header).toHaveAccessibleName();
      });
      
      // Check row structure
      const rows = screen.getAllByRole('row');
      expect(rows.length).toBeGreaterThan(1); // Header + data rows
    });

    it('has accessible action buttons for each bet', async () => {
      render(<UserBetManager {...defaultProps} />);
      
      const actionButtons = screen.getAllByRole('button');
      actionButtons.forEach(button => {
        expect(button).toHaveAccessibleName();
        
        // Check if button describes which bet it affects
        const name = button.getAttribute('aria-label') || button.textContent;
        expect(name).toBeTruthy();
      });
    });

    it('announces bet status changes', async () => {
      render(<UserBetManager {...defaultProps} />);
      
      // Check for status indicators with proper semantics
      const statusElements = document.querySelectorAll('[data-testid*="status"]');
      statusElements.forEach(element => {
        expect(element).toHaveAccessibleName();
      });
    });
  });
});