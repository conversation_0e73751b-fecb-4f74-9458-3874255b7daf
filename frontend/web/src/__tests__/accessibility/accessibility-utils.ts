/**
 * Custom Betting Platform - Accessibility Testing Utilities
 * ========================================================
 * 
 * Utilities for comprehensive accessibility testing.
 */

import { render, RenderResult } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import userEvent from '@testing-library/user-event';
import { defaultAxeConfig, formatAxeResults, categorizeViolationsBySeverity } from './axe-config';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Accessibility test wrapper
export async function runAxeTest(
  component: RenderResult,
  config = defaultAxeConfig,
  options: { throwOnViolation?: boolean; logResults?: boolean } = {}
) {
  const { throwOnViolation = true, logResults = false } = options;
  
  const results = await axe(component.container, config);
  const formatted = formatAxeResults(results);
  
  if (logResults) {
    console.log(formatted.summary);
    if (formatted.violations.length > 0) {
      console.table(formatted.violations);
    }
  }
  
  if (throwOnViolation) {
    expect(results).toHaveNoViolations();
  }
  
  return formatted;
}

// Keyboard navigation testing utility
export class KeyboardNavigationTester {
  private user: ReturnType<typeof userEvent.setup>;
  private focusableElements: Element[] = [];
  
  constructor() {
    this.user = userEvent.setup();
  }
  
  // Find all focusable elements in container
  findFocusableElements(container: Element): Element[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');
    
    this.focusableElements = Array.from(container.querySelectorAll(focusableSelectors));
    return this.focusableElements;
  }
  
  // Test tab order matches visual order
  async testTabOrder(container: Element): Promise<{
    success: boolean;
    issues: string[];
    tabOrder: string[];
  }> {
    const focusable = this.findFocusableElements(container);
    const issues: string[] = [];
    const tabOrder: string[] = [];
    
    if (focusable.length === 0) {
      return { success: true, issues: ['No focusable elements found'], tabOrder: [] };
    }
    
    // Focus first element
    (focusable[0] as HTMLElement).focus();
    
    for (let i = 0; i < focusable.length; i++) {
      const currentFocused = document.activeElement;
      const expectedElement = focusable[i];
      
      if (currentFocused !== expectedElement) {
        issues.push(`Tab order mismatch at index ${i}: expected ${this.getElementDescription(expectedElement)}, got ${this.getElementDescription(currentFocused)}`);
      }
      
      tabOrder.push(this.getElementDescription(currentFocused));
      
      // Tab to next element
      if (i < focusable.length - 1) {
        await this.user.tab();
      }
    }
    
    return {
      success: issues.length === 0,
      issues,
      tabOrder
    };
  }
  
  // Test reverse tab navigation
  async testReverseTabOrder(container: Element): Promise<{
    success: boolean;
    issues: string[];
  }> {
    const focusable = this.findFocusableElements(container);
    const issues: string[] = [];
    
    if (focusable.length === 0) {
      return { success: true, issues: ['No focusable elements found'] };
    }
    
    // Focus last element
    (focusable[focusable.length - 1] as HTMLElement).focus();
    
    for (let i = focusable.length - 1; i >= 0; i--) {
      const currentFocused = document.activeElement;
      const expectedElement = focusable[i];
      
      if (currentFocused !== expectedElement) {
        issues.push(`Reverse tab order mismatch at index ${i}: expected ${this.getElementDescription(expectedElement)}, got ${this.getElementDescription(currentFocused)}`);
      }
      
      // Shift+Tab to previous element
      if (i > 0) {
        await this.user.tab({ shift: true });
      }
    }
    
    return {
      success: issues.length === 0,
      issues
    };
  }
  
  // Test keyboard activation (Enter/Space)
  async testKeyboardActivation(container: Element): Promise<{
    success: boolean;
    results: Array<{ element: string; enterWorks: boolean; spaceWorks: boolean; }>;
  }> {
    const activatable = Array.from(container.querySelectorAll('button, [role="button"], a, input[type="submit"], input[type="button"]'));
    const results = [];
    
    for (const element of activatable) {
      const htmlElement = element as HTMLElement;
      const description = this.getElementDescription(element);
      
      // Test Enter key
      htmlElement.focus();
      const enterClickHandler = jest.fn();
      element.addEventListener('click', enterClickHandler);
      await this.user.keyboard('{Enter}');
      const enterWorks = enterClickHandler.mock.calls.length > 0;
      
      // Test Space key (for buttons)
      const spaceClickHandler = jest.fn();
      element.addEventListener('click', spaceClickHandler);
      await this.user.keyboard(' ');
      const spaceWorks = spaceClickHandler.mock.calls.length > 0 || element.tagName === 'A';
      
      results.push({
        element: description,
        enterWorks,
        spaceWorks: element.tagName === 'BUTTON' ? spaceWorks : true // Links don't need space activation
      });
    }
    
    const success = results.every(r => r.enterWorks && r.spaceWorks);
    
    return { success, results };
  }
  
  // Helper to get element description
  private getElementDescription(element: Element | null): string {
    if (!element) return 'null';
    
    const tag = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const className = element.className ? `.${element.className.split(' ').join('.')}` : '';
    const text = element.textContent?.slice(0, 20) || '';
    const ariaLabel = element.getAttribute('aria-label') || '';
    
    return `${tag}${id}${className} "${ariaLabel || text}"`.trim();
  }
}

// Screen reader testing utility
export class ScreenReaderTester {
  // Test if element has proper accessible name
  static hasAccessibleName(element: Element): {
    hasName: boolean;
    name: string;
    sources: string[];
  } {
    const sources: string[] = [];
    let name = '';
    
    // aria-label
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) {
      name = ariaLabel;
      sources.push('aria-label');
    }
    
    // aria-labelledby
    const ariaLabelledby = element.getAttribute('aria-labelledby');
    if (ariaLabelledby && !name) {
      const labelElement = document.getElementById(ariaLabelledby);
      if (labelElement) {
        name = labelElement.textContent || '';
        sources.push('aria-labelledby');
      }
    }
    
    // Associated label
    if (!name && element.tagName === 'INPUT') {
      const labels = document.querySelectorAll(`label[for="${element.id}"]`);
      if (labels.length > 0) {
        name = labels[0].textContent || '';
        sources.push('label[for]');
      }
    }
    
    // Text content
    if (!name) {
      name = element.textContent || '';
      if (name) sources.push('textContent');
    }
    
    // Alt text for images
    if (!name && element.tagName === 'IMG') {
      name = element.getAttribute('alt') || '';
      if (name) sources.push('alt');
    }
    
    return {
      hasName: name.trim().length > 0,
      name: name.trim(),
      sources
    };
  }
  
  // Test if element has proper role
  static hasProperRole(element: Element): {
    hasRole: boolean;
    role: string;
    isImplicit: boolean;
  } {
    const explicitRole = element.getAttribute('role');
    if (explicitRole) {
      return {
        hasRole: true,
        role: explicitRole,
        isImplicit: false
      };
    }
    
    // Check implicit roles
    const tagName = element.tagName.toLowerCase();
    const implicitRoles: Record<string, string> = {
      'button': 'button',
      'a': 'link',
      'input': 'textbox',
      'h1': 'heading',
      'h2': 'heading',
      'h3': 'heading',
      'h4': 'heading',
      'h5': 'heading',
      'h6': 'heading',
      'nav': 'navigation',
      'main': 'main',
      'section': 'region',
      'article': 'article',
      'aside': 'complementary',
      'header': 'banner',
      'footer': 'contentinfo'
    };
    
    const implicitRole = implicitRoles[tagName];
    return {
      hasRole: !!implicitRole,
      role: implicitRole || 'generic',
      isImplicit: true
    };
  }
  
  // Test if element announces state changes
  static hasLiveRegion(element: Element): {
    hasLiveRegion: boolean;
    ariaLive: string;
    isPolite: boolean;
  } {
    const ariaLive = element.getAttribute('aria-live') || '';
    const role = element.getAttribute('role');
    
    // Check for explicit aria-live
    if (ariaLive) {
      return {
        hasLiveRegion: true,
        ariaLive,
        isPolite: ariaLive === 'polite'
      };
    }
    
    // Check for implicit live regions
    const liveRoles = ['alert', 'status', 'log', 'marquee', 'timer'];
    if (role && liveRoles.includes(role)) {
      return {
        hasLiveRegion: true,
        ariaLive: role === 'alert' ? 'assertive' : 'polite',
        isPolite: role !== 'alert'
      };
    }
    
    return {
      hasLiveRegion: false,
      ariaLive: '',
      isPolite: false
    };
  }
}

// Color contrast testing utility
export class ColorContrastTester {
  // Calculate relative luminance
  static getRelativeLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb;
    
    const rsRGB = r / 255;
    const gsRGB = g / 255;
    const bsRGB = b / 255;
    
    const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
    const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
    const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
    
    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  }
  
  // Calculate contrast ratio
  static getContrastRatio(color1: { r: number; g: number; b: number }, color2: { r: number; g: number; b: number }): number {
    const l1 = this.getRelativeLuminance(color1);
    const l2 = this.getRelativeLuminance(color2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  // Check if contrast meets WCAG standards
  static meetsWCAGContrast(ratio: number, level: 'AA' | 'AAA' = 'AA', isLargeText = false): boolean {
    if (level === 'AAA') {
      return isLargeText ? ratio >= 4.5 : ratio >= 7;
    }
    return isLargeText ? ratio >= 3 : ratio >= 4.5;
  }
  
  // Parse CSS color to RGB
  static parseColor(color: string): { r: number; g: number; b: number } | null {
    const ctx = document.createElement('canvas').getContext('2d');
    if (!ctx) return null;
    
    ctx.fillStyle = color;
    const computedColor = ctx.fillStyle;
    
    const match = computedColor.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
    if (match) {
      return {
        r: parseInt(match[1], 16),
        g: parseInt(match[2], 16),
        b: parseInt(match[3], 16)
      };
    }
    
    const rgbMatch = computedColor.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (rgbMatch) {
      return {
        r: parseInt(rgbMatch[1]),
        g: parseInt(rgbMatch[2]),
        b: parseInt(rgbMatch[3])
      };
    }
    
    return null;
  }
}

// High-level accessibility test suite
export async function runAccessibilityTestSuite(
  component: RenderResult,
  options: {
    skipAxe?: boolean;
    skipKeyboard?: boolean;
    skipScreenReader?: boolean;
    axeConfig?: any;
  } = {}
): Promise<{
  axeResults?: any;
  keyboardResults?: any;
  screenReaderResults?: any;
  overallSuccess: boolean;
  summary: string;
}> {
  const results: any = {};
  let overallSuccess = true;
  const issues: string[] = [];
  
  // Run axe tests
  if (!options.skipAxe) {
    try {
      results.axeResults = await runAxeTest(component, options.axeConfig, { throwOnViolation: false });
      if (results.axeResults.violations.length > 0) {
        overallSuccess = false;
        issues.push(`${results.axeResults.violations.length} axe violations`);
      }
    } catch (error) {
      overallSuccess = false;
      issues.push('Axe test failed');
    }
  }
  
  // Run keyboard navigation tests
  if (!options.skipKeyboard) {
    const keyboardTester = new KeyboardNavigationTester();
    const tabOrder = await keyboardTester.testTabOrder(component.container);
    const activation = await keyboardTester.testKeyboardActivation(component.container);
    
    results.keyboardResults = { tabOrder, activation };
    
    if (!tabOrder.success) {
      overallSuccess = false;
      issues.push(`${tabOrder.issues.length} keyboard navigation issues`);
    }
    
    if (!activation.success) {
      overallSuccess = false;
      issues.push('Keyboard activation issues');
    }
  }
  
  // Run screen reader tests
  if (!options.skipScreenReader) {
    const interactiveElements = component.container.querySelectorAll('button, input, select, textarea, a, [role="button"], [role="link"]');
    const screenReaderIssues: string[] = [];
    
    interactiveElements.forEach(element => {
      const accessibleName = ScreenReaderTester.hasAccessibleName(element);
      if (!accessibleName.hasName) {
        screenReaderIssues.push(`Element missing accessible name: ${element.tagName.toLowerCase()}`);
      }
    });
    
    results.screenReaderResults = { issues: screenReaderIssues };
    
    if (screenReaderIssues.length > 0) {
      overallSuccess = false;
      issues.push(`${screenReaderIssues.length} screen reader issues`);
    }
  }
  
  const summary = `
Accessibility Test Suite Results:
- Overall Success: ${overallSuccess ? 'PASS' : 'FAIL'}
- Issues Found: ${issues.join(', ') || 'None'}
  `;
  
  return {
    ...results,
    overallSuccess,
    summary
  };
}