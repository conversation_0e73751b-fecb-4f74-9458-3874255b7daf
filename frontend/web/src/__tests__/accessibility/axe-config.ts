/**
 * Custom Betting Platform - Axe-core Accessibility Configuration
 * =============================================================
 * 
 * Configuration for automated accessibility testing with axe-core.
 */

import { AxeResults, RunOptions } from 'axe-core';

// Standard axe configuration for all tests
export const defaultAxeConfig: RunOptions = {
  rules: {
    // Enable all WCAG 2.1 Level AA rules
    'color-contrast': { enabled: true },
    'keyboard-navigation': { enabled: true },
    'focus-order-semantics': { enabled: true },
    'aria-required-attr': { enabled: true },
    'aria-roles': { enabled: true },
    'aria-valid-attr-value': { enabled: true },
    'aria-valid-attr': { enabled: true },
    'button-name': { enabled: true },
    'bypass': { enabled: true },
    'document-title': { enabled: true },
    'duplicate-id': { enabled: true },
    'form-field-multiple-labels': { enabled: true },
    'frame-title': { enabled: true },
    'html-has-lang': { enabled: true },
    'html-lang-valid': { enabled: true },
    'image-alt': { enabled: true },
    'input-image-alt': { enabled: true },
    'label': { enabled: true },
    'landmark-one-main': { enabled: true },
    'landmark-complementary-is-top-level': { enabled: true },
    'link-name': { enabled: true },
    'list': { enabled: true },
    'listitem': { enabled: true },
    'meta-refresh': { enabled: true },
    'meta-viewport': { enabled: true },
    'object-alt': { enabled: true },
    'page-has-heading-one': { enabled: true },
    'region': { enabled: true },
    'scope-attr-valid': { enabled: true },
    'server-side-image-map': { enabled: true },
    'skip-link': { enabled: true },
    'tabindex': { enabled: true },
    'table-fake-caption': { enabled: true },
    'td-headers-attr': { enabled: true },
    'th-has-data-cells': { enabled: true },
    'valid-lang': { enabled: true },
    'video-caption': { enabled: true },

    // Custom betting platform specific rules
    'autocomplete-valid': { enabled: true },
    'avoid-inline-spacing': { enabled: true },
    'scrollable-region-focusable': { enabled: true },
    'focus-order-semantics': { enabled: true },

    // Temporarily disable rules that may conflict with our design system
    'color-contrast-enhanced': { enabled: false }, // WCAG AAA level
    'focus-order-semantics': { enabled: false }, // May conflict with modal behavior
  },
  tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
  reporter: 'v2'
};

// Configuration for form-heavy components like CreateBetWizard
export const formAccessibilityConfig: RunOptions = {
  ...defaultAxeConfig,
  rules: {
    ...defaultAxeConfig.rules,
    // Enhanced form-specific rules
    'label-title-only': { enabled: true },
    'label-content-name-mismatch': { enabled: true },
    'form-field-multiple-labels': { enabled: true },
    'required-attr': { enabled: true },
    'aria-required-children': { enabled: true },
    'aria-required-parent': { enabled: true },
  }
};

// Configuration for interactive components like BettingInterface
export const interactiveAccessibilityConfig: RunOptions = {
  ...defaultAxeConfig,
  rules: {
    ...defaultAxeConfig.rules,
    // Enhanced interactive element rules
    'button-name': { enabled: true },
    'link-in-text-block': { enabled: true },
    'target-size': { enabled: true },
    'focus-order-semantics': { enabled: true },
    'keyboard-navigation': { enabled: true },
  }
};

// Configuration for data visualization components
export const visualizationAccessibilityConfig: RunOptions = {
  ...defaultAxeConfig,
  rules: {
    ...defaultAxeConfig.rules,
    // Data visualization specific rules
    'svg-img-alt': { enabled: true },
    'image-alt': { enabled: true },
    'color-contrast': { enabled: true },
    'focus-order-semantics': { enabled: false }, // Charts may have complex focus patterns
  }
};

// Configuration for mobile-specific testing
export const mobileAccessibilityConfig: RunOptions = {
  ...defaultAxeConfig,
  rules: {
    ...defaultAxeConfig.rules,
    // Mobile-specific accessibility rules
    'target-size': { enabled: true },
    'meta-viewport': { enabled: true },
    'scrollable-region-focusable': { enabled: true },
    'focus-order-semantics': { enabled: true },
  }
};

// Custom rule configurations for betting platform
export const customBettingRules = {
  // Ensure betting forms have proper error announcements
  'betting-form-errors': {
    enabled: true,
    selector: '[data-testid*="betting"], [data-testid*="bet-"]',
    any: ['aria-live', 'role-alert'],
    tags: ['custom', 'betting']
  },

  // Ensure market status is announced to screen readers
  'market-status-announcement': {
    enabled: true,
    selector: '[data-testid*="market-status"]',
    any: ['aria-live', 'aria-label'],
    tags: ['custom', 'market']
  },

  // Ensure odds updates are announced
  'odds-update-announcement': {
    enabled: true,
    selector: '[data-testid*="odds"]',
    any: ['aria-live'],
    tags: ['custom', 'odds']
  }
};

// Helper function to create custom axe configuration
export function createCustomAxeConfig(overrides: Partial<RunOptions> = {}): RunOptions {
  return {
    ...defaultAxeConfig,
    ...overrides,
    rules: {
      ...defaultAxeConfig.rules,
      ...overrides.rules
    }
  };
}

// Helper function to filter and format axe results
export function formatAxeResults(results: AxeResults): {
  violations: any[];
  passes: number;
  incomplete: any[];
  summary: string;
} {
  const violations = results.violations.map(violation => ({
    id: violation.id,
    impact: violation.impact,
    description: violation.description,
    help: violation.help,
    helpUrl: violation.helpUrl,
    nodes: violation.nodes.length,
    selectors: violation.nodes.map(node => node.target).flat(),
    messages: violation.nodes.map(node => node.failureSummary)
  }));

  const incomplete = results.incomplete.map(item => ({
    id: item.id,
    impact: item.impact,
    description: item.description,
    nodes: item.nodes.length,
    selectors: item.nodes.map(node => node.target).flat()
  }));

  const summary = `
Accessibility Test Summary:
- Violations: ${violations.length}
- Passes: ${results.passes.length}
- Incomplete: ${incomplete.length}
- Total Rules Tested: ${results.passes.length + violations.length + incomplete.length}
  `;

  return {
    violations,
    passes: results.passes.length,
    incomplete,
    summary
  };
}

// Severity levels for accessibility issues
export const AccessibilitySeverity = {
  CRITICAL: 'critical',
  SERIOUS: 'serious',
  MODERATE: 'moderate',
  MINOR: 'minor'
} as const;

export type AccessibilitySeverityType = typeof AccessibilitySeverity[keyof typeof AccessibilitySeverity];

// Helper to categorize violations by severity
export function categorizeViolationsBySeverity(violations: any[]) {
  const categorized = {
    [AccessibilitySeverity.CRITICAL]: [],
    [AccessibilitySeverity.SERIOUS]: [],
    [AccessibilitySeverity.MODERATE]: [],
    [AccessibilitySeverity.MINOR]: []
  };

  violations.forEach(violation => {
    const severity = violation.impact as AccessibilitySeverityType;
    if (categorized[severity]) {
      categorized[severity].push(violation);
    } else {
      categorized[AccessibilitySeverity.MINOR].push(violation);
    }
  });

  return categorized;
}

// Test environment detection
export function getTestEnvironmentConfig(): RunOptions {
  const isCI = process.env.CI === 'true';
  const isMobile = process.env.TEST_MOBILE === 'true';
  
  if (isMobile) {
    return mobileAccessibilityConfig;
  }
  
  if (isCI) {
    // More lenient config for CI to avoid flaky tests
    return {
      ...defaultAxeConfig,
      rules: {
        ...defaultAxeConfig.rules,
        'color-contrast': { enabled: false }, // May fail due to CI environment differences
        'focus-order-semantics': { enabled: false } // Complex focus patterns may be flaky
      }
    };
  }
  
  return defaultAxeConfig;
}