/**
 * Expert Analyst Security Tests
 * ==============================
 * 
 * Security tests for the Expert Analyst module covering:
 * - Authentication and authorization
 * - Input validation and XSS prevention
 * - CSRF protection
 * - Payment security
 * - Data exposure prevention
 * - Session management
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { expertAnalystApi } from '@/lib/expert-analyst-api';
import { mockExperts, mockPicks, mockSubscriptions } from '../expert-analyst/mocks/expert-analyst-data';

// Components under test
import ExpertsMarketplace from '@/app/experts/page';
import ExpertDetailPage from '@/app/experts/[id]/page';
import PicksFeedPage from '@/app/picks/page';
import ExpertPortalPage from '@/app/expert-portal/page';

// Mock dependencies
jest.mock('@clerk/nextjs');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));
jest.mock('@/lib/expert-analyst-api');

// Security test utilities
class SecurityTestUtils {
  static createMaliciousPayload(type: 'xss' | 'sql' | 'script'): string {
    switch (type) {
      case 'xss':
        return '<script>alert("XSS")</script>';
      case 'sql':
        return "'; DROP TABLE users; --";
      case 'script':
        return 'javascript:alert("XSS")';
      default:
        return '<img src=x onerror=alert("XSS")>';
    }
  }

  static createCSRFToken(): string {
    return 'csrf-token-' + Math.random().toString(36);
  }

  static mockUnauthenticatedUser() {
    return {
      getToken: jest.fn().mockRejectedValue(new Error('Unauthorized')),
      userId: null,
      isSignedIn: false,
      isLoaded: true
    };
  }

  static mockAuthenticatedUser(userId = 'test-user-id') {
    return {
      getToken: jest.fn().mockResolvedValue('valid-jwt-token'),
      userId,
      isSignedIn: true,
      isLoaded: true
    };
  }
}

describe('Expert Analyst Security Tests', () => {
  const mockPush = jest.fn();
  let consoleErrorSpy: jest.SpyInstance;
  
  beforeEach(() => {
    jest.clearAllMocks();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    // Default API mocks
    (expertAnalystApi.getExperts as jest.Mock).mockResolvedValue({
      experts: Object.values(mockExperts),
      total_count: Object.values(mockExperts).length
    });
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  describe('Authentication Security', () => {
    it('should redirect unauthenticated users from protected routes', async () => {
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockUnauthenticatedUser());
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/sign-in?redirect=/picks');
      });
    });

    it('should prevent access to expert portal without expert role', async () => {
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue(new Error('Forbidden'));
      
      render(<ExpertPortalPage />);
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/experts/apply');
      });
    });

    it('should handle expired tokens gracefully', async () => {
      const mockGetToken = jest.fn()
        .mockResolvedValueOnce('expired-token')
        .mockRejectedValueOnce(new Error('Token expired'));
      
      (useAuth as jest.Mock).mockReturnValue({
        getToken: mockGetToken,
        userId: 'test-user',
        isSignedIn: true
      });
      
      // Mock API returning 401
      (expertAnalystApi.getPicks as jest.Mock).mockRejectedValue({
        response: { status: 401 }
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Should attempt to refresh token and redirect to login
        expect(mockGetToken).toHaveBeenCalledTimes(2);
      });
    });

    it('should validate JWT token structure', async () => {
      const invalidTokens = [
        'invalid.token',
        'not-a-jwt',
        '',
        'header.payload', // Missing signature
        'too.many.parts.here'
      ];
      
      for (const invalidToken of invalidTokens) {
        const mockGetToken = jest.fn().mockResolvedValue(invalidToken);
        
        (useAuth as jest.Mock).mockReturnValue({
          getToken: mockGetToken,
          userId: 'test-user',
          isSignedIn: true
        });
        
        // API should reject invalid tokens
        (expertAnalystApi.getPicks as jest.Mock).mockRejectedValue({
          response: { status: 401, data: { error: 'Invalid token' } }
        });
        
        render(<PicksFeedPage />);
        
        await waitFor(() => {
          expect(consoleErrorSpy).toHaveBeenCalledWith(
            expect.stringMatching(/unauthorized|invalid/i),
            expect.any(Object)
          );
        });
      }
    });

    it('should prevent subscription access for wrong user', async () => {
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser('user-1'));
      
      // Mock subscription belonging to different user
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [{
          ...mockSubscriptions[0],
          user_id: 'user-2' // Different user
        }],
        total_count: 1
      });
      
      render(<PicksFeedPage />);
      
      await waitFor(() => {
        // Should not display picks from other user's subscription
        expect(screen.queryByTestId('pick-card')).not.toBeInTheDocument();
        expect(screen.getByText(/no active subscriptions/i)).toBeInTheDocument();
      });
    });
  });

  describe('Input Validation Security', () => {
    it('should sanitize user input in search fields', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search experts/i)).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText(/search experts/i);
      const maliciousInput = SecurityTestUtils.createMaliciousPayload('xss');
      
      await user.type(searchInput, maliciousInput);
      
      // Input should be sanitized - no script tags in DOM
      expect(document.body.innerHTML).not.toContain('<script>');
      expect(document.body.innerHTML).not.toContain('alert(');
      
      // Value should be escaped
      expect(searchInput).toHaveValue(expect.not.stringContaining('<script>'));
    });

    it('should prevent XSS in expert profile content', async () => {
      const maliciousExpert = {
        ...mockExperts.topPerformer,
        name: SecurityTestUtils.createMaliciousPayload('xss'),
        bio: '<img src=x onerror=alert("XSS")>',
        specializations: ['<script>alert("XSS")</script>']
      };
      
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(maliciousExpert);
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Script tags should not be executed
        expect(document.body.innerHTML).not.toContain('<script>');
        expect(document.body.innerHTML).not.toContain('onerror=');
        
        // Content should be safely rendered
        expect(screen.getByRole('heading')).toBeInTheDocument();
      });
    });

    it('should validate pick creation form inputs', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      (expertAnalystApi.getExpertAnalytics as jest.Mock).mockResolvedValue({
        total_revenue: 0,
        monthly_revenue: 0,
        subscriber_growth: 0,
        pick_performance: {
          total_picks: 0,
          successful_picks: 0,
          win_rate: 0,
          roi: 0
        },
        top_categories: []
      });
      
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      // Try to submit malicious content
      const titleInput = screen.getByLabelText(/title/i);
      const descriptionInput = screen.getByLabelText(/description/i);
      
      await user.type(titleInput, SecurityTestUtils.createMaliciousPayload('xss'));
      await user.type(descriptionInput, SecurityTestUtils.createMaliciousPayload('script'));
      
      const submitButton = screen.getByRole('button', { name: /publish|create/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        // Should reject malicious input
        expect(expertAnalystApi.createPick).not.toHaveBeenCalled();
        expect(screen.getByRole('alert')).toHaveTextContent(/invalid/i);
      });
    });

    it('should prevent SQL injection attempts', async () => {
      const user = userEvent.setup();
      render(<ExpertsMarketplace />);
      
      const searchInput = await screen.findByPlaceholderText(/search experts/i);
      const sqlInjection = SecurityTestUtils.createMaliciousPayload('sql');
      
      await user.type(searchInput, sqlInjection);
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        // API should be called with sanitized input
        expect(expertAnalystApi.getExperts).toHaveBeenCalledWith(
          expect.objectContaining({
            search: expect.not.stringContaining('DROP TABLE')
          })
        );
      });
    });

    it('should validate file upload inputs', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      
      render(<ExpertPortalPage />);
      
      // Navigate to profile settings
      const profileSection = await screen.findByText('Profile Settings');
      expect(profileSection).toBeInTheDocument();
      
      // Mock file input for avatar upload
      const fileInput = screen.getByLabelText(/avatar|photo/i);
      
      // Create malicious file
      const maliciousFile = new File(['<script>alert("XSS")</script>'], 'malicious.html', {
        type: 'text/html'
      });
      
      await user.upload(fileInput, maliciousFile);
      
      // Should reject non-image files
      await waitFor(() => {
        expect(screen.getByText(/only image files are allowed/i)).toBeInTheDocument();
      });
    });

    it('should enforce input length limits', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      const titleInput = screen.getByLabelText(/title/i);
      const longTitle = 'A'.repeat(1000); // Extremely long title
      
      await user.type(titleInput, longTitle);
      
      // Input should be truncated or rejected
      expect(titleInput).toHaveValue(expect.not.stringMatching(/A{1000}/));
      expect(titleInput.value.length).toBeLessThan(200);
    });
  });

  describe('Payment Security', () => {
    it('should use secure payment processing', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      (expertAnalystApi.getUserSubscriptions as jest.Mock).mockResolvedValue({
        subscriptions: [],
        total_count: 0
      });
      
      render(<ExpertDetailPage />);
      
      const subscribeButton = await screen.findByText('Subscribe');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        // Payment form should use HTTPS and secure elements
        const paymentFrame = screen.getByTestId('stripe-payment-element');
        expect(paymentFrame).toBeInTheDocument();
        
        // Should not expose card details in HTML
        expect(document.body.innerHTML).not.toContain('****************');
      });
    });

    it('should validate payment amounts', async () => {
      const user = userEvent.setup();
      
      // Mock API to return payment intent
      (expertAnalystApi.createPaymentIntent as jest.Mock).mockImplementation(async (data) => {
        // Should validate amount matches tier price
        if (data.amount !== 14999) { // $149.99 for premium tier
          throw new Error('Amount mismatch');
        }
        
        return {
          client_secret: 'pi_test_secret',
          payment_intent_id: 'pi_test',
          amount: data.amount,
          currency: 'usd'
        };
      });
      
      render(<ExpertDetailPage />);
      
      const premiumButton = screen.getAllByText('Subscribe')[1]; // Premium tier
      await user.click(premiumButton);
      
      await waitFor(() => {
        expect(expertAnalystApi.createPaymentIntent).toHaveBeenCalledWith(
          expect.objectContaining({
            amount: 14999
          })
        );
      });
    });

    it('should prevent payment replay attacks', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      
      let paymentIntentCallCount = 0;
      (expertAnalystApi.createPaymentIntent as jest.Mock).mockImplementation(async () => {
        paymentIntentCallCount++;
        return {
          client_secret: `pi_test_secret_${paymentIntentCallCount}`,
          payment_intent_id: `pi_test_${paymentIntentCallCount}`,
          amount: 14999,
          currency: 'usd'
        };
      });
      
      render(<ExpertDetailPage />);
      
      const subscribeButton = await screen.findByText('Subscribe');
      
      // Rapid clicks should not create multiple payment intents
      await user.click(subscribeButton);
      await user.click(subscribeButton);
      await user.click(subscribeButton);
      
      await waitFor(() => {
        expect(paymentIntentCallCount).toBe(1);
      });
    });

    it('should handle payment webhook security', async () => {
      // This would test webhook signature validation in a real implementation
      const webhookPayload = {
        id: 'evt_test',
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test',
            status: 'succeeded',
            amount: 14999
          }
        }
      };
      
      const validSignature = 'valid-webhook-signature';
      const invalidSignature = 'invalid-webhook-signature';
      
      // Mock webhook handler
      const handleWebhook = jest.fn();
      
      // Valid webhook should be processed
      expect(() => {
        // Signature validation would happen here
        if (validSignature === 'valid-webhook-signature') {
          handleWebhook(webhookPayload);
        }
      }).not.toThrow();
      
      // Invalid webhook should be rejected
      expect(() => {
        if (invalidSignature !== 'valid-webhook-signature') {
          throw new Error('Invalid webhook signature');
        }
      }).toThrow();
      
      expect(handleWebhook).toHaveBeenCalledWith(webhookPayload);
    });
  });

  describe('Data Protection', () => {
    it('should not expose sensitive data in client-side code', async () => {
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // Check that sensitive data is not in DOM
        expect(document.body.innerHTML).not.toContain('sk_'); // Stripe secret key
        expect(document.body.innerHTML).not.toContain('password');
        expect(document.body.innerHTML).not.toContain('secret');
        expect(document.body.innerHTML).not.toContain('private_key');
      });
    });

    it('should mask sensitive information in logs', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      
      // Mock API error with sensitive data
      (expertAnalystApi.createPaymentIntent as jest.Mock).mockRejectedValue({
        response: {
          data: {
            error: 'Payment failed',
            debug: {
              card_number: '****************',
              user_id: 'user-123'
            }
          }
        }
      });
      
      render(<ExpertDetailPage />);
      
      const subscribeButton = await screen.findByText('Subscribe');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        // Console logs should not contain sensitive data
        const consoleCalls = consoleErrorSpy.mock.calls.flat().join(' ');
        expect(consoleCalls).not.toContain('****************');
      });
    });

    it('should prevent data leakage through error messages', async () => {
      // Mock API error that could expose internal info
      (expertAnalystApi.getExpert as jest.Mock).mockRejectedValue({
        response: {
          status: 500,
          data: {
            error: 'Database connection failed',
            stack: 'Error at /var/www/app/database.js:123',
            query: 'SELECT * FROM experts WHERE id = ?'
          }
        }
      });
      
      render(<ExpertDetailPage />);
      
      await waitFor(() => {
        // User should see generic error message
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
        
        // Should not expose internal details
        expect(screen.queryByText(/database/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/var\/www/)).not.toBeInTheDocument();
        expect(screen.queryByText(/SELECT/i)).not.toBeInTheDocument();
      });
    });

    it('should implement proper session timeout', async () => {
      const mockGetToken = jest.fn();
      
      // Simulate session timeout
      mockGetToken
        .mockResolvedValueOnce('valid-token')
        .mockRejectedValueOnce(new Error('Session expired'));
      
      (useAuth as jest.Mock).mockReturnValue({
        getToken: mockGetToken,
        userId: 'test-user',
        isSignedIn: true
      });
      
      render(<PicksFeedPage />);
      
      // Initial load works
      await waitFor(() => {
        expect(screen.getByText('Expert Picks Feed')).toBeInTheDocument();
      });
      
      // Simulate session expiry on API call
      (expertAnalystApi.getPicks as jest.Mock).mockRejectedValue({
        response: { status: 401 }
      });
      
      // Trigger API call
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);
      
      await waitFor(() => {
        // Should redirect to login
        expect(mockPush).toHaveBeenCalledWith(expect.stringMatching(/sign-in/));
      });
    });
  });

  describe('CSRF Protection', () => {
    it('should include CSRF tokens in state-changing requests', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      (expertAnalystApi.getExpert as jest.Mock).mockResolvedValue(mockExperts.topPerformer);
      
      // Mock CSRF token
      const csrfToken = SecurityTestUtils.createCSRFToken();
      (global as any).window.__CSRF_TOKEN__ = csrfToken;
      
      render(<ExpertPortalPage />);
      
      const createButton = await screen.findByText('Create New Pick');
      await user.click(createButton);
      
      // Fill valid form
      await user.type(screen.getByLabelText(/title/i), 'Valid Pick Title');
      await user.type(screen.getByLabelText(/description/i), 'Valid description');
      
      const submitButton = screen.getByRole('button', { name: /publish|create/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        // API call should include CSRF token
        expect(expertAnalystApi.createPick).toHaveBeenCalledWith(
          expect.objectContaining({
            _csrf: csrfToken
          })
        );
      });
    });

    it('should reject requests without valid CSRF tokens', async () => {
      const user = userEvent.setup();
      
      (useAuth as jest.Mock).mockReturnValue(SecurityTestUtils.mockAuthenticatedUser());
      
      // Mock API to reject requests without CSRF token
      (expertAnalystApi.createSubscription as jest.Mock).mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Invalid CSRF token' }
        }
      });
      
      render(<ExpertDetailPage />);
      
      const subscribeButton = await screen.findByText('Subscribe');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        expect(screen.getByText(/security error/i)).toBeInTheDocument();
      });
    });
  });

  describe('Content Security Policy', () => {
    it('should prevent inline script execution', () => {
      // Mock CSP violation
      const cspViolationHandler = jest.fn();
      document.addEventListener('securitypolicyviolation', cspViolationHandler);
      
      // Try to inject inline script
      const script = document.createElement('script');
      script.innerHTML = 'alert("CSP Test")';
      document.head.appendChild(script);
      
      // CSP should prevent execution (in a real browser)
      // This test documents the expected behavior
      expect(cspViolationHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          violatedDirective: expect.stringContaining('script-src')
        })
      );
      
      document.removeEventListener('securitypolicyviolation', cspViolationHandler);
    });

    it('should only allow trusted domains for external resources', () => {
      render(<ExpertDetailPage />);
      
      // Check that only trusted domains are used for external resources
      const images = document.querySelectorAll('img[src^="http"]');
      const scripts = document.querySelectorAll('script[src^="http"]');
      const links = document.querySelectorAll('link[href^="http"]');
      
      const trustedDomains = [
        'cdn.betbet.com',
        'js.stripe.com',
        'fonts.googleapis.com'
      ];
      
      [...images, ...scripts, ...links].forEach(element => {
        const src = element.getAttribute('src') || element.getAttribute('href');
        if (src) {
          const url = new URL(src);
          expect(trustedDomains).toContain(url.hostname);
        }
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should implement rate limiting for API calls', async () => {
      const user = userEvent.setup();
      
      let apiCallCount = 0;
      (expertAnalystApi.getExperts as jest.Mock).mockImplementation(async () => {
        apiCallCount++;
        if (apiCallCount > 10) {
          throw new Error('Rate limit exceeded');
        }
        return { experts: [], total_count: 0 };
      });
      
      render(<ExpertsMarketplace />);
      
      // Rapid fire API calls
      for (let i = 0; i < 15; i++) {
        const searchInput = screen.getByPlaceholderText(/search experts/i);
        await user.type(searchInput, 'test');
        await user.keyboard('{Enter}');
      }
      
      await waitFor(() => {
        expect(apiCallCount).toBeLessThanOrEqual(10);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringMatching(/rate limit/i),
          expect.any(Error)
        );
      });
    });

    it('should prevent brute force login attempts', async () => {
      const user = userEvent.setup();
      
      let loginAttempts = 0;
      const mockAuth = {
        getToken: jest.fn().mockImplementation(async () => {
          loginAttempts++;
          if (loginAttempts <= 5) {
            throw new Error('Invalid credentials');
          } else {
            throw new Error('Too many failed attempts. Please wait.');
          }
        }),
        userId: null,
        isSignedIn: false
      };
      
      (useAuth as jest.Mock).mockReturnValue(mockAuth);
      
      render(<PicksFeedPage />);
      
      // Multiple failed login attempts should be blocked
      await waitFor(() => {
        expect(mockAuth.getToken).toHaveBeenCalled();
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringMatching(/too many failed attempts/i),
          expect.any(Error)
        );
      });
    });
  });

  describe('Secure Headers', () => {
    it('should set appropriate security headers', () => {
      // This test documents expected security headers
      // In a real implementation, these would be set by the server
      
      const expectedHeaders = {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
      };
      
      // Verify headers are documented for server implementation
      Object.entries(expectedHeaders).forEach(([header, value]) => {
        expect(typeof header).toBe('string');
        expect(typeof value).toBe('string');
        expect(header.length).toBeGreaterThan(0);
        expect(value.length).toBeGreaterThan(0);
      });
    });
  });
});