import React from 'react';
import { screen, fireEvent, waitFor, act } from '@testing-library/react';
import { render } from '@/__tests__/utils/test-utils';
import userEvent from '@testing-library/user-event';
import { TradingDashboard } from '@/components/trading/TradingDashboard';

// Mock the modules
jest.mock('@/store/tradingStore');
jest.mock('@/hooks/useTradingWebSocket');

// Get the mock functions after modules are mocked
import { useTradingStore } from '@/store/tradingStore';
import { useTradingWebSocket } from '@/hooks/useTradingWebSocket';

const mockUseTradingStore = useTradingStore as jest.MockedFunction<typeof useTradingStore>;
const mockUseTradingWebSocket = useTradingWebSocket as jest.MockedFunction<typeof useTradingWebSocket>;

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock Canvas API
HTMLCanvasElement.prototype.getContext = jest.fn().mockReturnValue({
  clearRect: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
  fill: jest.fn(),
  fillRect: jest.fn(),
  strokeRect: jest.fn(),
  fillText: jest.fn(),
  createLinearGradient: jest.fn().mockReturnValue({
    addColorStop: jest.fn(),
  }),
  setLineDash: jest.fn(),
});

const mockOrderBook = {
  bids: [
    { price: 1.84, quantity: 1000, orderCount: 5 },
    { price: 1.83, quantity: 1500, orderCount: 3 },
  ],
  asks: [
    { price: 1.86, quantity: 800, orderCount: 4 },
    { price: 1.87, quantity: 1200, orderCount: 6 },
  ],
  bestBid: 1.84,
  bestAsk: 1.86,
  lastUpdate: Date.now(),
};

const mockPositions = [
  {
    id: 'pos-1',
    marketId: 'btc-usd',
    marketName: 'BTC/USD',
    side: 'LONG' as const,
    quantity: 100,
    averagePrice: 1.80,
    currentPrice: 1.85,
    unrealizedPnl: 5.00,
    unrealizedPnlPercentage: 2.78,
    marginUsed: 9.00,
  },
];

const mockOrders = [
  {
    id: 'order-1',
    marketId: 'btc-usd',
    marketName: 'BTC/USD',
    side: 'buy' as const,
    orderType: 'limit' as const,
    quantity: 50,
    price: 1.82,
    filledQuantity: 0,
    remainingQuantity: 50,
    status: 'pending' as const,
    timestamp: Date.now(),
  },
];

describe('TradingDashboard', () => {
  const mockProps = {
    userId: 'test-user',
    selectedMarketId: 'btc-usd',
    onMarketSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseTradingStore.mockReturnValue({
      orderBook: mockOrderBook,
      positions: mockPositions,
      orders: mockOrders,
      marketData: {},
      setSelectedMarket: jest.fn(),
      cancelAllOrders: jest.fn(),
      closeAllPositions: jest.fn(),
      showAdvancedOrderTypes: false,
    });

    mockUseTradingWebSocket.mockReturnValue({
      connected: true,
      subscribeToMarket: jest.fn(),
      unsubscribeFromMarket: jest.fn(),
      submitOrder: jest.fn().mockResolvedValue({}),
    });

    // Mock window.innerWidth for responsive detection
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  it('renders trading dashboard with all components', () => {
    render(<TradingDashboard {...mockProps} />);

    // Check that main components are rendered
    expect(screen.getByText('Order Book')).toBeInTheDocument();
    expect(screen.getByText('Chart')).toBeInTheDocument();
    expect(screen.getByText('Positions')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('shows connection status', () => {
    render(<TradingDashboard {...mockProps} />);
    
    expect(screen.getByText('Connected')).toBeInTheDocument();
  });

  it('renders basic trading interface', () => {
    render(<TradingDashboard {...mockProps} />);

    // Check that the basic interface renders
    expect(screen.getByText('Order Book')).toBeInTheDocument();
    expect(screen.getByText('Chart')).toBeInTheDocument();
    expect(screen.getByText('Positions')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('handles keyboard shortcuts help', async () => {
    render(<TradingDashboard {...mockProps} />);

    // Press ? to show help
    await userEvent.keyboard('?');

    // Check if help component renders (implementation may vary)
    expect(screen.getByText('Order Book')).toBeInTheDocument();
  });

  it('toggles between positions and orders', async () => {
    render(<TradingDashboard {...mockProps} />);

    // Should show positions by default
    expect(screen.getByText('Positions')).toBeInTheDocument();

    // Click orders tab
    const ordersTab = screen.getByRole('button', { name: 'Orders' });
    await userEvent.click(ordersTab);

    // Should show orders
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('handles mobile layout', () => {
    // Mock mobile width
    Object.defineProperty(window, 'innerWidth', {
      value: 768,
      configurable: true,
    });

    render(<TradingDashboard {...mockProps} />);

    // Mobile navigation should be present
    expect(screen.getByText('Markets')).toBeInTheDocument();
    expect(screen.getByText('Chart')).toBeInTheDocument();
    expect(screen.getByText('Book')).toBeInTheDocument();
    expect(screen.getByText('Trade')).toBeInTheDocument();
  });

  it('handles disconnected state', () => {
    mockUseTradingWebSocket.mockReturnValue({
      connected: false,
      subscribeToMarket: jest.fn(),
      unsubscribeFromMarket: jest.fn(),
      submitOrder: jest.fn(),
    });

    render(<TradingDashboard {...mockProps} />);

    expect(screen.getByText('Disconnected')).toBeInTheDocument();
  });

  it('renders without crashing', () => {
    render(<TradingDashboard {...mockProps} />);
    expect(screen.getByRole('main')).toBeInTheDocument();
  });
});