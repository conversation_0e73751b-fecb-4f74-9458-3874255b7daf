import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { OrderEntry } from '@/components/trading/OrderEntry';
import { useTradingStore } from '@/store/tradingStore';

jest.mock('@/store/tradingStore');

const mockUseTradingStore = useTradingStore as jest.MockedFunction<typeof useTradingStore>;

const mockOrderBook = {
  bids: [{ price: 1.84, quantity: 1000, orderCount: 5 }],
  asks: [{ price: 1.86, quantity: 800, orderCount: 4 }],
  bestBid: 1.84,
  bestAsk: 1.86,
  lastUpdate: Date.now(),
};

describe('OrderEntry', () => {
  const mockProps = {
    onSubmit: jest.fn(),
    marketId: 'btc-usd',
    disabled: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseTradingStore.mockReturnValue({
      orderBook: mockOrderBook,
      showAdvancedOrderTypes: false,
    });

    // Mock usePortfolioSummary
    (useTradingStore as any).usePortfolioSummary = jest.fn().mockReturnValue({
      availableBalance: 1000,
      totalMarginUsed: 200,
    });
  });

  it('renders order entry form', () => {
    render(<OrderEntry {...mockProps} />);

    expect(screen.getByText('Order Type')).toBeInTheDocument();
    expect(screen.getByText('Quantity')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /BUY LIMIT/i })).toBeInTheDocument();
  });

  it('renders quick market order buttons', () => {
    render(<OrderEntry {...mockProps} />);

    expect(screen.getByText('BUY MARKET')).toBeInTheDocument();
    expect(screen.getByText('SELL MARKET')).toBeInTheDocument();
    expect(screen.getByText('@1.86')).toBeInTheDocument(); // Ask price
    expect(screen.getByText('@1.84')).toBeInTheDocument(); // Bid price
  });

  it('submits market buy order', async () => {
    render(<OrderEntry {...mockProps} />);

    const buyMarketButton = screen.getByText('BUY MARKET');
    await userEvent.click(buyMarketButton);

    expect(mockProps.onSubmit).toHaveBeenCalledWith({
      side: 'buy',
      order_type: 'market',
      quantity: '10',
      time_in_force: 'IOC',
    });
  });

  it('submits limit order with custom values', async () => {
    render(<OrderEntry {...mockProps} />);

    // Fill in quantity
    const quantityInput = screen.getByPlaceholderText('0.00');
    await userEvent.clear(quantityInput);
    await userEvent.type(quantityInput, '25');

    // Fill in price
    const priceInput = screen.getAllByPlaceholderText('0.00')[1];
    await userEvent.clear(priceInput);
    await userEvent.type(priceInput, '1.83');

    // Submit order
    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    await userEvent.click(submitButton);

    expect(mockProps.onSubmit).toHaveBeenCalledWith({
      side: 'buy',
      order_type: 'limit',
      quantity: '25',
      price: '1.83',
      time_in_force: 'GTC',
    });
  });

  it('switches between buy and sell', async () => {
    render(<OrderEntry {...mockProps} />);

    const sellTab = screen.getByText('Sell');
    await userEvent.click(sellTab);

    expect(screen.getByRole('button', { name: /SELL LIMIT/i })).toBeInTheDocument();
  });

  it('validates form inputs', async () => {
    render(<OrderEntry {...mockProps} />);

    // Try to submit without quantity
    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    await userEvent.click(submitButton);

    expect(screen.getByText('Quantity must be greater than 0')).toBeInTheDocument();
  });

  it('validates sufficient balance', async () => {
    // Mock insufficient balance
    (useTradingStore as any).usePortfolioSummary = jest.fn().mockReturnValue({
      availableBalance: 10,
      totalMarginUsed: 200,
    });

    render(<OrderEntry {...mockProps} />);

    // Enter large quantity
    const quantityInput = screen.getByPlaceholderText('0.00');
    await userEvent.type(quantityInput, '1000');

    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    await userEvent.click(submitButton);

    expect(screen.getByText('Insufficient balance')).toBeInTheDocument();
  });

  it('uses percentage buttons for quantity', async () => {
    render(<OrderEntry {...mockProps} />);

    const percent50Button = screen.getByText('50%');
    await userEvent.click(percent50Button);

    const quantityInput = screen.getByPlaceholderText('0.00');
    // Should calculate 50% of available balance / price
    expect(quantityInput).toHaveValue(270.27); // (1000 * 0.5) / 1.84
  });

  it('changes order type to market', async () => {
    render(<OrderEntry {...mockProps} />);

    const orderTypeSelect = screen.getByDisplayValue('Limit');
    await userEvent.click(orderTypeSelect);

    const marketOption = screen.getByText('Market');
    await userEvent.click(marketOption);

    // Price field should be hidden for market orders
    expect(screen.queryByText('Price')).not.toBeInTheDocument();
  });

  it('shows order calculations', () => {
    render(<OrderEntry {...mockProps} />);

    expect(screen.getByText('Estimated Value:')).toBeInTheDocument();
    expect(screen.getByText('Estimated Fees:')).toBeInTheDocument();
    expect(screen.getByText('Total Cost:')).toBeInTheDocument();
  });

  it('auto-fills price from order book', async () => {
    render(<OrderEntry {...mockProps} />);

    // Price should be auto-filled with best bid for buy orders
    const priceInput = screen.getAllByPlaceholderText('0.00')[1];
    expect(priceInput).toHaveValue(1.84);

    // Switch to sell
    const sellTab = screen.getByText('Sell');
    await userEvent.click(sellTab);

    // Price should update to best ask for sell orders
    await waitFor(() => {
      expect(priceInput).toHaveValue(1.86);
    });
  });

  it('handles advanced order types when enabled', () => {
    mockUseTradingStore.mockReturnValue({
      orderBook: mockOrderBook,
      showAdvancedOrderTypes: true,
    });

    render(<OrderEntry {...mockProps} />);

    const orderTypeSelect = screen.getByDisplayValue('Limit');
    fireEvent.click(orderTypeSelect);

    expect(screen.getByText('Stop Loss')).toBeInTheDocument();
    expect(screen.getByText('Take Profit')).toBeInTheDocument();
  });

  it('disables form when disabled prop is true', () => {
    render(<OrderEntry {...mockProps} disabled={true} />);

    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    expect(submitButton).toBeDisabled();

    const buyMarketButton = screen.getByText('BUY MARKET');
    expect(buyMarketButton).toBeDisabled();
  });

  it('shows message when no market selected', () => {
    render(<OrderEntry {...mockProps} marketId={null} />);

    expect(screen.getByText('Select a market to start trading')).toBeInTheDocument();
  });

  it('renders mobile layout correctly', () => {
    render(<OrderEntry {...mockProps} mobile={true} />);

    // Should have mobile-specific styling
    expect(screen.getByText('BUY MARKET')).toBeInTheDocument();
    expect(screen.getByText('SELL MARKET')).toBeInTheDocument();
  });

  it('resets form after successful submission', async () => {
    mockProps.onSubmit.mockResolvedValue(undefined);

    render(<OrderEntry {...mockProps} />);

    const quantityInput = screen.getByPlaceholderText('0.00');
    await userEvent.type(quantityInput, '25');

    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(quantityInput).toHaveValue(null);
    });
  });

  it('handles submission errors', async () => {
    mockProps.onSubmit.mockRejectedValue(new Error('Order failed'));

    render(<OrderEntry {...mockProps} />);

    const quantityInput = screen.getByPlaceholderText('0.00');
    await userEvent.type(quantityInput, '25');

    const submitButton = screen.getByRole('button', { name: /BUY LIMIT/i });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Order failed')).toBeInTheDocument();
    });
  });
});