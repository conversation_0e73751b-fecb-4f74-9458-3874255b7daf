/**
 * BetBet Gaming Engine - Real Game Store Tests
 * ===========================================
 * Testing the actual Zustand store implementation
 */

import { renderHook, act } from '@testing-library/react';
import { useGameStore } from '@/store/gameStore';
import { createMockUser, createMockGame, createMockSession } from '../utils/simple-test-utils';

// Don't mock the store for these tests - test the real implementation
jest.unmock('@/store/gameStore');

describe('Real Game Store Implementation', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { reset } = useGameStore.getState();
    act(() => {
      reset();
    });
  });

  describe('Authentication State Management', () => {
    it('has correct initial authentication state', () => {
      const { result } = renderHook(() => useGameStore());
      
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });

    it('updates state correctly on login', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();
      const mockToken = 'test-jwt-token';

      act(() => {
        result.current.login(mockUser, mockToken);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe(mockToken);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });

    it('clears state correctly on logout', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();

      // First login
      act(() => {
        result.current.login(mockUser, 'token');
      });

      // Verify logged in
      expect(result.current.isAuthenticated).toBe(true);

      // Then logout
      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('updates user profile correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser({ username: 'oldname', balance: 100 });

      act(() => {
        result.current.login(mockUser, 'token');
      });

      const updates = { username: 'newname', balance: 200 };
      
      act(() => {
        result.current.updateUser(updates);
      });

      expect(result.current.user?.username).toBe('newname');
      expect(result.current.user?.balance).toBe(200);
      expect(result.current.user?.id).toBe(mockUser.id); // Other fields preserved
    });

    it('manages loading state correctly', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Gaming State Management', () => {
    it('has correct initial gaming state', () => {
      const { result } = renderHook(() => useGameStore());
      
      expect(result.current.currentSession).toBeNull();
      expect(result.current.currentGameState).toBeNull();
      expect(result.current.participants).toEqual([]);
      expect(result.current.isConnected).toBe(false);
      expect(result.current.connectionError).toBeNull();
      expect(result.current.isSpectatorMode).toBe(false);
      expect(result.current.games).toEqual([]);
      expect(result.current.sessions).toEqual([]);
      expect(result.current.featuredGames).toEqual([]);
    });

    it('manages current session correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const mockSession = createMockSession();

      act(() => {
        result.current.setCurrentSession(mockSession);
      });

      expect(result.current.currentSession).toEqual(mockSession);
    });

    it('manages connection state correctly', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setConnected(true);
      });

      expect(result.current.isConnected).toBe(true);

      act(() => {
        result.current.setConnected(false);
      });

      expect(result.current.isConnected).toBe(false);
    });

    it('manages connection errors correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const errorMessage = 'WebSocket connection failed';

      act(() => {
        result.current.setConnectionError(errorMessage);
      });

      expect(result.current.connectionError).toBe(errorMessage);

      act(() => {
        result.current.setConnectionError(null);
      });

      expect(result.current.connectionError).toBeNull();
    });

    it('manages games list correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const mockGames = [
        createMockGame({ id: 'game-1', name: 'Game One' }),
        createMockGame({ id: 'game-2', name: 'Game Two' })
      ];

      act(() => {
        result.current.setGames(mockGames);
      });

      expect(result.current.games).toEqual(mockGames);
      expect(result.current.games).toHaveLength(2);
    });

    it('manages sessions list correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const mockSessions = [
        createMockSession({ id: 'session-1', session_name: 'Session One' }),
        createMockSession({ id: 'session-2', session_name: 'Session Two' })
      ];

      act(() => {
        result.current.setSessions(mockSessions);
      });

      expect(result.current.sessions).toEqual(mockSessions);
      expect(result.current.sessions).toHaveLength(2);
    });

    it('adds new session to existing list', () => {
      const { result } = renderHook(() => useGameStore());
      const existingSessions = [createMockSession({ id: 'session-1' })];
      const newSession = createMockSession({ id: 'session-2' });

      act(() => {
        result.current.setSessions(existingSessions);
      });

      act(() => {
        result.current.addSession(newSession);
      });

      expect(result.current.sessions).toHaveLength(2);
      expect(result.current.sessions).toContainEqual(newSession);
    });

    it('updates existing session correctly', () => {
      const { result } = renderHook(() => useGameStore());
      const session = createMockSession({ 
        id: 'session-1', 
        session_name: 'Original Name',
        status: 'waiting'
      });

      act(() => {
        result.current.setSessions([session]);
      });

      const updates = { 
        session_name: 'Updated Name',
        status: 'active' as const
      };

      act(() => {
        result.current.updateSession(session.id, updates);
      });

      expect(result.current.sessions[0].session_name).toBe('Updated Name');
      expect(result.current.sessions[0].status).toBe('active');
      expect(result.current.sessions[0].id).toBe('session-1'); // ID preserved
    });

    it('manages loading states correctly', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setGamesLoading(true);
        result.current.setSessionsLoading(true);
        result.current.setSessionLoading(true);
      });

      expect(result.current.gamesLoading).toBe(true);
      expect(result.current.sessionsLoading).toBe(true);
      expect(result.current.sessionLoading).toBe(true);

      act(() => {
        result.current.setGamesLoading(false);
        result.current.setSessionsLoading(false);
        result.current.setSessionLoading(false);
      });

      expect(result.current.gamesLoading).toBe(false);
      expect(result.current.sessionsLoading).toBe(false);
      expect(result.current.sessionLoading).toBe(false);
    });
  });

  describe('Advanced State Operations', () => {
    it('resets game state while preserving auth', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();
      const mockSession = createMockSession();

      // Set up both auth and game state
      act(() => {
        result.current.login(mockUser, 'token');
        result.current.setCurrentSession(mockSession);
        result.current.setConnected(true);
        result.current.setSpectatorMode(true);
      });

      // Reset only game state
      act(() => {
        result.current.resetGameState();
      });

      // Auth should be preserved
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockUser);
      
      // Game state should be reset
      expect(result.current.currentSession).toBeNull();
      expect(result.current.isConnected).toBe(false);
      expect(result.current.isSpectatorMode).toBe(false);
    });

    it('resets entire store completely', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();
      const mockGames = [createMockGame()];

      // Set up all state
      act(() => {
        result.current.login(mockUser, 'token');
        result.current.setGames(mockGames);
        result.current.setCurrentSession(createMockSession());
        result.current.setConnected(true);
      });

      // Reset everything
      act(() => {
        result.current.reset();
      });

      // Everything should be reset
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.currentSession).toBeNull();
      expect(result.current.games).toEqual([]);
      expect(result.current.isConnected).toBe(false);
    });

    it('manages spectator mode correctly', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setSpectatorMode(true);
      });

      expect(result.current.isSpectatorMode).toBe(true);

      act(() => {
        result.current.setSpectatorMode(false);
      });

      expect(result.current.isSpectatorMode).toBe(false);
    });

    it('manages chat state correctly', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setChatOpen(true);
      });

      expect(result.current.isChatOpen).toBe(true);

      act(() => {
        result.current.setChatOpen(false);
      });

      expect(result.current.isChatOpen).toBe(false);
    });
  });

  describe('Store Selectors and Computed Values', () => {
    it('correctly computes isAuthenticated based on user and token', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();

      // Should be false initially
      expect(result.current.isAuthenticated).toBe(false);

      // Should be true when both user and token are present
      act(() => {
        result.current.login(mockUser, 'valid-token');
      });

      expect(result.current.isAuthenticated).toBe(true);

      // Should be false when token is cleared
      act(() => {
        result.current.logout();
      });

      expect(result.current.isAuthenticated).toBe(false);
    });

    it('maintains referential stability for arrays when unchanged', () => {
      const { result } = renderHook(() => useGameStore());
      
      const initialGames = result.current.games;
      const initialSessions = result.current.sessions;
      
      // Re-render without changes
      const { result: result2 } = renderHook(() => useGameStore());
      
      expect(result2.current.games).toBe(initialGames);
      expect(result2.current.sessions).toBe(initialSessions);
    });
  });
});