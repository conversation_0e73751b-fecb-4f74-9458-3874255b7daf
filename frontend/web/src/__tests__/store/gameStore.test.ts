/**
 * BetBet Gaming Engine - Game Store Tests
 * ======================================
 */

import { renderHook, act } from '@testing-library/react';

// Import the actual store for testing
jest.unmock('@/store/gameStore');
import { useGameStore } from '@/store/gameStore';
import { createMockUser, createMockGame, createMockSession } from '../utils/test-utils';

describe('Game Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useGameStore.getState().reset();
  });

  describe('Authentication State', () => {
    it('has correct initial auth state', () => {
      const { result } = renderHook(() => useGameStore());
      
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });

    it('updates auth state on login', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();
      const mockToken = 'test-token';

      act(() => {
        result.current.login(mockUser, mockToken);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe(mockToken);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });

    it('clears auth state on logout', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();

      // First login
      act(() => {
        result.current.login(mockUser, 'token');
      });

      // Then logout
      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('updates user profile', () => {
      const { result } = renderHook(() => useGameStore());
      const mockUser = createMockUser();

      act(() => {
        result.current.login(mockUser, 'token');
      });

      const updates = { username: 'newusername', balance: 200 };
      
      act(() => {
        result.current.updateUser(updates);
      });

      expect(result.current.user?.username).toBe('newusername');
      expect(result.current.user?.balance).toBe(200);
    });

    it('sets loading state', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Gaming State', () => {
    it('has correct initial gaming state', () => {
      const { result } = renderHook(() => useGameStore());
      
      expect(result.current.currentSession).toBeNull();
      expect(result.current.currentGameState).toBeNull();
      expect(result.current.participants).toEqual([]);
      expect(result.current.isConnected).toBe(false);
      expect(result.current.connectionError).toBeNull();
      expect(result.current.isSpectatorMode).toBe(false);
      expect(result.current.games).toEqual([]);
      expect(result.current.sessions).toEqual([]);
      expect(result.current.featuredGames).toEqual([]);
    });

    it('sets current session', () => {
      const { result } = renderHook(() => useGameStore());
      const mockSession = createMockSession();

      act(() => {
        result.current.setCurrentSession(mockSession);
      });

      expect(result.current.currentSession).toEqual(mockSession);
    });

    it('sets connection state', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setConnected(true);
      });

      expect(result.current.isConnected).toBe(true);

      act(() => {
        result.current.setConnected(false);
      });

      expect(result.current.isConnected).toBe(false);
    });

    it('sets connection error', () => {
      const { result } = renderHook(() => useGameStore());
      const errorMessage = 'Connection failed';

      act(() => {
        result.current.setConnectionError(errorMessage);
      });

      expect(result.current.connectionError).toBe(errorMessage);
    });

    it('manages games list', () => {
      const { result } = renderHook(() => useGameStore());
      const mockGames = [createMockGame(), createMockGame({ id: 'game-2' })];

      act(() => {
        result.current.setGames(mockGames);
      });

      expect(result.current.games).toEqual(mockGames);
    });

    it('manages sessions list', () => {
      const { result } = renderHook(() => useGameStore());
      const mockSessions = [createMockSession(), createMockSession({ id: 'session-2' })];

      act(() => {
        result.current.setSessions(mockSessions);
      });

      expect(result.current.sessions).toEqual(mockSessions);
    });

    it('adds new session to list', () => {
      const { result } = renderHook(() => useGameStore());
      const existingSessions = [createMockSession()];
      const newSession = createMockSession({ id: 'session-2' });

      act(() => {
        result.current.setSessions(existingSessions);
      });

      act(() => {
        result.current.addSession(newSession);
      });

      expect(result.current.sessions).toHaveLength(2);
      expect(result.current.sessions).toContain(newSession);
    });

    it('updates existing session', () => {
      const { result } = renderHook(() => useGameStore());
      const session = createMockSession({ session_name: 'Original Name' });

      act(() => {
        result.current.setSessions([session]);
      });

      act(() => {
        result.current.updateSession(session.id, { session_name: 'Updated Name' });
      });

      expect(result.current.sessions[0].session_name).toBe('Updated Name');
    });

    it('sets loading states', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.setGamesLoading(true);
        result.current.setSessionsLoading(true);
        result.current.setSessionLoading(true);
      });

      expect(result.current.gamesLoading).toBe(true);
      expect(result.current.sessionsLoading).toBe(true);
      expect(result.current.sessionLoading).toBe(true);
    });

    it('resets game state', () => {
      const { result } = renderHook(() => useGameStore());

      // Set some state
      act(() => {
        result.current.setCurrentSession(createMockSession());
        result.current.setConnected(true);
        result.current.setSpectatorMode(true);
      });

      // Reset game state
      act(() => {
        result.current.resetGameState();
      });

      expect(result.current.currentSession).toBeNull();
      expect(result.current.isConnected).toBe(false);
      expect(result.current.isSpectatorMode).toBe(false);
    });

    it('resets entire store', () => {
      const { result } = renderHook(() => useGameStore());

      // Set some state
      act(() => {
        result.current.login(createMockUser(), 'token');
        result.current.setCurrentSession(createMockSession());
        result.current.setGames([createMockGame()]);
      });

      // Reset everything
      act(() => {
        result.current.reset();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.currentSession).toBeNull();
      expect(result.current.games).toEqual([]);
    });
  });
});