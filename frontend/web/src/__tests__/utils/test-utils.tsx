/**
 * BetBet Gaming Engine - Test Utilities
 * =====================================
 * 
 * Custom test utilities and providers for testing React components.
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
// Import the mock store
import { mockStore, useGameStore } from '@/store/gameStore';

// Re-export mockStore from the imported mock
const originalMockStore = {
  // Auth state
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  
  // Gaming state
  currentSession: null,
  currentGameState: null,
  participants: [],
  isConnected: false,
  connectionError: null,
  isChatOpen: false,
  isSpectatorMode: false,
  selectedGame: null,
  games: [],
  sessions: [],
  featuredGames: [],
  gamesLoading: false,
  sessionsLoading: false,
  sessionLoading: false,
  
  // Actions (mocked)
  login: jest.fn(),
  logout: jest.fn(),
  updateUser: jest.fn(),
  setLoading: jest.fn(),
  setCurrentSession: jest.fn(),
  setCurrentGameState: jest.fn(),
  setParticipants: jest.fn(),
  updateParticipant: jest.fn(),
  setConnected: jest.fn(),
  setConnectionError: jest.fn(),
  setChatOpen: jest.fn(),
  setSpectatorMode: jest.fn(),
  setSelectedGame: jest.fn(),
  setGames: jest.fn(),
  setSessions: jest.fn(),
  setFeaturedGames: jest.fn(),
  addSession: jest.fn(),
  updateSession: jest.fn(),
  setGamesLoading: jest.fn(),
  setSessionsLoading: jest.fn(),
  setSessionLoading: jest.fn(),
  reset: jest.fn(),
  resetGameState: jest.fn(),
};

// Mock the store hook
jest.mock('@/store/gameStore', () => ({
  useGameStore: () => mockStore,
  useAuth: () => ({
    user: mockStore.user,
    token: mockStore.token,
    isAuthenticated: mockStore.isAuthenticated,
    isLoading: mockStore.isLoading,
    login: mockStore.login,
    logout: mockStore.logout,
    updateUser: mockStore.updateUser,
    setLoading: mockStore.setLoading,
  }),
  useGames: () => ({
    games: mockStore.games,
    sessions: mockStore.sessions,
    featuredGames: mockStore.featuredGames,
    gamesLoading: mockStore.gamesLoading,
    sessionsLoading: mockStore.sessionsLoading,
    setGames: mockStore.setGames,
    setSessions: mockStore.setSessions,
    setFeaturedGames: mockStore.setFeaturedGames,
    setGamesLoading: mockStore.setGamesLoading,
    setSessionsLoading: mockStore.setSessionsLoading,
  }),
  useCurrentSession: () => ({
    session: mockStore.currentSession,
    gameState: mockStore.currentGameState,
    participants: mockStore.participants,
    isConnected: mockStore.isConnected,
    connectionError: mockStore.connectionError,
    isSpectatorMode: mockStore.isSpectatorMode,
    setCurrentSession: mockStore.setCurrentSession,
    setCurrentGameState: mockStore.setCurrentGameState,
    setParticipants: mockStore.setParticipants,
    setConnected: mockStore.setConnected,
    setConnectionError: mockStore.setConnectionError,
    setSpectatorMode: mockStore.setSpectatorMode,
  }),
}));

// Import notification context
import { NotificationProvider } from '@/contexts/NotificationContext';

// Mock the trading store completely
jest.mock('@/store/tradingStore', () => ({
  useTradingStore: jest.fn(() => ({
    orderBook: {
      marketId: null,
      bids: [],
      asks: [],
      bestBid: null,
      bestAsk: null,
      spread: null,
      timestamp: new Date().toISOString()
    },
    positions: [],
    orders: [],
    marketData: {},
    setSelectedMarket: jest.fn(),
    cancelAllOrders: jest.fn(),
    closeAllPositions: jest.fn(),
    showAdvancedOrderTypes: false,
    setConnected: jest.fn(),
    updateOrderBook: jest.fn(),
    updateOrders: jest.fn(),
    updatePositions: jest.fn(),
    addOrder: jest.fn(),
    updateOrder: jest.fn(),
    updateMarketTickers: jest.fn()
  })),
  usePortfolioSummary: () => ({
    totalValue: 10000,
    totalPnl: 250,
    totalPnlPercentage: 2.5,
    marginUsed: 2500,
    availableMargin: 7500,
    availableBalance: 5000, // Add missing availableBalance
    totalMarginUsed: 2500,  // Add missing totalMarginUsed
    positions: [],
    loading: false
  })
}));

// Mock the trading WebSocket hook
jest.mock('@/hooks/useTradingWebSocket', () => ({
  useTradingWebSocket: jest.fn(() => ({
    connected: true,
    connecting: false,
    error: null,
    subscribeToMarket: jest.fn(),
    unsubscribeFromMarket: jest.fn(),
    submitOrder: jest.fn().mockResolvedValue({ id: 'test-order' }),
    cancelOrder: jest.fn().mockResolvedValue({}),
    sendMessage: jest.fn()
  }))
}));

// Mock accessibility hooks
jest.mock('@/hooks/useAccessibility', () => ({
  useScreenReader: () => ({
    announce: jest.fn(),
    announceOrder: jest.fn(),
    announcePrice: jest.fn()
  }),
  useReducedMotion: () => false
}));

// Mock keyboard shortcuts hooks  
jest.mock('@/hooks/useKeyboardShortcuts', () => ({
  useTradingKeyboardShortcuts: jest.fn(),
  useKeyboardShortcuts: jest.fn(),
  useShortcutHelp: () => [
    { key: 'B', description: 'Buy Market Order' },
    { key: 'S', description: 'Sell Market Order' },
    { key: 'Q', description: 'Focus Quantity Input' },
    { key: '?', description: 'Show Help' }
  ]
}));

// Custom render function with all providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <NotificationProvider>
      {children}
    </NotificationProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  balance: 100,
  avatar_url: '',
  is_active: true,
  is_verified: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockGame = (overrides = {}) => ({
  id: 'game-1',
  name: 'Test Game',
  slug: 'test-game',
  category: 'trivia',
  description: 'A test game',
  min_players: 2,
  max_players: 8,
  min_skill_level: 1,
  estimated_duration_minutes: 15,
  scoring_system: 'points',
  has_spectator_betting: true,
  allows_practice_mode: false,
  is_active: true,
  is_featured: false,
  is_beta: false,
  total_sessions_played: 100,
  popularity_score: 7.5,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockSession = (overrides = {}) => ({
  id: 'session-1',
  game_id: 'game-1',
  session_name: 'Test Session',
  status: 'waiting' as const,
  entry_fee: 10,
  min_participants: 2,
  max_participants: 8,
  current_participants: 2,
  current_spectators: 5,
  estimated_duration_minutes: 15,
  total_prize_pool: 100,
  allow_spectators: true,
  spectator_chat_enabled: true,
  is_ranked: true,
  is_tournament_session: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockParticipant = (overrides = {}) => ({
  id: 'participant-1',
  user_id: 'user-1',
  session_id: 'session-1',
  status: 'ready' as const,
  score: 0,
  is_ready: true,
  is_spectator: false,
  connection_id: 'conn-1',
  join_order: 1,
  current_score: 0,
  ...overrides,
});

// Mock store update utilities
export const updateMockStore = (updates: Partial<typeof mockStore>) => {
  Object.assign(mockStore, updates);
};

export const resetMockStore = () => {
  Object.assign(mockStore, {
    user: null,
    token: null,
    isAuthenticated: false,
    currentSession: null,
    games: [],
    sessions: [],
    featuredGames: [],
  });
  
  // Reset all jest mocks
  Object.values(mockStore).forEach(value => {
    if (jest.isMockFunction(value)) {
      value.mockClear();
    }
  });
};

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };
export { mockStore };