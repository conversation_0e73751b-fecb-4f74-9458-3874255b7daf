/**
 * BetBet Gaming Engine - Simple Test Utilities
 * ============================================
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';

// Custom render function
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  balance: 100,
  avatar_url: '',
  is_active: true,
  is_verified: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockGame = (overrides = {}) => ({
  id: 'game-1',
  name: 'Test Game',
  slug: 'test-game',
  category: 'trivia',
  description: 'A test game',
  min_players: 2,
  max_players: 8,
  min_skill_level: 1,
  estimated_duration_minutes: 15,
  scoring_system: 'points',
  has_spectator_betting: true,
  allows_practice_mode: false,
  is_active: true,
  is_featured: false,
  is_beta: false,
  total_sessions_played: 100,
  popularity_score: 7.5,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockSession = (overrides = {}) => ({
  id: 'session-1',
  game_id: 'game-1',
  session_name: 'Test Session',
  status: 'waiting' as const,
  entry_fee: 10,
  min_participants: 2,
  max_participants: 8,
  current_participants: 2,
  current_spectators: 5,
  estimated_duration_minutes: 15,
  total_prize_pool: 100,
  allow_spectators: true,
  spectator_chat_enabled: true,
  is_ranked: true,
  is_tournament_session: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockParticipant = (overrides = {}) => ({
  id: 'participant-1',
  user_id: 'user-1',
  session_id: 'session-1',
  status: 'ready' as const,
  score: 0,
  is_ready: true,
  is_spectator: false,
  connection_id: 'conn-1',
  join_order: 1,
  current_score: 0,
  ...overrides,
});

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };