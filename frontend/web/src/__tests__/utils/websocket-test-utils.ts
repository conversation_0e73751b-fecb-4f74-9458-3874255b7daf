/**
 * Custom Betting Platform - WebSocket Testing Utilities
 * =====================================================
 * 
 * Utilities for testing WebSocket functionality in Custom Betting Platform components.
 */

export class MockWebSocket extends EventTarget {
  public readyState: number;
  public url: string;
  private messageQueue: string[] = [];
  private closeHandlers: ((event: CloseEvent) => void)[] = [];
  private errorHandlers: ((event: Event) => void)[] = [];
  private openHandlers: ((event: Event) => void)[] = [];

  constructor(url: string, protocols?: string | string[]) {
    super();
    this.url = url;
    this.readyState = MockWebSocket.CONNECTING;

    // Simulate connection after a brief delay
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      const event = new Event('open');
      this.dispatchEvent(event);
      this.openHandlers.forEach(handler => handler(event));
      
      // Process any queued messages
      this.processMessageQueue();
    }, 100);
  }

  // WebSocket readyState constants
  static readonly CONNECTING = 0;
  static readonly OPEN = 1;
  static readonly CLOSING = 2;
  static readonly CLOSED = 3;

  send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }

    // Echo the message back for testing
    setTimeout(() => {
      const event = new MessageEvent('message', { data });
      this.dispatchEvent(event);
    }, 50);
  }

  close(code?: number, reason?: string): void {
    this.readyState = MockWebSocket.CLOSING;
    
    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED;
      const event = new CloseEvent('close', { code: code || 1000, reason: reason || '' });
      this.dispatchEvent(event);
      this.closeHandlers.forEach(handler => handler(event));
    }, 50);
  }

  // Event handler setters (for compatibility with standard WebSocket API)
  set onopen(handler: ((event: Event) => void) | null) {
    if (handler) {
      this.openHandlers.push(handler);
    }
  }

  set onmessage(handler: ((event: MessageEvent) => void) | null) {
    if (handler) {
      this.addEventListener('message', handler);
    }
  }

  set onclose(handler: ((event: CloseEvent) => void) | null) {
    if (handler) {
      this.closeHandlers.push(handler);
    }
  }

  set onerror(handler: ((event: Event) => void) | null) {
    if (handler) {
      this.errorHandlers.push(handler);
    }
  }

  // Test utilities
  simulateMessage(data: any): void {
    if (this.readyState === MockWebSocket.OPEN) {
      const event = new MessageEvent('message', { data: JSON.stringify(data) });
      this.dispatchEvent(event);
    } else {
      // Queue message until connection is open
      this.messageQueue.push(JSON.stringify(data));
    }
  }

  simulateError(error?: string): void {
    const event = new ErrorEvent('error', { message: error || 'WebSocket error' });
    this.dispatchEvent(event);
    this.errorHandlers.forEach(handler => handler(event));
  }

  simulateClose(code: number = 1000, reason: string = ''): void {
    this.readyState = MockWebSocket.CLOSED;
    const event = new CloseEvent('close', { code, reason });
    this.dispatchEvent(event);
    this.closeHandlers.forEach(handler => handler(event));
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const data = this.messageQueue.shift();
      if (data) {
        const event = new MessageEvent('message', { data });
        this.dispatchEvent(event);
      }
    }
  }
}

// Mock WebSocket market update messages for Custom Betting Platform
export const mockWebSocketMessages = {
  // Market odds update
  oddsUpdate: (marketId: string, outcomeId: string, newOdds: number) => ({
    type: 'odds_update',
    market_id: marketId,
    outcome_id: outcomeId,
    new_odds: newOdds,
    timestamp: new Date().toISOString()
  }),

  // New participation in market
  newParticipation: (marketId: string, outcomeId: string, amount: number, positionType: 'backing' | 'laying') => ({
    type: 'new_participation',
    market_id: marketId,
    outcome_id: outcomeId,
    amount,
    position_type: positionType,
    timestamp: new Date().toISOString()
  }),

  // Market settlement
  marketSettled: (marketId: string, winningOutcomeId: string) => ({
    type: 'market_settled',
    market_id: marketId,
    winning_outcome_id: winningOutcomeId,
    timestamp: new Date().toISOString()
  }),

  // Market status change
  statusChange: (marketId: string, newStatus: string, reason?: string) => ({
    type: 'status_change',
    market_id: marketId,
    new_status: newStatus,
    reason,
    timestamp: new Date().toISOString()
  }),

  // User participation update
  participationUpdate: (participationId: string, status: string, matchedAmount?: number) => ({
    type: 'participation_update',
    participation_id: participationId,
    status,
    matched_amount: matchedAmount,
    timestamp: new Date().toISOString()
  }),

  // Dispute filed
  disputeFiled: (marketId: string, disputeId: string, reason: string) => ({
    type: 'dispute_filed',
    market_id: marketId,
    dispute_id: disputeId,
    reason,
    timestamp: new Date().toISOString()
  }),

  // Connection status
  connectionStatus: (status: 'connected' | 'disconnected' | 'error') => ({
    type: 'connection_status',
    status,
    timestamp: new Date().toISOString()
  }),

  // Heartbeat/ping message
  heartbeat: () => ({
    type: 'heartbeat',
    timestamp: new Date().toISOString()
  })
};

// WebSocket test utilities
export const webSocketTestUtils = {
  // Create a mock WebSocket instance
  createMockWebSocket: (url: string) => new MockWebSocket(url),

  // Setup WebSocket for testing with automatic message simulation
  setupAutoWebSocket: (marketId: string, interval: number = 2000) => {
    const ws = new MockWebSocket(`ws://localhost:8000/ws/custom-betting/market/${marketId}`);
    
    // Simulate periodic odds updates
    const oddsInterval = setInterval(() => {
      if (ws.readyState === MockWebSocket.OPEN) {
        ws.simulateMessage(
          mockWebSocketMessages.oddsUpdate(
            marketId,
            'outcome-1',
            2.0 + Math.random() * 2
          )
        );
      }
    }, interval);

    // Cleanup function
    const cleanup = () => {
      clearInterval(oddsInterval);
      if (ws.readyState === MockWebSocket.OPEN) {
        ws.close();
      }
    };

    return { ws, cleanup };
  },

  // Wait for WebSocket connection
  waitForConnection: (ws: MockWebSocket): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (ws.readyState === MockWebSocket.OPEN) {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 5000);

      ws.addEventListener('open', () => {
        clearTimeout(timeout);
        resolve();
      });

      ws.addEventListener('error', () => {
        clearTimeout(timeout);
        reject(new Error('WebSocket connection error'));
      });
    });
  },

  // Wait for specific message type
  waitForMessage: (ws: MockWebSocket, messageType: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout waiting for message type: ${messageType}`));
      }, 5000);

      const messageHandler = (event: MessageEvent) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === messageType) {
            clearTimeout(timeout);
            ws.removeEventListener('message', messageHandler);
            resolve(data);
          }
        } catch (error) {
          // Ignore parsing errors for non-JSON messages
        }
      };

      ws.addEventListener('message', messageHandler);
    });
  },

  // Simulate network issues
  simulateNetworkIssues: (ws: MockWebSocket, duration: number = 1000) => {
    ws.simulateClose(1006, 'Connection lost');
    
    setTimeout(() => {
      // Simulate reconnection
      const newWs = new MockWebSocket(ws.url);
      Object.setPrototypeOf(ws, Object.getPrototypeOf(newWs));
      Object.assign(ws, newWs);
    }, duration);
  },

  // Create test scenario with multiple messages
  createTestScenario: (ws: MockWebSocket, scenario: 'active_trading' | 'market_settlement' | 'dispute_resolution') => {
    switch (scenario) {
      case 'active_trading':
        // Simulate active trading with odds updates and new participations
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.oddsUpdate('market-1', 'outcome-1', 2.3)), 100);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.newParticipation('market-1', 'outcome-1', 100, 'backing')), 200);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.oddsUpdate('market-1', 'outcome-2', 1.7)), 300);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.newParticipation('market-1', 'outcome-2', 50, 'laying')), 400);
        break;

      case 'market_settlement':
        // Simulate market settlement process
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.statusChange('market-1', 'settling')), 100);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.marketSettled('market-1', 'outcome-1')), 200);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.participationUpdate('participation-1', 'won', 250)), 300);
        break;

      case 'dispute_resolution':
        // Simulate dispute filing and resolution
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.disputeFiled('market-1', 'dispute-1', 'incorrect_settlement')), 100);
        setTimeout(() => ws.simulateMessage(mockWebSocketMessages.statusChange('market-1', 'disputed', 'Settlement under review')), 200);
        break;
    }
  }
};

// React hook for testing WebSocket components
export const useWebSocketTestHook = (url: string) => {
  const ws = new MockWebSocket(url);
  
  return {
    ws,
    sendMessage: (message: any) => ws.send(JSON.stringify(message)),
    simulateMessage: (message: any) => ws.simulateMessage(message),
    close: () => ws.close(),
    readyState: ws.readyState
  };
};

// Global WebSocket mock for Jest
export const setupWebSocketMocks = () => {
  // Replace global WebSocket with mock
  (global as any).WebSocket = MockWebSocket;
  
  // Mock URL for testing
  (global as any).URL = class {
    constructor(public href: string) {}
  };
};

// Cleanup function for tests
export const cleanupWebSocketMocks = () => {
  delete (global as any).WebSocket;
  delete (global as any).URL;
};