/**
 * Custom Betting Platform - MSW API Handlers
 * ==========================================
 * 
 * Mock Service Worker handlers for Custom Betting Platform API endpoints.
 */

import { rest } from 'msw';
import {
  mockMarkets,
  mockBinaryMarket,
  mockUserParticipations,
  mockQueueStatus,
  mockDisputes,
  mockAnalytics,
  generateMockMarket,
  generateMockParticipation,
  mockUsers
} from './custom-betting-data';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const customBettingHandlers = [
  // Get all markets with filtering
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets`, (req, res, ctx) => {
    const category = req.url.searchParams.get('category');
    const status = req.url.searchParams.get('status');
    const search = req.url.searchParams.get('search');
    const limit = parseInt(req.url.searchParams.get('limit') || '10');
    const offset = parseInt(req.url.searchParams.get('offset') || '0');

    let filteredMarkets = [...mockMarkets];

    // Apply filters
    if (category && category !== 'all') {
      filteredMarkets = filteredMarkets.filter(market => market.category === category);
    }

    if (status && status !== 'all') {
      filteredMarkets = filteredMarkets.filter(market => market.status === status);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredMarkets = filteredMarkets.filter(market =>
        market.title.toLowerCase().includes(searchLower) ||
        market.description.toLowerCase().includes(searchLower)
      );
    }

    // Apply pagination
    const paginatedMarkets = filteredMarkets.slice(offset, offset + limit);

    return res(
      ctx.status(200),
      ctx.json({
        markets: paginatedMarkets,
        total: filteredMarkets.length,
        limit,
        offset,
        has_more: offset + limit < filteredMarkets.length
      })
    );
  }),

  // Get single market by ID
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const market = mockMarkets.find(m => m.id === id);
    
    if (!market) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Market not found' })
      );
    }

    return res(ctx.status(200), ctx.json(market));
  }),

  // Create new market
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/bets`, (req, res, ctx) => {
    const marketData = req.body as any;
    const newMarket = generateMockMarket({
      ...marketData,
      id: `market-${Date.now()}`,
      creator_user_id: mockUsers.user1,
      status: 'open',
      total_stakes: 0,
      total_participants: 0
    });

    // Add to mock markets array
    mockMarkets.push(newMarket);

    return res(ctx.status(201), ctx.json(newMarket));
  }),

  // Place bet
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/participate`, (req, res, ctx) => {
    const { id } = req.params;
    const betData = req.body as any;
    
    const market = mockMarkets.find(m => m.id === id);
    if (!market) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Market not found' })
      );
    }

    const participation = generateMockParticipation({
      bet_id: id as string,
      outcome_id: betData.outcome_id,
      position_type: betData.position_type,
      stake_amount: betData.stake_amount,
      desired_odds: betData.desired_odds,
      status: 'pending'
    });

    return res(ctx.status(201), ctx.json(participation));
  }),

  // Get user's bets
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/users/me/bets`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockUserParticipations));
  }),

  // Get user's created markets
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/users/me/created-bets`, (req, res, ctx) => {
    const userMarkets = mockMarkets.filter(m => m.creator_user_id === mockUsers.user1);
    return res(ctx.status(200), ctx.json(userMarkets));
  }),

  // Get user participation in specific market
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/participation`, (req, res, ctx) => {
    const { id } = req.params;
    const participation = mockUserParticipations.find(p => p.bet_id === id);
    
    if (!participation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'No participation found' })
      );
    }

    return res(ctx.status(200), ctx.json(participation));
  }),

  // Get market participants
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/participants`, (req, res, ctx) => {
    const { id } = req.params;
    const participants = mockUserParticipations.filter(p => p.bet_id === id);
    return res(ctx.status(200), ctx.json(participants));
  }),

  // Get queue status
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/queue-status`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockQueueStatus));
  }),

  // Cancel bet participation
  rest.delete(`${API_BASE_URL}/api/v1/custom-betting/participants/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const participantIndex = mockUserParticipations.findIndex(p => p.id === id);
    
    if (participantIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Participation not found' })
      );
    }

    // Update status to cancelled
    mockUserParticipations[participantIndex].status = 'cancelled';
    
    return res(ctx.status(200), ctx.json({ message: 'Participation cancelled' }));
  }),

  // Settle market (Admin)
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/settle`, (req, res, ctx) => {
    const { id } = req.params;
    const settlementData = req.body as any;
    
    const marketIndex = mockMarkets.findIndex(m => m.id === id);
    if (marketIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Market not found' })
      );
    }

    // Update market status
    mockMarkets[marketIndex].status = 'settled';
    mockMarkets[marketIndex].winning_outcome_id = settlementData.winning_outcome_id;
    
    // Update winning outcome
    const winningOutcomeIndex = mockMarkets[marketIndex].outcomes.findIndex(
      o => o.id === settlementData.winning_outcome_id
    );
    if (winningOutcomeIndex !== -1) {
      mockMarkets[marketIndex].outcomes[winningOutcomeIndex].is_winning_outcome = true;
    }

    return res(ctx.status(200), ctx.json({ message: 'Market settled successfully' }));
  }),

  // Admin: Get all markets
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/admin/bets`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockMarkets));
  }),

  // Admin: Suspend market
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/admin/bets/:id/suspend`, (req, res, ctx) => {
    const { id } = req.params;
    const marketIndex = mockMarkets.findIndex(m => m.id === id);
    
    if (marketIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Market not found' })
      );
    }

    mockMarkets[marketIndex].status = 'suspended';
    return res(ctx.status(200), ctx.json({ message: 'Market suspended' }));
  }),

  // Admin: Cancel market
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/admin/bets/:id/cancel`, (req, res, ctx) => {
    const { id } = req.params;
    const marketIndex = mockMarkets.findIndex(m => m.id === id);
    
    if (marketIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Market not found' })
      );
    }

    mockMarkets[marketIndex].status = 'cancelled';
    return res(ctx.status(200), ctx.json({ message: 'Market cancelled' }));
  }),

  // Disputes
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/disputes`, (req, res, ctx) => {
    const { id } = req.params;
    const marketDisputes = mockDisputes.filter(d => d.market_id === id);
    return res(ctx.status(200), ctx.json(marketDisputes));
  }),

  // Create dispute
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/disputes`, (req, res, ctx) => {
    const { id } = req.params;
    const disputeData = req.body as any;
    
    const newDispute = {
      id: `dispute-${Date.now()}`,
      market_id: id as string,
      disputer_user_id: mockUsers.user1,
      dispute_reason: disputeData.reason,
      dispute_details: disputeData.details,
      evidence_urls: disputeData.evidence_urls || [],
      status: 'pending',
      created_at: new Date().toISOString()
    };

    mockDisputes.push(newDispute);
    return res(ctx.status(201), ctx.json(newDispute));
  }),

  // Resolve dispute
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/disputes/:id/resolve`, (req, res, ctx) => {
    const { id } = req.params;
    const resolutionData = req.body as any;
    
    const disputeIndex = mockDisputes.findIndex(d => d.id === id);
    if (disputeIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Dispute not found' })
      );
    }

    // Update dispute
    mockDisputes[disputeIndex] = {
      ...mockDisputes[disputeIndex],
      status: resolutionData.action === 'accept' ? 'resolved' : 'rejected',
      resolution_notes: resolutionData.notes,
      resolved_at: new Date().toISOString(),
      admin_user_id: mockUsers.admin
    };

    return res(ctx.status(200), ctx.json(mockDisputes[disputeIndex]));
  }),

  // Analytics endpoint
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/analytics`, (req, res, ctx) => {
    const timeRange = req.url.searchParams.get('timeRange') || '30d';
    
    // Simulate different data based on time range
    const analyticsData = {
      ...mockAnalytics,
      timeRange,
      // Adjust data based on time range for realistic testing
      marketPerformance: {
        ...mockAnalytics.marketPerformance,
        totalVolume: timeRange === '7d' ? 50000 : 
                    timeRange === '90d' ? 500000 : 
                    timeRange === '1y' ? 2000000 : 
                    mockAnalytics.marketPerformance.totalVolume
      }
    };

    return res(ctx.status(200), ctx.json(analyticsData));
  }),

  // Error scenarios for testing error handling
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/error-test`, (req, res, ctx) => {
    const errorType = req.url.searchParams.get('type');
    
    switch (errorType) {
      case 'network':
        return res.networkError('Network error');
      case '500':
        return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
      case '401':
        return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }));
      case 'timeout':
        return res(ctx.delay('infinite'));
      default:
        return res(ctx.status(200), ctx.json({ status: 'ok' }));
    }
  }),

  // WebSocket connection mock (for testing WebSocket-related components)
  rest.get(`${API_BASE_URL}/ws/custom-betting/market/:id`, (req, res, ctx) => {
    // This would normally be handled by WebSocket, but we can mock the connection
    return res(ctx.status(200), ctx.json({ status: 'WebSocket connection established' }));
  })
];

// Export individual handlers for specific test needs
export const successHandlers = customBettingHandlers;

export const errorHandlers = [
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets`, (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),
  rest.post(`${API_BASE_URL}/api/v1/custom-betting/bets/:id/participate`, (req, res, ctx) => {
    return res(ctx.status(400), ctx.json({ error: 'Invalid bet data' }));
  }),
];

export const networkErrorHandlers = [
  rest.get(`${API_BASE_URL}/api/v1/custom-betting/bets`, (req, res, ctx) => {
    return res.networkError('Network connection failed');
  }),
];