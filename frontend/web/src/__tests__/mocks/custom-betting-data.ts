/**
 * Custom Betting Platform - Mock Data
 * ===================================
 * 
 * Comprehensive mock data for testing the Custom Betting Platform.
 */

import { CustomBet, BetParticipant, BetOutcome } from '@/lib/custom-betting-api';

// Mock Users
export const mockUsers = {
  user1: 'user_2NiQ5uVdvHcSR7z4M8eGf1xK2mL',
  user2: 'user_2NiQ5uVdvHcSR7z4M8eGf2xK3mL',
  user3: 'user_2NiQ5uVdvHcSR7z4M8eGf3xK4mL',
  admin: 'user_2NiQ5uVdvHcSR7z4M8eGf4xK5mL',
  creator: 'user_2NiQ5uVdvHcSR7z4M8eGf5xK6mL',
};

// Mock Outcomes
export const mockOutcomes: BetOutcome[] = [
  {
    id: 'outcome-1',
    outcome_text: 'Yes, Bitcoin will reach $100,000 by end of 2024',
    current_odds: 2.5,
    implied_probability: 0.4,
    total_backing: 15000,
    total_laying: 8000,
    is_winning_outcome: false
  },
  {
    id: 'outcome-2',
    outcome_text: 'No, Bitcoin will not reach $100,000 by end of 2024',
    current_odds: 1.8,
    implied_probability: 0.6,
    total_backing: 12000,
    total_laying: 18000,
    is_winning_outcome: false
  }
];

// Mock Binary Market
export const mockBinaryMarket: CustomBet = {
  id: 'market-1',
  title: 'Will Bitcoin reach $100,000 by end of 2024?',
  description: 'A binary prediction market on whether Bitcoin will reach or exceed $100,000 USD by December 31, 2024.',
  event_criteria: 'Bitcoin (BTC) must reach or exceed $100,000 USD on any major exchange (Coinbase, Binance, Kraken) at any point before midnight UTC on December 31, 2024. Price will be verified using CoinGecko API data.',
  category: 'crypto',
  bet_type: 'binary',
  status: 'active',
  creator_user_id: mockUsers.creator,
  outcomes: mockOutcomes,
  minimum_stake: 5,
  maximum_stake: 10000,
  participant_limit: null,
  total_stakes: 53000,
  total_participants: 27,
  deadline: '2024-12-31T23:59:59Z',
  verification_deadline: '2025-01-07T23:59:59Z',
  is_public: true,
  settlement_type: 'oracle',
  escrow_percentage: 100,
  tags: ['bitcoin', 'cryptocurrency', 'price-prediction'],
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-07-20T15:30:00Z',
  winning_outcome_id: null
};

// Mock Multiple Choice Market
export const mockMultiChoiceMarket: CustomBet = {
  id: 'market-2',
  title: 'Who will win the 2024 US Presidential Election?',
  description: 'Prediction market for the 2024 United States Presidential Election winner.',
  event_criteria: 'The candidate who is officially declared the winner of the 2024 US Presidential Election by major news outlets and receives the majority of Electoral College votes.',
  category: 'politics',
  bet_type: 'multi_choice',
  status: 'open',
  creator_user_id: mockUsers.creator,
  outcomes: [
    {
      id: 'outcome-3',
      outcome_text: 'Democratic Candidate',
      current_odds: 2.1,
      implied_probability: 0.48,
      total_backing: 25000,
      total_laying: 12000,
      is_winning_outcome: false
    },
    {
      id: 'outcome-4',
      outcome_text: 'Republican Candidate',
      current_odds: 2.2,
      implied_probability: 0.45,
      total_backing: 23000,
      total_laying: 15000,
      is_winning_outcome: false
    },
    {
      id: 'outcome-5',
      outcome_text: 'Third Party/Independent',
      current_odds: 15.0,
      implied_probability: 0.07,
      total_backing: 2000,
      total_laying: 800,
      is_winning_outcome: false
    }
  ],
  minimum_stake: 10,
  maximum_stake: 50000,
  participant_limit: 1000,
  total_stakes: 77800,
  total_participants: 156,
  deadline: '2024-11-05T06:00:00Z',
  verification_deadline: '2024-11-15T23:59:59Z',
  is_public: true,
  settlement_type: 'manual',
  escrow_percentage: 100,
  tags: ['politics', 'election', 'usa', 'president'],
  created_at: '2024-03-01T12:00:00Z',
  updated_at: '2024-07-21T09:15:00Z',
  winning_outcome_id: null
};

// Mock Settled Market
export const mockSettledMarket: CustomBet = {
  id: 'market-3',
  title: 'Will Apple stock reach $200 by June 2024?',
  description: 'Binary market on Apple stock price target.',
  event_criteria: 'Apple Inc. (AAPL) stock must reach or exceed $200 per share during regular trading hours by June 30, 2024.',
  category: 'business',
  bet_type: 'binary',
  status: 'settled',
  creator_user_id: mockUsers.creator,
  outcomes: [
    {
      id: 'outcome-6',
      outcome_text: 'Yes, AAPL will reach $200',
      current_odds: 3.2,
      implied_probability: 0.31,
      total_backing: 8000,
      total_laying: 15000,
      is_winning_outcome: false
    },
    {
      id: 'outcome-7',
      outcome_text: 'No, AAPL will not reach $200',
      current_odds: 1.4,
      implied_probability: 0.69,
      total_backing: 15000,
      total_laying: 8000,
      is_winning_outcome: true
    }
  ],
  minimum_stake: 1,
  maximum_stake: 5000,
  participant_limit: null,
  total_stakes: 46000,
  total_participants: 43,
  deadline: '2024-06-30T20:00:00Z',
  verification_deadline: '2024-07-07T23:59:59Z',
  is_public: true,
  settlement_type: 'oracle',
  escrow_percentage: 100,
  tags: ['stocks', 'apple', 'technology'],
  created_at: '2024-04-01T08:00:00Z',
  updated_at: '2024-07-01T16:45:00Z',
  winning_outcome_id: 'outcome-7'
};

// Mock Markets Array
export const mockMarkets: CustomBet[] = [
  mockBinaryMarket,
  mockMultiChoiceMarket,
  mockSettledMarket,
  // Additional markets for testing pagination, filtering, etc.
  {
    id: 'market-4',
    title: 'Will OpenAI release GPT-5 in 2024?',
    description: 'Prediction on OpenAI\'s next major language model release.',
    event_criteria: 'OpenAI must officially announce and release GPT-5 or equivalent successor to GPT-4 by December 31, 2024.',
    category: 'science',
    bet_type: 'binary',
    status: 'active',
    creator_user_id: mockUsers.user1,
    outcomes: [
      {
        id: 'outcome-8',
        outcome_text: 'Yes, GPT-5 will be released',
        current_odds: 1.9,
        implied_probability: 0.53,
        total_backing: 18000,
        total_laying: 12000,
        is_winning_outcome: false
      },
      {
        id: 'outcome-9',
        outcome_text: 'No, GPT-5 will not be released',
        current_odds: 2.3,
        implied_probability: 0.47,
        total_backing: 12000,
        total_laying: 18000,
        is_winning_outcome: false
      }
    ],
    minimum_stake: 2,
    maximum_stake: 2000,
    participant_limit: null,
    total_stakes: 60000,
    total_participants: 89,
    deadline: '2024-12-31T23:59:59Z',
    verification_deadline: '2025-01-07T23:59:59Z',
    is_public: true,
    settlement_type: 'manual',
    escrow_percentage: 100,
    tags: ['ai', 'openai', 'technology'],
    created_at: '2024-02-01T14:00:00Z',
    updated_at: '2024-07-21T11:20:00Z',
    winning_outcome_id: null
  }
];

// Mock User Participations
export const mockUserParticipations: BetParticipant[] = [
  {
    id: 'participation-1',
    user_id: mockUsers.user1,
    bet_id: 'market-1',
    outcome_id: 'outcome-1',
    position_type: 'backing',
    stake_amount: 100,
    desired_odds: 2.5,
    matched_amount: 100,
    status: 'active',
    potential_payout: 250,
    created_at: '2024-07-15T10:30:00Z',
    updated_at: '2024-07-15T10:30:00Z',
    settled_at: null,
    payout_amount: null
  },
  {
    id: 'participation-2',
    user_id: mockUsers.user1,
    bet_id: 'market-2',
    outcome_id: 'outcome-3',
    position_type: 'laying',
    stake_amount: 50,
    desired_odds: 2.1,
    matched_amount: 25,
    status: 'pending',
    potential_payout: 50,
    created_at: '2024-07-20T15:45:00Z',
    updated_at: '2024-07-20T15:45:00Z',
    settled_at: null,
    payout_amount: null
  },
  {
    id: 'participation-3',
    user_id: mockUsers.user1,
    bet_id: 'market-3',
    outcome_id: 'outcome-7',
    position_type: 'backing',
    stake_amount: 200,
    desired_odds: 1.4,
    matched_amount: 200,
    status: 'won',
    potential_payout: 280,
    created_at: '2024-05-10T09:00:00Z',
    updated_at: '2024-07-01T16:45:00Z',
    settled_at: '2024-07-01T16:45:00Z',
    payout_amount: 280
  }
];

// Mock Queue Status
export const mockQueueStatus = {
  market_id: 'market-1',
  total_pending: 5,
  backing_positions: 3,
  laying_positions: 2,
  queue_entries: [
    {
      user_id: mockUsers.user2,
      outcome_id: 'outcome-1',
      position_type: 'backing',
      stake_amount: 150,
      desired_odds: 2.4
    },
    {
      user_id: mockUsers.user3,
      outcome_id: 'outcome-2',
      position_type: 'laying',
      stake_amount: 75,
      desired_odds: 1.9
    }
  ]
};

// Mock Disputes
export const mockDisputes = [
  {
    id: 'dispute-1',
    market_id: 'market-3',
    disputer_user_id: mockUsers.user2,
    dispute_reason: 'incorrect_settlement',
    dispute_details: 'The settlement was incorrect because Apple stock actually did reach $200 on June 15th according to Yahoo Finance data.',
    evidence_urls: ['https://finance.yahoo.com/quote/AAPL/history'],
    status: 'pending',
    created_at: '2024-07-02T10:00:00Z',
    community_votes: {
      support: 12,
      oppose: 8,
      abstain: 3
    }
  }
];

// Mock Analytics Data
export const mockAnalytics = {
  marketPerformance: {
    totalVolume: 236800,
    averageVolume: 59200,
    totalMarkets: 4,
    activeMarkets: 2,
    settledMarkets: 1,
    averageParticipants: 78.75,
    volumeGrowth: 23.5,
    participantGrowth: 18.2
  },
  topCategories: [
    { category: 'Crypto', marketCount: 1, totalVolume: 53000, averageVolume: 53000, percentage: 22.4 },
    { category: 'Politics', marketCount: 1, totalVolume: 77800, averageVolume: 77800, percentage: 32.8 },
    { category: 'Business', marketCount: 1, totalVolume: 46000, averageVolume: 46000, percentage: 19.4 },
    { category: 'Science', marketCount: 1, totalVolume: 60000, averageVolume: 60000, percentage: 25.4 }
  ],
  userEngagement: {
    totalUsers: 200,
    activeUsers: 89,
    averageBetsPerUser: 2.8,
    averageStakeSize: 120.50,
    retentionRate: 72.3
  },
  settlementStats: {
    averageSettlementTime: 3.2,
    disputeRate: 2.3,
    accurateSettlements: 97.7,
    totalDisputes: 1
  }
};

// Utility functions for generating dynamic mock data
export const generateMockMarket = (overrides: Partial<CustomBet> = {}): CustomBet => {
  const baseMarket = {
    id: `market-${Date.now()}`,
    title: 'Test Market',
    description: 'A test market for unit testing',
    event_criteria: 'Test criteria for settlement',
    category: 'other',
    bet_type: 'binary' as const,
    status: 'active' as const,
    creator_user_id: mockUsers.creator,
    outcomes: [
      {
        id: 'test-outcome-1',
        outcome_text: 'Yes',
        current_odds: 2.0,
        implied_probability: 0.5,
        total_backing: 1000,
        total_laying: 1000,
        is_winning_outcome: false
      },
      {
        id: 'test-outcome-2',
        outcome_text: 'No',
        current_odds: 2.0,
        implied_probability: 0.5,
        total_backing: 1000,
        total_laying: 1000,
        is_winning_outcome: false
      }
    ],
    minimum_stake: 1,
    maximum_stake: 1000,
    participant_limit: null,
    total_stakes: 4000,
    total_participants: 10,
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    verification_deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
    is_public: true,
    settlement_type: 'manual' as const,
    escrow_percentage: 100,
    tags: ['test'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    winning_outcome_id: null,
    ...overrides
  };

  return baseMarket;
};

export const generateMockParticipation = (overrides: Partial<BetParticipant> = {}): BetParticipant => {
  return {
    id: `participation-${Date.now()}`,
    user_id: mockUsers.user1,
    bet_id: 'market-1',
    outcome_id: 'outcome-1',
    position_type: 'backing',
    stake_amount: 100,
    desired_odds: 2.0,
    matched_amount: 100,
    status: 'active',
    potential_payout: 200,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    settled_at: null,
    payout_amount: null,
    ...overrides
  };
};