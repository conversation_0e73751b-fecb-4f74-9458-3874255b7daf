/**
 * Custom Betting Platform - Playwright Global Teardown
 * ====================================================
 * 
 * Global teardown for E2E tests including cleanup tasks.
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test global teardown...');

  try {
    // Clean up test data
    await cleanupTestData();

    // Clear test authentication
    await cleanupTestAuthentication();

    // Additional cleanup tasks
    await performAdditionalCleanup();

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to prevent test failure
  }
}

async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');
  
  try {
    // Make request to cleanup endpoint
    const response = await fetch('http://localhost:8000/api/v1/test/cleanup-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ test_run: true })
    });

    if (response.ok) {
      console.log('✅ Test data cleaned up successfully');
    } else {
      console.warn('⚠️ Test data cleanup endpoint not available');
    }
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error);
  }
}

async function cleanupTestAuthentication() {
  console.log('🔐 Cleaning up test authentication...');
  
  try {
    // Clear any persistent authentication state
    // This would typically involve clearing cookies, localStorage, etc.
    console.log('✅ Test authentication cleaned up');
  } catch (error) {
    console.warn('⚠️ Authentication cleanup failed:', error);
  }
}

async function performAdditionalCleanup() {
  console.log('🧽 Performing additional cleanup...');
  
  try {
    // Any additional cleanup tasks
    // - Clear temporary files
    // - Reset database state
    // - Clear caches
    
    console.log('✅ Additional cleanup completed');
  } catch (error) {
    console.warn('⚠️ Additional cleanup failed:', error);
  }
}

export default globalTeardown;