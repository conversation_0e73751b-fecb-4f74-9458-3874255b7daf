/**
 * Custom Betting Platform - Market Creation E2E Tests
 * ===================================================
 * 
 * End-to-end tests for market creation functionality.
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Market Creation E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('test-user-id', 'user_test_12345');
    });
  });

  test('creates binary market end-to-end', async ({ page }) => {
    // Navigate to market creation
    await page.click('[data-testid="create-market-button"]');
    await expect(page).toHaveURL('/markets/create');
    
    // Step 1: Basic Information
    await expect(page.locator('h1')).toContainText('Create New Market');
    await expect(page.locator('text=Step 1 of 5: Basic Information')).toBeVisible();
    
    await page.fill('[data-testid="market-title"]', 'E2E Test: Will SpaceX land on Mars by 2030?');
    await page.fill('[data-testid="market-description"]', 'End-to-end test market for SpaceX Mars landing prediction. This market will settle based on official SpaceX announcements and NASA confirmation.');
    
    // Select category
    await page.click('[data-testid="category-select"]');
    await page.click('text=Science');
    
    // Add tags
    const tagsInput = page.locator('[data-testid="tags-input"]');
    await tagsInput.fill('spacex');
    await tagsInput.press('Enter');
    await tagsInput.fill('mars');
    await tagsInput.press('Enter');
    await tagsInput.fill('space');
    await tagsInput.press('Enter');
    
    // Verify tags are added
    await expect(page.locator('text=#spacex')).toBeVisible();
    await expect(page.locator('text=#mars')).toBeVisible();
    await expect(page.locator('text=#space')).toBeVisible();
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 2: Define Outcomes
    await expect(page.locator('text=Step 2 of 5: Define Outcomes')).toBeVisible();
    
    // Binary should be selected by default
    await expect(page.locator('[data-testid="binary-option"]')).toHaveClass(/border-primary/);
    
    // Verify default outcomes
    await expect(page.locator('[data-testid="outcome-input-0"]')).toHaveValue('Yes');
    await expect(page.locator('[data-testid="outcome-input-1"]')).toHaveValue('No');
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 3: Market Settings
    await expect(page.locator('text=Step 3 of 5: Market Settings')).toBeVisible();
    
    // Set stake limits
    await page.fill('[data-testid="min-stake"]', '5');
    await page.fill('[data-testid="max-stake"]', '2000');
    
    // Set deadline to future date
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    const dateString = futureDate.toISOString().slice(0, 16);
    await page.fill('[data-testid="deadline-input"]', dateString);
    
    // Select settlement method
    await page.click('[data-testid="settlement-select"]');
    await page.click('text=Manual');
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 4: Validation Criteria
    await expect(page.locator('text=Step 4 of 5: Validation Criteria')).toBeVisible();
    
    await page.fill('[data-testid="criteria-input"]', 'SpaceX must successfully land a crewed or uncrewed mission on Mars surface by December 31, 2030. Landing must be confirmed by SpaceX official announcements and verified by NASA or other space agencies. Successful landing means the spacecraft must touch down intact and operational on Mars surface.');
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 5: Review & Submit
    await expect(page.locator('text=Step 5 of 5: Review & Submit')).toBeVisible();
    
    // Verify market summary
    await expect(page.locator('text=E2E Test: Will SpaceX land on Mars by 2030?')).toBeVisible();
    await expect(page.locator('text=Science')).toBeVisible();
    await expect(page.locator('text=$5 - $2,000')).toBeVisible();
    
    // Create market
    await page.click('[data-testid="create-market-button"]');
    
    // Wait for creation success
    await expect(page.locator('text=Creating Market...')).toBeVisible();
    
    // Should redirect to market page
    await expect(page).toHaveURL(/\/markets\/[a-zA-Z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test: Will SpaceX land on Mars by 2030?');
  });

  test('creates multiple choice market end-to-end', async ({ page }) => {
    await page.click('[data-testid="create-market-button"]');
    
    // Step 1
    await page.fill('[data-testid="market-title"]', 'E2E Test: Best Programming Language 2025');
    await page.fill('[data-testid="market-description"]', 'End-to-end test market for most popular programming language in 2025. Settlement based on Stack Overflow Developer Survey results.');
    
    await page.click('[data-testid="category-select"]');
    await page.click('text=Other');
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 2: Multiple Choice
    await page.click('[data-testid="multiple-choice-option"]');
    await expect(page.locator('[data-testid="multiple-choice-option"]')).toHaveClass(/border-primary/);
    
    // Fill outcomes
    await page.fill('[data-testid="outcome-input-0"]', 'JavaScript');
    await page.fill('[data-testid="outcome-input-1"]', 'Python');
    await page.fill('[data-testid="outcome-input-2"]', 'TypeScript');
    
    // Add another outcome
    await page.click('[data-testid="add-outcome-button"]');
    await page.fill('[data-testid="outcome-input-3"]', 'Rust');
    
    // Verify 4 outcomes
    await expect(page.locator('[data-testid="outcome-input-0"]')).toHaveValue('JavaScript');
    await expect(page.locator('[data-testid="outcome-input-1"]')).toHaveValue('Python');
    await expect(page.locator('[data-testid="outcome-input-2"]')).toHaveValue('TypeScript');
    await expect(page.locator('[data-testid="outcome-input-3"]')).toHaveValue('Rust');
    
    await page.click('[data-testid="continue-button"]');
    
    // Continue with default settings
    await page.click('[data-testid="continue-button"]'); // Step 3
    
    await page.fill('[data-testid="criteria-input"]', 'Winner determined by Stack Overflow Developer Survey 2025 results. The programming language with highest percentage of professional developers using it as primary language will be declared winner.');
    await page.click('[data-testid="continue-button"]'); // Step 4
    
    // Review shows all outcomes
    await expect(page.locator('text=JavaScript')).toBeVisible();
    await expect(page.locator('text=Python')).toBeVisible();
    await expect(page.locator('text=TypeScript')).toBeVisible();
    await expect(page.locator('text=Rust')).toBeVisible();
    
    await page.click('[data-testid="create-market-button"]');
    
    await expect(page).toHaveURL(/\/markets\/[a-zA-Z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test: Best Programming Language 2025');
  });

  test('validates form fields correctly', async ({ page }) => {
    await page.click('[data-testid="create-market-button"]');
    
    // Try to continue without filling required fields
    const continueButton = page.locator('[data-testid="continue-button"]');
    await expect(continueButton).toBeDisabled();
    
    // Fill title but leave description empty
    await page.fill('[data-testid="market-title"]', 'Test Market');
    await expect(continueButton).toBeDisabled();
    
    // Add short description
    await page.fill('[data-testid="market-description"]', 'Too short');
    await expect(page.locator('text=Description must be at least 50 characters')).toBeVisible();
    await expect(continueButton).toBeDisabled();
    
    // Fix description but no category
    await page.fill('[data-testid="market-description"]', 'This is a valid description that meets the minimum character requirements for the market creation process.');
    await expect(continueButton).toBeDisabled();
    
    // Add category
    await page.click('[data-testid="category-select"]');
    await page.click('text=Business');
    
    // Should enable continue
    await expect(continueButton).toBeEnabled();
  });

  test('handles wizard navigation correctly', async ({ page }) => {
    await page.click('[data-testid="create-market-button"]');
    
    // Fill step 1
    await page.fill('[data-testid="market-title"]', 'Navigation Test Market');
    await page.fill('[data-testid="market-description"]', 'Testing navigation functionality in the market creation wizard.');
    await page.click('[data-testid="category-select"]');
    await page.click('text=Entertainment');
    
    await page.click('[data-testid="continue-button"]');
    
    // Step 2
    await expect(page.locator('text=Step 2 of 5: Define Outcomes')).toBeVisible();
    await page.click('[data-testid="continue-button"]');
    
    // Step 3
    await expect(page.locator('text=Step 3 of 5: Market Settings')).toBeVisible();
    
    // Fill some data
    await page.fill('[data-testid="min-stake"]', '10');
    
    // Go back to step 1
    await page.click('[data-testid="back-button"]'); // Step 2
    await page.click('[data-testid="back-button"]'); // Step 1
    
    // Verify data is preserved
    await expect(page.locator('[data-testid="market-title"]')).toHaveValue('Navigation Test Market');
    await expect(page.locator('text=Entertainment')).toBeVisible();
    
    // Navigate forward again
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="continue-button"]');
    
    // Verify step 3 data is preserved
    await expect(page.locator('[data-testid="min-stake"]')).toHaveValue('10');
  });

  test('handles market creation errors gracefully', async ({ page }) => {
    // Mock network failure
    await page.route('**/api/v1/custom-betting/bets', route => {
      route.abort('failed');
    });
    
    await page.click('[data-testid="create-market-button"]');
    
    // Complete wizard quickly
    await page.fill('[data-testid="market-title"]', 'Error Test Market');
    await page.fill('[data-testid="market-description"]', 'Testing error handling during market creation with network failures.');
    await page.click('[data-testid="category-select"]');
    await page.click('text=Other');
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="continue-button"]');
    await page.fill('[data-testid="criteria-input"]', 'Error test criteria for network failure handling during market creation.');
    await page.click('[data-testid="continue-button"]');
    
    // Try to create market
    await page.click('[data-testid="create-market-button"]');
    
    // Should show error message
    await expect(page.locator('text=Failed to create market')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });

  test('cancels market creation and returns to previous page', async ({ page }) => {
    // Start from markets page
    await page.goto('/markets');
    await page.click('[data-testid="create-market-button"]');
    
    // Fill some data
    await page.fill('[data-testid="market-title"]', 'Cancelled Market');
    
    // Cancel
    await page.click('[data-testid="cancel-button"]');
    
    // Should return to markets page
    await expect(page).toHaveURL('/markets');
  });

  test('shows progress indicator correctly', async ({ page }) => {
    await page.click('[data-testid="create-market-button"]');
    
    // Check initial progress
    const step1 = page.locator('[data-testid="progress-step-1"]');
    const step2 = page.locator('[data-testid="progress-step-2"]');
    const step3 = page.locator('[data-testid="progress-step-3"]');
    const step4 = page.locator('[data-testid="progress-step-4"]');
    const step5 = page.locator('[data-testid="progress-step-5"]');
    
    // Step 1 should be active
    await expect(step1).toHaveClass(/bg-primary/);
    await expect(step2).not.toHaveClass(/bg-primary/);
    
    // Complete step 1
    await page.fill('[data-testid="market-title"]', 'Progress Test');
    await page.fill('[data-testid="market-description"]', 'Testing progress indicator functionality in the wizard.');
    await page.click('[data-testid="category-select"]');
    await page.click('text=Sports');
    await page.click('[data-testid="continue-button"]');
    
    // Step 2 should be active
    await expect(step1).toHaveClass(/bg-green/); // Completed
    await expect(step2).toHaveClass(/bg-primary/); // Active
    await expect(step3).not.toHaveClass(/bg-primary/);
  });

  test('shows character counts for inputs', async ({ page }) => {
    await page.click('[data-testid="create-market-button"]');
    
    // Type in title
    await page.fill('[data-testid="market-title"]', 'Test Title');
    await expect(page.locator('text=10 / 200')).toBeVisible();
    
    // Type in description
    await page.fill('[data-testid="market-description"]', 'Short description for testing character count functionality.');
    await expect(page.locator('text=62 /')).toBeVisible();
  });
});

// Helper function to create a complete market
async function createCompleteMarket(page: Page, marketData: {
  title: string;
  description: string;
  category: string;
  type: 'binary' | 'multiple';
  outcomes?: string[];
  minStake?: string;
  maxStake?: string;
}) {
  // Step 1
  await page.fill('[data-testid="market-title"]', marketData.title);
  await page.fill('[data-testid="market-description"]', marketData.description);
  await page.click('[data-testid="category-select"]');
  await page.click(`text=${marketData.category}`);
  await page.click('[data-testid="continue-button"]');
  
  // Step 2
  if (marketData.type === 'multiple') {
    await page.click('[data-testid="multiple-choice-option"]');
    if (marketData.outcomes) {
      for (let i = 0; i < marketData.outcomes.length; i++) {
        if (i >= 3) {
          await page.click('[data-testid="add-outcome-button"]');
        }
        await page.fill(`[data-testid="outcome-input-${i}"]`, marketData.outcomes[i]);
      }
    }
  }
  await page.click('[data-testid="continue-button"]');
  
  // Step 3
  if (marketData.minStake) {
    await page.fill('[data-testid="min-stake"]', marketData.minStake);
  }
  if (marketData.maxStake) {
    await page.fill('[data-testid="max-stake"]', marketData.maxStake);
  }
  await page.click('[data-testid="continue-button"]');
  
  // Step 4
  await page.fill('[data-testid="criteria-input"]', 'Standard test criteria for automated market creation testing.');
  await page.click('[data-testid="continue-button"]');
  
  // Step 5
  await page.click('[data-testid="create-market-button"]');
}