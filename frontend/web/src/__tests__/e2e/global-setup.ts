/**
 * Custom Betting Platform - Playwright Global Setup
 * =================================================
 * 
 * Global setup for E2E tests including database seeding and authentication.
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test global setup...');

  // Launch browser for setup tasks
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    console.log('✅ Application is ready');

    // Setup test authentication if needed
    await setupTestAuthentication(page);

    // Seed test data
    await seedTestData(page);

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestAuthentication(page: any) {
  console.log('🔐 Setting up test authentication...');
  
  try {
    // Mock authentication for testing
    // In a real app, this might involve logging in with test credentials
    await page.evaluate(() => {
      // Mock authenticated user
      localStorage.setItem('test-user-id', 'user_test_12345');
      localStorage.setItem('test-auth-token', 'mock-jwt-token');
    });

    console.log('✅ Test authentication setup complete');
  } catch (error) {
    console.warn('⚠️ Authentication setup failed:', error);
    // Non-critical failure, continue with tests
  }
}

async function seedTestData(page: any) {
  console.log('🌱 Seeding test data...');
  
  try {
    // Navigate to a test endpoint that seeds data
    const response = await page.goto('http://localhost:8000/api/v1/test/seed-data', {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    if (response && response.ok()) {
      console.log('✅ Test data seeded successfully');
    } else {
      console.warn('⚠️ Test data seeding endpoint not available, using mock data');
    }
  } catch (error) {
    console.warn('⚠️ Test data seeding failed, tests will use mock data:', error);
    // Non-critical failure, tests can proceed with mock data
  }
}

export default globalSetup;