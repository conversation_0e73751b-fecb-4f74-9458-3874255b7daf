/**
 * useLeaderboardWebSocket Hook Tests
 * ==================================
 * 
 * Comprehensive test suite for WebSocket integration hooks
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { 
  useLeaderboardWebSocket, 
  useLiveLeaderboardData,
  useAchievementCelebration,
  useChallengeSpectator
} from '@/hooks/useLeaderboardWebSocket';

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState: number = MockWebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      this.onopen?.(new Event('open'));
    }, 10);
  }

  send = jest.fn();
  close = jest.fn((code?: number, reason?: string) => {
    this.readyState = MockWebSocket.CLOSED;
    this.onclose?.(new CloseEvent('close', { code, reason, wasClean: true }));
  });

  // Helper method to simulate receiving a message
  simulateMessage(data: any) {
    this.onmessage?.(new MessageEvent('message', { data: JSON.stringify(data) }));
  }

  // Helper method to simulate error
  simulateError() {
    this.onerror?.(new Event('error'));
  }
}

// Mock environment variable
const originalEnv = process.env;

beforeEach(() => {
  global.WebSocket = MockWebSocket as any;
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_WEBSOCKET_URL: 'ws://localhost:8006/ws'
  };
  jest.clearAllMocks();
});

afterEach(() => {
  process.env = originalEnv;
});

describe('useLeaderboardWebSocket', () => {
  describe('Connection Management', () => {
    test('establishes WebSocket connection on mount', async () => {
      const { result } = renderHook(() => useLeaderboardWebSocket('test-user'));

      expect(result.current.connectionStatus).toBe('connecting');

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });
    });

    test('includes user_id in WebSocket URL when provided', async () => {
      renderHook(() => useLeaderboardWebSocket('test-user-123'));

      await waitFor(() => {
        expect(MockWebSocket).toHaveBeenCalledWith('ws://localhost:8006/ws?user_id=test-user-123');
      });
    });

    test('connects without user_id when not provided', async () => {
      renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(MockWebSocket).toHaveBeenCalledWith('ws://localhost:8006/ws');
      });
    });

    test('sends subscription message on connection', async () => {
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      // Check if send was called with subscription message
      expect(MockWebSocket.prototype.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'subscribe',
          channels: ['leaderboard_updates', 'achievements', 'challenges', 'affiliations']
        })
      );
    });

    test('handles connection errors correctly', async () => {
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      // Simulate error
      act(() => {
        (global.WebSocket as any).prototype.simulateError();
      });

      expect(result.current.connectionStatus).toBe('error');
    });

    test('disconnects properly on unmount', async () => {
      const { result, unmount } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      unmount();

      expect(MockWebSocket.prototype.close).toHaveBeenCalledWith(1000, 'Manual disconnect');
    });
  });

  describe('Message Handling', () => {
    test('processes ranking update messages', async () => {
      const mockHandler = jest.fn();
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      act(() => {
        result.current.onRankingUpdate(mockHandler);
      });

      const mockMessage = {
        type: 'ranking_update',
        timestamp: '2024-01-15T10:00:00Z',
        data: {
          user_id: 'user123',
          username: 'TestUser',
          old_rank: 10,
          new_rank: 8,
          score_change: 150,
          module: 'gaming'
        }
      };

      // Simulate receiving a ranking update message
      act(() => {
        const mockWs = new MockWebSocket('ws://test');
        mockWs.simulateMessage(mockMessage);
      });

      expect(result.current.messageCount).toBe(1);
    });

    test('processes achievement unlock messages', async () => {
      const mockHandler = jest.fn();
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      act(() => {
        result.current.onAchievementUnlock(mockHandler);
      });

      const mockMessage = {
        type: 'achievement_unlock',
        timestamp: '2024-01-15T10:00:00Z',
        data: {
          user_id: 'user123',
          username: 'TestUser',
          achievement: {
            id: 'ach1',
            name: 'Test Achievement',
            description: 'Test description',
            rarity: 'Epic',
            points: 500
          },
          celebration_eligible: true
        }
      };

      // Simulate receiving achievement message
      act(() => {
        const mockWs = new MockWebSocket('ws://test');
        mockWs.simulateMessage(mockMessage);
      });

      expect(result.current.messageCount).toBe(1);
    });

    test('handles malformed messages gracefully', async () => {
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      // Simulate malformed message
      act(() => {
        const mockWs = new MockWebSocket('ws://test');
        mockWs.onmessage?.(new MessageEvent('message', { data: 'invalid json' }));
      });

      // Should not crash and connection should remain stable
      expect(result.current.connectionStatus).toBe('connected');
    });
  });

  describe('Reconnection Logic', () => {
    test('attempts reconnection on unexpected disconnect', async () => {
      const { result } = renderHook(() => useLeaderboardWebSocket());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });

      // Simulate unexpected disconnect
      act(() => {
        const mockWs = new MockWebSocket('ws://test');
        mockWs.onclose?.(new CloseEvent('close', { wasClean: false }));
      });

      expect(result.current.connectionStatus).toBe('disconnected');

      // Should attempt reconnection
      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connecting');
      }, { timeout: 5000 });
    });
  });
});

describe('useLiveLeaderboardData', () => {
  test('initializes with empty live data', () => {
    const { result } = renderHook(() => useLiveLeaderboardData('unified', 'test-user'));

    expect(result.current.liveData).toEqual([]);
    expect(result.current.pendingUpdates).toEqual([]);
  });

  test('processes ranking updates for matching view', async () => {
    const { result } = renderHook(() => useLiveLeaderboardData('gaming', 'test-user'));

    // Set initial data
    act(() => {
      result.current.setLiveData([
        {
          user_id: 'user123',
          rank: 10,
          gaming_score: 5000,
          username: 'TestUser',
          platform_tier: 'Gold'
        }
      ]);
    });

    // Wait for WebSocket connection
    await waitFor(() => {
      expect(result.current.connectionStatus).toBe('connected');
    });

    // The hook should process updates for the gaming module
    expect(result.current.liveData).toHaveLength(1);
  });
});

describe('useAchievementCelebration', () => {
  test('manages celebration queue correctly', async () => {
    const { result } = renderHook(() => useAchievementCelebration('test-user'));

    expect(result.current.currentCelebration).toBeNull();
    expect(result.current.celebrationQueue).toBe(0);

    // Test dismiss function
    act(() => {
      result.current.dismissCelebration();
    });

    expect(result.current.currentCelebration).toBeNull();
  });
});

describe('useChallengeSpectator', () => {
  test('subscribes to challenge updates', async () => {
    const { result } = renderHook(() => 
      useChallengeSpectator('challenge-123', 'test-user')
    );

    await waitFor(() => {
      expect(result.current.challengeData).toBeNull();
      expect(result.current.liveProgress).toBeNull();
    });
  });

  test('updates live progress on challenge messages', async () => {
    const { result } = renderHook(() => 
      useChallengeSpectator('challenge-123', 'test-user')
    );

    // Set initial challenge data
    act(() => {
      result.current.setChallengeData({
        id: 'challenge-123',
        name: 'Test Challenge'
      });
    });

    expect(result.current.challengeData).toEqual({
      id: 'challenge-123',
      name: 'Test Challenge'
    });
  });
});

describe('Performance and Memory Management', () => {
  test('cleans up event listeners on unmount', async () => {
    const { unmount } = renderHook(() => useLeaderboardWebSocket());

    await waitFor(() => {
      // Wait for connection
    });

    unmount();

    expect(MockWebSocket.prototype.close).toHaveBeenCalled();
  });

  test('handles rapid message updates efficiently', async () => {
    const { result } = renderHook(() => useLeaderboardWebSocket());

    await waitFor(() => {
      expect(result.current.connectionStatus).toBe('connected');
    });

    // Send multiple rapid updates
    act(() => {
      for (let i = 0; i < 10; i++) {
        const mockWs = new MockWebSocket('ws://test');
        mockWs.simulateMessage({
          type: 'ranking_update',
          timestamp: `2024-01-15T10:0${i}:00Z`,
          data: { user_id: 'user123', old_rank: 10 + i, new_rank: 9 + i }
        });
      }
    });

    expect(result.current.messageCount).toBe(10);
  });
});