/**
 * useLeaderboardApi Hook Tests
 * ============================
 * 
 * Comprehensive test suite for leaderboard API integration hooks
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { 
  useLeaderboardApi,
  useLeaderboardData,
  useUserPerformance
} from '@/hooks/useLeaderboardApi';

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock environment variable
const originalEnv = process.env;

beforeEach(() => {
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_LEADERBOARDS_API: 'http://localhost:8006'
  };
  jest.clearAllMocks();
});

afterEach(() => {
  process.env = originalEnv;
});

describe('useLeaderboardApi', () => {
  const mockLeaderboardResponse = {
    success: true,
    data: {
      leaderboard: [
        {
          rank: 1,
          user_id: 'user1',
          username: 'TopPlayer',
          unified_score: 9875.5,
          platform_tier: 'Master',
          gaming_score: 9500,
          gaming_change: 150
        }
      ],
      total_count: 1,
      cache_info: {
        cached: false,
        cache_age_seconds: 0,
        expires_in_seconds: 300
      }
    }
  };

  const mockUserPerformanceResponse = {
    success: true,
    data: {
      user_id: 'test-user',
      global_ranking: {
        unified_rank: 42,
        unified_score: 7834.5,
        percentile: 15.2,
        total_users: 50000
      },
      platform_tier: {
        current: 'Gold',
        next: 'Platinum',
        progress: 0.68,
        points_to_next: 1165
      }
    }
  };

  describe('API Call Management', () => {
    test('handles successful API calls', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockLeaderboardResponse)
      });

      const { result } = renderHook(() => useLeaderboardApi());

      let response;
      await act(async () => {
        response = await result.current.getUnifiedLeaderboard();
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/unified-leaderboards?',
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json'
          }
        })
      );

      expect(response).toEqual(mockLeaderboardResponse.data);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    test('handles API errors correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        try {
          await result.current.getUnifiedLeaderboard();
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toContain('API Error: 500');
    });

    test('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        try {
          await result.current.getUnifiedLeaderboard();
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.error).toBe('Network error');
    });

    test('sets loading state during API calls', async () => {
      let resolvePromise: (value: any) => void;
      const pendingPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockFetch.mockReturnValueOnce(pendingPromise);

      const { result } = renderHook(() => useLeaderboardApi());

      act(() => {
        result.current.getUnifiedLeaderboard();
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        resolvePromise!({
          ok: true,
          json: () => Promise.resolve(mockLeaderboardResponse)
        });
        await pendingPromise;
      });

      expect(result.current.loading).toBe(false);
    });
  });

  describe('getUnifiedLeaderboard', () => {
    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockLeaderboardResponse)
      });
    });

    test('builds query parameters correctly', async () => {
      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.getUnifiedLeaderboard({
          view: 'gaming',
          affiliation_filter: 'elite-team',
          time_range: 'weekly',
          limit: 50,
          offset: 10
        });
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/unified-leaderboards?view=gaming&affiliation_filter=elite-team&time_range=weekly&limit=50&offset=10',
        expect.any(Object)
      );
    });

    test('works with default parameters', async () => {
      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.getUnifiedLeaderboard();
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/unified-leaderboards?',
        expect.any(Object)
      );
    });
  });

  describe('getPersonalRanking', () => {
    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockUserPerformanceResponse)
      });
    });

    test('fetches personal ranking for specific user', async () => {
      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.getPersonalRanking('test-user-123');
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/personal-ranking/test-user-123',
        expect.any(Object)
      );
    });
  });

  describe('getAchievements', () => {
    const mockAchievementsResponse = {
      success: true,
      data: [
        {
          id: 'ach1',
          name: 'Master Trader',
          description: 'Achieved master level',
          category: 'trading',
          tier: 'Master',
          rarity: 'Legendary',
          points: 1000
        }
      ]
    };

    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockAchievementsResponse)
      });
    });

    test('builds achievement query parameters correctly', async () => {
      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.getAchievements({
          category: 'trading',
          modules: ['gaming', 'betting'],
          tier: 'Master',
          rarity: 'Legendary',
          user_id: 'test-user',
          unlocked_only: true
        });
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/achievements?category=trading&modules=gaming&modules=betting&tier=Master&rarity=Legendary&user_id=test-user&unlocked_only=true',
        expect.any(Object)
      );
    });
  });

  describe('getGlobalStats', () => {
    test('fetches global statistics', async () => {
      const mockStatsResponse = {
        success: true,
        data: {
          platform_overview: {},
          tier_distribution: {},
          module_popularity: {},
          top_achievements_this_week: [],
          leaderboard_highlights: {}
        }
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockStatsResponse)
      });

      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.getGlobalStats();
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/global-stats',
        expect.any(Object)
      );
    });
  });

  describe('refreshCache', () => {
    test('sends POST request to refresh cache', async () => {
      const mockRefreshResponse = {
        success: true,
        data: { success: true }
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockRefreshResponse)
      });

      const { result } = renderHook(() => useLeaderboardApi());

      await act(async () => {
        await result.current.refreshCache();
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8006/api/v1/leaderboards/refresh-cache',
        expect.objectContaining({
          method: 'POST'
        })
      );
    });
  });
});

describe('useLeaderboardData', () => {
  const mockDataResponse = {
    success: true,
    data: {
      leaderboard: [
        {
          rank: 1,
          user_id: 'user1',
          username: 'TopPlayer',
          unified_score: 9875.5,
          platform_tier: 'Master',
          gaming_score: 9500,
          gaming_change: 150
        }
      ],
      total_count: 1
    }
  };

  beforeEach(() => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockDataResponse)
    });
  });

  test('fetches data on mount', async () => {
    const { result } = renderHook(() => 
      useLeaderboardData('unified', 'all_time', 'elite-team')
    );

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toHaveLength(1);
    expect(result.current.error).toBeNull();
  });

  test('refetches data when parameters change', async () => {
    const { result, rerender } = renderHook(
      ({ view, timeRange }) => useLeaderboardData(view, timeRange),
      {
        initialProps: { view: 'unified' as const, timeRange: 'all_time' as const }
      }
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);

    rerender({ view: 'gaming', timeRange: 'weekly' });

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  test('handles API errors in data hook', async () => {
    mockFetch.mockRejectedValue(new Error('API Error'));

    const { result } = renderHook(() => useLeaderboardData());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toContain('API Error');
    expect(result.current.data).toEqual([]);
  });
});

describe('useUserPerformance', () => {
  const localMockUserPerformanceResponse = {
    success: true,
    data: {
      user_id: 'test-user',
      global_ranking: {
        unified_rank: 42,
        unified_score: 7834.5,
        percentile: 15.2,
        total_users: 50000
      },
      platform_tier: {
        current: 'Gold',
        next: 'Platinum',
        progress: 0.68,
        points_to_next: 1165
      }
    }
  };

  beforeEach(() => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(localMockUserPerformanceResponse)
    });
  });

  test('fetches user performance data', async () => {
    const { result } = renderHook(() => useUserPerformance('test-user-123'));

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(localMockUserPerformanceResponse.data);
    expect(result.current.error).toBeNull();
  });

  test('does not fetch when userId is not provided', () => {
    const { result } = renderHook(() => useUserPerformance());

    expect(result.current.loading).toBe(false);
    expect(result.current.data).toBeNull();
    expect(mockFetch).not.toHaveBeenCalled();
  });

  test('handles errors in user performance hook', async () => {
    mockFetch.mockRejectedValue(new Error('User not found'));

    const { result } = renderHook(() => useUserPerformance('invalid-user'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toContain('User not found');
    expect(result.current.data).toBeNull();
  });
});

describe('Performance and Caching', () => {
  test('reuses API instance across multiple calls', async () => {
    const { result } = renderHook(() => useLeaderboardApi());
    
    const localMockResponse = {
      success: true,
      data: {
        leaderboard: [
          {
            rank: 1,
            user_id: 'user1',
            username: 'TopPlayer',
            unified_score: 9875.5
          }
        ],
        total_count: 1
      }
    };

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(localMockResponse)
    });

    await act(async () => {
      await result.current.getUnifiedLeaderboard();
      await result.current.getUnifiedLeaderboard();
    });

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(result.current.loading).toBe(false);
  });

  test('handles concurrent API calls correctly', async () => {
    const { result } = renderHook(() => useLeaderboardApi());
    
    const localMockResponse = {
      success: true,
      data: {
        leaderboard: [],
        total_count: 0
      }
    };

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(localMockResponse)
    });

    await act(async () => {
      const promises = [
        result.current.getUnifiedLeaderboard({ view: 'gaming' }),
        result.current.getUnifiedLeaderboard({ view: 'betting' }),
        result.current.getUnifiedLeaderboard({ view: 'trading' })
      ];

      await Promise.all(promises);
    });

    expect(mockFetch).toHaveBeenCalledTimes(3);
    expect(result.current.error).toBeNull();
  });
});