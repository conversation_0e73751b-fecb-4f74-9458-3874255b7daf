/**
 * Leaderboard Accessibility Tests
 * ===============================
 * 
 * Comprehensive accessibility testing for leaderboard components
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, test, expect } from '@jest/globals';
import { TooltipProvider } from '@/components/ui/tooltip';
import LeaderboardTable from '@/components/leaderboards/LeaderboardTable';
import UnifiedPerformanceCard from '@/components/leaderboards/UnifiedPerformanceCard';

// Test wrapper with required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TooltipProvider>
    {children}
  </TooltipProvider>
);

// Mock data
const mockLeaderboardData = [
  {
    rank: 1,
    user_id: 'user1',
    username: 'TopPlayer',
    avatar_url: '/avatars/top.jpg',
    affiliation: {
      id: 'aff1',
      name: 'Elite Team',
      logo_url: '/logos/elite.png'
    },
    unified_score: 9875.5,
    platform_tier: 'Master' as const,
    tier_progress: 0.85,
    gaming_score: 9500,
    gaming_change: 150,
    betting_score: 8750,
    betting_change: -25,
    trading_score: 9200,
    trading_change: 75,
    analyst_score: 8900,
    analyst_change: 100,
    cross_platform_consistency: 0.92,
    active_modules: ['gaming', 'betting', 'trading'],
    recent_achievements: [
      {
        id: 'ach1',
        achievement_code: 'MASTER_TRADER',
        name: 'Master Trader',
        description: 'Achieved master level in trading',
        category: 'trading',
        tier: 'Master' as const,
        rarity: 'Legendary' as const,
        points: 1000,
        icon_url: '/achievements/master.png',
        unlocked_at: '2024-01-15T10:00:00Z'
      }
    ],
    score_history: [
      { timestamp: '2024-01-01', score: 9000 },
      { timestamp: '2024-01-02', score: 9200 },
      { timestamp: '2024-01-03', score: 9875.5 }
    ],
    rank_change: 0
  }
];

const mockUserPerformance = {
  user_id: 'test-user',
  global_ranking: {
    unified_rank: 42,
    unified_score: 7834.5,
    percentile: 15.2,
    total_users: 50000
  },
  platform_tier: {
    current: 'Gold' as const,
    next: 'Platinum' as const,
    progress: 0.68,
    points_to_next: 1165
  },
  module_rankings: {
    gaming: { rank: 23, score: 8245, percentile: 12.5, change_24h: 45, change_7d: 123, best_rank_ever: 18, best_score_ever: 8967 },
    betting: { rank: 67, score: 7123, percentile: 24.8, change_24h: -12, change_7d: 89, best_rank_ever: 45, best_score_ever: 7834 },
    trading: { rank: 156, score: 6234, percentile: 45.2, change_24h: 78, change_7d: -23, best_rank_ever: 98, best_score_ever: 7123 },
    analyst: { rank: 234, score: 5678, percentile: 67.3, change_24h: 23, change_7d: 45, best_rank_ever: 189, best_score_ever: 6234 }
  },
  cross_platform_consistency: 0.82,
  active_modules: ['gaming', 'betting', 'trading'],
  performance_trends: {
    daily_scores: [],
    weekly_scores: [],
    monthly_scores: []
  },
  achievement_summary: {
    total_unlocked: 24,
    total_available: 50,
    recent_unlocks: [],
    progress_towards_next: []
  }
};

describe('Leaderboard Accessibility Tests', () => {
  describe('LeaderboardTable Accessibility', () => {
    test('has proper table semantics', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Test table structure
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();

      // Test table headers
      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders.length).toBeGreaterThan(0);

      // Test table rows
      const rows = screen.getAllByRole('row');
      expect(rows.length).toBeGreaterThan(1); // Header row + data rows
    });

    test('provides proper ARIA labels and descriptions', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Check for images with alt text
      const avatars = screen.getAllByRole('img');
      avatars.forEach(avatar => {
        expect(avatar).toHaveAttribute('alt');
      });
    });

    test('supports keyboard navigation', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
            onUserClick={() => {}}
          />
        </TestWrapper>
      );

      // Table rows should be clickable and keyboard accessible
      const userRow = screen.getByText('TopPlayer').closest('tr');
      expect(userRow).toHaveClass('cursor-pointer');
    });

    test('announces loading state to screen readers', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={[]}
            view="unified"
            isLoading={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Loading leaderboard...')).toBeInTheDocument();
    });

    test('provides meaningful empty state information', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={[]}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      expect(screen.getByText('No rankings available')).toBeInTheDocument();
      expect(screen.getByText('Check back soon for updated leaderboards')).toBeInTheDocument();
    });
  });

  describe('UnifiedPerformanceCard Accessibility', () => {
    test('has proper heading structure', () => {
      render(
        <TestWrapper>
          <UnifiedPerformanceCard userPerformance={mockUserPerformance} />
        </TestWrapper>
      );

      // Should have clear heading hierarchy
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    test('provides text alternatives for visual elements', () => {
      render(
        <TestWrapper>
          <UnifiedPerformanceCard userPerformance={mockUserPerformance} />
        </TestWrapper>
      );

      // Progress indicators should have accessible labels
      const progressBars = screen.getAllByRole('progressbar');
      progressBars.forEach(progress => {
        expect(progress).toHaveAttribute('aria-valuenow');
        expect(progress).toHaveAttribute('aria-valuemin');
        expect(progress).toHaveAttribute('aria-valuemax');
      });
    });

    test('announces loading state properly', () => {
      render(
        <TestWrapper>
          <UnifiedPerformanceCard userPerformance={null} isLoading={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    test('handles null data gracefully with accessible messaging', () => {
      render(
        <TestWrapper>
          <UnifiedPerformanceCard userPerformance={null} />
        </TestWrapper>
      );

      expect(screen.getByText('No performance data')).toBeInTheDocument();
    });
  });

  describe('Color and Contrast Accessibility', () => {
    test('tier colors have sufficient contrast', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Tier badges should be visible
      const tierBadge = screen.getByText('Master');
      expect(tierBadge).toBeVisible();
      
      // The badge should have proper color contrast (tested through visual appearance)
      const badgeElement = tierBadge.closest('[class*="badge"]');
      expect(badgeElement).toBeInTheDocument();
    });

    test('score change indicators are accessible', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Score changes should be indicated by more than just color
      // Look for numerical indicators
      expect(screen.getByText('150')).toBeInTheDocument(); // Gaming change
    });
  });

  describe('Responsive and Mobile Accessibility', () => {
    test('maintains accessibility on smaller screens', () => {
      // Simulate mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 320,
      });

      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Table should still be accessible on mobile
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
      
      // Clean up
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
    });

    test('touch targets are appropriately sized', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
            onUserClick={() => {}}
          />
        </TestWrapper>
      );

      // Clickable rows should have sufficient size for touch interaction
      const userRow = screen.getByText('TopPlayer').closest('tr');
      expect(userRow).toBeInTheDocument();
      
      // Row should be clickable (implied touch target)
      expect(userRow).toHaveClass('cursor-pointer');
    });
  });

  describe('Screen Reader Support', () => {
    test('provides meaningful context for rankings', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Rank information should be clear
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('TopPlayer')).toBeInTheDocument();
    });

    test('score values are properly labeled', () => {
      render(
        <TestWrapper>
          <UnifiedPerformanceCard userPerformance={mockUserPerformance} />
        </TestWrapper>
      );

      // Numerical values should have context
      expect(screen.getByText('42')).toBeInTheDocument(); // Rank
      expect(screen.getByText('Gold')).toBeInTheDocument(); // Tier
      expect(screen.getByText('68%')).toBeInTheDocument(); // Progress
    });

    test('provides context for achievement displays', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Achievement information should be accessible
      expect(screen.getByText('Master Trader')).toBeInTheDocument();
    });
  });

  describe('Focus Management', () => {
    test('manages focus appropriately for interactive elements', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
            onUserClick={() => {}}
          />
        </TestWrapper>
      );

      const userRow = screen.getByText('TopPlayer').closest('tr');
      
      // Interactive elements should be focusable
      expect(userRow).toBeInTheDocument();
      
      // Row should support interaction
      expect(userRow).toHaveClass('cursor-pointer');
    });

    test('provides clear focus indicators', () => {
      render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
            onUserClick={() => {}}
          />
        </TestWrapper>
      );

      // Interactive elements should have hover states
      const userRow = screen.getByText('TopPlayer').closest('tr');
      expect(userRow).toHaveClass('hover:bg-gray-50');
    });
  });

  describe('Performance Accessibility', () => {
    test('handles large datasets without accessibility degradation', () => {
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        ...mockLeaderboardData[0],
        rank: i + 1,
        user_id: `user${i + 1}`,
        username: `Player${i + 1}`,
        unified_score: 9000 - i * 10
      }));

      render(
        <TestWrapper>
          <LeaderboardTable
            data={largeDataset}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Table should still be accessible with large dataset
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
      
      const rows = screen.getAllByRole('row');
      expect(rows.length).toBe(101); // Header + 100 data rows
    });

    test('maintains accessibility during real-time updates', () => {
      const { rerender } = render(
        <TestWrapper>
          <LeaderboardTable
            data={mockLeaderboardData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Update with new data
      const updatedData = [
        {
          ...mockLeaderboardData[0],
          rank: 2,
          unified_score: 9800
        }
      ];

      rerender(
        <TestWrapper>
          <LeaderboardTable
            data={updatedData}
            view="unified"
            isLoading={false}
          />
        </TestWrapper>
      );

      // Table should maintain accessibility after updates
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
      
      expect(screen.getByText('2')).toBeInTheDocument(); // Updated rank
    });
  });
});