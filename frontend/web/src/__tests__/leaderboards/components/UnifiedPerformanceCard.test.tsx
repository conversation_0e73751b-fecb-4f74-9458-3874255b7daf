/**
 * UnifiedPerformanceCard Component Tests
 * ====================================
 * 
 * Test suite for the unified performance card component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, test, expect } from '@jest/globals';
import { TooltipProvider } from '@/components/ui/tooltip';
import UnifiedPerformanceCard from '@/components/leaderboards/UnifiedPerformanceCard';

// Test wrapper with required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TooltipProvider>
    {children}
  </TooltipProvider>
);

const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

// Mock data
const mockUserPerformance = {
  user_id: 'test-user',
  global_ranking: {
    unified_rank: 42,
    unified_score: 7834.5,
    percentile: 15.2,
    total_users: 50000
  },
  platform_tier: {
    current: 'Gold' as const,
    next: 'Platinum' as const,
    progress: 0.68,
    points_to_next: 1165
  },
  module_rankings: {
    gaming: { rank: 23, score: 8245, percentile: 12.5, change_24h: 45, change_7d: 123, best_rank_ever: 18, best_score_ever: 8967 },
    betting: { rank: 67, score: 7123, percentile: 24.8, change_24h: -12, change_7d: 89, best_rank_ever: 45, best_score_ever: 7834 },
    trading: { rank: 156, score: 6234, percentile: 45.2, change_24h: 78, change_7d: -23, best_rank_ever: 98, best_score_ever: 7123 },
    analyst: { rank: 234, score: 5678, percentile: 67.3, change_24h: 23, change_7d: 45, best_rank_ever: 189, best_score_ever: 6234 }
  },
  cross_platform_consistency: 0.82,
  active_modules: ['gaming', 'betting', 'trading'],
  performance_trends: {
    daily_scores: [],
    weekly_scores: [],
    monthly_scores: []
  },
  achievement_summary: {
    total_unlocked: 24,
    total_available: 50,
    recent_unlocks: [],
    progress_towards_next: []
  }
};

describe('UnifiedPerformanceCard', () => {
  describe('Rendering', () => {
    test('renders performance card with basic information', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={mockUserPerformance} />);
      
      // Test that the card renders basic information
      expect(screen.getByText('42')).toBeInTheDocument(); // Rank number
      expect(screen.getByText('Gold')).toBeInTheDocument(); // Tier
    });

    test('shows loading state when isLoading is true', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={null} isLoading={true} />);
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    test('displays module rankings correctly', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={mockUserPerformance} />);
      
      // Test that module information is displayed
      expect(screen.getByText('23')).toBeInTheDocument(); // Gaming rank
      expect(screen.getByText('67')).toBeInTheDocument(); // Betting rank
    });
  });

  describe('Data Display', () => {
    test('shows tier progression correctly', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={mockUserPerformance} />);
      
      // Look for percentage display
      expect(screen.getByText('68%')).toBeInTheDocument();
    });

    test('displays cross-platform consistency', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={mockUserPerformance} />);
      
      expect(screen.getByText('82%')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles null userPerformance gracefully', () => {
      renderWithProviders(<UnifiedPerformanceCard userPerformance={null} />);
      
      // Component should render without crashing
      expect(screen.getByText('No performance data')).toBeInTheDocument();
    });

    test('handles empty achievement summary', () => {
      const emptyAchievementPerformance = {
        ...mockUserPerformance,
        achievement_summary: {
          total_unlocked: 0,
          total_available: 50,
          recent_unlocks: [],
          progress_towards_next: []
        }
      };
      
      renderWithProviders(<UnifiedPerformanceCard userPerformance={emptyAchievementPerformance} />);
      
      // Should still render the component
      expect(screen.getByText('0')).toBeInTheDocument();
    });
  });
});