/**
 * LeaderboardTable Component Tests
 * ===============================
 * 
 * Comprehensive test suite for the main leaderboard table component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, test, expect, jest, beforeEach } from '@jest/globals';
import LeaderboardTable from '@/components/leaderboards/LeaderboardTable';

// Mock the utility components
jest.mock('@/components/leaderboards/TierBadge', () => {
  return function MockTierBadge({ tier }: { tier: string }) {
    return <div data-testid={`tier-badge-${tier}`}>{tier}</div>;
  };
});

jest.mock('@/components/leaderboards/RankBadge', () => {
  return function MockRankBadge({ rank, change }: { rank: number; change?: number }) {
    return (
      <div data-testid={`rank-badge-${rank}`}>
        #{rank} {change && `(${change > 0 ? '+' : ''}${change})`}
      </div>
    );
  };
});

jest.mock('@/components/leaderboards/ScoreDisplay', () => {
  return function MockScoreDisplay({ value, change }: { value: number; change?: number }) {
    return (
      <div data-testid={`score-display-${value}`}>
        {value} {change && `(${change > 0 ? '+' : ''}${change})`}
      </div>
    );
  };
});

jest.mock('@/components/leaderboards/AchievementBadge', () => {
  return function MockAchievementBadge({ achievement }: { achievement: any }) {
    return <div data-testid={`achievement-${achievement.id}`}>{achievement.name}</div>;
  };
});

jest.mock('@/components/leaderboards/MiniTrendChart', () => {
  return function MockMiniTrendChart({ data }: { data: any[] }) {
    return <div data-testid="mini-trend-chart">Chart with {data.length} points</div>;
  };
});

// Mock data
const mockLeaderboardData = [
  {
    rank: 1,
    user_id: 'user1',
    username: 'TopPlayer',
    avatar_url: '/avatars/top.jpg',
    affiliation: {
      id: 'aff1',
      name: 'Elite Team',
      logo_url: '/logos/elite.png'
    },
    unified_score: 9875.5,
    platform_tier: 'Master' as const,
    tier_progress: 0.85,
    gaming_score: 9500,
    gaming_change: 150,
    betting_score: 8750,
    betting_change: -25,
    trading_score: 9200,
    trading_change: 75,
    analyst_score: 8900,
    analyst_change: 100,
    cross_platform_consistency: 0.92,
    active_modules: ['gaming', 'betting', 'trading'],
    recent_achievements: [
      {
        id: 'ach1',
        achievement_code: 'MASTER_TRADER',
        name: 'Master Trader',
        description: 'Achieved master level in trading',
        category: 'trading',
        tier: 'Master' as const,
        rarity: 'Legendary' as const,
        points: 1000,
        icon_url: '/achievements/master.png',
        unlocked_at: '2024-01-15T10:00:00Z'
      }
    ],
    score_history: [
      { timestamp: '2024-01-01', score: 9000 },
      { timestamp: '2024-01-02', score: 9200 },
      { timestamp: '2024-01-03', score: 9875.5 }
    ],
    rank_change: 0
  },
  {
    rank: 2,
    user_id: 'user2',
    username: 'SecondPlace',
    avatar_url: '/avatars/second.jpg',
    affiliation: undefined,
    unified_score: 9200.0,
    platform_tier: 'Diamond' as const,
    tier_progress: 0.65,
    gaming_score: 8800,
    gaming_change: 50,
    betting_score: 9100,
    betting_change: 200,
    trading_score: 8500,
    trading_change: -100,
    analyst_score: 8200,
    analyst_change: 25,
    cross_platform_consistency: 0.78,
    active_modules: ['gaming', 'betting'],
    recent_achievements: [],
    score_history: [
      { timestamp: '2024-01-01', score: 8900 },
      { timestamp: '2024-01-02', score: 9000 },
      { timestamp: '2024-01-03', score: 9200 }
    ],
    rank_change: 1
  }
];

describe('LeaderboardTable', () => {
  const defaultProps = {
    data: mockLeaderboardData,
    view: 'unified' as const,
    isLoading: false,
    currentUserId: 'user1',
    onUserClick: jest.fn(),
    showAffiliation: true,
    showTrends: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering and Layout', () => {
    test('renders table with correct headers for unified view', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByText('Rank')).toBeInTheDocument();
      expect(screen.getByText('Player')).toBeInTheDocument();
      expect(screen.getByText('Tier')).toBeInTheDocument();
      expect(screen.getByText('Unified Score')).toBeInTheDocument();
      expect(screen.getByText('Gaming')).toBeInTheDocument();
      expect(screen.getByText('Betting')).toBeInTheDocument();
      expect(screen.getByText('Trading')).toBeInTheDocument();
      expect(screen.getByText('Analysis')).toBeInTheDocument();
      expect(screen.getByText('Trend')).toBeInTheDocument();
      expect(screen.getByText('Achievements')).toBeInTheDocument();
    });

    test('renders table with correct headers for gaming view', () => {
      render(<LeaderboardTable {...defaultProps} view="gaming" />);
      
      expect(screen.getByText('Gaming Score')).toBeInTheDocument();
      expect(screen.getByText('Games Played')).toBeInTheDocument();
      expect(screen.getByText('Win Rate')).toBeInTheDocument();
      expect(screen.getByText('Streak')).toBeInTheDocument();
    });

    test('renders all leaderboard entries', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByText('TopPlayer')).toBeInTheDocument();
      expect(screen.getByText('SecondPlace')).toBeInTheDocument();
    });

    test('displays user avatars correctly', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const avatars = screen.getAllByRole('img');
      expect(avatars).toHaveLength(2);
    });
  });

  describe('Data Display', () => {
    test('shows rank badges for all users', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByTestId('rank-badge-1')).toBeInTheDocument();
      expect(screen.getByTestId('rank-badge-2')).toBeInTheDocument();
    });

    test('displays tier badges correctly', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByTestId('tier-badge-Master')).toBeInTheDocument();
      expect(screen.getByTestId('tier-badge-Diamond')).toBeInTheDocument();
    });

    test('shows score displays with changes', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByTestId('score-display-9500')).toBeInTheDocument();
      expect(screen.getByTestId('score-display-8750')).toBeInTheDocument();
    });

    test('displays affiliation information when available', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByText('Elite Team')).toBeInTheDocument();
    });

    test('shows achievements for users', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByTestId('achievement-ach1')).toBeInTheDocument();
      expect(screen.getByText('Master Trader')).toBeInTheDocument();
    });

    test('displays trend charts when enabled', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const trendCharts = screen.getAllByTestId('mini-trend-chart');
      expect(trendCharts).toHaveLength(2);
    });
  });

  describe('User Interaction', () => {
    test('calls onUserClick when user row is clicked', async () => {
      const mockOnUserClick = jest.fn();
      render(<LeaderboardTable {...defaultProps} onUserClick={mockOnUserClick} />);
      
      const userRow = screen.getByText('TopPlayer').closest('tr');
      expect(userRow).toBeInTheDocument();
      
      fireEvent.click(userRow!);
      
      await waitFor(() => {
        expect(mockOnUserClick).toHaveBeenCalledWith('user1');
      });
    });

    test('highlights current user row', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const currentUserRow = screen.getByText('TopPlayer').closest('tr');
      expect(currentUserRow).toHaveClass('bg-indigo-50');
      expect(screen.getByText('You')).toBeInTheDocument();
    });

    test('applies hover effects on rows', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const userRow = screen.getByText('SecondPlace').closest('tr');
      expect(userRow).toHaveClass('hover:bg-gray-50');
    });
  });

  describe('Loading and Empty States', () => {
    test('shows loading state when isLoading is true', () => {
      render(<LeaderboardTable {...defaultProps} isLoading={true} />);
      
      expect(screen.getByText('Loading leaderboard...')).toBeInTheDocument();
      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });

    test('shows empty state when no data provided', () => {
      render(<LeaderboardTable {...defaultProps} data={[]} isLoading={false} />);
      
      expect(screen.getByText('No rankings available')).toBeInTheDocument();
      expect(screen.getByText('Check back soon for updated leaderboards')).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    test('applies responsive classes correctly', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
    });

    test('handles optional props correctly', () => {
      render(
        <LeaderboardTable
          {...defaultProps}
          showAffiliation={false}
          showTrends={false}
        />
      );
      
      // Should not show affiliation column when disabled
      expect(screen.queryByText('Elite Team')).toBeInTheDocument(); // Still in player cell
      
      // Should not show trend charts when disabled
      expect(screen.queryByTestId('mini-trend-chart')).not.toBeInTheDocument();
    });
  });

  describe('Performance and Optimization', () => {
    test('handles large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        ...mockLeaderboardData[0],
        rank: i + 1,
        user_id: `user${i + 1}`,
        username: `Player${i + 1}`,
        unified_score: 9000 - i * 10
      }));

      const { container } = render(
        <LeaderboardTable {...defaultProps} data={largeDataset} />
      );
      
      expect(container.querySelectorAll('tbody tr')).toHaveLength(100);
    });

    test('memoizes expensive operations', () => {
      const { rerender } = render(<LeaderboardTable {...defaultProps} />);
      
      // Re-render with same props should not cause unnecessary updates
      rerender(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByText('TopPlayer')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getByRole('rowgroup')).toBeInTheDocument(); // thead
      expect(screen.getAllByRole('columnheader')).toHaveLength(8); // All headers
    });

    test('supports keyboard navigation', () => {
      render(<LeaderboardTable {...defaultProps} />);
      
      const userRow = screen.getByText('TopPlayer').closest('tr');
      expect(userRow).toHaveClass('cursor-pointer');
    });
  });
});