import { renderHook, act } from '@testing-library/react';
import { useKeyboardShortcuts, useTradingKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';

describe('useKeyboardShortcuts', () => {
  let mockHandlers: { [key: string]: jest.Mock };

  beforeEach(() => {
    mockHandlers = {
      buyHandler: jest.fn(),
      sellHandler: jest.fn(),
      helpHandler: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('handles simple key shortcuts', () => {
    const shortcuts = [
      {
        key: 'b',
        handler: mockHandlers.buyHandler,
        description: 'Buy',
      },
      {
        key: 's',
        handler: mockHandlers.sellHandler,
        description: 'Sell',
      },
    ];

    renderHook(() => useKeyboardShortcuts(shortcuts));

    // Simulate 'b' key press
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1);
    expect(mockHandlers.sellHandler).not.toHaveBeenCalled();
  });

  it('handles modifier key combinations', () => {
    const shortcuts = [
      {
        key: 'c',
        ctrl: true,
        handler: mockHandlers.buyHandler,
        description: 'Copy',
      },
    ];

    renderHook(() => useKeyboardShortcuts(shortcuts));

    // Simulate 'Ctrl+C' key press
    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'c', 
        ctrlKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1);

    // Simulate 'c' without ctrl (should not trigger)
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'c' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1); // Still only 1 call
  });

  it('ignores shortcuts when typing in input fields', () => {
    const shortcuts = [
      {
        key: 'b',
        handler: mockHandlers.buyHandler,
        description: 'Buy',
      },
    ];

    renderHook(() => useKeyboardShortcuts(shortcuts));

    // Create a mock input element
    const mockInput = document.createElement('input');
    document.body.appendChild(mockInput);
    mockInput.focus();

    // Simulate 'b' key press while input is focused
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      Object.defineProperty(event, 'target', { value: mockInput });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).not.toHaveBeenCalled();

    document.body.removeChild(mockInput);
  });

  it('can be disabled', () => {
    const shortcuts = [
      {
        key: 'b',
        handler: mockHandlers.buyHandler,
        description: 'Buy',
      },
    ];

    const { rerender } = renderHook(
      ({ enabled }) => useKeyboardShortcuts(shortcuts, { enabled }),
      { initialProps: { enabled: true } }
    );

    // Should work when enabled
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1);

    // Disable shortcuts
    rerender({ enabled: false });

    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1); // Still only 1 call
  });

  it('handles case insensitive keys', () => {
    const shortcuts = [
      {
        key: 'B',
        handler: mockHandlers.buyHandler,
        description: 'Buy',
      },
    ];

    renderHook(() => useKeyboardShortcuts(shortcuts));

    // Should work with lowercase
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(1);

    // Should work with uppercase
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'B' });
      window.dispatchEvent(event);
    });

    expect(mockHandlers.buyHandler).toHaveBeenCalledTimes(2);
  });
});

describe('useTradingKeyboardShortcuts', () => {
  let mockTradingHandlers: { [key: string]: jest.Mock };

  beforeEach(() => {
    mockTradingHandlers = {
      onBuyMarket: jest.fn(),
      onSellMarket: jest.fn(),
      onBuyLimit: jest.fn(),
      onSellLimit: jest.fn(),
      onCancelAllOrders: jest.fn(),
      onCloseAllPositions: jest.fn(),
      onToggleOrderBook: jest.fn(),
      onToggleChart: jest.fn(),
      onFocusQuantity: jest.fn(),
      onFocusPrice: jest.fn(),
      onSwitchMarket: jest.fn(),
      onToggleFullscreen: jest.fn(),
    };
  });

  it('handles buy market shortcut', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onBuyMarket).toHaveBeenCalledTimes(1);
  });

  it('handles sell market shortcut', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { key: 's' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onSellMarket).toHaveBeenCalledTimes(1);
  });

  it('handles buy limit shortcut with shift', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'b', 
        shiftKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onBuyLimit).toHaveBeenCalledTimes(1);
    expect(mockTradingHandlers.onBuyMarket).not.toHaveBeenCalled();
  });

  it('handles cancel all orders shortcut', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'c', 
        ctrlKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onCancelAllOrders).toHaveBeenCalledTimes(1);
  });

  it('handles close all positions shortcut', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'x', 
        ctrlKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onCloseAllPositions).toHaveBeenCalledTimes(1);
  });

  it('handles navigation shortcuts', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    // Toggle order book
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'o' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onToggleOrderBook).toHaveBeenCalledTimes(1);

    // Toggle chart
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'k' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onToggleChart).toHaveBeenCalledTimes(1);
  });

  it('handles focus shortcuts', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    // Focus quantity
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'q' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onFocusQuantity).toHaveBeenCalledTimes(1);

    // Focus price
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'p' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onFocusPrice).toHaveBeenCalledTimes(1);
  });

  it('handles market navigation shortcuts', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    // Next market
    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'ArrowDown', 
        altKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onSwitchMarket).toHaveBeenCalledWith('next');

    // Previous market
    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'ArrowUp', 
        altKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onSwitchMarket).toHaveBeenCalledWith('prev');
  });

  it('handles fullscreen shortcut', () => {
    renderHook(() => useTradingKeyboardShortcuts(mockTradingHandlers));

    act(() => {
      const event = new KeyboardEvent('keydown', { 
        key: 'f', 
        altKey: true 
      });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onToggleFullscreen).toHaveBeenCalledTimes(1);
  });

  it('can be disabled', () => {
    renderHook(() => 
      useTradingKeyboardShortcuts({
        ...mockTradingHandlers,
        enabled: false
      })
    );

    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onBuyMarket).not.toHaveBeenCalled();
  });

  it('filters out undefined handlers', () => {
    const partialHandlers = {
      onBuyMarket: mockTradingHandlers.onBuyMarket,
      // onSellMarket is undefined
      onToggleChart: mockTradingHandlers.onToggleChart,
    };

    renderHook(() => useTradingKeyboardShortcuts(partialHandlers));

    // Should work for defined handlers
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'b' });
      window.dispatchEvent(event);
    });

    expect(mockTradingHandlers.onBuyMarket).toHaveBeenCalledTimes(1);

    // Should not crash for undefined handlers
    act(() => {
      const event = new KeyboardEvent('keydown', { key: 's' });
      window.dispatchEvent(event);
    });

    // No error should be thrown
  });
});