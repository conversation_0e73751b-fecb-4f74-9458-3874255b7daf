/**
 * useWebSocket Hook Tests
 * Critical for real-time functionality
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useAuth } from '@clerk/nextjs';

// Mock Clerk auth
jest.mock('@clerk/nextjs', () => ({
  useAuth: jest.fn(),
}));

// Mock WebSocket
class MockWebSocket {
  url: string;
  readyState: number;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;

  constructor(url: string) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    
    // Simulate connection after a delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  send(data: string) {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }
}

// Replace global WebSocket
global.WebSocket = MockWebSocket as any;

describe('useWebSocket Hook', () => {
  const mockGetToken = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({
      getToken: mockGetToken,
    });
    mockGetToken.mockResolvedValue('test-token');
  });

  it('should initialize with disconnected state', () => {
    const { result } = renderHook(() => useWebSocket(null));

    expect(result.current.isConnected).toBe(false);
    expect(result.current.isConnecting).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.lastMessage).toBeNull();
  });

  it('should connect automatically when sessionId is provided', async () => {
    const { result } = renderHook(() => useWebSocket('session-123'));

    expect(result.current.isConnecting).toBe(true);

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
      expect(result.current.isConnecting).toBe(false);
    });

    expect(mockGetToken).toHaveBeenCalled();
  });

  it('should include auth token in connection URL', async () => {
    let capturedUrl: string | undefined;
    
    // Override MockWebSocket to capture URL
    const OriginalMockWebSocket = global.WebSocket;
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        capturedUrl = url;
      }
    } as any;

    renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(capturedUrl).toContain('session-123');
      expect(capturedUrl).toContain('token=test-token');
    });

    global.WebSocket = OriginalMockWebSocket;
  });

  it('should handle connection errors', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Mock WebSocket that fails to connect
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        setTimeout(() => {
          if (this.onerror) {
            this.onerror(new Event('error'));
          }
        }, 10);
      }
    } as any;

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.error).toBe('WebSocket connection error');
    });

    consoleErrorSpy.mockRestore();
  });

  it('should parse incoming messages', async () => {
    let mockWs: MockWebSocket;
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
      }
    } as any;

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    const testMessage = {
      type: 'game_update',
      data: { score: 100 },
    };

    act(() => {
      if (mockWs.onmessage) {
        mockWs.onmessage(new MessageEvent('message', {
          data: JSON.stringify(testMessage),
        }));
      }
    });

    expect(result.current.lastMessage).toEqual(testMessage);
  });

  it('should handle malformed messages gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    let mockWs: MockWebSocket;
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
      }
    } as any;

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    act(() => {
      if (mockWs.onmessage) {
        mockWs.onmessage(new MessageEvent('message', {
          data: 'invalid json',
        }));
      }
    });

    expect(result.current.lastMessage).toBeNull();
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Failed to parse WebSocket message:',
      expect.any(Error)
    );

    consoleErrorSpy.mockRestore();
  });

  it('should send messages when connected', async () => {
    let mockWs: MockWebSocket;
    const sendSpy = jest.fn();
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
        this.send = sendSpy;
      }
    } as any;

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    const success = result.current.sendMessage('test_type', { foo: 'bar' });
    
    expect(success).toBe(true);
    expect(sendSpy).toHaveBeenCalledWith(
      JSON.stringify({
        type: 'test_type',
        data: { foo: 'bar' },
        timestamp: expect.any(String),
      })
    );
  });

  it('should not send messages when disconnected', () => {
    const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
    
    const { result } = renderHook(() => useWebSocket(null));

    const success = result.current.sendMessage('test_type', { foo: 'bar' });
    
    expect(success).toBe(false);
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'WebSocket not connected, cannot send message'
    );

    consoleWarnSpy.mockRestore();
  });

  it('should handle reconnection after disconnect', async () => {
    jest.useFakeTimers();
    
    let mockWs: MockWebSocket;
    let connectionCount = 0;
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
        connectionCount++;
      }
    } as any;

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    expect(connectionCount).toBe(1);

    // Simulate disconnect
    act(() => {
      mockWs.close();
    });

    expect(result.current.isConnected).toBe(false);

    // Fast-forward to trigger reconnection
    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(connectionCount).toBe(2);
      expect(result.current.isConnected).toBe(true);
    });

    jest.useRealTimers();
  });

  it('should limit reconnection attempts', async () => {
    jest.useFakeTimers();
    
    let connectionCount = 0;
    
    // Mock WebSocket that always fails
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        connectionCount++;
        setTimeout(() => {
          this.close();
        }, 10);
      }
    } as any;

    renderHook(() => useWebSocket('session-123', {
      maxReconnectAttempts: 3,
      reconnectInterval: 1000,
    }));

    // Trigger multiple reconnection attempts
    for (let i = 0; i < 5; i++) {
      jest.advanceTimersByTime(1500);
      await Promise.resolve();
    }

    // Should stop at max attempts + 1 (initial connection)
    expect(connectionCount).toBe(4);

    jest.useRealTimers();
  });

  it('should disconnect on unmount', async () => {
    let mockWs: MockWebSocket;
    const closeSpy = jest.fn();
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
        this.close = closeSpy;
      }
    } as any;

    const { result, unmount } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    unmount();

    expect(closeSpy).toHaveBeenCalled();
  });

  it('should handle manual connect/disconnect', async () => {
    const { result } = renderHook(() => 
      useWebSocket('session-123', { autoConnect: false })
    );

    expect(result.current.isConnected).toBe(false);
    expect(result.current.isConnecting).toBe(false);

    act(() => {
      result.current.connect();
    });

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    act(() => {
      result.current.disconnect();
    });

    expect(result.current.isConnected).toBe(false);
  });

  it('should handle auth token errors', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockGetToken.mockRejectedValue(new Error('Auth error'));

    const { result } = renderHook(() => useWebSocket('session-123'));

    await waitFor(() => {
      expect(result.current.error).toBe('Failed to establish connection');
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Failed to connect WebSocket:',
      expect.any(Error)
    );

    consoleErrorSpy.mockRestore();
  });

  it('should not connect when sessionId is null', () => {
    const { result } = renderHook(() => useWebSocket(null));

    expect(result.current.isConnected).toBe(false);
    expect(result.current.isConnecting).toBe(false);
    
    act(() => {
      result.current.connect();
    });

    expect(result.current.isConnecting).toBe(false);
  });

  it('should handle custom callbacks', async () => {
    const onOpen = jest.fn();
    const onClose = jest.fn();
    const onMessage = jest.fn();
    const onError = jest.fn();

    let mockWs: MockWebSocket;
    
    global.WebSocket = class extends MockWebSocket {
      constructor(url: string) {
        super(url);
        mockWs = this;
      }
    } as any;

    const { result } = renderHook(() => 
      useWebSocket('session-123', {
        onOpen,
        onClose,
        onMessage,
        onError,
      })
    );

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    expect(onOpen).toHaveBeenCalled();

    // Test message callback
    const testMessage = { type: 'test', data: {} };
    act(() => {
      if (mockWs.onmessage) {
        mockWs.onmessage(new MessageEvent('message', {
          data: JSON.stringify(testMessage),
        }));
      }
    });

    expect(onMessage).toHaveBeenCalledWith(testMessage);

    // Test close callback
    act(() => {
      mockWs.close();
    });

    expect(onClose).toHaveBeenCalled();
  });
});