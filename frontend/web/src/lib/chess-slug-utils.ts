/**
 * Chess Game Slug Utilities
 * =========================
 * 
 * Utilities for generating and parsing chess game slugs.
 * 
 * Slug Format:
 * - With both players: chess-{variant}-{time_control}-{white_username}-vs-{black_username}-{date}-{time}
 * - Waiting for player: chess-{variant}-{time_control}-{white_username}-vs-waiting-for-opponent
 * 
 * Examples:
 * - chess-standard-blitz-tapiwanashe-vs-maposhere-29072025-1400
 * - chess-standard-blitz-tapiwanashe-vs-waiting-for-opponent
 */

export interface ChessGame {
  id: string;
  variant: string;
  time_control: string;
  white_player: {
    username: string;
    rating?: number;
  } | null;
  black_player: {
    username: string;
    rating?: number;
  } | null;
  created_at: string;
  status: string;
}

/**
 * Sanitizes a username for use in URLs
 */
function sanitizeUsername(username: string): string {
  return username
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric characters
    .substring(0, 15); // Limit length
}

/**
 * Formats a date for use in slugs (DDMMYYYY-HHMM)
 */
function formatDateForSlug(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${day}${month}${year}-${hours}${minutes}`;
}

/**
 * Generates a slug for a chess game
 */
export function generateChessGameSlug(game: ChessGame): string {
  const variant = game.variant.toLowerCase();
  const timeControl = game.time_control.toLowerCase().replace('_', '');
  
  const whiteUsername = game.white_player 
    ? sanitizeUsername(game.white_player.username)
    : 'unknown';
  
  const blackUsername = game.black_player
    ? sanitizeUsername(game.black_player.username)
    : 'waiting-for-opponent';
  
  // If both players are present, include date/time
  if (game.black_player) {
    const dateTime = formatDateForSlug(game.created_at);
    return `chess-${variant}-${timeControl}-${whiteUsername}-vs-${blackUsername}-${dateTime}`;
  } else {
    // If waiting for opponent, don't include date/time
    return `chess-${variant}-${timeControl}-${whiteUsername}-vs-${blackUsername}`;
  }
}

/**
 * Parses a chess game slug to extract information
 */
export function parseChessGameSlug(slug: string): {
  variant: string;
  timeControl: string;
  whiteUsername: string;
  blackUsername: string;
  isWaitingForOpponent: boolean;
  dateTime?: string;
} | null {
  // Remove 'chess-' prefix if present
  const cleanSlug = slug.startsWith('chess-') ? slug.substring(6) : slug;
  
  // Split by '-vs-' to separate players
  const parts = cleanSlug.split('-vs-');
  if (parts.length !== 2) return null;
  
  const [leftPart, rightPart] = parts;
  
  // Parse left part: variant-timecontrol-username
  const leftParts = leftPart.split('-');
  if (leftParts.length < 3) return null;
  
  const variant = leftParts[0];
  const timeControl = leftParts[1];
  const whiteUsername = leftParts.slice(2).join('-'); // Handle usernames with dashes
  
  // Parse right part: username or waiting-for-opponent, optionally with date-time
  const isWaitingForOpponent = rightPart.startsWith('waiting-for-opponent');
  
  if (isWaitingForOpponent) {
    return {
      variant,
      timeControl,
      whiteUsername,
      blackUsername: 'waiting-for-opponent',
      isWaitingForOpponent: true
    };
  } else {
    // Parse black username and optional date-time
    const rightParts = rightPart.split('-');
    
    // Look for date pattern (DDMMYYYY-HHMM)
    let blackUsername = '';
    let dateTime = '';
    
    // Find where the date starts (8 digits followed by dash and 4 digits)
    for (let i = 0; i < rightParts.length - 1; i++) {
      if (rightParts[i].length === 8 && /^\d{8}$/.test(rightParts[i]) &&
          rightParts[i + 1].length === 4 && /^\d{4}$/.test(rightParts[i + 1])) {
        blackUsername = rightParts.slice(0, i).join('-');
        dateTime = `${rightParts[i]}-${rightParts[i + 1]}`;
        break;
      }
    }
    
    // If no date found, entire right part is username
    if (!dateTime) {
      blackUsername = rightPart;
    }
    
    return {
      variant,
      timeControl,
      whiteUsername,
      blackUsername,
      isWaitingForOpponent: false,
      dateTime
    };
  }
}

/**
 * Generates a URL for a chess game
 */
export function getChessGameUrl(game: ChessGame): string {
  const slug = generateChessGameSlug(game);
  return `/games/chess/${slug}`;
}

/**
 * Checks if a slug matches a chess game
 */
export function doesSlugMatchGame(slug: string, game: ChessGame): boolean {
  const expectedSlug = generateChessGameSlug(game);
  return slug === expectedSlug;
}
