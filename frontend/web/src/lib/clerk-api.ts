/**
 * BetBet Platform - Clerk Authentication Integration
 * =================================================
 * 
 * Clean wrapper around the unified API client for Clerk authentication.
 * This is the ONLY way to make authenticated API calls.
 * 
 * ✅ Use this for: Clerk-authenticated API calls
 * ❌ Don't use: Direct service calls, multiple API clients
 */

import api from './api-client-unified';

// Re-export types from existing API
export * from './api';

/**
 * Clerk-authenticated API service
 * Handles token management and authenticated requests
 */
export class ClerkApiService {
  private getTokenFn: (() => Promise<string | null>) | null = null;

  /**
   * Initialize with Clerk's getToken function
   */
  initialize(getToken: () => Promise<string | null>) {
    this.getTokenFn = getToken;
    this.setupTokenRefresh();
  }

  /**
   * Setup automatic token refresh
   */
  private async setupTokenRefresh() {
    if (!this.getTokenFn) return;

    try {
      const token = await this.getTokenFn();
      if (token) {
        api.setAuthToken(token);
        console.log('Clerk token initialized');
      }
    } catch (error) {
      console.error('Failed to initialize Clerk token:', error);
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken() {
    if (!this.getTokenFn) {
      console.warn('Clerk getToken function not initialized');
      return;
    }

    try {
      const token = await this.getTokenFn();
      if (token) {
        api.setAuthToken(token);
        console.log('Clerk token refreshed');
      }
    } catch (error) {
      console.error('Failed to refresh Clerk token:', error);
    }
  }

  // ===================================
  // GAMING API METHODS
  // ===================================

  /**
   * Get user gaming statistics
   */
  async getUserStats(userId: string) {
    await this.refreshToken();
    return api.gaming.getUserStats(userId);
  }

  /**
   * Get user gaming activity
   */
  async getUserActivity(userId: string) {
    await this.refreshToken();
    return api.gaming.getUserActivity(userId);
  }

  /**
   * Get user active sessions
   */
  async getUserActiveSessions(userId: string) {
    await this.refreshToken();
    try {
      const response = await api.client.get(`/api/gaming/analytics/users/${userId}/active-sessions`);
      return response.data;
    } catch (error) {
      console.error('Failed to get user active sessions:', error);
      throw error;
    }
  }

  /**
   * Get gaming overview analytics
   */
  async getPlatformOverview() {
    return api.gaming.getOverview();
  }

  /**
   * Get available games
   */
  async getGames(params?: any) {
    return api.gaming.getGames(params);
  }

  /**
   * Get game sessions
   */
  async getSessions(params?: any) {
    return api.gaming.getSessions(params);
  }

  /**
   * Join a gaming session
   */
  async joinSession(sessionId: string) {
    await this.refreshToken();
    return api.gaming.joinSession(sessionId);
  }

  /**
   * Create a gaming session
   */
  async createSession(sessionData: any) {
    await this.refreshToken();
    return api.gaming.createSession(sessionData);
  }

  // ===================================
  // WALLET API METHODS
  // ===================================

  /**
   * Get user wallet balance
   */
  async getWalletBalance(userId: string) {
    await this.refreshToken();
    return api.wallet.getBalance(userId);
  }

  /**
   * Get user transactions
   */
  async getTransactions(userId: string, params?: any) {
    await this.refreshToken();
    return api.wallet.getTransactions(userId, params);
  }

  /**
   * Create deposit
   */
  async createDeposit(depositData: any) {
    await this.refreshToken();
    return api.wallet.createDeposit(depositData);
  }

  /**
   * Create withdrawal
   */
  async createWithdrawal(withdrawalData: any) {
    await this.refreshToken();
    return api.wallet.createWithdrawal(withdrawalData);
  }

  // ===================================
  // EXPERT API METHODS
  // ===================================

  /**
   * Get experts list
   */
  async getExperts(params?: any) {
    return api.experts.getExperts(params);
  }

  /**
   * Get expert details
   */
  async getExpert(expertId: string) {
    return api.experts.getExpert(expertId);
  }

  /**
   * Get expert performance stats
   */
  async getExpertStats(expertId: string) {
    return api.experts.getExpertStats(expertId);
  }

  // ===================================
  // LEADERBOARDS API METHODS
  // ===================================

  /**
   * Get unified leaderboard
   */
  async getLeaderboard(params?: any) {
    return api.leaderboards.getUnifiedLeaderboard(params);
  }

  /**
   * Get user achievements
   */
  async getUserAchievements(userId: string) {
    return api.leaderboards.getUserAchievements(userId);
  }

  // ===================================
  // SPORTS API METHODS
  // ===================================

  /**
   * Get sports fixtures
   */
  async getFixtures(params?: any) {
    return api.sports.getFixtures(params);
  }

  /**
   * Get sports teams
   */
  async getTeams(params?: any) {
    return api.sports.getTeams(params);
  }

  /**
   * Get sports leagues
   */
  async getLeagues(params?: any) {
    return api.sports.getLeagues(params);
  }

  // ===================================
  // CUSTOM BETTING API METHODS
  // ===================================

  /**
   * Get betting markets
   */
  async getMarkets(params?: any) {
    return api.customBetting.getMarkets(params);
  }

  /**
   * Create betting position
   */
  async createPosition(positionData: any) {
    await this.refreshToken();
    return api.customBetting.createPosition(positionData);
  }

  // ===================================
  // TRADING API METHODS
  // ===================================

  /**
   * Get trading markets
   */
  async getTradingMarkets(params?: any) {
    return api.trading.getMarkets(params);
  }

  /**
   * Create trading order
   */
  async createOrder(orderData: any) {
    await this.refreshToken();
    return api.trading.createOrder(orderData);
  }

  // ===================================
  // SYSTEM METHODS
  // ===================================

  /**
   * Health check all services
   */
  async healthCheck() {
    return api.system.healthAll();
  }
}

// Export singleton instance
export const clerkApi = new ClerkApiService();

// Export the unified API client as default
export default api;

// Legacy compatibility exports (deprecated)
export const createAuthenticatedApiClient = (getToken: () => Promise<string | null>) => {
  clerkApi.initialize(getToken);
  return api.client;
};

export const createPublicApiClient = () => api.client;
export const createGamingApiClient = () => api.client;
