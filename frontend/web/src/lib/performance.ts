/**
 * BetBet Gaming Engine - Performance Monitoring & Optimization
 * ===========================================================
 * 
 * Performance monitoring utilities and optimization helpers.
 */

// Performance timing utilities
export class PerformanceMonitor {
  private static measurements = new Map<string, number>();

  static startMeasurement(name: string): void {
    this.measurements.set(name, performance.now());
  }

  static endMeasurement(name: string): number {
    const startTime = this.measurements.get(name);
    if (!startTime) {
      console.warn(`No start measurement found for: ${name}`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    this.measurements.delete(name);
    
    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }
    
    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && duration > 100) {
      this.reportPerformanceMetric(name, duration);
    }
    
    return duration;
  }

  private static reportPerformanceMetric(name: string, duration: number): void {
    // TODO: Send to analytics service (e.g., Google Analytics, New Relic)
    console.log(`Performance metric: ${name} took ${duration.toFixed(2)}ms`);
  }

  static measureAsyncOperation<T>(
    name: string, 
    operation: () => Promise<T>
  ): Promise<T> {
    this.startMeasurement(name);
    return operation().finally(() => {
      this.endMeasurement(name);
    });
  }
}

// React component performance wrapper
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return function TrackedComponent(props: P) {
    React.useEffect(() => {
      PerformanceMonitor.startMeasurement(`${componentName}_render`);
      return () => {
        PerformanceMonitor.endMeasurement(`${componentName}_render`);
      };
    }, []);

    return React.createElement(Component, props);
  };
}

// WebSocket performance monitoring
export class WebSocketPerformanceTracker {
  private connectionStart: number = 0;
  private messagesSent: number = 0;
  private messagesReceived: number = 0;
  private totalLatency: number = 0;
  private latencyMeasurements: number = 0;

  onConnectionStart(): void {
    this.connectionStart = performance.now();
  }

  onConnectionEstablished(): void {
    if (this.connectionStart > 0) {
      const connectionTime = performance.now() - this.connectionStart;
      console.log(`🔌 WebSocket connected in ${connectionTime.toFixed(2)}ms`);
    }
  }

  onMessageSent(): void {
    this.messagesSent++;
  }

  onMessageReceived(latency?: number): void {
    this.messagesReceived++;
    if (latency) {
      this.totalLatency += latency;
      this.latencyMeasurements++;
    }
  }

  getStats() {
    return {
      messagesSent: this.messagesSent,
      messagesReceived: this.messagesReceived,
      averageLatency: this.latencyMeasurements > 0 
        ? this.totalLatency / this.latencyMeasurements 
        : 0
    };
  }
}

// API request performance tracking
export function trackApiRequest<T>(
  requestName: string,
  request: () => Promise<T>
): Promise<T> {
  return PerformanceMonitor.measureAsyncOperation(
    `api_${requestName}`,
    request
  );
}

// Memory usage monitoring
export class MemoryMonitor {
  static checkMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
      
      console.log(`📊 Memory: ${usedMB}MB / ${limitMB}MB`);
      
      // Warn if memory usage is high
      if (usedMB / limitMB > 0.8) {
        console.warn('⚠️ High memory usage detected');
      }
    }
  }

  static startMemoryMonitoring(intervalMs: number = 30000): () => void {
    const interval = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs);

    return () => clearInterval(interval);
  }
}

// Bundle size optimization helpers
export const lazyLoad = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  return React.lazy(importFn);
};

// Image optimization
export const optimizeImage = (src: string, width?: number, height?: number): string => {
  // In production, this could integrate with image optimization services
  if (!src) return '';
  
  // Add width/height parameters for optimization
  const url = new URL(src, window.location.origin);
  if (width) url.searchParams.set('w', width.toString());
  if (height) url.searchParams.set('h', height.toString());
  
  return url.toString();
};

// Virtual scrolling performance helpers
export const useVirtualization = (
  items: any[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );
  
  const visibleItems = items.slice(startIndex, endIndex);
  const offsetY = startIndex * itemHeight;
  
  return {
    visibleItems,
    offsetY,
    totalHeight: items.length * itemHeight,
    setScrollTop
  };
};

// Production-ready error reporting
export const reportError = (error: Error, context?: string): void => {
  if (process.env.NODE_ENV === 'production') {
    // TODO: Send to error reporting service (e.g., Sentry)
    console.error('Production error:', error, context);
  } else {
    console.error('Development error:', error, context);
  }
};

// Performance budgets and monitoring
export const PERFORMANCE_BUDGETS = {
  API_REQUEST_MAX_TIME: 2000, // 2 seconds
  COMPONENT_RENDER_MAX_TIME: 100, // 100ms
  WEBSOCKET_CONNECTION_MAX_TIME: 5000, // 5 seconds
  MEMORY_USAGE_THRESHOLD: 0.8 // 80% of heap limit
};

export const checkPerformanceBudget = (metric: string, value: number): boolean => {
  const budget = PERFORMANCE_BUDGETS[metric as keyof typeof PERFORMANCE_BUDGETS];
  if (budget && value > budget) {
    console.warn(`⚠️ Performance budget exceeded for ${metric}: ${value} > ${budget}`);
    return false;
  }
  return true;
};