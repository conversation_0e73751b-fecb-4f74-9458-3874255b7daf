/**
 * Railway WebSocket Configuration
 * ==============================
 * 
 * Optimized WebSocket connection handling for Railway deployment
 * with proper reconnection and error handling for production environment.
 */

import { io, Socket } from 'socket.io-client';

interface RailwayWebSocketConfig {
  backendUrl: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  timeout: number;
}

class RailwayWebSocketManager {
  private socket: Socket | null = null;
  private config: RailwayWebSocketConfig;
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnected = false;
  
  constructor(config: RailwayWebSocketConfig) {
    this.config = config;
  }
  
  connect(sessionId?: string): Promise<Socket> {
    return new Promise((resolve, reject) => {
      try {
        // Disconnect existing connection
        if (this.socket) {
          this.disconnect();
        }
        
        const wsUrl = this.config.backendUrl.replace(/^https?:/, 'wss:');
        const socketPath = sessionId ? `/ws/gaming/${sessionId}` : '/ws/gaming';
        
        console.log(`🔌 Connecting to Railway WebSocket: ${wsUrl}${socketPath}`);
        
        this.socket = io(wsUrl, {
          path: socketPath,
          transports: ['websocket', 'polling'], // Fallback to polling if WebSocket fails
          timeout: this.config.timeout,
          reconnection: true,
          reconnectionAttempts: this.config.maxReconnectAttempts,
          reconnectionDelay: this.config.reconnectInterval,
          forceNew: true,
          upgrade: true,
          rememberUpgrade: true
        });
        
        // Connection successful
        this.socket.on('connect', () => {
          console.log('✅ Railway WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve(this.socket!);
        });
        
        // Connection error
        this.socket.on('connect_error', (error) => {
          console.error('❌ Railway WebSocket connection error:', error);
          this.isConnected = false;
          
          if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            reject(new Error(`Failed to connect after ${this.config.maxReconnectAttempts} attempts`));
          } else {
            this.reconnectAttempts++;
            console.log(`🔄 Retrying connection (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`);
          }
        });
        
        // Disconnection
        this.socket.on('disconnect', (reason) => {
          console.log('🔌 Railway WebSocket disconnected:', reason);
          this.isConnected = false;
          this.stopHeartbeat();
          
          // Auto-reconnect on unexpected disconnection
          if (reason === 'io server disconnect') {
            // Server initiated disconnect, don't auto-reconnect
            console.log('🚫 Server initiated disconnect - not auto-reconnecting');
          } else {
            // Client-side disconnect or network issue, attempt reconnection
            console.log('🔄 Attempting auto-reconnection...');
          }
        });
        
        // Real-time game updates
        this.socket.on('game_state_update', (data) => {
          this.handleGameStateUpdate(data);
        });
        
        // Betting updates
        this.socket.on('betting_update', (data) => {
          this.handleBettingUpdate(data);
        });
        
        // Session updates
        this.socket.on('session_update', (data) => {
          this.handleSessionUpdate(data);
        });
        
        // Error handling
        this.socket.on('error', (error) => {
          console.error('❌ Railway WebSocket error:', error);
        });
        
      } catch (error) {
        console.error('❌ Failed to create Railway WebSocket connection:', error);
        reject(error);
      }
    });
  }
  
  disconnect(): void {
    if (this.socket) {
      console.log('🔌 Disconnecting Railway WebSocket...');
      this.stopHeartbeat();
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }
  
  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.isConnected) {
        this.socket.emit('heartbeat', { timestamp: Date.now() });
      }
    }, this.config.heartbeatInterval);
  }
  
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
  
  private handleGameStateUpdate(data: any): void {
    // Emit custom event for game state updates
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('railway-game-update', { detail: data }));
    }
  }
  
  private handleBettingUpdate(data: any): void {
    // Emit custom event for betting updates
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('railway-betting-update', { detail: data }));
    }
  }
  
  private handleSessionUpdate(data: any): void {
    // Emit custom event for session updates
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('railway-session-update', { detail: data }));
    }
  }
  
  // Utility methods
  isConnectionActive(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }
  
  emit(event: string, data: any): void {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    } else {
      console.warn('⚠️ Cannot emit - Railway WebSocket not connected');
    }
  }
  
  on(event: string, callback: (data: any) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }
  
  off(event: string, callback?: (data: any) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}

// Create Railway WebSocket instance with production configuration
export const createRailwayWebSocket = (): RailwayWebSocketManager => {
  const config: RailwayWebSocketConfig = {
    backendUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://gaming-engine-production.up.railway.app',
    reconnectInterval: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_INTERVAL || '5000'),
    maxReconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_MAX_RECONNECT_ATTEMPTS || '5'),
    heartbeatInterval: parseInt(process.env.NEXT_PUBLIC_WS_HEARTBEAT_INTERVAL || '30000'),
    timeout: 10000
  };
  
  return new RailwayWebSocketManager(config);
};

// Export types
export type { RailwayWebSocketConfig };
export { RailwayWebSocketManager };