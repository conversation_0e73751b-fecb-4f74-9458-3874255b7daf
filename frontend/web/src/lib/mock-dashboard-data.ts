/**
 * Mock Dashboard Data Service
 * ===========================
 * 
 * Provides fallback data for dashboard when analytics endpoints are not available.
 * Generates realistic mock data for development and testing purposes.
 */

export interface DashboardStats {
  gaming: {
    total_games: number;
    games_won: number;
    games_lost: number;
    win_rate: number;
    total_earnings: number;
    current_streak: number;
    favorite_game: string;
  };
  betting: {
    total_bets: number;
    bets_won: number;
    bets_lost: number;
    win_rate: number;
    total_wagered: number;
    total_winnings: number;
    profit_loss: number;
    biggest_win: number;
  };
  trading: {
    total_trades: number;
    profitable_trades: number;
    losing_trades: number;
    win_rate: number;
    total_volume: number;
    total_pnl: number;
    best_trade: number;
    worst_trade: number;
  };
  wallet: {
    available_balance: number;
    locked_balance: number;
    total_deposited: number;
    total_withdrawn: number;
    lifetime_winnings: number;
    lifetime_losses: number;
  };
  leaderboard: {
    global_rank: number;
    tier: string;
    tier_progress: number;
    points: number;
    achievements_unlocked: number;
    total_achievements: number;
  };
  activity: Array<{
    id: string;
    type: string;
    description: string;
    amount?: number;
    timestamp: string;
    module: string;
  }>;
}

export function generateMockDashboardData(userId: string): DashboardStats {
  // Generate consistent but varied data based on user ID
  const seed = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const random = (min: number, max: number) => {
    const x = Math.sin(seed * 9999) * 10000;
    return Math.floor((x - Math.floor(x)) * (max - min + 1)) + min;
  };
  
  const randomFloat = (min: number, max: number) => {
    const x = Math.sin(seed * 7777) * 10000;
    return (x - Math.floor(x)) * (max - min) + min;
  };
  
  // Gaming stats
  const totalGames = random(50, 500);
  const gamesWon = random(Math.floor(totalGames * 0.3), Math.floor(totalGames * 0.7));
  const gamesLost = totalGames - gamesWon;
  
  // Betting stats
  const totalBets = random(20, 200);
  const betsWon = random(Math.floor(totalBets * 0.25), Math.floor(totalBets * 0.6));
  const betsLost = totalBets - betsWon;
  const totalWagered = randomFloat(1000, 10000);
  const totalWinnings = randomFloat(totalWagered * 0.8, totalWagered * 1.5);
  
  // Trading stats
  const totalTrades = random(10, 100);
  const profitableTrades = random(Math.floor(totalTrades * 0.3), Math.floor(totalTrades * 0.7));
  const losingTrades = totalTrades - profitableTrades;
  const totalVolume = randomFloat(5000, 50000);
  const totalPnl = randomFloat(-2000, 5000);
  
  // Wallet stats
  const availableBalance = randomFloat(100, 5000);
  const lockedBalance = randomFloat(0, 1000);
  const totalDeposited = randomFloat(1000, 20000);
  const totalWithdrawn = randomFloat(500, totalDeposited * 0.8);
  
  // Leaderboard stats
  const globalRank = random(1, 10000);
  const tiers = ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Master'];
  const tierIndex = Math.min(Math.floor(globalRank / 2000), tiers.length - 1);
  const tier = tiers[tierIndex];
  const points = random(1000, 50000);
  const achievementsUnlocked = random(5, 50);
  
  // Recent activity
  const activityTypes = [
    { type: 'game_completed', module: 'gaming', descriptions: ['Won chess match', 'Completed poker game', 'Finished tournament'] },
    { type: 'bet_placed', module: 'betting', descriptions: ['Placed sports bet', 'Bet on market outcome', 'Wagered on event'] },
    { type: 'trade_executed', module: 'trading', descriptions: ['Executed buy order', 'Sold position', 'Closed trade'] },
    { type: 'deposit', module: 'wallet', descriptions: ['Deposited funds', 'Added balance', 'Funded account'] },
    { type: 'achievement', module: 'leaderboard', descriptions: ['Unlocked achievement', 'Reached milestone', 'Earned badge'] }
  ];
  
  const activity = Array.from({ length: 10 }, (_, i) => {
    const activityType = activityTypes[random(0, activityTypes.length - 1)];
    const description = activityType.descriptions[random(0, activityType.descriptions.length - 1)];
    const amount = ['deposit', 'bet_placed', 'trade_executed'].includes(activityType.type) 
      ? randomFloat(10, 1000) 
      : undefined;
    
    return {
      id: `activity_${i}_${seed}`,
      type: activityType.type,
      description,
      amount,
      timestamp: new Date(Date.now() - random(0, 7 * 24 * 60 * 60 * 1000)).toISOString(),
      module: activityType.module
    };
  }).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  return {
    gaming: {
      total_games: totalGames,
      games_won: gamesWon,
      games_lost: gamesLost,
      win_rate: totalGames > 0 ? (gamesWon / totalGames) * 100 : 0,
      total_earnings: randomFloat(500, 5000),
      current_streak: random(0, 10),
      favorite_game: ['Chess', 'Poker', 'Blackjack', 'Tournament'][random(0, 3)]
    },
    betting: {
      total_bets: totalBets,
      bets_won: betsWon,
      bets_lost: betsLost,
      win_rate: totalBets > 0 ? (betsWon / totalBets) * 100 : 0,
      total_wagered: totalWagered,
      total_winnings: totalWinnings,
      profit_loss: totalWinnings - totalWagered,
      biggest_win: randomFloat(100, 2000)
    },
    trading: {
      total_trades: totalTrades,
      profitable_trades: profitableTrades,
      losing_trades: losingTrades,
      win_rate: totalTrades > 0 ? (profitableTrades / totalTrades) * 100 : 0,
      total_volume: totalVolume,
      total_pnl: totalPnl,
      best_trade: randomFloat(100, 1000),
      worst_trade: randomFloat(-500, -50)
    },
    wallet: {
      available_balance: availableBalance,
      locked_balance: lockedBalance,
      total_deposited: totalDeposited,
      total_withdrawn: totalWithdrawn,
      lifetime_winnings: randomFloat(2000, 15000),
      lifetime_losses: randomFloat(1000, 8000)
    },
    leaderboard: {
      global_rank: globalRank,
      tier,
      tier_progress: randomFloat(0, 100),
      points,
      achievements_unlocked: achievementsUnlocked,
      total_achievements: 75
    },
    activity
  };
}

export function getMockUserStats(userId: string) {
  const mockData = generateMockDashboardData(userId);
  
  return {
    user_id: userId,
    gaming_stats: mockData.gaming,
    betting_stats: mockData.betting,
    trading_stats: mockData.trading,
    wallet_stats: mockData.wallet,
    leaderboard_stats: mockData.leaderboard,
    recent_activity: mockData.activity,
    last_updated: new Date().toISOString()
  };
}

export function createWelcomeEvents(userId: string) {
  return [
    {
      event_type: 'user_registration',
      user_id: userId,
      title: 'Welcome to BetBet!',
      description: 'Your account has been successfully created. Start exploring our platform!',
      data: { welcome_bonus: 100 },
      priority: 'high',
      channels: ['in_app', 'websocket']
    },
    {
      event_type: 'achievement_unlocked',
      user_id: userId,
      title: 'First Steps Achievement',
      description: 'Congratulations! You have unlocked your first achievement.',
      data: { achievement_id: 'first_steps', points: 50 },
      priority: 'medium',
      channels: ['in_app', 'websocket']
    }
  ];
}
