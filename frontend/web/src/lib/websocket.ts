/**
 * BetBet Gaming Engine - WebSocket Client
 * =======================================
 * 
 * Real-time communication client for gaming sessions and spectator mode.
 */

import { io, Socket } from 'socket.io-client';

export interface WebSocketMessage {
  type: MessageType;
  data: any;
  timestamp: string;
  sender_id?: string;
}

export enum MessageType {
  GAME_STATE = "game_state",
  GAME_ACTION = "game_action", 
  CHAT = "chat",
  PARTICIPANT_EVENT = "participant_event",
  HEARTBEAT = "heartbeat",
  ERROR = "error"
}

export interface GameStateData {
  session: {
    id: string;
    name: string;
    status: string;
    current_participants: number;
    max_participants: number;
    entry_fee: number;
    total_prize_pool: number;
  };
  participants: Array<{
    user_id: string;
    status: string;
    is_spectator: boolean;
    join_order: number;
    current_score: number;
    is_connected: boolean;
  }>;
  game_state?: {
    round_number: number;
    state_data: any;
    turn_order: string[];
    current_turn_user_id?: string;
  };
}

export interface GameAction {
  action_type: string;
  action_data: any;
}

export interface ChatMessage {
  user_id: string;
  message: string;
  timestamp: string;
}

class WebSocketClient {
  private socket: Socket | null = null;
  private sessionId: string | null = null;
  private token: string | null = null;
  private isSpectator: boolean = false;
  private connectionRetries: number = 0;
  private maxRetries: number = 5;
  private retryDelay: number = 1000;

  // Event listeners
  private onGameStateListener?: (data: GameStateData) => void;
  private onGameActionListener?: (data: any) => void;
  private onChatMessageListener?: (data: ChatMessage) => void;
  private onParticipantEventListener?: (data: any) => void;
  private onConnectionStatusListener?: (connected: boolean) => void;
  private onErrorListener?: (error: string) => void;

  constructor() {
    // Initialize with empty handlers
  }

  // Connection management
  connectToSession(sessionId: string, token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.sessionId = sessionId;
      this.token = token;
      this.isSpectator = false;

      const wsUrl = process.env.NEXT_PUBLIC_WS_BASE_URL || 'ws://localhost:8000';

      // All WebSocket connections go through API Gateway
      const wsEndpoint = `${wsUrl}/ws/sessions/${sessionId}?token=${token}`;
      
      try {
        // Create WebSocket connection
        const ws = new WebSocket(wsEndpoint);
        
        ws.onopen = () => {
          console.log('Connected to gaming session:', sessionId);
          this.connectionRetries = 0;
          this.onConnectionStatusListener?.(true);
          resolve();
        };

        ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        ws.onclose = () => {
          console.log('Disconnected from gaming session');
          this.onConnectionStatusListener?.(false);
          this.attemptReconnection();
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.onErrorListener?.('Connection failed');
          reject(error);
        };

        // Store the WebSocket (cast to Socket for compatibility)
        this.socket = ws as any;

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  connectAsSpectator(sessionId: string, token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.sessionId = sessionId;
      this.token = token || null;
      this.isSpectator = true;

      const wsUrl = process.env.NEXT_PUBLIC_WS_BASE_URL || 'ws://localhost:8000';
      const wsEndpoint = token
        ? `${wsUrl}/ws/spectate/${sessionId}?token=${token}`
        : `${wsUrl}/ws/spectate/${sessionId}`;
      
      try {
        const ws = new WebSocket(wsEndpoint);
        
        ws.onopen = () => {
          console.log('Connected as spectator to session:', sessionId);
          this.connectionRetries = 0;
          this.onConnectionStatusListener?.(true);
          resolve();
        };

        ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        ws.onclose = () => {
          console.log('Disconnected from spectator mode');
          this.onConnectionStatusListener?.(false);
          this.attemptReconnection();
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.onErrorListener?.('Connection failed');
          reject(error);
        };

        this.socket = ws as any;

      } catch (error) {
        console.error('Failed to create spectator WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.socket) {
      (this.socket as any).close();
      this.socket = null;
    }
    this.sessionId = null;
    this.token = null;
    this.connectionRetries = 0;
  }

  // Message handling
  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case MessageType.GAME_STATE:
        this.onGameStateListener?.(message.data);
        break;
      case MessageType.GAME_ACTION:
        this.onGameActionListener?.(message.data);
        break;
      case MessageType.CHAT:
        this.onChatMessageListener?.(message.data);
        break;
      case MessageType.PARTICIPANT_EVENT:
        this.onParticipantEventListener?.(message.data);
        break;
      case MessageType.ERROR:
        this.onErrorListener?.(message.data.message || 'Unknown error');
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  // Send messages (only for participants, not spectators)
  sendGameAction(action: GameAction): void {
    if (this.isSpectator) {
      console.warn('Spectators cannot send game actions');
      return;
    }

    this.sendMessage({
      type: "game_action",
      payload: action
    });
  }

  sendChatMessage(message: string): void {
    if (this.isSpectator) {
      console.warn('Spectators cannot send chat messages');
      return;
    }

    this.sendMessage({
      type: "chat_message",
      payload: { message }
    });
  }

  sendReadyStatus(ready: boolean): void {
    if (this.isSpectator) {
      console.warn('Spectators cannot set ready status');
      return;
    }

    this.sendMessage({
      type: "ready_status",
      payload: { ready }
    });
  }

  private sendMessage(data: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      (this.socket as any).send(JSON.stringify(data));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  // Connection retry logic
  private attemptReconnection(): void {
    if (this.connectionRetries >= this.maxRetries) {
      console.error('Max reconnection attempts reached');
      this.onErrorListener?.('Connection lost - max retries exceeded');
      return;
    }

    this.connectionRetries++;
    
    setTimeout(() => {
      console.log(`Attempting reconnection ${this.connectionRetries}/${this.maxRetries}`);
      
      if (this.sessionId && this.token) {
        if (this.isSpectator) {
          this.connectAsSpectator(this.sessionId, this.token);
        } else {
          this.connectToSession(this.sessionId, this.token);
        }
      }
    }, this.retryDelay * this.connectionRetries);
  }

  // Event listener setters
  onGameState(listener: (data: GameStateData) => void): void {
    this.onGameStateListener = listener;
  }

  onGameAction(listener: (data: any) => void): void {
    this.onGameActionListener = listener;
  }

  onChatMessage(listener: (data: ChatMessage) => void): void {
    this.onChatMessageListener = listener;
  }

  onParticipantEvent(listener: (data: any) => void): void {
    this.onParticipantEventListener = listener;
  }

  onConnectionStatus(listener: (connected: boolean) => void): void {
    this.onConnectionStatusListener = listener;
  }

  onError(listener: (error: string) => void): void {
    this.onErrorListener = listener;
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;
  }

  getSessionId(): string | null {
    return this.sessionId;
  }

  getSpectatorMode(): boolean {
    return this.isSpectator;
  }
}

// Export singleton instance
export const webSocketClient = new WebSocketClient();

export default webSocketClient;