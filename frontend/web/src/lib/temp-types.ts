/**
 * Temporary type definitions to prevent TypeScript errors
 * TODO: Replace with proper types from unified API client
 */

export interface Expert {
  id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  specializations: string[];
  verification_status: 'pending' | 'verified' | 'rejected';
  overall_rating: number;
  total_subscribers: number;
  total_picks: number;
  win_rate: number;
  roi: number;
  streak_type: 'win' | 'loss';
  current_streak: number;
  created_at: string;
  updated_at: string;
}

export interface Pick {
  id: string;
  expert_id: string;
  title: string;
  description: string;
  sport: string;
  league: string;
  match_date: string;
  pick_type: string;
  confidence: number;
  odds: number;
  stake_recommendation: number;
  status: 'pending' | 'won' | 'lost' | 'void';
  created_at: string;
  updated_at: string;
}

export interface ExpertAnalytics {
  total_picks: number;
  won_picks: number;
  lost_picks: number;
  win_rate: number;
  roi: number;
  total_profit: number;
  average_odds: number;
  best_sport: string;
  current_streak: number;
  monthly_performance: any[];
}

export interface ExpertPerformance {
  win_rate: number;
  roi: number;
  total_picks: number;
  profit_loss: number;
  average_odds: number;
  best_category: string;
  recent_form: string;
  monthly_stats: any[];
}

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number;
  duration: number;
  features: string[];
}

export interface Review {
  id: string;
  user_id: string;
  expert_id: string;
  rating: number;
  comment: string;
  created_at: string;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  expert_id: string;
  tier_id: string;
  status: string;
  created_at: string;
  expires_at: string;
}
