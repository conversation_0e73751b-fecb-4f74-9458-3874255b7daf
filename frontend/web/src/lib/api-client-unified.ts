/**
 * BetBet Platform - Unified API Client
 * ===================================
 *
 * SINGLE SOURCE OF TRUTH for all API communication.
 * All requests go through the API Gateway at http://localhost:8000
 *
 * ⚠️  IMPORTANT: This is the ONLY API client that should be used.
 * ⚠️  Do NOT create separate API clients for individual services.
 * ⚠️  Do NOT make direct service calls (localhost:8001-8009).
 */

import { apiClient, API_ROUTES, getRoute } from './unified-api-routes';

// Re-export the unified client and routes
export { apiClient, API_ROUTES, getRoute };

// Type definitions for common responses
export interface UserStats {
  total_sessions: number;
  sessions_won: number;
  sessions_lost: number;
  win_rate: number;
  total_earnings: number;
  total_spent: number;
  net_profit: number;
  favorite_game: string;
  current_streak: number;
  best_streak: number;
  tournaments_joined: number;
  tournaments_won: number;
  current_rank: number;
  skill_points: number;
}

export interface UserBalance {
  user_id: string;
  available_balance: string;
  locked_balance: string;
  total_balance: string;
  total_deposited: string;
  total_withdrawn: string;
  lifetime_winnings: string;
  lifetime_losses: string;
  currency: string;
}

export interface Expert {
  id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  specializations: string[];
  verification_status: 'pending' | 'verified' | 'rejected';
  overall_rating: number;
  total_subscribers: number;
  total_picks: number;
  win_rate: number;
  roi: number;
  streak_type: 'win' | 'loss';
  current_streak: number;
  created_at: string;
  updated_at: string;
}

export interface GameSession {
  id: string;
  game_id: string;
  status: string;
  max_players: number;
  current_players: number;
  entry_fee: number;
  prize_pool: number;
  created_at: string;
  starts_at?: string;
}

export interface Market {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  total_volume: number;
  created_at: string;
  closes_at: string;
}

/**
 * Gaming API Methods
 */
export const gamingApi = {
  // Analytics
  getOverview: () => apiClient.get(API_ROUTES.GAMING.ANALYTICS.OVERVIEW),
  getUserStats: (userId: string): Promise<UserStats> => 
    apiClient.get(getRoute.gaming.userStats(userId)),
  getUserActivity: (userId: string) => 
    apiClient.get(getRoute.gaming.userActivity(userId)),

  // Games
  getGames: (params?: { page?: number; limit?: number; category?: string }) => 
    apiClient.get(API_ROUTES.GAMING.GAMES, { params }),
  getGame: (gameId: string) =>
    apiClient.get(`${API_ROUTES.GAMING.GAMES}/${gameId}`),
  getGameBySlug: (slug: string) =>
    apiClient.get(`${API_ROUTES.GAMING.GAMES}/slug/${slug}`),

  // Sessions
  getSessions: (params?: { page?: number; limit?: number; status?: string }) => 
    apiClient.get(API_ROUTES.GAMING.SESSIONS, { params }),
  getSession: (sessionId: string): Promise<GameSession> => 
    apiClient.get(`${API_ROUTES.GAMING.SESSIONS}/${sessionId}`),
  joinSession: (sessionId: string) => 
    apiClient.post(`${API_ROUTES.GAMING.SESSIONS}/${sessionId}/join`),
  createSession: (sessionData: any) => 
    apiClient.post(API_ROUTES.GAMING.SESSIONS, sessionData),

  // Chess
  getChessGames: (params?: { page?: number; limit?: number }) => 
    apiClient.get(`${API_ROUTES.GAMING.CHESS}/games`, { params }),
  createChessGame: (gameData: any) => 
    apiClient.post(`${API_ROUTES.GAMING.CHESS}/games`, gameData),
  getChessGame: (gameId: string) => 
    apiClient.get(`${API_ROUTES.GAMING.CHESS}/games/${gameId}`),
  makeChessMove: (gameId: string, move: any) => 
    apiClient.post(`${API_ROUTES.GAMING.CHESS}/games/${gameId}/move`, move),

  // Health
  health: () => apiClient.get(API_ROUTES.GAMING.HEALTH),
};

/**
 * Wallet API Methods
 */
export const walletApi = {
  // Balance
  getBalance: (userId: string): Promise<UserBalance> => 
    apiClient.get(getRoute.wallet.balance(userId)),
  
  // Transactions
  getTransactions: (userId: string, params?: { page?: number; limit?: number }) => 
    apiClient.get(getRoute.wallet.transactions(userId), { params }),
  
  // Deposits & Withdrawals
  createDeposit: (depositData: { user_id: string; amount: number; payment_method?: string }) => 
    apiClient.post(API_ROUTES.WALLET.DEPOSIT, depositData),
  createWithdrawal: (withdrawalData: { user_id: string; amount: number; withdrawal_method?: string }) => 
    apiClient.post(API_ROUTES.WALLET.WITHDRAW, withdrawalData),
  
  // Transfers
  createTransfer: (transferData: { from_user_id: string; to_user_id: string; amount: number; description?: string }) => 
    apiClient.post(API_ROUTES.WALLET.TRANSFER, transferData),
  
  // Analytics
  getAnalyticsOverview: () => 
    apiClient.get(API_ROUTES.WALLET.ANALYTICS.OVERVIEW),
  
  // Health
  health: () => apiClient.get(API_ROUTES.WALLET.HEALTH),
};

/**
 * Expert Analysis API Methods
 */
export const expertsApi = {
  // Experts
  getExperts: (params?: { 
    page?: number; 
    limit?: number; 
    sport?: string; 
    verified_only?: boolean;
    sort_by?: string;
  }) => apiClient.get(API_ROUTES.EXPERTS.LIST, { params }),
  
  getExpert: (expertId: string): Promise<Expert> => 
    apiClient.get(getRoute.experts.detail(expertId)),
  
  getExpertStats: (expertId: string) => 
    apiClient.get(getRoute.experts.stats(expertId)),
  
  // Picks
  getPicks: (params?: { page?: number; limit?: number; expert_id?: string }) => 
    apiClient.get(API_ROUTES.EXPERTS.PICKS, { params }),
  
  createPick: (pickData: any) => 
    apiClient.post(API_ROUTES.EXPERTS.PICKS, pickData),
  
  // Analytics
  getAnalyticsOverview: () => 
    apiClient.get(API_ROUTES.EXPERTS.ANALYTICS.OVERVIEW),
  
  // Health
  health: () => apiClient.get(API_ROUTES.EXPERTS.HEALTH),
};

/**
 * Sports Analysis API Methods
 */
export const sportsApi = {
  // Fixtures
  getFixtures: (params?: { page?: number; limit?: number; league?: string }) => 
    apiClient.get(API_ROUTES.SPORTS.FIXTURES, { params }),
  
  // Teams & Leagues
  getTeams: (params?: { page?: number; limit?: number; league?: string }) => 
    apiClient.get(API_ROUTES.SPORTS.TEAMS, { params }),
  
  getLeagues: (params?: { page?: number; limit?: number; country?: string }) => 
    apiClient.get(API_ROUTES.SPORTS.LEAGUES, { params }),
  
  // Analytics
  getAnalyticsOverview: () => 
    apiClient.get(API_ROUTES.SPORTS.ANALYTICS.OVERVIEW),
  
  // Health
  health: () => apiClient.get(API_ROUTES.SPORTS.HEALTH),
};

/**
 * Leaderboards API Methods
 */
export const leaderboardsApi = {
  // Rankings
  getUnifiedLeaderboard: (params?: { page?: number; limit?: number; timeframe?: string }) => 
    apiClient.get(API_ROUTES.LEADERBOARDS.UNIFIED, { params }),
  
  getGamingLeaderboard: (params?: { page?: number; limit?: number }) => 
    apiClient.get(API_ROUTES.LEADERBOARDS.GAMING, { params }),
  
  getTradingLeaderboard: (params?: { page?: number; limit?: number }) => 
    apiClient.get(API_ROUTES.LEADERBOARDS.TRADING, { params }),
  
  // Achievements
  getAchievements: (params?: { page?: number; limit?: number }) => 
    apiClient.get(API_ROUTES.LEADERBOARDS.ACHIEVEMENTS, { params }),
  
  getUserAchievements: (userId: string) => 
    apiClient.get(getRoute.leaderboards.userAchievements(userId)),
  
  // Health
  health: () => apiClient.get(API_ROUTES.LEADERBOARDS.HEALTH),
};

/**
 * Custom Betting API Methods
 */
export const customBettingApi = {
  // Markets
  getMarkets: (params?: { page?: number; limit?: number; category?: string; status?: string }): Promise<{ markets: Market[]; total: number }> => 
    apiClient.get(API_ROUTES.CUSTOM_BETTING.MARKETS, { params }),
  
  getMarket: (marketId: string): Promise<Market> => 
    apiClient.get(getRoute.customBetting.marketDetail(marketId)),
  
  createMarket: (marketData: any) => 
    apiClient.post(API_ROUTES.CUSTOM_BETTING.MARKETS, marketData),
  
  // Positions
  getPositions: (params?: { page?: number; limit?: number; user_id?: string }) => 
    apiClient.get(API_ROUTES.CUSTOM_BETTING.POSITIONS, { params }),
  
  createPosition: (positionData: any) => 
    apiClient.post(API_ROUTES.CUSTOM_BETTING.POSITIONS, positionData),
  
  // Health
  health: () => apiClient.get(API_ROUTES.CUSTOM_BETTING.HEALTH),
};

/**
 * Trading API Methods
 */
export const tradingApi = {
  // Markets
  getMarkets: (params?: { page?: number; limit?: number }) => 
    apiClient.get(API_ROUTES.TRADING.MARKETS, { params }),
  
  // Orders
  getOrders: (params?: { page?: number; limit?: number; status?: string }) => 
    apiClient.get(API_ROUTES.TRADING.ORDERS, { params }),
  
  createOrder: (orderData: any) => 
    apiClient.post(API_ROUTES.TRADING.ORDERS, orderData),
  
  // Positions
  getPositions: (params?: { page?: number; limit?: number }) => 
    apiClient.get(API_ROUTES.TRADING.POSITIONS, { params }),
  
  // Health
  health: () => apiClient.get(API_ROUTES.TRADING.HEALTH),
};

/**
 * System API Methods
 */
export const systemApi = {
  // Health checks
  health: () => apiClient.get(API_ROUTES.SYSTEM.HEALTH),
  healthAll: () => apiClient.healthCheck(),
  
  // Documentation
  docs: () => window.open(`${apiClient['client'].defaults.baseURL}${API_ROUTES.SYSTEM.DOCS}`, '_blank'),
};

// Default export - the unified API client
export default {
  gaming: gamingApi,
  wallet: walletApi,
  experts: expertsApi,
  sports: sportsApi,
  leaderboards: leaderboardsApi,
  customBetting: customBettingApi,
  trading: tradingApi,
  system: systemApi,
  
  // Direct client access for custom requests
  client: apiClient,
  
  // Route helpers
  routes: API_ROUTES,
  getRoute,
  
  // Authentication
  setAuthToken: (token: string | null) => apiClient.setAuthToken(token),
};
