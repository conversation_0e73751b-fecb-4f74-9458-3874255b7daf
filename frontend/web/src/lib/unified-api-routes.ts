/**
 * BetBet Platform - Unified API Routes Configuration
 * =================================================
 * 
 * Single source of truth for all API routing.
 * All requests go through API Gateway at http://localhost:8000
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

// Base configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000',
  TIMEOUT: 15000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Unified route definitions
export const API_ROUTES = {
  // Gaming Engine
  GAMING: {
    BASE: '/api/gaming',
    GAMES: '/api/gaming/games',
    SESSIONS: '/api/gaming/sessions',
    CHESS: '/api/gaming/chess',
    ANALYTICS: {
      OVERVIEW: '/api/gaming/analytics/overview',
      USER_STATS: (userId: string) => `/api/gaming/analytics/users/${userId}/stats`,
      USER_ACTIVITY: (userId: string) => `/api/gaming/analytics/users/${userId}/activity`,
    },
    HEALTH: '/api/gaming/health',
  },

  // Wallet Service
  WALLET: {
    BASE: '/api/wallet',
    BALANCE: (userId: string) => `/api/wallet/balance/${userId}`,
    TRANSACTIONS: (userId: string) => `/api/wallet/transactions/${userId}`,
    DEPOSIT: '/api/wallet/deposit',
    WITHDRAW: '/api/wallet/withdraw',
    TRANSFER: '/api/wallet/transfer',
    ANALYTICS: {
      OVERVIEW: '/api/wallet/analytics/overview',
    },
    HEALTH: '/api/wallet/health',
  },

  // Expert Analysis
  EXPERTS: {
    BASE: '/api/experts',
    LIST: '/api/experts/list',
    DETAIL: (expertId: string) => `/api/experts/${expertId}`,
    PICKS: '/api/experts/picks',
    ANALYTICS: {
      OVERVIEW: '/api/experts/analytics/overview',
      EXPERT_STATS: (expertId: string) => `/api/experts/analytics/${expertId}/stats`,
    },
    HEALTH: '/api/experts/health',
  },

  // Sports Analysis
  SPORTS: {
    BASE: '/api/sports',
    FIXTURES: '/api/sports/fixtures',
    TEAMS: '/api/sports/teams',
    LEAGUES: '/api/sports/leagues',
    ANALYTICS: {
      OVERVIEW: '/api/sports/analytics/overview',
    },
    HEALTH: '/api/sports/health',
  },

  // Leaderboards
  LEADERBOARDS: {
    BASE: '/api/leaderboards',
    UNIFIED: '/api/leaderboards/unified-leaderboards',
    GAMING: '/api/leaderboards/gaming',
    TRADING: '/api/leaderboards/trading',
    ACHIEVEMENTS: '/api/leaderboards/achievements',
    USER_ACHIEVEMENTS: (userId: string) => `/api/leaderboards/achievements?user_id=${userId}`,
    HEALTH: '/api/leaderboards/health',
  },

  // Custom Betting
  CUSTOM_BETTING: {
    BASE: '/api/custom-betting',
    MARKETS: '/api/custom-betting/markets',
    MARKET_DETAIL: (marketId: string) => `/api/custom-betting/markets/${marketId}`,
    POSITIONS: '/api/custom-betting/positions',
    HEALTH: '/api/custom-betting/health',
  },

  // Trading
  TRADING: {
    BASE: '/api/trading',
    MARKETS: '/api/trading/markets',
    ORDERS: '/api/trading/orders',
    POSITIONS: '/api/trading/positions',
    HEALTH: '/api/trading/health',
  },

  // System
  SYSTEM: {
    HEALTH: '/health',
    DOCS: '/docs',
  },
} as const;

// Public routes (no authentication required)
export const PUBLIC_ROUTES = [
  API_ROUTES.SPORTS.FIXTURES,
  API_ROUTES.SPORTS.LEAGUES,
  API_ROUTES.LEADERBOARDS.UNIFIED,
  API_ROUTES.SYSTEM.HEALTH,
  API_ROUTES.SYSTEM.DOCS,
] as const;

// Admin routes (admin authentication required)
export const ADMIN_ROUTES = [
  '/api/gaming/admin',
  '/api/wallet/admin',
  '/api/experts/admin',
  '/api/sports/admin',
  '/api/leaderboards/admin',
  '/api/custom-betting/admin',
  '/api/trading/admin',
] as const;

/**
 * Unified API Client Class
 * Handles all API communication through the gateway
 */
export class UnifiedApiClient {
  private client: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        // Add request ID for tracking
        config.headers['X-Request-ID'] = `web-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API] ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        const status = error.response?.status;
        const url = error.config?.url;
        console.error(`[API] ${status} ${url}:`, error.response?.data || error.message);

        // Handle common errors
        if (status === 401) {
          console.warn('[API] Unauthorized - token may be expired');
          // Could trigger auth refresh here
        } else if (status === 403) {
          console.warn('[API] Forbidden - insufficient permissions');
        } else if (status >= 500) {
          console.error('[API] Server error - service may be down');
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string | null) {
    this.authToken = token;
  }

  /**
   * Generic request method
   */
  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.client.request<T>(config);
    return response.data;
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  /**
   * Health check for all services
   */
  async healthCheck() {
    const services = [
      { name: 'API Gateway', url: API_ROUTES.SYSTEM.HEALTH },
      { name: 'Gaming', url: API_ROUTES.GAMING.HEALTH },
      { name: 'Wallet', url: API_ROUTES.WALLET.HEALTH },
      { name: 'Experts', url: API_ROUTES.EXPERTS.HEALTH },
      { name: 'Sports', url: API_ROUTES.SPORTS.HEALTH },
      { name: 'Leaderboards', url: API_ROUTES.LEADERBOARDS.HEALTH },
      { name: 'Custom Betting', url: API_ROUTES.CUSTOM_BETTING.HEALTH },
      { name: 'Trading', url: API_ROUTES.TRADING.HEALTH },
    ];

    const results = await Promise.allSettled(
      services.map(async (service) => {
        try {
          const response = await this.get(service.url);
          return { name: service.name, status: 'healthy', data: response };
        } catch (error) {
          return { name: service.name, status: 'unhealthy', error: error.message };
        }
      })
    );

    return results.map((result, index) => ({
      service: services[index].name,
      ...(result.status === 'fulfilled' ? result.value : { status: 'error', error: result.reason }),
    }));
  }
}

// Create singleton instance
export const apiClient = new UnifiedApiClient();

// Export route helpers
export const getRoute = {
  gaming: {
    userStats: (userId: string) => API_ROUTES.GAMING.ANALYTICS.USER_STATS(userId),
    userActivity: (userId: string) => API_ROUTES.GAMING.ANALYTICS.USER_ACTIVITY(userId),
  },
  wallet: {
    balance: (userId: string) => API_ROUTES.WALLET.BALANCE(userId),
    transactions: (userId: string) => API_ROUTES.WALLET.TRANSACTIONS(userId),
  },
  experts: {
    detail: (expertId: string) => API_ROUTES.EXPERTS.DETAIL(expertId),
    stats: (expertId: string) => API_ROUTES.EXPERTS.ANALYTICS.EXPERT_STATS(expertId),
  },
  leaderboards: {
    userAchievements: (userId: string) => API_ROUTES.LEADERBOARDS.USER_ACHIEVEMENTS(userId),
  },
  customBetting: {
    marketDetail: (marketId: string) => API_ROUTES.CUSTOM_BETTING.MARKET_DETAIL(marketId),
  },
};

export default apiClient;
