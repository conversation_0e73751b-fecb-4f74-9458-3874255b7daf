import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/games',
  '/tournaments',
  '/sessions',
  '/spectate',
  '/leaderboard',
  '/sports-analysis',
  '/dashboard',
  '/wallet',
  '/custom-betting(.*)',
  '/custom-betting-test',
  '/test-api',
  '/chess(.*)',
  '/chess-test(.*)',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/api/public(.*)',
  '/api/health(.*)',
  '/test-integration',
  '/test-chess',
  '/simple-test'
]);

export default clerkMiddleware(async (auth, req) => {
  // Protect all routes except public ones
  if (!isPublicRoute(req)) {
    await auth.protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest|avif)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};