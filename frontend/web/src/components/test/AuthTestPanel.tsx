/**
 * Authentication Test Panel
 * Tests the end-to-end authentication flow through the API Gateway
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useMultiServiceApi } from '@/hooks/useApi';
import { useAuth, useUser } from '@clerk/nextjs';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Shield, 
  Key,
  Database,
  Activity
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'failed';
  message: string;
  details?: any;
}

export function AuthTestPanel() {
  const { user } = useUser();
  const { isSignedIn, getToken } = useAuth();
  const multiApi = useMultiServiceApi();
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const runAuthTests = async () => {
    setTesting(true);
    const testResults: TestResult[] = [];

    // Test 1: Clerk Authentication Status
    testResults.push({
      name: 'Clerk Authentication',
      status: isSignedIn ? 'success' : 'failed',
      message: isSignedIn ? 'User is authenticated' : 'User not authenticated',
      details: { userId: user?.id, email: user?.emailAddresses[0]?.emailAddress }
    });

    // Test 2: Token Retrieval
    try {
      const token = await getToken();
      testResults.push({
        name: 'Token Retrieval',
        status: token ? 'success' : 'failed',
        message: token ? 'Successfully retrieved Clerk token' : 'Failed to get token',
        details: { tokenPresent: !!token, tokenLength: token?.length }
      });
    } catch (error) {
      testResults.push({
        name: 'Token Retrieval',
        status: 'failed',
        message: `Token retrieval failed: ${error.message}`,
        details: { error: error.message }
      });
    }

    // Test 3: API Gateway Health Check
    try {
      const healthResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/health`);
      const healthData = await healthResponse.json();
      testResults.push({
        name: 'API Gateway Health',
        status: healthResponse.ok ? 'success' : 'failed',
        message: healthResponse.ok ? 'API Gateway is healthy' : 'API Gateway is down',
        details: healthData
      });
    } catch (error) {
      testResults.push({
        name: 'API Gateway Health',
        status: 'failed',
        message: `Gateway health check failed: ${error.message}`,
        details: { error: error.message }
      });
    }

    // Test 4: Authenticated Service Calls
    const serviceTests = [
      { name: 'Gaming Service', method: () => multiApi.gaming.getGames() },
      { name: 'Sports Service', method: () => multiApi.sports.getFixtures() },
      { name: 'Experts Service', method: () => multiApi.experts.getExperts() },
      { name: 'Leaderboards Service', method: () => multiApi.leaderboards.getUnifiedLeaderboard() }
    ];

    for (const serviceTest of serviceTests) {
      try {
        const response = await serviceTest.method();
        testResults.push({
          name: `${serviceTest.name} Authentication`,
          status: 'success',
          message: `Successfully called ${serviceTest.name}`,
          details: { 
            service: response.service, 
            source: response.source,
            timestamp: response.timestamp 
          }
        });
      } catch (error) {
        testResults.push({
          name: `${serviceTest.name} Authentication`,
          status: 'failed',
          message: `${serviceTest.name} call failed: ${error.response?.data?.error || error.message}`,
          details: { 
            status: error.response?.status,
            error: error.response?.data || error.message 
          }
        });
      }
    }

    // Test 5: Service Health Checks
    try {
      const healthArray = await multiApi.system.healthAll();
      const healthyCount = healthArray.filter(service => service.status === 'healthy').length;
      const totalCount = healthArray.length;
      
      testResults.push({
        name: 'Service Health Checks',
        status: healthyCount > 0 ? 'success' : 'failed',
        message: `${healthyCount}/${totalCount} services are healthy`,
        details: healthChecks
      });
    } catch (error) {
      testResults.push({
        name: 'Service Health Checks',
        status: 'failed',
        message: `Health checks failed: ${error.message}`,
        details: { error: error.message }
      });
    }

    setResults(testResults);
    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Success</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const successCount = results.filter(r => r.status === 'success').length;
  const totalCount = results.length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          Authentication Flow Test
        </CardTitle>
        <p className="text-sm text-gray-600">
          Test the end-to-end authentication flow through the API Gateway
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* User Info */}
        {user && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Key className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Current User</span>
            </div>
            <div className="text-sm text-gray-700">
              <p><strong>Email:</strong> {user.emailAddresses[0]?.emailAddress}</p>
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Status:</strong> {isSignedIn ? 'Authenticated' : 'Not authenticated'}</p>
            </div>
          </div>
        )}

        {/* Test Controls */}
        <div className="flex items-center justify-between">
          <Button onClick={runAuthTests} disabled={testing || !isSignedIn}>
            {testing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Activity className="h-4 w-4 mr-2" />
                Run Authentication Tests
              </>
            )}
          </Button>

          {results.length > 0 && (
            <div className="text-sm">
              <span className="font-medium">Results: </span>
              <span className={successCount === totalCount ? 'text-green-600' : 'text-orange-600'}>
                {successCount}/{totalCount} passed
              </span>
            </div>
          )}
        </div>

        {!isSignedIn && (
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <p className="text-yellow-800 text-sm">
              Please sign in to test the authentication flow.
            </p>
          </div>
        )}

        {/* Test Results */}
        {results.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Test Results
            </h4>
            
            {results.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.name}</span>
                  </div>
                  {getStatusBadge(result.status)}
                </div>
                
                <p className="text-sm text-gray-700 mb-2">{result.message}</p>
                
                {result.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                      View Details
                    </summary>
                    <pre className="mt-2 bg-gray-50 p-2 rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}