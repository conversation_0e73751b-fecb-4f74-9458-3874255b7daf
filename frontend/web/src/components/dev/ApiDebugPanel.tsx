/**
 * BetBet Gaming Engine - API Debug Panel
 * =====================================
 * 
 * Development-only panel for debugging API issues and viewing error logs.
 * Only visible in development mode.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bug, 
  RefreshCw, 
  Trash2, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  Eye,
  EyeOff,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useLeaderboardWebSocket } from '@/hooks/useLeaderboardWebSocket';
import { cn } from '@/lib/utils';

interface ApiError {
  id: string;
  timestamp: string;
  endpoint: string;
  method: string;
  error: string;
  details?: any;
}

interface ApiDebugPanelProps {
  className?: string;
}

export function ApiDebugPanel({ className }: ApiDebugPanelProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [errors, setErrors] = useState<ApiError[]>([]);
  const [apiStatus, setApiStatus] = useState<'unknown' | 'online' | 'offline'>('unknown');
  
  // WebSocket connection status - only connect when panel is visible
  const { connectionStatus } = useLeaderboardWebSocket(undefined, isVisible);

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    // Listen for custom API error events
    const handleApiError = (event: CustomEvent) => {
      const { endpoint, method, error, details } = event.detail;
      const newError: ApiError = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        endpoint,
        method,
        error: error.message || error.toString(),
        details
      };
      
      setErrors(prev => [newError, ...prev].slice(0, 50)); // Keep last 50 errors
    };

    // Add event listener for API errors
    window.addEventListener('api-error', handleApiError as EventListener);
    
    // Check API status
    checkApiStatus();
    
    return () => {
      window.removeEventListener('api-error', handleApiError as EventListener);
    };
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/health`);
      setApiStatus(response.ok ? 'online' : 'offline');
    } catch (err) {
      setApiStatus('offline');
    }
  };

  const clearErrors = () => {
    setErrors([]);
  };

  const getStatusColor = () => {
    switch (apiStatus) {
      case 'online': return 'text-emerald-400';
      case 'offline': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getStatusIcon = () => {
    switch (apiStatus) {
      case 'online': return <CheckCircle className="h-4 w-4" />;
      case 'offline': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      {/* Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="mb-2 bg-slate-900/90 backdrop-blur-sm border-slate-700 text-white shadow-lg hover:bg-slate-800/90"
      >
        {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        <Bug className="h-4 w-4 ml-1" />
        API Debug
        {errors.length > 0 && (
          <Badge variant="destructive" className="ml-2">
            {errors.length}
          </Badge>
        )}
        {/* WebSocket Status Indicator */}
        <div className={cn(
          "w-2 h-2 rounded-full ml-2",
          connectionStatus === 'connected' && "bg-emerald-400",
          connectionStatus === 'connecting' && "bg-yellow-400 animate-pulse",
          connectionStatus === 'disconnected' && "bg-red-400",
          connectionStatus === 'error' && "bg-red-400 animate-pulse"
        )}></div>
      </Button>

      {/* Debug Panel */}
      {isVisible && (
        <Card className="w-96 max-h-96 bg-slate-900/95 backdrop-blur-sm border-slate-700 shadow-xl">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center justify-between text-white">
              <div className="flex items-center space-x-2">
                <Bug className="h-4 w-4" />
                <span>API Debug Panel</span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={checkApiStatus}
                  className="h-6 w-6 p-0"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearErrors}
                  className="h-6 w-6 p-0"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-3">
            {/* API Status */}
            <div className="flex items-center justify-between text-sm text-slate-200">
              <span>Backend Status:</span>
              <div className={`flex items-center space-x-1 ${getStatusColor()}`}>
                {getStatusIcon()}
                <span className="capitalize">{apiStatus}</span>
              </div>
            </div>
            
            {/* WebSocket Status */}
            <div className="flex items-center justify-between text-sm text-slate-200">
              <span>WebSocket Status:</span>
              <div className={`flex items-center space-x-1 ${
                connectionStatus === 'connected' ? 'text-emerald-400' :
                connectionStatus === 'connecting' ? 'text-yellow-400' :
                'text-red-400'
              }`}>
                {connectionStatus === 'connected' ? <Wifi className="h-4 w-4" /> :
                 connectionStatus === 'connecting' ? <Clock className="h-4 w-4 animate-pulse" /> :
                 <WifiOff className="h-4 w-4" />}
                <span className="capitalize">
                  {connectionStatus === 'connected' ? 'Connected' :
                   connectionStatus === 'connecting' ? 'Connecting...' :
                   connectionStatus === 'disconnected' ? 'Offline' :
                   connectionStatus === 'error' ? 'Error' : 'Unknown'}
                </span>
              </div>
            </div>

            {/* Recent Errors */}
            <div>
              <div className="text-sm font-medium mb-2 text-white">Recent API Errors ({errors.length})</div>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {errors.length === 0 ? (
                  <div className="text-xs text-slate-400 text-center py-4">
                    No API errors detected
                  </div>
                ) : (
                  errors.map((error) => (
                    <div key={error.id} className="bg-red-900/20 border border-red-500/30 rounded p-2">
                      <div className="flex items-center justify-between mb-1">
                        <div className="text-xs font-medium text-red-400">
                          {error.method} {error.endpoint}
                        </div>
                        <div className="text-xs text-red-300">
                          {new Date(error.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                      <div className="text-xs text-red-300">
                        {error.error}
                      </div>
                      {error.details && (
                        <details className="mt-1">
                          <summary className="text-xs text-red-400 cursor-pointer">
                            Details
                          </summary>
                          <pre className="text-xs text-red-300 mt-1 whitespace-pre-wrap">
                            {JSON.stringify(error.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="pt-2 border-t border-slate-600">
              <div className="text-xs text-slate-400">
                💡 Tips:
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Add ?demo=true to URLs for mock data</li>
                  <li>Check console for detailed error logs</li>
                  <li>Errors are auto-captured from API calls</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper function to emit API errors from other components
export function emitApiError(endpoint: string, method: string, error: any, details?: any) {
  if (process.env.NODE_ENV === 'development') {
    const event = new CustomEvent('api-error', {
      detail: { endpoint, method, error, details }
    });
    window.dispatchEvent(event);
  }
}