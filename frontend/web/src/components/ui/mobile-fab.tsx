/**
 * BetBet Gaming Engine - Mobile Floating Action Button
 * ===================================================
 * 
 * Quick action button for mobile users.
 */

'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Plus, Zap } from 'lucide-react';
import { useAuth } from '@/store/gameStore';
import { cn } from '@/lib/utils';

interface MobileFABProps {
  className?: string;
}

export function MobileFAB({ className }: MobileFABProps) {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) return null;

  return (
    <Button
      asChild
      className={cn("mobile-fab", className)}
      size="lg"
    >
      <Link href="/games">
        <Plus className="h-6 w-6" />
        <span className="sr-only">Create Session</span>
      </Link>
    </Button>
  );
}

export default MobileFAB;