'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useNotifications, Notification } from '@/contexts/NotificationContext';
import { cn } from '@/lib/utils';

interface NotificationToastProps {
  notification: Notification;
  onClose: (id: string) => void;
  mobile?: boolean;
}

function NotificationToast({ notification, onClose, mobile = false }: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => onClose(notification.id), 200);
  };

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  const getColors = () => {
    switch (notification.type) {
      case 'success':
        return 'border-green-500/40 bg-green-500/10 text-green-100';
      case 'error':
        return 'border-red-500/40 bg-red-500/10 text-red-100';
      case 'warning':
        return 'border-yellow-500/40 bg-yellow-500/10 text-yellow-100';
      case 'info':
        return 'border-blue-500/40 bg-blue-500/10 text-blue-100';
      default:
        return 'border-gray-500/40 bg-gray-500/10 text-gray-100';
    }
  };

  return (
    <Card className={cn(
      "relative p-4 shadow-lg transition-all duration-200 ease-in-out border",
      getColors(),
      mobile ? "mx-4 mb-2" : "mb-3",
      isVisible && !isLeaving ? "opacity-100 transform translate-x-0" : "opacity-0 transform translate-x-full",
      isLeaving && "opacity-0 transform translate-x-full"
    )}>
      <div className="flex items-start gap-3">
        <div className="text-lg shrink-0">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm mb-1">
            {notification.title}
          </div>
          
          {notification.message && (
            <div className="text-sm opacity-90 break-words">
              {notification.message}
            </div>
          )}
          
          <div className="text-xs opacity-60 mt-2">
            {new Date(notification.timestamp).toLocaleTimeString()}
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-6 w-6 p-0 text-current hover:bg-white/10 shrink-0"
        >
          ✕
        </Button>
      </div>

      {/* Progress bar for timed notifications */}
      {!notification.persistent && notification.duration && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20 rounded-b">
          <div 
            className="h-full bg-white/40 rounded-b transition-all linear"
            style={{
              animation: `shrink ${notification.duration}ms linear forwards`,
            }}
          />
        </div>
      )}

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </Card>
  );
}

interface NotificationContainerProps {
  mobile?: boolean;
}

export function NotificationContainer({ mobile = false }: NotificationContainerProps) {
  const { notifications, removeNotification } = useNotifications();

  if (notifications.length === 0) return null;

  return (
    <div className={cn(
      "fixed z-50 pointer-events-none",
      mobile 
        ? "top-4 left-0 right-0" 
        : "top-4 right-4 w-96 max-w-[calc(100vw-2rem)]"
    )}>
      <div className="space-y-2 pointer-events-auto">
        {notifications.map((notification) => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={removeNotification}
            mobile={mobile}
          />
        ))}
      </div>
    </div>
  );
}

// Hook for trading-specific notifications
export function useTradingNotifications() {
  const { success, error, warning, info } = useNotifications();

  return {
    orderSuccess: (orderId: string, side: string, quantity: number, market: string) => {
      success(
        'Order Submitted',
        `${side.toUpperCase()} ${quantity} ${market} order #${orderId.slice(-6)} submitted successfully`
      );
    },

    orderFilled: (orderId: string, side: string, quantity: number, price: number, market: string) => {
      success(
        'Order Filled',
        `${side.toUpperCase()} ${quantity} ${market} at $${price.toFixed(2)} - Order #${orderId.slice(-6)}`
      );
    },

    orderCancelled: (orderId: string, market: string) => {
      info(
        'Order Cancelled',
        `Order #${orderId.slice(-6)} for ${market} has been cancelled`
      );
    },

    orderRejected: (reason: string, market: string) => {
      error(
        'Order Rejected',
        `${market}: ${reason}`,
        { persistent: false }
      );
    },

    positionClosed: (side: string, quantity: number, pnl: number, market: string) => {
      const isProfit = pnl >= 0;
      (isProfit ? success : warning)(
        'Position Closed',
        `${side} ${quantity} ${market} closed with ${isProfit ? 'profit' : 'loss'} of $${Math.abs(pnl).toFixed(2)}`
      );
    },

    connectionLost: () => {
      error(
        'Connection Lost',
        'Real-time data connection lost. Reconnecting...',
        { persistent: true }
      );
    },

    connectionRestored: () => {
      success(
        'Connection Restored',
        'Real-time data connection restored'
      );
    },

    marketUnavailable: (market: string) => {
      warning(
        'Market Unavailable',
        `${market} is currently unavailable for trading`
      );
    },

    insufficientBalance: (required: number, available: number) => {
      warning(
        'Insufficient Balance',
        `Required: $${required.toFixed(2)}, Available: $${available.toFixed(2)}`
      );
    },

    riskLimitReached: (limit: string) => {
      error(
        'Risk Limit Reached',
        `${limit} risk limit reached. Position size limited.`,
        { persistent: true }
      );
    },

    systemMaintenance: (message: string) => {
      warning(
        'System Maintenance',
        message,
        { persistent: true, duration: 0 }
      );
    },
  };
}