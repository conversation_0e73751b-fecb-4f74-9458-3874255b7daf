/**
 * Expert Analyst Card Component
 * Displays individual expert information with stats and follow/subscribe actions
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { 
  Star, 
  TrendingUp, 
  Users, 
  Award,
  Eye,
  DollarSign,
  Calendar
} from 'lucide-react';

interface Expert {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  bio?: string;
  specialization: string[];
  verification_status: 'verified' | 'pending' | 'unverified';
  performance_metrics: {
    accuracy_rate: number;
    total_picks: number;
    followers_count: number;
    tier: 'bronze' | 'silver' | 'gold' | 'platinum';
    monthly_roi: number;
    streak_current: number;
  };
  subscription_tiers: {
    basic: { price: number; features: string[] };
    premium?: { price: number; features: string[] };
  };
  recent_picks?: number;
  is_following?: boolean;
  is_subscribed?: boolean;
}

interface ExpertCardProps {
  expert: Expert;
  onFollow?: (expertId: string) => void;
  onSubscribe?: (expertId: string, tier: string) => void;
  onViewProfile?: (expertId: string) => void;
  showSubscriptionOptions?: boolean;
}

export function ExpertCard({ 
  expert, 
  onFollow, 
  onSubscribe, 
  onViewProfile,
  showSubscriptionOptions = true 
}: ExpertCardProps) {
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum': return 'text-purple-400 bg-purple-900/30 border-purple-600';
      case 'gold': return 'text-yellow-400 bg-yellow-900/30 border-yellow-600';
      case 'silver': return 'text-slate-400 bg-slate-800/30 border-slate-600';
      default: return 'text-orange-400 bg-orange-900/30 border-orange-600';
    }
  };

  const getVerificationBadge = () => {
    if (expert.verification_status === 'verified') {
      return <Badge className="bg-blue-900/30 text-blue-400 border-blue-600"><Award className="h-3 w-3 mr-1" />Verified</Badge>;
    }
    return null;
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-200 bg-slate-900/50 border-slate-700">
      <CardHeader className="pb-4">
        <div className="flex items-start gap-3">
          <Avatar className="h-12 w-12">
            {expert.avatar_url ? (
              <img src={expert.avatar_url} alt={expert.display_name} className="rounded-full" />
            ) : (
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 text-white flex items-center justify-center text-lg font-semibold">
                {expert.display_name.charAt(0)}
              </div>
            )}
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-lg truncate text-white">{expert.display_name}</h3>
              {getVerificationBadge()}
            </div>
            <p className="text-sm text-slate-400 mb-2">@{expert.username}</p>
            
            <div className="flex items-center gap-2 mb-2">
              <Badge className={getTierColor(expert.performance_metrics.tier)}>
                {expert.performance_metrics.tier.toUpperCase()}
              </Badge>
              <div className="flex items-center gap-1 text-sm text-slate-300">
                <Star className="h-4 w-4 text-yellow-400" />
                <span>{expert.performance_metrics.accuracy_rate}% accuracy</span>
              </div>
            </div>
            
            {expert.specialization.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {expert.specialization.slice(0, 3).map((spec, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs bg-slate-800/50 border-slate-600 text-slate-200">
                    {spec}
                  </Badge>
                ))}
                {expert.specialization.length > 3 && (
                  <Badge variant="outline" className="text-xs bg-slate-800/50 border-slate-600 text-slate-200">
                    +{expert.specialization.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {expert.bio && (
          <p className="text-sm text-slate-300 line-clamp-2">{expert.bio}</p>
        )}

        {/* Performance Stats */}
        <div className="grid grid-cols-2 gap-4 py-3 border-y border-slate-600">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-green-400 mb-1">
              <TrendingUp className="h-4 w-4" />
              <span className="font-semibold">+{expert.performance_metrics.monthly_roi}%</span>
            </div>
            <p className="text-xs text-slate-400">Monthly ROI</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-blue-400 mb-1">
              <Users className="h-4 w-4" />
              <span className="font-semibold">{expert.performance_metrics.followers_count.toLocaleString()}</span>
            </div>
            <p className="text-xs text-slate-400">Followers</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-slate-400" />
            <span className="text-slate-300">{expert.performance_metrics.total_picks} picks</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-slate-400" />
            <span className="text-slate-300">{expert.performance_metrics.streak_current} streak</span>
          </div>
        </div>

        {/* Subscription Options */}
        {showSubscriptionOptions && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <DollarSign className="h-4 w-4 text-emerald-400" />
              <span className="font-medium text-slate-200">From ${expert.subscription_tiers.basic.price}/month</span>
            </div>
            
            <div className="flex gap-2">
              {!expert.is_following && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => onFollow?.(expert.id)}
                  className="flex-1 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                >
                  Follow
                </Button>
              )}
              
              {!expert.is_subscribed && (
                <Button 
                  size="sm" 
                  onClick={() => onSubscribe?.(expert.id, 'basic')}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Subscribe
                </Button>
              )}
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onViewProfile?.(expert.id)}
                className="bg-slate-800/30 hover:bg-slate-700 text-slate-300 hover:text-white"
              >
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}