/**
 * Experts List Component
 * Displays a grid of expert analysts with filtering and sorting
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ExpertCard } from './ExpertCard';
import { useMultiServiceApi } from '@/hooks/useApi';
import { 
  Search, 
  Filter, 
  TrendingUp, 
  Users, 
  Star,
  Award,
  Loader2
} from 'lucide-react';

interface Expert {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  bio?: string;
  specialization: string[];
  verification_status: 'verified' | 'pending' | 'unverified';
  performance_metrics: {
    accuracy_rate: number;
    total_picks: number;
    followers_count: number;
    tier: 'bronze' | 'silver' | 'gold' | 'platinum';
    monthly_roi: number;
    streak_current: number;
  };
  subscription_tiers: {
    basic: { price: number; features: string[] };
    premium?: { price: number; features: string[] };
  };
  recent_picks?: number;
  is_following?: boolean;
  is_subscribed?: boolean;
}

interface ExpertsListProps {
  specialization?: string;
  limit?: number;
  showFilters?: boolean;
  layout?: 'grid' | 'list';
}

export function ExpertsList({ 
  specialization, 
  limit,
  showFilters = true,
  layout = 'grid' 
}: ExpertsListProps) {
  const multiApi = useMultiServiceApi();
  const [experts, setExperts] = useState<Expert[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('accuracy');
  const [filterTier, setFilterTier] = useState('all');
  const [filterSpecialization, setFilterSpecialization] = useState('all');

  useEffect(() => {
    loadExperts();
  }, [specialization, sortBy, filterTier, filterSpecialization]);

  const loadExperts = async () => {
    try {
      setLoading(true);
      const response = await multiApi.experts.getExperts();
      
      let filteredExperts = response.data.experts || [];
      
      // Apply filters
      if (specialization) {
        filteredExperts = filteredExperts.filter(expert => 
          expert.specialization.includes(specialization)
        );
      }
      
      if (filterTier !== 'all') {
        filteredExperts = filteredExperts.filter(expert => 
          expert.performance_metrics.tier === filterTier
        );
      }
      
      if (filterSpecialization !== 'all') {
        filteredExperts = filteredExperts.filter(expert => 
          expert.specialization.includes(filterSpecialization)
        );
      }
      
      if (searchTerm) {
        filteredExperts = filteredExperts.filter(expert =>
          expert.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          expert.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          expert.specialization.some(spec => 
            spec.toLowerCase().includes(searchTerm.toLowerCase())
          )
        );
      }
      
      // Apply sorting
      switch (sortBy) {
        case 'accuracy':
          filteredExperts.sort((a, b) => b.performance_metrics.accuracy_rate - a.performance_metrics.accuracy_rate);
          break;
        case 'followers':
          filteredExperts.sort((a, b) => b.performance_metrics.followers_count - a.performance_metrics.followers_count);
          break;
        case 'roi':
          filteredExperts.sort((a, b) => b.performance_metrics.monthly_roi - a.performance_metrics.monthly_roi);
          break;
        case 'picks':
          filteredExperts.sort((a, b) => b.performance_metrics.total_picks - a.performance_metrics.total_picks);
          break;
      }
      
      if (limit) {
        filteredExperts = filteredExperts.slice(0, limit);
      }
      
      setExperts(filteredExperts);
    } catch (error) {
      console.error('Failed to load experts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async (expertId: string) => {
    try {
      // TODO: Implement follow functionality
      console.log('Following expert:', expertId);
      // Update local state optimistically
      setExperts(prev => 
        prev.map(expert => 
          expert.id === expertId 
            ? { ...expert, is_following: true, performance_metrics: { 
                ...expert.performance_metrics, 
                followers_count: expert.performance_metrics.followers_count + 1 
              }}
            : expert
        )
      );
    } catch (error) {
      console.error('Failed to follow expert:', error);
    }
  };

  const handleSubscribe = async (expertId: string, tier: string) => {
    try {
      // TODO: Implement subscription functionality
      console.log('Subscribing to expert:', expertId, 'tier:', tier);
      // Update local state optimistically
      setExperts(prev => 
        prev.map(expert => 
          expert.id === expertId 
            ? { ...expert, is_subscribed: true }
            : expert
        )
      );
    } catch (error) {
      console.error('Failed to subscribe to expert:', error);
    }
  };

  const handleViewProfile = (expertId: string) => {
    // TODO: Navigate to expert profile page
    console.log('Viewing expert profile:', expertId);
  };

  const getUniqueSpecializations = () => {
    const allSpecs = experts.flatMap(expert => expert.specialization);
    return [...new Set(allSpecs)];
  };

  if (loading) {
    return (
      <Card className="bg-slate-900/50 border-slate-700">
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white font-bold">
            <Award className="h-5 w-5 text-blue-400" />
            Expert Analysts
            <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">{experts.length} experts</Badge>
          </CardTitle>
        </CardHeader>
        
        {showFilters && (
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search experts by name or specialization..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
              />
            </div>
            
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="accuracy">Accuracy Rate</SelectItem>
                  <SelectItem value="followers">Followers</SelectItem>
                  <SelectItem value="roi">Monthly ROI</SelectItem>
                  <SelectItem value="picks">Total Picks</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterTier} onValueChange={setFilterTier}>
                <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                  <SelectValue placeholder="Filter by tier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterSpecialization} onValueChange={setFilterSpecialization}>
                <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                  <SelectValue placeholder="Specialization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Specializations</SelectItem>
                  {getUniqueSpecializations().map((spec) => (
                    <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Results */}
      {experts.length === 0 ? (
        <Card className="bg-slate-900/50 border-slate-700">
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No experts found</h3>
            <p className="text-slate-300">Try adjusting your search or filters</p>
          </CardContent>
        </Card>
      ) : (
        <div className={
          layout === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        }>
          {experts.map((expert) => (
            <ExpertCard
              key={expert.id}
              expert={expert}
              onFollow={handleFollow}
              onSubscribe={handleSubscribe}
              onViewProfile={handleViewProfile}
            />
          ))}
        </div>
      )}

      {/* Stats Summary */}
      <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
        <CardContent className="py-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="flex items-center justify-center gap-1 text-blue-400 mb-1">
                <Award className="h-4 w-4" />
                <span className="font-semibold">
                  {experts.filter(e => e.verification_status === 'verified').length}
                </span>
              </div>
              <p className="text-xs text-slate-300">Verified Experts</p>
            </div>
            
            <div>
              <div className="flex items-center justify-center gap-1 text-green-400 mb-1">
                <TrendingUp className="h-4 w-4" />
                <span className="font-semibold">
                  {Math.round(experts.reduce((sum, e) => sum + e.performance_metrics.accuracy_rate, 0) / experts.length)}%
                </span>
              </div>
              <p className="text-xs text-slate-300">Avg Accuracy</p>
            </div>
            
            <div>
              <div className="flex items-center justify-center gap-1 text-purple-400 mb-1">
                <Users className="h-4 w-4" />
                <span className="font-semibold">
                  {experts.reduce((sum, e) => sum + e.performance_metrics.followers_count, 0).toLocaleString()}
                </span>
              </div>
              <p className="text-xs text-slate-300">Total Followers</p>
            </div>
            
            <div>
              <div className="flex items-center justify-center gap-1 text-yellow-400 mb-1">
                <Star className="h-4 w-4" />
                <span className="font-semibold">
                  {experts.reduce((sum, e) => sum + e.performance_metrics.total_picks, 0).toLocaleString()}
                </span>
              </div>
              <p className="text-xs text-slate-300">Total Picks</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}