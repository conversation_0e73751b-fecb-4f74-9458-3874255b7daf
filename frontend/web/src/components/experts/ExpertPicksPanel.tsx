/**
 * Expert Picks Panel Component
 * Displays expert predictions and analysis with betting integration
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useMultiServiceApi } from '@/hooks/useApi';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Target,
  Star,
  Lock,
  Eye,
  ThumbsUp,
  MessageCircle,
  DollarSign,
  BarChart3
} from 'lucide-react';

interface ExpertPick {
  id: string;
  expert_id: string;
  expert_name: string;
  event_title: string;
  prediction: string;
  confidence_level: number;
  expected_outcome: 'win' | 'loss' | 'draw';
  odds_when_picked: number;
  current_odds?: number;
  reasoning: string;
  tags: string[];
  created_at: string;
  event_date: string;
  status: 'pending' | 'won' | 'lost' | 'void';
  tier_required: 'free' | 'basic' | 'premium';
  engagement: {
    likes: number;
    comments: number;
    views: number;
  };
  potential_return?: number;
}

interface ExpertPicksPanelProps {
  expertId?: string;
  limit?: number;
  showSubscriptionGate?: boolean;
  userSubscriptionTier?: 'free' | 'basic' | 'premium';
}

export function ExpertPicksPanel({ 
  expertId, 
  limit = 10,
  showSubscriptionGate = true,
  userSubscriptionTier = 'free'
}: ExpertPicksPanelProps) {
  const multiApi = useMultiServiceApi();
  const [picks, setPicks] = useState<ExpertPick[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('recent');

  useEffect(() => {
    loadExpertPicks();
  }, [expertId, activeTab]);

  const loadExpertPicks = async () => {
    try {
      setLoading(true);
      const response = await multiApi.experts.getPicks();
      
      // Filter picks based on tab and access level
      let filteredPicks = response.data.picks || [];
      
      if (expertId) {
        filteredPicks = filteredPicks.filter(pick => pick.expert_id === expertId);
      }
      
      switch (activeTab) {
        case 'recent':
          filteredPicks = filteredPicks.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          break;
        case 'hot':
          filteredPicks = filteredPicks.sort((a, b) => 
            (b.engagement.likes + b.engagement.comments) - (a.engagement.likes + a.engagement.comments)
          );
          break;
        case 'winning':
          filteredPicks = filteredPicks.filter(pick => pick.status === 'won');
          break;
      }
      
      setPicks(filteredPicks.slice(0, limit));
    } catch (error) {
      console.error('Failed to load expert picks:', error);
    } finally {
      setLoading(false);
    }
  };

  const canViewPick = (pick: ExpertPick) => {
    if (pick.tier_required === 'free') return true;
    if (pick.tier_required === 'basic' && ['basic', 'premium'].includes(userSubscriptionTier)) return true;
    if (pick.tier_required === 'premium' && userSubscriptionTier === 'premium') return true;
    return false;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'won': return 'bg-green-100 text-green-800';
      case 'lost': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          Expert Picks
        </CardTitle>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="hot">Hot</TabsTrigger>
            <TabsTrigger value="winning">Winning</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>

      <CardContent className="space-y-4">
        {picks.length === 0 ? (
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No picks available</p>
          </div>
        ) : (
          picks.map((pick) => (
            <div key={pick.id} className="border rounded-lg p-4 space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold text-lg">{pick.event_title}</h4>
                  <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                    <span>{pick.expert_name}</span>
                    <Badge className={getStatusColor(pick.status)}>
                      {pick.status.toUpperCase()}
                    </Badge>
                    <Clock className="h-4 w-4" />
                    <span>{formatTimeAgo(pick.created_at)}</span>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`text-lg font-bold ${getConfidenceColor(pick.confidence_level)}`}>
                    {pick.confidence_level}%
                  </div>
                  <p className="text-xs text-gray-500">confidence</p>
                </div>
              </div>

              {/* Prediction */}
              {canViewPick(pick) ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {pick.expected_outcome === 'win' ? (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">{pick.prediction}</span>
                  </div>
                  
                  {pick.reasoning && (
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">
                      {pick.reasoning}
                    </p>
                  )}
                  
                  {/* Odds and Returns */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <BarChart3 className="h-4 w-4 text-gray-500" />
                        <span>Odds: {pick.odds_when_picked}</span>
                        {pick.current_odds && pick.current_odds !== pick.odds_when_picked && (
                          <span className={pick.current_odds > pick.odds_when_picked ? 'text-green-600' : 'text-red-600'}>
                            → {pick.current_odds}
                          </span>
                        )}
                      </div>
                      
                      {pick.potential_return && (
                        <div className="flex items-center gap-1 text-green-600">
                          <DollarSign className="h-4 w-4" />
                          <span>+{pick.potential_return}% potential</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-6 bg-gray-50 rounded border-2 border-dashed">
                  <div className="text-center">
                    <Lock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 font-medium">Premium Pick</p>
                    <p className="text-sm text-gray-500">Subscribe to view this analysis</p>
                    {showSubscriptionGate && (
                      <Button size="sm" className="mt-2">
                        Upgrade to {pick.tier_required}
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* Tags */}
              {pick.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {pick.tags.map((tag, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Engagement */}
              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <ThumbsUp className="h-4 w-4" />
                    <span>{pick.engagement.likes}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="h-4 w-4" />
                    <span>{pick.engagement.comments}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>{pick.engagement.views}</span>
                  </div>
                </div>
                
                <Button variant="outline" size="sm">
                  View Details
                </Button>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}