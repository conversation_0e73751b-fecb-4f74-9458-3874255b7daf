/**
 * Loading Boundary Component
 * ==========================
 * 
 * Centralized loading state management for async operations.
 */

'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { Loader2, Wifi, WifiOff } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
  error?: string;
  isOffline?: boolean;
}

interface LoadingContextValue {
  state: LoadingState;
  setLoading: (loading: boolean, message?: string, progress?: number) => void;
  setError: (error: string | null) => void;
  setOffline: (offline: boolean) => void;
  clearState: () => void;
}

const LoadingContext = createContext<LoadingContextValue | null>(null);

export function useLoading() {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingBoundary');
  }
  return context;
}

interface LoadingBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ state: LoadingState }>;
}

export function LoadingBoundary({ children, fallback: FallbackComponent }: LoadingBoundaryProps) {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
  });

  const setLoading = useCallback((loading: boolean, message?: string, progress?: number) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
      message: loading ? message : undefined,
      progress: loading ? progress : undefined,
      error: loading ? undefined : prev.error, // Clear error when starting new loading
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error: error || undefined,
      isLoading: false,
    }));
  }, []);

  const setOffline = useCallback((offline: boolean) => {
    setState(prev => ({
      ...prev,
      isOffline: offline,
    }));
  }, []);

  const clearState = useCallback(() => {
    setState({
      isLoading: false,
    });
  }, []);

  const contextValue: LoadingContextValue = {
    state,
    setLoading,
    setError,
    setOffline,
    clearState,
  };

  // Show loading UI if loading
  if (state.isLoading) {
    if (FallbackComponent) {
      return <FallbackComponent state={state} />;
    }

    return (
      <div className="flex items-center justify-center p-8">
        <Card className="w-full max-w-sm">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-fit">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
              
              {state.message && (
                <p className="text-sm text-muted-foreground">
                  {state.message}
                </p>
              )}
              
              {state.progress !== undefined && (
                <div className="space-y-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(100, Math.max(0, state.progress))}%` }}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {Math.round(state.progress)}% complete
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state if error
  if (state.error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            {state.error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Show offline indicator if offline
  if (state.isOffline) {
    return (
      <div className="p-4">
        <Alert>
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            You're currently offline. Some features may not be available.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
    </LoadingContext.Provider>
  );
}

// Network status monitoring
export function useNetworkStatus() {
  const { setOffline } = useLoading();
  
  React.useEffect(() => {
    const handleOnline = () => setOffline(false);
    const handleOffline = () => setOffline(true);
    
    // Set initial status
    setOffline(!navigator.onLine);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [setOffline]);
}

// Component-level loading state
export function PageLoader({ 
  message = 'Loading...', 
  progress,
  fullScreen = false 
}: { 
  message?: string; 
  progress?: number;
  fullScreen?: boolean;
}) {
  const containerClass = fullScreen 
    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClass}>
      <Card className="w-full max-w-sm">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="mx-auto w-fit">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
            
            <p className="text-sm text-muted-foreground">
              {message}
            </p>
            
            {progress !== undefined && (
              <div className="space-y-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  {Math.round(progress)}% complete
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default LoadingBoundary;