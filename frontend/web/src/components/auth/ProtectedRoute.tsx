/**
 * BetBet Gaming Engine - Protected Route Component
 * ==============================================
 * 
 * Higher-order component for protecting routes that require authentication.
 */

import React from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Shield, Lock } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAdmin?: boolean;
  redirectTo?: string;
  showLoader?: boolean;
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requireAdmin = false,
  redirectTo = '/sign-in',
  showLoader = true
}: ProtectedRouteProps) {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const router = useRouter();

  // Show loading while auth is being determined
  if (!isLoaded && showLoader) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto" />
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Check if user is signed in
  if (!isSignedIn) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardContent className="p-6 text-center space-y-4">
          <Lock className="h-12 w-12 text-gray-400 mx-auto" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Authentication Required
            </h2>
            <p className="text-gray-600">
              You must be signed in to access this page.
            </p>
          </div>
          <Button 
            onClick={() => router.push(redirectTo)}
            className="w-full"
          >
            Sign In to Continue
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Check admin requirement
  if (requireAdmin) {
    const userRole = user?.publicMetadata?.role as string;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    if (!isAdmin) {
      if (fallback) {
        return <>{fallback}</>;
      }

      return (
        <Card className="max-w-md mx-auto mt-8 border-red-200 bg-red-50">
          <CardContent className="p-6 text-center space-y-4">
            <Shield className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h2 className="text-xl font-semibold text-red-900 mb-2">
                Admin Access Required
              </h2>
              <p className="text-red-700">
                You don't have permission to access this page.
              </p>
            </div>
            <Button 
              variant="outline"
              onClick={() => router.push('/')}
              className="w-full"
            >
              Go to Home
            </Button>
          </CardContent>
        </Card>
      );
    }
  }

  // User is authenticated and authorized
  return <>{children}</>;
}

// Higher-order component version
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}