/**
 * BetBet Gaming Engine - Tournament Registration Modal
 * ==================================================
 * 
 * Modal component for registering for tournaments with payment flow.
 */

import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Trophy,
  Users,
  DollarSign,
  Calendar,
  Clock,
  Target,
  Crown,
  Eye,
  Loader2,
  AlertCircle,
  CheckCircle,
  UserPlus,
  CreditCard,
  Shield,
  Hash
} from 'lucide-react';

interface Tournament {
  id: string;
  slug: string;
  name: string;
  description?: string;
  game_id: string;
  game_name?: string;
  tournament_type: string;
  status: string;
  entry_fee: number;
  prize_pool: number;
  max_participants: number;
  current_participants: number;
  registration_start_time: string;
  registration_end_time: string;
  tournament_start_time: string;
  estimated_duration_minutes: number;
  bracket_type: string;
  allows_spectators: boolean;
  allows_spectator_betting: boolean;
  rules?: string[];
}

interface TournamentRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  tournament: Tournament | null;
  onRegistrationSuccess?: (tournamentId: string) => void;
}

export function TournamentRegistrationModal({ 
  isOpen, 
  onClose, 
  tournament,
  onRegistrationSuccess 
}: TournamentRegistrationModalProps) {
  const api = useApi();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptRules, setAcceptRules] = useState(false);
  const [step, setStep] = useState<'details' | 'payment' | 'confirmation'>('details');

  useEffect(() => {
    if (isOpen && tournament) {
      // Reset state when modal opens
      setStep('details');
      setAcceptTerms(false);
      setAcceptRules(false);
      setError(null);
      setSuccess(false);
    }
  }, [isOpen, tournament]);

  const handleRegister = async () => {
    if (!tournament) return;

    try {
      setLoading(true);
      setError(null);

      // If there's an entry fee, go to payment step
      if (tournament.entry_fee > 0 && step === 'details') {
        setStep('payment');
        setLoading(false);
        return;
      }

      // Register for tournament
      await api.registerForTournament(tournament.id);
      
      setSuccess(true);
      setStep('confirmation');
      
      // Call success callback
      if (onRegistrationSuccess) {
        onRegistrationSuccess(tournament.id);
      }

      // Auto-close after success
      setTimeout(() => {
        handleClose();
      }, 2000);

    } catch (err) {
      console.error('Failed to register for tournament:', err);
      setError('Failed to register for tournament. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!tournament) return;

    try {
      setLoading(true);
      setError(null);

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Register for tournament after payment
      await api.registerForTournament(tournament.id);
      
      setSuccess(true);
      setStep('confirmation');
      
      // Call success callback
      if (onRegistrationSuccess) {
        onRegistrationSuccess(tournament.id);
      }

      // Auto-close after success
      setTimeout(() => {
        handleClose();
      }, 2000);

    } catch (err) {
      console.error('Payment failed:', err);
      setError('Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setStep('details');
    setAcceptTerms(false);
    setAcceptRules(false);
    setError(null);
    setSuccess(false);
    onClose();
  };

  const getBracketIcon = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return <Target className="h-4 w-4" />;
      case 'double_elimination':
        return <Crown className="h-4 w-4" />;
      case 'round_robin':
        return <Users className="h-4 w-4" />;
      default:
        return <Trophy className="h-4 w-4" />;
    }
  };

  const getBracketLabel = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return 'Single Elimination';
      case 'double_elimination':
        return 'Double Elimination';
      case 'round_robin':
        return 'Round Robin';
      default:
        return bracketType;
    }
  };

  const isRegistrationAvailable = () => {
    if (!tournament) return false;
    const now = new Date();
    const regEnd = new Date(tournament.registration_end_time);
    return tournament.status === 'registration_open' && 
           now <= regEnd &&
           tournament.current_participants < tournament.max_participants;
  };

  const canProceed = () => {
    if (!tournament) return false;
    if (!acceptTerms) return false;
    if (tournament.rules && tournament.rules.length > 0 && !acceptRules) return false;
    return true;
  };

  if (!tournament) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            {step === 'confirmation' ? 'Registration Successful!' : 'Tournament Registration'}
          </DialogTitle>
          <DialogDescription>
            {step === 'details' && 'Review tournament details and confirm your registration'}
            {step === 'payment' && 'Complete payment to secure your tournament spot'}
            {step === 'confirmation' && 'You have successfully registered for this tournament'}
          </DialogDescription>
        </DialogHeader>

        {/* Tournament Details Step */}
        {step === 'details' && (
          <div className="space-y-6">
            {/* Tournament Overview */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl">{tournament.name}</CardTitle>
                    <CardDescription>{tournament.game_name}</CardDescription>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge variant="secondary" className="bg-blue-500 text-white">
                      {tournament.status === 'registration_open' ? 'Registration Open' : tournament.status}
                    </Badge>
                    {tournament.slug && (
                      <div className="flex items-center gap-1 text-xs text-gray-500 font-mono">
                        <Hash className="h-3 w-3" />
                        <span>{tournament.slug.slice(0, 20)}...</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {tournament.description && (
                  <p className="text-gray-600">{tournament.description}</p>
                )}

                {/* Tournament Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <Users className="h-5 w-5 mx-auto mb-1 text-blue-600" />
                    <p className="text-sm font-medium">{tournament.current_participants}/{tournament.max_participants}</p>
                    <p className="text-xs text-gray-600">Players</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <DollarSign className="h-5 w-5 mx-auto mb-1 text-green-600" />
                    <p className="text-sm font-medium">${tournament.prize_pool}</p>
                    <p className="text-xs text-gray-600">Prize Pool</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <Clock className="h-5 w-5 mx-auto mb-1 text-orange-600" />
                    <p className="text-sm font-medium">{tournament.estimated_duration_minutes}m</p>
                    <p className="text-xs text-gray-600">Duration</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    {getBracketIcon(tournament.bracket_type)}
                    <p className="text-sm font-medium">{getBracketLabel(tournament.bracket_type)}</p>
                    <p className="text-xs text-gray-600">Format</p>
                  </div>
                </div>

                {/* Tournament Schedule */}
                <div className="space-y-2">
                  <h4 className="font-medium">Tournament Schedule</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>Registration ends: {new Date(tournament.registration_end_time).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>Tournament starts: {new Date(tournament.tournament_start_time).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Features */}
                <div className="flex flex-wrap gap-2">
                  {tournament.allows_spectators && (
                    <Badge variant="outline">
                      <Eye className="h-3 w-3 mr-1" />
                      Spectators Allowed
                    </Badge>
                  )}
                  {tournament.allows_spectator_betting && (
                    <Badge variant="outline">
                      <DollarSign className="h-3 w-3 mr-1" />
                      Betting Enabled
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Entry Fee */}
            {tournament.entry_fee > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Entry Fee
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-lg">
                    <span>Tournament Entry:</span>
                    <span className="font-bold">${tournament.entry_fee}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    Entry fee contributes to the total prize pool for winners
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Rules */}
            {tournament.rules && tournament.rules.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Tournament Rules
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {tournament.rules.map((rule, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </span>
                        <span>{rule}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Registration Status Check */}
            {!isRegistrationAvailable() && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="flex items-center gap-3 py-4">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="font-medium text-red-800">Registration Not Available</p>
                    <p className="text-sm text-red-600">
                      {tournament.current_participants >= tournament.max_participants 
                        ? 'Tournament is full' 
                        : 'Registration period has ended'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Terms and Conditions */}
            {isRegistrationAvailable() && (
              <div className="space-y-3">
                <Separator />
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Checkbox 
                      id="accept-terms"
                      checked={acceptTerms}
                      onCheckedChange={setAcceptTerms}
                    />
                    <label htmlFor="accept-terms" className="text-sm leading-relaxed">
                      I agree to the tournament terms and conditions, including fair play policies and prize distribution rules.
                    </label>
                  </div>
                  
                  {tournament.rules && tournament.rules.length > 0 && (
                    <div className="flex items-start gap-3">
                      <Checkbox 
                        id="accept-rules"
                        checked={acceptRules}
                        onCheckedChange={setAcceptRules}
                      />
                      <label htmlFor="accept-rules" className="text-sm leading-relaxed">
                        I have read and accept the tournament-specific rules listed above.
                      </label>
                    </div>
                  )}
                </div>
              </div>
            )}

            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="flex items-center gap-2 py-4">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-red-800">{error}</span>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 justify-end pt-4">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              {isRegistrationAvailable() && (
                <Button 
                  onClick={handleRegister}
                  disabled={!canProceed() || loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      {tournament.entry_fee > 0 ? 'Proceed to Payment' : 'Register Now'}
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Payment Step */}
        {step === 'payment' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <span>Tournament Entry Fee:</span>
                  <span className="text-xl font-bold">${tournament.entry_fee}</span>
                </div>
                
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">Payment processing would be integrated here</p>
                  <p className="text-sm text-gray-500">
                    This is a demo - in production, this would integrate with a payment processor
                  </p>
                </div>

                <div className="flex gap-3 justify-end">
                  <Button variant="outline" onClick={() => setStep('details')}>
                    Back
                  </Button>
                  <Button onClick={handlePayment} disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Processing Payment...
                      </>
                    ) : (
                      <>
                        <CreditCard className="h-4 w-4 mr-2" />
                        Complete Payment
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Confirmation Step */}
        {step === 'confirmation' && (
          <div className="space-y-6 text-center">
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-600" />
              <div>
                <h3 className="text-xl font-bold text-green-800">Registration Successful!</h3>
                <p className="text-gray-600">You have been registered for {tournament.name}</p>
              </div>
            </div>

            <Card>
              <CardContent className="p-6 space-y-3">
                <div className="flex justify-between">
                  <span>Tournament:</span>
                  <span className="font-medium">{tournament.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Game:</span>
                  <span className="font-medium">{tournament.game_name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Start Time:</span>
                  <span className="font-medium">{new Date(tournament.tournament_start_time).toLocaleString()}</span>
                </div>
                {tournament.entry_fee > 0 && (
                  <div className="flex justify-between">
                    <span>Entry Fee Paid:</span>
                    <span className="font-medium">${tournament.entry_fee}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <p className="text-sm text-gray-600">
              You will receive notifications about tournament updates and when it's time to compete.
            </p>

            <Button onClick={handleClose} className="w-full">
              Close
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}