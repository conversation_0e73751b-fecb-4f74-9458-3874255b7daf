/**
 * BetBet Gaming Engine - Tournament List Component
 * ===============================================
 * 
 * Displays a list of tournaments with filtering and registration options.
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  Trophy, 
  Users, 
  Calendar, 
  DollarSign, 
  Clock,
  Loader2,
  AlertCircle,
  Search,
  Filter,
  Eye,
  UserPlus,
  Crown,
  Target,
  Hash,
  Zap
} from 'lucide-react';

interface TournamentParticipant {
  user_id: string;
  username?: string;
  registration_time: string;
  current_rank?: number;
  elimination_round?: number;
}

interface Tournament {
  id: string;
  slug: string;
  name: string;
  description?: string;
  game_id: string;
  game_name?: string;
  tournament_type: string;
  status: string;
  entry_fee: number;
  prize_pool: number;
  max_participants: number;
  current_participants: number;
  registration_start_time: string;
  registration_end_time: string;
  tournament_start_time: string;
  estimated_duration_minutes: number;
  bracket_type: string;
  allows_spectators: boolean;
  allows_spectator_betting: boolean;
  participants?: TournamentParticipant[];
  created_at: string;
  updated_at: string;
}

interface TournamentListProps {
  limit?: number;
  gameId?: string;
  status?: string;
  showRegistrationOpen?: boolean;
}

export function TournamentList({ 
  limit = 12, 
  gameId,
  status,
  showRegistrationOpen = false
}: TournamentListProps) {
  const api = useApi();
  const router = useRouter();
  
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('start_time');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [registering, setRegistering] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadTournaments();
  }, [limit, gameId, status, showRegistrationOpen, sortBy]);

  const loadTournaments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {
        limit,
        game_id: gameId,
        status: status || (showRegistrationOpen ? 'registration_open' : undefined),
        sort_by: sortBy,
      };

      const response = await api.getTournaments(params);
      setTournaments(response);
    } catch (err) {
      console.error('Failed to load tournaments:', err);
      setError('Failed to load tournaments');
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterForTournament = async (tournamentId: string) => {
    try {
      setRegistering(prev => new Set(prev).add(tournamentId));
      await api.registerForTournament(tournamentId);
      
      // Reload tournaments to get updated participant count
      await loadTournaments();
    } catch (err) {
      console.error('Failed to register for tournament:', err);
      alert('Failed to register for tournament');
    } finally {
      setRegistering(prev => {
        const newSet = new Set(prev);
        newSet.delete(tournamentId);
        return newSet;
      });
    }
  };

  const handleViewTournament = (tournament: Tournament) => {
    router.push(`/tournaments/${tournament.slug}`);
  };

  const getStatusColor = (tournamentStatus: string) => {
    switch (tournamentStatus) {
      case 'registration_open':
        return 'bg-blue-500';
      case 'registration_closed':
        return 'bg-orange-500';
      case 'in_progress':
        return 'bg-green-500';
      case 'completed':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (tournamentStatus: string) => {
    switch (tournamentStatus) {
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return tournamentStatus;
    }
  };

  const getBracketIcon = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return <Target className="h-4 w-4" />;
      case 'double_elimination':
        return <Crown className="h-4 w-4" />;
      case 'round_robin':
        return <Users className="h-4 w-4" />;
      default:
        return <Trophy className="h-4 w-4" />;
    }
  };

  const getBracketLabel = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return 'Single Elimination';
      case 'double_elimination':
        return 'Double Elimination';
      case 'round_robin':
        return 'Round Robin';
      default:
        return bracketType;
    }
  };

  const isRegistrationOpen = (tournament: Tournament) => {
    const now = new Date();
    const regStart = new Date(tournament.registration_start_time);
    const regEnd = new Date(tournament.registration_end_time);
    return tournament.status === 'registration_open' && 
           now >= regStart && 
           now <= regEnd &&
           tournament.current_participants < tournament.max_participants;
  };

  const filteredTournaments = tournaments.filter(tournament => {
    if (filterStatus !== 'all' && tournament.status !== filterStatus) {
      return false;
    }
    if (searchTerm && !tournament.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadTournaments}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tournaments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tournaments</SelectItem>
            <SelectItem value="registration_open">Registration Open</SelectItem>
            <SelectItem value="registration_closed">Registration Closed</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="start_time">Start Time</SelectItem>
            <SelectItem value="prize_pool">Prize Pool</SelectItem>
            <SelectItem value="participants">Participants</SelectItem>
            <SelectItem value="entry_fee">Entry Fee</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tournament Grid */}
      {filteredTournaments.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'No tournaments match your search' : 'No tournaments available'}
            </p>
            <Button 
              variant="outline" 
              onClick={() => router.push('/tournaments/create')}
            >
              Create Tournament
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTournaments.map((tournament) => (
            <Card key={tournament.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg leading-6">{tournament.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="secondary" 
                        className={`${getStatusColor(tournament.status)} text-white text-xs`}
                      >
                        {getStatusLabel(tournament.status)}
                      </Badge>
                      {tournament.allows_spectator_betting && (
                        <Badge variant="outline" className="text-xs">
                          <DollarSign className="h-3 w-3 mr-1" />
                          Betting
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Trophy className="h-5 w-5 text-yellow-500 flex-shrink-0" />
                </div>
                
                {tournament.game_name && (
                  <CardDescription className="text-sm">
                    {tournament.game_name}
                  </CardDescription>
                )}
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Tournament Stats */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span>{tournament.current_participants}/{tournament.max_participants}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span>${tournament.prize_pool}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-orange-600" />
                    <span>{tournament.estimated_duration_minutes}m</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getBracketIcon(tournament.bracket_type)}
                    <span className="text-xs">{getBracketLabel(tournament.bracket_type)}</span>
                  </div>
                </div>

                {/* Entry Fee */}
                {tournament.entry_fee > 0 && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Entry Fee:</span>
                    <span className="font-medium">${tournament.entry_fee}</span>
                  </div>
                )}

                {/* Tournament Times */}
                <div className="space-y-1 text-xs text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Starts: {new Date(tournament.tournament_start_time).toLocaleDateString()}</span>
                  </div>
                  {isRegistrationOpen(tournament) && (
                    <div className="flex items-center gap-1 text-orange-600">
                      <Clock className="h-3 w-3" />
                      <span>Reg ends: {new Date(tournament.registration_end_time).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>

                {/* Slug */}
                {tournament.slug && (
                  <div className="flex items-center gap-1 text-xs text-gray-500 font-mono">
                    <Hash className="h-3 w-3" />
                    <span>{tournament.slug.slice(0, 20)}...</span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  {isRegistrationOpen(tournament) ? (
                    <Button 
                      size="sm" 
                      onClick={() => handleRegisterForTournament(tournament.id)}
                      disabled={registering.has(tournament.id)}
                      className="flex-1"
                    >
                      {registering.has(tournament.id) ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Registering...
                        </>
                      ) : (
                        <>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Register
                        </>
                      )}
                    </Button>
                  ) : (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleViewTournament(tournament)}
                      className="flex-1"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  )}
                  
                  {tournament.allows_spectators && (
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleViewTournament(tournament)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {filteredTournaments.length === limit && (
        <div className="text-center pt-6">
          <Button 
            variant="outline" 
            onClick={() => loadTournaments()}
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            Load More Tournaments
          </Button>
        </div>
      )}
    </div>
  );
}