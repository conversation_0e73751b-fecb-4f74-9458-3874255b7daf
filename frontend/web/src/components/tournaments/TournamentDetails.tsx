/**
 * BetBet Gaming Engine - Tournament Details Component
 * =================================================
 * 
 * Comprehensive tournament details with bracket, registration, and betting.
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShareButton } from '@/components/shared/ShareButton';
import { TournamentBracket } from './TournamentBracket';
import { TournamentRegistrationModal } from './TournamentRegistrationModal';
import { BettingMarkets } from '@/components/betting/BettingMarkets';
import { 
  Trophy,
  Users,
  Calendar,
  DollarSign,
  Clock,
  Target,
  Crown,
  Eye,
  UserPlus,
  Loader2,
  AlertCircle,
  Hash,
  Zap,
  Play,
  CheckCircle
} from 'lucide-react';

interface TournamentParticipant {
  user_id: string;
  username?: string;
  registration_time: string;
  current_rank?: number;
  elimination_round?: number;
}

interface Tournament {
  id: string;
  slug: string;
  name: string;
  description?: string;
  game_id: string;
  game_name?: string;
  tournament_type: string;
  status: string;
  entry_fee: number;
  prize_pool: number;
  max_participants: number;
  current_participants: number;
  registration_start_time: string;
  registration_end_time: string;
  tournament_start_time: string;
  estimated_duration_minutes: number;
  bracket_type: string;
  allows_spectators: boolean;
  allows_spectator_betting: boolean;
  participants?: TournamentParticipant[];
  rules?: string[];
  created_at: string;
  updated_at: string;
}

interface TournamentDetailsProps {
  tournamentId?: string;
  tournamentSlug?: string;
}

export function TournamentDetails({ tournamentId, tournamentSlug }: TournamentDetailsProps) {
  const api = useApi();
  const router = useRouter();
  
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);

  // WebSocket connection for real-time updates
  const { isConnected, lastMessage } = useWebSocket(
    tournament?.id || null,
    {
      onMessage: (message) => {
        if (message.type === 'tournament_update') {
          setTournament(prev => prev ? { ...prev, ...message.data } : null);
        } else if (message.type === 'participant_registered' || message.type === 'participant_unregistered') {
          loadTournament(); // Reload to get updated participant list
        }
      }
    }
  );

  useEffect(() => {
    if (tournamentId || tournamentSlug) {
      loadTournament();
    }
  }, [tournamentId, tournamentSlug]);

  const loadTournament = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      if (tournamentSlug) {
        response = await api.getTournamentBySlug(tournamentSlug);
      } else if (tournamentId) {
        response = await api.getTournament(tournamentId);
      } else {
        throw new Error('No tournament ID or slug provided');
      }
      
      setTournament(response);
    } catch (err) {
      console.error('Failed to load tournament:', err);
      setError('Failed to load tournament details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registration_open':
        return 'bg-blue-500';
      case 'registration_closed':
        return 'bg-orange-500';
      case 'in_progress':
        return 'bg-green-500';
      case 'completed':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const getBracketIcon = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return <Target className="h-4 w-4" />;
      case 'double_elimination':
        return <Crown className="h-4 w-4" />;
      case 'round_robin':
        return <Users className="h-4 w-4" />;
      default:
        return <Trophy className="h-4 w-4" />;
    }
  };

  const getBracketLabel = (bracketType: string) => {
    switch (bracketType) {
      case 'single_elimination':
        return 'Single Elimination';
      case 'double_elimination':
        return 'Double Elimination';
      case 'round_robin':
        return 'Round Robin';
      default:
        return bracketType;
    }
  };

  const isRegistrationOpen = () => {
    if (!tournament) return false;
    const now = new Date();
    const regStart = new Date(tournament.registration_start_time);
    const regEnd = new Date(tournament.registration_end_time);
    return tournament.status === 'registration_open' && 
           now >= regStart && 
           now <= regEnd &&
           tournament.current_participants < tournament.max_participants;
  };

  const handleRegistrationSuccess = () => {
    loadTournament(); // Reload tournament data
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error || !tournament) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error || 'Tournament not found'}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => router.push('/tournaments')}
              className="mt-2"
            >
              Back to Tournaments
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{tournament.name}</h1>
            <Badge 
              variant="secondary" 
              className={`${getStatusColor(tournament.status)} text-white`}
            >
              {getStatusLabel(tournament.status)}
            </Badge>
            {isConnected && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                Live
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <Target className="h-4 w-4" />
              {tournament.game_name}
            </span>
            {tournament.slug && (
              <span className="flex items-center gap-1 font-mono">
                <Hash className="h-4 w-4" />
                {tournament.slug}
              </span>
            )}
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {new Date(tournament.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="flex gap-2">
          <ShareButton
            url={`${window.location.origin}/tournaments/${tournament.slug}`}
            title={`Join ${tournament.name} Tournament on BetBet`}
            description={`${tournament.game_name} tournament with $${tournament.prize_pool} prize pool. ${tournament.current_participants}/${tournament.max_participants} registered.`}
            hashtags={['BetBet', 'Tournament', 'Gaming', tournament.game_name?.replace(/\s+/g, '') || 'Game']}
            size="sm"
            showLabel={true}
          />
          
          {isRegistrationOpen() && (
            <Button
              onClick={() => setShowRegistrationModal(true)}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Register
            </Button>
          )}
          
          {tournament.allows_spectators && tournament.status === 'in_progress' && (
            <Button
              variant="outline"
              onClick={() => setActiveTab('bracket')}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Watch Live
            </Button>
          )}
        </div>
      </div>

      {/* Tournament Info Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{tournament.current_participants}/{tournament.max_participants}</p>
            <p className="text-sm text-gray-600">Participants</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <DollarSign className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">${tournament.prize_pool}</p>
            <p className="text-sm text-gray-600">Prize Pool</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-6 w-6 text-orange-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{tournament.estimated_duration_minutes}m</p>
            <p className="text-sm text-gray-600">Duration</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            {getBracketIcon(tournament.bracket_type)}
            <p className="text-sm font-bold mt-2">{getBracketLabel(tournament.bracket_type)}</p>
            <p className="text-sm text-gray-600">Format</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="participants">Participants</TabsTrigger>
          <TabsTrigger value="bracket">Bracket</TabsTrigger>
          <TabsTrigger value="betting" disabled={!tournament.allows_spectator_betting}>
            Betting
          </TabsTrigger>
          <TabsTrigger value="rules">Rules</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tournament Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {tournament.description && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <p className="mt-1">{tournament.description}</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Game</label>
                  <p>{tournament.game_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Tournament Type</label>
                  <p>{tournament.tournament_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Entry Fee</label>
                  <p>${tournament.entry_fee}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Bracket Format</label>
                  <p>{getBracketLabel(tournament.bracket_type)}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Schedule</label>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span>Registration: {new Date(tournament.registration_start_time).toLocaleString()} - {new Date(tournament.registration_end_time).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Play className="h-4 w-4 text-gray-400" />
                    <span>Tournament starts: {new Date(tournament.tournament_start_time).toLocaleString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2 flex-wrap">
                {tournament.allows_spectators && (
                  <Badge variant="outline">
                    <Eye className="h-3 w-3 mr-1" />
                    Spectators Allowed
                  </Badge>
                )}
                {tournament.allows_spectator_betting && (
                  <Badge variant="outline">
                    <DollarSign className="h-3 w-3 mr-1" />
                    Betting Enabled
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="participants" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Registered Participants ({tournament.current_participants}/{tournament.max_participants})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {tournament.participants && tournament.participants.length > 0 ? (
                  tournament.participants
                    .sort((a, b) => new Date(a.registration_time).getTime() - new Date(b.registration_time).getTime())
                    .map((participant, index) => (
                      <div key={participant.user_id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-800 text-sm font-medium">
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium">
                              {participant.username || `Player ${participant.user_id.slice(0, 8)}`}
                            </p>
                            <p className="text-xs text-gray-500">
                              Registered: {new Date(participant.registration_time).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        {participant.current_rank && (
                          <div className="text-right">
                            <p className="font-bold">#{participant.current_rank}</p>
                            <p className="text-xs text-gray-500">Current Rank</p>
                          </div>
                        )}
                      </div>
                    ))
                ) : (
                  <p className="text-gray-500 text-center py-8">No participants registered yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bracket" className="space-y-6">
          {tournament.status === 'registration_open' || tournament.status === 'registration_closed' ? (
            <Card>
              <CardContent className="text-center py-12">
                <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Tournament bracket will be available once the tournament starts</p>
                <p className="text-sm text-gray-500">
                  Starts: {new Date(tournament.tournament_start_time).toLocaleString()}
                </p>
              </CardContent>
            </Card>
          ) : (
            <TournamentBracket 
              tournamentId={tournament.id} 
              isLive={tournament.status === 'in_progress'}
            />
          )}
        </TabsContent>

        <TabsContent value="betting" className="space-y-6">
          {tournament.allows_spectator_betting ? (
            <BettingMarkets 
              tournamentId={tournament.id}
              showLiveUpdates={tournament.status === 'in_progress'}
            />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Betting is not enabled for this tournament</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tournament Rules</CardTitle>
            </CardHeader>
            <CardContent>
              {tournament.rules && tournament.rules.length > 0 ? (
                <ol className="space-y-3">
                  {tournament.rules.map((rule, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </span>
                      <span>{rule}</span>
                    </li>
                  ))}
                </ol>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                  <p className="text-gray-600">Standard tournament rules apply</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Fair play, respect for opponents, and adherence to game-specific rules
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Registration Modal */}
      <TournamentRegistrationModal
        isOpen={showRegistrationModal}
        onClose={() => setShowRegistrationModal(false)}
        tournament={tournament}
        onRegistrationSuccess={handleRegistrationSuccess}
      />
    </div>
  );
}