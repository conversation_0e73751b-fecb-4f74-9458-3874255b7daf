/**
 * BetBet Gaming Engine - Tournament Bracket Viewer
 * ===============================================
 * 
 * Interactive tournament bracket visualization with real-time updates.
 */

import React, { useEffect, useState } from 'react';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { 
  Trophy,
  Users,
  Crown,
  Target,
  Clock,
  Calendar,
  Loader2,
  AlertCircle,
  Eye,
  Play,
  CheckCircle,
  User,
  Zap
} from 'lucide-react';

interface BracketParticipant {
  user_id: string;
  username?: string;
  seed: number;
  current_round: number;
  is_eliminated: boolean;
  elimination_round?: number;
  total_wins: number;
  total_losses: number;
}

interface BracketMatch {
  id: string;
  round: number;
  match_number: number;
  participant_1_id?: string;
  participant_2_id?: string;
  winner_id?: string;
  status: string;
  scheduled_time?: string;
  started_at?: string;
  completed_at?: string;
  score_participant_1?: number;
  score_participant_2?: number;
  next_match_id?: string;
}

interface TournamentBracket {
  tournament_id: string;
  bracket_type: string;
  total_rounds: number;
  current_round: number;
  participants: BracketParticipant[];
  matches: BracketMatch[];
  winner_id?: string;
  is_completed: boolean;
}

interface TournamentBracketProps {
  tournamentId: string;
  isLive?: boolean;
}

export function TournamentBracket({ tournamentId, isLive = false }: TournamentBracketProps) {
  const api = useApi();
  const [bracket, setBracket] = useState<TournamentBracket | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // WebSocket connection for live updates
  const { isConnected, lastMessage } = useWebSocket(
    isLive ? tournamentId : null,
    {
      onMessage: (message) => {
        if (message.type === 'bracket_update') {
          setBracket(prev => prev ? { ...prev, ...message.data } : null);
        } else if (message.type === 'match_update') {
          setBracket(prev => {
            if (!prev) return null;
            const updatedMatches = prev.matches.map(match => 
              match.id === message.data.match_id 
                ? { ...match, ...message.data.match }
                : match
            );
            return { ...prev, matches: updatedMatches };
          });
        }
      }
    }
  );

  useEffect(() => {
    loadBracket();
  }, [tournamentId]);

  const loadBracket = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.getTournamentBracket(tournamentId);
      setBracket(response);
    } catch (err) {
      console.error('Failed to load tournament bracket:', err);
      setError('Failed to load tournament bracket');
    } finally {
      setLoading(false);
    }
  };

  const getParticipantById = (id: string | undefined) => {
    if (!id || !bracket) return null;
    return bracket.participants.find(p => p.user_id === id);
  };

  const getMatchesByRound = (round: number) => {
    if (!bracket) return [];
    return bracket.matches.filter(match => match.round === round);
  };

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-gray-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getMatchStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'in_progress':
        return 'Live';
      case 'completed':
        return 'Complete';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const renderParticipant = (participantId: string | undefined, isWinner: boolean = false) => {
    const participant = getParticipantById(participantId);
    
    if (!participant) {
      return (
        <div className="flex items-center gap-2 p-2 border rounded bg-gray-50 text-gray-400">
          <User className="h-4 w-4" />
          <span className="text-sm">TBD</span>
        </div>
      );
    }

    return (
      <div className={`flex items-center gap-2 p-2 border rounded transition-colors ${
        isWinner 
          ? 'bg-green-50 border-green-200 text-green-800' 
          : participant.is_eliminated 
            ? 'bg-red-50 border-red-200 text-red-600'
            : 'bg-white border-gray-200'
      }`}>
        <Avatar className="h-6 w-6">
          <div className="w-full h-full bg-blue-100 flex items-center justify-center text-blue-800 text-xs font-medium">
            {participant.seed}
          </div>
        </Avatar>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">
            {participant.username || `Player ${participant.user_id.slice(0, 8)}`}
          </p>
          <p className="text-xs text-gray-500">
            Seed #{participant.seed}
          </p>
        </div>
        {isWinner && <Crown className="h-4 w-4 text-yellow-500" />}
      </div>
    );
  };

  const renderMatch = (match: BracketMatch) => {
    const participant1 = getParticipantById(match.participant_1_id);
    const participant2 = getParticipantById(match.participant_2_id);
    const isLiveMatch = match.status === 'in_progress';
    const isCompleted = match.status === 'completed';

    return (
      <Card key={match.id} className={`${isLiveMatch ? 'ring-2 ring-blue-500' : ''}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">
              Match {match.match_number}
            </CardTitle>
            <Badge 
              variant="secondary"
              className={`${getMatchStatusColor(match.status)} text-white text-xs`}
            >
              {getMatchStatusLabel(match.status)}
            </Badge>
          </div>
          {match.scheduled_time && (
            <CardDescription className="text-xs">
              <Calendar className="h-3 w-3 inline mr-1" />
              {new Date(match.scheduled_time).toLocaleString()}
            </CardDescription>
          )}
        </CardHeader>
        
        <CardContent className="space-y-2">
          <div className="space-y-2">
            {renderParticipant(match.participant_1_id, match.winner_id === match.participant_1_id)}
            
            <div className="flex items-center justify-center">
              <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                {isCompleted && match.score_participant_1 !== undefined && match.score_participant_2 !== undefined
                  ? `${match.score_participant_1} - ${match.score_participant_2}`
                  : 'VS'
                }
              </div>
            </div>
            
            {renderParticipant(match.participant_2_id, match.winner_id === match.participant_2_id)}
          </div>

          {isLiveMatch && (
            <div className="flex items-center justify-center gap-1 text-blue-600 text-xs mt-2">
              <Zap className="h-3 w-3" />
              <span>Live Match</span>
            </div>
          )}

          {match.status === 'scheduled' && match.participant_1_id && match.participant_2_id && (
            <Button size="sm" variant="outline" className="w-full mt-2">
              <Eye className="h-3 w-3 mr-1" />
              Watch
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderSingleEliminationBracket = () => {
    if (!bracket) return null;

    const rounds = Array.from({ length: bracket.total_rounds }, (_, i) => i + 1);

    return (
      <div className="space-y-8">
        {rounds.map(roundNumber => {
          const roundMatches = getMatchesByRound(roundNumber);
          const isCurrentRound = roundNumber === bracket.current_round;
          
          return (
            <div key={roundNumber} className="space-y-4">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">
                  {roundNumber === bracket.total_rounds 
                    ? 'Final' 
                    : roundNumber === bracket.total_rounds - 1 
                      ? 'Semi-Final'
                      : `Round ${roundNumber}`
                  }
                </h3>
                {isCurrentRound && (
                  <Badge variant="default">
                    <Play className="h-3 w-3 mr-1" />
                    Current Round
                  </Badge>
                )}
                {isLive && isConnected && isCurrentRound && (
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Live Updates
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {roundMatches.map(match => renderMatch(match))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderDoubleEliminationBracket = () => {
    // For double elimination, we'd need to separate winners and losers brackets
    // This is a simplified version - in production, you'd want more sophisticated layout
    return renderSingleEliminationBracket();
  };

  const renderRoundRobinBracket = () => {
    if (!bracket) return null;

    // For round robin, show all matches grouped by round
    const rounds = Array.from({ length: bracket.total_rounds }, (_, i) => i + 1);

    return (
      <div className="space-y-6">
        {rounds.map(roundNumber => {
          const roundMatches = getMatchesByRound(roundNumber);
          
          return (
            <div key={roundNumber} className="space-y-4">
              <h3 className="text-lg font-semibold">Round {roundNumber}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {roundMatches.map(match => renderMatch(match))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderBracket = () => {
    if (!bracket) return null;

    switch (bracket.bracket_type) {
      case 'single_elimination':
        return renderSingleEliminationBracket();
      case 'double_elimination':
        return renderDoubleEliminationBracket();
      case 'round_robin':
        return renderRoundRobinBracket();
      default:
        return renderSingleEliminationBracket();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadBracket}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!bracket) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No bracket data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Bracket Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-yellow-500" />
                Tournament Bracket
              </CardTitle>
              <CardDescription>
                {bracket.bracket_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Format
              </CardDescription>
            </div>
            {isLive && isConnected && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                <Zap className="h-3 w-3 mr-1" />
                Live Updates
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold">{bracket.participants.length}</p>
              <p className="text-sm text-gray-600">Participants</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{bracket.current_round}/{bracket.total_rounds}</p>
              <p className="text-sm text-gray-600">Current Round</p>
            </div>
            <div>
              <p className="text-2xl font-bold">
                {bracket.matches.filter(m => m.status === 'completed').length}
              </p>
              <p className="text-sm text-gray-600">Completed Matches</p>
            </div>
            <div>
              <p className="text-2xl font-bold">
                {bracket.participants.filter(p => !p.is_eliminated).length}
              </p>
              <p className="text-sm text-gray-600">Remaining</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Winner Announcement */}
      {bracket.is_completed && bracket.winner_id && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="text-center py-6">
            <Crown className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-yellow-800 mb-2">Tournament Champion!</h2>
            <div className="flex items-center justify-center gap-2">
              {renderParticipant(bracket.winner_id, true)}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bracket Visualization */}
      {renderBracket()}

      {/* Participants Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Participants ({bracket.participants.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {bracket.participants
              .sort((a, b) => a.seed - b.seed)
              .map(participant => (
                <div 
                  key={participant.user_id}
                  className={`flex items-center gap-2 p-3 rounded-lg border ${
                    participant.user_id === bracket.winner_id
                      ? 'bg-yellow-50 border-yellow-200'
                      : participant.is_eliminated
                        ? 'bg-red-50 border-red-200'
                        : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <Avatar className="h-8 w-8">
                    <div className="w-full h-full bg-blue-100 flex items-center justify-center text-blue-800 text-xs font-medium">
                      {participant.seed}
                    </div>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">
                      {participant.username || `Player ${participant.user_id.slice(0, 8)}`}
                      {participant.user_id === bracket.winner_id && (
                        <Crown className="h-4 w-4 inline ml-1 text-yellow-500" />
                      )}
                    </p>
                    <p className="text-xs text-gray-500">
                      {participant.is_eliminated 
                        ? `Eliminated R${participant.elimination_round}`
                        : `Round ${participant.current_round}`
                      }
                    </p>
                  </div>
                  <div className="text-xs text-gray-500">
                    {participant.total_wins}W-{participant.total_losses}L
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}