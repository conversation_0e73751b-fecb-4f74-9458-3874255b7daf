/**
 * BetBet Gaming Engine - Create Session Modal
 * ==========================================
 * 
 * Modal component for creating new game sessions.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  GamepadIcon,
  <PERSON>,
  Clock,
  DollarSign,
  Calendar,
  Settings,
  Eye,
  Loader2,
  AlertCircle,
  Trophy,
  Target
} from 'lucide-react';

interface Game {
  id: string;
  name: string;
  slug: string;
  category: string;
  description?: string;
  min_players: number;
  max_players: number;
  estimated_duration_minutes: number;
  scoring_system: string;
  has_spectator_betting: boolean;
  allows_practice_mode: boolean;
  is_active: boolean;
}

interface CreateSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGameId?: string;
  selectedGameSlug?: string;
}

export function CreateSessionModal({ 
  isOpen, 
  onClose, 
  selectedGameId,
  selectedGameSlug 
}: CreateSessionModalProps) {
  const api = useApi();
  const router = useRouter();
  
  const [games, setGames] = useState<Game[]>([]);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [sessionName, setSessionName] = useState('');
  const [entryFee, setEntryFee] = useState('0');
  const [minParticipants, setMinParticipants] = useState('2');
  const [maxParticipants, setMaxParticipants] = useState('4');
  const [scheduledStartTime, setScheduledStartTime] = useState('');
  const [allowSpectators, setAllowSpectators] = useState(true);
  const [allowPracticeMode, setAllowPracticeMode] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadGames();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedGameId && games.length > 0) {
      const game = games.find(g => g.id === selectedGameId);
      if (game) {
        setSelectedGame(game);
        updateFormDefaults(game);
      }
    } else if (selectedGameSlug && games.length > 0) {
      const game = games.find(g => g.slug === selectedGameSlug);
      if (game) {
        setSelectedGame(game);
        updateFormDefaults(game);
      }
    }
  }, [selectedGameId, selectedGameSlug, games]);

  const loadGames = async () => {
    try {
      setLoading(true);
      const response = await api.getGames({ is_active: true, limit: 50 });
      setGames(response);
    } catch (err) {
      console.error('Failed to load games:', err);
      setError('Failed to load games');
    } finally {
      setLoading(false);
    }
  };

  const updateFormDefaults = (game: Game) => {
    setSessionName(`${game.name} Session`);
    setMinParticipants(game.min_players.toString());
    setMaxParticipants(game.max_players.toString());
    setAllowSpectators(true);
    setAllowPracticeMode(game.allows_practice_mode);
  };

  const handleGameSelect = (gameId: string) => {
    const game = games.find(g => g.id === gameId);
    if (game) {
      setSelectedGame(game);
      updateFormDefaults(game);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedGame) {
      setError('Please select a game');
      return;
    }

    if (!sessionName.trim()) {
      setError('Please enter a session name');
      return;
    }

    const minPart = parseInt(minParticipants);
    const maxPart = parseInt(maxParticipants);

    if (minPart < selectedGame.min_players || minPart > selectedGame.max_players) {
      setError(`Minimum participants must be between ${selectedGame.min_players} and ${selectedGame.max_players}`);
      return;
    }

    if (maxPart < selectedGame.min_players || maxPart > selectedGame.max_players) {
      setError(`Maximum participants must be between ${selectedGame.min_players} and ${selectedGame.max_players}`);
      return;
    }

    if (minPart > maxPart) {
      setError('Minimum participants cannot be greater than maximum participants');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      const sessionData = {
        game_id: selectedGame.id,
        session_name: sessionName.trim(),
        entry_fee: parseFloat(entryFee) || 0,
        min_participants: minPart,
        max_participants: maxPart,
        scheduled_start_time: scheduledStartTime || undefined,
        estimated_duration_minutes: selectedGame.estimated_duration_minutes,
        allow_spectators: allowSpectators,
        allow_practice_mode: allowPracticeMode && selectedGame.allows_practice_mode,
      };

      const response = await api.createSession(sessionData);
      
      // Close modal and navigate to the new session
      onClose();
      router.push(`/sessions/${response.slug || response.id}`);
    } catch (err) {
      console.error('Failed to create session:', err);
      setError('Failed to create session. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    setSessionName('');
    setEntryFee('0');
    setMinParticipants('2');
    setMaxParticipants('4');
    setScheduledStartTime('');
    setAllowSpectators(true);
    setAllowPracticeMode(false);
    setSelectedGame(null);
    setError(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Generate a minimum datetime for scheduling (current time + 5 minutes)
  const minDateTime = new Date(Date.now() + 5 * 60 * 1000).toISOString().slice(0, 16);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GamepadIcon className="h-5 w-5" />
            Create Gaming Session
          </DialogTitle>
          <DialogDescription>
            Set up a new multiplayer gaming session for other players to join.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Game Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Select Game</Label>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <Select 
                value={selectedGame?.id || ''} 
                onValueChange={handleGameSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a game..." />
                </SelectTrigger>
                <SelectContent>
                  {games.map((game) => (
                    <SelectItem key={game.id} value={game.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{game.name}</span>
                        <div className="flex items-center gap-2 ml-4">
                          <Badge variant="outline" className="text-xs">
                            {game.category}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {game.min_players}-{game.max_players} players
                          </span>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            {selectedGame && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">{selectedGame.name}</CardTitle>
                  <CardDescription className="text-xs">
                    {selectedGame.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="grid grid-cols-3 gap-4 text-xs">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{selectedGame.min_players}-{selectedGame.max_players} players</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{selectedGame.estimated_duration_minutes} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      <span>{selectedGame.scoring_system}</span>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-2">
                    {selectedGame.has_spectator_betting && (
                      <Badge variant="outline" className="text-xs">
                        <DollarSign className="h-3 w-3 mr-1" />
                        Betting
                      </Badge>
                    )}
                    {selectedGame.allows_practice_mode && (
                      <Badge variant="outline" className="text-xs">
                        Practice Mode
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Session Configuration */}
          {selectedGame && (
            <div className="space-y-4">
              <Label className="text-base font-medium">Session Configuration</Label>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="sessionName">Session Name *</Label>
                  <Input
                    id="sessionName"
                    value={sessionName}
                    onChange={(e) => setSessionName(e.target.value)}
                    placeholder="Enter session name..."
                    maxLength={100}
                  />
                </div>
                <div>
                  <Label htmlFor="entryFee">Entry Fee ($)</Label>
                  <Input
                    id="entryFee"
                    type="number"
                    min="0"
                    step="0.01"
                    value={entryFee}
                    onChange={(e) => setEntryFee(e.target.value)}
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="minParticipants">Min Players *</Label>
                  <Input
                    id="minParticipants"
                    type="number"
                    min={selectedGame.min_players}
                    max={selectedGame.max_players}
                    value={minParticipants}
                    onChange={(e) => setMinParticipants(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Game supports {selectedGame.min_players}-{selectedGame.max_players} players
                  </p>
                </div>
                <div>
                  <Label htmlFor="maxParticipants">Max Players *</Label>
                  <Input
                    id="maxParticipants"
                    type="number"
                    min={selectedGame.min_players}
                    max={selectedGame.max_players}
                    value={maxParticipants}
                    onChange={(e) => setMaxParticipants(e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="scheduledStartTime">Scheduled Start (Optional)</Label>
                <Input
                  id="scheduledStartTime"
                  type="datetime-local"
                  min={minDateTime}
                  value={scheduledStartTime}
                  onChange={(e) => setScheduledStartTime(e.target.value)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to start immediately when enough players join
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <Label htmlFor="allowSpectators">Allow Spectators</Label>
                  </div>
                  <Switch
                    id="allowSpectators"
                    checked={allowSpectators}
                    onCheckedChange={setAllowSpectators}
                  />
                </div>
                
                {selectedGame.allows_practice_mode && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <Label htmlFor="allowPracticeMode">Practice Mode</Label>
                    </div>
                    <Switch
                      id="allowPracticeMode"
                      checked={allowPracticeMode}
                      onCheckedChange={setAllowPracticeMode}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          )}

          <div className="flex gap-3 justify-end pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!selectedGame || submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <GamepadIcon className="h-4 w-4 mr-2" />
                  Create Session
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}