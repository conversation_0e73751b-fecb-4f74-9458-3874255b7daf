/**
 * BetBet Gaming Engine - Session List Component
 * ============================================
 * 
 * Displays a list of game sessions with filtering options.
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useApi, useMultiServiceApi } from '@/hooks/useApi';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Clock, 
  DollarSign, 
  Play,
  Eye,
  Loader2,
  AlertCircle,
  Calendar,
  Hash
} from 'lucide-react';

interface SessionParticipant {
  user_id: string;
  username?: string;
  is_spectator: boolean;
  join_order: number;
}

interface Session {
  id: string;
  slug: string;
  game_id: string;
  game_name?: string;
  session_name: string;
  host_user_id: string;
  state: string;
  entry_fee: number;
  min_participants: number;
  max_participants: number;
  current_participants: number;
  scheduled_start_time?: string;
  actual_start_time?: string;
  estimated_duration_minutes: number;
  allows_spectators: boolean;
  allows_spectator_betting: boolean;
  participants?: SessionParticipant[];
  created_at: string;
}

interface SessionListProps {
  limit?: number;
  gameId?: string;
  showJoinable?: boolean;
  showMySessionsOnly?: boolean;
}

export function SessionList({ 
  limit = 10, 
  gameId,
  showJoinable = false,
  showMySessionsOnly = false
}: SessionListProps) {
  const api = useApi();
  const multiApi = useMultiServiceApi();
  const router = useRouter();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSessions();
  }, [limit, gameId, showJoinable]);

  const loadSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {
        limit,
        game_id: gameId,
        status: showJoinable ? 'waiting' : undefined,
        has_available_slots: showJoinable || undefined,
      };

      // Try multi-service API first
      try {
        const response = await multiApi.gaming.getSessions(params);
        setSessions(response.data.sessions || response.data);
      } catch (multiErr) {
        console.log('Multi-service API failed, trying fallback');
        // Fallback to original API
        const response = await api.getSessions(params);
        setSessions(response.sessions || response);
      }
    } catch (err) {
      console.error('Failed to load sessions:', err);
      setError('Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinSession = async (sessionId: string) => {
    try {
      await api.joinSession(sessionId, false);
      router.push(`/sessions/${sessionId}`);
    } catch (err) {
      console.error('Failed to join session:', err);
      alert('Failed to join session');
    }
  };

  const handleSpectateSession = (sessionId: string, slug: string) => {
    router.push(`/sessions/${slug}`);
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'waiting':
        return 'bg-yellow-500';
      case 'starting':
        return 'bg-orange-500';
      case 'active':
        return 'bg-green-500';
      case 'completed':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStateLabel = (state: string) => {
    switch (state) {
      case 'waiting':
        return 'Waiting for Players';
      case 'starting':
        return 'Starting Soon';
      case 'active':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return state;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadSessions}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (sessions.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">
            {showJoinable ? 'No joinable sessions available' : 'No active sessions'}
          </p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/games')}
            className="mt-4"
          >
            Create New Session
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {sessions.map((session) => (
        <Card key={session.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-lg">{session.session_name}</h3>
                  <Badge 
                    variant="secondary" 
                    className={`${getStateColor(session.state)} text-white`}
                  >
                    {getStateLabel(session.state)}
                  </Badge>
                </div>
                
                {session.game_name && (
                  <p className="text-sm text-gray-600">{session.game_name}</p>
                )}
                
                <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{session.current_participants}/{session.max_participants} players</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{session.estimated_duration_minutes} min</span>
                  </div>
                  {session.entry_fee > 0 && (
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4" />
                      <span>${session.entry_fee} entry</span>
                    </div>
                  )}
                  {session.scheduled_start_time && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(session.scheduled_start_time).toLocaleTimeString()}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  {session.allows_spectator_betting && (
                    <Badge variant="outline" className="text-xs">
                      <DollarSign className="h-3 w-3 mr-1" />
                      Betting
                    </Badge>
                  )}
                  {session.allows_spectators && (
                    <Badge variant="outline" className="text-xs">
                      <Eye className="h-3 w-3 mr-1" />
                      Spectators
                    </Badge>
                  )}
                  {session.slug && (
                    <Badge variant="outline" className="text-xs font-mono">
                      <Hash className="h-3 w-3 mr-1" />
                      {session.slug.split('-').slice(0, 3).join('-')}...
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-2 ml-4">
                {session.state === 'waiting' && session.current_participants < session.max_participants && (
                  <Button 
                    size="sm"
                    onClick={() => handleJoinSession(session.id)}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                )}
                {(session.state === 'active' || session.state === 'starting') && session.allows_spectators && (
                  <Button 
                    size="sm"
                    variant="outline"
                    onClick={() => handleSpectateSession(session.id, session.slug)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Watch
                  </Button>
                )}
                {session.state === 'completed' && (
                  <Button 
                    size="sm"
                    variant="ghost"
                    onClick={() => router.push(`/sessions/${session.slug}`)}
                  >
                    View Results
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}