/**
 * Custom Betting Platform - User Bet Manager Component
 * ===================================================
 * 
 * Component for managing user's active bets and positions.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Target,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  ExternalLink,
  Info
} from 'lucide-react';
import { BetParticipant, CustomBet } from '@/lib/custom-betting-api';
import customBettingAPI from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface UserBetManagerProps {
  userBets: BetParticipant[];
  onBetCancelled?: (betId: string) => void;
  onRefresh?: () => void;
}

export function UserBetManager({ userBets, onBetCancelled, onRefresh }: UserBetManagerProps) {
  const [selectedBetId, setSelectedBetId] = useState<string | null>(null);
  const [cancellingBetId, setCancellingBetId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Group bets by status
  const activeBets = userBets.filter(b => b.status === 'active' || b.status === 'pending');
  const settledBets = userBets.filter(b => b.status === 'won' || b.status === 'lost');
  const cancelledBets = userBets.filter(b => b.status === 'cancelled');

  const handleCancelBet = async (betId: string) => {
    try {
      setCancellingBetId(betId);
      setError(null);

      await customBettingAPI.cancelBetParticipation(betId);
      
      // Notify parent component
      onBetCancelled?.(betId);
      
      // Refresh data
      onRefresh?.();

    } catch (err: any) {
      setError(err.message || 'Failed to cancel bet');
    } finally {
      setCancellingBetId(null);
    }
  };

  const calculatePotentialPayout = (bet: BetParticipant) => {
    if (bet.position_type === 'backing') {
      return bet.stake_amount * bet.desired_odds;
    } else {
      // For laying, you win the stake if the outcome doesn't happen
      return bet.stake_amount;
    }
  };

  const calculateLiability = (bet: BetParticipant) => {
    if (bet.position_type === 'laying') {
      // Liability for laying = (odds - 1) * stake
      return (bet.desired_odds - 1) * bet.stake_amount;
    }
    return bet.stake_amount;
  };

  const renderBetCard = (bet: BetParticipant) => {
    const isActive = bet.status === 'active' || bet.status === 'pending';
    const isWon = bet.status === 'won';
    const isLost = bet.status === 'lost';
    const isCancelling = cancellingBetId === bet.id;

    return (
      <Card 
        key={bet.id}
        className={cn(
          "transition-all duration-200",
          selectedBetId === bet.id && "ring-2 ring-primary",
          isWon && "border-green-300 bg-green-50/50",
          isLost && "border-red-300 bg-red-50/50"
        )}
      >
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium">Market #{bet.bet_id}</h4>
                  <Badge 
                    variant="outline" 
                    className={cn(
                      isActive && "bg-blue-100 text-blue-800",
                      isWon && "bg-green-100 text-green-800",
                      isLost && "bg-red-100 text-red-800",
                      bet.status === 'cancelled' && "bg-gray-100 text-gray-800"
                    )}
                  >
                    {bet.status}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Outcome: {bet.outcome_id}
                </p>
              </div>

              <Button variant="ghost" size="sm" asChild>
                <Link href={`/custom-betting/market/${bet.bet_id}`}>
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Position Details */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-600 mb-1">Position Type</div>
                <div className="flex items-center gap-1 font-medium">
                  {bet.position_type === 'backing' ? (
                    <>
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="text-green-700">Backing</span>
                    </>
                  ) : (
                    <>
                      <TrendingDown className="h-4 w-4 text-red-600" />
                      <span className="text-red-700">Laying</span>
                    </>
                  )}
                </div>
              </div>

              <div>
                <div className="text-gray-600 mb-1">Stake</div>
                <div className="font-medium">${bet.stake_amount.toFixed(2)}</div>
              </div>

              <div>
                <div className="text-gray-600 mb-1">Odds</div>
                <div className="font-medium">{bet.desired_odds}x</div>
              </div>

              <div>
                <div className="text-gray-600 mb-1">
                  {bet.position_type === 'backing' ? 'Potential Win' : 'Liability'}
                </div>
                <div className="font-medium">
                  ${bet.position_type === 'backing' 
                    ? calculatePotentialPayout(bet).toFixed(2)
                    : calculateLiability(bet).toFixed(2)
                  }
                </div>
              </div>
            </div>

            {/* Status-specific information */}
            {isActive && bet.matched_amount !== undefined && bet.matched_amount < bet.stake_amount && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Matched</span>
                  <span className="font-medium">
                    ${bet.matched_amount.toFixed(2)} / ${bet.stake_amount.toFixed(2)}
                  </span>
                </div>
                <Progress 
                  value={(bet.matched_amount / bet.stake_amount) * 100} 
                  className="h-2"
                />
              </div>
            )}

            {isWon && bet.payout_amount && (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Won! Payout: ${bet.payout_amount.toFixed(2)}
                </AlertDescription>
              </Alert>
            )}

            {isLost && (
              <Alert className="bg-red-50 border-red-200">
                <XCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  Lost. Amount: -${bet.stake_amount.toFixed(2)}
                </AlertDescription>
              </Alert>
            )}

            {/* Actions */}
            {isActive && bet.status === 'pending' && (
              <div className="flex justify-end pt-2 border-t">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleCancelBet(bet.id)}
                  disabled={isCancelling}
                >
                  {isCancelling ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Cancelling...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Cancel Bet
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* Timestamps */}
            <div className="flex items-center gap-4 text-xs text-gray-500 pt-2 border-t">
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Placed: {new Date(bet.created_at).toLocaleDateString()}
              </span>
              {bet.settled_at && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Settled: {new Date(bet.settled_at).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {activeBets.length}
            </div>
            <div className="text-sm text-gray-600">Active Bets</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {settledBets.filter(b => b.status === 'won').length}
            </div>
            <div className="text-sm text-gray-600">Won</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {settledBets.filter(b => b.status === 'lost').length}
            </div>
            <div className="text-sm text-gray-600">Lost</div>
          </CardContent>
        </Card>
      </div>

      {/* Bets by Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Your Positions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="active">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="active">
                Active ({activeBets.length})
              </TabsTrigger>
              <TabsTrigger value="settled">
                Settled ({settledBets.length})
              </TabsTrigger>
              <TabsTrigger value="cancelled">
                Cancelled ({cancelledBets.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4 mt-4">
              {activeBets.length > 0 ? (
                activeBets.map(renderBetCard)
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Target className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>No active bets</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="settled" className="space-y-4 mt-4">
              {settledBets.length > 0 ? (
                settledBets.map(renderBetCard)
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>No settled bets</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="cancelled" className="space-y-4 mt-4">
              {cancelledBets.length > 0 ? (
                cancelledBets.map(renderBetCard)
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <XCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>No cancelled bets</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          You can only cancel pending bets that haven't been matched yet. 
          Once a bet is matched, it cannot be cancelled.
        </AlertDescription>
      </Alert>
    </div>
  );
}