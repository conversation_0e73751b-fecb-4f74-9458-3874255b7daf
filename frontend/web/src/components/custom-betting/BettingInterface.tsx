/**
 * Custom Betting Platform - Betting Interface Component
 * ====================================================
 * 
 * Main betting interface for placing bets on market outcomes.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Target,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calculator,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface BettingInterfaceProps {
  market: CustomBet;
  userParticipation?: BetParticipant | null;
  onBetPlaced?: () => void;
}

export function BettingInterface({ market, userParticipation, onBetPlaced }: BettingInterfaceProps) {
  const [selectedOutcome, setSelectedOutcome] = useState<string>('');
  const [positionType, setPositionType] = useState<'backing' | 'laying'>('backing');
  const [stakeAmount, setStakeAmount] = useState<string>('');
  const [desiredOdds, setDesiredOdds] = useState<string>('');
  const [isPlacing, setIsPlacing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedOutcomeData = market.outcomes.find(o => o.id === selectedOutcome);
  const stakeNum = parseFloat(stakeAmount) || 0;
  const oddsNum = parseFloat(desiredOdds) || 0;

  // Calculate potential payout
  const calculatePayout = () => {
    if (!stakeNum || !oddsNum) return 0;
    
    if (positionType === 'backing') {
      return stakeNum * oddsNum; // Total return
    } else {
      return stakeNum; // Stake is what you win if outcome doesn't happen
    }
  };

  const calculateProfit = () => {
    const payout = calculatePayout();
    return positionType === 'backing' ? payout - stakeNum : payout;
  };

  const isValidBet = () => {
    if (!selectedOutcome || !stakeAmount || !desiredOdds) return false;
    if (stakeNum < market.minimum_stake || stakeNum > market.maximum_stake) return false;
    if (oddsNum <= 1) return false;
    return true;
  };

  const handlePlaceBet = async () => {
    if (!isValidBet()) return;

    setIsPlacing(true);
    setError(null);

    try {
      // TODO: Implement actual API call
      console.log('Placing bet:', {
        outcomeId: selectedOutcome,
        positionType,
        stakeAmount: stakeNum,
        desiredOdds: oddsNum
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Reset form
      setSelectedOutcome('');
      setStakeAmount('');
      setDesiredOdds('');
      
      // Notify parent component
      onBetPlaced?.();

    } catch (err) {
      setError('Failed to place bet. Please try again.');
    } finally {
      setIsPlacing(false);
    }
  };

  const getQuickOdds = () => {
    if (!selectedOutcomeData) return [];
    
    const baseOdds = selectedOutcomeData.current_odds;
    return [
      baseOdds * 0.95,
      baseOdds,
      baseOdds * 1.05,
      baseOdds * 1.1
    ].map(odds => parseFloat(odds.toFixed(2)));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Place Your Bet
          {userParticipation && (
            <Badge variant="outline" className="ml-auto">
              You're already participating
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Position Alert */}
        {userParticipation && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              You have a {userParticipation.position_type} position with ${userParticipation.stake_amount} stake.
              You can place additional bets on other outcomes.
            </AlertDescription>
          </Alert>
        )}

        {/* Outcome Selection */}
        <div className="space-y-2">
          <Label htmlFor="outcome">Select Outcome</Label>
          <Select value={selectedOutcome} onValueChange={setSelectedOutcome}>
            <SelectTrigger>
              <SelectValue placeholder="Choose an outcome..." />
            </SelectTrigger>
            <SelectContent>
              {market.outcomes.map((outcome) => (
                <SelectItem key={outcome.id} value={outcome.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{outcome.outcome_text}</span>
                    <span className="ml-4 text-primary font-medium">
                      {outcome.current_odds.toFixed(2)}x
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Position Type */}
        <div className="space-y-2">
          <Label>Position Type</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={positionType === 'backing' ? 'default' : 'outline'}
              onClick={() => setPositionType('backing')}
              className="flex items-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              Back (For)
            </Button>
            <Button
              variant={positionType === 'laying' ? 'default' : 'outline'}
              onClick={() => setPositionType('laying')}
              className="flex items-center gap-2"
            >
              <TrendingDown className="h-4 w-4" />
              Lay (Against)
            </Button>
          </div>
          <div className="text-xs text-gray-600">
            {positionType === 'backing' 
              ? 'You win if this outcome happens'
              : 'You win if this outcome does NOT happen'
            }
          </div>
        </div>

        {/* Stake Amount */}
        <div className="space-y-2">
          <Label htmlFor="stake">Stake Amount</Label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="stake"
              type="number"
              placeholder="0.00"
              value={stakeAmount}
              onChange={(e) => setStakeAmount(e.target.value)}
              className="pl-9"
              min={market.minimum_stake}
              max={market.maximum_stake}
              step="0.01"
            />
          </div>
          <div className="flex justify-between text-xs text-gray-600">
            <span>Min: ${market.minimum_stake}</span>
            <span>Max: ${market.maximum_stake}</span>
          </div>
        </div>

        {/* Desired Odds */}
        <div className="space-y-2">
          <Label htmlFor="odds">Desired Odds</Label>
          <Input
            id="odds"
            type="number"
            placeholder="2.00"
            value={desiredOdds}
            onChange={(e) => setDesiredOdds(e.target.value)}
            min="1.01"
            step="0.01"
          />
          
          {/* Quick Odds Selection */}
          {selectedOutcomeData && (
            <div className="space-y-2">
              <div className="text-xs text-gray-600">Quick odds:</div>
              <div className="grid grid-cols-4 gap-1">
                {getQuickOdds().map((odds) => (
                  <Button
                    key={odds}
                    variant="outline"
                    size="sm"
                    onClick={() => setDesiredOdds(odds.toString())}
                    className="text-xs"
                  >
                    {odds}x
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Calculation Summary */}
        {stakeNum > 0 && oddsNum > 0 && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Calculator className="h-4 w-4" />
              Bet Summary
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Stake:</span>
                <span className="font-medium">${stakeNum.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Odds:</span>
                <span className="font-medium">{oddsNum.toFixed(2)}x</span>
              </div>
              <div className="flex justify-between">
                <span>Potential Payout:</span>
                <span className="font-medium text-primary">
                  ${calculatePayout().toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between border-t pt-1">
                <span>Potential Profit:</span>
                <span className={cn(
                  "font-medium",
                  calculateProfit() > 0 ? "text-green-600" : "text-red-600"
                )}>
                  ${calculateProfit().toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Place Bet Button */}
        <Button 
          onClick={handlePlaceBet}
          disabled={!isValidBet() || isPlacing}
          className="w-full"
          size="lg"
        >
          {isPlacing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Placing Bet...
            </>
          ) : (
            <>
              <Target className="h-4 w-4 mr-2" />
              Place {positionType === 'backing' ? 'Back' : 'Lay'} Bet
            </>
          )}
        </Button>

        {/* Terms Notice */}
        <div className="text-xs text-gray-500 text-center">
          By placing a bet, you agree to the market terms and conditions.
          All bets are final once placed and matched.
        </div>
      </CardContent>
    </Card>
  );
}