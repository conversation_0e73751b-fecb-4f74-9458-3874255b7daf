/**
 * Custom Betting Platform - Loading Spinner Component
 * ==================================================
 * 
 * Reusable loading spinner for async operations.
 */

'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullPage?: boolean;
  className?: string;
}

export function LoadingSpinner({ 
  size = 'md', 
  text = 'Loading...', 
  fullPage = false,
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  if (fullPage) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className={cn(sizeClasses[size], "animate-spin mx-auto mb-4 text-primary")} />
          <p className={cn(textSizeClasses[size], "text-gray-600")}>{text}</p>
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-center space-x-3">
          <Loader2 className={cn(sizeClasses[size], "animate-spin text-primary")} />
          <span className={cn(textSizeClasses[size], "text-gray-600")}>{text}</span>
        </div>
      </CardContent>
    </Card>
  );
}