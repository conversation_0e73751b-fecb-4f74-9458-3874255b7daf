/**
 * Custom Betting Platform - Trending Markets Component
 * ===================================================
 * 
 * Displays trending markets based on recent activity.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { MarketCard } from './MarketCard';
import { LoadingSpinner } from './LoadingSpinner';
import { EmptyState } from './EmptyState';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp,
  RefreshCw,
  Filter,
  Clock,
  Flame
} from 'lucide-react';
import customBettingAPI, { CustomBet } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface TrendingMarketsProps {
  limit?: number;
  timeframe?: number; // hours
  showControls?: boolean;
}

export function TrendingMarkets({ 
  limit = 12, 
  timeframe = 24,
  showControls = true 
}: TrendingMarketsProps) {
  const [markets, setMarkets] = useState<CustomBet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);

  const timeframeOptions = [
    { label: '1 Hour', value: 1 },
    { label: '6 Hours', value: 6 },
    { label: '24 Hours', value: 24 },
    { label: '7 Days', value: 168 }
  ];

  const loadTrendingMarkets = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await customBettingAPI.getTrendingMarkets(limit, selectedTimeframe);
      setMarkets(data);
      
    } catch (err) {
      console.error('Failed to load trending markets:', err);
      setError('Failed to load trending markets. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTrendingMarkets();
  }, [limit, selectedTimeframe]);

  const handleRefresh = () => {
    loadTrendingMarkets();
  };

  const getTrendingIcon = (index: number) => {
    if (index === 0) return <Flame className="h-4 w-4 text-red-500" />;
    if (index === 1) return <TrendingUp className="h-4 w-4 text-orange-500" />;
    if (index === 2) return <TrendingUp className="h-4 w-4 text-yellow-500" />;
    return <TrendingUp className="h-4 w-4 text-gray-400" />;
  };

  const getTrendingLabel = (index: number) => {
    if (index === 0) return 'Hottest';
    if (index === 1) return 'Hot';
    if (index === 2) return 'Rising';
    return 'Trending';
  };

  if (loading && markets.length === 0) {
    return <LoadingSpinner text="Loading trending markets..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      {showControls && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Trending Markets
                <Badge variant="outline" className="ml-2">
                  {selectedTimeframe}h timeframe
                </Badge>
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {/* Timeframe Selector */}
                <div className="flex border rounded-lg">
                  {timeframeOptions.map((option) => (
                    <Button
                      key={option.value}
                      variant={selectedTimeframe === option.value ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedTimeframe(option.value)}
                      className={cn(
                        "rounded-none first:rounded-l-lg last:rounded-r-lg",
                        "text-xs px-3"
                      )}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading}
                >
                  <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-red-600 mb-4">
              <Filter className="h-12 w-12 mx-auto mb-2" />
              <p className="font-medium">{error}</p>
            </div>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !error && markets.length === 0 && (
        <EmptyState
          icon="trending"
          title="No trending markets"
          description={`No markets are trending in the last ${selectedTimeframe} hours. Check back later or try a different timeframe.`}
          actionLabel="Refresh"
          onAction={handleRefresh}
        />
      )}

      {/* Markets Grid */}
      {markets.length > 0 && (
        <div className="space-y-6">
          {/* Top 3 Featured */}
          {markets.slice(0, 3).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Flame className="h-5 w-5 text-red-500" />
                Hottest Right Now
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {markets.slice(0, 3).map((market, index) => (
                  <div key={market.id} className="relative">
                    {/* Trending Badge */}
                    <div className="absolute -top-2 -left-2 z-10">
                      <Badge className="flex items-center gap-1 bg-gradient-to-r from-red-500 to-orange-500 text-white">
                        {getTrendingIcon(index)}
                        #{index + 1} {getTrendingLabel(index)}
                      </Badge>
                    </div>
                    <MarketCard 
                      market={market} 
                      showCategory={true}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Rest of trending markets */}
          {markets.slice(3).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                More Trending
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {markets.slice(3).map((market, index) => (
                  <div key={market.id} className="relative">
                    {/* Trending Number */}
                    <div className="absolute -top-2 -left-2 z-10">
                      <Badge variant="outline" className="bg-white">
                        #{index + 4}
                      </Badge>
                    </div>
                    <MarketCard 
                      market={market} 
                      showCategory={true}
                      compact={true}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Loading indicator for refresh */}
          {loading && markets.length > 0 && (
            <div className="flex justify-center py-4">
              <div className="flex items-center gap-2 text-gray-600">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-sm">Updating trending markets...</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}