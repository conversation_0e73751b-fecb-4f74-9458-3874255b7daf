/**
 * Custom Betting Platform - Market Header Component
 * ================================================
 * 
 * Header section for individual market pages showing title,
 * status, key metrics, and user participation.
 */

'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock,
  Users,
  DollarSign,
  Star,
  Calendar,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface MarketHeaderProps {
  market: CustomBet;
  userParticipation?: BetParticipant | null;
  onRefresh?: () => void;
}

export function MarketHeader({ market, userParticipation, onRefresh }: MarketHeaderProps) {
  const timeUntilDeadline = new Date(market.deadline).getTime() - Date.now();
  const isEndingSoon = timeUntilDeadline > 0 && timeUntilDeadline < 24 * 60 * 60 * 1000; // 24 hours
  const isExpired = timeUntilDeadline <= 0;
  
  const participationRate = market.participant_limit ? 
    (market.total_participants / market.participant_limit) * 100 : 0;

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return 'Expired';
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h remaining`;
    if (hours > 0) return `${hours}h ${minutes}m remaining`;
    return `${minutes}m remaining`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800 border-green-200';
      case 'active': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'settling': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'settled': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      case 'expired': return 'bg-gray-100 text-gray-600 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      politics: 'bg-purple-100 text-purple-800',
      sports: 'bg-green-100 text-green-800',
      entertainment: 'bg-pink-100 text-pink-800',
      crypto: 'bg-orange-100 text-orange-800',
      tech: 'bg-blue-100 text-blue-800',
      default: 'bg-gray-100 text-gray-800'
    };
    return colors[category as keyof typeof colors] || colors.default;
  };

  return (
    <Card className={cn(
      "border-2",
      isEndingSoon && "border-yellow-300",
      isExpired && "opacity-75"
    )}>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Header Row */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
            <div className="flex-1 min-w-0">
              {/* Badges Row */}
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="outline" className={getCategoryColor(market.category)}>
                  {market.category}
                </Badge>
                <Badge variant="outline" className={getStatusColor(market.status)}>
                  {market.status}
                </Badge>
                {market.is_featured && (
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    Featured
                  </Badge>
                )}
                {isEndingSoon && (
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Ending Soon
                  </Badge>
                )}
              </div>

              {/* Title */}
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                {market.title}
              </h1>

              {/* Description */}
              <p className="text-gray-600 text-lg">
                {market.description}
              </p>
            </div>

            {/* Time Remaining */}
            <div className="text-right">
              <div className={cn(
                "text-sm font-medium mb-1",
                isEndingSoon ? "text-yellow-600" : isExpired ? "text-gray-500" : "text-gray-700"
              )}>
                <Clock className="h-4 w-4 inline mr-1" />
                {formatTimeRemaining(timeUntilDeadline)}
              </div>
              <div className="text-xs text-gray-500">
                Deadline: {new Date(market.deadline).toLocaleDateString()}
              </div>
            </div>
          </div>

          {/* User Participation Alert */}
          {userParticipation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">You're participating in this market</span>
              </div>
              <div className="mt-2 text-sm text-blue-700">
                Position: {userParticipation.position_type} • 
                Stake: ${userParticipation.stake_amount} • 
                Status: {userParticipation.status}
              </div>
            </div>
          )}

          {/* Market Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Total Volume */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Total Volume</span>
              </div>
              <div className="text-xl font-bold text-gray-900">
                ${market.total_stakes.toLocaleString()}
              </div>
            </div>

            {/* Participants */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Participants</span>
              </div>
              <div className="text-xl font-bold text-gray-900">
                {market.total_participants}
              </div>
              {market.participant_limit && (
                <div className="text-xs text-gray-500 mt-1">
                  of {market.participant_limit} max
                </div>
              )}
            </div>

            {/* Market Type */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Type</span>
              </div>
              <div className="text-xl font-bold text-gray-900 capitalize">
                {market.bet_type.replace('_', ' ')}
              </div>
            </div>

            {/* Created Date */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Created</span>
              </div>
              <div className="text-sm font-bold text-gray-900">
                {new Date(market.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          {/* Participation Progress */}
          {market.participant_limit && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Market Capacity</span>
                <span className="text-sm text-gray-600">
                  {market.total_participants}/{market.participant_limit} participants
                </span>
              </div>
              <Progress 
                value={participationRate} 
                className="h-3"
              />
              <div className="text-xs text-gray-500 mt-1">
                {participationRate.toFixed(1)}% filled
              </div>
            </div>
          )}

          {/* Tags */}
          {market.tags && market.tags.length > 0 && (
            <div>
              <span className="text-sm font-medium text-gray-700 mb-2 block">Tags:</span>
              <div className="flex flex-wrap gap-2">
                {market.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}