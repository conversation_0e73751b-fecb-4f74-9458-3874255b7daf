/**
 * Custom Betting Platform - Empty State Component
 * ==============================================
 * 
 * Component for showing empty states with optional actions.
 */

'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Search,
  Plus,
  Filter,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: 'search' | 'filter' | 'create' | 'trending' | 'error';
  actionLabel?: string;
  onAction?: () => void;
  secondaryActionLabel?: string;
  onSecondaryAction?: () => void;
}

export function EmptyState({
  title,
  description,
  icon = 'search',
  actionLabel,
  onAction,
  secondaryActionLabel,
  onSecondaryAction
}: EmptyStateProps) {
  const iconComponents = {
    search: Search,
    filter: Filter,
    create: Plus,
    trending: TrendingUp,
    error: AlertCircle
  };

  const IconComponent = iconComponents[icon];

  const iconColors = {
    search: 'text-gray-400',
    filter: 'text-blue-400',
    create: 'text-green-400',
    trending: 'text-purple-400',
    error: 'text-red-400'
  };

  return (
    <Card>
      <CardContent className="p-12 text-center">
        <div className={`${iconColors[icon]} mb-6`}>
          <IconComponent className="h-16 w-16 mx-auto" />
        </div>
        
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {title}
        </h3>
        
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          {description}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {actionLabel && onAction && (
            <Button onClick={onAction}>
              {actionLabel}
            </Button>
          )}
          
          {secondaryActionLabel && onSecondaryAction && (
            <Button variant="outline" onClick={onSecondaryAction}>
              {secondaryActionLabel}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}