/**
 * Custom Betting Platform - Create Bet Wizard Component
 * ====================================================
 * 
 * Multi-step wizard for creating custom betting markets.
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  ArrowRight,
  Plus,
  Minus,
  Calendar,
  DollarSign,
  Users,
  Shield,
  AlertCircle,
  CheckCircle,
  Loader2,
  X,
  Info
} from 'lucide-react';
import customBettingAPI from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface CreateBetWizardProps {
  onComplete?: (betId: string) => void;
  onCancel?: () => void;
}

interface BetFormData {
  // Step 1: Basic Information
  title: string;
  description: string;
  event_criteria: string;
  category: string;
  bet_type: 'binary' | 'multi_choice' | 'range';
  
  // Step 2: Outcomes
  outcomes: Array<{
    id: string;
    outcome_text: string;
    initial_odds: number;
  }>;
  
  // Step 3: Rules & Limits
  minimum_stake: number;
  maximum_stake: number;
  participant_limit?: number;
  deadline: string;
  verification_deadline: string;
  
  // Step 4: Advanced Settings
  is_public: boolean;
  escrow_percentage: number;
  settlement_type: 'manual' | 'oracle' | 'consensus';
  tags: string[];
}

interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  is_active: boolean;
}

const WIZARD_STEPS = [
  { id: 1, title: 'Basic Information', description: 'Set up your betting market' },
  { id: 2, title: 'Outcomes', description: 'Define possible outcomes' },
  { id: 3, title: 'Rules & Limits', description: 'Configure betting rules' },
  { id: 4, title: 'Advanced Settings', description: 'Additional options' },
  { id: 5, title: 'Review & Create', description: 'Confirm your market' }
];

export function CreateBetWizard({ onComplete, onCancel }: CreateBetWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  
  const [formData, setFormData] = useState<BetFormData>({
    title: '',
    description: '',
    event_criteria: '',
    category: '',
    bet_type: 'binary',
    outcomes: [
      { id: '1', outcome_text: '', initial_odds: 2.0 },
      { id: '2', outcome_text: '', initial_odds: 2.0 }
    ],
    minimum_stake: 1,
    maximum_stake: 1000,
    participant_limit: undefined,
    deadline: '',
    verification_deadline: '',
    is_public: true,
    escrow_percentage: 100,
    settlement_type: 'manual',
    tags: []
  });

  const updateFormData = (updates: Partial<BetFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setError(null);
  };

  // Load categories on mount
  React.useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await customBettingAPI.getCategories();
        setCategories(response);
      } catch (err) {
        console.error('Failed to load categories:', err);
        // Use fallback categories if API fails
        setCategories([
          { id: 'sports', name: 'Sports', slug: 'sports', is_active: true },
          { id: 'politics', name: 'Politics', slug: 'politics', is_active: true },
          { id: 'entertainment', name: 'Entertainment', slug: 'entertainment', is_active: true },
          { id: 'crypto', name: 'Cryptocurrency', slug: 'crypto', is_active: true },
          { id: 'other', name: 'Other', slug: 'other', is_active: true }
        ]);
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  const addOutcome = () => {
    const newOutcome = {
      id: Date.now().toString(),
      outcome_text: '',
      initial_odds: 2.0
    };
    updateFormData({
      outcomes: [...formData.outcomes, newOutcome]
    });
  };

  const removeOutcome = (id: string) => {
    if (formData.outcomes.length <= 2) return;
    updateFormData({
      outcomes: formData.outcomes.filter(o => o.id !== id)
    });
  };

  const updateOutcome = (id: string, updates: Partial<typeof formData.outcomes[0]>) => {
    updateFormData({
      outcomes: formData.outcomes.map(o => 
        o.id === id ? { ...o, ...updates } : o
      )
    });
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(
          formData.title.trim() &&
          formData.description.trim() &&
          formData.resolution_criteria.trim() &&
          formData.category_id &&
          formData.market_type
        );
      case 2:
        return formData.outcomes.every(o => 
          o.title.trim()
        );
      case 3:
        return !!(
          formData.minimum_stake > 0 &&
          formData.maximum_stake > formData.minimum_stake &&
          formData.deadline &&
          formData.verification_deadline &&
          new Date(formData.deadline) > new Date() &&
          new Date(formData.verification_deadline) > new Date(formData.deadline)
        );
      case 4:
        return formData.escrow_percentage >= 0 && formData.escrow_percentage <= 100;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (!validateStep(currentStep)) {
      setError('Please fill in all required fields correctly.');
      return;
    }
    
    if (currentStep < WIZARD_STEPS.length) {
      setCurrentStep(currentStep + 1);
      setError(null);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setError(null);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Prepare API payload
      const payload = {
        title: formData.title,
        description: formData.description,
        event_criteria: formData.event_criteria,
        category: formData.category,
        bet_type: formData.bet_type,
        outcomes: formData.outcomes.map(o => ({
          outcome_text: o.outcome_text,
          initial_odds: o.initial_odds
        })),
        minimum_stake: formData.minimum_stake,
        maximum_stake: formData.maximum_stake,
        participant_limit: formData.participant_limit || null,
        deadline: formData.deadline,
        verification_deadline: formData.verification_deadline,
        is_public: formData.is_public,
        escrow_percentage: formData.escrow_percentage,
        settlement_type: formData.settlement_type,
        tags: formData.tags.filter(t => t.trim())
      };

      const createdBet = await customBettingAPI.createBet(payload);
      
      // Success - redirect to the new market
      if (onComplete) {
        onComplete(createdBet.id);
      } else {
        router.push(`/custom-betting/market/${createdBet.id}`);
      }

    } catch (err: any) {
      console.error('Failed to create bet:', err);
      setError(err.message || 'Failed to create betting market. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Market Title *</Label>
              <Input
                id="title"
                placeholder="e.g., Will Bitcoin reach $100,000 by end of 2024?"
                value={formData.title}
                onChange={(e) => updateFormData({ title: e.target.value })}
                maxLength={100}
              />
              <div className="text-xs text-gray-500">
                {formData.title.length}/100 characters
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                placeholder="Provide a clear description of what this market is about..."
                value={formData.description}
                onChange={(e) => updateFormData({ description: e.target.value })}
                rows={3}
                maxLength={500}
              />
              <div className="text-xs text-gray-500">
                {formData.description.length}/500 characters
              </div>
            </div>

            {/* Event Criteria */}
            <div className="space-y-2">
              <Label htmlFor="criteria">Settlement Criteria *</Label>
              <Textarea
                id="criteria"
                placeholder="Clearly define how this market will be settled. What specific conditions determine the outcome?"
                value={formData.event_criteria}
                onChange={(e) => updateFormData({ event_criteria: e.target.value })}
                rows={4}
              />
              <div className="text-xs text-gray-500">
                Be specific about sources, dates, and conditions for settlement
              </div>
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select 
                value={formData.category_id} 
                onValueChange={(value) => updateFormData({ category_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category..." />
                </SelectTrigger>
                <SelectContent>
                  {loadingCategories ? (
                    <SelectItem value="loading" disabled>
                      Loading categories...
                    </SelectItem>
                  ) : (
                    categories.map(cat => (
                      <SelectItem key={cat.id} value={cat.id}>
                        {cat.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Market Type */}
            <div className="space-y-2">
              <Label>Market Type *</Label>
              <RadioGroup
                value={formData.market_type}
                onValueChange={(value: any) => updateFormData({ market_type: value })}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="binary" id="binary" />
                  <Label htmlFor="binary" className="font-normal">
                    Binary (Yes/No)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="multiple_choice" id="multi" />
                  <Label htmlFor="multi" className="font-normal">
                    Multiple Choice
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="scalar" id="scalar" />
                  <Label htmlFor="scalar" className="font-normal">
                    Scalar/Numeric
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Define all possible outcomes for your market. Initial odds will adjust based on betting activity.
              </AlertDescription>
            </Alert>

            {/* Outcomes List */}
            <div className="space-y-4">
              {formData.outcomes.map((outcome, index) => (
                <Card key={outcome.id}>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Outcome {index + 1}</Label>
                        {formData.outcomes.length > 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeOutcome(outcome.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Input
                          placeholder="e.g., Yes, it will reach $100,000"
                          value={outcome.title}
                          onChange={(e) => updateOutcome(outcome.id, { title: e.target.value })}
                        />
                        <Textarea
                          placeholder="Optional description for this outcome"
                          value={outcome.description || ''}
                          onChange={(e) => updateOutcome(outcome.id, { description: e.target.value })}
                          rows={2}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Add Outcome Button */}
            {formData.market_type === 'multiple_choice' && (
              <Button
                variant="outline"
                onClick={addOutcome}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another Outcome
              </Button>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            {/* Stake Limits */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Stake Limits
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min-stake">Minimum Stake ($)</Label>
                  <Input
                    id="min-stake"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={formData.minimum_stake}
                    onChange={(e) => updateFormData({ 
                      minimum_stake: parseFloat(e.target.value) || 0.01 
                    })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="max-stake">Maximum Stake ($)</Label>
                  <Input
                    id="max-stake"
                    type="number"
                    min="1"
                    step="1"
                    value={formData.maximum_stake}
                    onChange={(e) => updateFormData({ 
                      maximum_stake: parseFloat(e.target.value) || 1 
                    })}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Time Limits */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Time Limits
              </h3>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="deadline">Market Closes At *</Label>
                  <Input
                    id="deadline"
                    type="datetime-local"
                    value={formData.closes_at}
                    onChange={(e) => updateFormData({ closes_at: e.target.value })}
                    min={new Date().toISOString().slice(0, 16)}
                  />
                  <p className="text-xs text-gray-500">
                    When betting closes for this market
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="verification">Resolution Date *</Label>
                  <Input
                    id="verification"
                    type="datetime-local"
                    value={formData.resolves_at}
                    onChange={(e) => updateFormData({ resolves_at: e.target.value })}
                    min={formData.closes_at || new Date().toISOString().slice(0, 16)}
                  />
                  <p className="text-xs text-gray-500">
                    When the outcome must be determined by
                  </p>
                </div>
              </div>
            </div>

          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            {/* Resolution Source */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Resolution Source (Optional)
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="source">Data Source URL or Description</Label>
                <Input
                  id="source"
                  placeholder="e.g., https://coinmarketcap.com or Official NFL scores"
                  value={formData.resolution_source || ''}
                  onChange={(e) => updateFormData({ resolution_source: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Specify where the outcome data will come from for transparent resolution
                </p>
              </div>
            </div>

            <Separator />

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="font-medium">Tags (Optional)</h3>
              <div className="space-y-2">
                <Input
                  placeholder="Enter tags separated by commas"
                  value={formData.tags.join(', ')}
                  onChange={(e) => updateFormData({ 
                    tags: e.target.value.split(',').map(t => t.trim()).filter(t => t) 
                  })}
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Please review your market details before creating.
              </AlertDescription>
            </Alert>

            {/* Summary */}
            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h3 className="font-medium mb-3">Basic Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Title:</span>
                    <span className="font-medium text-right max-w-xs">{formData.title}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <Badge variant="outline">
                      {categories.find(c => c.id === formData.category_id)?.name || formData.category_id}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{formData.market_type.replace('_', ' ')}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Outcomes */}
              <div>
                <h3 className="font-medium mb-3">Outcomes</h3>
                <div className="space-y-2">
                  {formData.outcomes.map((outcome, index) => (
                    <div key={outcome.id} className="flex justify-between text-sm">
                      <span className="text-gray-600">{index + 1}. {outcome.title}</span>
                      {outcome.description && (
                        <span className="text-xs text-gray-400">({outcome.description})</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Rules */}
              <div>
                <h3 className="font-medium mb-3">Rules & Limits</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Stake Range:</span>
                    <span className="font-medium">
                      ${formData.minimum_stake} - ${formData.maximum_stake || 'No limit'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Market Closes:</span>
                    <span className="font-medium">
                      {new Date(formData.closes_at).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Resolution By:</span>
                    <span className="font-medium">
                      {new Date(formData.resolves_at).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Settings */}
              <div>
                <h3 className="font-medium mb-3">Additional Settings</h3>
                <div className="space-y-2 text-sm">
                  {formData.resolution_source && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Resolution Source:</span>
                      <span className="font-medium text-right max-w-xs truncate">
                        {formData.resolution_source}
                      </span>
                    </div>
                  )}
                  {formData.tags.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tags:</span>
                      <div className="flex gap-1">
                        {formData.tags.map((tag, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Fee Notice */}
            <Alert className="bg-blue-50 border-blue-200">
              <DollarSign className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                A market creation fee of $10 will be charged. Platform fees apply to winnings.
              </AlertDescription>
            </Alert>
          </div>
        );

      default:
        return null;
    }
  };

  const progressPercentage = (currentStep / WIZARD_STEPS.length) * 100;

  return (
    <Card className="max-w-3xl mx-auto">
      <CardHeader>
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Step {currentStep} of {WIZARD_STEPS.length}</span>
              <span>{Math.round(progressPercentage)}% Complete</span>
            </div>
          </div>

          {/* Step Header */}
          <div>
            <CardTitle className="text-2xl">
              {WIZARD_STEPS[currentStep - 1].title}
            </CardTitle>
            <CardDescription>
              {WIZARD_STEPS[currentStep - 1].description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between mt-8 pt-6 border-t">
          <div>
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={isSubmitting}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={onCancel || (() => router.push('/custom-betting'))}
              disabled={isSubmitting}
            >
              Cancel
            </Button>

            {currentStep < WIZARD_STEPS.length ? (
              <Button onClick={handleNext}>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting || !validateStep(currentStep)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Create Market
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}