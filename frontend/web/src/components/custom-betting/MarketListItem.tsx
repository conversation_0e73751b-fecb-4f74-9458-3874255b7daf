/**
 * Custom Betting Platform - Market List Item Component
 * ===================================================
 * 
 * Individual market item for list view display.
 * More compact than MarketCard, shows key info in horizontal layout.
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock,
  Users,
  DollarSign,
  TrendingUp,
  Star,
  Target,
  ChevronRight
} from 'lucide-react';
import { CustomBet } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface MarketListItemProps {
  market: CustomBet;
  showCategory?: boolean;
}

export function MarketListItem({ market, showCategory = true }: MarketListItemProps) {
  const timeUntilDeadline = new Date(market.deadline).getTime() - Date.now();
  const isEndingSoon = timeUntilDeadline > 0 && timeUntilDeadline < 24 * 60 * 60 * 1000; // 24 hours
  const isExpired = timeUntilDeadline <= 0;

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return 'Expired';
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'settling': return 'bg-yellow-100 text-yellow-800';
      case 'settled': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      politics: 'bg-purple-100 text-purple-800',
      sports: 'bg-green-100 text-green-800',
      entertainment: 'bg-pink-100 text-pink-800',
      crypto: 'bg-orange-100 text-orange-800',
      tech: 'bg-blue-100 text-blue-800',
      default: 'bg-gray-100 text-gray-800'
    };
    return colors[category as keyof typeof colors] || colors.default;
  };

  // Get the most likely outcome for display
  const topOutcome = market.outcomes
    .sort((a, b) => b.implied_probability - a.implied_probability)[0];

  return (
    <Card className={cn(
      "group hover:shadow-md transition-all duration-200 border hover:border-primary/20",
      isEndingSoon && "border-yellow-300",
      isExpired && "opacity-60"
    )}>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1 min-w-0">
                {/* Badges Row */}
                <div className="flex items-center gap-2 mb-2">
                  {showCategory && (
                    <Badge variant="outline" className={getCategoryColor(market.category)}>
                      {market.category}
                    </Badge>
                  )}
                  <Badge variant="outline" className={getStatusColor(market.status)}>
                    {market.status}
                  </Badge>
                  {market.is_featured && (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  )}
                  {isEndingSoon && (
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                      Ending Soon
                    </Badge>
                  )}
                </div>

                {/* Title and Description */}
                <Link href={`/custom-betting/market/${market.id}`}>
                  <h3 className="font-semibold text-base group-hover:text-primary transition-colors cursor-pointer line-clamp-1 mb-1">
                    {market.title}
                  </h3>
                </Link>
                <p className="text-sm text-gray-600 line-clamp-1 mb-3">
                  {market.description}
                </p>

                {/* Stats Row */}
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">${market.total_stakes.toLocaleString()}</span>
                    <span className="text-gray-500">volume</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">{market.total_participants}</span>
                    <span className="text-gray-500">participants</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className={cn(
                      "font-medium",
                      isEndingSoon ? "text-yellow-600" : isExpired ? "text-gray-500" : "text-gray-700"
                    )}>
                      {formatTimeRemaining(timeUntilDeadline)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Top Outcome */}
              {topOutcome && (
                <div className="text-right ml-4">
                  <div className="text-sm text-gray-600 mb-1">Top Option</div>
                  <div className="bg-gray-50 rounded-lg p-2 min-w-[120px]">
                    <div className="text-sm font-medium line-clamp-1 mb-1">
                      {topOutcome.outcome_text}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-primary font-bold text-lg">
                        {topOutcome.current_odds.toFixed(2)}
                      </span>
                      <span className="text-gray-500 text-xs">
                        {(topOutcome.implied_probability * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {market.status === 'open' && (
              <Button size="sm" className="flex items-center gap-1">
                <Target className="h-4 w-4" />
                Bet
              </Button>
            )}
            
            <Button 
              variant="outline" 
              size="sm"
              asChild
              className="flex items-center gap-1"
            >
              <Link href={`/custom-betting/market/${market.id}`}>
                View
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}