/**
 * Custom Betting Platform - Create Bet Button Component
 * ====================================================
 * 
 * Quick action button to create a new betting market.
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { 
  Plus,
  Zap
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';

interface CreateBetButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function CreateBetButton({ 
  variant = 'default',
  size = 'default',
  showIcon = true,
  className 
}: CreateBetButtonProps) {
  const { isSignedIn } = useAuth();

  if (!isSignedIn) {
    return (
      <Button 
        variant="outline" 
        size={size}
        className={className}
        disabled
      >
        {showIcon && <Plus className="h-4 w-4 mr-2" />}
        Create Market
      </Button>
    );
  }

  return (
    <Button 
      asChild
      variant={variant}
      size={size}
      className={className}
    >
      <Link href="/custom-betting/create" className="flex items-center gap-2">
        {showIcon && <Plus className="h-4 w-4" />}
        Create Market
      </Link>
    </Button>
  );
}