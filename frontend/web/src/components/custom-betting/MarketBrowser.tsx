/**
 * Custom Betting Platform - Market Browser Component
 * =================================================
 * 
 * Main component for browsing and filtering betting markets.
 * Supports grid/list view, advanced filtering, and pagination.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { MarketCard } from './MarketCard';
import { MarketListItem } from './MarketListItem';
import { LoadingSpinner } from './LoadingSpinner';
import { EmptyState } from './EmptyState';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Grid3X3,
  List,
  Filter,
  SortAsc,
  SortDesc,
  RefreshCw,
  Calendar,
  DollarSign,
  Users,
  TrendingUp
} from 'lucide-react';
import customBettingAPI, { CustomBet, MarketFilters, BetSearchResponse } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface MarketBrowserProps {
  searchQuery?: string;
  category?: string;
  filters?: Partial<MarketFilters>;
  endingSoon?: boolean;
}

export function MarketBrowser({ 
  searchQuery = '', 
  category = 'all', 
  filters = {},
  endingSoon = false 
}: MarketBrowserProps) {
  const [markets, setMarkets] = useState<CustomBet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  
  const pageSize = 12;

  const loadMarkets = async () => {
    try {
      console.log('MarketBrowser: Starting to load markets...', { searchQuery, category, endingSoon });
      setLoading(true);
      setError(null);

      const marketFilters: MarketFilters = {
        ...filters,
        category: category !== 'all' ? category : undefined,
        search: searchQuery || undefined,
        sortBy,
        sortOrder,
      };

      const paginationParams = {
        page: currentPage,
        limit: pageSize,
      };

      console.log('MarketBrowser: Filters and pagination:', { marketFilters, paginationParams });

      let response: BetSearchResponse;

      if (endingSoon) {
        // For ending soon, use specialized endpoint
        console.log('MarketBrowser: Loading ending soon markets...');
        const endingSoonMarkets = await customBettingAPI.getEndingSoonMarkets(24, pageSize);
        response = {
          bets: endingSoonMarkets,
          total: endingSoonMarkets.length,
          page: 1,
          limit: pageSize,
          has_next: false,
          has_prev: false,
        };
      } else if (searchQuery) {
        // Use search API
        console.log('MarketBrowser: Searching markets...');
        response = await customBettingAPI.searchMarkets(searchQuery, marketFilters, paginationParams);
      } else {
        // Use browse API
        console.log('MarketBrowser: Loading all markets...');
        response = await customBettingAPI.getMarkets(marketFilters, paginationParams);
      }

      console.log('MarketBrowser: API response:', response);
      setMarkets(response.bets);
      setTotalResults(response.total);
      setHasNextPage(response.has_next);
      
    } catch (err) {
      console.error('MarketBrowser: Failed to load markets:', err);
      setError('Failed to load markets. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMarkets();
  }, [searchQuery, category, filters, sortBy, sortOrder, currentPage, endingSoon]);

  const handleRefresh = () => {
    setCurrentPage(1);
    loadMarkets();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const toggleSortOrder = () => {
    setSortOrder(current => current === 'asc' ? 'desc' : 'asc');
  };

  if (loading && markets.length === 0) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-red-600 mb-4">
            <Filter className="h-12 w-12 mx-auto mb-2" />
            <p className="font-medium">{error}</p>
          </div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!loading && markets.length === 0) {
    return (
      <EmptyState
        title="No markets found"
        description={searchQuery ? 
          `No markets match your search for "${searchQuery}"` : 
          "No markets available with the current filters"
        }
        actionLabel="Clear Filters"
        onAction={() => {
          setCurrentPage(1);
          // Parent should handle filter clearing
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Results Info */}
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                {totalResults.toLocaleString()} markets found
              </div>
              {loading && (
                <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>

            {/* View and Sort Controls */}
            <div className="flex items-center gap-3">
              {/* Sort Controls */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">
                    <span className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Newest
                    </span>
                  </SelectItem>
                  <SelectItem value="total_stakes">
                    <span className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Volume
                    </span>
                  </SelectItem>
                  <SelectItem value="total_participants">
                    <span className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Activity
                    </span>
                  </SelectItem>
                  <SelectItem value="deadline">
                    <span className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Ending Soon
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={toggleSortOrder}
                className="px-3"
              >
                {sortOrder === 'desc' ? (
                  <SortDesc className="h-4 w-4" />
                ) : (
                  <SortAsc className="h-4 w-4" />
                )}
              </Button>

              {/* View Mode Toggle */}
              <div className="flex border rounded-lg">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Markets Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {markets.map((market) => (
            <MarketCard 
              key={market.id} 
              market={market}
              showCategory={category === 'all'}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {markets.map((market) => (
            <MarketListItem 
              key={market.id} 
              market={market}
              showCategory={category === 'all'}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalResults > pageSize && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalResults)} of {totalResults.toLocaleString()} markets
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, Math.ceil(totalResults / pageSize)) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={page === currentPage ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                        className="w-8 h-8 p-0"
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!hasNextPage}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}