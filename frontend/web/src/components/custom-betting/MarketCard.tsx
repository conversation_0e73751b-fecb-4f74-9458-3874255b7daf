/**
 * Custom Betting Platform - Market Card Component
 * ==============================================
 * 
 * Individual market card showing bet details, odds, and actions.
 * Displays in grid view for market browsing.
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock,
  Users,
  DollarSign,
  TrendingUp,
  Star,
  Calendar,
  Target,
  Activity
} from 'lucide-react';
import { CustomBet } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface MarketCardProps {
  market: CustomBet;
  showCategory?: boolean;
  compact?: boolean;
}

export function MarketCard({ market, showCategory = true, compact = false }: MarketCardProps) {
  const timeUntilDeadline = market.closes_at ? new Date(market.closes_at).getTime() - Date.now() : Infinity;
  const isEndingSoon = timeUntilDeadline > 0 && timeUntilDeadline < 24 * 60 * 60 * 1000; // 24 hours
  const isExpired = timeUntilDeadline <= 0;
  
  const participationRate = 0; // Backend doesn't have participant limit

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return 'Expired';
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'settled': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'disputed': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      politics: 'bg-purple-100 text-purple-800',
      sports: 'bg-green-100 text-green-800',
      entertainment: 'bg-pink-100 text-pink-800',
      crypto: 'bg-orange-100 text-orange-800',
      tech: 'bg-blue-100 text-blue-800',
      default: 'bg-gray-100 text-gray-800'
    };
    return colors[category as keyof typeof colors] || colors.default;
  };
  
  // Extract category from tags if not directly available
  const marketCategory = market.tags?.[0] || 'general';

  // Get the two most likely outcomes for display
  const topOutcomes = market.outcomes
    .sort((a, b) => b.implied_probability - a.implied_probability)
    .slice(0, 2);

  return (
    <Card className={cn(
      "group hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/20",
      isEndingSoon && "border-yellow-300",
      isExpired && "opacity-60",
      compact && "h-auto"
    )}>
      <CardHeader className={cn("pb-3", compact && "pb-2")}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            {/* Category and Status Badges */}
            <div className="flex items-center gap-2 mb-2">
              {showCategory && marketCategory && (
                <Badge variant="outline" className={getCategoryColor(marketCategory)}>
                  {marketCategory}
                </Badge>
              )}
              <Badge variant="outline" className={getStatusColor(market.status)}>
                {market.status}
              </Badge>
              {market.is_featured && (
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
              )}
            </div>

            {/* Title */}
            <Link href={`/custom-betting/market/${market.id}`}>
              <h3 className={cn(
                "font-semibold group-hover:text-primary transition-colors cursor-pointer line-clamp-2",
                compact ? "text-sm" : "text-base"
              )}>
                {market.title}
              </h3>
            </Link>
          </div>

          {/* Time Remaining */}
          <div className="flex flex-col items-end text-right">
            <div className={cn(
              "flex items-center gap-1 text-xs",
              isEndingSoon ? "text-yellow-600" : isExpired ? "text-gray-500" : "text-gray-600"
            )}>
              <Clock className="h-3 w-3" />
              {formatTimeRemaining(timeUntilDeadline)}
            </div>
            {isEndingSoon && (
              <Badge variant="outline" className="mt-1 text-xs bg-yellow-50 text-yellow-700">
                Ending Soon
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={cn("pt-0", compact && "pb-3")}>
        {/* Description */}
        {!compact && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {market.description}
          </p>
        )}

        {/* Top Outcomes */}
        <div className="space-y-2 mb-4">
          {topOutcomes.map((outcome, index) => (
            <div key={outcome.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
              <span className={cn(
                "font-medium",
                compact ? "text-xs" : "text-sm"
              )}>
                {outcome.title}
              </span>
              <div className="flex items-center gap-2">
                <span className={cn(
                  "text-primary font-bold",
                  compact ? "text-sm" : "text-base"
                )}>
                  {outcome.current_odds?.toFixed(2) || (1 / outcome.implied_probability).toFixed(2)}
                </span>
                <span className={cn(
                  "text-gray-500",
                  compact ? "text-xs" : "text-sm"
                )}>
                  ({(outcome.implied_probability * 100).toFixed(0)}%)
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Market Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-gray-400" />
            <div>
              <div className={cn(
                "font-semibold",
                compact ? "text-sm" : "text-base"
              )}>
                ${market.total_stakes.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Volume</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-400" />
            <div>
              <div className={cn(
                "font-semibold",
                compact ? "text-sm" : "text-base"
              )}>
                {market.total_participants}
              </div>
              <div className="text-xs text-gray-500">Participants</div>
            </div>
          </div>
        </div>

        {/* Participation Progress */}
        {market.participant_limit && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-600">Participation</span>
              <span className="text-xs text-gray-600">
                {market.total_participants}/{market.participant_limit}
              </span>
            </div>
            <Progress 
              value={participationRate} 
              className="h-2"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            asChild 
            size={compact ? "sm" : "default"}
            className="flex-1"
          >
            <Link href={`/custom-betting/market/${market.id}`}>
              View Market
            </Link>
          </Button>
          
          {market.status === 'open' && (
            <Button 
              variant="outline" 
              size={compact ? "sm" : "default"}
              className="flex items-center gap-1"
            >
              <Target className="h-4 w-4" />
              {compact ? "Bet" : "Place Bet"}
            </Button>
          )}
        </div>

        {/* Quick Info */}
        <div className="flex items-center justify-between mt-3 pt-3 border-t text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Created {new Date(market.created_at).toLocaleDateString()}
          </div>
          
          <div className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {market.market_type}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}