/**
 * Custom Betting Platform - Live Notifications Component
 * =====================================================
 * 
 * Real-time notifications for betting activity and market updates.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  X,
  DollarSign,
  TrendingUp,
  Users,
  Bell,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useCustomBettingWebSocket, WebSocketMessage } from '@/hooks/useCustomBettingWebSocket';
import { cn } from '@/lib/utils';

interface LiveNotificationsProps {
  marketId: string;
  className?: string;
}

interface Notification {
  id: string;
  type: 'bet_placed' | 'bet_matched' | 'odds_change' | 'market_update';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
}

export function LiveNotifications({ marketId, className }: LiveNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isVisible, setIsVisible] = useState(true);
  
  const { subscribe } = useCustomBettingWebSocket({ marketId });

  // Subscribe to WebSocket messages and convert to notifications
  useEffect(() => {
    const unsubscribeBetPlaced = subscribe('bet_placed', (data: any) => {
      addNotification({
        type: 'bet_placed',
        title: 'New Bet Placed',
        message: `$${data.stake_amount} ${data.position_type} bet on ${data.outcome_id}`,
        priority: 'medium'
      });
    });

    const unsubscribeBetMatched = subscribe('bet_matched', (data: any) => {
      addNotification({
        type: 'bet_matched',
        title: 'Bet Matched',
        message: `$${data.stake_amount} bet matched at ${data.odds}x odds`,
        priority: 'high'
      });
    });

    const unsubscribeOddsChange = subscribe('odds_change', (data: any) => {
      addNotification({
        type: 'odds_change',
        title: 'Odds Updated',
        message: `${data.outcome_id} odds changed to ${data.new_odds}x`,
        priority: 'low'
      });
    });

    const unsubscribeMarketUpdate = subscribe('market_update', (data: any) => {
      addNotification({
        type: 'market_update',
        title: 'Market Updated',
        message: `Total volume: $${data.total_stakes.toLocaleString()}`,
        priority: 'low'
      });
    });

    return () => {
      unsubscribeBetPlaced();
      unsubscribeBetMatched();
      unsubscribeOddsChange();
      unsubscribeMarketUpdate();
    };
  }, [subscribe]);

  const addNotification = (notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const notification: Notification = {
      ...notificationData,
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep only latest 10

    // Auto-remove low priority notifications after 5 seconds
    if (notification.priority === 'low') {
      setTimeout(() => {
        removeNotification(notification.id);
      }, 5000);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'bet_placed': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'bet_matched': return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'odds_change': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'market_update': return <Users className="h-4 w-4 text-purple-600" />;
      default: return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-blue-200 bg-blue-50';
      case 'low': return 'border-gray-200 bg-gray-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    
    if (diffSecs < 60) return 'Just now';
    const diffMins = Math.floor(diffSecs / 60);
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    return `${diffHours}h ago`;
  };

  if (!isVisible || notifications.length === 0) {
    return null;
  }

  return (
    <div className={cn("fixed top-4 right-4 z-50 w-80 space-y-2", className)}>
      {/* Header */}
      <div className="flex items-center justify-between bg-white rounded-lg border shadow-lg p-3">
        <div className="flex items-center gap-2">
          <Bell className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-sm">Live Activity</span>
          {notifications.filter(n => !n.read).length > 0 && (
            <Badge variant="default" className="text-xs">
              {notifications.filter(n => !n.read).length}
            </Badge>
          )}
        </div>
        <div className="flex gap-1">
          {notifications.length > 0 && (
            <Button variant="ghost" size="sm" onClick={clearAll} className="h-6 w-6 p-0">
              <CheckCircle className="h-3 w-3" />
            </Button>
          )}
          <Button variant="ghost" size="sm" onClick={() => setIsVisible(false)} className="h-6 w-6 p-0">
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Notifications */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {notifications.map((notification) => (
          <Card 
            key={notification.id}
            className={cn(
              "transition-all duration-300 cursor-pointer hover:shadow-md",
              getPriorityColor(notification.priority),
              !notification.read && "ring-2 ring-blue-200"
            )}
            onClick={() => markAsRead(notification.id)}
          >
            <CardContent className="p-3">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-sm text-gray-900">
                      {notification.title}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeNotification(notification.id);
                      }}
                      className="h-4 w-4 p-0 text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">
                    {notification.message}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {formatTimeAgo(notification.timestamp)}
                    </span>
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs",
                        notification.priority === 'high' && "border-red-300 text-red-700",
                        notification.priority === 'medium' && "border-blue-300 text-blue-700",
                        notification.priority === 'low' && "border-gray-300 text-gray-700"
                      )}
                    >
                      {notification.priority}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Show Notifications Button (when hidden) */}
      {!isVisible && (
        <Button
          onClick={() => setIsVisible(true)}
          className="fixed top-4 right-4 z-50"
          size="sm"
        >
          <Bell className="h-4 w-4 mr-2" />
          Show Activity
        </Button>
      )}
    </div>
  );
}