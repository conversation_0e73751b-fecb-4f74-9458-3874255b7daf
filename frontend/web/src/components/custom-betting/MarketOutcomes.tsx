/**
 * Custom Betting Platform - Market Outcomes Component
 * ==================================================
 * 
 * Displays betting outcomes with current odds and betting buttons.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp,
  TrendingDown,
  Target,
  Users,
  DollarSign,
  Trophy,
  Minus,
  Plus
} from 'lucide-react';
import { CustomBet, BetParticipant, BetOutcome } from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';
import { useOddsChanges } from '@/hooks/useCustomBettingWebSocket';

interface MarketOutcomesProps {
  market: CustomBet;
  userParticipation?: BetParticipant | null;
  canBet: boolean;
  onBetPlaced?: () => void;
}

export function MarketOutcomes({ 
  market, 
  userParticipation, 
  canBet, 
  onBetPlaced 
}: MarketOutcomesProps) {
  const [selectedOutcome, setSelectedOutcome] = useState<string | null>(null);
  const [previousOdds, setPreviousOdds] = useState<Map<string, number>>(new Map());
  
  // Real-time odds changes
  const oddsChanges = useOddsChanges(market.id);

  const handleOutcomeSelect = (outcomeId: string) => {
    if (canBet) {
      setSelectedOutcome(outcomeId === selectedOutcome ? null : outcomeId);
    }
  };

  const getOutcomeProgress = (outcome: BetOutcome) => {
    const totalVolume = market.outcomes.reduce((sum, o) => sum + o.total_backing + o.total_laying, 0);
    const outcomeVolume = outcome.total_backing + outcome.total_laying;
    return totalVolume > 0 ? (outcomeVolume / totalVolume) * 100 : 0;
  };

  const isUserOutcome = (outcomeId: string) => {
    return userParticipation?.outcome_id === outcomeId;
  };

  // Track odds changes for visual indicators
  useEffect(() => {
    market.outcomes.forEach(outcome => {
      const currentOdds = outcome.current_odds;
      const liveOdds = oddsChanges.get(outcome.id);
      const effectiveOdds = liveOdds || currentOdds;
      
      if (previousOdds.has(outcome.id) && previousOdds.get(outcome.id) !== effectiveOdds) {
        // Odds changed - could trigger animation here
      }
      
      setPreviousOdds(prev => new Map(prev.set(outcome.id, effectiveOdds)));
    });
  }, [market.outcomes, oddsChanges, previousOdds]);

  const getEffectiveOdds = (outcome: BetOutcome) => {
    return oddsChanges.get(outcome.id) || outcome.current_odds;
  };

  const getOddsChangeIndicator = (outcome: BetOutcome) => {
    const currentOdds = getEffectiveOdds(outcome);
    const previousOdds = outcome.current_odds;
    
    if (currentOdds > previousOdds) {
      return { direction: 'up', className: 'text-green-600' };
    } else if (currentOdds < previousOdds) {
      return { direction: 'down', className: 'text-red-600' };
    }
    return null;
  };

  const sortedOutcomes = [...market.outcomes].sort((a, b) => 
    b.implied_probability - a.implied_probability
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Market Outcomes
          <Badge variant="outline" className="ml-auto">
            {market.outcomes.length} options
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {sortedOutcomes.map((outcome, index) => {
          const isWinning = outcome.is_winning_outcome;
          const isSelected = selectedOutcome === outcome.id;
          const isUserPosition = isUserOutcome(outcome.id);
          const progress = getOutcomeProgress(outcome);

          return (
            <div
              key={outcome.id}
              className={cn(
                "relative border-2 rounded-lg p-4 transition-all duration-200",
                isSelected && canBet && "border-primary bg-primary/5",
                isUserPosition && "border-blue-300 bg-blue-50",
                isWinning && "border-green-300 bg-green-50",
                canBet && "cursor-pointer hover:border-gray-300",
                !canBet && "opacity-75"
              )}
              onClick={() => handleOutcomeSelect(outcome.id)}
            >
              {/* Ranking Badge */}
              <div className="absolute -top-2 -left-2">
                <Badge 
                  className={cn(
                    "text-xs",
                    index === 0 && "bg-yellow-500",
                    index === 1 && "bg-gray-400",
                    index === 2 && "bg-orange-600",
                    index > 2 && "bg-gray-300"
                  )}
                >
                  #{index + 1}
                </Badge>
              </div>

              {/* User Position Indicator */}
              {isUserPosition && (
                <div className="absolute -top-2 -right-2">
                  <Badge className="bg-blue-500 text-white text-xs">
                    Your Bet
                  </Badge>
                </div>
              )}

              {/* Winning Outcome Indicator */}
              {isWinning && (
                <div className="absolute -top-2 right-8">
                  <Badge className="bg-green-500 text-white text-xs flex items-center gap-1">
                    <Trophy className="h-3 w-3" />
                    Winner
                  </Badge>
                </div>
              )}

              <div className="space-y-3">
                {/* Outcome Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                      {outcome.outcome_text}
                    </h3>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        ${(outcome.total_backing + outcome.total_laying).toLocaleString()} volume
                      </span>
                      <span>{(outcome.implied_probability * 100).toFixed(1)}% probability</span>
                    </div>
                  </div>

                  {/* Odds Display */}
                  <div className="text-right">
                    <div className="flex items-center gap-1 justify-end">
                      <div className="text-2xl font-bold text-primary">
                        {getEffectiveOdds(outcome).toFixed(2)}
                      </div>
                      {(() => {
                        const changeIndicator = getOddsChangeIndicator(outcome);
                        return changeIndicator ? (
                          <div className={`${changeIndicator.className} transition-colors duration-300`}>
                            {changeIndicator.direction === 'up' ? (
                              <TrendingUp className="h-4 w-4" />
                            ) : (
                              <TrendingDown className="h-4 w-4" />
                            )}
                          </div>
                        ) : null;
                      })()}
                    </div>
                    <div className="text-xs text-gray-500">
                      odds
                    </div>
                  </div>
                </div>

                {/* Volume Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>Market Share</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>

                {/* Backing vs Laying */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-50 rounded p-2">
                    <div className="flex items-center gap-1 text-green-700 mb-1">
                      <TrendingUp className="h-3 w-3" />
                      <span className="font-medium">Backing</span>
                    </div>
                    <div className="font-semibold">${outcome.total_backing.toLocaleString()}</div>
                  </div>
                  <div className="bg-red-50 rounded p-2">
                    <div className="flex items-center gap-1 text-red-700 mb-1">
                      <TrendingDown className="h-3 w-3" />
                      <span className="font-medium">Laying</span>
                    </div>
                    <div className="font-semibold">${outcome.total_laying.toLocaleString()}</div>
                  </div>
                </div>

                {/* Betting Buttons */}
                {canBet && isSelected && (
                  <div className="pt-2 border-t">
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        className="flex-1 bg-green-600 hover:bg-green-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Open betting modal for backing
                          console.log('Back outcome:', outcome.id);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Back {getEffectiveOdds(outcome).toFixed(2)}
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        className="flex-1 border-red-300 text-red-700 hover:bg-red-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Open betting modal for laying
                          console.log('Lay outcome:', outcome.id);
                        }}
                      >
                        <Minus className="h-4 w-4 mr-1" />
                        Lay {getEffectiveOdds(outcome).toFixed(2)}
                      </Button>
                    </div>
                  </div>
                )}

                {/* User Position Details */}
                {isUserPosition && userParticipation && (
                  <div className="pt-2 border-t bg-blue-50 -mx-4 -mb-4 px-4 pb-4 rounded-b-lg">
                    <div className="text-sm">
                      <div className="font-medium text-blue-800 mb-1">Your Position</div>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="text-blue-600">Stake:</span> ${userParticipation.stake_amount}
                        </div>
                        <div>
                          <span className="text-blue-600">Type:</span> {userParticipation.position_type}
                        </div>
                        <div>
                          <span className="text-blue-600">Odds:</span> {userParticipation.desired_odds}
                        </div>
                        <div>
                          <span className="text-blue-600">Potential:</span> ${userParticipation.potential_payout}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* Betting Instructions */}
        {canBet && (
          <div className="text-center py-4 border-t">
            <p className="text-sm text-gray-600">
              Click on an outcome to see betting options
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}