/**
 * Custom Betting Platform - Market Activity Component
 * ==================================================
 * 
 * Shows recent activity, participants, and market statistics.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Activity,
  Users,
  TrendingUp,
  Clock,
  DollarSign,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import customBettingAPI, { BetParticipant } from '@/lib/custom-betting-api';
import { LoadingSpinner } from './LoadingSpinner';
import { useBetActivity } from '@/hooks/useCustomBettingWebSocket';

interface MarketActivityProps {
  marketId: string;
}

interface MockActivity {
  id: string;
  type: 'bet_placed' | 'bet_matched' | 'participant_joined';
  user: string;
  amount?: number;
  outcome?: string;
  timestamp: string;
}

export function MarketActivity({ marketId }: MarketActivityProps) {
  const [participants, setParticipants] = useState<BetParticipant[]>([]);
  const [queueStatus, setQueueStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time bet activity from WebSocket
  const liveBetActivity = useBetActivity(marketId);
  
  // Mock activity data for demonstration (will be replaced by real-time data)
  const [staticActivity] = useState<MockActivity[]>([
    {
      id: '1',
      type: 'bet_placed',
      user: 'Alice',
      amount: 50,
      outcome: 'Option A',
      timestamp: new Date(Date.now() - 300000).toISOString() // 5 min ago
    },
    {
      id: '2', 
      type: 'bet_matched',
      user: 'Bob',
      amount: 25,
      outcome: 'Option B',
      timestamp: new Date(Date.now() - 600000).toISOString() // 10 min ago
    },
    {
      id: '3',
      type: 'participant_joined',
      user: 'Charlie',
      amount: 100,
      outcome: 'Option A',
      timestamp: new Date(Date.now() - 900000).toISOString() // 15 min ago
    }
  ]);

  // Combine static and live activity data
  const combinedActivity = React.useMemo(() => {
    const liveActivities: MockActivity[] = liveBetActivity.map(activity => ({
      id: `live-${activity.user_id}-${Date.now()}`,
      type: activity.activity_type === 'placed' ? 'bet_placed' : activity.activity_type === 'matched' ? 'bet_matched' : 'participant_joined',
      user: `User-${activity.user_id.slice(-4)}`,
      amount: activity.stake_amount,
      outcome: activity.outcome_id,
      timestamp: new Date().toISOString()
    }));

    return [...liveActivities, ...staticActivity]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10); // Keep only latest 10
  }, [liveBetActivity, staticActivity]);

  const recentActivity = combinedActivity;

  const loadActivity = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load participants
      const participantsData = await customBettingAPI.getBetParticipants(marketId);
      setParticipants(participantsData);

      // Load queue status
      const queueData = await customBettingAPI.getQueueStatus(marketId);
      setQueueStatus(queueData);

    } catch (err) {
      console.error('Failed to load market activity:', err);
      setError('Failed to load activity data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadActivity();
  }, [marketId]);

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'bet_placed': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'bet_matched': return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'participant_joined': return <Users className="h-4 w-4 text-purple-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActivityMessage = (activity: MockActivity) => {
    switch (activity.type) {
      case 'bet_placed':
        return `placed a $${activity.amount} bet on ${activity.outcome}`;
      case 'bet_matched':
        return `matched a $${activity.amount} bet on ${activity.outcome}`;
      case 'participant_joined':
        return `joined with $${activity.amount} on ${activity.outcome}`;
      default:
        return 'unknown activity';
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading activity..." />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Market Activity
          </CardTitle>
          <Button variant="outline" size="sm" onClick={loadActivity}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="activity" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="activity">Recent Activity</TabsTrigger>
            <TabsTrigger value="participants">Participants</TabsTrigger>
            <TabsTrigger value="queue">Queue Status</TabsTrigger>
          </TabsList>

          <TabsContent value="activity" className="space-y-4">
            {recentActivity.length > 0 ? (
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-center w-8 h-8 bg-white rounded-full">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{activity.user}</span>
                        <span className="text-sm text-gray-600">
                          {getActivityMessage(activity)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                        <Clock className="h-3 w-3" />
                        {formatTimeAgo(activity.timestamp)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No recent activity</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="participants" className="space-y-4">
            {participants.length > 0 ? (
              <div className="space-y-3">
                {participants.slice(0, 10).map((participant) => (
                  <div key={participant.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {participant.user_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium text-sm">
                          User {participant.user_id.slice(-4)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {participant.position_type} • {participant.status}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-sm">
                        ${participant.stake_amount}
                      </div>
                      <div className="text-xs text-gray-500">
                        {participant.desired_odds}x odds
                      </div>
                    </div>
                  </div>
                ))}
                
                {participants.length > 10 && (
                  <div className="text-center py-2">
                    <Button variant="outline" size="sm">
                      Show {participants.length - 10} more
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No participants yet</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="queue" className="space-y-4">
            {queueStatus ? (
              <div className="space-y-4">
                {/* Queue Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-800">
                      {queueStatus.total_pending || 0}
                    </div>
                    <div className="text-xs text-blue-600">Total Pending</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-green-800">
                      {queueStatus.backing_positions || 0}
                    </div>
                    <div className="text-xs text-green-600">Backing</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-lg font-bold text-red-800">
                      {queueStatus.laying_positions || 0}
                    </div>
                    <div className="text-xs text-red-600">Laying</div>
                  </div>
                </div>

                {/* Queue Details */}
                {queueStatus.queue_entries && queueStatus.queue_entries.length > 0 ? (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Pending Orders</h4>
                    {queueStatus.queue_entries.slice(0, 5).map((entry: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                        <span className="capitalize">{entry.position_type}</span>
                        <span>${entry.stake_amount}</span>
                        <span>{entry.desired_odds}x</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No pending orders</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Queue data unavailable</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}