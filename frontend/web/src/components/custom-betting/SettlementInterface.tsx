/**
 * Custom Betting Platform - Settlement Interface Component
 * =======================================================
 * 
 * Interface for settling betting markets and determining outcomes.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Gavel,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Users,
  FileText,
  Eye,
  Loader2,
  Info,
  ExternalLink
} from 'lucide-react';
import { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import customBettingAPI from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface SettlementInterfaceProps {
  market: CustomBet;
  participants: BetParticipant[];
  canSettle: boolean;
  onSettled?: () => void;
}

interface SettlementData {
  winning_outcome_id: string;
  settlement_notes: string;
  evidence_urls: string[];
  settlement_method: 'manual' | 'oracle' | 'consensus';
}

export function SettlementInterface({ 
  market, 
  participants, 
  canSettle, 
  onSettled 
}: SettlementInterfaceProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isSettling, setIsSettling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settlementData, setSettlementData] = useState<SettlementData>({
    winning_outcome_id: '',
    settlement_notes: '',
    evidence_urls: [],
    settlement_method: market.settlement_type as any || 'manual'
  });

  // Calculate settlement statistics
  const calculateSettlementStats = () => {
    const stats = {
      totalParticipants: participants.length,
      totalStakes: market.total_stakes,
      outcomeBreakdown: market.outcomes.map(outcome => {
        const outcomeParticipants = participants.filter(p => p.outcome_id === outcome.id);
        const totalStaked = outcomeParticipants.reduce((sum, p) => sum + p.stake_amount, 0);
        
        return {
          outcome,
          participantCount: outcomeParticipants.length,
          totalStaked,
          percentage: market.total_stakes > 0 ? (totalStaked / market.total_stakes) * 100 : 0
        };
      }),
      estimatedPayouts: market.outcomes.map(outcome => {
        if (settlementData.winning_outcome_id !== outcome.id) return null;
        
        const winners = participants.filter(p => 
          p.outcome_id === outcome.id && p.position_type === 'backing'
        );
        
        const totalWinnerStakes = winners.reduce((sum, p) => sum + p.stake_amount, 0);
        const totalLoserStakes = market.total_stakes - totalWinnerStakes;
        
        return {
          outcome,
          winnerCount: winners.length,
          totalWinnerStakes,
          totalPayouts: totalWinnerStakes + totalLoserStakes,
          averagePayout: winners.length > 0 ? (totalWinnerStakes + totalLoserStakes) / winners.length : 0
        };
      }).filter(Boolean)[0]
    };

    return stats;
  };

  const handleSettlement = async () => {
    if (!settlementData.winning_outcome_id) {
      setError('Please select a winning outcome.');
      return;
    }

    try {
      setIsSettling(true);
      setError(null);

      await customBettingAPI.settleBet(market.id, {
        winning_outcome_id: settlementData.winning_outcome_id,
        settlement_notes: settlementData.settlement_notes,
        evidence_urls: settlementData.evidence_urls.filter(url => url.trim())
      });

      onSettled?.();

    } catch (err: any) {
      setError(err.message || 'Failed to settle market');
    } finally {
      setIsSettling(false);
    }
  };

  const addEvidenceUrl = () => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: [...prev.evidence_urls, '']
    }));
  };

  const updateEvidenceUrl = (index: number, url: string) => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: prev.evidence_urls.map((u, i) => i === index ? url : u)
    }));
  };

  const removeEvidenceUrl = (index: number) => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: prev.evidence_urls.filter((_, i) => i !== index)
    }));
  };

  const stats = calculateSettlementStats();
  const isSettled = market.status === 'settled';
  const isSettling_status = market.status === 'settling';

  return (
    <div className="space-y-6">
      {/* Settlement Status Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gavel className="h-5 w-5" />
            Market Settlement
            <Badge 
              className={cn(
                isSettled && "bg-green-100 text-green-800",
                isSettling_status && "bg-yellow-100 text-yellow-800",
                !isSettled && !isSettling_status && "bg-gray-100 text-gray-800"
              )}
            >
              {market.status.charAt(0).toUpperCase() + market.status.slice(1)}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium mb-1">{market.title}</h3>
              <p className="text-sm text-gray-600">
                Settlement deadline: {new Date(market.verification_deadline).toLocaleString()}
              </p>
            </div>
            
            {isSettled && market.winning_outcome_id && (
              <div className="text-right">
                <div className="text-sm text-gray-600">Winning Outcome</div>
                <div className="font-medium text-green-700">
                  {market.outcomes.find(o => o.id === market.winning_outcome_id)?.outcome_text}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Settlement Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="outcomes">Outcomes</TabsTrigger>
          <TabsTrigger value="participants">Participants</TabsTrigger>
          <TabsTrigger value="settle">Settlement</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.totalParticipants}
                </div>
                <div className="text-sm text-gray-600">Total Participants</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  ${stats.totalStakes.toFixed(2)}
                </div>
                <div className="text-sm text-gray-600">Total Volume</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {market.outcomes.length}
                </div>
                <div className="text-sm text-gray-600">Possible Outcomes</div>
              </CardContent>
            </Card>
          </div>

          {/* Market Criteria */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Settlement Criteria
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800">{market.event_criteria}</p>
              </div>
            </CardContent>
          </Card>

          {/* Settlement Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <div className="font-medium">Market Created</div>
                  <div className="text-sm text-gray-600">
                    {new Date(market.created_at).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  new Date() > new Date(market.deadline) ? "bg-green-500" : "bg-gray-300"
                )}></div>
                <div>
                  <div className="font-medium">Betting Deadline</div>
                  <div className="text-sm text-gray-600">
                    {new Date(market.deadline).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  isSettled ? "bg-green-500" : new Date() > new Date(market.verification_deadline) ? "bg-red-500" : "bg-yellow-500"
                )}></div>
                <div>
                  <div className="font-medium">Settlement Deadline</div>
                  <div className="text-sm text-gray-600">
                    {new Date(market.verification_deadline).toLocaleString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Outcomes Tab */}
        <TabsContent value="outcomes" className="space-y-4">
          {stats.outcomeBreakdown.map((item) => (
            <Card key={item.outcome.id}>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{item.outcome.outcome_text}</h4>
                    <Badge variant="outline">
                      {item.outcome.current_odds}x odds
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Participants</div>
                      <div className="font-medium">{item.participantCount}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Total Staked</div>
                      <div className="font-medium">${item.totalStaked.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Market Share</div>
                      <div className="font-medium">{item.percentage.toFixed(1)}%</div>
                    </div>
                  </div>
                  
                  <Progress value={item.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Participants Tab */}
        <TabsContent value="participants" className="space-y-4">
          {participants.length > 0 ? (
            <div className="space-y-2">
              {participants.map((participant) => (
                <div 
                  key={participant.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium font-mono text-sm">
                        {participant.user_id.slice(-8)}
                      </span>
                      <Badge 
                        variant="outline" 
                        className={cn(
                          participant.position_type === 'backing' && "bg-green-100 text-green-800",
                          participant.position_type === 'laying' && "bg-red-100 text-red-800"
                        )}
                      >
                        {participant.position_type}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      Outcome: {participant.outcome_id}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-medium">${participant.stake_amount.toFixed(2)}</div>
                    <div className="text-sm text-gray-600">{participant.desired_odds}x</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No participants in this market</p>
            </div>
          )}
        </TabsContent>

        {/* Settlement Tab */}
        <TabsContent value="settle" className="space-y-6">
          {!canSettle ? (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                You don't have permission to settle this market. Only the market creator or admin can perform settlement.
              </AlertDescription>
            </Alert>
          ) : isSettled ? (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                This market has already been settled.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-6">
              {/* Winning Outcome Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Select Winning Outcome</CardTitle>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={settlementData.winning_outcome_id}
                    onValueChange={(value) => setSettlementData(prev => ({ ...prev, winning_outcome_id: value }))}
                  >
                    {market.outcomes.map((outcome) => (
                      <div key={outcome.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={outcome.id} id={outcome.id} />
                        <Label htmlFor={outcome.id} className="flex-1 cursor-pointer">
                          <div className="flex items-center justify-between">
                            <span>{outcome.outcome_text}</span>
                            <span className="text-sm text-gray-600">
                              {outcome.current_odds}x odds
                            </span>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>

              {/* Settlement Notes */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Settlement Notes</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="notes">Explain the settlement decision</Label>
                    <Textarea
                      id="notes"
                      placeholder="Provide details about how and why this outcome was determined..."
                      value={settlementData.settlement_notes}
                      onChange={(e) => setSettlementData(prev => ({ ...prev, settlement_notes: e.target.value }))}
                      rows={4}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Evidence URLs */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <ExternalLink className="h-5 w-5" />
                    Supporting Evidence (Optional)
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {settlementData.evidence_urls.map((url, index) => (
                    <div key={index} className="flex gap-2">
                      <input
                        type="url"
                        placeholder="https://example.com/evidence"
                        value={url}
                        onChange={(e) => updateEvidenceUrl(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeEvidenceUrl(index)}
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <Button variant="outline" onClick={addEvidenceUrl}>
                    Add Evidence URL
                  </Button>
                </CardContent>
              </Card>

              {/* Estimated Payouts */}
              {settlementData.winning_outcome_id && stats.estimatedPayouts && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      Estimated Payouts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-600">Winners</div>
                        <div className="text-xl font-bold text-green-600">
                          {stats.estimatedPayouts.winnerCount}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-600">Total Payouts</div>
                        <div className="text-xl font-bold">
                          ${stats.estimatedPayouts.totalPayouts.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Settlement Actions */}
              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button
                  onClick={handleSettlement}
                  disabled={isSettling || !settlementData.winning_outcome_id}
                  size="lg"
                >
                  {isSettling ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Settling Market...
                    </>
                  ) : (
                    <>
                      <Gavel className="h-4 w-4 mr-2" />
                      Settle Market
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}