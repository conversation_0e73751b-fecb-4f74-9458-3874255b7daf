/**
 * Custom Betting Platform - Component Exports
 * ==========================================
 * 
 * Centralized exports for all custom betting components.
 */

// Core Components
export { MarketBrowser } from './MarketBrowser';
export { MarketCard } from './MarketCard';
export { MarketListItem } from './MarketListItem';
export { TrendingMarkets } from './TrendingMarkets';
export { MarketStats } from './MarketStats';
export { CreateBetButton } from './CreateBetButton';
export { CreateBetWizard } from './CreateBetWizard';

// Market Detail Components
export { MarketHeader } from './MarketHeader';
export { MarketOutcomes } from './MarketOutcomes';
export { MarketActivity } from './MarketActivity';
export { MarketInfo } from './MarketInfo';
export { BettingInterface } from './BettingInterface';

// UI Components
export { LoadingSpinner } from './LoadingSpinner';
export { EmptyState } from './EmptyState';
export { LiveNotifications } from './LiveNotifications';

// User Management Components
export { UserBetManager } from './UserBetManager';

// Settlement and Admin Components
export { SettlementInterface } from './SettlementInterface';
export { AdminDashboard } from './AdminDashboard';
export { DisputeResolution } from './DisputeResolution';
export { MarketAnalytics } from './MarketAnalytics';

// Types
export type { CustomBet, BetOutcome, BetParticipant } from '@/lib/custom-betting-api';