/**
 * Custom Betting Platform - Dispute Resolution Component
 * =====================================================
 * 
 * Interface for handling disputes on betting market settlements.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Scale,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  FileText,
  Vote,
  Eye,
  Flag,
  Loader2,
  Upload,
  ExternalLink,
  Shield
} from 'lucide-react';
import { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import customBettingAPI from '@/lib/custom-betting-api';
import { cn } from '@/lib/utils';

interface Dispute {
  id: string;
  market_id: string;
  disputer_user_id: string;
  dispute_reason: string;
  dispute_details: string;
  evidence_urls: string[];
  status: 'pending' | 'under_review' | 'resolved' | 'rejected';
  created_at: string;
  resolved_at?: string;
  resolution_notes?: string;
  admin_user_id?: string;
  community_votes?: {
    support: number;
    oppose: number;
    abstain: number;
  };
}

interface DisputeResolutionProps {
  market: CustomBet;
  disputes: Dispute[];
  canCreateDispute: boolean;
  canResolveDisputes: boolean;
  userParticipation?: BetParticipant | null;
  onDisputeCreated?: () => void;
  onDisputeResolved?: () => void;
}

export function DisputeResolution({
  market,
  disputes,
  canCreateDispute,
  canResolveDisputes,
  userParticipation,
  onDisputeCreated,
  onDisputeResolved
}: DisputeResolutionProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isCreatingDispute, setIsCreatingDispute] = useState(false);
  const [isResolvingDispute, setIsResolvingDispute] = useState(false);
  const [selectedDisputeId, setSelectedDisputeId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const [newDispute, setNewDispute] = useState({
    reason: '',
    details: '',
    evidence_urls: ['']
  });

  const [resolution, setResolution] = useState({
    action: '',
    notes: ''
  });

  const disputeReasons = [
    { value: 'incorrect_settlement', label: 'Incorrect Settlement Decision' },
    { value: 'insufficient_evidence', label: 'Insufficient Evidence Provided' },
    { value: 'criteria_violation', label: 'Settlement Criteria Not Followed' },
    { value: 'technical_error', label: 'Technical Error in Settlement' },
    { value: 'fraud_suspected', label: 'Suspected Fraud or Manipulation' },
    { value: 'other', label: 'Other (Specify in Details)' }
  ];

  const handleCreateDispute = async () => {
    if (!newDispute.reason || !newDispute.details.trim()) {
      setError('Please select a reason and provide details for your dispute.');
      return;
    }

    try {
      setIsCreatingDispute(true);
      setError(null);

      await customBettingAPI.createDispute(market.id, {
        reason: newDispute.reason,
        details: newDispute.details,
        evidence_urls: newDispute.evidence_urls.filter(url => url.trim())
      });

      // Reset form
      setNewDispute({
        reason: '',
        details: '',
        evidence_urls: ['']
      });

      onDisputeCreated?.();

    } catch (err: any) {
      setError(err.message || 'Failed to create dispute');
    } finally {
      setIsCreatingDispute(false);
    }
  };

  const handleResolveDispute = async (disputeId: string) => {
    if (!resolution.action || !resolution.notes.trim()) {
      setError('Please select an action and provide resolution notes.');
      return;
    }

    try {
      setIsResolvingDispute(true);
      setError(null);

      await customBettingAPI.resolveDispute(disputeId, {
        action: resolution.action,
        notes: resolution.notes
      });

      setSelectedDisputeId(null);
      setResolution({ action: '', notes: '' });
      onDisputeResolved?.();

    } catch (err: any) {
      setError(err.message || 'Failed to resolve dispute');
    } finally {
      setIsResolvingDispute(false);
    }
  };

  const addEvidenceUrl = () => {
    setNewDispute(prev => ({
      ...prev,
      evidence_urls: [...prev.evidence_urls, '']
    }));
  };

  const updateEvidenceUrl = (index: number, url: string) => {
    setNewDispute(prev => ({
      ...prev,
      evidence_urls: prev.evidence_urls.map((u, i) => i === index ? url : u)
    }));
  };

  const removeEvidenceUrl = (index: number) => {
    if (newDispute.evidence_urls.length > 1) {
      setNewDispute(prev => ({
        ...prev,
        evidence_urls: prev.evidence_urls.filter((_, i) => i !== index)
      }));
    }
  };

  const getDisputeStatusBadge = (status: string) => {
    const variants: Record<string, { className: string; icon: React.ReactNode }> = {
      'pending': { 
        className: 'bg-yellow-100 text-yellow-800', 
        icon: <Clock className="h-3 w-3" />
      },
      'under_review': { 
        className: 'bg-blue-100 text-blue-800', 
        icon: <Eye className="h-3 w-3" />
      },
      'resolved': { 
        className: 'bg-green-100 text-green-800', 
        icon: <CheckCircle className="h-3 w-3" />
      },
      'rejected': { 
        className: 'bg-red-100 text-red-800', 
        icon: <XCircle className="h-3 w-3" />
      }
    };

    const config = variants[status] || variants['pending'];
    
    return (
      <Badge variant="outline" className={config.className}>
        <div className="flex items-center gap-1">
          {config.icon}
          {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
        </div>
      </Badge>
    );
  };

  const pendingDisputes = disputes.filter(d => d.status === 'pending' || d.status === 'under_review');
  const resolvedDisputes = disputes.filter(d => d.status === 'resolved' || d.status === 'rejected');

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scale className="h-5 w-5" />
            Dispute Resolution
            {disputes.length > 0 && (
              <Badge variant="outline">
                {disputes.length} dispute{disputes.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">{market.title}</h3>
              <p className="text-sm text-gray-600">
                Settlement status: {market.status === 'settled' ? 'Settled' : 'Pending Settlement'}
              </p>
            </div>
            
            {market.status === 'settled' && (
              <div className="text-right">
                <div className="text-sm text-gray-600">Settled Outcome</div>
                <div className="font-medium">
                  {market.outcomes.find(o => o.is_winning_outcome)?.outcome_text || 'Unknown'}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dispute Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">
            Overview ({disputes.length})
          </TabsTrigger>
          <TabsTrigger value="create">
            Create Dispute
          </TabsTrigger>
          <TabsTrigger value="resolve">
            Resolution
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {disputes.length > 0 ? (
            <div className="space-y-4">
              {/* Pending Disputes */}
              {pendingDisputes.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-medium text-lg">Active Disputes</h3>
                  {pendingDisputes.map((dispute) => (
                    <Card key={dispute.id} className="border-yellow-300 bg-yellow-50/50">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">
                                {disputeReasons.find(r => r.value === dispute.dispute_reason)?.label}
                              </h4>
                              {getDisputeStatusBadge(dispute.status)}
                            </div>
                            <div className="text-sm text-gray-600">
                              {new Date(dispute.created_at).toLocaleDateString()}
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-700">{dispute.dispute_details}</p>
                          
                          {dispute.evidence_urls && dispute.evidence_urls.length > 0 && (
                            <div className="space-y-2">
                              <div className="text-xs font-medium text-gray-600">Evidence:</div>
                              {dispute.evidence_urls.map((url, index) => (
                                <a 
                                  key={index}
                                  href={url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  Evidence Link {index + 1}
                                </a>
                              ))}
                            </div>
                          )}
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              Disputer: {dispute.disputer_user_id.slice(-8)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Created: {new Date(dispute.created_at).toLocaleString()}
                            </span>
                          </div>
                          
                          {/* Community Votes (if available) */}
                          {dispute.community_votes && (
                            <div className="bg-white rounded p-3 border">
                              <div className="text-xs font-medium text-gray-600 mb-2">Community Votes</div>
                              <div className="flex items-center gap-4 text-sm">
                                <span className="flex items-center gap-1 text-green-600">
                                  <CheckCircle className="h-3 w-3" />
                                  Support: {dispute.community_votes.support}
                                </span>
                                <span className="flex items-center gap-1 text-red-600">
                                  <XCircle className="h-3 w-3" />
                                  Oppose: {dispute.community_votes.oppose}
                                </span>
                                <span className="flex items-center gap-1 text-gray-600">
                                  <MessageSquare className="h-3 w-3" />
                                  Abstain: {dispute.community_votes.abstain}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Resolved Disputes */}
              {resolvedDisputes.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-medium text-lg">Resolved Disputes</h3>
                  {resolvedDisputes.map((dispute) => (
                    <Card key={dispute.id} className="border-gray-200">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">
                                {disputeReasons.find(r => r.value === dispute.dispute_reason)?.label}
                              </h4>
                              {getDisputeStatusBadge(dispute.status)}
                            </div>
                          </div>
                          
                          {dispute.resolution_notes && (
                            <div className="bg-gray-50 rounded p-3">
                              <div className="text-xs font-medium text-gray-600 mb-1">Resolution</div>
                              <p className="text-sm text-gray-700">{dispute.resolution_notes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Scale className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <h3 className="font-medium mb-1">No Disputes Filed</h3>
              <p>This market currently has no active disputes.</p>
            </div>
          )}
        </TabsContent>

        {/* Create Dispute Tab */}
        <TabsContent value="create" className="space-y-6">
          {!canCreateDispute ? (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                You must be a participant in this market to file a dispute.
              </AlertDescription>
            </Alert>
          ) : market.status !== 'settled' ? (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Disputes can only be filed after the market has been settled.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-6">
              <Alert>
                <Flag className="h-4 w-4" />
                <AlertDescription>
                  Filing a dispute will initiate a review process. Please provide detailed information and evidence to support your claim.
                </AlertDescription>
              </Alert>

              {/* Dispute Reason */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Dispute Reason</CardTitle>
                </CardHeader>
                <CardContent>
                  <Select 
                    value={newDispute.reason} 
                    onValueChange={(value) => setNewDispute(prev => ({ ...prev, reason: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a reason for your dispute..." />
                    </SelectTrigger>
                    <SelectContent>
                      {disputeReasons.map(reason => (
                        <SelectItem key={reason.value} value={reason.value}>
                          {reason.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {/* Dispute Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Dispute Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label htmlFor="details">Detailed Explanation</Label>
                    <Textarea
                      id="details"
                      placeholder="Provide a detailed explanation of why you believe the settlement is incorrect. Include specific facts, dates, and reasoning..."
                      value={newDispute.details}
                      onChange={(e) => setNewDispute(prev => ({ ...prev, details: e.target.value }))}
                      rows={6}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Evidence */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Supporting Evidence</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {newDispute.evidence_urls.map((url, index) => (
                    <div key={index} className="flex gap-2">
                      <input
                        type="url"
                        placeholder="https://example.com/evidence"
                        value={url}
                        onChange={(e) => updateEvidenceUrl(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                      {newDispute.evidence_urls.length > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeEvidenceUrl(index)}
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  
                  <Button variant="outline" onClick={addEvidenceUrl}>
                    <Upload className="h-4 w-4 mr-2" />
                    Add Evidence URL
                  </Button>
                </CardContent>
              </Card>

              {/* Submit Dispute */}
              <div className="flex justify-end">
                <Button 
                  onClick={handleCreateDispute}
                  disabled={isCreatingDispute}
                  size="lg"
                >
                  {isCreatingDispute ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Filing Dispute...
                    </>
                  ) : (
                    <>
                      <Flag className="h-4 w-4 mr-2" />
                      File Dispute
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </TabsContent>

        {/* Resolution Tab */}
        <TabsContent value="resolve" className="space-y-6">
          {!canResolveDisputes ? (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                You don't have permission to resolve disputes. Only admins and market creators can resolve disputes.
              </AlertDescription>
            </Alert>
          ) : pendingDisputes.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <h3 className="font-medium mb-1">No Pending Disputes</h3>
              <p>All disputes have been resolved.</p>
            </div>
          ) : (
            <div className="space-y-6">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  As an administrator, you can review and resolve pending disputes. 
                  Consider all evidence and community input before making a decision.
                </AlertDescription>
              </Alert>

              {pendingDisputes.map((dispute) => (
                <Card key={dispute.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      Dispute Resolution
                      {getDisputeStatusBadge(dispute.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="font-medium mb-2">
                        {disputeReasons.find(r => r.value === dispute.dispute_reason)?.label}
                      </div>
                      <p className="text-sm text-gray-700">{dispute.dispute_details}</p>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="action">Resolution Action</Label>
                        <Select 
                          value={resolution.action} 
                          onValueChange={(value) => setResolution(prev => ({ ...prev, action: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select resolution action..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="accept">Accept Dispute - Reverse Settlement</SelectItem>
                            <SelectItem value="partial">Partial Accept - Modify Outcome</SelectItem>
                            <SelectItem value="reject">Reject Dispute - Maintain Settlement</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="resolution-notes">Resolution Notes</Label>
                        <Textarea
                          id="resolution-notes"
                          placeholder="Explain your decision and any actions taken..."
                          value={resolution.notes}
                          onChange={(e) => setResolution(prev => ({ ...prev, notes: e.target.value }))}
                          rows={4}
                        />
                      </div>

                      <div className="flex justify-end">
                        <Button 
                          onClick={() => handleResolveDispute(dispute.id)}
                          disabled={isResolvingDispute}
                        >
                          {isResolvingDispute ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Resolving...
                            </>
                          ) : (
                            <>
                              <Scale className="h-4 w-4 mr-2" />
                              Resolve Dispute
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}