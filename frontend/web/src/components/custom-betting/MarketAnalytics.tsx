/**
 * Custom Betting Platform - Market Analytics Component
 * ===================================================
 * 
 * Analytics dashboard for market performance and user behavior insights.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Activity,
  Calendar,
  Target,
  Zap,
  PieChart,
  LineChart,
  Clock,
  RefreshCw
} from 'lucide-react';
import { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import customBettingAPI from '@/lib/custom-betting-api';
import { LoadingSpinner } from '@/components/custom-betting';
import { cn } from '@/lib/utils';

interface AnalyticsData {
  marketPerformance: {
    totalVolume: number;
    averageVolume: number;
    totalMarkets: number;
    activeMarkets: number;
    settledMarkets: number;
    averageParticipants: number;
    volumeGrowth: number;
    participantGrowth: number;
  };
  topCategories: Array<{
    category: string;
    marketCount: number;
    totalVolume: number;
    averageVolume: number;
    percentage: number;
  }>;
  recentTrends: Array<{
    date: string;
    newMarkets: number;
    totalVolume: number;
    activeUsers: number;
  }>;
  userEngagement: {
    totalUsers: number;
    activeUsers: number;
    averageBetsPerUser: number;
    averageStakeSize: number;
    retentionRate: number;
  };
  settlementStats: {
    averageSettlementTime: number;
    disputeRate: number;
    accurateSettlements: number;
    totalDisputes: number;
  };
}

interface MarketAnalyticsProps {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  className?: string;
}

export function MarketAnalytics({ timeRange = '30d', className }: MarketAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, [selectedTimeRange]);

  const loadAnalytics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      else setLoading(true);
      
      setError(null);

      // In a real implementation, this would fetch from analytics API
      // For now, we'll simulate with some realistic data
      const mockAnalytics: AnalyticsData = {
        marketPerformance: {
          totalVolume: 125750.50,
          averageVolume: 2515.01,
          totalMarkets: 50,
          activeMarkets: 12,
          settledMarkets: 35,
          averageParticipants: 8.5,
          volumeGrowth: 15.2,
          participantGrowth: 23.1
        },
        topCategories: [
          { category: 'Sports', marketCount: 18, totalVolume: 45200.00, averageVolume: 2511.11, percentage: 35.9 },
          { category: 'Politics', marketCount: 12, totalVolume: 32500.00, averageVolume: 2708.33, percentage: 25.9 },
          { category: 'Crypto', marketCount: 8, totalVolume: 28100.00, averageVolume: 3512.50, percentage: 22.4 },
          { category: 'Entertainment', marketCount: 7, totalVolume: 12200.00, averageVolume: 1742.86, percentage: 9.7 },
          { category: 'Business', marketCount: 3, totalVolume: 5500.00, averageVolume: 1833.33, percentage: 4.4 },
          { category: 'Other', marketCount: 2, totalVolume: 2250.50, averageVolume: 1125.25, percentage: 1.8 }
        ],
        recentTrends: Array.from({ length: 30 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - i);
          return {
            date: date.toISOString().split('T')[0],
            newMarkets: Math.floor(Math.random() * 5),
            totalVolume: Math.random() * 10000,
            activeUsers: Math.floor(Math.random() * 100) + 50
          };
        }).reverse(),
        userEngagement: {
          totalUsers: 1247,
          activeUsers: 342,
          averageBetsPerUser: 3.2,
          averageStakeSize: 85.75,
          retentionRate: 68.5
        },
        settlementStats: {
          averageSettlementTime: 2.3,
          disputeRate: 4.2,
          accurateSettlements: 95.8,
          totalDisputes: 7
        }
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalytics(mockAnalytics);

    } catch (err: any) {
      console.error('Failed to load analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number, showSign = false) => {
    const sign = showSign && value > 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className={className}>
        <LoadingSpinner text="Loading analytics..." />
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-red-600">{error || 'Failed to load analytics'}</p>
        <Button variant="outline" onClick={() => loadAnalytics()}>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            Market Analytics
          </h2>
          <p className="text-gray-600">Platform performance and user insights</p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => loadAnalytics(true)}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              Total Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(analytics.marketPerformance.totalVolume)}
            </div>
            <div className="flex items-center gap-1 text-xs mt-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-green-600">
                {formatPercentage(analytics.marketPerformance.volumeGrowth, true)}
              </span>
              <span className="text-gray-500">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-600" />
              Active Markets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {analytics.marketPerformance.activeMarkets}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              of {analytics.marketPerformance.totalMarkets} total markets
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-600" />
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {analytics.userEngagement.activeUsers.toLocaleString()}
            </div>
            <div className="flex items-center gap-1 text-xs mt-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-green-600">
                {formatPercentage(analytics.marketPerformance.participantGrowth, true)}
              </span>
              <span className="text-gray-500">growth</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4 text-orange-600" />
              Avg Settlement Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {analytics.settlementStats.averageSettlementTime} days
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {formatPercentage(analytics.settlementStats.accurateSettlements)} accuracy
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="categories" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="users">User Engagement</TabsTrigger>
          <TabsTrigger value="settlement">Settlement Stats</TabsTrigger>
        </TabsList>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Market Categories Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analytics.topCategories.map((category) => (
                <div key={category.category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{category.category}</h4>
                      <Badge variant="outline">
                        {category.marketCount} markets
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{formatCurrency(category.totalVolume)}</div>
                      <div className="text-sm text-gray-600">
                        avg: {formatCurrency(category.averageVolume)}
                      </div>
                    </div>
                  </div>
                  
                  <Progress value={category.percentage} className="h-2" />
                  
                  <div className="text-xs text-gray-500">
                    {formatPercentage(category.percentage)} of total volume
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Platform Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-600">Daily Volume</div>
                  <div className="text-2xl font-bold">
                    {formatCurrency(analytics.recentTrends.slice(-1)[0]?.totalVolume || 0)}
                  </div>
                  <div className="text-xs text-gray-500">Latest day</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-600">New Markets</div>
                  <div className="text-2xl font-bold">
                    {analytics.recentTrends.reduce((sum, day) => sum + day.newMarkets, 0)}
                  </div>
                  <div className="text-xs text-gray-500">This period</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-600">Peak Active Users</div>
                  <div className="text-2xl font-bold">
                    {Math.max(...analytics.recentTrends.map(day => day.activeUsers))}
                  </div>
                  <div className="text-xs text-gray-500">Single day record</div>
                </div>
              </div>
              
              {/* Trend visualization would go here */}
              <div className="mt-6 p-8 bg-gray-50 rounded-lg text-center text-gray-500">
                <LineChart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Chart visualization would be displayed here</p>
                <p className="text-xs">Integration with charting library needed</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Engagement Tab */}
        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Users</span>
                  <span className="font-bold">{analytics.userEngagement.totalUsers.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Active Users</span>
                  <span className="font-bold">{analytics.userEngagement.activeUsers.toLocaleString()}</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Retention Rate</span>
                    <span className="font-bold">{formatPercentage(analytics.userEngagement.retentionRate)}</span>
                  </div>
                  <Progress value={analytics.userEngagement.retentionRate} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Engagement Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Avg Bets per User</span>
                  <span className="font-bold">{analytics.userEngagement.averageBetsPerUser}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Avg Stake Size</span>
                  <span className="font-bold">{formatCurrency(analytics.userEngagement.averageStakeSize)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Avg Participants/Market</span>
                  <span className="font-bold">{analytics.marketPerformance.averageParticipants}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settlement Stats Tab */}
        <TabsContent value="settlement" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Settlement Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Average Time</span>
                  <span className="font-bold">{analytics.settlementStats.averageSettlementTime} days</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Settlement Accuracy</span>
                    <span className="font-bold text-green-600">
                      {formatPercentage(analytics.settlementStats.accurateSettlements)}
                    </span>
                  </div>
                  <Progress value={analytics.settlementStats.accurateSettlements} className="h-2" />
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Markets Settled</span>
                  <span className="font-bold">{analytics.marketPerformance.settledMarkets}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Dispute Resolution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Disputes</span>
                  <span className="font-bold">{analytics.settlementStats.totalDisputes}</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Dispute Rate</span>
                    <span className={cn(
                      "font-bold",
                      analytics.settlementStats.disputeRate > 5 ? "text-red-600" : "text-green-600"
                    )}>
                      {formatPercentage(analytics.settlementStats.disputeRate)}
                    </span>
                  </div>
                  <Progress 
                    value={analytics.settlementStats.disputeRate} 
                    className="h-2"
                  />
                </div>
                
                <div className="text-xs text-gray-500">
                  Lower dispute rates indicate better settlement quality
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}