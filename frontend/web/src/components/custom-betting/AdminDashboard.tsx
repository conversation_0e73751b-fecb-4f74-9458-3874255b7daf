/**
 * Custom Betting Platform - Admin Dashboard Component
 * ==================================================
 * 
 * Administrative dashboard for managing betting markets and platform operations.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield,
  BarChart3,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Activity,
  Search,
  Filter,
  Eye,
  Gavel,
  Ban,
  RefreshCw,
  Download
} from 'lucide-react';
import customBettingAPI, { CustomBet, BetParticipant } from '@/lib/custom-betting-api';
import { LoadingSpinner, EmptyState } from '@/components/custom-betting';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface AdminStats {
  totalMarkets: number;
  activeMarkets: number;
  settledMarkets: number;
  pendingSettlement: number;
  totalVolume: number;
  totalUsers: number;
  recentActivity: number;
  platformFees: number;
}

interface AdminDashboardProps {
  isAdmin?: boolean;
}

export function AdminDashboard({ isAdmin = false }: AdminDashboardProps) {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [markets, setMarkets] = useState<CustomBet[]>([]);
  const [filteredMarkets, setFilteredMarkets] = useState<CustomBet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (isAdmin) {
      loadAdminData();
    }
  }, [isAdmin]);

  useEffect(() => {
    filterMarkets();
  }, [markets, searchTerm, statusFilter]);

  const loadAdminData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all markets (admin endpoint)
      const marketsData = await customBettingAPI.getAllBets();
      setMarkets(marketsData);

      // Calculate admin statistics
      calculateStats(marketsData);

    } catch (err: any) {
      console.error('Failed to load admin data:', err);
      setError(err.message || 'Failed to load admin dashboard');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (marketsData: CustomBet[]) => {
    const adminStats: AdminStats = {
      totalMarkets: marketsData.length,
      activeMarkets: marketsData.filter(m => m.status === 'open' || m.status === 'active').length,
      settledMarkets: marketsData.filter(m => m.status === 'settled').length,
      pendingSettlement: marketsData.filter(m => 
        m.status === 'settling' || 
        (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
      ).length,
      totalVolume: marketsData.reduce((sum, m) => sum + m.total_stakes, 0),
      totalUsers: new Set(marketsData.map(m => m.creator_user_id)).size,
      recentActivity: marketsData.filter(m => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return new Date(m.created_at) > oneDayAgo;
      }).length,
      platformFees: marketsData.reduce((sum, m) => sum + (m.total_stakes * 0.02), 0) // Assuming 2% fee
    };

    setStats(adminStats);
  };

  const filterMarkets = () => {
    let filtered = markets;

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(m => 
        m.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      if (statusFilter === 'needs_settlement') {
        filtered = filtered.filter(m => 
          m.status === 'settling' || 
          (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
        );
      } else {
        filtered = filtered.filter(m => m.status === statusFilter);
      }
    }

    setFilteredMarkets(filtered);
  };

  const handleMarketAction = async (marketId: string, action: 'suspend' | 'cancel' | 'force_settle') => {
    try {
      setActionLoading(marketId);
      setError(null);

      switch (action) {
        case 'suspend':
          await customBettingAPI.suspendBet(marketId);
          break;
        case 'cancel':
          await customBettingAPI.cancelBet(marketId);
          break;
        case 'force_settle':
          // This would need additional parameters in a real implementation
          break;
      }

      // Reload data
      await loadAdminData();

    } catch (err: any) {
      setError(err.message || `Failed to ${action} market`);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, { variant: any; className: string }> = {
      'open': { variant: 'default', className: 'bg-blue-100 text-blue-800' },
      'active': { variant: 'default', className: 'bg-green-100 text-green-800' },
      'settling': { variant: 'outline', className: 'bg-yellow-100 text-yellow-800' },
      'settled': { variant: 'outline', className: 'bg-gray-100 text-gray-800' },
      'cancelled': { variant: 'outline', className: 'bg-red-100 text-red-800' },
      'suspended': { variant: 'outline', className: 'bg-orange-100 text-orange-800' }
    };

    const config = variants[status] || variants['open'];
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const exportData = () => {
    // In a real implementation, this would generate a CSV or Excel export
    const csvData = filteredMarkets.map(m => ({
      ID: m.id,
      Title: m.title,
      Status: m.status,
      'Total Stakes': m.total_stakes,
      'Total Participants': m.total_participants,
      'Created At': m.created_at,
      'Deadline': m.deadline
    }));

    console.log('Export data:', csvData);
    // Would implement actual CSV download here
  };

  if (!isAdmin) {
    return (
      <Alert variant="destructive">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Access denied. Admin privileges required to view this dashboard.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return <LoadingSpinner text="Loading admin dashboard..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6 text-primary" />
            Admin Dashboard
          </h1>
          <p className="text-gray-600">Manage betting markets and platform operations</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadAdminData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/analytics">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Link>
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Admin Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                Total Markets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMarkets}</div>
              <p className="text-xs text-gray-600 mt-1">
                {stats.activeMarkets} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                Pending Settlement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats.pendingSettlement}
              </div>
              <p className="text-xs text-gray-600 mt-1">
                Requires attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                Total Volume
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ${stats.totalVolume.toFixed(2)}
              </div>
              <p className="text-xs text-gray-600 mt-1">
                Platform fees: ${stats.platformFees.toFixed(2)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Activity className="h-4 w-4 text-purple-600" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recentActivity}</div>
              <p className="text-xs text-gray-600 mt-1">
                New markets (24h)
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Market Management */}
      <Card>
        <CardHeader>
          <CardTitle>Market Management</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <div className="flex items-center justify-between mb-4">
              <TabsList>
                <TabsTrigger value="all">All Markets</TabsTrigger>
                <TabsTrigger value="pending">Pending Settlement</TabsTrigger>
                <TabsTrigger value="flagged">Flagged Issues</TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search markets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="settling">Settling</SelectItem>
                    <SelectItem value="settled">Settled</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="needs_settlement">Needs Settlement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <TabsContent value="all" className="space-y-4">
              {filteredMarkets.length > 0 ? (
                <div className="space-y-4">
                  {filteredMarkets.map((market) => {
                    const needsSettlement = new Date() > new Date(market.verification_deadline) && market.status !== 'settled';
                    
                    return (
                      <div 
                        key={market.id}
                        className={cn(
                          "flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50",
                          needsSettlement && "border-yellow-300 bg-yellow-50"
                        )}
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{market.title}</h4>
                            {getStatusBadge(market.status)}
                            {needsSettlement && (
                              <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                                Overdue Settlement
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              ${market.total_stakes.toFixed(2)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {market.total_participants} participants
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Deadline: {new Date(market.verification_deadline).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/custom-betting/market/${market.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          
                          {needsSettlement && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/admin/settle/${market.id}`}>
                                <Gavel className="h-4 w-4" />
                              </Link>
                            </Button>
                          )}
                          
                          {market.status === 'open' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleMarketAction(market.id, 'suspend')}
                              disabled={actionLoading === market.id}
                            >
                              <Ban className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <EmptyState
                  title="No markets found"
                  description="No markets match your current filters."
                />
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {/* Markets that need settlement */}
              {filteredMarkets.filter(m => 
                m.status === 'settling' || 
                (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
              ).map((market) => (
                <Card key={market.id} className="border-yellow-300 bg-yellow-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{market.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          Settlement overdue by {Math.ceil((Date.now() - new Date(market.verification_deadline).getTime()) / (1000 * 60 * 60 * 24))} days
                        </p>
                      </div>
                      
                      <Button asChild>
                        <Link href={`/admin/settle/${market.id}`}>
                          <Gavel className="h-4 w-4 mr-2" />
                          Settle Now
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="flagged" className="space-y-4">
              {/* Markets with potential issues */}
              <EmptyState
                title="No flagged issues"
                description="All markets are operating normally."
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}