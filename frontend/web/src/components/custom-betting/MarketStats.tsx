/**
 * Custom Betting Platform - Market Statistics Component
 * ====================================================
 * 
 * Displays platform-wide statistics and metrics.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign,
  Users,
  Activity,
  TrendingUp,
  Target,
  Clock,
  BarChart3,
  Zap
} from 'lucide-react';
import customBettingAPI, { MarketStats as MarketStatsType } from '@/lib/custom-betting-api';
import { LoadingSpinner } from './LoadingSpinner';

export function MarketStats() {
  const [stats, setStats] = useState<MarketStatsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStats = async () => {
      try {
        const data = await customBettingAPI.getMarketStats();
        setStats(data);
      } catch (err) {
        console.error('Failed to load market stats:', err);
        setError('Failed to load statistics');
      } finally {
        setLoading(false);
      }
    };

    loadStats();
    
    // Refresh stats every 30 seconds
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return <LoadingSpinner size="sm" text="Loading statistics..." />;
  }

  if (error || !stats) {
    return null; // Fail silently for stats component
  }

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    }
    if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toLocaleString();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Volume */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Volume</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(stats.platform_statistics.total_volume)}
                </p>
                {stats.recent_activity_24h.recent_volume > 0 && (
                  <Badge variant="outline" className="ml-2 text-green-600 bg-green-50">
                    +{formatCurrency(stats.recent_activity_24h.recent_volume)} 24h
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Markets */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Markets</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-semibold text-gray-900">
                  {formatNumber(stats.platform_statistics.total_markets)}
                </p>
                {stats.recent_activity_24h.new_markets > 0 && (
                  <Badge variant="outline" className="ml-2 text-blue-600 bg-blue-50">
                    +{stats.recent_activity_24h.new_markets} today
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Markets */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg">
              <Zap className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Markets</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-semibold text-gray-900">
                  {formatNumber(stats.platform_statistics.active_markets)}
                </p>
                <div className="ml-2">
                  <Badge variant="outline" className="text-purple-600 bg-purple-50">
                    Live
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Participants */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg">
              <Users className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Participants</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-semibold text-gray-900">
                  {formatNumber(stats.platform_statistics.total_participants)}
                </p>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Avg: {formatCurrency(stats.platform_statistics.average_volume_per_market)}/market
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdown - Spans full width on larger screens */}
      {stats.category_breakdown.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Category Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {stats.category_breakdown.map((category) => (
                <div key={category.category} className="text-center">
                  <div className="bg-gray-50 rounded-lg p-3">
                    <p className="text-sm font-medium text-gray-900 capitalize mb-1">
                      {category.category}
                    </p>
                    <p className="text-lg font-semibold text-primary">
                      {category.market_count}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatCurrency(category.total_volume)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}