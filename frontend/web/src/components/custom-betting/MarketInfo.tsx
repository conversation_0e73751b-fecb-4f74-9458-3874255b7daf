/**
 * Custom Betting Platform - Market Info Component
 * ==============================================
 * 
 * Displays detailed market information including event criteria,
 * settlement details, and market rules.
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Info,
  Calendar,
  Shield,
  Target,
  DollarSign,
  Users,
  Clock,
  AlertTriangle,
  FileText
} from 'lucide-react';
import { CustomBet } from '@/lib/custom-betting-api';

interface MarketInfoProps {
  market: CustomBet;
}

export function MarketInfo({ market }: MarketInfoProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Market Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Event Criteria */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <Target className="h-4 w-4 text-blue-600" />
            Event Criteria
          </h4>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              {market.event_criteria}
            </p>
          </div>
        </div>

        <Separator />

        {/* Key Dates */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <Calendar className="h-4 w-4 text-green-600" />
            Important Dates
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Market Created:</span>
              <span className="font-medium">
                {formatDate(market.created_at)}
              </span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Betting Deadline:</span>
              <span className="font-medium">
                {formatDate(market.deadline)}
              </span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Verification Deadline:</span>
              <span className="font-medium">
                {formatDate(market.verification_deadline)}
              </span>
            </div>
            {market.updated_at !== market.created_at && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">Last Updated:</span>
                <span className="font-medium">
                  {formatDate(market.updated_at)}
                </span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Market Rules */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <Shield className="h-4 w-4 text-purple-600" />
            Market Rules
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Market Type:</span>
              <Badge variant="outline" className="capitalize">
                {market.bet_type.replace('_', ' ')}
              </Badge>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Minimum Stake:</span>
              <span className="font-medium">
                {formatCurrency(market.minimum_stake)}
              </span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Maximum Stake:</span>
              <span className="font-medium">
                {formatCurrency(market.maximum_stake)}
              </span>
            </div>
            {market.participant_limit && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">Participant Limit:</span>
                <span className="font-medium">
                  {market.participant_limit.toLocaleString()}
                </span>
              </div>
            )}
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Public Market:</span>
              <Badge variant={market.is_public ? "default" : "outline"}>
                {market.is_public ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        {/* Current Statistics */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <DollarSign className="h-4 w-4 text-orange-600" />
            Current Statistics
          </h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-xs text-gray-600 mb-1">Total Volume</div>
              <div className="text-lg font-bold">
                {formatCurrency(market.total_stakes)}
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-xs text-gray-600 mb-1">Participants</div>
              <div className="text-lg font-bold">
                {market.total_participants}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Market Status Info */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <Clock className="h-4 w-4 text-indigo-600" />
            Market Status
          </h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Status:</span>
              <Badge 
                variant="outline" 
                className={`
                  ${market.status === 'open' ? 'bg-green-100 text-green-800' : ''}
                  ${market.status === 'active' ? 'bg-blue-100 text-blue-800' : ''}
                  ${market.status === 'settling' ? 'bg-yellow-100 text-yellow-800' : ''}
                  ${market.status === 'settled' ? 'bg-gray-100 text-gray-800' : ''}
                  ${market.status === 'cancelled' ? 'bg-red-100 text-red-800' : ''}
                  ${market.status === 'expired' ? 'bg-gray-100 text-gray-600' : ''}
                `}
              >
                {market.status.charAt(0).toUpperCase() + market.status.slice(1)}
              </Badge>
            </div>

            {/* Status-specific information */}
            {market.status === 'open' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-green-800 text-sm">
                  <Target className="h-4 w-4" />
                  <span className="font-medium">Market is accepting bets</span>
                </div>
                <p className="text-xs text-green-700 mt-1">
                  Participants can place bets until the deadline.
                </p>
              </div>
            )}

            {market.status === 'settling' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-yellow-800 text-sm">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Settlement in progress</span>
                </div>
                <p className="text-xs text-yellow-700 mt-1">
                  Outcome verification and settlement processing.
                </p>
              </div>
            )}

            {market.status === 'settled' && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-gray-800 text-sm">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Market settled</span>
                </div>
                <p className="text-xs text-gray-700 mt-1">
                  Final outcome determined and payouts distributed.
                </p>
              </div>
            )}

            {market.status === 'cancelled' && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-red-800 text-sm">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-medium">Market cancelled</span>
                </div>
                <p className="text-xs text-red-700 mt-1">
                  Bets have been refunded to participants.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Creator Information */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-3">
            <Users className="h-4 w-4 text-gray-600" />
            Creator Information
          </h4>
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Created by:</span>
              <span className="font-medium font-mono">
                {market.creator_user_id.slice(0, 8)}...
              </span>
            </div>
          </div>
        </div>

        {/* Additional Notes */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <FileText className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm">
              <div className="font-medium text-blue-800 mb-1">Important Notes</div>
              <ul className="text-blue-700 space-y-1 text-xs">
                <li>• All bets are final once placed and matched</li>
                <li>• Settlement is based on the event criteria specified</li>
                <li>• Disputes can be filed during the verification period</li>
                <li>• Platform fees may apply to winnings</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}