/**
 * BetBet Gaming Engine - Share Button Component
 * ===========================================
 * 
 * Reusable share button component with social media integration.
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  Share2,
  Copy,
  CheckCircle,
  Twitter,
  Facebook,
  MessageCircle,
  Mail,
  Link,
  ExternalLink
} from 'lucide-react';

interface ShareButtonProps {
  url: string;
  title: string;
  description?: string;
  hashtags?: string[];
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showLabel?: boolean;
}

export function ShareButton({ 
  url, 
  title, 
  description, 
  hashtags = [],
  variant = 'outline',
  size = 'default',
  showLabel = true
}: ShareButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleTwitterShare = () => {
    const twitterUrl = new URL('https://twitter.com/intent/tweet');
    twitterUrl.searchParams.set('url', url);
    twitterUrl.searchParams.set('text', title);
    if (hashtags.length > 0) {
      twitterUrl.searchParams.set('hashtags', hashtags.join(','));
    }
    window.open(twitterUrl.toString(), '_blank', 'width=600,height=400');
  };

  const handleFacebookShare = () => {
    const facebookUrl = new URL('https://www.facebook.com/sharer/sharer.php');
    facebookUrl.searchParams.set('u', url);
    facebookUrl.searchParams.set('quote', title);
    window.open(facebookUrl.toString(), '_blank', 'width=600,height=400');
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = new URL('https://wa.me/');
    const message = `${title}\n${description ? description + '\n' : ''}${url}`;
    whatsappUrl.searchParams.set('text', message);
    window.open(whatsappUrl.toString(), '_blank');
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent(title);
    const body = encodeURIComponent(
      `${title}\n\n${description ? description + '\n\n' : ''}Check it out: ${url}`
    );
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          {showLabel && <span>Share</span>}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={handleCopyLink}>
          {copied ? (
            <>
              <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
              <span className="text-green-600">Copied!</span>
            </>
          ) : (
            <>
              <Copy className="h-4 w-4 mr-2" />
              <span>Copy Link</span>
            </>
          )}
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={handleTwitterShare}>
          <Twitter className="h-4 w-4 mr-2 text-blue-500" />
          <span>Share on Twitter</span>
          <ExternalLink className="h-3 w-3 ml-auto" />
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleFacebookShare}>
          <Facebook className="h-4 w-4 mr-2 text-blue-600" />
          <span>Share on Facebook</span>
          <ExternalLink className="h-3 w-3 ml-auto" />
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleWhatsAppShare}>
          <MessageCircle className="h-4 w-4 mr-2 text-green-500" />
          <span>Share on WhatsApp</span>
          <ExternalLink className="h-3 w-3 ml-auto" />
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleEmailShare}>
          <Mail className="h-4 w-4 mr-2 text-gray-600" />
          <span>Share via Email</span>
          <ExternalLink className="h-3 w-3 ml-auto" />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}