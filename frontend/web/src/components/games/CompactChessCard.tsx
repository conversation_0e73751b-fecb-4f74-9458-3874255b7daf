/**
 * BetBet Compact Chess Card Component
 * ==================================
 * 
 * Minimal chess card following GamesArena pattern for general use.
 */

import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  Crown,
  ChevronRight,
  TrendingUp
} from 'lucide-react';

interface CompactChessCardProps {
  onSelect?: (slug: string) => void;
  trending?: boolean;
}

export function CompactChessCard({ onSelect, trending = true }: CompactChessCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    if (onSelect) {
      onSelect('chess');
    } else {
      router.push('/chess');
    }
  };

  const chessData = {
    difficulty: 'hard',
    duration: '15-45',
    playerMode: '1v1',
    activeSessions: 8,
    totalPlayers: 24,
    spectators: 45,
    stakes: 'medium'
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'hard': return 'text-red-400 bg-red-500/20';
      default: return 'text-slate-400 bg-slate-500/20';
    }
  };

  const getStakesDisplay = (stakes: string) => {
    switch (stakes) {
      case 'medium': return '$25-75';
      default: return 'Free';
    }
  };

  return (
    <div 
      className="bg-slate-800/90 backdrop-blur-lg border border-slate-700 rounded-xl p-6 hover:border-purple-500/50 transition-all cursor-pointer group relative"
      onClick={handleCardClick}
    >
      {/* Trending Badge */}
      {trending && (
        <div className="absolute top-4 right-4 flex items-center space-x-1 bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded-full text-xs">
          <TrendingUp className="h-3 w-3" />
          <span>Featured</span>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-3xl">♟️</div>
          <div>
            <h3 className="text-xl font-bold text-white group-hover:text-purple-300 transition-colors">
              Chess
            </h3>
            <p className="text-slate-400 text-sm">Strategic board game</p>
          </div>
        </div>
        <ChevronRight className="h-5 w-5 text-slate-400 group-hover:text-purple-400 transition-colors" />
      </div>

      {/* Game Metadata */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(chessData.difficulty)}`}>
          {chessData.difficulty.charAt(0).toUpperCase() + chessData.difficulty.slice(1)}
        </div>
        <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-slate-300">
          {chessData.duration} min
        </div>
        <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-slate-300">
          {chessData.playerMode}
        </div>
        <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-green-400">
          {getStakesDisplay(chessData.stakes)}
        </div>
      </div>

      {/* Live Activity Stats */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        <div className="bg-slate-700/50 rounded-lg p-2 text-center">
          <div className="text-sm font-bold text-green-400">{chessData.activeSessions}</div>
          <div className="text-xs text-slate-400">Live</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-2 text-center">
          <div className="text-sm font-bold text-blue-400">{chessData.totalPlayers}</div>
          <div className="text-xs text-slate-400">Players</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-2 text-center">
          <div className="text-sm font-bold text-purple-400">{chessData.spectators}</div>
          <div className="text-xs text-slate-400">Watching</div>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`h-2 w-2 rounded-full ${chessData.activeSessions > 0 ? 'bg-green-500 animate-pulse' : 'bg-slate-500'}`}></div>
          <span className="text-xs text-slate-300">
            {chessData.activeSessions > 0 ? 'Active now' : 'Available'}
          </span>
        </div>
        <div className="text-xs text-slate-500 uppercase tracking-wide">
          Strategy
        </div>
      </div>
    </div>
  );
}

CompactChessCard.displayName = 'CompactChessCard';