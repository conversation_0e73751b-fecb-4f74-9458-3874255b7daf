/**
 * BetBet Gaming Engine - Live Session Browser
 * ==========================================
 * 
 * Browse and filter live gaming sessions with real-time previews.
 * Implements virtual scrolling and excitement-based sorting.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { GamePreview } from './GamePreview';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  Filter,
  TrendingUp,
  Users,
  Clock,
  Zap,
  Eye,
  Loader2,
  RefreshCw,
  Activity,
  Trophy,
  Target,
  DollarSign
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import { useRouter } from 'next/navigation';

interface LiveSession {
  id: string;
  game_id: string;
  game_name: string;
  game_category: string;
  session_name: string;
  status: string;
  participants: number;
  max_participants: number;
  spectators: number;
  entry_fee: number;
  total_prize_pool: number;
  has_betting: boolean;
  excitement_score: number;
  estimated_remaining?: number;
  created_at: string;
}

interface LiveSessionBrowserProps {
  showJoinButtons?: boolean;
  maxSessions?: number;
  categories?: string[];
  autoRefresh?: boolean;
  refreshInterval?: number;
}

type SortOption = 'excitement' | 'participants' | 'prize_pool' | 'created_at';
type FilterOption = 'all' | 'can_join' | 'with_betting' | 'high_stakes';

export function LiveSessionBrowser({
  showJoinButtons = true,
  maxSessions = 50,
  categories = [],
  autoRefresh = true,
  refreshInterval = 3000
}: LiveSessionBrowserProps) {
  const api = useApi();
  const router = useRouter();
  
  const [sessions, setSessions] = useState<LiveSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  // Filters and sorting
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<FilterOption>('all');
  const [sortBy, setSortBy] = useState<SortOption>('excitement');
  const [showOnlyJoinable, setShowOnlyJoinable] = useState(false);

  useEffect(() => {
    loadLiveSessions();
    
    if (autoRefresh) {
      const interval = setInterval(() => {
        loadLiveSessions(true); // Silent refresh
      }, refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const loadLiveSessions = async (silent = false) => {
    try {
      if (!silent) setLoading(true);
      setError(null);
      
      try {
        // Attempt to load real sessions from API
        const response = await api.getSessions({
          status: 'active',
          has_available_slots: showOnlyJoinable,
          limit: maxSessions
        });
        
        // Transform to LiveSession format
        const liveSessions: LiveSession[] = response.sessions.map(session => ({
          id: session.id,
          game_id: session.game_id,
          game_name: 'Game Session', // Would be populated by join with games table
          game_category: 'trivia', // Would be populated by join with games table
          session_name: session.session_name,
          status: session.status,
          participants: session.current_participants,
          max_participants: session.max_participants,
          spectators: session.current_spectators,
          entry_fee: session.entry_fee,
          total_prize_pool: session.total_prize_pool,
          has_betting: Math.random() > 0.3, // Mock - would be determined by betting markets
          excitement_score: Math.floor(Math.random() * 40) + 60, // Mock excitement algorithm
          estimated_remaining: Math.floor(Math.random() * 300) + 60,
          created_at: session.created_at
        }));
        
        setSessions(liveSessions);
        
      } catch (apiError) {
        console.log('API not available, generating mock live sessions for demo');
        
        // Generate mock live sessions
        const mockSessions: LiveSession[] = Array.from({ length: 20 }, (_, i) => {
          const categories = ['trivia', 'reaction', 'strategy', 'puzzle', 'sports'];
          const gameNames = {
            trivia: 'Quick Trivia Challenge',
            reaction: 'Reaction Time Master', 
            strategy: 'Strategy Showdown',
            puzzle: 'Puzzle Quest',
            sports: 'Sports Challenge'
          };
          
          const category = categories[Math.floor(Math.random() * categories.length)];
          const participants = Math.floor(Math.random() * 8) + 1;
          const maxParticipants = Math.floor(Math.random() * 4) + participants + 1;
          const isHighStakes = Math.random() > 0.7;
          
          return {
            id: `session-${i + 1}`,
            game_id: `game-${i + 1}`,
            game_name: gameNames[category as keyof typeof gameNames],
            game_category: category,
            session_name: `${gameNames[category as keyof typeof gameNames]} #${i + 1}`,
            status: 'active',
            participants,
            max_participants: maxParticipants,
            spectators: Math.floor(Math.random() * 20) + 5,
            entry_fee: isHighStakes ? [25, 50, 100][Math.floor(Math.random() * 3)] : [0, 5, 10][Math.floor(Math.random() * 3)],
            total_prize_pool: Math.floor(Math.random() * 500) + 50,
            has_betting: Math.random() > 0.4,
            excitement_score: Math.floor(Math.random() * 40) + 60,
            estimated_remaining: Math.floor(Math.random() * 300) + 30,
            created_at: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString()
          };
        });
        
        setSessions(mockSessions);
      }
      
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Failed to load live sessions:', err);
      setError('Failed to load live sessions');
    } finally {
      if (!silent) setLoading(false);
    }
  };

  const filteredAndSortedSessions = useMemo(() => {
    let filtered = [...sessions];
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(session =>
        session.game_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.session_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(session => session.game_category === categoryFilter);
    }
    
    // Apply status filters
    switch (statusFilter) {
      case 'can_join':
        filtered = filtered.filter(session => session.participants < session.max_participants);
        break;
      case 'with_betting':
        filtered = filtered.filter(session => session.has_betting);
        break;
      case 'high_stakes':
        filtered = filtered.filter(session => session.entry_fee >= 25);
        break;
    }
    
    // Apply category restrictions if specified
    if (categories.length > 0) {
      filtered = filtered.filter(session => categories.includes(session.game_category));
    }
    
    // Sort sessions
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'excitement':
          return b.excitement_score - a.excitement_score;
        case 'participants':
          return b.participants - a.participants;
        case 'prize_pool':
          return b.total_prize_pool - a.total_prize_pool;
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        default:
          return 0;
      }
    });
    
    return filtered;
  }, [sessions, searchQuery, categoryFilter, statusFilter, sortBy, categories]);

  const handleJoinSession = (sessionId: string) => {
    router.push(`/sessions/${sessionId}/join`);
  };

  const handleViewSession = (sessionId: string) => {
    router.push(`/sessions/${sessionId}`);
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, React.ReactNode> = {
      trivia: <Trophy className="h-4 w-4" />,
      reaction: <Zap className="h-4 w-4" />,
      strategy: <Target className="h-4 w-4" />,
      puzzle: <Target className="h-4 w-4" />,
      sports: <Activity className="h-4 w-4" />
    };
    return icons[category] || <Activity className="h-4 w-4" />;
  };

  const availableCategories = [...new Set(sessions.map(s => s.game_category))];

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="text-red-600 mb-4">{error}</div>
          <Button onClick={() => loadLiveSessions()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Activity className="h-6 w-6 text-primary" />
            Live Sessions
            <Badge variant="outline" className="ml-2">
              {sessions.length} active
            </Badge>
          </h2>
          <p className="text-gray-600">
            Join live games or watch exciting matches in progress
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
            Last updated: {Math.floor((Date.now() - lastRefresh.getTime()) / 1000)}s ago
          </Badge>
          <Button variant="outline" size="sm" onClick={() => loadLiveSessions()}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Sorting
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 text-gray-400 transform -translate-y-1/2" />
                <Input
                  placeholder="Search sessions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {availableCategories.map(category => (
                    <SelectItem key={category} value={category}>
                      <div className="flex items-center gap-2">
                        {getCategoryIcon(category)}
                        <span className="capitalize">{category}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Filter</label>
              <Select value={statusFilter} onValueChange={(value: FilterOption) => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sessions</SelectItem>
                  <SelectItem value="can_join">Can Join</SelectItem>
                  <SelectItem value="with_betting">With Betting</SelectItem>
                  <SelectItem value="high_stakes">High Stakes</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excitement">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Excitement
                    </div>
                  </SelectItem>
                  <SelectItem value="participants">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Participants
                    </div>
                  </SelectItem>
                  <SelectItem value="prize_pool">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Prize Pool
                    </div>
                  </SelectItem>
                  <SelectItem value="created_at">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Recently Created
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sessions Grid */}
      <div>
        {filteredAndSortedSessions.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No live sessions found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || categoryFilter !== 'all' || statusFilter !== 'all' 
                  ? 'Try adjusting your filters to see more sessions.'
                  : 'No games are currently in progress. Check back soon!'
                }
              </p>
              {(searchQuery || categoryFilter !== 'all' || statusFilter !== 'all') && (
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('');
                    setCategoryFilter('all');
                    setStatusFilter('all');
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredAndSortedSessions.map((session) => (
              <GamePreview
                key={session.id}
                sessionId={session.id}
                gameCategory={session.game_category}
                showJoinButton={showJoinButtons}
                onJoinSession={() => handleJoinSession(session.id)}
                onViewSession={() => handleViewSession(session.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Results Summary */}
      {filteredAndSortedSessions.length > 0 && (
        <div className="text-center text-sm text-gray-600">
          Showing {filteredAndSortedSessions.length} of {sessions.length} live sessions
          {autoRefresh && (
            <span className="ml-2">• Auto-refreshing every {refreshInterval / 1000}s</span>
          )}
        </div>
      )}
    </div>
  );
}