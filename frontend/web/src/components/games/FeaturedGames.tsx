/**
 * BetBet Gaming Engine - Featured Games Component
 * ==============================================
 * 
 * Displays featured games in a carousel or grid layout.
 */

import React, { useEffect, useState } from 'react';
import { useApi, useMultiServiceApi } from '@/hooks/useApi';
import { GameCard } from './GameCard';
import { CompactChessCard } from './CompactChessCard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  ChevronLeft, 
  ChevronRight, 
  Loader2,
  AlertCircle,
  Sparkles
} from 'lucide-react';

interface Game {
  id: string;
  name: string;
  slug: string;
  category: string;
  description?: string;
  min_players: number;
  max_players: number;
  estimated_duration_minutes: number;
  scoring_system: string;
  has_spectator_betting: boolean;
  is_featured: boolean;
  is_active: boolean;
  current_sessions?: number;
  total_plays?: number;
}

interface FeaturedGamesProps {
  limit?: number;
  showTitle?: boolean;
  layout?: 'carousel' | 'grid';
}

export function FeaturedGames({ 
  limit = 6, 
  showTitle = true,
  layout = 'grid' 
}: FeaturedGamesProps) {
  const api = useApi();
  const multiApi = useMultiServiceApi();
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    loadFeaturedGames();
  }, [limit]);

  const loadFeaturedGames = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try multi-service API first
      try {
        const response = await multiApi.gaming.getGames({ is_featured: true, limit });
        setGames(response.data);
      } catch (multiErr) {
        console.log('Multi-service API failed, trying fallback');
        // Fallback to original API
        const response = await api.getFeaturedGames(limit);
        setGames(response);
      }
    } catch (err) {
      console.error('Failed to load featured games:', err);
      setError('Failed to load featured games');
    } finally {
      setLoading(false);
    }
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? games.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === games.length - 1 ? 0 : prev + 1));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadFeaturedGames}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (games.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No featured games available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-yellow-500" />
            Featured Games
          </h2>
          {layout === 'carousel' && games.length > 1 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handlePrevious}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleNext}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      )}

      {layout === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {games.map((game) => 
            game.slug === 'chess' || game.name.toLowerCase().includes('chess') ? (
              <CompactChessCard key={game.id} trending={game.is_featured} />
            ) : (
              <GameCard key={game.id} game={game} />
            )
          )}
        </div>
      ) : (
        <div className="relative overflow-hidden">
          <div 
            className="flex transition-transform duration-300 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {games.map((game) => (
              <div key={game.id} className="w-full flex-shrink-0 px-2">
                {game.slug === 'chess' || game.name.toLowerCase().includes('chess') ? (
                  <CompactChessCard trending={game.is_featured} />
                ) : (
                  <GameCard game={game} />
                )}
              </div>
            ))}
          </div>
          
          {games.length > 1 && (
            <div className="flex justify-center mt-4 space-x-2">
              {games.map((_, index) => (
                <button
                  key={index}
                  className={`h-2 w-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                  onClick={() => setCurrentIndex(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}