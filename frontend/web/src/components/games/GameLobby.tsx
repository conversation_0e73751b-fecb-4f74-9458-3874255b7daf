/**
 * BetBet Gaming Engine - Game Lobby Component
 * ==========================================
 * 
 * Main game lobby interface with filtering and search.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useApi } from '@/hooks/useApi';
import { GameCard } from './GameCard';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Search, 
  Filter, 
  Loader2, 
  AlertCircle,
  GamepadIcon,
  X
} from 'lucide-react';

interface Game {
  id: string;
  name: string;
  slug: string;
  category: string;
  description?: string;
  min_players: number;
  max_players: number;
  estimated_duration_minutes: number;
  scoring_system: string;
  has_spectator_betting: boolean;
  is_featured: boolean;
  is_active: boolean;
  current_sessions?: number;
  total_plays?: number;
}

interface GameLobbyProps {
  showFeatured?: boolean;
  initialCategory?: string;
}

const categories = [
  { value: 'all', label: 'All Games', color: 'bg-gray-500' },
  { value: 'trivia', label: 'Trivia', color: 'bg-blue-500' },
  { value: 'reaction', label: 'Reaction', color: 'bg-orange-500' },
  { value: 'strategy', label: 'Strategy', color: 'bg-purple-500' },
  { value: 'puzzle', label: 'Puzzle', color: 'bg-green-500' },
];

export function GameLobby({ showFeatured = true, initialCategory = 'all' }: GameLobbyProps) {
  const api = useApi();
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [showBettingOnly, setShowBettingOnly] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const loadGames = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {
        page: reset ? 1 : page,
        limit: 12,
        is_active: true,
        search: searchTerm || undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
      };

      if (showBettingOnly) {
        params.has_spectator_betting = true;
      }

      const response = await api.getGames(params);
      
      if (reset) {
        setGames(response);
        setPage(1);
      } else {
        setGames(prev => [...prev, ...response]);
      }
      
      setHasMore(response.length === 12);
    } catch (err) {
      console.error('Failed to load games:', err);
      setError('Failed to load games');
    } finally {
      setLoading(false);
    }
  }, [api, page, searchTerm, selectedCategory, showBettingOnly]);

  useEffect(() => {
    loadGames(true);
  }, [searchTerm, selectedCategory, showBettingOnly]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadGames(true);
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
    loadGames(false);
  };

  const filteredGames = games.filter(game => {
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      return (
        game.name.toLowerCase().includes(search) ||
        game.description?.toLowerCase().includes(search) ||
        game.category.toLowerCase().includes(search)
      );
    }
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Search games..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit">Search</Button>
        </form>

        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm font-medium">Category:</span>
          {categories.map((cat) => (
            <Badge
              key={cat.value}
              variant={selectedCategory === cat.value ? 'default' : 'outline'}
              className={`cursor-pointer transition-colors ${
                selectedCategory === cat.value ? cat.color + ' text-white' : ''
              }`}
              onClick={() => setSelectedCategory(cat.value)}
            >
              {cat.label}
            </Badge>
          ))}
          
          <div className="ml-auto flex items-center gap-2">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={showBettingOnly}
                onChange={(e) => setShowBettingOnly(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">Betting games only</span>
            </label>
          </div>
        </div>

        {(searchTerm || selectedCategory !== 'all' || showBettingOnly) && (
          <div className="flex items-center gap-2 text-sm">
            <span>Active filters:</span>
            {searchTerm && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: {searchTerm}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => setSearchTerm('')}
                />
              </Badge>
            )}
            {selectedCategory !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {categories.find(c => c.value === selectedCategory)?.label}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => setSelectedCategory('all')}
                />
              </Badge>
            )}
            {showBettingOnly && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Betting only
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => setShowBettingOnly(false)}
                />
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Results */}
      {loading && games.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center space-y-2">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
              <p className="text-red-800">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => loadGames(true)}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : filteredGames.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <GamepadIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No games found</p>
            {(searchTerm || selectedCategory !== 'all' || showBettingOnly) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setShowBettingOnly(false);
                }}
                className="mt-4"
              >
                Clear Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredGames.map((game) => (
              <GameCard key={game.id} game={game} />
            ))}
          </div>

          {hasMore && !loading && (
            <div className="text-center pt-4">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  'Load More Games'
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}