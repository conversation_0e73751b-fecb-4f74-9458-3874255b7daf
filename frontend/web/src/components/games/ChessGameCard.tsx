/**
 * BetBet Chess Game Card Component
 * ===============================
 * 
 * Compact chess game card matching GamesArena design pattern.
 */

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Users, 
  Clock, 
  Crown,
  ChevronRight,
  Info,
  TrendingUp,
  Target,
  Trophy,
  Eye
} from 'lucide-react';
import { Game } from '@/lib/api';

interface ChessGameCardProps {
  game: Game;
  onSelect?: (slug: string) => void;
  showStats?: boolean;
}

export function ChessGameCard({ game, onSelect, showStats = true }: ChessGameCardProps) {
  const router = useRouter();
  const [showDetails, setShowDetails] = useState(false);

  const handleCardClick = () => {
    if (onSelect) {
      onSelect(game.slug);
    } else {
      router.push(`/games/${game.slug}`);
    }
  };

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDetails(!showDetails);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20';
      case 'hard': return 'text-red-400 bg-red-500/20';
      default: return 'text-slate-400 bg-slate-500/20';
    }
  };

  const getStakesDisplay = (stakes: string) => {
    switch (stakes) {
      case 'low': return '$1-25';
      case 'medium': return '$25-75';
      case 'high': return '$75+';
      default: return 'Free';
    }
  };

  // Mock chess-specific data
  const chessData = {
    difficulty: 'hard',
    duration: '15-45',
    playerMode: '1v1',
    activeSessions: game.current_sessions || 8,
    totalPlayers: game.total_plays ? Math.min(game.total_plays, 999) : 24,
    spectators: 45,
    stakes: 'medium',
    trending: game.is_featured || false
  };

  return (
    <>
      <div 
        className="bg-slate-800/90 backdrop-blur-lg border border-slate-700 rounded-xl p-6 hover:border-purple-500/50 transition-all cursor-pointer group relative"
        onClick={handleCardClick}
      >
        {/* Trending Badge */}
        {chessData.trending && (
          <div className="absolute top-4 right-4 flex items-center space-x-1 bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded-full text-xs">
            <TrendingUp className="h-3 w-3" />
            <span>Featured</span>
          </div>
        )}

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="text-3xl">♟️</div>
            <div>
              <h3 className="text-xl font-bold text-white group-hover:text-purple-300 transition-colors">
                Chess
              </h3>
              <p className="text-slate-400 text-sm">Strategic board game</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleInfoClick}
              className="p-1 text-slate-400 hover:text-purple-400 transition-colors"
              title="View chess details"
            >
              <Info className="h-4 w-4" />
            </button>
            <ChevronRight className="h-5 w-5 text-slate-400 group-hover:text-purple-400 transition-colors" />
          </div>
        </div>

        {/* Game Metadata */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(chessData.difficulty)}`}>
            {chessData.difficulty.charAt(0).toUpperCase() + chessData.difficulty.slice(1)}
          </div>
          <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-slate-300">
            {chessData.duration} min
          </div>
          <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-slate-300">
            {chessData.playerMode}
          </div>
          <div className="bg-slate-700/50 px-2 py-1 rounded text-xs text-green-400">
            {getStakesDisplay(chessData.stakes)}
          </div>
        </div>

        {/* Live Activity Stats */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="bg-slate-700/50 rounded-lg p-2 text-center">
            <div className="text-sm font-bold text-green-400">{chessData.activeSessions}</div>
            <div className="text-xs text-slate-400">Live</div>
          </div>
          <div className="bg-slate-700/50 rounded-lg p-2 text-center">
            <div className="text-sm font-bold text-blue-400">{chessData.totalPlayers}</div>
            <div className="text-xs text-slate-400">Players</div>
          </div>
          <div className="bg-slate-700/50 rounded-lg p-2 text-center">
            <div className="text-sm font-bold text-purple-400">{chessData.spectators}</div>
            <div className="text-xs text-slate-400">Watching</div>
          </div>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`h-2 w-2 rounded-full ${chessData.activeSessions > 0 ? 'bg-green-500 animate-pulse' : 'bg-slate-500'}`}></div>
            <span className="text-xs text-slate-300">
              {chessData.activeSessions > 0 ? 'Active now' : 'Available'}
            </span>
          </div>
          <div className="text-xs text-slate-500 uppercase tracking-wide">
            Strategy
          </div>
        </div>
      </div>

      {/* Chess Details Popup */}
      {showDetails && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm" onClick={() => setShowDetails(false)}>
          <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Crown className="h-6 w-6 text-yellow-500" />
                <h3 className="text-xl font-bold text-white">Chess Details</h3>
              </div>
              <button
                onClick={() => setShowDetails(false)}
                className="text-slate-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>

            {/* Chess Board Preview */}
            <div className="mb-4 flex justify-center">
              <div className="grid grid-cols-8 gap-0 w-32 h-32 border-2 border-amber-600 rounded">
                {Array.from({ length: 64 }, (_, i) => {
                  const row = Math.floor(i / 8);
                  const col = i % 8;
                  const isLight = (row + col) % 2 === 0;
                  const pieceMap: Record<number, string> = {
                    0: '♜', 1: '♞', 2: '♝', 3: '♛', 4: '♚', 5: '♝', 6: '♞', 7: '♜',
                    8: '♟', 9: '♟', 10: '♟', 11: '♟', 12: '♟', 13: '♟', 14: '♟', 15: '♟',
                    48: '♙', 49: '♙', 50: '♙', 51: '♙', 52: '♙', 53: '♙', 54: '♙', 55: '♙',
                    56: '♖', 57: '♘', 58: '♗', 59: '♕', 60: '♔', 61: '♗', 62: '♘', 63: '♖'
                  };
                  
                  return (
                    <div
                      key={i}
                      className={`w-4 h-4 flex items-center justify-center text-xs ${
                        isLight ? 'bg-amber-100' : 'bg-amber-600'
                      }`}
                    >
                      {pieceMap[i] || ''}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Chess Features */}
            <div className="space-y-3">
              <h4 className="font-semibold text-white mb-2">Chess Features</h4>
              <div className="grid grid-cols-2 gap-2 text-sm text-slate-300">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-purple-400" />
                  <span>Multiple variants</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-400" />
                  <span>Time controls</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Trophy className="h-4 w-4 text-yellow-400" />
                  <span>Tournaments</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-green-400" />
                  <span>Spectator betting</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-indigo-400" />
                  <span>Real-time P2P</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Crown className="h-4 w-4 text-purple-400" />
                  <span>Rating system</span>
                </div>
              </div>
            </div>

            <div className="mt-6 flex gap-3">
              <button 
                onClick={() => {
                  setShowDetails(false);
                  router.push(`/games/${game.slug}`);
                }}
                className="flex-1 bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Browse Games
              </button>
              <button 
                onClick={() => {
                  setShowDetails(false);
                  router.push(`/games/${game.slug}`);
                }}
                className="flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Play Now
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

ChessGameCard.displayName = 'ChessGameCard';