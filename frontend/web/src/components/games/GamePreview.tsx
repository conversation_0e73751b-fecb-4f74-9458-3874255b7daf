/**
 * BetBet Gaming Engine - Real-time Game Preview Component
 * =====================================================
 * 
 * Displays lightweight real-time previews of live game sessions
 * for users browsing active sessions.
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Eye,
  Users,
  Clock,
  Zap,
  Play,
  DollarSign,
  TrendingUp,
  Activity,
  Heart,
  Trophy,
  Target
} from 'lucide-react';
import { useWebSocket } from '@/hooks/useWebSocket';

interface GamePreviewData {
  session_id: string;
  game_name: string;
  game_category: string;
  current_state: 'waiting' | 'starting' | 'active' | 'paused' | 'completed';
  participants: number;
  max_participants: number;
  spectators: number;
  elapsed_time?: number;
  estimated_remaining?: number;
  current_leader?: {
    username: string;
    score: number;
  };
  recent_action?: string;
  excitement_score: number; // 0-100 for sorting interesting games
  entry_fee: number;
  total_prize_pool: number;
  has_betting: boolean;
  preview_data?: any; // Game-specific preview data
}

interface GamePreviewProps {
  sessionId: string;
  gameCategory: string;
  showJoinButton?: boolean;
  onJoinSession?: () => void;
  onViewSession?: () => void;
  className?: string;
}

export function GamePreview({ 
  sessionId, 
  gameCategory,
  showJoinButton = true,
  onJoinSession,
  onViewSession,
  className = ""
}: GamePreviewProps) {
  const [previewData, setPreviewData] = useState<GamePreviewData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // WebSocket connection for real-time updates
  const { isConnected, lastMessage } = useWebSocket(sessionId, {
    onMessage: (message) => {
      if (message.type === 'game_state_update' && message.session_id === sessionId) {
        setPreviewData(message.data);
        setLastUpdate(new Date());
      } else if (message.type === 'participant_update' && message.session_id === sessionId) {
        setPreviewData(prev => prev ? {
          ...prev,
          participants: message.data.participants,
          spectators: message.data.spectators
        } : null);
        setLastUpdate(new Date());
      }
    }
  });

  useEffect(() => {
    // Load initial preview data
    loadPreviewData();
  }, [sessionId]);

  const loadPreviewData = async () => {
    try {
      setIsLoading(true);
      // Mock initial data - would be replaced with API call
      const mockData: GamePreviewData = {
        session_id: sessionId,
        game_name: getGameNameForCategory(gameCategory),
        game_category: gameCategory,
        current_state: 'active',
        participants: Math.floor(Math.random() * 6) + 2,
        max_participants: 8,
        spectators: Math.floor(Math.random() * 15) + 5,
        elapsed_time: Math.floor(Math.random() * 300) + 60, // 1-5 minutes
        estimated_remaining: Math.floor(Math.random() * 180) + 30, // 30s-3min remaining
        current_leader: {
          username: `Player${Math.floor(Math.random() * 100)}`,
          score: Math.floor(Math.random() * 1000) + 100
        },
        recent_action: getRecentActionForCategory(gameCategory),
        excitement_score: Math.floor(Math.random() * 40) + 60, // 60-100 for active games
        entry_fee: [0, 5, 10, 25][Math.floor(Math.random() * 4)],
        total_prize_pool: Math.floor(Math.random() * 500) + 100,
        has_betting: Math.random() > 0.3,
        preview_data: getPreviewDataForCategory(gameCategory)
      };
      
      setPreviewData(mockData);
    } catch (error) {
      console.error('Failed to load preview data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getGameNameForCategory = (category: string): string => {
    const names: Record<string, string> = {
      trivia: 'Quick Trivia Challenge',
      reaction: 'Reaction Time Master',
      strategy: 'Strategy Showdown',
      puzzle: 'Puzzle Quest',
      sports: 'Sports Challenge'
    };
    return names[category] || 'Game Session';
  };

  const getRecentActionForCategory = (category: string): string => {
    const actions: Record<string, string[]> = {
      trivia: ['Correct answer!', 'Wrong answer!', 'Time\'s up!', 'Bonus round!'],
      reaction: ['Perfect timing!', 'Too slow!', 'New record!', 'Close call!'],
      strategy: ['Bold move!', 'Counter attack!', 'Strategic play!', 'Unexpected turn!'],
      puzzle: ['Puzzle solved!', 'New clue found!', 'Time pressure!', 'Creative solution!'],
      sports: ['Goal scored!', 'Amazing play!', 'Close call!', 'Upset victory!']
    };
    const categoryActions = actions[category] || ['Game action!'];
    return categoryActions[Math.floor(Math.random() * categoryActions.length)];
  };

  const getPreviewDataForCategory = (category: string): any => {
    switch (category) {
      case 'trivia':
        return {
          current_question: 'Geography',
          round: Math.floor(Math.random() * 10) + 1,
          correct_answers: Math.floor(Math.random() * 8) + 1
        };
      case 'reaction':
        return {
          average_reaction_time: (Math.random() * 500 + 200).toFixed(0) + 'ms',
          perfect_reactions: Math.floor(Math.random() * 5),
          current_combo: Math.floor(Math.random() * 10)
        };
      case 'strategy':
        return {
          turn_number: Math.floor(Math.random() * 20) + 5,
          pieces_remaining: Math.floor(Math.random() * 15) + 5,
          last_move: 'Aggressive play'
        };
      default:
        return {};
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, React.ReactNode> = {
      trivia: <Trophy className="h-4 w-4" />,
      reaction: <Zap className="h-4 w-4" />,
      strategy: <Target className="h-4 w-4" />,
      puzzle: <Target className="h-4 w-4" />,
      sports: <Activity className="h-4 w-4" />
    };
    return icons[category] || <Play className="h-4 w-4" />;
  };

  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      trivia: 'bg-blue-500',
      reaction: 'bg-orange-500',
      strategy: 'bg-purple-500',
      puzzle: 'bg-green-500',
      sports: 'bg-red-500'
    };
    return colors[category] || 'bg-gray-500';
  };

  const getStateColor = (state: string): string => {
    switch (state) {
      case 'active': return 'bg-green-500';
      case 'starting': return 'bg-yellow-500';
      case 'waiting': return 'bg-blue-500';
      case 'paused': return 'bg-orange-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getExcitementIndicator = (score: number) => {
    if (score >= 90) return { icon: <Heart className="h-3 w-3" />, color: 'text-red-500', label: 'Hot!' };
    if (score >= 80) return { icon: <TrendingUp className="h-3 w-3" />, color: 'text-orange-500', label: 'Trending' };
    if (score >= 70) return { icon: <Activity className="h-3 w-3" />, color: 'text-blue-500', label: 'Active' };
    return null;
  };

  if (isLoading) {
    return (
      <Card className={`${className} animate-pulse`}>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!previewData) return null;

  const excitement = getExcitementIndicator(previewData.excitement_score);

  return (
    <Card className={`${className} group hover:shadow-lg transition-all duration-200 border-l-4 ${
      previewData.current_state === 'active' ? 'border-l-green-500' : 'border-l-gray-300'
    }`}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <div className={`p-1.5 rounded ${getCategoryColor(previewData.game_category)} text-white`}>
              {getCategoryIcon(previewData.game_category)}
            </div>
            <div>
              <CardTitle className="text-sm font-medium">{previewData.game_name}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="secondary" 
                  className={`${getStateColor(previewData.current_state)} text-white text-xs`}
                >
                  {previewData.current_state}
                </Badge>
                {excitement && (
                  <Badge variant="outline" className={`${excitement.color} border-current text-xs`}>
                    {excitement.icon}
                    <span className="ml-1">{excitement.label}</span>
                  </Badge>
                )}
                {isConnected && (
                  <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                    Live
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Session Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3 text-gray-500" />
            <span>{previewData.participants}/{previewData.max_participants}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3 text-gray-500" />
            <span>{previewData.spectators} watching</span>
          </div>
          {previewData.elapsed_time && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              <span>{formatTime(previewData.elapsed_time)} elapsed</span>
            </div>
          )}
          {previewData.entry_fee > 0 && (
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-gray-500" />
              <span>${previewData.total_prize_pool} pool</span>
            </div>
          )}
        </div>

        {/* Current Leader */}
        {previewData.current_leader && (
          <div className="bg-gray-50 p-2 rounded text-xs">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Leader:</span>
              <span className="font-medium">
                {previewData.current_leader.username} 
                <span className="text-blue-600 ml-1">
                  ({previewData.current_leader.score} pts)
                </span>
              </span>
            </div>
          </div>
        )}

        {/* Recent Action */}
        {previewData.recent_action && (
          <div className="text-xs text-center p-2 bg-blue-50 rounded text-blue-800 font-medium">
            {previewData.recent_action}
          </div>
        )}

        {/* Game-specific Preview Data */}
        {previewData.preview_data && (
          <div className="text-xs space-y-1">
            {previewData.game_category === 'trivia' && previewData.preview_data.current_question && (
              <div className="flex justify-between">
                <span>Topic:</span>
                <span className="font-medium">{previewData.preview_data.current_question}</span>
              </div>
            )}
            {previewData.game_category === 'reaction' && previewData.preview_data.average_reaction_time && (
              <div className="flex justify-between">
                <span>Avg Time:</span>
                <span className="font-medium">{previewData.preview_data.average_reaction_time}</span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1 text-xs"
            onClick={onViewSession}
          >
            <Eye className="h-3 w-3 mr-1" />
            Watch
          </Button>
          {showJoinButton && previewData.participants < previewData.max_participants && (
            <Button 
              size="sm" 
              className="flex-1 text-xs"
              onClick={onJoinSession}
            >
              <Play className="h-3 w-3 mr-1" />
              Join
            </Button>
          )}
        </div>

        {/* Last Update Indicator */}
        <div className="text-xs text-gray-400 text-center">
          Updated {Math.floor((Date.now() - lastUpdate.getTime()) / 1000)}s ago
        </div>
      </CardContent>
    </Card>
  );
}