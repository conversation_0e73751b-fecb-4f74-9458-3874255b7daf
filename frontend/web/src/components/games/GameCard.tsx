/**
 * BetBet Gaming Engine - Game Card Component
 * =========================================
 * 
 * Card component for displaying game information in the lobby.
 */

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Clock, 
  Trophy, 
  DollarSign, 
  Play,
  Star,
  Zap,
  Target
} from 'lucide-react';

interface GameCardProps {
  game: {
    id: string;
    name: string;
    slug: string;
    category: string;
    description?: string;
    cover_image?: string;
    min_players: number;
    max_players: number;
    estimated_duration_minutes: number;
    scoring_system: string;
    has_spectator_betting: boolean;
    is_featured: boolean;
    is_active: boolean;
    current_sessions?: number;
    total_plays?: number;
  };
  onQuickPlay?: () => void;
  showStats?: boolean;
}

const categoryIcons: Record<string, React.ReactNode> = {
  trivia: <Trophy className="h-4 w-4" />,
  reaction: <Zap className="h-4 w-4" />,
  strategy: <Target className="h-4 w-4" />,
  puzzle: <Target className="h-4 w-4" />,
};

const categoryColors: Record<string, string> = {
  trivia: 'bg-blue-500',
  reaction: 'bg-orange-500',
  strategy: 'bg-purple-500',
  puzzle: 'bg-green-500',
};

export function GameCard({ game, onQuickPlay, showStats = true }: GameCardProps) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`/games/${game.slug}`);
  };

  const handleQuickPlay = () => {
    if (onQuickPlay) {
      onQuickPlay();
    } else {
      // Default quick play - create a new session
      router.push(`/games/${game.slug}/create-session`);
    }
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 relative overflow-hidden">
      {/* Cover Image */}
      {game.cover_image && (
        <div className="relative h-32 w-full overflow-hidden">
          <img 
            src={game.cover_image} 
            alt={game.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </div>
      )}

      {game.is_featured && (
        <div className="absolute top-2 right-2 z-10">
          <Badge variant="secondary" className="bg-yellow-500 text-white">
            <Star className="h-3 w-3 mr-1" />
            Featured
          </Badge>
        </div>
      )}

      <CardHeader className={game.cover_image ? "pb-2" : ""}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-lg ${categoryColors[game.category] || 'bg-gray-500'} text-white`}>
              {categoryIcons[game.category] || <Trophy className="h-4 w-4" />}
            </div>
            <div>
              <CardTitle className="text-lg">{game.name}</CardTitle>
              <CardDescription className="text-xs capitalize">{game.category}</CardDescription>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {game.description && (
          <p className="text-sm text-gray-600 line-clamp-2">{game.description}</p>
        )}

        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center space-x-1 text-gray-600">
            <Users className="h-4 w-4" />
            <span>{game.min_players}-{game.max_players} players</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-600">
            <Clock className="h-4 w-4" />
            <span>{game.estimated_duration_minutes} min</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="text-xs">
            {game.scoring_system}
          </Badge>
          {game.has_spectator_betting && (
            <Badge variant="outline" className="text-xs">
              <DollarSign className="h-3 w-3 mr-1" />
              Betting
            </Badge>
          )}
        </div>

        {showStats && (game.current_sessions || game.total_plays) && (
          <div className="flex justify-between text-xs text-gray-500">
            {game.current_sessions !== undefined && (
              <span>{game.current_sessions} active sessions</span>
            )}
            {game.total_plays !== undefined && (
              <span>{game.total_plays.toLocaleString()} plays</span>
            )}
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1"
            onClick={handleViewDetails}
          >
            View Details
          </Button>
          <Button 
            size="sm" 
            className="flex-1"
            onClick={handleQuickPlay}
            disabled={!game.is_active}
          >
            <Play className="h-4 w-4 mr-1" />
            Quick Play
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}