'use client';

import { ReactNode, useState } from 'react';
import AuthenticatedNavigation from './AuthenticatedNavigation';
import AuthenticatedSidebar from './AuthenticatedSidebar';
import MobileNavigation from './MobileNavigation';
import MobileDrawer from './MobileDrawer';
import MobileFAB from '../ui/mobile-fab';
import ErrorBoundary from '../error/ErrorBoundary';
import LoadingBoundary from '../error/LoadingBoundary';
import { ApiDebugPanel } from '../dev/ApiDebugPanel';
import { cn } from '@/lib/utils';

interface AuthenticatedLayoutProps {
  children: ReactNode;
  showSidebar?: boolean;
  className?: string;
}

export function AuthenticatedLayout({ 
  children, 
  showSidebar = true, 
  className 
}: AuthenticatedLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);

  return (
    <ErrorBoundary>
      <LoadingBoundary>
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
          {/* Top Navigation */}
          <AuthenticatedNavigation 
            onSidebarToggle={setIsSidebarOpen}
            isSidebarOpen={isSidebarOpen}
          />
          
          {/* Mobile Drawer */}
          <MobileDrawer 
            isOpen={isMobileDrawerOpen} 
            onClose={() => setIsMobileDrawerOpen(false)} 
          />
          
          {/* Main Content Area */}
          <div className="flex">
            {/* Desktop Sidebar */}
            {showSidebar && (
              <AuthenticatedSidebar 
                className="hidden lg:block" 
                isOpen={isSidebarOpen}
                onSidebarToggle={setIsSidebarOpen}
                onMobileMenuOpen={() => setIsMobileDrawerOpen(true)}
              />
            )}
            
            {/* Page Content */}
            <main className={cn(
              "flex-1 overflow-hidden",
              "pb-16 sm:pb-0", // Bottom padding for mobile nav
              className
            )}>
              <div className="p-6">
                <ErrorBoundary>
                  {children}
                </ErrorBoundary>
              </div>
            </main>
          </div>

          {/* Mobile Bottom Navigation */}
          <MobileNavigation />
          
          {/* Mobile FAB */}
          <MobileFAB />
          
          {/* Development API Debug Panel */}
          <ApiDebugPanel />
        </div>
      </LoadingBoundary>
    </ErrorBoundary>
  );
}

export default AuthenticatedLayout;