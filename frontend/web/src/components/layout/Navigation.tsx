'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import api from '@/lib/api-client-unified';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Trophy,
  Bell,
  Plus,
  Menu,
  Gamepad2 as GamepadIcon,
  Target,
  Star,
  Home,
  Crown,
  Wallet,
} from 'lucide-react';
import { useAuth, useUser, SignInButton, SignUpButton, UserButton } from '@clerk/nextjs';
import { cn } from '@/lib/utils';

interface NavigationProps {
  className?: string;
  onMobileMenuClick?: () => void;
}

export function Navigation({ className, onMobileMenuClick }: NavigationProps) {
  const { isSignedIn, getToken } = useAuth();
  const { user } = useUser();
  const [userBalance, setUserBalance] = useState<number>(0);
  const [balanceLoading, setBalanceLoading] = useState(true);

  // Function to refresh wallet balance
  const refreshBalance = async () => {
    if (!user || !isSignedIn) return;
    
    try {
      // Set auth token for unified API
      const token = await getToken();
      api.setAuthToken(token);

      // Fetch balance
      const balanceData = await api.wallet.getBalance(user.id);
      setUserBalance(parseFloat(balanceData.available_balance.toString()));
    } catch (err) {
      console.error('Failed to refresh wallet balance:', err);
      setUserBalance(0);
    }
  };

  // Fetch wallet balance for authenticated users
  useEffect(() => {
    const fetchBalance = async () => {
      if (!user || !isSignedIn) {
        setBalanceLoading(false);
        return;
      }
      
      try {
        setBalanceLoading(true);
        await refreshBalance();
      } finally {
        setBalanceLoading(false);
      }
    };

    fetchBalance();
  }, [user, isSignedIn]);

  // Refresh balance periodically for authenticated users
  useEffect(() => {
    if (!user || !isSignedIn) return;
    
    const interval = setInterval(refreshBalance, 30000);
    return () => clearInterval(interval);
  }, [user, isSignedIn, getToken]);

  return (
    <nav className={cn("fixed top-0 w-full z-50 glass border-b border-white/10", className)}>
      <div className="container mx-auto max-w-7xl px-4">
        <div className="flex items-center justify-between h-16">
          {/* Enhanced Logo */}
          <div className="flex items-center space-x-3 group">
            <Link href="/" className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Trophy className="h-7 w-7 text-white" />
              </div>
              <h1 className="text-2xl font-black text-white">BetBet</h1>
            </Link>
          </div>

          {/* Right Actions */}
          <div className="flex items-center gap-4">
            {isSignedIn ? (
              <>
                {/* Notifications */}
                <Button variant="ghost" size="sm" className="relative text-slate-300 hover:text-white">
                  <Bell className="h-5 w-5" />
                  <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 text-xs flex items-center justify-center p-0">
                    3
                  </Badge>
                </Button>

                {/* Quick Create */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" className="bg-gradient-secondary text-white rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-glow">
                      <Plus className="h-4 w-4 mr-2" />
                      Create
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64 glass border-white/10">
                    <DropdownMenuLabel className="text-white">Quick Actions</DropdownMenuLabel>
                    
                    <DropdownMenuItem asChild>
                      <Link href="/games" className="cursor-pointer text-slate-300 hover:text-white">
                        <GamepadIcon className="mr-2 h-4 w-4" />
                        New Game Session
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem asChild>
                      <Link href="/games/chess" className="cursor-pointer text-slate-300 hover:text-white">
                        <Crown className="mr-2 h-4 w-4" />
                        Browse Chess Games
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/games/chess/create-session" className="cursor-pointer text-slate-300 hover:text-white">
                        <Crown className="mr-2 h-4 w-4" />
                        New Chess Game
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem asChild>
                      <Link href="/custom-betting/create" className="cursor-pointer text-slate-300 hover:text-white">
                        <Target className="mr-2 h-4 w-4" />
                        New Betting Market
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem asChild>
                      <Link href="/tournaments/create" className="cursor-pointer text-slate-300 hover:text-white">
                        <Trophy className="mr-2 h-4 w-4" />
                        New Tournament
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuSeparator className="bg-white/10" />
                    
                    <DropdownMenuItem asChild>
                      <Link href="/expert-portal" className="cursor-pointer text-slate-300 hover:text-white">
                        <Star className="mr-2 h-4 w-4" />
                        Expert Dashboard
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Wallet Balance */}
                <Link href="/wallet">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white flex items-center gap-2">
                    <Wallet className="h-4 w-4 text-emerald-400" />
                    {balanceLoading ? (
                      <span className="text-slate-400">...</span>
                    ) : (
                      <span className="font-semibold">
                        ${userBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </span>
                    )}
                  </Button>
                </Link>

                {/* Dashboard Link */}
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                    <Home className="h-4 w-4 mr-2" />
                    Dashboard
                  </Button>
                </Link>

                {/* User Button from Clerk */}
                <UserButton 
                  appearance={{
                    elements: {
                      avatarBox: "w-10 h-10 ring-2 ring-white/20"
                    }
                  }}
                  userProfileMode="navigation"
                  userProfileUrl="/profile"
                />
              </>
            ) : (
              <>
                {/* Guest Actions */}
                <SignInButton mode="modal">
                  <Button variant="ghost" size="lg" className="text-slate-300 hover:text-white transition-colors duration-300 px-6">
                    Sign In
                  </Button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <Button size="lg" className="bg-gradient-primary text-white rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-glow btn-hover-lift px-8">
                    Get Started
                  </Button>
                </SignUpButton>
              </>
            )}

            {/* Mobile Menu */}
            <Button 
              variant="ghost" 
              size="sm" 
              className="md:hidden text-slate-300 hover:text-white"
              onClick={onMobileMenuClick}
            >
              <Menu className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}

export default Navigation;