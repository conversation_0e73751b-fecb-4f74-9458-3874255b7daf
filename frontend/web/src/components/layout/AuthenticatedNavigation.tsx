'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth, useUser, UserButton } from '@clerk/nextjs';
import api from '@/lib/api-client-unified';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Menu,
  X,
  Search,
  Bell,
  Wallet,
  Home,
  Gamepad2,
  TrendingUp,
  Users,
  BarChart3,
  Trophy,
  Target,
  Star,
  Activity,
  Play,
  Award,
  ArrowUp
} from 'lucide-react';

interface AuthenticatedNavigationProps {
  className?: string;
  onSidebarToggle?: (isOpen: boolean) => void;
  isSidebarOpen?: boolean;
}

export function AuthenticatedNavigation({ 
  className, 
  onSidebarToggle,
  isSidebarOpen = true 
}: AuthenticatedNavigationProps) {
  const { getToken } = useAuth();
  const { user } = useUser();
  const [userBalance, setUserBalance] = useState<number>(0);
  const [balanceLoading, setBalanceLoading] = useState(true);
  
  // Mock real-time stats
  const [liveStats, setLiveStats] = useState({
    onlineUsers: 1247,
    activeGames: 892,
    totalBets: 15420,
    jackpot: 125000
  });

  // Function to refresh wallet balance
  const refreshBalance = async () => {
    if (!user) return;
    
    try {
      // Set auth token for unified API
      const token = await getToken();
      api.setAuthToken(token);

      // Fetch balance
      const balanceData = await api.wallet.getBalance(user.id);
      setUserBalance(parseFloat(balanceData.available_balance.toString()));
    } catch (err) {
      console.error('Failed to refresh wallet balance:', err);
    }
  };

  // Fetch wallet balance on mount
  useEffect(() => {
    const fetchBalance = async () => {
      if (!user) return;
      
      try {
        setBalanceLoading(true);
        await refreshBalance();
      } finally {
        setBalanceLoading(false);
      }
    };

    fetchBalance();
  }, [user]);

  // Refresh balance periodically (every 30 seconds)
  useEffect(() => {
    if (!user) return;
    
    const interval = setInterval(refreshBalance, 30000);
    return () => clearInterval(interval);
  }, [user, getToken]);

  useEffect(() => {
    const interval = setInterval(() => {
      setLiveStats(prev => ({
        onlineUsers: prev.onlineUsers + Math.floor(Math.random() * 10 - 5),
        activeGames: prev.activeGames + Math.floor(Math.random() * 6 - 3),
        totalBets: prev.totalBets + Math.floor(Math.random() * 20),
        jackpot: prev.jackpot + Math.floor(Math.random() * 1000 - 500)
      }));
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleSidebarToggle = () => {
    const newState = !isSidebarOpen;
    onSidebarToggle?.(newState);
  };

  return (
    <header className={cn(
      "sticky top-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50",
      className
    )}>
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-4">
          <button
            onClick={handleSidebarToggle}
            className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors"
          >
            {isSidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
          
          <Link href="/dashboard" className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-lg flex items-center justify-center">
              <Trophy className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
              BetBet
            </span>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <input
              type="text"
              placeholder="Search games, events..."
              className="pl-10 pr-4 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all text-white placeholder:text-slate-400"
            />
          </div>
          
          {/* Notifications */}
          <button className="relative p-2 hover:bg-slate-700/50 rounded-lg transition-colors">
            <Bell className="w-5 h-5 text-white" />
            <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 text-xs flex items-center justify-center p-0">
              3
            </Badge>
          </button>
          
          {/* Wallet Balance */}
          <Link href="/wallet">
            <div className="flex items-center gap-3 px-3 py-2 bg-slate-700/50 rounded-lg border border-slate-600/50 hover:bg-slate-600/50 transition-colors cursor-pointer group">
              <Wallet className="w-4 h-4 text-emerald-400 group-hover:text-emerald-300 transition-colors" />
              {balanceLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-slate-400 rounded-full animate-pulse"></div>
                  <span className="font-semibold text-slate-400">Loading...</span>
                </div>
              ) : (
                <span className="font-semibold text-white group-hover:text-slate-100 transition-colors">
                  ${userBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              )}
            </div>
          </Link>
          
          {/* User Profile */}
          <UserButton 
            appearance={{
              elements: {
                avatarBox: "w-8 h-8 rounded-full border border-slate-600"
              }
            }}
            userProfileMode="navigation"
            userProfileUrl="/profile"
          />
        </div>
      </div>
    </header>
  );
}

export default AuthenticatedNavigation;