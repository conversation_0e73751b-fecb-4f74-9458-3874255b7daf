/**
 * BetBet Gaming Engine - Main Layout
 * ==================================
 * 
 * Main application layout with navigation, sidebar, and content area.
 */

'use client';

import { ReactNode, useState } from 'react';
import Navigation from './Navigation';
import { Sidebar } from './Sidebar';
import MobileNavigation from './MobileNavigation';
import MobileDrawer from './MobileDrawer';
import MobileFAB from '../ui/mobile-fab';
import ErrorBoundary from '../error/ErrorBoundary';
import LoadingBoundary from '../error/LoadingBoundary';
import { ApiDebugPanel } from '../dev/ApiDebugPanel';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: ReactNode;
  showSidebar?: boolean;
  className?: string;
}

export function MainLayout({ 
  children, 
  showSidebar = true, 
  className 
}: MainLayoutProps) {
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);

  return (
    <ErrorBoundary>
      <LoadingBoundary>
        <div className="min-h-screen bg-background">
          {/* Top Navigation */}
          <Navigation onMobileMenuClick={() => setIsMobileDrawerOpen(true)} />
          
          {/* Mobile Drawer */}
          <MobileDrawer 
            isOpen={isMobileDrawerOpen} 
            onClose={() => setIsMobileDrawerOpen(false)} 
          />
          
          {/* Main Content Area */}
          <div className="flex pt-20"> {/* Add top padding to account for fixed navbar */}
            {/* Desktop Sidebar */}
            {showSidebar && (
              <Sidebar className="hidden lg:flex" />
            )}
            
            {/* Page Content */}
            <main className={cn(
              "flex-1 overflow-hidden",
              showSidebar && "lg:ml-64",
              "pb-16 sm:pb-0", // Bottom padding for mobile nav
              className
            )}>
              <div className="responsive-container responsive-section">
                <ErrorBoundary>
                  {children}
                </ErrorBoundary>
              </div>
            </main>
          </div>

          {/* Mobile Bottom Navigation */}
          <MobileNavigation />
          
          {/* Mobile FAB */}
          <MobileFAB />
          
          {/* Development API Debug Panel */}
          <ApiDebugPanel />
        </div>
      </LoadingBoundary>
    </ErrorBoundary>
  );
}

export default MainLayout;