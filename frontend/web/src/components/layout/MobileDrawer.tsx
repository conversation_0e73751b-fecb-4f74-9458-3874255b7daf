/**
 * BetBet Gaming Engine - Mobile Drawer
 * ====================================
 * 
 * Slide-out navigation drawer for mobile devices.
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth, UserButton } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  X,
  Home,
  Gamepad2,
  Target,
  TrendingUp,
  Users,
  BarChart3,
  Trophy,
  Star,
  Wallet,
  Settings,
  LogOut,
  DollarSign,
  Bell,
  Crown,
  Zap,
  Shield,
  ChevronRight,
  Activity,
  Play,
  Award
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileDrawer({ isOpen, onClose }: MobileDrawerProps) {
  const { user, isSignedIn } = useAuth();

  // Close drawer on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  const handleLogout = () => {
    logout();
    onClose();
  };

  const navigationSections = [
    {
      title: 'Platform',
      items: [
        { href: '/dashboard', icon: Home, label: 'Overview', badge: null },
        { href: '/games', icon: Gamepad2, label: 'Gaming', badge: 12 },
        { href: '/custom-betting', icon: Target, label: 'Sports Betting', badge: null },
        { href: '/trading', icon: TrendingUp, label: 'Trading', badge: null },
      ],
    },
    {
      title: 'Community & Analytics',
      items: [
        { href: '/experts', icon: Users, label: 'Community', badge: 3 },
        { href: '/sports-analysis', icon: BarChart3, label: 'Analytics', badge: null },
        { href: '/leaderboard', icon: Trophy, label: 'Leaderboards', badge: null },
        { href: '/expert-portal', icon: Star, label: 'Expert Portal', badge: null },
      ],
    },
  ];

  const quickActions = [
    { href: '/games/create', icon: Play, label: 'Join Game', color: 'text-blue-400' },
    { href: '/custom-betting/create', icon: Target, label: 'Place Bet', color: 'text-green-400' },
    { href: '/trading', icon: TrendingUp, label: 'Trade', color: 'text-yellow-400' },
    { href: '/wallet', icon: Wallet, label: 'Wallet', color: 'text-purple-400' },
  ];

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="mobile-drawer" onClick={onClose} />
      
      {/* Drawer Content */}
      <div className="mobile-drawer-content animate-in slide-in-from-left duration-300">
        <div className="flex flex-col h-full">
          
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-slate-700/50 bg-slate-800/50">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-lg flex items-center justify-center">
                <Trophy className="h-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
                BetBet
              </span>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="text-slate-400 hover:text-white">
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* User Section */}
          {isSignedIn && user ? (
            <div className="p-4 border-b bg-slate-800/30">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12 border border-slate-600">
                  <AvatarImage src={user.imageUrl} />
                  <AvatarFallback className="bg-gradient-to-br from-orange-500 to-yellow-500">
                    {user.firstName?.slice(0, 1) || 'U'}{user.lastName?.slice(0, 1) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold truncate text-white">
                    {user.firstName || 'User'} {user.lastName || ''}
                  </p>
                  <p className="text-sm text-slate-400 truncate">
                    {user.primaryEmailAddress?.emailAddress}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Wallet className="h-3 w-3 text-green-400" />
                    <span className="text-xs font-medium text-green-400">
                      $2,547.80
                    </span>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/notifications">
                    <Bell className="h-4 w-4 text-slate-400" />
                    <Badge variant="destructive" className="ml-1 h-4 w-4 p-0 text-xs">
                      3
                    </Badge>
                  </Link>
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 border-b bg-slate-800/30">
              <div className="space-y-2">
                <Button className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600" onClick={onClose}>
                  Sign In
                </Button>
                <Button variant="outline" className="w-full" asChild onClick={onClose}>
                  <Link href="/register">Sign Up</Link>
                </Button>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          {isAuthenticated && (
            <>
              <div className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">Quick Actions</h3>
                <div className="space-y-2">
                  {quickActions.map((action) => (
                    <Link
                      key={action.href}
                      href={action.href}
                      onClick={onClose}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors"
                    >
                      <action.icon className={cn("h-5 w-5", action.color)} />
                      <span className="font-medium">{action.label}</span>
                      <ChevronRight className="h-4 w-4 ml-auto text-muted-foreground" />
                    </Link>
                  ))}
                </div>
              </div>
              <Separator />
            </>
          )}

          {/* Navigation Sections */}
          <div className="flex-1 overflow-y-auto">
            {navigationSections.map((section) => (
              <div key={section.title} className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {section.title}
                </h3>
                <div className="space-y-1">
                  {section.items.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={onClose}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors"
                    >
                      <item.icon className="h-5 w-5 text-muted-foreground" />
                      <span className="font-medium flex-1">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Bottom Section */}
          {isAuthenticated && (
            <div className="border-t p-4 space-y-2">
              <Link
                href="/profile"
                onClick={onClose}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors"
              >
                <User className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Profile</span>
                <ChevronRight className="h-4 w-4 ml-auto text-muted-foreground" />
              </Link>
              
              <Link
                href="/settings"
                onClick={onClose}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors"
              >
                <Settings className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Settings</span>
                <ChevronRight className="h-4 w-4 ml-auto text-muted-foreground" />
              </Link>
              
              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors w-full text-left"
              >
                <LogOut className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Logout</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default MobileDrawer;