'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { UserButton, SignedIn, SignedOut, useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import {
  GamepadIcon,
  Menu,
  X,
  Home,
  TrendingUp,
  Trophy,
  Star,
  BarChart3,
  Wallet,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';

export function LandingNavigation() {
  const router = useRouter();
  const { user } = useUser();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Show navbar when scrolled more than 100px
      if (currentScrollY > 100) {
        setIsScrolled(true);
        setIsVisible(true);
      } else {
        setIsScrolled(false);
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { href: '/games', label: 'Games', icon: GamepadIcon },
    { href: '/custom-betting', label: 'Betting', icon: Target },
    { href: '/trading', label: 'Trading', icon: TrendingUp },
    { href: '/experts', label: 'Experts', icon: Star },
    { href: '/sports-analysis', label: 'Analysis', icon: BarChart3 },
    { href: '/leaderboard', label: 'Leaderboard', icon: Trophy },
    { href: '/wallet', label: 'Wallet', icon: Wallet },
  ];

  return (
    <>
      {/* Static Header Elements (Always visible at top) */}
      <div className="fixed top-0 left-0 right-0 z-40 p-4">
        <div className="container mx-auto max-w-7xl">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg blur opacity-75 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-gradient-to-r from-purple-500 to-indigo-500 p-2 rounded-lg">
                  <GamepadIcon className="h-8 w-8 text-white" />
                </div>
              </div>
              <span className="text-2xl font-bold text-white">BetBet</span>
            </Link>

            {/* User Controls */}
            <div className="flex items-center space-x-4">
              <SignedIn>
                <Button
                  variant="outline"
                  className="hidden md:flex glass text-white border-white/30 hover:glass-hover"
                  onClick={() => router.push('/dashboard')}
                >
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
                <UserButton afterSignOutUrl="/" />
              </SignedIn>
              
              <SignedOut>
                <Button 
                  variant="outline"
                  className="hidden md:block glass text-white border-white/30 hover:glass-hover"
                  onClick={() => router.push('/sign-in')}
                >
                  Sign In
                </Button>
                <Button 
                  className="hidden md:block bg-gradient-primary text-white hover:opacity-90"
                  onClick={() => router.push('/sign-up')}
                >
                  Sign Up
                </Button>
              </SignedOut>

              {/* Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 glass rounded-lg text-white hover:glass-hover transition-all duration-300"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Sliding Navigation Bar */}
      <nav className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-lg border-b border-slate-700/50 transition-all duration-500",
        isVisible ? "translate-y-0 opacity-100" : "-translate-y-full opacity-0"
      )}>
        <div className="container mx-auto max-w-7xl px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg blur opacity-75 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-gradient-to-r from-purple-500 to-indigo-500 p-1.5 rounded-lg">
                  <GamepadIcon className="h-6 w-6 text-white" />
                </div>
              </div>
              <span className="text-xl font-bold text-white">BetBet</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="flex items-center space-x-2 text-slate-300 hover:text-white transition-colors duration-200"
                >
                  <link.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{link.label}</span>
                </Link>
              ))}
            </div>

            {/* User Controls */}
            <div className="flex items-center space-x-4">
              <SignedIn>
                <Button
                  size="sm"
                  variant="outline"
                  className="hidden md:flex bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/dashboard')}
                >
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
                <UserButton afterSignOutUrl="/" />
              </SignedIn>
              
              <SignedOut>
                <Button 
                  size="sm"
                  variant="outline"
                  className="hidden md:block bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/sign-in')}
                >
                  Sign In
                </Button>
                <Button 
                  size="sm"
                  className="hidden md:block bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => router.push('/sign-up')}
                >
                  Sign Up
                </Button>
              </SignedOut>

              {/* Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 bg-slate-800/50 rounded-lg text-white hover:bg-slate-700 transition-all duration-300"
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm md:hidden" onClick={() => setMobileMenuOpen(false)}>
          <div className="fixed right-0 top-0 h-full w-64 bg-slate-900 border-l border-slate-700" onClick={(e) => e.stopPropagation()}>
            <div className="p-4 border-b border-slate-700">
              <SignedIn>
                <div className="flex items-center space-x-3 mb-4">
                  <UserButton afterSignOutUrl="/" />
                  <span className="text-white font-medium">{user?.firstName || 'User'}</span>
                </div>
              </SignedIn>
              <SignedOut>
                <div className="space-y-2">
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    onClick={() => {
                      router.push('/sign-up');
                      setMobileMenuOpen(false);
                    }}
                  >
                    Sign Up
                  </Button>
                  <Button 
                    variant="outline"
                    className="w-full bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                    onClick={() => {
                      router.push('/sign-in');
                      setMobileMenuOpen(false);
                    }}
                  >
                    Sign In
                  </Button>
                </div>
              </SignedOut>
            </div>

            <nav className="p-4 space-y-2">
              <SignedIn>
                <Link
                  href="/dashboard"
                  className="flex items-center space-x-3 p-3 rounded-lg text-white hover:bg-slate-800 transition-colors duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Home className="h-5 w-5" />
                  <span>Dashboard</span>
                </Link>
              </SignedIn>
              
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="flex items-center space-x-3 p-3 rounded-lg text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <link.icon className="h-5 w-5" />
                  <span>{link.label}</span>
                </Link>
              ))}
            </nav>
          </div>
        </div>
      )}
    </>
  );
}