/**
 * BetBet Gaming Engine - Sidebar Navigation
 * =========================================
 * 
 * Left sidebar with quick access to games, sessions, and user features.
 */

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Gamepad2 as GamepadIcon,
  Users,
  Trophy,
  Eye,
  TrendingUp,
  Clock,
  Star,
  Zap,
  Target,
  DollarSign,
  Crown,
} from 'lucide-react';
import { useAuth, useGames, useCurrentSession } from '@/store/gameStore';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();
  const { featuredGames, sessions } = useGames();
  const { session } = useCurrentSession();

  const navigationItems = [
    {
      title: 'Browse',
      items: [
        {
          name: 'All Games',
          href: '/games',
          icon: GamepadIcon,
          count: featuredGames.length,
        },
        {
          name: 'Chess Games',
          href: '/games/chess',
          icon: Crown,
          count: 0, // TODO: Add active chess games count
        },
        {
          name: 'Active Sessions',
          href: '/sessions',
          icon: Users,
          count: sessions.filter(s => s.status === 'waiting' || s.status === 'active').length,
        },
        {
          name: 'Tournaments',
          href: '/tournaments',
          icon: Trophy,
          count: 0, // TODO: Add tournament count
        },
        {
          name: 'Spectate',
          href: '/spectate',
          icon: Eye,
          count: 0, // TODO: Add spectator count
        },
      ],
    },
    {
      title: 'Statistics',
      items: [
        {
          name: 'Leaderboard',
          href: '/leaderboard',
          icon: TrendingUp,
        },
        {
          name: 'Recent Games',
          href: '/recent',
          icon: Clock,
        },
      ],
    },
  ];

  const gameCategories = [
    { name: 'Trivia', icon: Target, color: 'bg-blue-500' },
    { name: 'Strategy', icon: Zap, color: 'bg-green-500' },
    { name: 'Reaction', icon: Clock, color: 'bg-yellow-500' },
    { name: 'Sports', icon: Trophy, color: 'bg-purple-500' },
  ];

  return (
    <aside className={cn(
      "fixed left-0 top-16 z-30 h-[calc(100vh-4rem)] w-64 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="flex h-full flex-col gap-4 p-4 overflow-y-auto">
        
        {/* Current Session Status */}
        {session && (
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-primary">Current Session</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <p className="text-sm font-medium">{session.session_name}</p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Players</span>
                  <span>{session.current_participants}/{session.max_participants}</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Prize Pool</span>
                  <span className="font-medium">${session.total_prize_pool}</span>
                </div>
                <Badge variant="outline" className="w-full justify-center">
                  {session.status}
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Create */}
        {isAuthenticated && (
          <Card>
            <CardContent className="p-4">
              <Button className="w-full" size="sm" asChild>
                <Link href="/games">
                  <GamepadIcon className="h-4 w-4 mr-2" />
                  Create Session
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="space-y-4">
          {navigationItems.map((section) => (
            <div key={section.title}>
              <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                {section.title}
              </h3>
              <div className="space-y-1">
                {section.items.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                        isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground"
                      )}
                    >
                      <div className="flex items-center space-x-2">
                        <item.icon className="h-4 w-4" />
                        <span>{item.name}</span>
                      </div>
                      {item.count !== undefined && item.count > 0 && (
                        <Badge variant="secondary" className="h-5 text-xs">
                          {item.count}
                        </Badge>
                      )}
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Game Categories */}
        <div>
          <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Categories
          </h3>
          <div className="space-y-1">
            {gameCategories.map((category) => (
              <Link
                key={category.name}
                href={`/games?category=${category.name.toLowerCase()}`}
                className="flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground"
              >
                <div className={cn("w-2 h-2 rounded-full", category.color)} />
                <span>{category.name}</span>
              </Link>
            ))}
          </div>
        </div>

        <Separator />

        {/* Featured Games */}
        {featuredGames.length > 0 && (
          <div>
            <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Featured
            </h3>
            <div className="space-y-2">
              {featuredGames.slice(0, 3).map((game) => (
                <Link
                  key={game.id}
                  href={`/games/${game.id}`}
                  className="block rounded-md p-2 text-sm transition-colors hover:bg-accent"
                >
                  <div className="flex items-start space-x-2">
                    <Star className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-foreground truncate">
                        {game.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {game.category} • {game.min_players}-{game.max_players} players
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Bottom Spacer */}
        <div className="flex-1" />
        
        {/* Footer Stats */}
        <Card className="bg-muted/30">
          <CardContent className="p-3">
            <div className="grid grid-cols-2 gap-2 text-center">
              <div>
                <p className="text-xs text-muted-foreground">Online</p>
                <p className="text-sm font-semibold">1,247</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Playing</p>
                <p className="text-sm font-semibold">892</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </aside>
  );
}

export default Sidebar;