'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth, UserButton } from '@clerk/nextjs';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Menu,
  X,
  Search,
  Bell,
  Wallet,
  ChevronDown,
  ChevronRight,
  Home,
  Gamepad2,
  Target,
  TrendingUp,
  Users,
  BarChart3,
  Trophy,
  Star,
  Activity,
  Play,
  Award,
  Plus,
  Eye,
  Zap
} from 'lucide-react';

interface AuthenticatedSidebarProps {
  className?: string;
  isOpen?: boolean;
  onSidebarToggle?: (isOpen: boolean) => void;
  onMobileMenuOpen?: () => void;
}

interface NavItem {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  href?: string;
  badge?: string;
  submenu?: {
    name: string;
    href: string;
    icon: React.ComponentType<any>;
  }[];
}

export function AuthenticatedSidebar({ 
  className, 
  isOpen = true, 
  onSidebarToggle,
  onMobileMenuOpen 
}: AuthenticatedSidebarProps) {
  const pathname = usePathname();
  const { user } = useAuth();
  const [userBalance] = useState(2547.80);
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  
  // Mock real-time stats for sidebar widget
  const [liveStats, setLiveStats] = useState({
    onlineUsers: 1247,
    activeGames: 892,
    totalBets: 15420,
    jackpot: 125000
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setLiveStats(prev => ({
        onlineUsers: prev.onlineUsers + Math.floor(Math.random() * 10 - 5),
        activeGames: prev.activeGames + Math.floor(Math.random() * 6 - 3),
        totalBets: prev.totalBets + Math.floor(Math.random() * 20),
        jackpot: prev.jackpot + Math.floor(Math.random() * 1000 - 500)
      }));
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const navItems: NavItem[] = [
    { 
      id: 'overview', 
      name: 'Overview', 
      icon: Home, 
      href: '/dashboard' 
    },
    { 
      id: 'gaming', 
      name: 'Gaming', 
      icon: Gamepad2,
      badge: '12',
      submenu: [
        { name: 'Browse Games', href: '/games', icon: Gamepad2 },
        { name: 'Live Sessions', href: '/sessions', icon: Play },
        { name: 'Tournaments', href: '/tournaments', icon: Trophy },
        { name: 'Spectate', href: '/spectate', icon: Eye }
      ]
    },
    { 
      id: 'markets', 
      name: 'Markets', 
      icon: Target,
      submenu: [
        { name: 'Custom Betting', href: '/custom-betting', icon: Target },
        { name: 'Odds Exchange', href: '/trading', icon: TrendingUp },
        { name: 'Sports Analysis', href: '/sports-analysis', icon: BarChart3 },
        { name: 'Create Market', href: '/custom-betting/create', icon: Plus }
      ]
    },
    { 
      id: 'community', 
      name: 'Community', 
      icon: Users, 
      badge: '3',
      submenu: [
        { name: 'Expert Analysis', href: '/experts', icon: Star },
        { name: 'Expert Picks', href: '/picks', icon: Target },
        { name: 'Expert Portal', href: '/expert-portal', icon: Zap },
        { name: 'Join Chat', href: '/community/chat', icon: Users }
      ]
    },
    { 
      id: 'leaderboards', 
      name: 'Leaderboards', 
      icon: Trophy, 
      href: '/leaderboard' 
    },
    { 
      id: 'wallet', 
      name: 'Wallet', 
      icon: Wallet, 
      href: '/wallet' 
    }
  ];

  const handleSidebarToggle = () => {
    const newState = !isOpen;
    onSidebarToggle?.(newState);
  };

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus(prev => {
      if (prev.includes(menuId)) {
        // Collapse the menu
        return prev.filter(id => id !== menuId);
      } else {
        // Expand the menu and collapse others (true accordion behavior)
        return [menuId];
      }
    });
  };

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(href);
  };

  const hasActiveSubmenu = (submenu?: NavItem['submenu']) => {
    if (!submenu) return false;
    return submenu.some(item => isActive(item.href));
  };

  // Auto-expand menu containing current page on initial load
  useEffect(() => {
    // Find which menu should be expanded based on current path
    let menuToExpand = '';
    
    if (pathname.startsWith('/games') || pathname.startsWith('/sessions') || pathname.startsWith('/tournaments') || pathname.startsWith('/spectate')) {
      menuToExpand = 'gaming';
    } else if (pathname.startsWith('/custom-betting') || pathname.startsWith('/trading') || pathname.startsWith('/sports-analysis')) {
      menuToExpand = 'markets';
    } else if (pathname.startsWith('/experts') || pathname.startsWith('/picks') || pathname.startsWith('/expert-portal') || pathname.startsWith('/community')) {
      menuToExpand = 'community';
    }
    
    // Only auto-expand if no menu is currently expanded and we have a menu to expand
    if (menuToExpand && expandedMenus.length === 0) {
      setExpandedMenus([menuToExpand]);
    }
  }, [pathname]); // Remove expandedMenus from dependencies to prevent conflicts

  return (
    <aside className={cn(
      `${isOpen ? 'w-64' : 'w-0'} transition-all duration-300 overflow-hidden bg-slate-800/50 backdrop-blur-sm border-r border-slate-700/50 flex flex-col h-screen`,
      className
    )}>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navItems.map((item) => (
          <div key={item.id}>
            {item.submenu ? (
              <div>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSubmenu(item.id);
                  }}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200",
                    hasActiveSubmenu(item.submenu) || expandedMenus.includes(item.id)
                      ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                      : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                  )}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="font-medium flex-1">{item.name}</span>
                  {item.badge && (
                    <span className="px-2 py-0.5 bg-red-500 text-white text-xs rounded-full">
                      {item.badge}
                    </span>
                  )}
                  <ChevronRight className={cn(
                    "w-4 h-4 transition-transform duration-200",
                    expandedMenus.includes(item.id) && "rotate-90"
                  )} />
                </button>
                
                {/* Submenu */}
                <div 
                  className={cn(
                    "overflow-hidden transition-all duration-300 ease-in-out",
                    expandedMenus.includes(item.id) 
                      ? "max-h-96 opacity-100" 
                      : "max-h-0 opacity-0"
                  )}
                >
                  <div className="ml-6 mt-1 space-y-1 pb-1">
                    {item.submenu.map((subItem) => (
                      <Link key={subItem.href} href={subItem.href}>
                        <button
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all duration-200 text-sm",
                            isActive(subItem.href)
                              ? 'bg-orange-500/10 text-orange-400 border-l-2 border-orange-500'
                              : 'text-slate-400 hover:bg-slate-700/30 hover:text-slate-300'
                          )}
                        >
                          <subItem.icon className="w-4 h-4" />
                          <span>{subItem.name}</span>
                        </button>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <Link href={item.href!}>
                <button
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200",
                    isActive(item.href!)
                      ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                      : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                  )}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="font-medium">{item.name}</span>
                  {item.badge && (
                    <span className="ml-auto px-2 py-0.5 bg-red-500 text-white text-xs rounded-full">
                      {item.badge}
                    </span>
                  )}
                </button>
              </Link>
            )}
          </div>
        ))}
      </nav>
      
      {/* Platform Status Widget */}
      <div className="p-4 border-t border-slate-700/50">
        <div className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg p-3 border border-slate-600/50">
          <h4 className="font-semibold mb-2 text-slate-200 text-sm">Platform Status</h4>
          <div className="space-y-1.5 text-xs">
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Online Users</span>
              <span className="text-green-400 font-medium">{liveStats.onlineUsers.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Active Games</span>
              <span className="text-blue-400 font-medium">{liveStats.activeGames}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Total Bets</span>
              <span className="text-purple-400 font-medium">{liveStats.totalBets.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Jackpot</span>
              <span className="text-yellow-400 font-medium">${liveStats.jackpot.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}

export default AuthenticatedSidebar;