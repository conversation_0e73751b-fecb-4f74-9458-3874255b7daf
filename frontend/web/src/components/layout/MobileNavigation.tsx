/**
 * BetBet Gaming Engine - Mobile Navigation
 * =======================================
 * 
 * Bottom navigation bar optimized for mobile devices.
 */

'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  Home,
  Gamepad2 as GamepadIcon,
  Users,
  Trophy,
  Eye,
  User,
  Settings,
  Menu,
  Search,
  TrendingUp,
  Crown,
} from 'lucide-react';
import { useAuth } from '@/store/gameStore';
import { cn } from '@/lib/utils';

interface MobileNavItem {
  href: string;
  icon: React.ElementType;
  label: string;
  badge?: number;
  requiresAuth?: boolean;
}

export function MobileNavigation() {
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();

  const navItems: MobileNavItem[] = [
    {
      href: '/',
      icon: Home,
      label: 'Home',
    },
    {
      href: '/games',
      icon: GamepadIcon,
      label: 'Games',
    },
    {
      href: '/games/chess',
      icon: Crown,
      label: 'Chess',
    },
    {
      href: '/custom-betting',
      icon: TrendingUp,
      label: 'Markets',
    },
    {
      href: '/sessions',
      icon: Users,
      label: 'Live',
      badge: 3, // Mock active sessions
    },
    {
      href: '/spectate',
      icon: Eye,
      label: 'Spectate',
    },
    {
      href: isAuthenticated ? '/profile' : '/login',
      icon: isAuthenticated ? User : Settings,
      label: isAuthenticated ? 'Profile' : 'Login',
      requiresAuth: false,
    },
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className="mobile-nav">
      <div className="grid grid-cols-6 h-16">
        {navItems.map((item) => {
          const isItemActive = isActive(item.href);
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "mobile-nav-item touch-target relative",
                isItemActive && "text-primary"
              )}
            >
              <item.icon className={cn(
                "h-5 w-5 mb-1",
                isItemActive && "text-primary"
              )} />
              <span className={cn(
                "text-xs",
                isItemActive && "text-primary font-medium"
              )}>
                {item.label}
              </span>
              
              {/* Badge for notifications */}
              {item.badge && item.badge > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                >
                  {item.badge > 99 ? '99+' : item.badge}
                </Badge>
              )}
              
              {/* Active indicator */}
              {isItemActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-primary rounded-full" />
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
}

export default MobileNavigation;