/**
 * BetBet Gaming Engine - User Dashboard
 * ===================================
 * 
 * Comprehensive user dashboard with gaming stats, history, and quick actions.
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  User,
  Trophy,
  Target,
  DollarSign,
  Clock,
  TrendingUp,
  TrendingDown,
  Calendar,
  Users,
  Eye,
  Play,
  GamepadIcon,
  Zap,
  Crown,
  Star,
  Activity,
  BarChart3,
  Loader2,
  AlertCircle,
  Plus
} from 'lucide-react';

interface UserStats {
  total_sessions: number;
  sessions_won: number;
  sessions_lost: number;
  win_rate: number;
  total_earnings: number;
  total_spent: number;
  net_profit: number;
  favorite_game: string;
  current_streak: number;
  best_streak: number;
  tournaments_joined: number;
  tournaments_won: number;
  current_rank: number;
  skill_points: number;
}

interface RecentActivity {
  id: string;
  type: 'session' | 'tournament' | 'bet';
  title: string;
  game_name?: string;
  result?: 'won' | 'lost' | 'draw' | 'pending';
  amount?: number;
  created_at: string;
  session_id?: string;
  tournament_id?: string;
}

interface ActiveSession {
  id: string;
  slug: string;
  session_name: string;
  game_name: string;
  state: string;
  current_participants: number;
  max_participants: number;
  entry_fee: number;
  user_role: 'player' | 'spectator';
  scheduled_start_time?: string;
}

interface UserDashboardProps {
  userId?: string;
}

export function UserDashboard({ userId }: UserDashboardProps) {
  const api = useApi();
  const router = useRouter();
  
  const [stats, setStats] = useState<UserStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, [userId]);


  const loadDashboardData = async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Load real data from API - no fallbacks
      const statsResponse = await api.getUserStats(userId);
      setStats(statsResponse);
      
      const activityResponse = await api.getUserActivity(userId, { limit: 10 });
      setRecentActivity(activityResponse);
      
      const sessionsResponse = await api.getUserActiveSessions(userId);
      setActiveSessions(sessionsResponse);
      
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError(`Failed to load dashboard data: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string, result?: string) => {
    if (type === 'session') {
      if (result === 'won') return <Trophy className="h-4 w-4 text-yellow-500" />;
      if (result === 'lost') return <Target className="h-4 w-4 text-red-500" />;
      return <GamepadIcon className="h-4 w-4 text-blue-500" />;
    }
    if (type === 'tournament') {
      return <Crown className="h-4 w-4 text-purple-500" />;
    }
    if (type === 'bet') {
      return <DollarSign className="h-4 w-4 text-green-500" />;
    }
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  const getActivityColor = (result?: string) => {
    switch (result) {
      case 'won':
        return 'text-green-400 bg-green-900/30 border-green-600';
      case 'lost':
        return 'text-red-400 bg-red-900/30 border-red-600';
      case 'draw':
        return 'text-yellow-400 bg-yellow-900/30 border-yellow-600';
      case 'pending':
        return 'text-blue-400 bg-blue-900/30 border-blue-600';
      default:
        return 'text-slate-300 bg-slate-800/30 border-slate-600';
    }
  };

  const getSessionStateColor = (state: string) => {
    switch (state) {
      case 'waiting':
        return 'bg-yellow-500';
      case 'starting':
        return 'bg-orange-500';
      case 'active':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getSessionStateLabel = (state: string) => {
    switch (state) {
      case 'waiting':
        return 'Waiting';
      case 'starting':
        return 'Starting';
      case 'active':
        return 'Live';
      default:
        return state;
    }
  };

  const getRankBadge = (rank: number) => {
    if (rank <= 10) return { color: 'bg-yellow-500', label: 'Elite' };
    if (rank <= 100) return { color: 'bg-purple-500', label: 'Pro' };
    if (rank <= 1000) return { color: 'bg-blue-500', label: 'Advanced' };
    return { color: 'bg-gray-500', label: 'Beginner' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-400" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto" />
            <div>
              <p className="text-white font-medium text-lg mb-2">Dashboard Error</p>
              <p className="text-red-400 text-sm mb-4">{error}</p>
              <div className="text-slate-400 text-xs">
                Check your connection and try again. If the problem persists, contact support.
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadDashboardData}
              className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-yellow-400 mx-auto" />
            <p className="text-white font-medium">No user data available</p>
            <p className="text-slate-400 text-sm">User ID: {userId || 'Not provided'}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const rankBadge = getRankBadge(stats.current_rank);

  return (
    <div className="space-y-6">
      {/* Header with Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardContent className="p-4 text-center">
            <Trophy className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white drop-shadow-sm">{stats.sessions_won}</p>
            <p className="text-sm text-slate-300 font-medium">Wins</p>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 text-blue-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white drop-shadow-sm">{(stats.win_rate * 100).toFixed(1)}%</p>
            <p className="text-sm text-slate-300 font-medium">Win Rate</p>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardContent className="p-4 text-center">
            <DollarSign className="h-8 w-8 text-emerald-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white drop-shadow-sm">${stats.net_profit}</p>
            <p className="text-sm text-slate-300 font-medium">Net Profit</p>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardContent className="p-4 text-center">
            <Crown className="h-8 w-8 text-purple-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white drop-shadow-sm">#{stats.current_rank}</p>
            <p className="text-sm text-slate-300 font-medium">Global Rank</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 bg-slate-800/50 border border-slate-600">
          <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">Overview</TabsTrigger>
          <TabsTrigger value="active" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">Active Games</TabsTrigger>
          <TabsTrigger value="experts" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">Expert Picks</TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">History</TabsTrigger>
          <TabsTrigger value="stats" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Player Profile */}
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white font-bold">
                <User className="h-5 w-5 text-slate-300" />
                Player Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <div className="w-full h-full bg-blue-100 flex items-center justify-center text-blue-800 text-lg font-bold">
                    {userId?.slice(0, 2).toUpperCase() || 'U'}
                  </div>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white">Player {userId?.slice(0, 8) || 'Unknown'}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={`${rankBadge.color} text-white`}>
                      {rankBadge.label}
                    </Badge>
                    <span className="text-sm text-slate-300 font-medium">#{stats.current_rank} Global</span>
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm text-slate-200 font-medium">{stats.skill_points} Skill Points</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-slate-800/50 rounded-lg border border-slate-700">
                  <p className="text-2xl font-bold text-blue-400">{stats.total_sessions}</p>
                  <p className="text-sm text-slate-300">Total Sessions</p>
                </div>
                <div className="text-center p-3 bg-slate-800/50 rounded-lg border border-slate-700">
                  <p className="text-2xl font-bold text-purple-400">{stats.tournaments_joined}</p>
                  <p className="text-sm text-slate-300">Tournaments</p>
                </div>
                <div className="text-center p-3 bg-slate-800/50 rounded-lg border border-slate-700">
                  <p className="text-2xl font-bold text-orange-400">{stats.current_streak}</p>
                  <p className="text-sm text-slate-300">Current Streak</p>
                </div>
                <div className="text-center p-3 bg-slate-800/50 rounded-lg border border-slate-700">
                  <p className="text-2xl font-bold text-green-400">{stats.best_streak}</p>
                  <p className="text-sm text-slate-300">Best Streak</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                <Button 
                  className="flex items-center gap-2 h-auto p-4 bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => router.push('/games')}
                >
                  <Play className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Quick Play</p>
                    <p className="text-xs opacity-90">Start a game now</p>
                  </div>
                </Button>
                <Button 
                  variant="outline"
                  className="flex items-center gap-2 h-auto p-4 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/tournaments')}
                >
                  <Trophy className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Tournaments</p>
                    <p className="text-xs opacity-75">Join competition</p>
                  </div>
                </Button>
                <Button 
                  variant="outline"
                  className="flex items-center gap-2 h-auto p-4 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/experts')}
                >
                  <Star className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Expert Picks</p>
                    <p className="text-xs opacity-75">Premium insights</p>
                  </div>
                </Button>
                <Button 
                  variant="outline"
                  className="flex items-center gap-2 h-auto p-4 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/games')}
                >
                  <Plus className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Host Game</p>
                    <p className="text-xs opacity-75">Create session</p>
                  </div>
                </Button>
                <Button 
                  variant="outline"
                  className="flex items-center gap-2 h-auto p-4 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/picks')}
                >
                  <Target className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Pick Feed</p>
                    <p className="text-xs opacity-75">Latest picks</p>
                  </div>
                </Button>
                <Button 
                  variant="outline"
                  className="flex items-center gap-2 h-auto p-4 bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  onClick={() => router.push('/sessions')}
                >
                  <Eye className="h-5 w-5" />
                  <div className="text-left">
                    <p className="font-medium">Spectate</p>
                    <p className="text-xs opacity-75">Watch & bet</p>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity Summary */}
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-center gap-3 p-3 border border-slate-600 rounded-lg bg-slate-800/30">
                    {getActivityIcon(activity.type, activity.result)}
                    <div className="flex-1">
                      <p className="font-medium text-white">{activity.title}</p>
                      {activity.game_name && (
                        <p className="text-sm text-slate-300">{activity.game_name}</p>
                      )}
                    </div>
                    <div className="text-right">
                      {activity.result && (
                        <Badge variant="outline" className={getActivityColor(activity.result)}>
                          {activity.result}
                        </Badge>
                      )}
                      {activity.amount && (
                        <p className="text-sm text-slate-300 mt-1">${activity.amount}</p>
                      )}
                    </div>
                    <p className="text-xs text-slate-400">
                      {new Date(activity.created_at).toLocaleDateString()}
                    </p>
                  </div>
                ))}
                {recentActivity.length === 0 && (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-300 mb-2">No gaming activity yet</p>
                    <p className="text-slate-400 text-sm">Start playing to see your activity history here</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="space-y-6">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white font-bold">
                <Zap className="h-5 w-5 text-yellow-400" />
                Active Sessions ({activeSessions?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeSessions?.map((session) => (
                  <Card key={session.id} className="hover:shadow-md transition-shadow bg-slate-800/50 border-slate-600">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-white">{session.session_name}</h3>
                            <Badge 
                              variant="secondary"
                              className={`${getSessionStateColor(session.state)} text-white`}
                            >
                              {getSessionStateLabel(session.state)}
                            </Badge>
                            <Badge variant="outline">
                              {session.user_role}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-slate-300 mb-2">{session.game_name}</p>
                          
                          <div className="flex items-center gap-4 text-sm text-slate-300">
                            <span className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {session.current_participants}/{session.max_participants}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              ${session.entry_fee}
                            </span>
                            {session.scheduled_start_time && (
                              <span className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                {new Date(session.scheduled_start_time).toLocaleTimeString()}
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <Button 
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={() => router.push(`/sessions/${session.slug}`)}
                        >
                          {session.state === 'active' ? 'Join Game' : 'View Session'}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {(!activeSessions || activeSessions.length === 0) && (
                  <div className="text-center py-8">
                    <GamepadIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-300 mb-2">No active gaming sessions</p>
                    <p className="text-slate-400 text-sm mb-4">Join or create a game session to see it here</p>
                    <Button 
                      onClick={() => router.push('/games')}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Browse Games
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="experts" className="space-y-6">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center space-y-4">
                <Star className="h-16 w-16 text-slate-400 mx-auto" />
                <div>
                  <p className="text-white font-medium text-lg mb-2">Expert Picks Coming Soon</p>
                  <p className="text-slate-400 text-sm mb-4">
                    Professional analyst insights and picks will be available here
                  </p>
                  <div className="text-slate-500 text-xs">
                    This feature is currently in development
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/games')}
                  className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                >
                  Explore Games Instead
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Activity History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-3 p-3 border border-slate-600 rounded-lg bg-slate-800/30">
                    {getActivityIcon(activity.type, activity.result)}
                    <div className="flex-1">
                      <p className="font-medium text-white">{activity.title}</p>
                      {activity.game_name && (
                        <p className="text-sm text-slate-300">{activity.game_name}</p>
                      )}
                      <p className="text-xs text-slate-400">
                        {new Date(activity.created_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-right">
                      {activity.result && (
                        <Badge variant="outline" className={getActivityColor(activity.result)}>
                          {activity.result}
                        </Badge>
                      )}
                      {activity.amount && (
                        <p className="text-sm font-medium text-white">
                          {activity.result === 'won' ? '+' : '-'}${activity.amount}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
                
                {recentActivity.length === 0 && (
                  <div className="text-center py-12">
                    <Activity className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-300 mb-2">No gaming history yet</p>
                    <p className="text-slate-400 text-sm mb-6">Your completed games and activities will appear here</p>
                    <Button 
                      onClick={() => router.push('/games')}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Start Your First Game
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Performance Stats */}
            <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white font-bold">
                  <BarChart3 className="h-5 w-5 text-blue-400" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-slate-300">Win Rate</span>
                    <span className="text-white font-medium">{(stats.win_rate * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={stats.win_rate * 100} className="h-2" />
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-3 bg-green-900/30 border border-green-600 rounded-lg">
                    <p className="text-2xl font-bold text-green-400">{stats.sessions_won}</p>
                    <p className="text-sm text-green-300">Wins</p>
                  </div>
                  <div className="p-3 bg-red-900/30 border border-red-600 rounded-lg">
                    <p className="text-2xl font-bold text-red-400">{stats.sessions_lost}</p>
                    <p className="text-sm text-red-300">Losses</p>
                  </div>
                </div>
                
                <div className="text-center p-3 bg-blue-900/30 border border-blue-600 rounded-lg">
                  <p className="text-lg font-bold text-blue-400">{stats.favorite_game}</p>
                  <p className="text-sm text-blue-300">Favorite Game</p>
                </div>
              </CardContent>
            </Card>

            {/* Financial Stats */}
            <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white font-bold">
                  <DollarSign className="h-5 w-5 text-emerald-400" />
                  Financials
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-900/30 border border-green-600 rounded-lg">
                    <p className="text-lg font-bold text-green-400">${stats.total_earnings}</p>
                    <p className="text-sm text-green-300">Total Earnings</p>
                  </div>
                  <div className="text-center p-3 bg-red-900/30 border border-red-600 rounded-lg">
                    <p className="text-lg font-bold text-red-400">${stats.total_spent}</p>
                    <p className="text-sm text-red-300">Total Spent</p>
                  </div>
                </div>
                
                <div className={`text-center p-4 rounded-lg border ${
                  stats.net_profit >= 0 ? 'bg-green-900/30 border-green-600' : 'bg-red-900/30 border-red-600'
                }`}>
                  <div className="flex items-center justify-center gap-2 mb-1">
                    {stats.net_profit >= 0 ? (
                      <TrendingUp className="h-5 w-5 text-green-400" />
                    ) : (
                      <TrendingDown className="h-5 w-5 text-red-400" />
                    )}
                    <p className={`text-2xl font-bold ${
                      stats.net_profit >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      ${Math.abs(stats.net_profit)}
                    </p>
                  </div>
                  <p className={`text-sm ${
                    stats.net_profit >= 0 ? 'text-green-300' : 'text-red-300'
                  }`}>
                    Net {stats.net_profit >= 0 ? 'Profit' : 'Loss'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tournament Stats */}
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white font-bold">
                <Trophy className="h-5 w-5 text-yellow-400" />
                Tournament Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
                  <p className="text-2xl font-bold text-blue-400">{stats.tournaments_joined}</p>
                  <p className="text-sm text-slate-300">Joined</p>
                </div>
                <div className="p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-400">{stats.tournaments_won}</p>
                  <p className="text-sm text-slate-300">Won</p>
                </div>
                <div className="p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
                  <p className="text-2xl font-bold text-purple-400">
                    {stats.tournaments_joined > 0 
                      ? ((stats.tournaments_won / stats.tournaments_joined) * 100).toFixed(1)
                      : 0
                    }%
                  </p>
                  <p className="text-sm text-slate-300">Win Rate</p>
                </div>
                <div className="p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
                  <p className="text-2xl font-bold text-green-400">#{stats.current_rank}</p>
                  <p className="text-sm text-slate-300">Current Rank</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}