'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useTradingStore, usePortfolioSummary } from '@/store/tradingStore';
import { cn } from '@/lib/utils';

interface OrderEntryProps {
  onSubmit: (orderData: OrderFormData) => Promise<void>;
  marketId: string | null;
  disabled?: boolean;
  mobile?: boolean;
  className?: string;
  quantityInputRef?: React.RefObject<HTMLInputElement>;
  priceInputRef?: React.RefObject<HTMLInputElement>;
}

interface OrderFormData {
  side: 'buy' | 'sell';
  order_type: 'market' | 'limit' | 'stop_loss' | 'take_profit';
  quantity: string;
  price?: string;
  time_in_force: string;
}

type OrderSide = 'buy' | 'sell';
type OrderType = 'market' | 'limit' | 'stop_loss' | 'take_profit';

export function OrderEntry({ 
  onSubmit, 
  marketId, 
  disabled = false, 
  mobile = false,
  className,
  quantityInputRef,
  priceInputRef
}: OrderEntryProps) {
  const [side, setSide] = useState<OrderSide>('buy');
  const [orderType, setOrderType] = useState<OrderType>('limit');
  const [quantity, setQuantity] = useState('');
  const [price, setPrice] = useState('');
  const [timeInForce, setTimeInForce] = useState('GTC');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { orderBook, showAdvancedOrderTypes } = useTradingStore();
  const { availableBalance, totalMarginUsed } = usePortfolioSummary();

  // Auto-fill price from order book
  useEffect(() => {
    if (orderType === 'limit' && !price && orderBook) {
      const defaultPrice = side === 'buy' ? orderBook.bestBid : orderBook.bestAsk;
      if (defaultPrice) {
        setPrice(defaultPrice.toString());
      }
    }
  }, [side, orderType, orderBook, price]);

  // Calculate order value and fees
  const orderCalculations = useMemo(() => {
    const qty = parseFloat(quantity) || 0;
    const prc = parseFloat(price) || 0;
    
    let estimatedPrice = prc;
    if (orderType === 'market') {
      estimatedPrice = side === 'buy' ? (orderBook.bestAsk || 0) : (orderBook.bestBid || 0);
    }
    
    const notionalValue = qty * estimatedPrice;
    const estimatedFees = notionalValue * 0.001; // 0.1% fee
    const marginRequired = notionalValue * 0.05; // 5% margin
    const totalCost = side === 'buy' ? notionalValue + estimatedFees : marginRequired;
    
    return {
      notionalValue,
      estimatedFees,
      marginRequired,
      totalCost,
      estimatedPrice
    };
  }, [quantity, price, orderType, side, orderBook]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!marketId) {
      newErrors.market = 'Please select a market';
    }
    
    if (!quantity || parseFloat(quantity) <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }
    
    if (orderType !== 'market' && (!price || parseFloat(price) <= 0)) {
      newErrors.price = 'Price must be greater than 0';
    }
    
    if (orderCalculations.totalCost > availableBalance) {
      newErrors.balance = 'Insufficient balance';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [marketId, quantity, price, orderType, orderCalculations.totalCost, availableBalance]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      const orderData: OrderFormData = {
        side,
        order_type: orderType,
        quantity,
        time_in_force: timeInForce
      };
      
      if (orderType !== 'market') {
        orderData.price = price;
      }
      
      await onSubmit(orderData);
      
      // Reset form on success
      setQuantity('');
      if (orderType === 'market') {
        setPrice('');
      }
      setErrors({});
    } catch (error) {
      console.error('Order submission failed:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to submit order' });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, isSubmitting, side, orderType, quantity, price, timeInForce, onSubmit]);

  // Quick percentage buttons
  const handlePercentageClick = useCallback((percentage: number) => {
    const maxQuantity = availableBalance / (orderCalculations.estimatedPrice || 1);
    const newQuantity = (maxQuantity * percentage / 100).toFixed(2);
    setQuantity(newQuantity);
  }, [availableBalance, orderCalculations.estimatedPrice]);

  // Quick buy/sell market orders
  const handleQuickOrder = useCallback(async (orderSide: OrderSide) => {
    if (!marketId || isSubmitting) return;
    
    const quickQuantity = '10'; // Default quick order size
    const marketPrice = orderSide === 'buy' ? orderBook.bestAsk : orderBook.bestBid;
    
    if (!marketPrice) return;
    
    setIsSubmitting(true);
    
    try {
      await onSubmit({
        side: orderSide,
        order_type: 'market',
        quantity: quickQuantity,
        time_in_force: 'IOC'
      });
    } catch (error) {
      console.error('Quick order failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [marketId, isSubmitting, orderBook, onSubmit]);

  if (!marketId) {
    return (
      <div className={cn("flex items-center justify-center py-8 text-gray-500", className)}>
        <div className="text-center">
          <div className="text-2xl mb-2">💹</div>
          <div className="text-sm">Select a market to start trading</div>
        </div>
      </div>
    );
  }

  if (mobile) {
    return (
      <div className={cn("p-4 space-y-4", className)}>
        {/* Quick Market Orders */}
        <div className="grid grid-cols-2 gap-3">
          <Button
            onClick={() => handleQuickOrder('buy')}
            disabled={disabled || isSubmitting || !orderBook.bestAsk}
            className="bg-green-600 hover:bg-green-700 text-white py-3"
          >
            <div className="text-center">
              <div className="font-bold">BUY MARKET</div>
              <div className="text-xs">@{orderBook.bestAsk?.toFixed(2) || '--'}</div>
            </div>
          </Button>
          
          <Button
            onClick={() => handleQuickOrder('sell')}
            disabled={disabled || isSubmitting || !orderBook.bestBid}
            className="bg-red-600 hover:bg-red-700 text-white py-3"
          >
            <div className="text-center">
              <div className="font-bold">SELL MARKET</div>
              <div className="text-xs">@{orderBook.bestBid?.toFixed(2) || '--'}</div>
            </div>
          </Button>
        </div>

        {/* Mobile Order Form */}
        <Card className="p-4 bg-gray-900 border-gray-800">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Order Type and Side */}
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Side</Label>
                <Select value={side} onValueChange={(value: OrderSide) => setSide(value)}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="buy">Buy</SelectItem>
                    <SelectItem value="sell">Sell</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label className="text-xs">Type</Label>
                <Select value={orderType} onValueChange={(value: OrderType) => setOrderType(value)}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="market">Market</SelectItem>
                    <SelectItem value="limit">Limit</SelectItem>
                    {showAdvancedOrderTypes && (
                      <>
                        <SelectItem value="stop_loss">Stop Loss</SelectItem>
                        <SelectItem value="take_profit">Take Profit</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Quantity */}
            <div>
              <Label className="text-xs">Quantity</Label>
              <Input
                ref={quantityInputRef}
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="bg-gray-800 border-gray-700"
                placeholder="0.00"
                step="0.01"
              />
              {errors.quantity && (
                <div className="text-red-400 text-xs mt-1">{errors.quantity}</div>
              )}
            </div>

            {/* Price (if not market order) */}
            {orderType !== 'market' && (
              <div>
                <Label className="text-xs">Price</Label>
                <Input
                  ref={priceInputRef}
                  type="number"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                  placeholder="0.00"
                  step="0.01"
                />
                {errors.price && (
                  <div className="text-red-400 text-xs mt-1">{errors.price}</div>
                )}
              </div>
            )}

            {/* Order Summary */}
            <div className="text-xs text-gray-400 space-y-1">
              <div className="flex justify-between">
                <span>Est. Value:</span>
                <span>${orderCalculations.notionalValue.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Est. Fees:</span>
                <span>${orderCalculations.estimatedFees.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Total:</span>
                <span>${orderCalculations.totalCost.toFixed(2)}</span>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={disabled || isSubmitting}
              className={cn(
                "w-full py-3",
                side === 'buy' 
                  ? "bg-green-600 hover:bg-green-700" 
                  : "bg-red-600 hover:bg-red-700"
              )}
            >
              {isSubmitting ? 'Submitting...' : `${side.toUpperCase()} ${orderType.toUpperCase()}`}
            </Button>

            {errors.submit && (
              <div className="text-red-400 text-xs text-center">{errors.submit}</div>
            )}
          </form>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Quick Market Orders */}
      <div className="grid grid-cols-2 gap-3">
        <Button
          onClick={() => handleQuickOrder('buy')}
          disabled={disabled || isSubmitting || !orderBook.bestAsk}
          className="bg-green-600 hover:bg-green-700 text-white h-12"
        >
          <div className="text-center">
            <div className="font-bold text-sm">BUY MARKET</div>
            <div className="text-xs opacity-90">@{orderBook.bestAsk?.toFixed(2) || '--'}</div>
          </div>
        </Button>
        
        <Button
          onClick={() => handleQuickOrder('sell')}
          disabled={disabled || isSubmitting || !orderBook.bestBid}
          className="bg-red-600 hover:bg-red-700 text-white h-12"
        >
          <div className="text-center">
            <div className="font-bold text-sm">SELL MARKET</div>
            <div className="text-xs opacity-90">@{orderBook.bestBid?.toFixed(2) || '--'}</div>
          </div>
        </Button>
      </div>

      {/* Advanced Order Form */}
      <Tabs value={side} onValueChange={(value) => setSide(value as OrderSide)}>
        <TabsList className="grid w-full grid-cols-2 bg-gray-800">
          <TabsTrigger value="buy" className="data-[state=active]:bg-green-600">
            Buy
          </TabsTrigger>
          <TabsTrigger value="sell" className="data-[state=active]:bg-red-600">
            Sell
          </TabsTrigger>
        </TabsList>

        <TabsContent value={side} className="space-y-4 mt-4">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Order Type */}
            <div>
              <Label className="text-sm">Order Type</Label>
              <Select value={orderType} onValueChange={(value: OrderType) => setOrderType(value)}>
                <SelectTrigger className="bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="market">Market</SelectItem>
                  <SelectItem value="limit">Limit</SelectItem>
                  {showAdvancedOrderTypes && (
                    <>
                      <SelectItem value="stop_loss">Stop Loss</SelectItem>
                      <SelectItem value="take_profit">Take Profit</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Quantity with percentage buttons */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm">Quantity</Label>
                <div className="flex gap-1">
                  {[25, 50, 75, 100].map((pct) => (
                    <Button
                      key={pct}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handlePercentageClick(pct)}
                      className="text-xs h-6 px-2 bg-gray-800 border-gray-700 hover:bg-gray-700"
                    >
                      {pct}%
                    </Button>
                  ))}
                </div>
              </div>
              <Input
                ref={mobile ? undefined : quantityInputRef}
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="bg-gray-800 border-gray-700"
                placeholder="0.00"
                step="0.01"
              />
              {errors.quantity && (
                <div className="text-red-400 text-xs mt-1">{errors.quantity}</div>
              )}
            </div>

            {/* Price (if not market order) */}
            {orderType !== 'market' && (
              <div>
                <Label className="text-sm">Price</Label>
                <Input
                  ref={mobile ? undefined : priceInputRef}
                  type="number"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                  placeholder="0.00"
                  step="0.01"
                />
                {errors.price && (
                  <div className="text-red-400 text-xs mt-1">{errors.price}</div>
                )}
              </div>
            )}

            {/* Time in Force */}
            <div>
              <Label className="text-sm">Time in Force</Label>
              <Select value={timeInForce} onValueChange={setTimeInForce}>
                <SelectTrigger className="bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GTC">Good Till Cancelled</SelectItem>
                  <SelectItem value="IOC">Immediate or Cancel</SelectItem>
                  <SelectItem value="FOK">Fill or Kill</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Order Summary */}
            <div className="bg-gray-800 rounded-lg p-3 space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Estimated Value:</span>
                <span>${orderCalculations.notionalValue.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Estimated Fees:</span>
                <span>${orderCalculations.estimatedFees.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Margin Required:</span>
                <span>${orderCalculations.marginRequired.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-medium border-t border-gray-700 pt-2">
                <span>Total Cost:</span>
                <span>${orderCalculations.totalCost.toFixed(2)}</span>
              </div>
              
              {/* Balance check */}
              <div className="flex justify-between text-xs">
                <span className="text-gray-400">Available Balance:</span>
                <span className={orderCalculations.totalCost > availableBalance ? 'text-red-400' : 'text-green-400'}>
                  ${availableBalance.toFixed(2)}
                </span>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={disabled || isSubmitting}
              className={cn(
                "w-full h-10",
                side === 'buy' 
                  ? "bg-green-600 hover:bg-green-700" 
                  : "bg-red-600 hover:bg-red-700"
              )}
            >
              {isSubmitting ? 'Submitting...' : `${side.toUpperCase()} ${orderType.toUpperCase()}`}
            </Button>

            {errors.submit && (
              <div className="text-red-400 text-xs text-center">{errors.submit}</div>
            )}
            
            {errors.balance && (
              <div className="text-red-400 text-xs text-center">{errors.balance}</div>
            )}
          </form>
        </TabsContent>
      </Tabs>
    </div>
  );
}