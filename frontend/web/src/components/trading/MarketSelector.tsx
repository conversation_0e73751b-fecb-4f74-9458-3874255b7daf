'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface Market {
  id: string;
  symbol: string;
  name: string;
  category: string;
  baseAsset: string;
  quoteAsset: string;
  lastPrice: number;
  priceChange24h: number;
  priceChangePercent24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  isActive: boolean;
  isFavorite?: boolean;
}

interface MarketSelectorProps {
  selectedMarketId: string | null;
  onMarketSelect: (marketId: string) => void;
  mobile?: boolean;
  className?: string;
}

const MOCK_MARKETS: Market[] = [
  {
    id: 'btc-usd',
    symbol: 'BTC/USD',
    name: 'Bitcoin / US Dollar',
    category: 'crypto',
    baseAsset: 'BTC',
    quoteAsset: 'USD',
    lastPrice: 43250.00,
    priceChange24h: 1250.50,
    priceChangePercent24h: 2.98,
    volume24h: 1850000,
    high24h: 44100.00,
    low24h: 41800.00,
    isActive: true,
    isFavorite: true
  },
  {
    id: 'eth-usd',
    symbol: 'ETH/USD',
    name: 'Ethereum / US Dollar',
    category: 'crypto',
    baseAsset: 'ETH',
    quoteAsset: 'USD',
    lastPrice: 2650.75,
    priceChange24h: -85.25,
    priceChangePercent24h: -3.12,
    volume24h: 950000,
    high24h: 2750.00,
    low24h: 2580.00,
    isActive: true
  },
  {
    id: 'nfl-chiefs-win',
    symbol: 'KC-WIN',
    name: 'Kansas City Chiefs Win Super Bowl',
    category: 'sports',
    baseAsset: 'KC-WIN',
    quoteAsset: 'USD',
    lastPrice: 1.85,
    priceChange24h: 0.05,
    priceChangePercent24h: 2.78,
    volume24h: 125000,
    high24h: 1.92,
    low24h: 1.78,
    isActive: true
  },
  {
    id: 'election-2024',
    symbol: 'PRES-2024',
    name: '2024 Presidential Election',
    category: 'politics',
    baseAsset: 'PRES-2024',
    quoteAsset: 'USD',
    lastPrice: 1.45,
    priceChange24h: -0.12,
    priceChangePercent24h: -7.64,
    volume24h: 78000,
    high24h: 1.65,
    low24h: 1.42,
    isActive: true,
    isFavorite: true
  },
  {
    id: 'weather-nyc',
    symbol: 'NYC-SNOW',
    name: 'NYC Snow This Winter',
    category: 'weather',
    baseAsset: 'NYC-SNOW',
    quoteAsset: 'USD',
    lastPrice: 2.15,
    priceChange24h: 0.25,
    priceChangePercent24h: 13.16,
    volume24h: 45000,
    high24h: 2.20,
    low24h: 1.85,
    isActive: true
  }
];

const CATEGORIES = [
  { value: 'all', label: 'All Markets' },
  { value: 'crypto', label: 'Crypto' },
  { value: 'sports', label: 'Sports' },
  { value: 'politics', label: 'Politics' },
  { value: 'weather', label: 'Weather' }
];

const SORT_OPTIONS = [
  { value: 'volume', label: 'Volume' },
  { value: 'price_change', label: 'Price Change' },
  { value: 'alphabetical', label: 'Alphabetical' }
];

interface MarketRowProps {
  market: Market;
  isSelected: boolean;
  onSelect: (marketId: string) => void;
  mobile?: boolean;
}

function MarketRow({ market, isSelected, onSelect, mobile = false }: MarketRowProps) {
  const priceChangeColor = market.priceChange24h >= 0 ? 'text-green-400' : 'text-red-400';
  
  const handleClick = () => {
    onSelect(market.id);
  };

  if (mobile) {
    return (
      <div
        className={cn(
          "p-3 border-b border-gray-800 cursor-pointer hover:bg-gray-800/50 transition-colors",
          isSelected && "bg-blue-600/20 border-blue-600/40"
        )}
        onClick={handleClick}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{market.symbol}</span>
            {market.isFavorite && (
              <span className="text-yellow-400 text-xs">⭐</span>
            )}
            <Badge variant="outline" className="text-xs">
              {market.category.toUpperCase()}
            </Badge>
          </div>
          <div className="text-right">
            <div className="font-mono text-sm">${market.lastPrice.toFixed(2)}</div>
            <div className={cn("text-xs", priceChangeColor)}>
              {market.priceChangePercent24h > 0 ? '+' : ''}
              {market.priceChangePercent24h.toFixed(2)}%
            </div>
          </div>
        </div>
        
        <div className="text-xs text-gray-400 truncate">
          {market.name}
        </div>
        
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Vol: ${(market.volume24h / 1000).toFixed(0)}K</span>
          <span>24h: ${market.low24h.toFixed(2)} - ${market.high24h.toFixed(2)}</span>
        </div>
      </div>
    );
  }

  return (
    <tr
      className={cn(
        "border-b border-gray-800 cursor-pointer hover:bg-gray-800/50 transition-colors",
        isSelected && "bg-blue-600/20"
      )}
      onClick={handleClick}
    >
      <td className="px-3 py-2">
        <div className="flex items-center gap-2">
          {market.isFavorite && (
            <span className="text-yellow-400 text-xs">⭐</span>
          )}
          <div>
            <div className="font-medium text-sm">{market.symbol}</div>
            <div className="text-xs text-gray-400 truncate max-w-32">
              {market.name}
            </div>
          </div>
        </div>
      </td>
      <td className="px-3 py-2">
        <Badge variant="outline" className="text-xs">
          {market.category.toUpperCase()}
        </Badge>
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        ${market.lastPrice.toFixed(2)}
      </td>
      <td className="px-3 py-2">
        <div className={cn("text-sm font-medium", priceChangeColor)}>
          {market.priceChange24h > 0 ? '+' : ''}${market.priceChange24h.toFixed(2)}
        </div>
        <div className={cn("text-xs", priceChangeColor)}>
          {market.priceChangePercent24h > 0 ? '+' : ''}
          {market.priceChangePercent24h.toFixed(2)}%
        </div>
      </td>
      <td className="px-3 py-2 text-sm font-mono text-gray-400">
        ${(market.volume24h / 1000).toFixed(0)}K
      </td>
      <td className="px-3 py-2 text-xs text-gray-400">
        <div>H: ${market.high24h.toFixed(2)}</div>
        <div>L: ${market.low24h.toFixed(2)}</div>
      </td>
    </tr>
  );
}

export function MarketSelector({ 
  selectedMarketId, 
  onMarketSelect, 
  mobile = false,
  className 
}: MarketSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('volume');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Filter and sort markets
  const filteredAndSortedMarkets = useMemo(() => {
    let filtered = MOCK_MARKETS.filter(market => {
      // Category filter
      if (selectedCategory !== 'all' && market.category !== selectedCategory) {
        return false;
      }
      
      // Search filter
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        return (
          market.symbol.toLowerCase().includes(term) ||
          market.name.toLowerCase().includes(term) ||
          market.baseAsset.toLowerCase().includes(term)
        );
      }
      
      // Favorites filter
      if (showFavoritesOnly && !market.isFavorite) {
        return false;
      }
      
      return market.isActive;
    });

    // Sort markets
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'volume':
          return b.volume24h - a.volume24h;
        case 'price_change':
          return b.priceChangePercent24h - a.priceChangePercent24h;
        case 'alphabetical':
          return a.symbol.localeCompare(b.symbol);
        default:
          return 0;
      }
    });

    return sorted;
  }, [searchTerm, selectedCategory, sortBy, showFavoritesOnly]);

  return (
    <div className={cn("flex flex-col h-full bg-gray-900", className)}>
      {/* Header with search and filters */}
      <div className="p-3 border-b border-gray-800 space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-white">Markets</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            className={cn(
              "h-6 px-2 text-xs",
              showFavoritesOnly && "bg-yellow-600/20 text-yellow-400"
            )}
          >
            ⭐
          </Button>
        </div>

        {/* Search input */}
        <Input
          type="text"
          placeholder="Search markets..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="bg-gray-800 border-gray-700 text-sm h-8"
        />

        {/* Filters */}
        <div className={cn(
          "flex gap-2",
          mobile && "flex-col"
        )}>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="bg-gray-800 border-gray-700 text-xs h-7">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CATEGORIES.map(category => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="bg-gray-800 border-gray-700 text-xs h-7">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Markets list */}
      <div className="flex-1 overflow-y-auto">
        {filteredAndSortedMarkets.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-2xl mb-2">🔍</div>
              <div className="text-sm">No markets found</div>
              <div className="text-xs text-gray-600 mt-1">
                Try adjusting your search or filters
              </div>
            </div>
          </div>
        ) : mobile ? (
          <div>
            {filteredAndSortedMarkets.map((market) => (
              <MarketRow
                key={market.id}
                market={market}
                isSelected={selectedMarketId === market.id}
                onSelect={onMarketSelect}
                mobile
              />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="sticky top-0 bg-gray-800 border-b border-gray-700">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    Market
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    Category
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    Price
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    24h Change
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    Volume
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-400">
                    24h Range
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedMarkets.map((market) => (
                  <MarketRow
                    key={market.id}
                    market={market}
                    isSelected={selectedMarketId === market.id}
                    onSelect={onMarketSelect}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Footer with market count */}
      <div className="p-2 border-t border-gray-800 text-xs text-gray-400">
        <div className="flex justify-between">
          <span>
            Showing {filteredAndSortedMarkets.length} of {MOCK_MARKETS.length} markets
          </span>
          <span>
            {showFavoritesOnly ? 'Favorites' : 'All'}
          </span>
        </div>
      </div>
    </div>
  );
}

export function MarketSelectorSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="flex flex-col h-full bg-gray-900 animate-pulse">
      <div className="p-3 border-b border-gray-800 space-y-2">
        <div className="h-4 bg-gray-700 rounded w-20"></div>
        <div className="h-6 bg-gray-700 rounded"></div>
        <div className="flex gap-2">
          <div className="h-6 bg-gray-700 rounded flex-1"></div>
          <div className="h-6 bg-gray-700 rounded flex-1"></div>
        </div>
      </div>
      
      <div className="flex-1 p-3 space-y-2">
        {Array.from({ length: mobile ? 8 : 12 }).map((_, i) => (
          <div key={i} className="h-16 bg-gray-800 rounded"></div>
        ))}
      </div>
    </div>
  );
}