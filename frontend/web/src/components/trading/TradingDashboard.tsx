'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { MarketSelector } from './MarketSelector';
import { OrderBook } from './OrderBook';
import { TradingChart } from './TradingChart';
import { OrderEntry } from './OrderEntry';
import { PositionsPanel } from './PositionsPanel';
import { OrdersPanel } from './OrdersPanel';
import { MarketTicker } from './MarketTicker';
import { KeyboardShortcutsHelp, KeyboardShortcutsButton } from './KeyboardShortcutsHelp';
import { NotificationContainer, useTradingNotifications } from '@/components/ui/NotificationToast';
import { useTradingWebSocket } from '@/hooks/useTradingWebSocket';
import { useTradingStore } from '@/store/tradingStore';
import { useTradingKeyboardShortcuts, useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { ErrorHandler, handleOrderError } from '@/utils/errorHandler';
import { useNotifications } from '@/contexts/NotificationContext';
import { useScreenReader, useReducedMotion } from '@/hooks/useAccessibility';
import { Card } from '@/components/ui/card';

interface TradingDashboardProps {
  userId: string;
  selectedMarketId: string | null;
  onMarketSelect: (marketId: string) => void;
}

export function TradingDashboard({ 
  userId, 
  selectedMarketId, 
  onMarketSelect 
}: TradingDashboardProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState<'positions' | 'orders'>('positions');
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);
  const [showOrderBook, setShowOrderBook] = useState(true);
  const [showChart, setShowChart] = useState(true);
  
  // Refs for focusing inputs
  const quantityInputRef = useRef<HTMLInputElement>(null);
  const priceInputRef = useRef<HTMLInputElement>(null);
  
  // Notification systems
  const { addNotification } = useNotifications();
  const tradingNotifications = useTradingNotifications();
  
  // Accessibility
  const { announce, announceOrder, announcePrice } = useScreenReader();
  const prefersReducedMotion = useReducedMotion();
  
  const {
    connected,
    subscribeToMarket,
    unsubscribeFromMarket,
    submitOrder
  } = useTradingWebSocket(userId);

  const {
    orderBook,
    positions,
    orders,
    marketData,
    setSelectedMarket,
    cancelAllOrders,
    closeAllPositions
  } = useTradingStore();

  // Available markets for navigation
  const availableMarkets = ['btc-usd', 'eth-usd', 'nfl-chiefs-win', 'election-2024', 'weather-nyc'];

  // Handle connection status changes
  useEffect(() => {
    if (connected) {
      tradingNotifications.connectionRestored();
    } else {
      tradingNotifications.connectionLost();
    }
  }, [connected, tradingNotifications]);

  // Responsive layout detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Handle market selection
  const handleMarketSelect = useCallback((marketId: string) => {
    // Unsubscribe from previous market
    if (selectedMarketId) {
      unsubscribeFromMarket(selectedMarketId);
    }
    
    // Subscribe to new market
    subscribeToMarket(marketId);
    setSelectedMarket(marketId);
    onMarketSelect(marketId);
  }, [selectedMarketId, subscribeToMarket, unsubscribeFromMarket, setSelectedMarket, onMarketSelect]);

  // Handle order submission
  const handleOrderSubmit = useCallback(async (orderData: any) => {
    if (!selectedMarketId) return;
    
    try {
      const result = await submitOrder({
        ...orderData,
        market_id: selectedMarketId
      });
      
      // Show success notification
      tradingNotifications.orderSuccess(
        result.orderId || 'unknown',
        orderData.side,
        parseFloat(orderData.quantity),
        selectedMarketId
      );
      
      // Announce to screen readers
      announceOrder(
        orderData.side,
        parseFloat(orderData.quantity),
        selectedMarketId,
        'submitted successfully'
      );
    } catch (error) {
      // Handle and show error notification
      const errorDetails = handleOrderError(error, orderData);
      const notification = ErrorHandler.toNotification(errorDetails);
      addNotification(notification);
    }
  }, [selectedMarketId, submitOrder, tradingNotifications, addNotification]);

  // Keyboard shortcut handlers
  const handleBuyMarket = useCallback(() => {
    if (!selectedMarketId || !connected) return;
    handleOrderSubmit({
      side: 'buy',
      order_type: 'market',
      quantity: '10',
      time_in_force: 'IOC'
    });
  }, [selectedMarketId, connected, handleOrderSubmit]);

  const handleSellMarket = useCallback(() => {
    if (!selectedMarketId || !connected) return;
    handleOrderSubmit({
      side: 'sell',
      order_type: 'market',
      quantity: '10',
      time_in_force: 'IOC'
    });
  }, [selectedMarketId, connected, handleOrderSubmit]);

  const handleBuyLimit = useCallback(() => {
    if (!selectedMarketId || !connected || !orderBook.bestBid) return;
    handleOrderSubmit({
      side: 'buy',
      order_type: 'limit',
      quantity: '10',
      price: orderBook.bestBid.toString(),
      time_in_force: 'GTC'
    });
  }, [selectedMarketId, connected, orderBook.bestBid, handleOrderSubmit]);

  const handleSellLimit = useCallback(() => {
    if (!selectedMarketId || !connected || !orderBook.bestAsk) return;
    handleOrderSubmit({
      side: 'sell',
      order_type: 'limit',
      quantity: '10',
      price: orderBook.bestAsk.toString(),
      time_in_force: 'GTC'
    });
  }, [selectedMarketId, connected, orderBook.bestAsk, handleOrderSubmit]);

  const handleSwitchMarket = useCallback((direction: 'next' | 'prev') => {
    if (!selectedMarketId) return;
    
    const currentIndex = availableMarkets.indexOf(selectedMarketId);
    if (currentIndex === -1) return;
    
    let newIndex;
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % availableMarkets.length;
    } else {
      newIndex = currentIndex === 0 ? availableMarkets.length - 1 : currentIndex - 1;
    }
    
    handleMarketSelect(availableMarkets[newIndex]);
  }, [selectedMarketId, availableMarkets, handleMarketSelect]);

  const handleToggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  }, []);

  const handleFocusQuantity = useCallback(() => {
    quantityInputRef.current?.focus();
  }, []);

  const handleFocusPrice = useCallback(() => {
    priceInputRef.current?.focus();
  }, []);

  const handleCancelAllOrders = useCallback(async () => {
    if (orders.length === 0) return;
    try {
      await cancelAllOrders();
      tradingNotifications.info(
        'Orders Cancelled',
        `${orders.length} order(s) cancelled successfully`
      );
    } catch (error) {
      const errorDetails = ErrorHandler.handleError(error, 'CANCEL_ALL_ORDERS');
      const notification = ErrorHandler.toNotification(errorDetails);
      addNotification(notification);
    }
  }, [orders.length, cancelAllOrders, tradingNotifications, addNotification]);

  const handleCloseAllPositions = useCallback(async () => {
    if (positions.length === 0) return;
    try {
      await closeAllPositions();
      tradingNotifications.info(
        'Positions Closed',
        `${positions.length} position(s) closed successfully`
      );
    } catch (error) {
      const errorDetails = ErrorHandler.handleError(error, 'CLOSE_ALL_POSITIONS');
      const notification = ErrorHandler.toNotification(errorDetails);
      addNotification(notification);
    }
  }, [positions.length, closeAllPositions, tradingNotifications, addNotification]);

  // Help shortcut
  useKeyboardShortcuts([
    {
      key: '?',
      description: 'Show keyboard shortcuts help',
      handler: () => setShowKeyboardHelp(true)
    }
  ]);

  // Trading keyboard shortcuts
  useTradingKeyboardShortcuts({
    onBuyMarket: handleBuyMarket,
    onSellMarket: handleSellMarket,
    onBuyLimit: handleBuyLimit,
    onSellLimit: handleSellLimit,
    onCancelAllOrders: handleCancelAllOrders,
    onCloseAllPositions: handleCloseAllPositions,
    onToggleOrderBook: () => setShowOrderBook(!showOrderBook),
    onToggleChart: () => setShowChart(!showChart),
    onFocusQuantity: handleFocusQuantity,
    onFocusPrice: handleFocusPrice,
    onSwitchMarket: handleSwitchMarket,
    onToggleFullscreen: handleToggleFullscreen,
    enabled: !isMobile // Disable shortcuts on mobile
  });

  if (isMobile) {
    return <MobileTradingDashboard 
      userId={userId}
      selectedMarketId={selectedMarketId}
      onMarketSelect={handleMarketSelect}
      onOrderSubmit={handleOrderSubmit}
      connected={connected}
      activeTab={activeTab}
      setActiveTab={setActiveTab}
    />;
  }

  return (
    <div className="h-screen flex flex-col bg-black text-white" role="main" aria-label="Trading Dashboard">
      {/* Market Ticker Header */}
      <header role="banner" aria-label="Market Information">
        <MarketTicker marketId={selectedMarketId} />
      </header>
      
      {/* Connection Status */}
      <div className="flex items-center justify-end p-2 border-b border-gray-800" role="status" aria-live="polite">
        <div className="flex items-center gap-2">
          <div 
            className={`h-2 w-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`}
            aria-hidden="true"
          ></div>
          <span className="text-sm text-gray-400">
            {connected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Main Content Grid */}
      <main className="flex-1 grid grid-cols-12 gap-4 p-4 overflow-hidden" role="application" aria-label="Trading Interface">
        {/* Left Panel - Market List */}
        <aside className="col-span-2" role="complementary" aria-label="Market Selection">
          <Card className="bg-gray-900 border-gray-800 overflow-y-auto h-full">
            <MarketSelector 
              selectedMarketId={selectedMarketId}
              onMarketSelect={handleMarketSelect}
            />
          </Card>
        </aside>

        {/* Center Panel - Chart and Order Entry */}
        <section className="col-span-7 flex flex-col gap-4" aria-label="Trading and Chart">
          {/* Trading Chart */}
          <Card className="flex-1 bg-gray-900 border-gray-800 p-0" role="img" aria-label="Trading Chart">
            <TradingChart marketId={selectedMarketId} />
          </Card>
          
          {/* Order Entry */}
          <Card className="bg-gray-900 border-gray-800 p-4" role="form" aria-label="Order Entry">
            <OrderEntry 
              onSubmit={handleOrderSubmit}
              marketId={selectedMarketId}
              disabled={!connected || !selectedMarketId}
              quantityInputRef={quantityInputRef}
              priceInputRef={priceInputRef}
            />
          </Card>
        </section>

        {/* Right Panel - Order Book and Positions */}
        <aside className="col-span-3 flex flex-col gap-4" role="complementary" aria-label="Order Book and Portfolio">
          {/* Order Book */}
          {showOrderBook && (
            <Card className="flex-1 bg-gray-900 border-gray-800 p-0" role="table" aria-label="Order Book">
              <OrderBook 
                marketId={selectedMarketId}
                orderBook={orderBook}
                onPriceClick={(price) => {
                  announcePrice(price, selectedMarketId || 'market');
                  // Auto-fill order form with clicked price
                  // TODO: Implement price click handler
                }}
              />
            </Card>
          )}
          
          {/* Positions and Orders */}
          <Card className="flex-1 bg-gray-900 border-gray-800 p-0" role="tabpanel" aria-label="Portfolio Management">
            <div className="flex border-b border-gray-800" role="tablist" aria-label="Portfolio Tabs">
              <button
                role="tab"
                aria-selected={activeTab === 'positions'}
                aria-controls="positions-panel"
                className={`flex-1 py-2 px-4 text-sm font-medium ${
                  activeTab === 'positions' 
                    ? 'text-white border-b-2 border-green-500' 
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab('positions')}
              >
                Positions
              </button>
              <button
                role="tab"
                aria-selected={activeTab === 'orders'}
                aria-controls="orders-panel"
                className={`flex-1 py-2 px-4 text-sm font-medium ${
                  activeTab === 'orders' 
                    ? 'text-white border-b-2 border-green-500' 
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab('orders')}
              >
                Orders
              </button>
            </div>
            
            <div className="flex-1 overflow-hidden">
              {activeTab === 'positions' ? (
                <div id="positions-panel" role="tabpanel" aria-labelledby="positions-tab">
                  <PositionsPanel positions={positions} />
                </div>
              ) : (
                <div id="orders-panel" role="tabpanel" aria-labelledby="orders-tab">
                  <OrdersPanel orders={orders} />
                </div>
              )}
            </div>
          </Card>
        </aside>
      </main>

      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp 
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
        mobile={isMobile}
      />

      {/* Floating Shortcuts Button */}
      {!isMobile && (
        <KeyboardShortcutsButton
          onClick={() => setShowKeyboardHelp(true)}
        />
      )}

      {/* Notification Container */}
      <NotificationContainer mobile={isMobile} />
    </div>
  );
}

// Mobile-optimized trading dashboard
function MobileTradingDashboard({ 
  userId,
  selectedMarketId,
  onMarketSelect,
  onOrderSubmit,
  connected,
  activeTab,
  setActiveTab
}: TradingDashboardProps & {
  onOrderSubmit: (orderData: any) => void;
  connected: boolean;
  activeTab: 'positions' | 'orders';
  setActiveTab: (tab: 'positions' | 'orders') => void;
}) {
  const [mobileView, setMobileView] = useState<'markets' | 'chart' | 'orderbook' | 'trading'>('chart');
  
  return (
    <div className="h-screen flex flex-col bg-black text-white">
      {/* Mobile Header */}
      <MarketTicker marketId={selectedMarketId} mobile />
      
      <div className="flex items-center justify-end p-2 border-b border-gray-800">
        <div className={`h-2 w-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
      </div>

      {/* Mobile Content */}
      <div className="flex-1 flex flex-col">
        {/* Mobile Navigation Tabs */}
        <div className="flex border-b border-gray-800">
          <button
            className={`flex-1 py-2 text-xs ${
              mobileView === 'markets' ? 'bg-gray-800 text-white' : 'text-gray-400'
            }`}
            onClick={() => setMobileView('markets')}
          >
            Markets
          </button>
          <button
            className={`flex-1 py-2 text-xs ${
              mobileView === 'chart' ? 'bg-gray-800 text-white' : 'text-gray-400'
            }`}
            onClick={() => setMobileView('chart')}
          >
            Chart
          </button>
          <button
            className={`flex-1 py-2 text-xs ${
              mobileView === 'orderbook' ? 'bg-gray-800 text-white' : 'text-gray-400'
            }`}
            onClick={() => setMobileView('orderbook')}
          >
            Book
          </button>
          <button
            className={`flex-1 py-2 text-xs ${
              mobileView === 'trading' ? 'bg-gray-800 text-white' : 'text-gray-400'
            }`}
            onClick={() => setMobileView('trading')}
          >
            Trade
          </button>
        </div>

        {/* Mobile View Content */}
        <div className="flex-1 overflow-hidden">
          {mobileView === 'markets' && (
            <MarketSelector 
              selectedMarketId={selectedMarketId}
              onMarketSelect={onMarketSelect}
              mobile
            />
          )}
          
          {mobileView === 'chart' && (
            <TradingChart marketId={selectedMarketId} mobile />
          )}
          
          {mobileView === 'orderbook' && (
            <OrderBook 
              marketId={selectedMarketId}
              orderBook={useTradingStore().orderBook}
              mobile
            />
          )}
          
          {mobileView === 'trading' && (
            <div className="p-4">
              <OrderEntry 
                onSubmit={onOrderSubmit}
                marketId={selectedMarketId}
                disabled={!connected || !selectedMarketId}
                mobile
              />
            </div>
          )}
        </div>

        {/* Mobile Bottom Panel */}
        <div className="border-t border-gray-800">
          <div className="flex">
            <button
              className={`flex-1 py-2 text-sm ${
                activeTab === 'positions' ? 'bg-gray-800 text-white' : 'text-gray-400'
              }`}
              onClick={() => setActiveTab('positions')}
            >
              Positions
            </button>
            <button
              className={`flex-1 py-2 text-sm ${
                activeTab === 'orders' ? 'bg-gray-800 text-white' : 'text-gray-400'
              }`}
              onClick={() => setActiveTab('orders')}
            >
              Orders
            </button>
          </div>
          
          <div className="h-48 overflow-y-auto">
            {activeTab === 'positions' ? (
              <PositionsPanel positions={useTradingStore().positions} mobile />
            ) : (
              <OrdersPanel orders={useTradingStore().orders} mobile />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}