'use client';

import React, { memo, useMemo, useCallback, useState } from 'react';
import { OrderBook as OrderBookType, PriceLevel } from '@/store/tradingStore';
import { useVirtualList, usePerformanceMonitor, useThrottled } from '@/hooks/usePerformanceOptimizations';
import { cn } from '@/lib/utils';

interface OrderBookProps {
  marketId: string | null;
  orderBook: OrderBookType;
  onPriceClick?: (price: number, side: 'buy' | 'sell') => void;
  mobile?: boolean;
  className?: string;
}

interface PriceLevelRowProps {
  level: PriceLevel;
  side: 'bid' | 'ask';
  maxQuantity: number;
  onPriceClick?: (price: number, side: 'buy' | 'sell') => void;
  mobile?: boolean;
}

// Memoized price level row component for performance
const PriceLevelRow = memo<PriceLevelRowProps>(({ 
  level, 
  side, 
  maxQuantity, 
  onPriceClick,
  mobile = false 
}) => {
  const intensityPercentage = maxQuantity > 0 ? (level.quantity / maxQuantity) * 100 : 0;
  const isBid = side === 'bid';
  
  const handleClick = useCallback(() => {
    onPriceClick?.(level.price, isBid ? 'buy' : 'sell');
  }, [level.price, isBid, onPriceClick]);

  return (
    <div
      className={cn(
        "relative flex items-center h-6 cursor-pointer hover:bg-gray-700/50 transition-colors",
        mobile && "h-8"
      )}
      onClick={handleClick}
    >
      {/* Background intensity bar */}
      <div
        className={cn(
          "absolute inset-0 transition-all duration-200",
          isBid 
            ? "bg-green-500/20 border-r-2 border-green-500/40" 
            : "bg-red-500/20 border-l-2 border-red-500/40"
        )}
        style={{
          width: `${intensityPercentage}%`,
          [isBid ? 'left' : 'right']: 0
        }}
      />
      
      {/* Content */}
      <div className={cn(
        "relative z-10 flex items-center justify-between w-full px-2 text-xs font-mono",
        mobile && "text-sm px-3"
      )}>
        {isBid ? (
          <>
            <span className="text-green-400 font-medium">
              {level.price.toFixed(2)}
            </span>
            <span className="text-gray-300">
              {level.quantity.toLocaleString()}
            </span>
          </>
        ) : (
          <>
            <span className="text-gray-300">
              {level.quantity.toLocaleString()}
            </span>
            <span className="text-red-400 font-medium">
              {level.price.toFixed(2)}
            </span>
          </>
        )}
      </div>
    </div>
  );
});

PriceLevelRow.displayName = 'PriceLevelRow';

export function OrderBook({ 
  marketId, 
  orderBook, 
  onPriceClick, 
  mobile = false,
  className 
}: OrderBookProps) {
  const [aggregationLevel, setAggregationLevel] = useState<number>(0.01);
  const [maxDepth, setMaxDepth] = useState<number>(mobile ? 10 : 15);
  
  // Performance monitoring
  usePerformanceMonitor('OrderBook');
  
  // Throttle price clicks to prevent spam
  const throttledPriceClick = useThrottled(onPriceClick || (() => {}), 100);

  // Process and aggregate order book data
  const { processedBids, processedAsks, maxQuantity } = useMemo(() => {
    if (!orderBook.bids.length && !orderBook.asks.length) {
      return { processedBids: [], processedAsks: [], maxQuantity: 0 };
    }

    // Aggregate levels based on aggregation setting
    const aggregateLevels = (levels: PriceLevel[]): PriceLevel[] => {
      if (aggregationLevel <= 0) return levels;
      
      const aggregated = new Map<number, PriceLevel>();
      
      levels.forEach(level => {
        const aggregatedPrice = Math.floor(level.price / aggregationLevel) * aggregationLevel;
        
        if (aggregated.has(aggregatedPrice)) {
          const existing = aggregated.get(aggregatedPrice)!;
          existing.quantity += level.quantity;
          existing.orderCount += level.orderCount;
        } else {
          aggregated.set(aggregatedPrice, {
            price: aggregatedPrice,
            quantity: level.quantity,
            orderCount: level.orderCount
          });
        }
      });
      
      return Array.from(aggregated.values());
    };

    // Sort and limit depth
    const sortedBids = aggregateLevels(orderBook.bids)
      .sort((a, b) => b.price - a.price)
      .slice(0, maxDepth);
      
    const sortedAsks = aggregateLevels(orderBook.asks)
      .sort((a, b) => a.price - b.price)
      .slice(0, maxDepth);

    // Calculate max quantity for intensity visualization
    const allQuantities = [...sortedBids, ...sortedAsks].map(level => level.quantity);
    const maxQty = Math.max(...allQuantities, 1);

    return {
      processedBids: sortedBids,
      processedAsks: sortedAsks,
      maxQuantity: maxQty
    };
  }, [orderBook.bids, orderBook.asks, aggregationLevel, maxDepth]);

  // Calculate spread
  const spread = orderBook.bestAsk && orderBook.bestBid 
    ? orderBook.bestAsk - orderBook.bestBid 
    : null;
    
  const spreadPercentage = spread && orderBook.bestBid 
    ? (spread / orderBook.bestBid) * 100 
    : null;

  if (!marketId) {
    return (
      <div className={cn("flex items-center justify-center h-full text-gray-500", className)}>
        <div className="text-center">
          <div className="text-lg mb-2">📊</div>
          <div className="text-sm">Select a market to view order book</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-gray-900", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-800">
        <h3 className="text-sm font-medium text-white">Order Book</h3>
        
        <div className="flex items-center gap-2">
          {/* Aggregation selector */}
          <select
            value={aggregationLevel}
            onChange={(e) => setAggregationLevel(parseFloat(e.target.value))}
            className="bg-gray-800 text-white text-xs rounded px-2 py-1 border border-gray-700"
          >
            <option value={0.01}>0.01</option>
            <option value={0.1}>0.1</option>
            <option value={1}>1.0</option>
          </select>
          
          {/* Depth selector */}
          {!mobile && (
            <select
              value={maxDepth}
              onChange={(e) => setMaxDepth(parseInt(e.target.value))}
              className="bg-gray-800 text-white text-xs rounded px-2 py-1 border border-gray-700"
            >
              <option value={10}>10</option>
              <option value={15}>15</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          )}
        </div>
      </div>

      {/* Column headers */}
      <div className={cn(
        "flex items-center justify-between px-2 py-1 bg-gray-800 text-xs text-gray-400 font-medium",
        mobile && "px-3 text-sm"
      )}>
        <span>Price</span>
        <span>Size</span>
      </div>

      {/* Order book content */}
      <div className="flex-1 overflow-hidden">
        {/* Asks (sell orders) */}
        <div className="h-1/2 overflow-y-auto flex flex-col-reverse">
          {processedAsks.map((level, index) => (
            <PriceLevelRow
              key={`ask-${level.price}-${index}`}
              level={level}
              side="ask"
              maxQuantity={maxQuantity}
              onPriceClick={throttledPriceClick}
              mobile={mobile}
            />
          ))}
        </div>

        {/* Spread display */}
        <div className={cn(
          "flex items-center justify-center py-2 bg-gray-800 border-y border-gray-700",
          mobile && "py-3"
        )}>
          {spread !== null ? (
            <div className="text-center">
              <div className={cn("text-yellow-400 font-mono font-medium", mobile ? "text-sm" : "text-xs")}>
                Spread: {spread.toFixed(2)}
              </div>
              {spreadPercentage !== null && (
                <div className={cn("text-gray-400", mobile ? "text-xs" : "text-[10px]")}>
                  ({spreadPercentage.toFixed(2)}%)
                </div>
              )}
            </div>
          ) : (
            <div className={cn("text-gray-500", mobile ? "text-sm" : "text-xs")}>
              No spread data
            </div>
          )}
        </div>

        {/* Bids (buy orders) */}
        <div className="h-1/2 overflow-y-auto">
          {processedBids.map((level, index) => (
            <PriceLevelRow
              key={`bid-${level.price}-${index}`}
              level={level}
              side="bid"
              maxQuantity={maxQuantity}
              onPriceClick={throttledPriceClick}
              mobile={mobile}
            />
          ))}
        </div>
      </div>

      {/* Footer with summary */}
      <div className={cn(
        "p-2 border-t border-gray-800 text-xs text-gray-400",
        mobile && "p-3 text-sm"
      )}>
        <div className="flex justify-between">
          <span>Best Bid: {orderBook.bestBid?.toFixed(2) || '--'}</span>
          <span>Best Ask: {orderBook.bestAsk?.toFixed(2) || '--'}</span>
        </div>
      </div>
    </div>
  );
}

// Loading state component
export function OrderBookSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="flex flex-col h-full bg-gray-900 animate-pulse">
      {/* Header skeleton */}
      <div className="p-3 border-b border-gray-800">
        <div className="h-4 bg-gray-700 rounded w-24"></div>
      </div>
      
      {/* Content skeleton */}
      <div className="flex-1 p-2 space-y-1">
        {Array.from({ length: mobile ? 10 : 15 }).map((_, i) => (
          <div key={i} className="flex justify-between">
            <div className="h-3 bg-gray-700 rounded w-16"></div>
            <div className="h-3 bg-gray-700 rounded w-20"></div>
          </div>
        ))}
      </div>
    </div>
  );
}