'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useShortcutHelp } from '@/hooks/useKeyboardShortcuts';
import { cn } from '@/lib/utils';

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
  mobile?: boolean;
}

export function KeyboardShortcutsHelp({ 
  isOpen, 
  onClose, 
  mobile = false 
}: KeyboardShortcutsHelpProps) {
  const shortcuts = useShortcutHelp();

  if (!isOpen) return null;

  const categories = [
    {
      title: 'Trading Actions',
      shortcuts: shortcuts.slice(0, 4)
    },
    {
      title: 'Order Management',
      shortcuts: shortcuts.slice(4, 6)
    },
    {
      title: 'Navigation',
      shortcuts: shortcuts.slice(6, 13)
    },
    {
      title: 'Help',
      shortcuts: shortcuts.slice(13)
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className={cn(
        "bg-gray-900 border-gray-700 max-h-[80vh] overflow-y-auto",
        mobile ? "w-full max-w-sm" : "w-full max-w-2xl"
      )}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-white">
              Keyboard Shortcuts
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </Button>
          </div>

          <div className={cn(
            "space-y-6",
            mobile ? "space-y-4" : ""
          )}>
            {categories.map((category) => (
              <div key={category.title}>
                <h3 className={cn(
                  "font-medium text-white mb-3",
                  mobile ? "text-sm" : "text-base"
                )}>
                  {category.title}
                </h3>
                <div className="space-y-2">
                  {category.shortcuts.map((shortcut, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-1"
                    >
                      <span className={cn(
                        "text-gray-300",
                        mobile ? "text-sm" : "text-base"
                      )}>
                        {shortcut.description}
                      </span>
                      <Badge 
                        variant="outline" 
                        className={cn(
                          "bg-gray-800 border-gray-600 text-gray-300 font-mono",
                          mobile ? "text-xs px-2 py-1" : "text-sm px-3 py-1"
                        )}
                      >
                        {shortcut.keys}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700">
            <p className={cn(
              "text-gray-400 text-center",
              mobile ? "text-xs" : "text-sm"
            )}>
              Press <Badge variant="outline" className="mx-1 bg-gray-800 border-gray-600 text-xs">?</Badge> 
              anytime to show this help
            </p>
          </div>

          <div className="mt-4 flex justify-center">
            <Button
              onClick={onClose}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Got it
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

// Floating help button component
export function KeyboardShortcutsButton({ 
  onClick, 
  mobile = false 
}: { 
  onClick: () => void; 
  mobile?: boolean; 
}) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={cn(
        "fixed bottom-4 right-4 bg-gray-800 hover:bg-gray-700 text-gray-300 border border-gray-600 shadow-lg",
        mobile ? "h-10 w-10 p-0" : "h-8 px-3"
      )}
      title="Keyboard Shortcuts (Press ?)"
    >
      {mobile ? "?" : "Shortcuts"}
    </Button>
  );
}