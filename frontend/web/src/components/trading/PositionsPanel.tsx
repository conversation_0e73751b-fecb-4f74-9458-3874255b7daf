'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Position } from '@/store/tradingStore';
import { cn } from '@/lib/utils';

interface PositionsPanelProps {
  positions: Position[];
  mobile?: boolean;
  className?: string;
}

interface PositionRowProps {
  position: Position;
  onClose: (positionId: string) => void;
  mobile?: boolean;
}

function PositionRow({ position, onClose, mobile = false }: PositionRowProps) {
  const [isClosing, setIsClosing] = useState(false);
  
  const handleClose = async () => {
    setIsClosing(true);
    try {
      await onClose(position.id);
    } catch (error) {
      console.error('Failed to close position:', error);
    } finally {
      setIsClosing(false);
    }
  };

  const pnlColor = position.unrealizedPnl >= 0 ? 'text-green-400' : 'text-red-400';
  const sideColor = position.side === 'LONG' ? 'text-green-400' : 'text-red-400';

  if (mobile) {
    return (
      <div className="p-3 border-b border-gray-800 last:border-b-0">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{position.marketName}</span>
            <Badge variant="outline" className={cn("text-xs", sideColor)}>
              {position.side}
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleClose}
            disabled={isClosing}
            className="h-6 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
          >
            {isClosing ? '...' : 'Close'}
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-400">Qty:</span>
            <span className="ml-1">{position.quantity.toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-400">Avg:</span>
            <span className="ml-1">{position.averagePrice.toFixed(2)}</span>
          </div>
          <div>
            <span className="text-gray-400">Current:</span>
            <span className="ml-1">{position.currentPrice.toFixed(2)}</span>
          </div>
          <div>
            <span className="text-gray-400">P&L:</span>
            <span className={cn("ml-1 font-medium", pnlColor)}>
              ${position.unrealizedPnl.toFixed(2)}
            </span>
          </div>
        </div>
        
        <div className="mt-2 text-xs">
          <span className="text-gray-400">P&L %:</span>
          <span className={cn("ml-1 font-medium", pnlColor)}>
            {position.unrealizedPnlPercentage > 0 ? '+' : ''}
            {position.unrealizedPnlPercentage.toFixed(2)}%
          </span>
        </div>
      </div>
    );
  }

  return (
    <tr className="border-b border-gray-800 hover:bg-gray-800/50">
      <td className="px-3 py-2">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{position.marketName}</span>
          <Badge variant="outline" className={cn("text-xs", sideColor)}>
            {position.side}
          </Badge>
        </div>
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {position.quantity.toLocaleString()}
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {position.averagePrice.toFixed(2)}
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {position.currentPrice.toFixed(2)}
      </td>
      <td className="px-3 py-2">
        <div className={cn("text-sm font-medium", pnlColor)}>
          ${position.unrealizedPnl.toFixed(2)}
        </div>
        <div className={cn("text-xs", pnlColor)}>
          {position.unrealizedPnlPercentage > 0 ? '+' : ''}
          {position.unrealizedPnlPercentage.toFixed(2)}%
        </div>
      </td>
      <td className="px-3 py-2 text-sm font-mono text-gray-400">
        ${position.marginUsed.toFixed(2)}
      </td>
      <td className="px-3 py-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleClose}
          disabled={isClosing}
          className="h-7 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
        >
          {isClosing ? '...' : 'Close'}
        </Button>
      </td>
    </tr>
  );
}

export function PositionsPanel({ positions, mobile = false, className }: PositionsPanelProps) {
  const [sortBy, setSortBy] = useState<'market' | 'pnl' | 'size'>('pnl');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Portfolio summary calculations
  const portfolioSummary = useMemo(() => {
    const totalUnrealizedPnl = positions.reduce((sum, pos) => sum + pos.unrealizedPnl, 0);
    const totalMarginUsed = positions.reduce((sum, pos) => sum + pos.marginUsed, 0);
    const totalPositions = positions.length;
    const profitablePositions = positions.filter(pos => pos.unrealizedPnl > 0).length;
    
    return {
      totalUnrealizedPnl,
      totalMarginUsed,
      totalPositions,
      profitablePositions,
      winRate: totalPositions > 0 ? (profitablePositions / totalPositions) * 100 : 0
    };
  }, [positions]);

  // Sort positions
  const sortedPositions = useMemo(() => {
    const sorted = [...positions].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'market':
          comparison = a.marketName.localeCompare(b.marketName);
          break;
        case 'pnl':
          comparison = a.unrealizedPnl - b.unrealizedPnl;
          break;
        case 'size':
          comparison = a.quantity - b.quantity;
          break;
      }
      
      return sortDirection === 'desc' ? -comparison : comparison;
    });
    
    return sorted;
  }, [positions, sortBy, sortDirection]);

  const handleSort = (field: 'market' | 'pnl' | 'size') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('desc');
    }
  };

  const handleClosePosition = async (positionId: string) => {
    // In real implementation, this would call the API to close the position
    console.log('Closing position:', positionId);
    
    // Mock API call
    return new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
  };

  const handleCloseAllPositions = async () => {
    // In real implementation, this would close all positions
    console.log('Closing all positions');
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header with portfolio summary */}
      <div className="p-3 border-b border-gray-800">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-white">
            Positions ({positions.length})
          </h3>
          
          {positions.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCloseAllPositions}
              className="h-6 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
            >
              Close All
            </Button>
          )}
        </div>
        
        {/* Portfolio Summary */}
        <div className={cn(
          "grid gap-2 text-xs",
          mobile ? "grid-cols-2" : "grid-cols-4"
        )}>
          <div>
            <span className="text-gray-400">Total P&L:</span>
            <div className={cn(
              "font-medium",
              portfolioSummary.totalUnrealizedPnl >= 0 ? 'text-green-400' : 'text-red-400'
            )}>
              ${portfolioSummary.totalUnrealizedPnl.toFixed(2)}
            </div>
          </div>
          
          <div>
            <span className="text-gray-400">Margin Used:</span>
            <div className="font-medium text-white">
              ${portfolioSummary.totalMarginUsed.toFixed(2)}
            </div>
          </div>
          
          {!mobile && (
            <>
              <div>
                <span className="text-gray-400">Positions:</span>
                <div className="font-medium text-white">
                  {portfolioSummary.totalPositions}
                </div>
              </div>
              
              <div>
                <span className="text-gray-400">Win Rate:</span>
                <div className="font-medium text-white">
                  {portfolioSummary.winRate.toFixed(1)}%
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Positions List */}
      <div className="flex-1 overflow-y-auto">
        {positions.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-2xl mb-2">📊</div>
              <div className="text-sm">No open positions</div>
              <div className="text-xs text-gray-600 mt-1">
                Place a trade to see your positions here
              </div>
            </div>
          </div>
        ) : mobile ? (
          <div>
            {sortedPositions.map((position) => (
              <PositionRow
                key={position.id}
                position={position}
                onClose={handleClosePosition}
                mobile
              />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="sticky top-0 bg-gray-800 border-b border-gray-700">
                <tr>
                  <th 
                    className="px-3 py-2 text-left cursor-pointer hover:bg-gray-700"
                    onClick={() => handleSort('market')}
                  >
                    <div className="flex items-center gap-1">
                      Market
                      {sortBy === 'market' && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th 
                    className="px-3 py-2 text-left cursor-pointer hover:bg-gray-700"
                    onClick={() => handleSort('size')}
                  >
                    <div className="flex items-center gap-1">
                      Size
                      {sortBy === 'size' && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left">Avg Price</th>
                  <th className="px-3 py-2 text-left">Current</th>
                  <th 
                    className="px-3 py-2 text-left cursor-pointer hover:bg-gray-700"
                    onClick={() => handleSort('pnl')}
                  >
                    <div className="flex items-center gap-1">
                      P&L
                      {sortBy === 'pnl' && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left">Margin</th>
                  <th className="px-3 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedPositions.map((position) => (
                  <PositionRow
                    key={position.id}
                    position={position}
                    onClose={handleClosePosition}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Footer with quick stats */}
      {positions.length > 0 && (
        <div className={cn(
          "p-2 border-t border-gray-800 text-xs text-gray-400",
          mobile && "hidden"
        )}>
          <div className="flex justify-between">
            <span>
              Profitable: {portfolioSummary.profitablePositions}/{portfolioSummary.totalPositions}
            </span>
            <span>
              Win Rate: {portfolioSummary.winRate.toFixed(1)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export function PositionsPanelSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="flex flex-col h-full animate-pulse">
      <div className="p-3 border-b border-gray-800">
        <div className="h-4 bg-gray-700 rounded w-24 mb-2"></div>
        <div className={cn("grid gap-2", mobile ? "grid-cols-2" : "grid-cols-4")}>
          {Array.from({ length: mobile ? 2 : 4 }).map((_, i) => (
            <div key={i} className="space-y-1">
              <div className="h-3 bg-gray-700 rounded w-16"></div>
              <div className="h-3 bg-gray-700 rounded w-12"></div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="flex-1 p-3 space-y-2">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="h-12 bg-gray-800 rounded"></div>
        ))}
      </div>
    </div>
  );
}