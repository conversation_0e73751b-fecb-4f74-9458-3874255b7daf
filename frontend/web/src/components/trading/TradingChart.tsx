'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCanvasOptimizations, usePerformanceMonitor, useThrottled, useAnimationFrame } from '@/hooks/usePerformanceOptimizations';
import { cn } from '@/lib/utils';

interface TradingChartProps {
  marketId: string | null;
  mobile?: boolean;
  className?: string;
}

interface CandlestickData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface ChartIndicator {
  id: string;
  name: string;
  enabled: boolean;
  color: string;
}

const TIMEFRAMES = [
  { value: '1m', label: '1min' },
  { value: '5m', label: '5min' },
  { value: '15m', label: '15min' },
  { value: '30m', label: '30min' },
  { value: '1h', label: '1hour' },
  { value: '4h', label: '4hour' },
  { value: '1d', label: '1day' },
];

const CHART_TYPES = [
  { value: 'candlestick', label: 'Candlestick' },
  { value: 'line', label: 'Line' },
  { value: 'area', label: 'Area' },
];

const INDICATORS = [
  { id: 'sma20', name: 'SMA 20', enabled: false, color: '#FFB800' },
  { id: 'sma50', name: 'SMA 50', enabled: false, color: '#FF6B6B' },
  { id: 'ema20', name: 'EMA 20', enabled: false, color: '#4ECDC4' },
  { id: 'bb', name: 'Bollinger Bands', enabled: false, color: '#A8E6CF' },
  { id: 'rsi', name: 'RSI', enabled: false, color: '#FFD93D' },
  { id: 'macd', name: 'MACD', enabled: false, color: '#6BCF7F' },
];

export function TradingChart({ marketId, mobile = false, className }: TradingChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [timeframe, setTimeframe] = useState('1h');
  const [chartType, setChartType] = useState('candlestick');
  const [indicators, setIndicators] = useState<ChartIndicator[]>(INDICATORS);
  const [isLoading, setIsLoading] = useState(false);
  const [chartData, setChartData] = useState<CandlestickData[]>([]);
  const [crosshair, setCrosshair] = useState<{ x: number; y: number; visible: boolean }>({
    x: 0,
    y: 0,
    visible: false
  });

  // Performance optimizations
  usePerformanceMonitor('TradingChart');
  const { dpr, ctx } = useCanvasOptimizations(canvasRef);
  
  // Throttle mouse events
  const throttledMouseMove = useThrottled((e: React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    setCrosshair({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
      visible: true
    });
  }, 16); // ~60fps

  // Mock data generator for development
  const generateMockData = useMemo(() => {
    const basePrice = 1.85;
    const data: CandlestickData[] = [];
    const now = Date.now();
    
    for (let i = 100; i >= 0; i--) {
      const time = now - (i * 60000); // 1 minute intervals
      const randomChange = (Math.random() - 0.5) * 0.1;
      const open = basePrice + randomChange;
      const close = open + (Math.random() - 0.5) * 0.05;
      const high = Math.max(open, close) + Math.random() * 0.02;
      const low = Math.min(open, close) - Math.random() * 0.02;
      const volume = Math.random() * 10000;
      
      data.push({ time, open, high, low, close, volume });
    }
    
    return data;
  }, []);

  // Load chart data
  useEffect(() => {
    if (!marketId) return;
    
    setIsLoading(true);
    
    // In real implementation, this would fetch from API
    // For now, use mock data
    setTimeout(() => {
      setChartData(generateMockData);
      setIsLoading(false);
    }, 500);
  }, [marketId, timeframe, generateMockData]);

  // Canvas drawing functions
  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || !chartData.length) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const { width, height } = canvas;
    const padding = mobile ? 20 : 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Calculate price range
    const prices = chartData.flatMap(d => [d.high, d.low]);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    
    // Price to Y coordinate
    const priceToY = (price: number) => {
      return padding + ((maxPrice - price) / priceRange) * chartHeight;
    };
    
    // Time to X coordinate
    const timeToX = (time: number, index: number) => {
      return padding + (index / (chartData.length - 1)) * chartWidth;
    };
    
    if (chartType === 'candlestick') {
      drawCandlesticks(ctx, chartData, timeToX, priceToY);
    } else if (chartType === 'line') {
      drawLine(ctx, chartData, timeToX, priceToY);
    } else if (chartType === 'area') {
      drawArea(ctx, chartData, timeToX, priceToY, chartHeight + padding);
    }
    
    // Draw enabled indicators
    indicators.forEach(indicator => {
      if (indicator.enabled) {
        drawIndicator(ctx, indicator, chartData, timeToX, priceToY);
      }
    });
    
    // Draw grid and axes
    drawGrid(ctx, width, height, padding, chartWidth, chartHeight, minPrice, maxPrice, priceRange);
    
    // Draw crosshair
    if (crosshair.visible) {
      drawCrosshair(ctx, crosshair.x, crosshair.y, width, height);
    }
  };

  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    data: CandlestickData[],
    timeToX: (time: number, index: number) => number,
    priceToY: (price: number) => number
  ) => {
    const candleWidth = Math.max(2, (canvasRef.current!.width - 80) / data.length - 2);
    
    data.forEach((candle, index) => {
      const x = timeToX(candle.time, index);
      const openY = priceToY(candle.open);
      const closeY = priceToY(candle.close);
      const highY = priceToY(candle.high);
      const lowY = priceToY(candle.low);
      
      const isGreen = candle.close > candle.open;
      ctx.strokeStyle = isGreen ? '#00D964' : '#FF3B30';
      ctx.fillStyle = isGreen ? '#00D964' : '#FF3B30';
      
      // Draw wick
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();
      
      // Draw body
      const bodyTop = Math.min(openY, closeY);
      const bodyHeight = Math.abs(closeY - openY);
      
      if (isGreen) {
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      } else {
        ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      }
    });
  };

  const drawLine = (
    ctx: CanvasRenderingContext2D,
    data: CandlestickData[],
    timeToX: (time: number, index: number) => number,
    priceToY: (price: number) => number
  ) => {
    ctx.strokeStyle = '#00D964';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((candle, index) => {
      const x = timeToX(candle.time, index);
      const y = priceToY(candle.close);
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  };

  const drawArea = (
    ctx: CanvasRenderingContext2D,
    data: CandlestickData[],
    timeToX: (time: number, index: number) => number,
    priceToY: (price: number) => number,
    bottom: number
  ) => {
    // Create gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, bottom);
    gradient.addColorStop(0, 'rgba(0, 217, 100, 0.3)');
    gradient.addColorStop(1, 'rgba(0, 217, 100, 0.05)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    
    // Start from bottom left
    const firstX = timeToX(data[0].time, 0);
    ctx.moveTo(firstX, bottom);
    
    // Draw line along prices
    data.forEach((candle, index) => {
      const x = timeToX(candle.time, index);
      const y = priceToY(candle.close);
      ctx.lineTo(x, y);
    });
    
    // Close to bottom right
    const lastX = timeToX(data[data.length - 1].time, data.length - 1);
    ctx.lineTo(lastX, bottom);
    ctx.closePath();
    ctx.fill();
    
    // Draw line on top
    drawLine(ctx, data, timeToX, priceToY);
  };

  const drawIndicator = (
    ctx: CanvasRenderingContext2D,
    indicator: ChartIndicator,
    data: CandlestickData[],
    timeToX: (time: number, index: number) => number,
    priceToY: (price: number) => number
  ) => {
    // Simplified SMA calculation for demo
    if (indicator.id.startsWith('sma')) {
      const period = parseInt(indicator.id.replace('sma', ''));
      ctx.strokeStyle = indicator.color;
      ctx.lineWidth = 1;
      ctx.beginPath();
      
      for (let i = period - 1; i < data.length; i++) {
        const sum = data.slice(i - period + 1, i + 1).reduce((acc, d) => acc + d.close, 0);
        const avg = sum / period;
        const x = timeToX(data[i].time, i);
        const y = priceToY(avg);
        
        if (i === period - 1) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.stroke();
    }
  };

  const drawGrid = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number,
    padding: number,
    chartWidth: number,
    chartHeight: number,
    minPrice: number,
    maxPrice: number,
    priceRange: number
  ) => {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    ctx.font = mobile ? '10px monospace' : '12px monospace';
    ctx.fillStyle = '#8E8E93';
    
    // Horizontal grid lines (price levels)
    const priceSteps = 5;
    for (let i = 0; i <= priceSteps; i++) {
      const price = minPrice + (priceRange * i / priceSteps);
      const y = padding + (chartHeight * (priceSteps - i) / priceSteps);
      
      // Grid line
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
      
      // Price label
      ctx.fillText(price.toFixed(2), width - padding + 5, y + 3);
    }
    
    // Vertical grid lines (time)
    const timeSteps = mobile ? 3 : 6;
    for (let i = 0; i <= timeSteps; i++) {
      const x = padding + (chartWidth * i / timeSteps);
      
      // Grid line
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
      
      // Time label (simplified)
      if (chartData.length > 0) {
        const dataIndex = Math.floor((chartData.length - 1) * i / timeSteps);
        const time = new Date(chartData[dataIndex]?.time || 0);
        const timeLabel = mobile 
          ? time.getHours().toString().padStart(2, '0')
          : `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
        
        ctx.fillText(timeLabel, x - 15, height - 5);
      }
    }
  };

  const drawCrosshair = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number
  ) => {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    
    // Vertical line
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
    
    // Horizontal line
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(width, y);
    ctx.stroke();
    
    ctx.setLineDash([]);
  };

  // Canvas setup and drawing
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = chartContainerRef.current;
    
    if (!canvas || !container) return;
    
    const resizeCanvas = () => {
      const rect = container.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
      drawChart();
    };
    
    resizeCanvas();
    
    const resizeObserver = new ResizeObserver(resizeCanvas);
    resizeObserver.observe(container);
    
    return () => resizeObserver.disconnect();
  }, [chartData, chartType, indicators, crosshair]);

  // Mouse events for crosshair
  const handleMouseLeave = () => {
    setCrosshair(prev => ({ ...prev, visible: false }));
  };

  // Toggle indicator
  const toggleIndicator = (indicatorId: string) => {
    setIndicators(prev => 
      prev.map(ind => 
        ind.id === indicatorId 
          ? { ...ind, enabled: !ind.enabled }
          : ind
      )
    );
  };

  if (!marketId) {
    return (
      <div className={cn("flex items-center justify-center h-full text-gray-500", className)}>
        <div className="text-center">
          <div className="text-3xl mb-2">📈</div>
          <div className="text-sm">Select a market to view chart</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-gray-900", className)}>
      {/* Chart Controls */}
      <div className={cn(
        "flex items-center justify-between p-3 border-b border-gray-800",
        mobile && "flex-wrap gap-2"
      )}>
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium text-white">Chart</h3>
          {isLoading && (
            <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* Timeframe selector */}
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-20 h-8 bg-gray-800 border-gray-700 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TIMEFRAMES.map(tf => (
                <SelectItem key={tf.value} value={tf.value}>
                  {tf.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* Chart type selector */}
          <Select value={chartType} onValueChange={setChartType}>
            <SelectTrigger className="w-28 h-8 bg-gray-800 border-gray-700 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CHART_TYPES.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Indicators Panel */}
      {!mobile && (
        <div className="flex items-center gap-2 p-2 border-b border-gray-800 overflow-x-auto">
          {indicators.map(indicator => (
            <Button
              key={indicator.id}
              variant="outline"
              size="sm"
              onClick={() => toggleIndicator(indicator.id)}
              className={cn(
                "text-xs h-6 px-2 border-gray-700",
                indicator.enabled 
                  ? "bg-gray-700 text-white border-gray-600" 
                  : "bg-gray-800 text-gray-400 hover:bg-gray-700"
              )}
            >
              <div 
                className="w-2 h-2 rounded-full mr-1"
                style={{ backgroundColor: indicator.enabled ? indicator.color : 'transparent' }}
              />
              {indicator.name}
            </Button>
          ))}
        </div>
      )}

      {/* Chart Canvas */}
      <div 
        ref={chartContainerRef}
        className="flex-1 relative"
        onMouseMove={throttledMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        <canvas
          ref={canvasRef}
          className="absolute inset-0 cursor-crosshair"
        />
        
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <div className="text-sm text-gray-400">Loading chart data...</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export function TradingChartSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="flex flex-col h-full bg-gray-900 animate-pulse">
      <div className="p-3 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div className="h-4 bg-gray-700 rounded w-16"></div>
          <div className="flex gap-2">
            <div className="h-6 bg-gray-700 rounded w-16"></div>
            <div className="h-6 bg-gray-700 rounded w-20"></div>
          </div>
        </div>
      </div>
      
      <div className="flex-1 p-4">
        <div className="h-full bg-gray-800 rounded"></div>
      </div>
    </div>
  );
}