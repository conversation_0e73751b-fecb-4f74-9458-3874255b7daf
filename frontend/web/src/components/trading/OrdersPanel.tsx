'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Order } from '@/store/tradingStore';
import { cn } from '@/lib/utils';

interface OrdersPanelProps {
  orders: Order[];
  mobile?: boolean;
  className?: string;
}

interface OrderRowProps {
  order: Order;
  onCancel: (orderId: string) => void;
  onModify: (orderId: string) => void;
  mobile?: boolean;
}

function OrderRow({ order, onCancel, onModify, mobile = false }: OrderRowProps) {
  const [isCancelling, setIsCancelling] = useState(false);
  
  const handleCancel = async () => {
    if (order.status === 'filled' || order.status === 'cancelled') return;
    
    setIsCancelling(true);
    try {
      await onCancel(order.id);
    } catch (error) {
      console.error('Failed to cancel order:', error);
    } finally {
      setIsCancelling(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 border-yellow-400/40';
      case 'partial': return 'text-blue-400 border-blue-400/40';
      case 'filled': return 'text-green-400 border-green-400/40';
      case 'cancelled': return 'text-gray-400 border-gray-400/40';
      case 'rejected': return 'text-red-400 border-red-400/40';
      default: return 'text-gray-400 border-gray-400/40';
    }
  };

  const getSideColor = (side: string) => {
    return side === 'buy' ? 'text-green-400' : 'text-red-400';
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'market': return 'text-orange-400';
      case 'limit': return 'text-blue-400';
      case 'stop_loss': return 'text-red-400';
      case 'take_profit': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const canCancel = order.status === 'pending' || order.status === 'partial';
  const canModify = order.status === 'pending' && order.orderType === 'limit';

  if (mobile) {
    return (
      <div className="p-3 border-b border-gray-800 last:border-b-0">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{order.marketName}</span>
            <Badge variant="outline" className={cn("text-xs", getStatusColor(order.status))}>
              {order.status.toUpperCase()}
            </Badge>
          </div>
          
          <div className="flex gap-1">
            {canModify && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onModify(order.id)}
                className="h-6 px-2 text-xs bg-blue-600/20 border-blue-600/40 hover:bg-blue-600/30"
              >
                Edit
              </Button>
            )}
            {canCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                disabled={isCancelling}
                className="h-6 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
              >
                {isCancelling ? '...' : 'Cancel'}
              </Button>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs mb-2">
          <div>
            <span className="text-gray-400">Side:</span>
            <span className={cn("ml-1 font-medium", getSideColor(order.side))}>
              {order.side.toUpperCase()}
            </span>
          </div>
          <div>
            <span className="text-gray-400">Type:</span>
            <span className={cn("ml-1", getTypeColor(order.orderType))}>
              {order.orderType.toUpperCase()}
            </span>
          </div>
          <div>
            <span className="text-gray-400">Qty:</span>
            <span className="ml-1">{order.quantity.toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-400">Price:</span>
            <span className="ml-1">
              {order.price ? order.price.toFixed(2) : 'Market'}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-400">Filled:</span>
            <span className="ml-1">{order.filledQuantity.toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-400">Remaining:</span>
            <span className="ml-1">{order.remainingQuantity.toLocaleString()}</span>
          </div>
        </div>
        
        <div className="mt-2 text-xs text-gray-400">
          {new Date(order.timestamp).toLocaleString()}
        </div>
      </div>
    );
  }

  return (
    <tr className="border-b border-gray-800 hover:bg-gray-800/50">
      <td className="px-3 py-2">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{order.marketName}</span>
          <Badge variant="outline" className={cn("text-xs", getStatusColor(order.status))}>
            {order.status.toUpperCase()}
          </Badge>
        </div>
      </td>
      <td className="px-3 py-2">
        <span className={cn("text-sm font-medium", getSideColor(order.side))}>
          {order.side.toUpperCase()}
        </span>
      </td>
      <td className="px-3 py-2">
        <span className={cn("text-sm", getTypeColor(order.orderType))}>
          {order.orderType.toUpperCase()}
        </span>
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {order.quantity.toLocaleString()}
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {order.price ? order.price.toFixed(2) : 'Market'}
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {order.filledQuantity.toLocaleString()}
      </td>
      <td className="px-3 py-2 text-sm font-mono">
        {order.remainingQuantity.toLocaleString()}
      </td>
      <td className="px-3 py-2 text-xs text-gray-400">
        {new Date(order.timestamp).toLocaleString()}
      </td>
      <td className="px-3 py-2">
        <div className="flex gap-1">
          {canModify && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onModify(order.id)}
              className="h-6 px-2 text-xs bg-blue-600/20 border-blue-600/40 hover:bg-blue-600/30"
            >
              Edit
            </Button>
          )}
          {canCancel && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={isCancelling}
              className="h-6 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
            >
              {isCancelling ? '...' : 'Cancel'}
            </Button>
          )}
        </div>
      </td>
    </tr>
  );
}

export function OrdersPanel({ orders, mobile = false, className }: OrdersPanelProps) {
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'timestamp' | 'market' | 'status'>('timestamp');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter and sort orders
  const filteredAndSortedOrders = useMemo(() => {
    let filtered = orders;
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = orders.filter(order => order.status === statusFilter);
    }
    
    // Sort orders
    const sorted = [...filtered].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'timestamp':
          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
          break;
        case 'market':
          comparison = a.marketName.localeCompare(b.marketName);
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
      }
      
      return sortDirection === 'desc' ? -comparison : comparison;
    });
    
    return sorted;
  }, [orders, statusFilter, sortBy, sortDirection]);

  // Calculate order statistics
  const orderStats = useMemo(() => {
    const total = orders.length;
    const pending = orders.filter(o => o.status === 'pending').length;
    const filled = orders.filter(o => o.status === 'filled').length;
    const cancelled = orders.filter(o => o.status === 'cancelled').length;
    const partial = orders.filter(o => o.status === 'partial').length;
    
    return { total, pending, filled, cancelled, partial };
  }, [orders]);

  const handleSort = (field: 'timestamp' | 'market' | 'status') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('desc');
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    // In real implementation, this would call the API to cancel the order
    console.log('Cancelling order:', orderId);
    
    // Mock API call
    return new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
  };

  const handleModifyOrder = (orderId: string) => {
    // In real implementation, this would open a modify order dialog
    console.log('Modifying order:', orderId);
  };

  const handleCancelAllOrders = async () => {
    // In real implementation, this would cancel all pending orders
    console.log('Cancelling all orders');
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header with filters and stats */}
      <div className="p-3 border-b border-gray-800">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-white">
            Orders ({filteredAndSortedOrders.length})
          </h3>
          
          <div className="flex items-center gap-2">
            {/* Status filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-24 h-7 bg-gray-800 border-gray-700 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="partial">Partial</SelectItem>
                <SelectItem value="filled">Filled</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            
            {orderStats.pending > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelAllOrders}
                className="h-7 px-2 text-xs bg-red-600/20 border-red-600/40 hover:bg-red-600/30"
              >
                Cancel All
              </Button>
            )}
          </div>
        </div>
        
        {/* Order Statistics */}
        <div className={cn(
          "grid gap-2 text-xs",
          mobile ? "grid-cols-2" : "grid-cols-5"
        )}>
          <div>
            <span className="text-gray-400">Total:</span>
            <span className="ml-1 font-medium text-white">{orderStats.total}</span>
          </div>
          <div>
            <span className="text-gray-400">Pending:</span>
            <span className="ml-1 font-medium text-yellow-400">{orderStats.pending}</span>
          </div>
          <div>
            <span className="text-gray-400">Filled:</span>
            <span className="ml-1 font-medium text-green-400">{orderStats.filled}</span>
          </div>
          {!mobile && (
            <>
              <div>
                <span className="text-gray-400">Partial:</span>
                <span className="ml-1 font-medium text-blue-400">{orderStats.partial}</span>
              </div>
              <div>
                <span className="text-gray-400">Cancelled:</span>
                <span className="ml-1 font-medium text-gray-400">{orderStats.cancelled}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Orders List */}
      <div className="flex-1 overflow-y-auto">
        {filteredAndSortedOrders.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-2xl mb-2">📋</div>
              <div className="text-sm">
                {statusFilter === 'all' ? 'No orders' : `No ${statusFilter} orders`}
              </div>
              <div className="text-xs text-gray-600 mt-1">
                Place a trade to see your orders here
              </div>
            </div>
          </div>
        ) : mobile ? (
          <div>
            {filteredAndSortedOrders.map((order) => (
              <OrderRow
                key={order.id}
                order={order}
                onCancel={handleCancelOrder}
                onModify={handleModifyOrder}
                mobile
              />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="sticky top-0 bg-gray-800 border-b border-gray-700">
                <tr>
                  <th 
                    className="px-3 py-2 text-left cursor-pointer hover:bg-gray-700"
                    onClick={() => handleSort('market')}
                  >
                    <div className="flex items-center gap-1">
                      Market
                      {sortBy === 'market' && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left">Side</th>
                  <th className="px-3 py-2 text-left">Type</th>
                  <th className="px-3 py-2 text-left">Quantity</th>
                  <th className="px-3 py-2 text-left">Price</th>
                  <th className="px-3 py-2 text-left">Filled</th>
                  <th className="px-3 py-2 text-left">Remaining</th>
                  <th 
                    className="px-3 py-2 text-left cursor-pointer hover:bg-gray-700"
                    onClick={() => handleSort('timestamp')}
                  >
                    <div className="flex items-center gap-1">
                      Time
                      {sortBy === 'timestamp' && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedOrders.map((order) => (
                  <OrderRow
                    key={order.id}
                    order={order}
                    onCancel={handleCancelOrder}
                    onModify={handleModifyOrder}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Footer with quick actions */}
      {filteredAndSortedOrders.length > 0 && (
        <div className={cn(
          "p-2 border-t border-gray-800 text-xs text-gray-400",
          mobile && "hidden"
        )}>
          <div className="flex justify-between">
            <span>
              Showing {filteredAndSortedOrders.length} of {orders.length} orders
            </span>
            <span>
              Active: {orderStats.pending + orderStats.partial}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export function OrdersPanelSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="flex flex-col h-full animate-pulse">
      <div className="p-3 border-b border-gray-800">
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 bg-gray-700 rounded w-20"></div>
          <div className="h-6 bg-gray-700 rounded w-16"></div>
        </div>
        <div className={cn("grid gap-2", mobile ? "grid-cols-2" : "grid-cols-5")}>
          {Array.from({ length: mobile ? 2 : 5 }).map((_, i) => (
            <div key={i} className="space-y-1">
              <div className="h-3 bg-gray-700 rounded w-12"></div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="flex-1 p-3 space-y-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-16 bg-gray-800 rounded"></div>
        ))}
      </div>
    </div>
  );
}