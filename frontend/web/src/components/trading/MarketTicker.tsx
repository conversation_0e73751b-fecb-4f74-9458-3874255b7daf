'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface TickerData {
  marketId: string;
  symbol: string;
  name: string;
  lastPrice: number;
  priceChange24h: number;
  priceChangePercent24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  bid: number;
  ask: number;
  spread: number;
  lastUpdate: number;
}

interface MarketTickerProps {
  marketId: string | null;
  mobile?: boolean;
  className?: string;
}

const MOCK_TICKER_DATA: Record<string, TickerData> = {
  'btc-usd': {
    marketId: 'btc-usd',
    symbol: 'BTC/USD',
    name: 'Bitcoin / US Dollar',
    lastPrice: 43250.00,
    priceChange24h: 1250.50,
    priceChangePercent24h: 2.98,
    volume24h: 1850000,
    high24h: 44100.00,
    low24h: 41800.00,
    bid: 43245.50,
    ask: 43254.50,
    spread: 9.00,
    lastUpdate: Date.now()
  },
  'eth-usd': {
    marketId: 'eth-usd',
    symbol: 'ETH/USD',
    name: 'Ethereum / US Dollar',
    lastPrice: 2650.75,
    priceChange24h: -85.25,
    priceChangePercent24h: -3.12,
    volume24h: 950000,
    high24h: 2750.00,
    low24h: 2580.00,
    bid: 2649.80,
    ask: 2651.70,
    spread: 1.90,
    lastUpdate: Date.now()
  },
  'nfl-chiefs-win': {
    marketId: 'nfl-chiefs-win',
    symbol: 'KC-WIN',
    name: 'Kansas City Chiefs Win Super Bowl',
    lastPrice: 1.85,
    priceChange24h: 0.05,
    priceChangePercent24h: 2.78,
    volume24h: 125000,
    high24h: 1.92,
    low24h: 1.78,
    bid: 1.84,
    ask: 1.86,
    spread: 0.02,
    lastUpdate: Date.now()
  }
};

export function MarketTicker({ marketId, mobile = false, className }: MarketTickerProps) {
  const [tickerData, setTickerData] = useState<TickerData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(0);

  // Simulate real-time price updates
  useEffect(() => {
    if (!marketId || !MOCK_TICKER_DATA[marketId]) {
      setTickerData(null);
      return;
    }

    const baseTicker = MOCK_TICKER_DATA[marketId];
    setTickerData(baseTicker);
    setIsConnected(true);

    // Simulate price updates every 2-5 seconds
    const interval = setInterval(() => {
      setTickerData(prev => {
        if (!prev) return null;

        // Small random price movements
        const priceChange = (Math.random() - 0.5) * (prev.lastPrice * 0.001);
        const newPrice = Math.max(0.01, prev.lastPrice + priceChange);
        
        // Update bid/ask relative to new price
        const halfSpread = prev.spread / 2;
        const newBid = newPrice - halfSpread;
        const newAsk = newPrice + halfSpread;

        return {
          ...prev,
          lastPrice: newPrice,
          bid: newBid,
          ask: newAsk,
          lastUpdate: Date.now()
        };
      });
      setLastUpdateTime(Date.now());
    }, Math.random() * 3000 + 2000); // 2-5 seconds

    return () => clearInterval(interval);
  }, [marketId]);

  // Calculate derived values
  const derivedValues = useMemo(() => {
    if (!tickerData) return null;

    const spreadPercent = tickerData.lastPrice > 0 
      ? (tickerData.spread / tickerData.lastPrice) * 100 
      : 0;

    const midPrice = (tickerData.bid + tickerData.ask) / 2;
    
    const priceChangeColor = tickerData.priceChange24h >= 0 
      ? 'text-green-400' 
      : 'text-red-400';

    return {
      spreadPercent,
      midPrice,
      priceChangeColor
    };
  }, [tickerData]);

  if (!marketId) {
    return (
      <div className={cn("flex items-center justify-center py-4 text-gray-500", className)}>
        <div className="text-center">
          <div className="text-lg mb-1">📊</div>
          <div className="text-sm">Select a market to view ticker</div>
        </div>
      </div>
    );
  }

  if (!tickerData || !derivedValues) {
    return (
      <div className={cn("p-4 text-center text-gray-500", className)}>
        <div className="text-sm">Loading market data...</div>
      </div>
    );
  }

  if (mobile) {
    return (
      <div className={cn("bg-gray-900 border-b border-gray-800", className)}>
        {/* Mobile Header */}
        <div className="p-3 border-b border-gray-800">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <h2 className="font-bold text-lg">{tickerData.symbol}</h2>
              <div className={cn("flex items-center gap-1", isConnected ? "text-green-400" : "text-red-400")}>
                <div className="w-2 h-2 rounded-full bg-current"></div>
                <span className="text-xs">{isConnected ? 'Live' : 'Disconnected'}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="font-mono text-xl font-bold">
                ${tickerData.lastPrice.toFixed(2)}
              </div>
              <div className={cn("text-sm", derivedValues.priceChangeColor)}>
                {tickerData.priceChange24h >= 0 ? '+' : ''}
                ${tickerData.priceChange24h.toFixed(2)} ({tickerData.priceChangePercent24h.toFixed(2)}%)
              </div>
            </div>
          </div>
          
          <div className="text-xs text-gray-400 truncate">
            {tickerData.name}
          </div>
        </div>

        {/* Mobile Stats Grid */}
        <div className="grid grid-cols-2 gap-3 p-3">
          <div className="space-y-2">
            <div>
              <div className="text-xs text-gray-400">24h High</div>
              <div className="font-mono text-sm">${tickerData.high24h.toFixed(2)}</div>
            </div>
            <div>
              <div className="text-xs text-gray-400">24h Low</div>
              <div className="font-mono text-sm">${tickerData.low24h.toFixed(2)}</div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Bid</div>
              <div className="font-mono text-sm text-green-400">${tickerData.bid.toFixed(2)}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div>
              <div className="text-xs text-gray-400">24h Volume</div>
              <div className="font-mono text-sm">${(tickerData.volume24h / 1000).toFixed(0)}K</div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Spread</div>
              <div className="font-mono text-sm">{derivedValues.spreadPercent.toFixed(3)}%</div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Ask</div>
              <div className="font-mono text-sm text-red-400">${tickerData.ask.toFixed(2)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-gray-900 border-b border-gray-800", className)}>
      {/* Desktop Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-4">
          <div>
            <div className="flex items-center gap-2">
              <h2 className="font-bold text-xl">{tickerData.symbol}</h2>
              <div className={cn("flex items-center gap-1", isConnected ? "text-green-400" : "text-red-400")}>
                <div className="w-2 h-2 rounded-full bg-current"></div>
                <span className="text-xs">{isConnected ? 'Live' : 'Disconnected'}</span>
              </div>
            </div>
            <div className="text-sm text-gray-400">{tickerData.name}</div>
          </div>
          
          <div className="text-right">
            <div className="font-mono text-2xl font-bold">
              ${tickerData.lastPrice.toFixed(2)}
            </div>
            <div className={cn("text-sm", derivedValues.priceChangeColor)}>
              {tickerData.priceChange24h >= 0 ? '+' : ''}
              ${tickerData.priceChange24h.toFixed(2)} ({tickerData.priceChangePercent24h.toFixed(2)}%)
            </div>
          </div>
        </div>

        <div className="flex items-center gap-6 text-sm">
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">24h High</div>
            <div className="font-mono">${tickerData.high24h.toFixed(2)}</div>
          </div>
          
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">24h Low</div>
            <div className="font-mono">${tickerData.low24h.toFixed(2)}</div>
          </div>
          
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">24h Volume</div>
            <div className="font-mono">${(tickerData.volume24h / 1000).toFixed(0)}K</div>
          </div>
          
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">Spread</div>
            <div className="font-mono">{derivedValues.spreadPercent.toFixed(3)}%</div>
          </div>
        </div>
      </div>

      {/* Best Bid/Ask Bar */}
      <div className="flex items-center justify-center py-2 border-t border-gray-800 bg-gray-800/50">
        <div className="flex items-center gap-8 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-400">Best Bid:</span>
            <Badge variant="outline" className="text-green-400 border-green-400/40 bg-green-400/10">
              ${tickerData.bid.toFixed(2)}
            </Badge>
          </div>
          
          <div className="text-gray-400">•</div>
          
          <div className="flex items-center gap-2">
            <span className="text-gray-400">Best Ask:</span>
            <Badge variant="outline" className="text-red-400 border-red-400/40 bg-red-400/10">
              ${tickerData.ask.toFixed(2)}
            </Badge>
          </div>
          
          <div className="text-gray-400">•</div>
          
          <div className="flex items-center gap-2">
            <span className="text-gray-400">Mid:</span>
            <span className="font-mono">${derivedValues.midPrice.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Last Update Indicator */}
      <div className="px-4 py-1 text-xs text-gray-500 border-t border-gray-800">
        Last update: {new Date(tickerData.lastUpdate).toLocaleTimeString()}
      </div>
    </div>
  );
}

export function MarketTickerSkeleton({ mobile = false }: { mobile?: boolean }) {
  return (
    <div className="bg-gray-900 border-b border-gray-800 animate-pulse">
      {mobile ? (
        <div>
          <div className="p-3 border-b border-gray-800">
            <div className="flex items-center justify-between mb-2">
              <div className="h-6 bg-gray-700 rounded w-20"></div>
              <div className="text-right space-y-1">
                <div className="h-6 bg-gray-700 rounded w-24"></div>
                <div className="h-4 bg-gray-700 rounded w-20"></div>
              </div>
            </div>
            <div className="h-3 bg-gray-700 rounded w-40"></div>
          </div>
          
          <div className="grid grid-cols-2 gap-3 p-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-1">
                <div className="h-3 bg-gray-700 rounded w-16"></div>
                <div className="h-4 bg-gray-700 rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <div className="space-y-2">
                <div className="h-6 bg-gray-700 rounded w-24"></div>
                <div className="h-4 bg-gray-700 rounded w-32"></div>
              </div>
              <div className="text-right space-y-1">
                <div className="h-8 bg-gray-700 rounded w-32"></div>
                <div className="h-4 bg-gray-700 rounded w-24"></div>
              </div>
            </div>
            
            <div className="flex gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="text-center space-y-1">
                  <div className="h-3 bg-gray-700 rounded w-16"></div>
                  <div className="h-4 bg-gray-700 rounded w-20"></div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="py-2 border-t border-gray-800">
            <div className="flex justify-center gap-8">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-6 bg-gray-700 rounded w-24"></div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}