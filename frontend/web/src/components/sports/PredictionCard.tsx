/**
 * Sports Prediction Card Component
 * Displays AI-generated sports predictions with confidence metrics
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Brain, 
  Target,
  Clock,
  BarChart3,
  Eye,
  Star,
  Activity,
  Zap
} from 'lucide-react';

interface Prediction {
  id: string;
  event_id: string;
  event_title: string;
  event_date: string;
  sport: string;
  league: string;
  teams: {
    home: { name: string; logo?: string };
    away: { name: string; logo?: string };
  };
  prediction: {
    outcome: 'home_win' | 'away_win' | 'draw';
    confidence: number;
    predicted_score?: string;
    win_probability: {
      home: number;
      away: number;
      draw: number;
    };
  };
  analysis: {
    key_factors: string[];
    risk_factors: string[];
    value_assessment: 'high' | 'medium' | 'low';
    ai_reasoning: string;
  };
  odds: {
    home: number;
    away: number;
    draw: number;
  };
  metrics: {
    expected_value: number;
    kelly_criterion: number;
    model_accuracy: number;
  };
  status: 'pending' | 'in_progress' | 'completed';
  created_at: string;
}

interface PredictionCardProps {
  prediction: Prediction;
  onViewDetails?: (predictionId: string) => void;
  onPlaceBet?: (prediction: Prediction) => void;
  showAnalysis?: boolean;
  compact?: boolean;
}

export function PredictionCard({ 
  prediction, 
  onViewDetails, 
  onPlaceBet,
  showAnalysis = true,
  compact = false 
}: PredictionCardProps) {
  const getOutcomeIcon = () => {
    switch (prediction.prediction.outcome) {
      case 'home_win':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'away_win':
        return <TrendingUp className="h-4 w-4 text-blue-600" />;
      default:
        return <TrendingDown className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getOutcomeLabel = () => {
    switch (prediction.prediction.outcome) {
      case 'home_win':
        return prediction.teams.home.name;
      case 'away_win':
        return prediction.teams.away.name;
      default:
        return 'Draw';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getValueColor = (value: string) => {
    switch (value) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-red-100 text-red-800';
    }
  };

  const formatTimeUntilEvent = () => {
    const now = new Date();
    const eventDate = new Date(prediction.event_date);
    const diffInHours = Math.floor((eventDate.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Starting soon';
    if (diffInHours < 24) return `${diffInHours}h`;
    return `${Math.floor(diffInHours / 24)}d`;
  };

  if (compact) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">{prediction.teams.home.name}</span>
                <span className="text-gray-400">vs</span>
                <span className="font-medium text-sm">{prediction.teams.away.name}</span>
              </div>
              
              <div className="flex items-center gap-2">
                {getOutcomeIcon()}
                <span className="text-sm font-medium">{getOutcomeLabel()}</span>
                <Badge variant="outline" className="text-xs">
                  {prediction.prediction.confidence}%
                </Badge>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-gray-600">{formatTimeUntilEvent()}</div>
              <Badge className={getValueColor(prediction.analysis.value_assessment)}>
                {prediction.analysis.value_assessment} value
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-200">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline">{prediction.sport}</Badge>
              <Badge variant="outline">{prediction.league}</Badge>
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{formatTimeUntilEvent()}</span>
              </div>
            </div>
            
            <h3 className="font-bold text-lg mb-1">
              {prediction.teams.home.name} vs {prediction.teams.away.name}
            </h3>
            
            <p className="text-sm text-gray-600">{prediction.event_title}</p>
          </div>
          
          <div className="text-right">
            <Badge className={getValueColor(prediction.analysis.value_assessment)}>
              {prediction.analysis.value_assessment} value
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI Prediction */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-3">
            <Brain className="h-5 w-5 text-purple-600" />
            <span className="font-medium text-purple-900">AI Prediction</span>
          </div>
          
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {getOutcomeIcon()}
              <span className="font-semibold text-lg">{getOutcomeLabel()}</span>
              {prediction.prediction.predicted_score && (
                <span className="text-sm text-gray-600">({prediction.prediction.predicted_score})</span>
              )}
            </div>
            
            <div className={`text-2xl font-bold ${getConfidenceColor(prediction.prediction.confidence)}`}>
              {prediction.prediction.confidence}%
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>{prediction.teams.home.name}</span>
              <span>{prediction.prediction.win_probability.home}%</span>
            </div>
            <Progress value={prediction.prediction.win_probability.home} className="h-2" />
            
            <div className="flex justify-between text-sm">
              <span>{prediction.teams.away.name}</span>
              <span>{prediction.prediction.win_probability.away}%</span>
            </div>
            <Progress value={prediction.prediction.win_probability.away} className="h-2" />
            
            {prediction.prediction.win_probability.draw > 0 && (
              <>
                <div className="flex justify-between text-sm">
                  <span>Draw</span>
                  <span>{prediction.prediction.win_probability.draw}%</span>
                </div>
                <Progress value={prediction.prediction.win_probability.draw} className="h-2" />
              </>
            )}
          </div>
        </div>

        {/* Analysis */}
        {showAnalysis && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Key Factors</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {prediction.analysis.key_factors.slice(0, 4).map((factor, idx) => (
                <div key={idx} className="flex items-center gap-2 text-sm bg-green-50 p-2 rounded">
                  <Star className="h-3 w-3 text-green-600" />
                  <span>{factor}</span>
                </div>
              ))}
            </div>
            
            {prediction.analysis.risk_factors.length > 0 && (
              <>
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-red-600" />
                  <span className="font-medium">Risk Factors</span>
                </div>
                
                <div className="space-y-1">
                  {prediction.analysis.risk_factors.slice(0, 2).map((risk, idx) => (
                    <div key={idx} className="text-sm bg-red-50 p-2 rounded text-red-800">
                      {risk}
                    </div>
                  ))}
                </div>
              </>
            )}
            
            {prediction.analysis.ai_reasoning && (
              <div className="bg-gray-50 p-3 rounded text-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium">AI Analysis</span>
                </div>
                <p className="text-gray-700">{prediction.analysis.ai_reasoning}</p>
              </div>
            )}
          </div>
        )}

        {/* Metrics */}
        <div className="grid grid-cols-3 gap-4 py-3 border-y">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
              <BarChart3 className="h-4 w-4" />
              <span className="font-semibold">{prediction.metrics.expected_value.toFixed(2)}%</span>
            </div>
            <p className="text-xs text-gray-600">Expected Value</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
              <Target className="h-4 w-4" />
              <span className="font-semibold">{prediction.metrics.kelly_criterion.toFixed(2)}%</span>
            </div>
            <p className="text-xs text-gray-600">Kelly %</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-purple-600 mb-1">
              <Brain className="h-4 w-4" />
              <span className="font-semibold">{prediction.metrics.model_accuracy}%</span>
            </div>
            <p className="text-xs text-gray-600">Model Accuracy</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onViewDetails?.(prediction.id)}
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-1" />
            View Details
          </Button>
          
          <Button 
            size="sm" 
            onClick={() => onPlaceBet?.(prediction)}
            className="flex-1"
          >
            Place Bet
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}