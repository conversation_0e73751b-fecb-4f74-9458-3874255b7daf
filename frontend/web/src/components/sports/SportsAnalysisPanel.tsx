/**
 * Sports Analysis Panel Component
 * Main dashboard for AI-powered sports analysis and predictions
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PredictionCard } from './PredictionCard';
import { useMultiServiceApi } from '@/hooks/useApi';
import { 
  Brain, 
  TrendingUp, 
  Target,
  Activity,
  BarChart3,
  Zap,
  Clock,
  Award,
  Loader2,
  Filter
} from 'lucide-react';

interface SportsEvent {
  id: string;
  title: string;
  sport: string;
  league: string;
  teams: {
    home: { name: string; logo?: string };
    away: { name: string; logo?: string };
  };
  event_date: string;
  status: 'upcoming' | 'live' | 'completed';
}

interface Prediction {
  id: string;
  event_id: string;
  event_title: string;
  event_date: string;
  sport: string;
  league: string;
  teams: {
    home: { name: string; logo?: string };
    away: { name: string; logo?: string };
  };
  prediction: {
    outcome: 'home_win' | 'away_win' | 'draw';
    confidence: number;
    predicted_score?: string;
    win_probability: {
      home: number;
      away: number;
      draw: number;
    };
  };
  analysis: {
    key_factors: string[];
    risk_factors: string[];
    value_assessment: 'high' | 'medium' | 'low';
    ai_reasoning: string;
  };
  odds: {
    home: number;
    away: number;
    draw: number;
  };
  metrics: {
    expected_value: number;
    kelly_criterion: number;
    model_accuracy: number;
  };
  status: 'pending' | 'in_progress' | 'completed';
  created_at: string;
}

interface AnalyticsData {
  model_performance: {
    overall_accuracy: number;
    recent_accuracy: number;
    total_predictions: number;
    profitable_predictions: number;
  };
  top_leagues: Array<{
    name: string;
    accuracy: number;
    predictions: number;
  }>;
  recent_performance: Array<{
    date: string;
    accuracy: number;
    predictions: number;
  }>;
}

export function SportsAnalysisPanel() {
  const multiApi = useMultiServiceApi();
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [events, setEvents] = useState<SportsEvent[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('predictions');
  const [sportFilter, setSportFilter] = useState('all');
  const [valueFilter, setValueFilter] = useState('all');

  useEffect(() => {
    loadSportsData();
  }, [activeTab, sportFilter, valueFilter]);

  const loadSportsData = async () => {
    try {
      setLoading(true);
      
      // Load sports data from the service
      const sportsResponse = await multiApi.sports.getFixtures();
      const predictionsResponse = await multiApi.sports.getFixtures(); // Using fixtures as predictions for now
      
      // Mock analytics data since the service is still developing
      const mockAnalytics: AnalyticsData = {
        model_performance: {
          overall_accuracy: 73.2,
          recent_accuracy: 76.8,
          total_predictions: 1247,
          profitable_predictions: 892
        },
        top_leagues: [
          { name: 'NFL', accuracy: 78.5, predictions: 156 },
          { name: 'NBA', accuracy: 74.2, predictions: 203 },
          { name: 'Premier League', accuracy: 71.8, predictions: 134 }
        ],
        recent_performance: [
          { date: '2025-01-20', accuracy: 82, predictions: 12 },
          { date: '2025-01-21', accuracy: 75, predictions: 8 },
          { date: '2025-01-22', accuracy: 69, predictions: 15 }
        ]
      };

      // Mock predictions data
      const mockPredictions: Prediction[] = [
        {
          id: '1',
          event_id: 'nfl-001',
          event_title: 'NFL Conference Championship',
          event_date: '2025-01-26T18:00:00Z',
          sport: 'NFL',
          league: 'National Football League',
          teams: {
            home: { name: 'Kansas City Chiefs' },
            away: { name: 'Buffalo Bills' }
          },
          prediction: {
            outcome: 'home_win',
            confidence: 78,
            predicted_score: '27-24',
            win_probability: { home: 62, away: 38, draw: 0 }
          },
          analysis: {
            key_factors: [
              'Home field advantage in playoffs',
              'KC superior red zone efficiency',
              'Buffalo struggling vs elite pass rush',
              'Weather conditions favor KC offense'
            ],
            risk_factors: [
              'Buffalo\'s explosive passing attack',
              'Josh Allen\'s mobility in big games'
            ],
            value_assessment: 'high',
            ai_reasoning: 'Model identifies significant edge in KC\'s favor based on historical playoff performance at Arrowhead and Buffalo\'s road struggles in cold weather games.'
          },
          odds: { home: 1.95, away: 2.15, draw: 0 },
          metrics: {
            expected_value: 12.4,
            kelly_criterion: 8.2,
            model_accuracy: 78.5
          },
          status: 'pending',
          created_at: '2025-01-23T10:00:00Z'
        },
        {
          id: '2',
          event_id: 'nba-002',
          event_title: 'NBA Regular Season',
          event_date: '2025-01-24T20:00:00Z',
          sport: 'NBA',
          league: 'National Basketball Association',
          teams: {
            home: { name: 'Boston Celtics' },
            away: { name: 'Los Angeles Lakers' }
          },
          prediction: {
            outcome: 'home_win',
            confidence: 65,
            predicted_score: '118-112',
            win_probability: { home: 65, away: 35, draw: 0 }
          },
          analysis: {
            key_factors: [
              'Boston\'s home court advantage',
              'Superior defensive efficiency',
              'Lakers on back-to-back games'
            ],
            risk_factors: [
              'LeBron James in primetime',
              'Lakers improved road performance'
            ],
            value_assessment: 'medium',
            ai_reasoning: 'Boston has strong underlying metrics at home but Lakers have championship experience in big games.'
          },
          odds: { home: 1.75, away: 2.25, draw: 0 },
          metrics: {
            expected_value: 8.7,
            kelly_criterion: 5.3,
            model_accuracy: 74.2
          },
          status: 'pending',
          created_at: '2025-01-23T08:00:00Z'
        }
      ];

      setPredictions(mockPredictions);
      setAnalytics(mockAnalytics);
      
      // Set events from predictions
      setEvents(mockPredictions.map(p => ({
        id: p.event_id,
        title: p.event_title,
        sport: p.sport,
        league: p.league,
        teams: p.teams,
        event_date: p.event_date,
        status: 'upcoming' as const
      })));
      
    } catch (error) {
      console.error('Failed to load sports data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPredictions = predictions.filter(prediction => {
    if (sportFilter !== 'all' && prediction.sport !== sportFilter) return false;
    if (valueFilter !== 'all' && prediction.analysis.value_assessment !== valueFilter) return false;
    return true;
  });

  const handleViewDetails = (predictionId: string) => {
    console.log('Viewing prediction details:', predictionId);
    // TODO: Navigate to detailed prediction page
  };

  const handlePlaceBet = (prediction: Prediction) => {
    console.log('Placing bet on prediction:', prediction);
    // TODO: Open betting interface
  };

  const getUniqueLeagues = () => {
    return [...new Set(predictions.map(p => p.sport))];
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            AI Sports Analysis
            <Badge variant="secondary">Powered by RAG Engine</Badge>
          </CardTitle>
          
          {analytics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
                  <Target className="h-4 w-4" />
                  <span className="font-semibold">{analytics.model_performance.overall_accuracy}%</span>
                </div>
                <p className="text-xs text-gray-600">Overall Accuracy</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
                  <TrendingUp className="h-4 w-4" />
                  <span className="font-semibold">{analytics.model_performance.recent_accuracy}%</span>
                </div>
                <p className="text-xs text-gray-600">Recent Accuracy</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-purple-600 mb-1">
                  <BarChart3 className="h-4 w-4" />
                  <span className="font-semibold">{analytics.model_performance.total_predictions}</span>
                </div>
                <p className="text-xs text-gray-600">Total Predictions</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-yellow-600 mb-1">
                  <Award className="h-4 w-4" />
                  <span className="font-semibold">{Math.round((analytics.model_performance.profitable_predictions / analytics.model_performance.total_predictions) * 100)}%</span>
                </div>
                <p className="text-xs text-gray-600">Profitable</p>
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <TabsList className="grid w-full md:w-auto grid-cols-3">
            <TabsTrigger value="predictions">AI Predictions</TabsTrigger>
            <TabsTrigger value="events">Upcoming Events</TabsTrigger>
            <TabsTrigger value="analytics">Performance</TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <Select value={sportFilter} onValueChange={setSportFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Sport" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sports</SelectItem>
                {getUniqueLeagues().map(league => (
                  <SelectItem key={league} value={league}>{league}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={valueFilter} onValueChange={setValueFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Value" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Value</SelectItem>
                <SelectItem value="high">High Value</SelectItem>
                <SelectItem value="medium">Medium Value</SelectItem>
                <SelectItem value="low">Low Value</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="predictions" className="space-y-4">
          {filteredPredictions.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No predictions available</h3>
                <p className="text-gray-600">Check back soon for AI-generated predictions</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredPredictions.map(prediction => (
                <PredictionCard
                  key={prediction.id}
                  prediction={prediction}
                  onViewDetails={handleViewDetails}
                  onPlaceBet={handlePlaceBet}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {events.map(event => (
              <Card key={event.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline">{event.sport}</Badge>
                    <Badge variant="outline">{event.league}</Badge>
                  </div>
                  
                  <h4 className="font-semibold mb-2">
                    {event.teams.home.name} vs {event.teams.away.name}
                  </h4>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{new Date(event.event_date).toLocaleDateString()}</span>
                    <span>{new Date(event.event_date).toLocaleTimeString()}</span>
                  </div>
                  
                  <Button variant="outline" size="sm" className="w-full mt-3">
                    <Zap className="h-4 w-4 mr-1" />
                    Generate Prediction
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {analytics && (
            <>
              {/* Top Leagues Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>League Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.top_leagues.map(league => (
                      <div key={league.name} className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">{league.name}</span>
                          <span className="text-sm text-gray-600 ml-2">
                            {league.predictions} predictions
                          </span>
                        </div>
                        <div className="text-right">
                          <span className="font-semibold text-green-600">
                            {league.accuracy}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Performance Trend */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.recent_performance.map(day => (
                      <div key={day.date} className="flex items-center justify-between">
                        <span className="text-sm">
                          {new Date(day.date).toLocaleDateString()}
                        </span>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">
                            {day.predictions} predictions
                          </span>
                          <span className={`font-semibold ${
                            day.accuracy >= 75 ? 'text-green-600' : 
                            day.accuracy >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {day.accuracy}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}