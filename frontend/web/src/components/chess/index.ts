/**
 * BetBet Chess Components - Export Index
 * ======================================
 * 
 * Central export file for all chess-related components.
 */

// Main chess game component
export { ChessGame, default } from './ChessGame';

// Core chess components
export { ChessGamePage } from './ChessGamePage';
export { ChessBoard } from './ChessBoard';
export { ChessPiece } from './ChessPiece';
export { ChessSquare } from './ChessSquare';

// Modal and UI components
export { ChessGameCreationModal } from './ChessGameCreationModal';

// Hooks
export { useChessGame } from '../../hooks/useChessGame';
export { useSoundEffects } from '../../hooks/useSoundEffects';

// Types (re-export for convenience)
export type { 
  ChessGameState,
  ChessGameInfo,
  ChessMove,
  UseChessGameOptions 
} from '../../hooks/useChessGame';