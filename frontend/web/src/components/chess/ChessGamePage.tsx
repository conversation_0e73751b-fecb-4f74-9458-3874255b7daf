/**
 * BetBet Chess Game Page
 * ======================
 * 
 * Complete chess game interface with board, controls, and real-time features.
 */

import React from 'react';
import { ChessBoard } from './ChessBoard';
import { useChessGame } from '../../hooks/useChessGame';
import './ChessBoard.css';

interface ChessGamePageProps {
  gameId: string;
}

export const ChessGamePage: React.FC<ChessGamePageProps> = ({ gameId }) => {
  const {
    gameState,
    gameInfo,
    playerColor,
    legalMoves,
    isPlayerTurn,
    opponentColor,
    connectionStatus,
    isLoading,
    error,
    makeMove,
    resignGame,
    offerDraw,
    respondToDraw,
    sendChatMessage,
    reconnect
  } = useChessGame({
    gameId,
    onGameEnd: (result) => {
      console.log('Game ended:', result);
      // Handle game end
    },
    onError: (error) => {
      console.error('Chess game error:', error);
    },
    onOpponentDisconnect: () => {
      console.log('Opponent disconnected');
    },
    onOpponentReconnect: () => {
      console.log('Opponent reconnected');
    }
  });
  
  // Loading state
  if (isLoading) {
    return (
      <div className="chess-game-loading">
        <div className="chess-loading-spinner" />
        <p>Loading chess game...</p>
      </div>
    );
  }
  
  // Error state
  if (error && !gameState) {
    return (
      <div className="chess-game-error">
        <h2>Error Loading Game</h2>
        <p>{error}</p>
        <button onClick={reconnect} className="chess-retry-button">
          Retry Connection
        </button>
      </div>
    );
  }
  
  // No game state yet
  if (!gameState || !gameInfo) {
    return (
      <div className="chess-game-waiting">
        <p>Waiting for game data...</p>
      </div>
    );
  }
  
  return (
    <div className="chess-game-page">
      {/* Game Header */}
      <div className="chess-game-header">
        <div className="chess-player-info chess-player-info--opponent">
          <div className="chess-player-name">
            {playerColor === 'white' ? gameInfo.blackPlayerName : gameInfo.whitePlayerName}
          </div>
          <div className="chess-player-time">
            {playerColor === 'white' 
              ? Math.floor(gameState.blackTimeRemaining / 60) + ':' + 
                (gameState.blackTimeRemaining % 60).toString().padStart(2, '0')
              : Math.floor(gameState.whiteTimeRemaining / 60) + ':' + 
                (gameState.whiteTimeRemaining % 60).toString().padStart(2, '0')
            }
          </div>
        </div>
        
        <div className="chess-game-status">
          <div className="chess-connection-indicator">
            <span className={`chess-connection-dot chess-connection-dot--${connectionStatus}`} />
            {connectionStatus}
          </div>
          
          {gameInfo.status === 'active' && (
            <div className="chess-turn-indicator">
              {isPlayerTurn ? 'Your turn' : `${opponentColor}'s turn`}
            </div>
          )}
          
          {gameState.isCheck && (
            <div className="chess-check-indicator">
              ⚠️ Check!
            </div>
          )}
          
          {gameState.isCheckmate && (
            <div className="chess-checkmate-indicator">
              👑 Checkmate!
            </div>
          )}
        </div>
      </div>
      
      {/* Chess Board */}
      <div className="chess-game-board">
        <ChessBoard
          position={gameState.fen}
          playerColor={playerColor}
          isPlayerTurn={Boolean(isPlayerTurn)}
          legalMoves={legalMoves}
          lastMove={gameState.moveHistory[gameState.moveHistory.length - 1]}
          onMove={makeMove}
          isCheck={gameState.isCheck}
          isCheckmate={gameState.isCheckmate}
          gameStatus={gameInfo.status}
          showCoordinates={true}
          showLegalMoves={true}
          showLastMove={true}
          animateMovement={true}
          boardTheme="modern"
          allowSpectatorInteraction={false}
        />
      </div>
      
      {/* Game Controls */}
      <div className="chess-game-controls">
        <div className="chess-player-info chess-player-info--self">
          <div className="chess-player-name">
            {playerColor === 'white' ? gameInfo.whitePlayerName : gameInfo.blackPlayerName}
            <span className="chess-player-color-indicator">
              ({playerColor === 'spectator' ? 'Spectator' : playerColor})
            </span>
          </div>
          <div className="chess-player-time">
            {playerColor === 'white' 
              ? Math.floor(gameState.whiteTimeRemaining / 60) + ':' + 
                (gameState.whiteTimeRemaining % 60).toString().padStart(2, '0')
              : Math.floor(gameState.blackTimeRemaining / 60) + ':' + 
                (gameState.blackTimeRemaining % 60).toString().padStart(2, '0')
            }
          </div>
        </div>
        
        {/* Action Buttons */}
        {playerColor !== 'spectator' && gameInfo.status === 'active' && (
          <div className="chess-action-buttons">
            <button 
              className="chess-action-button chess-action-button--draw"
              onClick={offerDraw}
            >
              Offer Draw
            </button>
            
            <button 
              className="chess-action-button chess-action-button--resign"
              onClick={resignGame}
            >
              Resign
            </button>
          </div>
        )}
      </div>
      
      {/* Move History Sidebar */}
      <div className="chess-move-history">
        <h3>Move History</h3>
        <div className="chess-moves-list">
          {gameState.moves.map((move, index) => (
            <div key={index} className="chess-move-item">
              <span className="chess-move-number">{Math.floor(index / 2) + 1}.</span>
              <span className="chess-move-notation">{move}</span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Game Info */}
      <div className="chess-game-info">
        <div className="chess-game-details">
          <div><strong>Variant:</strong> {gameInfo.variant}</div>
          <div><strong>Time Control:</strong> {gameInfo.timeControl}</div>
          <div><strong>Spectators:</strong> {gameInfo.spectatorCount}</div>
        </div>
        
        {error && (
          <div className="chess-error-message">
            ⚠️ {error}
          </div>
        )}
      </div>
    </div>
  );
};

// Add basic styles for the game page
const gamePageStyles = `
.chess-game-page {
  display: grid;
  grid-template-columns: 1fr auto 300px;
  grid-template-rows: auto 1fr auto;
  gap: 1rem;
  padding: 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: white;
}

.chess-game-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.chess-game-board {
  grid-row: 2;
  grid-column: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chess-game-controls {
  grid-column: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.chess-move-history {
  grid-column: 3;
  grid-row: 2;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  max-height: 600px;
  overflow-y: auto;
}

.chess-game-info {
  grid-column: 3;
  grid-row: 3;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.chess-player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.chess-player-name {
  font-size: 1.1rem;
  font-weight: 600;
}

.chess-player-time {
  font-size: 1.5rem;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.chess-connection-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.chess-connection-dot--connected { background: #22c55e; }
.chess-connection-dot--connecting { background: #f59e0b; }
.chess-connection-dot--disconnected { background: #ef4444; }
.chess-connection-dot--error { background: #dc2626; }

.chess-action-buttons {
  display: flex;
  gap: 1rem;
}

.chess-action-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chess-action-button--draw {
  background: #3b82f6;
  color: white;
}

.chess-action-button--resign {
  background: #ef4444;
  color: white;
}

.chess-action-button:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.chess-moves-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.25rem;
  max-height: 400px;
  overflow-y: auto;
}

.chess-move-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.chess-move-item:nth-child(odd) {
  background: rgba(255, 255, 255, 0.1);
}

.chess-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chess-game-loading,
.chess-game-error,
.chess-game-waiting {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  text-align: center;
}

@media (max-width: 1024px) {
  .chess-game-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
  }
  
  .chess-move-history,
  .chess-game-info {
    grid-column: 1;
  }
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = gamePageStyles;
  document.head.appendChild(styleSheet);
}