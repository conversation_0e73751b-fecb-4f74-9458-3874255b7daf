/**
 * BetBet Chess Game Component
 * ===========================
 * 
 * Complete chess game interface with real-time gameplay, spectator betting,
 * and P2P tournament integration. This is the main component that handles
 * chess game sessions and routing.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { ChessGamePage } from './ChessGamePage';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { parseChessGameSlug, generateChessGameSlug, type ChessGame as ChessGameType } from '@/lib/chess-slug-utils';
import {
  Crown,
  Users,
  Clock,
  Eye,
  ArrowLeft,
  Loader2,
  AlertCircle,
  Trophy,
  Target,
  Zap
} from 'lucide-react';

interface ChessGameProps {
  gameId?: string; // Can be either a UUID (legacy) or a slug (new format)
  mode?: 'play' | 'spectate' | 'browse';
  variant?: 'standard' | 'chess960' | 'king_of_the_hill' | 'three_check' | 'antichess';
  timeControl?: 'bullet' | 'blitz' | 'rapid' | 'classical';
  isRated?: boolean;
  onGameEnd?: (result: string) => void;
  onError?: (error: string) => void;
}

interface GameSession {
  id: string;
  slug: string;
  game_id: string;
  status: 'waiting' | 'active' | 'paused' | 'completed';
  player_count: number;
  max_players: number;
  created_at: string;
  updated_at: string;
  is_public: boolean;
  white_player?: {
    id: string;
    username: string;
    rating?: number;
  };
  black_player?: {
    id: string;
    username: string;
    rating?: number;
  };
  spectator_count: number;
  variant: string;
  time_control: string;
  is_rated: boolean;
  result?: string;
}

export const ChessGame: React.FC<ChessGameProps> = ({
  gameId,
  mode = 'play',
  variant = 'standard',
  timeControl = 'blitz',
  isRated = true,
  onGameEnd,
  onError
}) => {
  const router = useRouter();
  const { getToken, userId } = useAuth();
  
  // State
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [loading, setLoading] = useState(!!gameId);
  const [error, setError] = useState<string | null>(null);
  const [playerRole, setPlayerRole] = useState<'white' | 'black' | 'spectator'>('spectator');

  // Load game session if gameId is provided
  useEffect(() => {
    if (gameId) {
      loadGameSession();
    }
  }, [gameId]);

  const loadGameSession = async () => {
    if (!gameId) return;

    try {
      setLoading(true);
      setError(null);

      const token = await getToken();

      // Check if gameId is a UUID (legacy) or a slug (new format)
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(gameId);

      // Parse slug if it's not a UUID
      let slugInfo = null;
      if (!isUUID) {
        slugInfo = parseChessGameSlug(gameId);
        if (!slugInfo) {
          setError('Invalid game URL format');
          setLoading(false);
          return;
        }
      }

      // Fetch all chess games to find the matching one
      let response = await fetch('/api/gaming/chess/games', {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      });

      if (response.ok) {
        const chessGamesData = await response.json();
        let chessGame = null;

        if (isUUID) {
          // Legacy UUID lookup
          chessGame = chessGamesData.games?.find((game: any) => game.id === gameId);
        } else {
          // Slug-based lookup - find game that matches the slug
          chessGame = chessGamesData.games?.find((game: ChessGameType) => {
            const gameSlug = generateChessGameSlug(game);
            return gameSlug === gameId;
          });
        }

        if (chessGame) {
          // Convert chess game to session format
          const sessionData = {
            id: chessGame.id,
            slug: generateChessGameSlug(chessGame),
            game_id: chessGame.id,
            status: chessGame.status,
            player_count: chessGame.black_player ? 2 : 1,
            max_players: 2,
            created_at: chessGame.created_at,
            updated_at: chessGame.created_at,
            is_public: !chessGame.is_private,
            white_player: chessGame.white_player,
            black_player: chessGame.black_player,
            spectator_count: 0, // Will be updated via WebSocket
            variant: chessGame.variant,
            time_control: chessGame.time_control,
            is_rated: chessGame.is_rated,
            result: chessGame.result
          };
          setGameSession(sessionData);
          
          // Determine player role
          if (sessionData.white_player?.id === userId) {
            setPlayerRole('white');
          } else if (sessionData.black_player?.id === userId) {
            setPlayerRole('black');
          } else {
            setPlayerRole('spectator');
          }
          
          setLoading(false);
          return;
        }
      }
      
      // If not found as chess game, try to load the test chess game
      response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/gaming/chess/testgame`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load game: ${response.statusText}`);
      }

      const chessGameResponse = await response.json();
      const chessGameData = chessGameResponse.game;

      if (chessGameData) {
        // Convert chess game to session format
        const sessionData = {
          id: chessGameData.id,
          slug: chessGameData.id,
          game_id: chessGameData.id,
          game_type: 'chess',
          status: chessGameData.status,
          white_player: chessGameData.white_player,
          black_player: chessGameData.black_player,
          current_fen: chessGameData.current_fen,
          move_count: chessGameData.move_count,
          time_control: chessGameData.time_control,
          initial_time_seconds: chessGameData.initial_time_seconds,
          increment_seconds: chessGameData.increment_seconds,
          white_time_remaining: chessGameData.white_time_remaining,
          black_time_remaining: chessGameData.black_time_remaining,
          variant: chessGameData.variant,
          is_rated: chessGameData.is_rated,
          wager_amount: chessGameData.wager_amount,
          description: chessGameData.description
        };

        setGameSession(sessionData);

        // Determine player role
        if (sessionData.white_player?.id === userId) {
          setPlayerRole('white');
        } else if (sessionData.black_player?.id === userId) {
          setPlayerRole('black');
        } else {
          setPlayerRole('spectator');
        }
      } else {
        throw new Error('Chess game not found');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load game session';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleGameEnd = (result: string) => {
    console.log('Chess game ended:', result);
    onGameEnd?.(result);
    
    // Optionally navigate back to games list
    setTimeout(() => {
      router.push('/games/chess');
    }, 3000);
  };

  const handleError = (error: string) => {
    setError(error);
    onError?.(error);
  };

  const getVariantDisplay = (variant: string) => {
    switch (variant) {
      case 'standard': return 'Standard Chess';
      case 'chess960': return 'Chess960 (Fischer Random)';
      case 'king_of_the_hill': return 'King of the Hill';
      case 'three_check': return 'Three-check';
      case 'antichess': return 'Antichess';
      default: return variant.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getTimeControlDisplay = (timeControl: string) => {
    switch (timeControl) {
      case 'bullet': return 'Bullet (1-2 min)';
      case 'blitz': return 'Blitz (3-5 min)';
      case 'rapid': return 'Rapid (10-15 min)';
      case 'classical': return 'Classical (30+ min)';
      default: return timeControl.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getVariantIcon = (variant: string) => {
    switch (variant) {
      case 'standard': return <Crown className="h-5 w-5 text-yellow-400" />;
      case 'chess960': return <Zap className="h-5 w-5 text-purple-400" />;
      case 'king_of_the_hill': return <Target className="h-5 w-5 text-red-400" />;
      case 'three_check': return <Trophy className="h-5 w-5 text-blue-400" />;
      case 'antichess': return <Crown className="h-5 w-5 text-green-400 transform rotate-180" />;
      default: return <Crown className="h-5 w-5 text-yellow-400" />;
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] p-8">
        <div className="text-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-blue-400 mx-auto" />
          <h2 className="text-2xl font-bold text-white">Loading Chess Game</h2>
          <p className="text-slate-300">Connecting to game session...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !gameSession) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] p-8">
        <Card className="w-full max-w-md bg-slate-800/90 border-slate-700">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-6 w-6 text-red-400" />
              <CardTitle className="text-xl text-white">Error Loading Game</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-slate-300">{error}</p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/games/chess')}
                className="flex-1 bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Chess
              </Button>
              <Button
                onClick={loadGameSession}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Game session view - render the actual chess game
  if (gameSession && gameId) {
    return (
      <div className="min-h-[calc(100vh-4rem)]">
        {/* Game Header */}
        <div className="border-b border-slate-700 bg-slate-800/50 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/games/chess')}
                  className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Chess Games
                </Button>
                
                <div className="flex items-center space-x-3">
                  {getVariantIcon(gameSession.variant)}
                  <div>
                    <h1 className="text-xl font-bold text-white">
                      {getVariantDisplay(gameSession.variant)}
                    </h1>
                    <div className="flex items-center space-x-4 text-sm text-slate-300">
                      <span className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{getTimeControlDisplay(gameSession.time_control)}</span>
                      </span>
                      <Badge variant={gameSession.is_rated ? 'default' : 'secondary'}>
                        {gameSession.is_rated ? 'Rated' : 'Casual'}
                      </Badge>
                      <span className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{gameSession.spectator_count} watching</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Player Info */}
              <div className="flex items-center space-x-6">
                <div className="text-right">
                  <div className="text-sm text-slate-400">
                    {gameSession.white_player ? 'White' : 'Waiting for White'}
                  </div>
                  <div className="font-semibold text-white">
                    {gameSession.white_player?.username || 'Open Seat'}
                  </div>
                  {gameSession.white_player?.rating && (
                    <div className="text-xs text-slate-300">
                      {gameSession.white_player.rating} rating
                    </div>
                  )}
                </div>
                
                <div className="text-center text-slate-400">vs</div>
                
                <div className="text-left">
                  <div className="text-sm text-slate-400">
                    {gameSession.black_player ? 'Black' : 'Waiting for Black'}
                  </div>
                  <div className="font-semibold text-white">
                    {gameSession.black_player?.username || 'Open Seat'}
                  </div>
                  {gameSession.black_player?.rating && (
                    <div className="text-xs text-slate-300">
                      {gameSession.black_player.rating} rating
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Chess Game Component */}
        <ChessGamePage 
          gameId={gameId}
          onGameEnd={handleGameEnd}
          onError={handleError}
          onOpponentDisconnect={() => console.log('Opponent disconnected')}
          onOpponentReconnect={() => console.log('Opponent reconnected')}
        />
      </div>
    );
  }

  // Default browse mode - this shouldn't happen if used correctly
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] p-8">
      <Card className="w-full max-w-md bg-slate-800/90 border-slate-700">
        <CardHeader>
          <CardTitle className="text-xl text-white flex items-center space-x-2">
            <Crown className="h-6 w-6 text-yellow-400" />
            <span>Chess Game</span>
          </CardTitle>
          <CardDescription className="text-slate-300">
            No game session provided. Please navigate from the chess games list.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => router.push('/games/chess')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Crown className="h-4 w-4 mr-2" />
            Browse Chess Games
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

// Export additional chess-related components for convenience
export { ChessGamePage } from './ChessGamePage';
export { ChessBoard } from './ChessBoard';
export { ChessPiece } from './ChessPiece';
export { ChessSquare } from './ChessSquare';
export { ChessGameCreationModal } from './ChessGameCreationModal';

// Export the main component as default
export default ChessGame;