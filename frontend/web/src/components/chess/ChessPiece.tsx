/**
 * BetBet Chess Piece Component
 * ============================
 * 
 * Individual chess piece with drag & drop and animations.
 */

import React, { memo } from 'react';

interface ChessPieceData {
  type: 'pawn' | 'rook' | 'knight' | 'bishop' | 'queen' | 'king';
  color: 'white' | 'black';
  position: {
    file: string;
    rank: number;
  };
  hasMoved?: boolean;
}

interface ChessPieceProps {
  piece: ChessPieceData;
  isDragging: boolean;
  isSelected: boolean;
  onDragStart: (event: React.DragEvent<HTMLDivElement>) => void;
  playerColor: 'white' | 'black' | 'spectator';
  animateMovement?: boolean;
}

export const ChessPiece: React.FC<ChessPieceProps> = memo(({
  piece,
  isDragging,
  isSelected,
  onDragStart,
  playerColor,
  animateMovement = true
}) => {
  // Don't render if piece is being dragged (show drag preview instead)
  if (isDragging) {
    return null;
  }
  
  // Piece classes for styling
  const pieceClasses = [
    'chess-piece',
    `chess-piece--${piece.color}`,
    `chess-piece--${piece.type}`,
    isSelected ? 'chess-piece--selected' : '',
    piece.color === playerColor ? 'chess-piece--own' : 'chess-piece--opponent',
    animateMovement ? 'chess-piece--animated' : '',
    piece.hasMoved ? 'chess-piece--moved' : ''
  ].filter(Boolean).join(' ');
  
  // Determine if piece should be draggable
  const isDraggable = playerColor !== 'spectator' && piece.color === playerColor;
  
  return (
    <div
      className={pieceClasses}
      draggable={isDraggable}
      onDragStart={isDraggable ? onDragStart : undefined}
      style={{
        // Pieces rotate back to stay upright when board is rotated for black
        transform: playerColor === 'black' ? 'rotate(180deg)' : 'none',
        cursor: isDraggable ? 'grab' : 'default',
        opacity: isDragging ? 0.5 : 1
      }}
    >
      <img
        src={`/assets/chess/pieces/${piece.color}-${piece.type}.png`}
        alt={`${piece.color} ${piece.type}`}
        className="chess-piece-image"
        draggable={false} // Prevent default image drag
      />
      
      {/* Selection indicator */}
      {isSelected && (
        <div className="chess-piece-selection-ring" />
      )}
      
      {/* Piece value indicator for learning mode */}
      {process.env.NODE_ENV === 'development' && (
        <div className="chess-piece-debug-info">
          {piece.position.file}{piece.position.rank}
        </div>
      )}
    </div>
  );
});

ChessPiece.displayName = 'ChessPiece';