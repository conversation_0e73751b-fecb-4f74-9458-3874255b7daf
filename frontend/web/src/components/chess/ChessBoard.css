/**
 * BetBet Chess Board Styles
 * =========================
 * 
 * Responsive chess board with perspective rotation and modern design.
 */

/* Chess Board Container */
.chess-board-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
}

/* Main Chess Board */
.chess-board {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(8, 1fr);
  gap: 0;
  width: min(80vw, 640px);
  height: min(80vw, 640px);
  max-width: 640px;
  max-height: 640px;
  border: 4px solid #94a3b8;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
  position: relative;
  background: #f8fafc;
}

/* Black Player Perspective - Board Rotation */
.chess-board--black-perspective {
  transform: rotate(180deg);
}

/* Board Themes */
.chess-board--classic {
  background: #f1f5f9;
  border-color: #8b5cf6;
}

.chess-board--modern {
  background: #1e293b;
  border-color: #06b6d4;
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.3);
}

.chess-board--neon {
  background: #0f172a;
  border-color: #a855f7;
  box-shadow: 0 0 40px rgba(168, 85, 247, 0.4);
}

/* Game State Styles */
.chess-board--check {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
  animation: checkPulse 1.5s ease-in-out infinite;
}

.chess-board--checkmate {
  box-shadow: 0 0 40px rgba(239, 68, 68, 0.8);
  filter: saturate(1.2);
}

.chess-board--game-over {
  opacity: 0.9;
  filter: grayscale(0.2);
}

.chess-board--waiting {
  opacity: 0.8;
  cursor: not-allowed;
}

@keyframes checkPulse {
  0%, 100% { box-shadow: 0 0 30px rgba(239, 68, 68, 0.6); }
  50% { box-shadow: 0 0 50px rgba(239, 68, 68, 0.9); }
}

/* Chess Squares */
.chess-square {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  overflow: hidden;
}

/* Square Colors - Classic Theme */
.chess-board--classic .chess-square--light {
  background-color: #f1f5f9;
}

.chess-board--classic .chess-square--dark {
  background-color: #64748b;
}

/* Square Colors - Modern Theme */
.chess-board--modern .chess-square--light {
  background-color: #374151;
}

.chess-board--modern .chess-square--dark {
  background-color: #1f2937;
}

/* Square Colors - Neon Theme */
.chess-board--neon .chess-square--light {
  background-color: #1e1b4b;
}

.chess-board--neon .chess-square--dark {
  background-color: #0f0f23;
}

/* Square States */
.chess-square--selected {
  background-color: rgba(34, 197, 94, 0.6) !important;
  box-shadow: inset 0 0 20px rgba(34, 197, 94, 0.4);
}

.chess-square--last-move {
  background-color: rgba(251, 191, 36, 0.4) !important;
}

.chess-square--hovered {
  background-color: rgba(59, 130, 246, 0.3) !important;
  transform: scale(1.02);
}

.chess-square--king-in-check {
  background-color: rgba(239, 68, 68, 0.7) !important;
  animation: checkSquarePulse 1s ease-in-out infinite;
}

@keyframes checkSquarePulse {
  0%, 100% { background-color: rgba(239, 68, 68, 0.7) !important; }
  50% { background-color: rgba(239, 68, 68, 0.9) !important; }
}

/* Legal Move Indicators */
.chess-square--legal-move {
  position: relative;
}

.chess-square-legal-indicator {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  z-index: 2;
}

.chess-square-legal-indicator--move {
  width: 30%;
  height: 30%;
  background-color: rgba(34, 197, 94, 0.6);
  border: 2px solid rgba(34, 197, 94, 0.8);
}

.chess-square-legal-indicator--capture {
  width: 90%;
  height: 90%;
  background-color: transparent;
  border: 4px solid rgba(239, 68, 68, 0.7);
  border-radius: 8px;
}

/* Coordinate Labels */
.chess-square-coordinate {
  position: absolute;
  font-size: 12px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.6);
  pointer-events: none;
  z-index: 1;
}

.chess-board--modern .chess-square-coordinate,
.chess-board--neon .chess-square-coordinate {
  color: rgba(255, 255, 255, 0.6);
}

.chess-square-coordinate--file {
  bottom: 2px;
  right: 4px;
}

.chess-square-coordinate--rank {
  top: 2px;
  left: 4px;
}

/* Square Content */
.chess-square-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

/* Chess Pieces */
.chess-piece {
  position: relative;
  width: 85%;
  height: 85%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 4;
  transition: transform 0.2s ease;
}

.chess-piece:active {
  cursor: grabbing;
}

.chess-piece--animated {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chess-piece--selected {
  transform: scale(1.1);
  filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.6));
}

.chess-piece--own {
  cursor: grab;
}

.chess-piece--opponent {
  cursor: default;
}

.chess-piece-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  transition: filter 0.2s ease;
}

.chess-piece:hover .chess-piece-image {
  filter: drop-shadow(2px 2px 8px rgba(0, 0, 0, 0.5));
}

/* Selection Ring */
.chess-piece-selection-ring {
  position: absolute;
  top: -5%;
  left: -5%;
  width: 110%;
  height: 110%;
  border: 3px solid #22c55e;
  border-radius: 50%;
  pointer-events: none;
  animation: selectionPulse 2s ease-in-out infinite;
}

@keyframes selectionPulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.8;
  }
  50% { 
    transform: scale(1.05);
    opacity: 1;
  }
}

/* Drag Preview */
.chess-drag-preview {
  pointer-events: none;
  z-index: 1000;
}

.chess-drag-preview .chess-piece-image {
  filter: drop-shadow(4px 4px 12px rgba(0, 0, 0, 0.6));
}

/* Promotion Dialog */
.chess-promotion-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chess-promotion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.chess-promotion-content {
  position: relative;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 90vw;
}

.chess-promotion-content h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.chess-promotion-pieces {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.chess-promotion-piece {
  width: 80px;
  height: 80px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.chess-promotion-piece:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.05);
}

.chess-promotion-piece img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Debug Styles (Development Only) */
.chess-square-debug {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 8px;
  color: rgba(255, 0, 0, 0.5);
  pointer-events: none;
  z-index: 10;
}

.chess-square-debug-notation {
  display: block;
  font-weight: bold;
}

.chess-square-debug-coords {
  display: block;
  font-size: 6px;
}

.chess-piece-debug-info {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 8px;
  color: rgba(255, 0, 0, 0.7);
  font-weight: bold;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.8);
  padding: 1px 3px;
  border-radius: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chess-board-container {
    padding: 0.5rem;
  }
  
  .chess-board {
    width: min(95vw, 500px);
    height: min(95vw, 500px);
    border-width: 2px;
  }
  
  .chess-square-coordinate {
    font-size: 10px;
  }
  
  .chess-promotion-content {
    padding: 1.5rem;
  }
  
  .chess-promotion-content h3 {
    font-size: 1.25rem;
  }
  
  .chess-promotion-piece {
    width: 60px;
    height: 60px;
  }
  
  .chess-promotion-pieces {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .chess-board {
    width: min(98vw, 400px);
    height: min(98vw, 400px);
  }
  
  .chess-promotion-piece {
    width: 50px;
    height: 50px;
  }
  
  .chess-square-coordinate {
    font-size: 8px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chess-piece-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .chess-piece--animated,
  .chess-board,
  .chess-square,
  .chess-piece {
    transition: none;
    animation: none;
  }
  
  .chess-piece-selection-ring {
    animation: none;
  }
  
  .chess-board--check {
    animation: none;
  }
  
  .chess-square--king-in-check {
    animation: none;
  }
}

/* Focus Styles for Accessibility */
.chess-square:focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: -3px;
}

.chess-piece:focus-visible {
  outline: 2px solid #22c55e;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .chess-board-container {
    box-shadow: none;
    background: white;
  }
  
  .chess-board {
    border: 2px solid #000;
  }
  
  .chess-square--light {
    background-color: #fff !important;
  }
  
  .chess-square--dark {
    background-color: #ccc !important;
  }
}