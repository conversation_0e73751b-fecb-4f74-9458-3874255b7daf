import React, { useState } from 'react';
import {
  X,
  Users,
  Clock,
  DollarSign,
  Crown,
  Shield,
  AlertCircle,
  Play,
  Loader2,
  Wallet,
  Lock,
  Info
} from 'lucide-react';
import { BoardPreview } from './BoardPreview';

interface GameSession {
  id: string;
  slug: string;
  title: string;
  gameType: string;
  mode: string;
  status: 'waiting' | 'running' | 'finished';
  players: string[];
  maxPlayers: number;
  spectators: number;
  stakes?: string;
  createdAt: string;
  estimatedDuration?: string;
  gameState?: any;
  config?: {
    timeControl: string;
    wagerAmount: number;
    playerColor: 'white' | 'black' | 'random';
    allowSpectators: boolean;
    isPrivate: boolean;
    variant: string;
    requirePassword?: boolean;
  };
}

interface ChessJoinGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  gameSession: GameSession;
  onJoinGame: (gameId: string, password?: string) => Promise<void>;
  isJoining?: boolean;
  error?: string | null;
  userBalance?: number;
}

export const ChessJoinGameModal: React.FC<ChessJoinGameModalProps> = ({
  isOpen,
  onClose,
  gameSession,
  onJoinGame,
  isJoining = false,
  error = null,
  userBalance = 1000 // Mock user balance
}) => {
  const [password, setPassword] = useState('');
  const [confirmWager, setConfirmWager] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const wagerAmount = parseInt(gameSession.stakes?.replace('$', '') || '0');
  const hasInsufficientFunds = userBalance < wagerAmount;
  const requiresPassword = gameSession.config?.requirePassword;

  const handleJoinGame = async () => {
    if (!confirmWager || !agreedToTerms) return;
    if (requiresPassword && !password) return;
    if (hasInsufficientFunds) return;

    try {
      await onJoinGame(gameSession.id, requiresPassword ? password : undefined);
    } catch (error) {
      // Error handled by parent
    }
  };

  const getAssignedColor = () => {
    const creatorColor = gameSession.config?.playerColor;
    if (creatorColor === 'white') return 'black';
    if (creatorColor === 'black') return 'white';
    return 'random'; // Will be assigned randomly
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just created';
    if (diffMins < 60) return `${diffMins} min ago`;
    const diffHours = Math.floor(diffMins / 60);
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900 border border-slate-700 rounded-xl w-full max-w-2xl max-h-[85vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-green-600 rounded-lg">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Join Chess Game</h2>
              <p className="text-sm text-slate-400">Review game details and place your wager</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-white transition-colors"
            disabled={isJoining}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4 space-y-4 max-h-[calc(85vh-180px)] overflow-y-auto">
          {/* Game Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Game Preview */}
            <div className="space-y-3">
              <h3 className="text-base font-semibold text-white flex items-center space-x-2">
                <Crown className="h-4 w-4 text-yellow-400" />
                <span>Game Preview</span>
              </h3>
              
              <div className="bg-slate-800 rounded-lg p-3">
                <div className="flex justify-center mb-3">
                  <BoardPreview 
                    gameState={gameSession.gameState}
                    gameType="chess"
                    size="medium" 
                  />
                </div>
                <div className="text-center">
                  <div className="text-sm text-slate-400 mb-2">You will play as:</div>
                  <div className="text-lg font-semibold text-white">
                    {getAssignedColor() === 'white' && '♔ White Pieces'}
                    {getAssignedColor() === 'black' && '♚ Black Pieces'}
                    {getAssignedColor() === 'random' && '🎲 Random Color'}
                  </div>
                </div>
              </div>
            </div>

            {/* Game Details */}
            <div className="space-y-3">
              <h3 className="text-base font-semibold text-white flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-400" />
                <span>Game Details</span>
              </h3>
              
              <div className="space-y-2">
                <div className="bg-slate-800 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Opponent</span>
                    <span className="text-white font-medium">{gameSession.players[0] || 'Unknown'}</span>
                  </div>
                </div>

                <div className="bg-slate-800 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Time Control</span>
                    <span className="text-white font-medium">{gameSession.mode}</span>
                  </div>
                </div>

                <div className="bg-slate-800 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Variant</span>
                    <span className="text-white font-medium">
                      {gameSession.config?.variant || 'Standard'}
                    </span>
                  </div>
                </div>

                <div className="bg-slate-800 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Created</span>
                    <span className="text-white font-medium">{formatTimeAgo(gameSession.createdAt)}</span>
                  </div>
                </div>

                <div className="bg-slate-800 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Spectators</span>
                    <span className="text-purple-400 font-medium">
                      {gameSession.config?.allowSpectators ? 'Allowed' : 'Not allowed'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Wager Section */}
          <div className="space-y-3">
            <h3 className="text-base font-semibold text-white flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span>Wager Details</span>
            </h3>
            
            <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg p-3">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="text-lg font-bold text-white">${wagerAmount}</div>
                  <div className="text-sm text-green-400">Required to join</div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-slate-400">Your balance</div>
                  <div className={`text-lg font-semibold ${hasInsufficientFunds ? 'text-red-400' : 'text-green-400'}`}>
                    ${userBalance}
                  </div>
                </div>
              </div>

              {hasInsufficientFunds && (
                <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-3 mb-4">
                  <div className="flex items-center space-x-2 text-red-400">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">Insufficient funds</span>
                  </div>
                  <div className="text-xs text-red-300 mt-1">
                    You need ${wagerAmount - userBalance} more to join this game
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={confirmWager}
                    onChange={(e) => setConfirmWager(e.target.checked)}
                    disabled={hasInsufficientFunds}
                    className="w-4 h-4 text-green-600 disabled:opacity-50"
                  />
                  <span className="text-white text-sm">
                    I confirm that I want to wager <span className="font-bold text-green-400">${wagerAmount}</span> for this game
                  </span>
                </label>

                <div className="text-xs text-slate-400 ml-7">
                  Funds will be held in escrow until the game concludes. Winner takes all.
                </div>
              </div>
            </div>
          </div>

          {/* Password Section */}
          {requiresPassword && (
            <div className="space-y-3">
              <h3 className="text-base font-semibold text-white flex items-center space-x-2">
                <Lock className="h-4 w-4 text-orange-400" />
                <span>Password Required</span>
              </h3>
              
              <div className="bg-slate-800 rounded-lg p-3">
                <label className="text-sm text-slate-400 mb-2 block">Game Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                  placeholder="Enter game password"
                />
                <div className="text-xs text-slate-500 mt-2">
                  This game requires a password to join
                </div>
              </div>
            </div>
          )}

          {/* Terms Agreement */}
          <div className="space-y-3">
            <h3 className="text-base font-semibold text-white flex items-center space-x-2">
              <Shield className="h-4 w-4 text-purple-400" />
              <span>Terms & Conditions</span>
            </h3>
            
            <div className="bg-slate-800 rounded-lg p-3 space-y-2">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  className="w-4 h-4 text-purple-600 mt-0.5"
                />
                <div className="text-sm text-white">
                  I agree to the game terms and conditions:
                  <ul className="list-disc list-inside text-xs text-slate-400 mt-2 space-y-1">
                    <li>Fair play rules apply - no cheating or external assistance</li>
                    <li>Game decisions are final and binding</li>
                    <li>Wagers are non-refundable once the game begins</li>
                    <li>Disconnection during play may result in forfeit</li>
                  </ul>
                </div>
              </label>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-3">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-slate-700 space-y-3">
          <div className="text-xs text-slate-400 text-center">
            Game ID: <span className="font-mono text-slate-300">{gameSession.slug}</span>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2.5 text-slate-400 hover:text-white transition-colors border border-slate-600 rounded-lg text-sm"
              disabled={isJoining}
            >
              Cancel
            </button>
            
            <button
              onClick={handleJoinGame}
              disabled={isJoining || !confirmWager || !agreedToTerms || hasInsufficientFunds || (requiresPassword && !password)}
              className="flex-[2] px-6 py-2.5 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              {isJoining ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Joining Game...</span>
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  <span>Join Game</span>
                  <span className="bg-white/20 px-2 py-1 rounded text-xs font-bold">${wagerAmount}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};