/**
 * BetBet Chess Board Component
 * ===========================
 * 
 * Interactive chess board with perspective rotation for P2P gameplay.
 * Players sit behind their pieces with CSS rotation for black player.
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { ChessPiece } from './ChessPiece';
import { ChessSquare } from './ChessSquare';
import { useSoundEffects } from '../../hooks/useSoundEffects';

// Types
interface Position {
  file: string; // a-h
  rank: number; // 1-8
}

interface ChessPieceData {
  type: 'pawn' | 'rook' | 'knight' | 'bishop' | 'queen' | 'king';
  color: 'white' | 'black';
  position: Position;
  hasMoved?: boolean;
}

interface ChessMove {
  from: Position;
  to: Position;
  piece: ChessPieceData;
  capturedPiece?: ChessPieceData;
  isCheck?: boolean;
  isCheckmate?: boolean;
  isCastling?: boolean;
  isEnPassant?: boolean;
  promotion?: 'queen' | 'rook' | 'bishop' | 'knight';
}

interface ChessBoardProps {
  // Game state
  position: string; // FEN string
  playerColor: 'white' | 'black' | 'spectator';
  isPlayerTurn: boolean;
  legalMoves: string[]; // UCI format moves
  lastMove?: ChessMove;
  
  // Interactive features
  onMove: (move: { from: string; to: string; promotion?: string }) => void;
  onSquareClick?: (square: string) => void;
  onPieceSelect?: (piece: ChessPieceData) => void;
  
  // Game status
  isCheck: boolean;
  isCheckmate: boolean;
  gameStatus: 'waiting' | 'active' | 'paused' | 'completed';
  
  // Visual options
  showCoordinates?: boolean;
  showLegalMoves?: boolean;
  showLastMove?: boolean;
  animateMovement?: boolean;
  boardTheme?: 'classic' | 'modern' | 'neon';
  
  // Spectator mode
  allowSpectatorInteraction?: boolean;
}

// Convert FEN to board array
const parseFEN = (fen: string): (ChessPieceData | null)[][] => {
  const [position] = fen.split(' ');
  const ranks = position.split('/');
  const board: (ChessPieceData | null)[][] = [];
  
  const pieceMap: { [key: string]: { type: ChessPieceData['type']; color: 'white' | 'black' } } = {
    'P': { type: 'pawn', color: 'white' }, 'p': { type: 'pawn', color: 'black' },
    'R': { type: 'rook', color: 'white' }, 'r': { type: 'rook', color: 'black' },
    'N': { type: 'knight', color: 'white' }, 'n': { type: 'knight', color: 'black' },
    'B': { type: 'bishop', color: 'white' }, 'b': { type: 'bishop', color: 'black' },
    'Q': { type: 'queen', color: 'white' }, 'q': { type: 'queen', color: 'black' },
    'K': { type: 'king', color: 'white' }, 'k': { type: 'king', color: 'black' },
  };
  
  ranks.forEach((rank, rankIndex) => {
    const row: (ChessPieceData | null)[] = [];
    let fileIndex = 0;
    
    for (const char of rank) {
      if (char >= '1' && char <= '8') {
        // Empty squares
        const emptyCount = parseInt(char);
        for (let i = 0; i < emptyCount; i++) {
          row.push(null);
          fileIndex++;
        }
      } else if (pieceMap[char]) {
        // Piece
        const piece = pieceMap[char];
        row.push({
          ...piece,
          position: {
            file: String.fromCharCode(97 + fileIndex), // a-h
            rank: 8 - rankIndex // 1-8
          }
        });
        fileIndex++;
      }
    }
    board.push(row);
  });
  
  return board;
};

// Convert position to square notation
const positionToSquare = (file: number, rank: number): string => {
  return String.fromCharCode(97 + file) + (8 - rank).toString();
};

// Convert square notation to position
const squareToPosition = (square: string): { file: number; rank: number } => {
  return {
    file: square.charCodeAt(0) - 97,
    rank: 8 - parseInt(square[1])
  };
};

export const ChessBoard: React.FC<ChessBoardProps> = ({
  position,
  playerColor,
  isPlayerTurn,
  legalMoves,
  lastMove,
  onMove,
  onSquareClick,
  onPieceSelect,
  isCheck,
  isCheckmate,
  gameStatus,
  showCoordinates = true,
  showLegalMoves = true,
  showLastMove = true,
  animateMovement = true,
  boardTheme = 'classic',
  allowSpectatorInteraction = false
}) => {
  // Hooks
  const { playMoveSound, playCaptureSound, playCheckSound } = useSoundEffects();
  
  // State
  const [board, setBoard] = useState<(ChessPieceData | null)[][]>([]);
  const [selectedSquare, setSelectedSquare] = useState<string | null>(null);
  const [draggedPiece, setDraggedPiece] = useState<ChessPieceData | null>(null);
  const [dragPosition, setDragPosition] = useState<{ x: number; y: number } | null>(null);
  const [hoveredSquare, setHoveredSquare] = useState<string | null>(null);
  const [promotionDialog, setPromotionDialog] = useState<{
    from: string;
    to: string;
    show: boolean;
  } | null>(null);
  
  // Refs
  const boardRef = useRef<HTMLDivElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  
  // Parse FEN when position changes
  useEffect(() => {
    setBoard(parseFEN(position));
  }, [position]);
  
  // Play sound effects for moves
  useEffect(() => {
    if (lastMove) {
      if (lastMove.isCheckmate) {
        // Special checkmate sound would go here
        playCheckSound();
      } else if (lastMove.isCheck) {
        playCheckSound();
      } else if (lastMove.capturedPiece) {
        playCaptureSound();
      } else {
        playMoveSound();
      }
    }
  }, [lastMove, playMoveSound, playCaptureSound, playCheckSound]);
  
  // Check if square is legal move destination
  const isLegalMove = useCallback((from: string, to: string): boolean => {
    const moveUCI = from + to;
    return legalMoves.includes(moveUCI) || 
           legalMoves.some(move => move.startsWith(moveUCI)); // Handle promotions
  }, [legalMoves]);
  
  // Get legal moves for selected piece
  const getLegalMovesForSquare = useCallback((square: string): string[] => {
    return legalMoves
      .filter(move => move.startsWith(square))
      .map(move => move.substring(2, 4));
  }, [legalMoves]);
  
  // Handle square click
  const handleSquareClick = useCallback((file: number, rank: number) => {
    const square = positionToSquare(file, rank);
    const piece = board[rank]?.[file];
    
    // Spectator mode restrictions
    if (playerColor === 'spectator' && !allowSpectatorInteraction) {
      return;
    }
    
    // Game must be active and player's turn
    if (gameStatus !== 'active' || !isPlayerTurn) {
      return;
    }
    
    // If no square selected, select this square if it has player's piece
    if (!selectedSquare) {
      if (piece && piece.color === playerColor) {
        setSelectedSquare(square);
        onPieceSelect?.(piece);
      }
      onSquareClick?.(square);
      return;
    }
    
    // If clicking the same square, deselect
    if (selectedSquare === square) {
      setSelectedSquare(null);
      onSquareClick?.(square);
      return;
    }
    
    // If clicking another piece of same color, select it
    if (piece && piece.color === playerColor) {
      setSelectedSquare(square);
      onPieceSelect?.(piece);
      onSquareClick?.(square);
      return;
    }
    
    // Otherwise, try to make a move
    if (isLegalMove(selectedSquare, square)) {
      makeMove(selectedSquare, square);
    }
    
    setSelectedSquare(null);
    onSquareClick?.(square);
  }, [board, selectedSquare, playerColor, gameStatus, isPlayerTurn, allowSpectatorInteraction, isLegalMove, onSquareClick, onPieceSelect]);
  
  // Handle piece drag start
  const handleDragStart = useCallback((piece: ChessPieceData, event: React.DragEvent) => {
    // Only allow dragging player's pieces during their turn
    if (playerColor === 'spectator' || 
        gameStatus !== 'active' || 
        !isPlayerTurn || 
        piece.color !== playerColor) {
      event.preventDefault();
      return;
    }
    
    const square = piece.position.file + piece.position.rank;
    setDraggedPiece(piece);
    setSelectedSquare(square);
    
    // Set drag image to be transparent (we'll show custom drag preview)
    const dragImage = new Image();
    dragImage.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    event.dataTransfer.setDragImage(dragImage, 0, 0);
    
    onPieceSelect?.(piece);
  }, [playerColor, gameStatus, isPlayerTurn, onPieceSelect]);
  
  // Handle drag over
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
  }, []);
  
  // Handle drop
  const handleDrop = useCallback((file: number, rank: number, event: React.DragEvent) => {
    event.preventDefault();
    
    if (!draggedPiece || !selectedSquare) return;
    
    const targetSquare = positionToSquare(file, rank);
    
    if (isLegalMove(selectedSquare, targetSquare)) {
      makeMove(selectedSquare, targetSquare);
    }
    
    setDraggedPiece(null);
    setSelectedSquare(null);
  }, [draggedPiece, selectedSquare, isLegalMove]);
  
  // Make move with promotion handling
  const makeMove = useCallback((from: string, to: string) => {
    const fromPos = squareToPosition(from);
    const toPos = squareToPosition(to);
    const piece = board[fromPos.rank]?.[fromPos.file];
    
    // Check if move requires promotion
    if (piece?.type === 'pawn' && 
        ((piece.color === 'white' && to[1] === '8') || 
         (piece.color === 'black' && to[1] === '1'))) {
      setPromotionDialog({ from, to, show: true });
      return;
    }
    
    // Make the move
    onMove({ from, to });
  }, [board, onMove]);
  
  // Handle promotion selection
  const handlePromotion = useCallback((piece: 'queen' | 'rook' | 'bishop' | 'knight') => {
    if (promotionDialog) {
      onMove({
        from: promotionDialog.from,
        to: promotionDialog.to,
        promotion: piece
      });
      setPromotionDialog(null);
    }
  }, [promotionDialog, onMove]);
  
  // Mouse tracking for drag preview
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (draggedPiece && boardRef.current) {
      const rect = boardRef.current.getBoundingClientRect();
      setDragPosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      });
    }
  }, [draggedPiece]);
  
  // Render square
  const renderSquare = (file: number, rank: number) => {
    const square = positionToSquare(file, rank);
    const piece = board[rank]?.[file];
    const isSelected = selectedSquare === square;
    const isLastMoveSquare = showLastMove && lastMove && 
      (square === (lastMove.from.file + lastMove.from.rank) || 
       square === (lastMove.to.file + lastMove.to.rank));
    const isLegalDestination = selectedSquare && 
      getLegalMovesForSquare(selectedSquare).includes(square);
    const isHovered = hoveredSquare === square;
    const isKingInCheck = isCheck && piece?.type === 'king' && piece?.color === playerColor;
    
    return (
      <ChessSquare
        key={square}
        file={file}
        rank={rank}
        piece={piece}
        isLight={(file + rank) % 2 === 0}
        isSelected={isSelected}
        isLastMove={isLastMoveSquare}
        isLegalMove={isLegalDestination}
        isHovered={isHovered}
        isKingInCheck={isKingInCheck}
        showCoordinates={showCoordinates && (file === 0 || rank === 7)}
        boardTheme={boardTheme}
        onClick={() => handleSquareClick(file, rank)}
        onMouseEnter={() => setHoveredSquare(square)}
        onMouseLeave={() => setHoveredSquare(null)}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(file, rank, e)}
      >
        {piece && (
          <ChessPiece
            piece={piece}
            isDragging={draggedPiece === piece}
            isSelected={isSelected}
            onDragStart={(e) => handleDragStart(piece, e)}
            playerColor={playerColor}
            animateMovement={animateMovement}
          />
        )}
      </ChessSquare>
    );
  };
  
  // Board CSS classes
  const boardClasses = [
    'chess-board',
    `chess-board--${boardTheme}`,
    playerColor === 'black' ? 'chess-board--black-perspective' : '',
    gameStatus === 'completed' ? 'chess-board--game-over' : '',
    isCheckmate ? 'chess-board--checkmate' : '',
    isCheck ? 'chess-board--check' : '',
    !isPlayerTurn && gameStatus === 'active' ? 'chess-board--waiting' : ''
  ].filter(Boolean).join(' ');
  
  return (
    <div className="chess-board-container">
      <div 
        ref={boardRef}
        className={boardClasses}
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setDragPosition(null)}
      >
        {/* Render board squares */}
        {Array.from({ length: 8 }, (_, rank) =>
          Array.from({ length: 8 }, (_, file) => renderSquare(file, rank))
        )}
        
        {/* Drag preview */}
        {draggedPiece && dragPosition && (
          <div 
            className="chess-drag-preview"
            style={{
              position: 'absolute',
              left: dragPosition.x - 30,
              top: dragPosition.y - 30,
              width: 60,
              height: 60,
              pointerEvents: 'none',
              zIndex: 1000,
              transform: playerColor === 'black' ? 'rotate(180deg)' : 'none'
            }}
          >
            <img 
              src={`/assets/chess/pieces/${draggedPiece.color}-${draggedPiece.type}.png`}
              alt={`${draggedPiece.color} ${draggedPiece.type}`}
              className="chess-piece-image"
              style={{ width: '100%', height: '100%', opacity: 0.8 }}
            />
          </div>
        )}
      </div>
      
      {/* Promotion dialog */}
      {promotionDialog?.show && (
        <div className="chess-promotion-dialog">
          <div className="chess-promotion-overlay" />
          <div className="chess-promotion-content">
            <h3>Choose promotion piece:</h3>
            <div className="chess-promotion-pieces">
              {(['queen', 'rook', 'bishop', 'knight'] as const).map((pieceType) => (
                <button
                  key={pieceType}
                  className="chess-promotion-piece"
                  onClick={() => handlePromotion(pieceType)}
                >
                  <img 
                    src={`/assets/chess/pieces/${playerColor}-${pieceType}.png`}
                    alt={`${playerColor} ${pieceType}`}
                    style={{ 
                      transform: playerColor === 'black' ? 'rotate(180deg)' : 'none' 
                    }}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};