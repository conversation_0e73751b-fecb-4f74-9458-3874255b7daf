/**
 * Chess Board Preview Component
 * ============================
 * 
 * Displays a miniature chess board showing current game state.
 * Used in game browsing, spectating, and join modals.
 * Supports WebSocket updates for live position sync.
 */

'use client';

import React from 'react';
import { Crown, Shield } from 'lucide-react';

interface BoardPreviewProps {
  gameState?: {
    board?: string[][]; // 8x8 array of piece positions
    fen?: string; // FEN notation for position
    currentPlayer?: 'white' | 'black';
    lastMove?: {
      from: string;
      to: string;
    };
  };
  gameType?: 'chess' | 'checkers' | 'other';
  size?: 'small' | 'medium' | 'large';
  showCoordinates?: boolean;
  className?: string;
}

// Default starting chess position
const DEFAULT_BOARD = [
  ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'],
  ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'],
  ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R']
];

// Chess piece Unicode symbols
const PIECE_SYMBOLS: Record<string, string> = {
  // White pieces (uppercase)
  'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙',
  // Black pieces (lowercase)
  'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟'
};

/**
 * Converts FEN notation to board array
 */
function fenToBoard(fen: string): string[][] {
  const board: (string | null)[][] = Array(8).fill(null).map(() => Array(8).fill(null));
  const position = fen.split(' ')[0]; // Get just the position part
  const ranks = position.split('/');
  
  for (let rank = 0; rank < 8; rank++) {
    let file = 0;
    for (const char of ranks[rank]) {
      if (char >= '1' && char <= '8') {
        // Empty squares
        file += parseInt(char);
      } else {
        // Piece
        board[rank][file] = char;
        file++;
      }
    }
  }
  
  return board as string[][];
}

/**
 * Gets square color (light/dark)
 */
function getSquareColor(row: number, col: number): 'light' | 'dark' {
  return (row + col) % 2 === 0 ? 'light' : 'dark';
}

/**
 * Gets square coordinate (e.g., 'e4')
 */
function getSquareCoordinate(row: number, col: number): string {
  const files = 'abcdefgh';
  const ranks = '87654321'; // Flipped because row 0 is rank 8
  return files[col] + ranks[row];
}

export const BoardPreview: React.FC<BoardPreviewProps> = ({
  gameState,
  gameType = 'chess',
  size = 'medium',
  showCoordinates = false,
  className = ''
}) => {
  // Get board from gameState or use default
  const board = gameState?.board || 
                (gameState?.fen ? fenToBoard(gameState.fen) : DEFAULT_BOARD);
  
  // Size configurations
  const sizeConfig = {
    small: {
      boardSize: 'w-32 h-32',
      squareSize: 'w-4 h-4',
      fontSize: 'text-xs',
      pieceSize: 'text-sm'
    },
    medium: {
      boardSize: 'w-48 h-48',
      squareSize: 'w-6 h-6',
      fontSize: 'text-sm',
      pieceSize: 'text-lg'
    },
    large: {
      boardSize: 'w-64 h-64',
      squareSize: 'w-8 h-8',
      fontSize: 'text-base',
      pieceSize: 'text-xl'
    }
  };
  
  const config = sizeConfig[size];
  
  // Check if square was part of last move
  const isLastMoveSquare = (row: number, col: number): boolean => {
    if (!gameState?.lastMove) return false;
    const coord = getSquareCoordinate(row, col);
    return coord === gameState.lastMove.from || coord === gameState.lastMove.to;
  };
  
  return (
    <div className={`inline-block ${className}`}>
      {/* Board */}
      <div className={`${config.boardSize} border-2 border-slate-600 rounded-lg overflow-hidden bg-slate-800`}>
        <div className="grid grid-cols-8 h-full">
          {board.map((rank, row) =>
            rank.map((piece, col) => {
              const squareColor = getSquareColor(row, col);
              const isLastMove = isLastMoveSquare(row, col);
              
              return (
                <div
                  key={`${row}-${col}`}
                  className={`
                    ${config.squareSize} 
                    flex items-center justify-center
                    ${squareColor === 'light' 
                      ? 'bg-amber-100' 
                      : 'bg-amber-800'
                    }
                    ${isLastMove 
                      ? 'ring-2 ring-blue-400 ring-inset' 
                      : ''
                    }
                    relative
                  `}
                >
                  {/* Piece */}
                  {piece && (
                    <span 
                      className={`
                        ${config.pieceSize} 
                        ${piece === piece.toUpperCase() 
                          ? 'text-white drop-shadow-sm' 
                          : 'text-black drop-shadow-sm'
                        }
                        font-bold select-none
                      `}
                    >
                      {PIECE_SYMBOLS[piece] || piece}
                    </span>
                  )}
                  
                  {/* Coordinates (if enabled) */}
                  {showCoordinates && (
                    <>
                      {/* File labels (a-h) on bottom rank */}
                      {row === 7 && (
                        <span className={`absolute bottom-0 right-0 ${config.fontSize} text-slate-600 leading-none`}>
                          {'abcdefgh'[col]}
                        </span>
                      )}
                      {/* Rank labels (1-8) on left file */}
                      {col === 0 && (
                        <span className={`absolute top-0 left-0 ${config.fontSize} text-slate-600 leading-none`}>
                          {8 - row}
                        </span>
                      )}
                    </>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
      
      {/* Game Info */}
      {gameState && (
        <div className="mt-2 flex items-center justify-center gap-2">
          {/* Current player indicator */}
          {gameState.currentPlayer && (
            <div className="flex items-center gap-1">
              {gameState.currentPlayer === 'white' ? (
                <Crown className="h-3 w-3 text-white" />
              ) : (
                <Shield className="h-3 w-3 text-slate-800" />
              )}
              <span className={`${config.fontSize} text-slate-300`}>
                {gameState.currentPlayer === 'white' ? 'White' : 'Black'} to move
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
