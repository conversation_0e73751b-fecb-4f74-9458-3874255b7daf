'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Clock, Users, TrendingUp, AlertCircle } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';

interface BettingOutcome {
  id: string;
  name: string;
  odds: number;
  total_bet_amount: number;
  bet_count: number;
  implied_probability?: number;
  is_active: boolean;
}

interface BettingMarket {
  id: string;
  name: string;
  description?: string;
  market_type: string;
  status: 'active' | 'suspended' | 'closed' | 'settled';
  total_pool: number;
  total_bets: number;
  opens_at: string;
  closes_at?: string;
  outcomes: BettingOutcome[];
  created_at: string;
}

interface BettingMarketCardProps {
  market: BettingMarket;
  onPlaceBet: (marketId: string, outcomeId: string) => void;
  isAuthenticated: boolean;
}

export function BettingMarketCard({ market, onPlaceBet, isAuthenticated }: BettingMarketCardProps) {
  const [selectedOutcome, setSelectedOutcome] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'suspended':
        return 'bg-yellow-500';
      case 'closed':
        return 'bg-gray-500';
      case 'settled':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatOdds = (odds: number) => {
    return odds.toFixed(2);
  };

  const calculateImpliedProbability = (odds: number) => {
    return ((1 / odds) * 100).toFixed(1);
  };

  const getOutcomePercentage = (outcome: BettingOutcome) => {
    if (market.total_pool === 0) return 0;
    return (outcome.total_bet_amount / market.total_pool) * 100;
  };

  const canPlaceBet = market.status === 'active' && isAuthenticated;

  return (
    <Card className="w-full hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold">{market.name}</CardTitle>
            {market.description && (
              <CardDescription className="text-sm text-gray-600">
                {market.description}
              </CardDescription>
            )}
          </div>
          <Badge className={`${getStatusColor(market.status)} text-white`}>
            {market.status.toUpperCase()}
          </Badge>
        </div>

        <div className="flex items-center gap-4 mt-3 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <TrendingUp className="w-4 h-4" />
            <span>${market.total_pool.toFixed(2)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{market.total_bets} bets</span>
          </div>
          {market.closes_at && market.status === 'active' && (
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>Closes {formatDistanceToNow(new Date(market.closes_at), { addSuffix: true })}</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {market.outcomes.map((outcome) => {
          const isSelected = selectedOutcome === outcome.id;
          const percentage = getOutcomePercentage(outcome);

          return (
            <div
              key={outcome.id}
              className={`border rounded-lg p-3 transition-all cursor-pointer ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              } ${!outcome.is_active ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => outcome.is_active && setSelectedOutcome(outcome.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{outcome.name}</h4>
                  <div className="flex items-center gap-3 mt-1 text-xs text-gray-600">
                    <span className="font-semibold text-lg text-gray-900">
                      {formatOdds(outcome.odds)}x
                    </span>
                    <span>({calculateImpliedProbability(outcome.odds)}% implied)</span>
                    <span>{outcome.bet_count} bets</span>
                  </div>
                </div>
                {outcome.is_active && canPlaceBet && (
                  <Button
                    size="sm"
                    variant={isSelected ? "default" : "outline"}
                    onClick={(e) => {
                      e.stopPropagation();
                      onPlaceBet(market.id, outcome.id);
                    }}
                  >
                    Bet
                  </Button>
                )}
              </div>
              
              {percentage > 0 && (
                <div className="mt-2">
                  <Progress value={percentage} className="h-2" />
                  <p className="text-xs text-gray-600 mt-1">
                    ${outcome.total_bet_amount.toFixed(2)} ({percentage.toFixed(1)}% of pool)
                  </p>
                </div>
              )}
            </div>
          );
        })}

        {!isAuthenticated && market.status === 'active' && (
          <div className="flex items-center gap-2 mt-4 p-3 bg-yellow-50 rounded-lg">
            <AlertCircle className="w-4 h-4 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              Please sign in to place bets
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}