/**
 * BetBet Gaming Engine - Betting Markets Display
 * ============================================
 * 
 * Component for displaying betting markets with live odds and bet placement.
 */

import React, { useEffect, useState } from 'react';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import { BetPlacementModal } from './BetPlacementModal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  Target,
  Loader2,
  AlertCircle,
  Eye,
  Zap,
  Trophy,
  Activity
} from 'lucide-react';

interface BettingOutcome {
  id: string;
  name: string;
  description?: string;
  odds: number;
  implied_probability: number;
  total_bet_amount: number;
  bet_count: number;
  is_active: boolean;
  outcome_type: string;
}

interface BettingMarket {
  id: string;
  name: string;
  description?: string;
  market_type: string;
  status: string;
  total_pool: number;
  total_bets: number;
  outcomes: BettingOutcome[];
  created_at: string;
  closes_at?: string;
  settlement_time?: string;
}

interface BettingMarketsProps {
  sessionId?: string;
  tournamentId?: string;
  marketType?: string;
  showLiveUpdates?: boolean;
}

export function BettingMarkets({ 
  sessionId, 
  tournamentId, 
  marketType,
  showLiveUpdates = true 
}: BettingMarketsProps) {
  const api = useApi();
  const [markets, setMarkets] = useState<BettingMarket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMarket, setSelectedMarket] = useState<string | null>(null);
  const [betModalOpen, setBetModalOpen] = useState(false);
  const [selectedOutcome, setSelectedOutcome] = useState<BettingOutcome | null>(null);
  const [selectedMarketForBet, setSelectedMarketForBet] = useState<BettingMarket | null>(null);

  // WebSocket connection for live odds updates
  const { isConnected, lastMessage } = useWebSocket(
    showLiveUpdates ? (sessionId || tournamentId || null) : null,
    {
      onMessage: (message) => {
        if (message.type === 'odds_update') {
          updateOdds(message.data);
        } else if (message.type === 'new_bet') {
          updateMarketStats(message.data);
        } else if (message.type === 'market_status_change') {
          updateMarketStatus(message.data);
        }
      }
    }
  );

  useEffect(() => {
    loadBettingMarkets();
  }, [sessionId, tournamentId, marketType]);

  const loadBettingMarkets = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response: BettingMarket[] = [];
      
      if (sessionId) {
        response = await api.getSessionBettingMarkets(sessionId);
      } else if (tournamentId) {
        response = await api.getTournamentBettingMarkets(tournamentId);
      }

      // Filter by market type if specified
      if (marketType) {
        response = response.filter(market => market.market_type === marketType);
      }

      setMarkets(response);
      
      // Auto-select first active market
      const activeMarket = response.find(market => market.status === 'active');
      if (activeMarket) {
        setSelectedMarket(activeMarket.id);
      }
    } catch (err) {
      console.error('Failed to load betting markets:', err);
      setError('Failed to load betting markets');
    } finally {
      setLoading(false);
    }
  };

  const updateOdds = (oddsUpdate: any) => {
    setMarkets(prev => prev.map(market => {
      if (market.id === oddsUpdate.market_id) {
        return {
          ...market,
          outcomes: market.outcomes.map(outcome => 
            outcome.id === oddsUpdate.outcome_id
              ? { ...outcome, odds: oddsUpdate.new_odds, implied_probability: oddsUpdate.implied_probability }
              : outcome
          )
        };
      }
      return market;
    }));
  };

  const updateMarketStats = (betData: any) => {
    setMarkets(prev => prev.map(market => {
      if (market.id === betData.market_id) {
        return {
          ...market,
          total_pool: market.total_pool + betData.bet_amount,
          total_bets: market.total_bets + 1,
          outcomes: market.outcomes.map(outcome => 
            outcome.id === betData.outcome_id
              ? { 
                  ...outcome, 
                  total_bet_amount: outcome.total_bet_amount + betData.bet_amount,
                  bet_count: outcome.bet_count + 1
                }
              : outcome
          )
        };
      }
      return market;
    }));
  };

  const updateMarketStatus = (statusUpdate: any) => {
    setMarkets(prev => prev.map(market => 
      market.id === statusUpdate.market_id
        ? { ...market, status: statusUpdate.new_status }
        : market
    ));
  };

  const getMarketStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'suspended':
        return 'bg-yellow-500';
      case 'closed':
        return 'bg-red-500';
      case 'settled':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getMarketStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Open for Betting';
      case 'suspended':
        return 'Temporarily Suspended';
      case 'closed':
        return 'Betting Closed';
      case 'settled':
        return 'Settled';
      default:
        return status;
    }
  };

  const getOddsDirection = (outcome: BettingOutcome) => {
    // This would compare current odds to previous odds
    // For demo purposes, we'll randomly assign directions
    const directions = ['up', 'down', 'stable'];
    return directions[Math.floor(Math.random() * directions.length)];
  };

  const handlePlaceBet = (outcome: BettingOutcome, market: BettingMarket) => {
    setSelectedOutcome(outcome);
    setSelectedMarketForBet(market);
    setBetModalOpen(true);
  };

  const handleBetPlaced = (placedBet: any) => {
    // Refresh markets or update local state with new bet
    loadBettingMarkets();
  };

  const renderOutcomeCard = (outcome: BettingOutcome, market: BettingMarket) => {
    const direction = getOddsDirection(outcome);
    const isActive = market.status === 'active' && outcome.is_active;
    
    return (
      <Card 
        key={outcome.id} 
        className={`cursor-pointer transition-all hover:shadow-md ${
          isActive ? 'border-blue-200' : 'border-gray-200 opacity-60'
        }`}
      >
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Outcome Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-sm">{outcome.name}</h4>
                {outcome.description && (
                  <p className="text-xs text-gray-600 mt-1">{outcome.description}</p>
                )}
              </div>
              {direction !== 'stable' && (
                <div className={`flex items-center gap-1 text-xs ${
                  direction === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {direction === 'up' ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                </div>
              )}
            </div>

            {/* Odds Display */}
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-blue-600">
                {outcome.odds > 0 ? `+${outcome.odds}` : outcome.odds}
              </div>
              <div className="text-xs text-gray-500">
                {(outcome.implied_probability * 100).toFixed(1)}% probability
              </div>
            </div>

            {/* Betting Stats */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="text-center">
                <p className="font-medium">${outcome.total_bet_amount.toLocaleString()}</p>
                <p className="text-gray-500">Total Wagered</p>
              </div>
              <div className="text-center">
                <p className="font-medium">{outcome.bet_count}</p>
                <p className="text-gray-500">Bets Placed</p>
              </div>
            </div>

            {/* Bet Button */}
            <Button 
              size="sm" 
              className="w-full"
              disabled={!isActive}
              variant={isActive ? "default" : "secondary"}
              onClick={() => isActive && handlePlaceBet(outcome, market)}
            >
              {isActive ? (
                <>
                  <DollarSign className="h-3 w-3 mr-1" />
                  Place Bet
                </>
              ) : (
                'Betting Closed'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderMarketCard = (market: BettingMarket) => {
    return (
      <Card key={market.id} className="space-y-4">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-lg">{market.name}</CardTitle>
              {market.description && (
                <CardDescription className="mt-1">{market.description}</CardDescription>
              )}
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge 
                variant="secondary"
                className={`${getMarketStatusColor(market.status)} text-white`}
              >
                {getMarketStatusLabel(market.status)}
              </Badge>
              {showLiveUpdates && isConnected && market.status === 'active' && (
                <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  Live
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Market Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="space-y-1">
              <p className="text-2xl font-bold text-green-600">
                ${market.total_pool.toLocaleString()}
              </p>
              <p className="text-xs text-gray-600">Total Pool</p>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-blue-600">{market.total_bets}</p>
              <p className="text-xs text-gray-600">Total Bets</p>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-purple-600">{market.outcomes.length}</p>
              <p className="text-xs text-gray-600">Outcomes</p>
            </div>
          </div>

          {/* Market Timing */}
          {market.closes_at && (
            <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
              <Clock className="h-4 w-4" />
              <span>Betting closes: {new Date(market.closes_at).toLocaleString()}</span>
            </div>
          )}

          {/* Outcomes Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {market.outcomes.map(outcome => renderOutcomeCard(outcome, market))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const groupMarketsByType = () => {
    const grouped: { [key: string]: BettingMarket[] } = {};
    markets.forEach(market => {
      if (!grouped[market.market_type]) {
        grouped[market.market_type] = [];
      }
      grouped[market.market_type].push(market);
    });
    return grouped;
  };

  const getMarketTypeLabel = (type: string) => {
    switch (type) {
      case 'match_winner':
        return 'Match Winner';
      case 'tournament_winner':
        return 'Tournament Winner';
      case 'player_performance':
        return 'Player Performance';
      case 'special_events':
        return 'Special Events';
      default:
        return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getMarketTypeIcon = (type: string) => {
    switch (type) {
      case 'match_winner':
        return <Target className="h-4 w-4" />;
      case 'tournament_winner':
        return <Trophy className="h-4 w-4" />;
      case 'player_performance':
        return <Activity className="h-4 w-4" />;
      case 'special_events':
        return <Zap className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto" />
            <p className="text-red-800">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadBettingMarkets}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (markets.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <DollarSign className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-4">No betting markets available</p>
          <p className="text-sm text-gray-500">
            Betting markets will appear when the event supports wagering
          </p>
        </CardContent>
      </Card>
    );
  }

  const groupedMarkets = groupMarketsByType();
  const marketTypes = Object.keys(groupedMarkets);

  // If only one market type, render directly without tabs
  if (marketTypes.length === 1) {
    return (
      <div className="space-y-6">
        {/* Live Updates Indicator */}
        {showLiveUpdates && isConnected && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="flex items-center gap-3 py-4">
              <Zap className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Live Betting Markets</p>
                <p className="text-sm text-green-600">Odds and statistics update in real-time</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Markets */}
        <div className="space-y-6">
          {groupedMarkets[marketTypes[0]].map(market => renderMarketCard(market))}
        </div>

        {/* Bet Placement Modal */}
        <BetPlacementModal
          isOpen={betModalOpen}
          onClose={() => setBetModalOpen(false)}
          market={selectedMarketForBet}
          outcome={selectedOutcome}
          onBetPlaced={handleBetPlaced}
        />
      </div>
    );
  }

  // Multiple market types - render with tabs
  return (
    <div className="space-y-6">
      {/* Live Updates Indicator */}
      {showLiveUpdates && isConnected && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="flex items-center gap-3 py-4">
            <Zap className="h-5 w-5 text-green-600" />
            <div>
              <p className="font-medium text-green-800">Live Betting Markets</p>
              <p className="text-sm text-green-600">Odds and statistics update in real-time</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Market Type Tabs */}
      <Tabs defaultValue={marketTypes[0]} className="space-y-6">
        <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${marketTypes.length}, 1fr)` }}>
          {marketTypes.map(type => (
            <TabsTrigger key={type} value={type} className="flex items-center gap-2">
              {getMarketTypeIcon(type)}
              <span className="hidden sm:inline">{getMarketTypeLabel(type)}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {marketTypes.map(type => (
          <TabsContent key={type} value={type} className="space-y-6">
            {groupedMarkets[type].map(market => renderMarketCard(market))}
          </TabsContent>
        ))}
      </Tabs>

      {/* Bet Placement Modal */}
      <BetPlacementModal
        isOpen={betModalOpen}
        onClose={() => setBetModalOpen(false)}
        market={selectedMarketForBet}
        outcome={selectedOutcome}
        onBetPlaced={handleBetPlaced}
      />
    </div>
  );
}