'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  TrendingUp,
  DollarSign,
  Users,
  AlertCircle,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  Settings,
  Loader2
} from 'lucide-react';

interface BettingOutcome {
  id?: string;
  name: string;
  description?: string;
  odds: number;
  implied_probability?: number;
}

interface BettingMarket {
  id: string;
  name: string;
  description?: string;
  market_type: string;
  status: string;
  total_pool: number;
  total_bets: number;
  session_id?: string;
  tournament_id?: string;
  outcomes: BettingOutcome[];
  created_at: string;
  closes_at?: string;
}

interface CreateMarketFormData {
  session_id?: string;
  tournament_id?: string;
  name: string;
  description: string;
  market_type: string;
  status: string;
  closes_at?: string;
  outcomes: BettingOutcome[];
}

interface AdminBettingMarketManagerProps {
  sessionId?: string;
  tournamentId?: string;
  onMarketCreated?: (market: BettingMarket) => void;
  onMarketUpdated?: (market: BettingMarket) => void;
  onMarketDeleted?: (marketId: string) => void;
}

export function AdminBettingMarketManager({
  sessionId,
  tournamentId,
  onMarketCreated,
  onMarketUpdated,
  onMarketDeleted
}: AdminBettingMarketManagerProps) {
  const { user } = useAuth();
  const api = useApi();
  
  const [markets, setMarkets] = useState<BettingMarket[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingMarket, setEditingMarket] = useState<BettingMarket | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<CreateMarketFormData>({
    session_id: sessionId,
    tournament_id: tournamentId,
    name: '',
    description: '',
    market_type: 'winner',
    status: 'active',
    outcomes: [
      { name: 'Player 1', odds: 2.0 },
      { name: 'Player 2', odds: 2.0 }
    ]
  });

  // Check if user has admin permissions
  const isAdmin = user?.publicMetadata?.roles?.includes('admin') || false;

  useEffect(() => {
    if (isAdmin && (sessionId || tournamentId)) {
      loadMarkets();
    }
  }, [sessionId, tournamentId, isAdmin]);

  const loadMarkets = async () => {
    try {
      setLoading(true);
      let marketsData = [];
      
      if (sessionId) {
        marketsData = await api.getSessionBettingMarkets(sessionId);
      } else if (tournamentId) {
        marketsData = await api.getTournamentBettingMarkets(tournamentId);
      }
      
      setMarkets(marketsData);
    } catch (err: any) {
      console.error('Failed to load markets:', err);
      setError('Failed to load betting markets');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      session_id: sessionId,
      tournament_id: tournamentId,
      name: '',
      description: '',
      market_type: 'winner',
      status: 'active',
      outcomes: [
        { name: 'Player 1', odds: 2.0 },
        { name: 'Player 2', odds: 2.0 }
      ]
    });
  };

  const addOutcome = () => {
    setFormData(prev => ({
      ...prev,
      outcomes: [...prev.outcomes, { name: '', odds: 2.0 }]
    }));
  };

  const removeOutcome = (index: number) => {
    setFormData(prev => ({
      ...prev,
      outcomes: prev.outcomes.filter((_, i) => i !== index)
    }));
  };

  const updateOutcome = (index: number, field: keyof BettingOutcome, value: any) => {
    setFormData(prev => ({
      ...prev,
      outcomes: prev.outcomes.map((outcome, i) => 
        i === index ? { ...outcome, [field]: value } : outcome
      )
    }));
  };

  const handleCreateMarket = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate implied probabilities
      const processedOutcomes = formData.outcomes.map(outcome => ({
        ...outcome,
        implied_probability: (1 / outcome.odds) * 100
      }));

      const marketData = {
        ...formData,
        outcomes: processedOutcomes
      };

      const createdMarket = await api.createBettingMarket(marketData);
      setMarkets(prev => [...prev, createdMarket]);
      onMarketCreated?.(createdMarket);
      
      setShowCreateModal(false);
      resetForm();
    } catch (err: any) {
      console.error('Failed to create market:', err);
      setError(err.response?.data?.detail || 'Failed to create betting market');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateMarketStatus = async (marketId: string, newStatus: string) => {
    try {
      setLoading(true);
      const updatedMarket = await api.updateBettingMarket(marketId, { status: newStatus });
      
      setMarkets(prev => prev.map(market => 
        market.id === marketId ? { ...market, ...updatedMarket } : market
      ));
      
      onMarketUpdated?.(updatedMarket);
    } catch (err: any) {
      console.error('Failed to update market:', err);
      setError('Failed to update market status');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMarket = async (marketId: string) => {
    if (!confirm('Are you sure you want to delete this betting market?')) {
      return;
    }

    try {
      setLoading(true);
      await api.deleteBettingMarket(marketId);
      
      setMarkets(prev => prev.filter(market => market.id !== marketId));
      onMarketDeleted?.(marketId);
    } catch (err: any) {
      console.error('Failed to delete market:', err);
      setError('Failed to delete betting market');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'suspended': return 'bg-yellow-500';
      case 'closed': return 'bg-gray-500';
      case 'settled': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  if (!isAdmin) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Admin access required to manage betting markets.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Betting Market Management
          </h3>
          <p className="text-sm text-gray-600">Create and manage betting markets</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Market
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Markets List */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : markets.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold mb-2">No Betting Markets</h4>
              <p className="text-gray-600 mb-4">Create your first betting market to get started.</p>
              <Button onClick={() => setShowCreateModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Market
              </Button>
            </CardContent>
          </Card>
        ) : (
          markets.map((market) => (
            <Card key={market.id}>
              <CardHeader className="flex flex-row items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-base">{market.name}</CardTitle>
                  {market.description && (
                    <p className="text-sm text-gray-600">{market.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={`${getStatusColor(market.status)} text-white`}>
                    {market.status.toUpperCase()}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span>${market.total_pool.toFixed(2)} pool</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span>{market.total_bets} bets</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-purple-600" />
                    <span>{market.outcomes.length} outcomes</span>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Select 
                    value={market.status}
                    onValueChange={(status) => handleUpdateMarketStatus(market.id, status)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                      <SelectItem value="settled">Settled</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setEditingMarket(market)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDeleteMarket(market.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create Market Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Betting Market</DialogTitle>
            <DialogDescription>
              Create a new betting market for this {sessionId ? 'session' : 'tournament'}.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Market Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Winner"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="market_type">Market Type</Label>
                <Select 
                  value={formData.market_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, market_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="winner">Winner</SelectItem>
                    <SelectItem value="score">Score</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="special">Special</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this market is about..."
                rows={2}
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Outcomes</Label>
                <Button type="button" variant="outline" size="sm" onClick={addOutcome}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Outcome
                </Button>
              </div>
              
              {formData.outcomes.map((outcome, index) => (
                <div key={index} className="flex gap-2 items-end">
                  <div className="flex-1">
                    <Input
                      placeholder="Outcome name"
                      value={outcome.name}
                      onChange={(e) => updateOutcome(index, 'name', e.target.value)}
                    />
                  </div>
                  <div className="w-24">
                    <Input
                      type="number"
                      step="0.1"
                      min="1.1"
                      placeholder="Odds"
                      value={outcome.odds}
                      onChange={(e) => updateOutcome(index, 'odds', parseFloat(e.target.value) || 2.0)}
                    />
                  </div>
                  {formData.outcomes.length > 2 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeOutcome(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateMarket} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Market'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}