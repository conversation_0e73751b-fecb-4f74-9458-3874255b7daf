/**
 * BetBet Gaming Engine - Bet Placement Modal
 * ==========================================
 * 
 * Modal component for placing bets on betting market outcomes.
 */

import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { useAuth } from '@clerk/nextjs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DollarSign,
  TrendingUp,
  AlertCircle,
  Check,
  Loader2,
  Calculator,
  Target,
  Trophy,
  Zap
} from 'lucide-react';

interface BettingOutcome {
  id: string;
  name: string;
  description?: string;
  odds: number;
  implied_probability: number;
  total_bet_amount: number;
  bet_count: number;
  is_active: boolean;
  outcome_type: string;
}

interface BettingMarket {
  id: string;
  name: string;
  description?: string;
  market_type: string;
  status: string;
  total_pool: number;
  total_bets: number;
  outcomes: BettingOutcome[];
  created_at: string;
  closes_at?: string;
  settlement_time?: string;
  session_id?: string;
  tournament_id?: string;
}

interface BetPlacementModalProps {
  isOpen: boolean;
  onClose: () => void;
  market: BettingMarket | null;
  outcome: BettingOutcome | null;
  onBetPlaced?: (bet: any) => void;
}

export function BetPlacementModal({
  isOpen,
  onClose,
  market,
  outcome,
  onBetPlaced
}: BetPlacementModalProps) {
  const api = useApi();
  const { user } = useAuth();
  
  const [betAmount, setBetAmount] = useState<string>('');
  const [isPlacing, setIsPlacing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [userBalance, setUserBalance] = useState<number>(0);

  // Preset bet amounts
  const presetAmounts = [5, 10, 25, 50, 100];

  useEffect(() => {
    if (isOpen && market && outcome) {
      // Reset state when modal opens
      setBetAmount('');
      setError(null);
      setSuccess(false);
      loadUserBalance();
    }
  }, [isOpen, market, outcome]);

  const loadUserBalance = async () => {
    try {
      // Attempt to fetch user's actual balance from the API
      try {
        const userProfile = await api.getProfile();
        setUserBalance(userProfile.balance || 500);
      } catch (apiError) {
        console.error('Could not load user balance from API:', apiError);
        
        // Always show the actual error for debugging
        const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown API error';
        
        if (process.env.NODE_ENV === 'development') {
          console.group('💰 User Balance API Debug Info');
          console.log('API Error:', apiError);
          console.log('Error Message:', errorMessage);
          console.log('Tip: Check backend user profile API endpoint');
          console.groupEnd();
        }
        
        // Set balance to 0 to force proper error handling
        setUserBalance(0);
      }
    } catch (err) {
      console.error('Failed to load user balance:', err);
      // No fallback balance - force proper error handling
      setUserBalance(0);
    }
  };

  const calculatePayout = () => {
    const amount = parseFloat(betAmount);
    if (!amount || !outcome) return 0;
    
    // Calculate potential payout based on decimal odds
    const payout = amount * outcome.odds;
    
    return Math.round(payout * 100) / 100;
  };

  const calculateProfit = () => {
    const amount = parseFloat(betAmount);
    return calculatePayout() - amount;
  };

  const validateBet = () => {
    const amount = parseFloat(betAmount);
    
    if (!amount || amount <= 0) {
      return 'Please enter a valid bet amount';
    }
    
    if (amount < 1) {
      return 'Minimum bet amount is $1';
    }
    
    if (amount > userBalance) {
      return 'Insufficient balance';
    }
    
    if (amount > 1000) {
      return 'Maximum bet amount is $1000';
    }
    
    return null;
  };

  const handlePlaceBet = async () => {
    if (!market || !outcome) return;
    
    const validationError = validateBet();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setIsPlacing(true);
      setError(null);

      const betData = {
        market_id: market.id,
        outcome_id: outcome.id,
        bet_amount: parseFloat(betAmount)
      };

      const placedBet = await api.placeBet(betData);
      
      setSuccess(true);
      
      // Update user balance
      setUserBalance(prev => prev - parseFloat(betAmount));
      
      // Notify parent component
      if (onBetPlaced) {
        onBetPlaced(placedBet);
      }
      
      // Close modal after delay
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (err: any) {
      console.error('Failed to place bet:', err);
      setError(err.message || 'Failed to place bet. Please try again.');
    } finally {
      setIsPlacing(false);
    }
  };

  const getMarketIcon = () => {
    if (!market) return <DollarSign className="h-5 w-5" />;
    
    switch (market.market_type) {
      case 'match_winner':
        return <Target className="h-5 w-5" />;
      case 'tournament_winner':
        return <Trophy className="h-5 w-5" />;
      default:
        return <DollarSign className="h-5 w-5" />;
    }
  };

  const formatOdds = (odds: number) => {
    return `${odds.toFixed(2)}x`;
  };

  if (!market || !outcome) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getMarketIcon()}
            Place Bet
          </DialogTitle>
          <DialogDescription className="text-left">
            Place your bet on "{outcome.name}" in {market.name}
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="text-center py-8">
            <Check className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Bet Placed Successfully!
            </h3>
            <p className="text-sm text-gray-600">
              Your bet of ${betAmount} has been placed on "{outcome.name}"
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* User Balance */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Available Balance</span>
                <span className="font-semibold text-green-600">
                  ${userBalance.toLocaleString()}
                </span>
              </div>
            </div>

            {/* Betting Outcome Info */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Outcome</span>
                <Badge variant="outline">{outcome.name}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="font-medium">Odds</span>
                <span className="text-lg font-bold text-blue-600">
                  {formatOdds(outcome.odds)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="font-medium">Probability</span>
                <span className="text-sm text-gray-600">
                  {outcome.implied_probability ? 
                    outcome.implied_probability.toFixed(1) + '%' : 
                    ((1 / outcome.odds) * 100).toFixed(1) + '%'
                  }
                </span>
              </div>
            </div>

            <Separator />

            {/* Bet Amount Input */}
            <div className="space-y-3">
              <Label htmlFor="bet-amount">Bet Amount ($)</Label>
              <Input
                id="bet-amount"
                type="number"
                placeholder="Enter amount"
                value={betAmount}
                onChange={(e) => setBetAmount(e.target.value)}
                min="1"
                max={userBalance}
                step="1"
              />
              
              {/* Preset Amount Buttons */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {presetAmounts.map(amount => (
                  <Button
                    key={amount}
                    variant={betAmount === amount.toString() ? "default" : "outline"}
                    size="sm"
                    onClick={() => setBetAmount(amount.toString())}
                    disabled={amount > userBalance}
                    className="text-xs"
                  >
                    ${amount}
                  </Button>
                ))}
              </div>
            </div>

            {/* Bet Calculation */}
            {betAmount && parseFloat(betAmount) > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                <div className="flex items-center gap-2 mb-2">
                  <Calculator className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Bet Summary</span>
                </div>
                
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Stake:</span>
                    <span className="font-medium">${betAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Potential Payout:</span>
                    <span className="font-medium text-green-600">
                      ${calculatePayout().toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Potential Profit:</span>
                    <span className="font-medium text-green-600">
                      ${calculateProfit().toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Market Closure Warning */}
            {market.closes_at && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Betting closes at {new Date(market.closes_at).toLocaleString()}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {!success && (
          <DialogFooter>
            <Button variant="outline" onClick={onClose} disabled={isPlacing}>
              Cancel
            </Button>
            <Button 
              onClick={handlePlaceBet} 
              disabled={isPlacing || !betAmount || parseFloat(betAmount) <= 0}
            >
              {isPlacing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Placing Bet...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Place Bet ${betAmount || '0'}
                </>
              )}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}