'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Wallet, 
  Plus, 
  Minus, 
  ArrowUpRight, 
  ArrowDownLeft, 
  History,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Lock
} from 'lucide-react';
import { useUser, useAuth } from '@clerk/nextjs';
import api from '@/lib/api-client-unified';

interface WalletBalance {
  user_id: string;
  available_balance: number;
  locked_balance: number;
  total_balance?: number; // Optional as it's calculated
  total_deposited: number;
  total_withdrawn: number;
  lifetime_winnings: number;
  lifetime_losses: number;
  currency: string;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  balance_before: number;
  balance_after: number;
  description?: string;
  status: string;
  created_at: string;
}

const WalletDashboard: React.FC = () => {
  const { user } = useUser();
  const { getToken } = useAuth();
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [depositAmount, setDepositAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [depositLoading, setDepositLoading] = useState(false);
  const [withdrawLoading, setWithdrawLoading] = useState(false);

  // Fetch wallet balance from API
  useEffect(() => {
    const fetchWalletData = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        setError(null);
        
        // Set auth token for unified API
        const token = await getToken();
        api.setAuthToken(token);

        // Fetch balance
        const balanceData = await api.wallet.getBalance(user.id);
        setBalance(balanceData);

        // Fetch transactions
        const transactionsData = await api.wallet.getTransactions(user.id, { page: 1, limit: 10 });
        setTransactions(transactionsData.transactions);
        setTotalTransactions(transactionsData.total_count);
        
      } catch (err: any) {
        console.error('Failed to fetch wallet data:', err);
        console.error('Error details:', err.response?.data);
        setError(`Failed to load wallet data: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
        
        // Set default empty state
        setBalance({
          user_id: user.id,
          available_balance: 0,
          locked_balance: 0,
          total_balance: 0,
          total_deposited: 0,
          total_withdrawn: 0,
          lifetime_winnings: 0,
          lifetime_losses: 0,
          currency: 'USD'
        });
        setTransactions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchWalletData();
  }, [user, getToken]);

  // Fetch transactions for pagination
  const fetchTransactions = async (page: number) => {
    if (!user) return;
    
    try {
      setTransactionsLoading(true);
      
      // Set auth token
      const token = await getToken();
      api.setAuthToken(token);

      const transactionsData = await api.wallet.getTransactions(user.id, { page, limit: 10 });
      setTransactions(transactionsData.transactions);
      setCurrentPage(page);
    } catch (err) {
      console.error('Failed to fetch transactions:', err);
    } finally {
      setTransactionsLoading(false);
    }
  };

  const handleDeposit = async () => {
    if (!depositAmount || !user) return;
    
    try {
      setError(null);
      setDepositLoading(true);
      
      // Set auth token
      const token = await getToken();
      api.setAuthToken(token);

      const amount = parseFloat(depositAmount);

      // Create deposit via API
      const result = await api.wallet.createDeposit({
        user_id: user.id,
        amount,
        payment_method: 'demo', // For testing, use 'stripe' for real payments
      });

      if (result.success) {
        // Refresh balance
        const updatedBalance = await api.wallet.getBalance(user.id);
        setBalance(updatedBalance);

        // Refresh transactions
        const transactionsData = await api.wallet.getTransactions(user.id, { page: 1, limit: 10 });
        setTransactions(transactionsData.transactions);
        
        setDepositAmount('');
        
        // Show success message
        if (result.requires_payment) {
          // Handle Stripe payment flow
          console.log('Payment required:', result.client_secret);
          alert('Payment processing would be handled here with Stripe');
        }
      }
    } catch (err: any) {
      console.error('Deposit failed:', err);
      setError(err.response?.data?.detail || 'Failed to process deposit');
    } finally {
      setDepositLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!withdrawAmount || !user || !balance) return;
    
    try {
      setError(null);
      setWithdrawLoading(true);
      
      // Set auth token
      const token = await getToken();
      api.setAuthToken(token);

      const amount = parseFloat(withdrawAmount);

      if (amount > balance.available_balance) {
        setError('Insufficient funds');
        setWithdrawLoading(false);
        return;
      }

      // Create withdrawal via API
      const result = await api.wallet.createWithdrawal({
        user_id: user.id,
        amount,
        withdrawal_method: 'bank_transfer',
      });

      if (result.success) {
        // Refresh balance
        const updatedBalance = await api.wallet.getBalance(user.id);
        setBalance(updatedBalance);

        // Refresh transactions
        const transactionsData = await api.wallet.getTransactions(user.id, { page: 1, limit: 10 });
        setTransactions(transactionsData.transactions);
        
        setWithdrawAmount('');
      }
    } catch (err: any) {
      console.error('Withdrawal failed:', err);
      setError(err.response?.data?.detail || 'Failed to process withdrawal');
    } finally {
      setWithdrawLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit': return <ArrowDownLeft className="h-4 w-4 text-green-500" />;
      case 'withdrawal': return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      case 'bet': return <Minus className="h-4 w-4 text-orange-500" />;
      case 'win': return <Plus className="h-4 w-4 text-green-500" />;
      case 'loss': return <Minus className="h-4 w-4 text-red-500" />;
      default: return <DollarSign className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'text-green-600';
      case 'win': return 'text-green-600';
      case 'withdrawal': return 'text-red-600';
      case 'bet': return 'text-orange-600';
      case 'loss': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getEnhancedTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'text-emerald-400';
      case 'win': return 'text-emerald-400';
      case 'withdrawal': return 'text-red-400';
      case 'bet': return 'text-amber-400';
      case 'loss': return 'text-red-400';
      default: return 'text-slate-300';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!balance) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Unable to load wallet information</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <div className="bg-red-900/30 border border-red-600 text-red-400 px-4 py-3 rounded-lg flex items-center justify-between">
          <span>{error}</span>
          <button onClick={() => setError(null)} className="text-red-400 hover:text-red-300">
            ✕
          </button>
        </div>
      )}

      {/* Balance Overview - Improved Contrast */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-white">Available Balance</CardTitle>
            <Wallet className="h-5 w-5 text-slate-300" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-400 drop-shadow-sm">
              {formatCurrency(balance.available_balance)}
            </div>
            <p className="text-xs text-slate-300 font-medium">
              Ready to use
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-white">Locked Balance</CardTitle>
            <Lock className="h-5 w-5 text-slate-300" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-400 drop-shadow-sm">
              {formatCurrency(balance.locked_balance)}
            </div>
            <p className="text-xs text-slate-300 font-medium">
              In active bets
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-white">Lifetime Winnings</CardTitle>
            <TrendingUp className="h-5 w-5 text-slate-300" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-400 drop-shadow-sm">
              {formatCurrency(balance.lifetime_winnings)}
            </div>
            <p className="text-xs text-slate-300 font-medium">
              Total winnings
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-white">Net P&L</CardTitle>
            <TrendingDown className="h-5 w-5 text-slate-300" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold drop-shadow-sm ${balance.lifetime_winnings > balance.lifetime_losses ? 'text-emerald-400' : 'text-red-400'}`}>
              {formatCurrency(balance.lifetime_winnings - balance.lifetime_losses)}
            </div>
            <p className="text-xs text-slate-300 font-medium">
              Net profit/loss
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Wallet Interface - Improved Contrast */}
      <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white font-bold">
            <Wallet className="h-5 w-5 text-slate-300" />
            Wallet Management
          </CardTitle>
          <CardDescription className="text-slate-300 font-medium">
            Manage your account balance, deposits, and withdrawals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="deposit" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 border border-slate-600">
              <TabsTrigger value="deposit" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-slate-300 font-semibold">Deposit</TabsTrigger>
              <TabsTrigger value="withdraw" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-slate-300 font-semibold">Withdraw</TabsTrigger>
              <TabsTrigger value="transactions" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-slate-300 font-semibold">History</TabsTrigger>
            </TabsList>

            <TabsContent value="deposit" className="space-y-4 mt-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="deposit-amount" className="text-white font-semibold text-sm">Amount to Deposit</Label>
                  <div className="flex gap-3 mt-2">
                    <Input
                      id="deposit-amount"
                      type="number"
                      placeholder="0.00"
                      value={depositAmount}
                      onChange={(e) => setDepositAmount(e.target.value)}
                      className="flex-1 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500/20 font-medium"
                    />
                    <Button 
                      onClick={handleDeposit} 
                      disabled={!depositAmount || depositLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 shadow-lg border border-blue-500"
                    >
                      {depositLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Deposit
                        </>
                      )}
                    </Button>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  {[25, 50, 100, 250].map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      onClick={() => setDepositAmount(amount.toString())}
                      className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white hover:border-slate-500 font-semibold"
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>

                <div className="p-4 bg-slate-800/50 border border-slate-600 rounded-lg">
                  <h4 className="font-semibold text-white mb-3">Payment Methods</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3 text-slate-200">
                      <CreditCard className="h-5 w-5 text-slate-300" />
                      <span className="font-medium">Credit/Debit Cards (Visa, Mastercard, Amex)</span>
                    </div>
                    <div className="flex items-center gap-3 text-slate-200">
                      <DollarSign className="h-5 w-5 text-slate-300" />
                      <span className="font-medium">Bank Transfer (ACH)</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="withdraw" className="space-y-4 mt-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="withdraw-amount" className="text-white font-semibold text-sm">Amount to Withdraw</Label>
                  <div className="flex gap-3 mt-2">
                    <Input
                      id="withdraw-amount"
                      type="number"
                      placeholder="0.00"
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      className="flex-1 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500/20 font-medium"
                    />
                    <Button 
                      onClick={handleWithdraw} 
                      disabled={!withdrawAmount || withdrawLoading}
                      className="bg-orange-600 hover:bg-orange-700 text-white font-semibold px-6 shadow-lg border border-orange-500"
                    >
                      {withdrawLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <ArrowUpRight className="h-4 w-4 mr-2" />
                          Withdraw
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                <div className="p-4 bg-slate-800/50 border border-slate-600 rounded-lg">
                  <h4 className="font-semibold text-white mb-3">Withdrawal Information</h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-slate-200 font-medium">• Available balance: <span className="text-emerald-400 font-bold">{formatCurrency(balance.available_balance)}</span></p>
                    <p className="text-slate-200 font-medium">• Processing time: <span className="text-slate-300">1-3 business days</span></p>
                    <p className="text-slate-200 font-medium">• Minimum withdrawal: <span className="text-slate-300">$10.00</span></p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="transactions" className="space-y-4 mt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-white">Recent Transactions</h3>
                  <Badge className="bg-slate-700 text-slate-200 border-slate-600">
                    <History className="h-3 w-3 mr-1" />
                    {transactions.length} transactions
                  </Badge>
                </div>

                <div className="space-y-3">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 bg-slate-800/50 border border-slate-600 rounded-lg hover:bg-slate-800/70 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-slate-700/50 rounded-lg">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        <div>
                          <div className="font-semibold text-white capitalize text-sm">
                            {transaction.type}
                          </div>
                          <div className="text-sm text-slate-300 font-medium">
                            {transaction.description}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-bold text-sm ${getEnhancedTransactionColor(transaction.type)}`}>
                          {transaction.type === 'withdrawal' || transaction.type === 'bet' || transaction.type === 'loss' ? '-' : '+'}
                          {formatCurrency(transaction.amount)}
                        </div>
                        <div className="text-xs text-slate-400 font-medium">
                          {new Date(transaction.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {transactions.length === 0 && (
                  <div className="text-center py-8 text-slate-400">
                    <History className="h-12 w-12 mx-auto mb-2 text-slate-500" />
                    <p className="font-medium">No transactions yet</p>
                  </div>
                )}

                {/* Pagination */}
                {totalTransactions > 10 && (
                  <div className="flex items-center justify-center gap-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchTransactions(currentPage - 1)}
                      disabled={currentPage === 1 || transactionsLoading}
                      className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-slate-300 font-medium px-3">
                      Page {currentPage} of {Math.ceil(totalTransactions / 10)}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchTransactions(currentPage + 1)}
                      disabled={currentPage >= Math.ceil(totalTransactions / 10) || transactionsLoading}
                      className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default WalletDashboard;