/**
 * AchievementBadge - Achievement Display Component
 * ==============================================
 * 
 * Displays individual achievements with appropriate rarity styling and tooltips.
 */

'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Star, 
  Sparkles, 
  Crown, 
  Gem,
  Trophy,
  Award
} from 'lucide-react';
import { cn } from '@/lib/utils';

type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
type AchievementRarity = 'Common' | 'Rare' | 'Epic' | 'Legendary';

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: AchievementRarity;
  points: number;
  icon_url?: string;
  unlocked_at?: string;
}

interface AchievementBadgeProps {
  achievement: Achievement;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  showPoints?: boolean;
  onClick?: () => void;
  className?: string;
}

const getRarityConfig = (rarity: AchievementRarity) => {
  const configs = {
    Common: {
      icon: Star,
      color: 'bg-gray-100 text-gray-700 border-gray-300',
      bgColor: 'bg-gray-50',
      glow: '',
      sparkle: false
    },
    Rare: {
      icon: Trophy,
      color: 'bg-blue-100 text-blue-700 border-blue-300',
      bgColor: 'bg-blue-50',
      glow: 'shadow-blue-200 shadow-md',
      sparkle: false
    },
    Epic: {
      icon: Crown,
      color: 'bg-purple-100 text-purple-700 border-purple-300',
      bgColor: 'bg-purple-50',
      glow: 'shadow-purple-200 shadow-lg',
      sparkle: true
    },
    Legendary: {
      icon: Gem,
      color: 'bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-700 border-orange-300',
      bgColor: 'bg-gradient-to-br from-yellow-50 to-orange-50',
      glow: 'shadow-orange-200 shadow-xl',
      sparkle: true
    }
  };
  return configs[rarity];
};

const getSizeClasses = (size: 'xs' | 'sm' | 'md' | 'lg') => {
  const sizes = {
    xs: {
      container: 'w-6 h-6',
      icon: 'w-3 h-3',
      text: 'text-xs',
      badge: 'text-xs px-1 py-0.5',
      tooltip: 'text-xs'
    },
    sm: {
      container: 'w-8 h-8',
      icon: 'w-4 h-4',
      text: 'text-sm',
      badge: 'text-xs px-2 py-1',
      tooltip: 'text-sm'
    },
    md: {
      container: 'w-10 h-10',
      icon: 'w-5 h-5',
      text: 'text-base',
      badge: 'text-sm px-3 py-1.5',
      tooltip: 'text-base'
    },
    lg: {
      container: 'w-12 h-12',
      icon: 'w-6 h-6',
      text: 'text-lg',
      badge: 'text-base px-4 py-2',
      tooltip: 'text-lg'
    }
  };
  return sizes[size];
};

const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  achievement,
  size = 'md',
  showTooltip = false,
  showPoints = false,
  onClick,
  className
}) => {
  const config = getRarityConfig(achievement.rarity);
  const sizeClasses = getSizeClasses(size);
  const Icon = config.icon;
  
  const isUnlocked = !!achievement.unlocked_at;
  
  const badgeContent = (
    <div 
      className={cn(
        "relative inline-flex items-center justify-center rounded-lg border-2 transition-all duration-200",
        config.color,
        config.glow,
        onClick && "cursor-pointer hover:scale-105",
        !isUnlocked && "opacity-40 grayscale",
        sizeClasses.container,
        className
      )}
      onClick={onClick}
    >
      {/* Sparkle Animation for Epic+ */}
      {config.sparkle && isUnlocked && (
        <div className="absolute inset-0 animate-pulse">
          <Sparkles className="absolute top-0 right-0 w-2 h-2 text-yellow-400 animate-bounce" style={{ animationDelay: '0.2s' }} />
          <Sparkles className="absolute bottom-0 left-0 w-2 h-2 text-yellow-400 animate-bounce" style={{ animationDelay: '0.8s' }} />
        </div>
      )}
      
      {/* Achievement Icon */}
      {achievement.icon_url ? (
        <img 
          src={achievement.icon_url} 
          alt={achievement.name}
          className={cn("rounded", sizeClasses.icon)}
        />
      ) : (
        <Icon className={sizeClasses.icon} />
      )}
      
      {/* Points Badge (if showing points) */}
      {showPoints && (
        <div className="absolute -top-1 -right-1 bg-indigo-500 text-white text-xs rounded-full px-1.5 py-0.5 font-semibold">
          {achievement.points}
        </div>
      )}
    </div>
  );

  if (!showTooltip) {
    return badgeContent;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        {badgeContent}
      </TooltipTrigger>
      <TooltipContent side="top" className="max-w-xs">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-gray-900">{achievement.name}</h4>
            <Badge 
              variant="secondary" 
              className={cn("text-xs", config.color)}
            >
              {achievement.rarity}
            </Badge>
          </div>
          
          <p className="text-sm text-gray-600">
            {achievement.description}
          </p>
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span className="capitalize">{achievement.category.replace('_', ' ')}</span>
            <span>{achievement.points} pts</span>
          </div>
          
          {achievement.unlocked_at && (
            <p className="text-xs text-green-600 font-medium">
              Unlocked {new Date(achievement.unlocked_at).toLocaleDateString()}
            </p>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

export default AchievementBadge;