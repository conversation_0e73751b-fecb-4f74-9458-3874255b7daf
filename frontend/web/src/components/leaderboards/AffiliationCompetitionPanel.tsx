/**
 * AffiliationCompetitionPanel - Team Competition Display
 * ====================================================
 * 
 * Displays affiliation-based competition with team rankings, dominance metrics,
 * and strength analysis across all platform modules.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Trophy, 
  TrendingUp,
  Crown,
  Shield,
  Loader2,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import utility components
import RankBadge from './RankBadge';

interface AffiliationRanking {
  rank: number;
  rank_change?: number;
  affiliation: {
    id: string;
    name: string;
    logo_url?: string;
    description?: string;
    member_count: number;
  };
  dominance_metrics: {
    overall_dominance_score: number;
    gaming_dominance: number;
    betting_dominance: number;
    trading_dominance: number;
    analyst_dominance: number;
    average_member_score: number;
    top_performers_count: number;
    cross_platform_masters: number;
    total_achievements_unlocked: number;
  };
  strengths: string[];
  growth_opportunities: string[];
}

const AffiliationCompetitionPanel: React.FC = () => {
  const [affiliationRankings, setAffiliationRankings] = useState<AffiliationRanking[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'weekly' | 'monthly' | 'season'>('monthly');

  // Mock data generation
  useEffect(() => {
    const generateMockAffiliations = (): AffiliationRanking[] => {
      const affiliationNames = [
        'Elite Gaming Club',
        'Pro Traders Union', 
        'Betting Champions',
        'Analysis Masters',
        'Strategy Experts',
        'Rising Stars'
      ];

      const strengthOptions = ['Gaming', 'Betting', 'Trading', 'Analysis', 'Community', 'Consistency'];
      
      return affiliationNames.map((name, index) => ({
        rank: index + 1,
        rank_change: Math.floor(Math.random() * 6) - 3, // -3 to +3
        affiliation: {
          id: `aff-${index + 1}`,
          name,
          logo_url: `/affiliations/${name.toLowerCase().replace(/\s+/g, '-')}.png`,
          description: `Professional ${name.toLowerCase()} community`,
          member_count: Math.floor(Math.random() * 500) + 50
        },
        dominance_metrics: {
          overall_dominance_score: Math.max(1000, 10000 - index * 1200 + Math.random() * 800),
          gaming_dominance: Math.max(800, 9000 - index * 1000 + Math.random() * 600),
          betting_dominance: Math.max(700, 8500 - index * 1100 + Math.random() * 700),
          trading_dominance: Math.max(600, 8000 - index * 1200 + Math.random() * 800),
          analyst_dominance: Math.max(500, 7500 - index * 1300 + Math.random() * 900),
          average_member_score: Math.max(2000, 8000 - index * 800 + Math.random() * 400),
          top_performers_count: Math.max(5, 50 - index * 5 + Math.floor(Math.random() * 10)),
          cross_platform_masters: Math.max(2, 20 - index * 2 + Math.floor(Math.random() * 5)),
          total_achievements_unlocked: Math.max(50, 500 - index * 50 + Math.floor(Math.random() * 100))
        },
        strengths: strengthOptions
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 3) + 2),
        growth_opportunities: strengthOptions
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 2) + 1)
      }));
    };

    // Simulate API call
    setTimeout(() => {
      const mockData = generateMockAffiliations();
      setAffiliationRankings(mockData);
      setLoading(false);
    }, 1000);
  }, [timeframe]);

  const getStrengthIcon = (strength: string) => {
    const icons = {
      Gaming: Trophy,
      Betting: TrendingUp,
      Trading: Shield,
      Analysis: Crown,
      Community: Users,
      Consistency: Trophy
    };
    return icons[strength as keyof typeof icons] || Trophy;
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-white font-bold">
              <Users className="w-5 h-5 text-slate-300" />
              Team Competition
            </CardTitle>
            <CardDescription className="text-slate-300 font-medium">
              Affiliation dominance across all platform activities
            </CardDescription>
          </div>
          
          <div className="flex gap-2">
            {(['weekly', 'monthly', 'season'] as const).map(period => (
              <Button
                key={period}
                variant={timeframe === period ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeframe(period)}
                className="capitalize bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white data-[state=active]:bg-indigo-600 data-[state=active]:text-white font-semibold"
              >
                {period}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
            <span className="ml-2 text-slate-300 font-medium">Loading team rankings...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {affiliationRankings.slice(0, 6).map((affiliation) => (
              <div 
                key={affiliation.affiliation.id} 
                className="flex items-center justify-between p-4 rounded-lg bg-slate-800/30 border border-slate-600/30 hover:bg-slate-800/50 transition-colors cursor-pointer group"
              >
                <div className="flex items-center gap-4 flex-1 min-w-0">
                  {/* Rank */}
                  <RankBadge 
                    rank={affiliation.rank} 
                    change={affiliation.rank_change}
                    size="sm"
                  />
                  
                  {/* Affiliation Info */}
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {affiliation.affiliation.logo_url && (
                      <div className="w-10 h-10 bg-slate-800/50 rounded-lg shadow-sm border border-slate-600 flex items-center justify-center">
                        <img 
                          src={affiliation.affiliation.logo_url}
                          alt={affiliation.affiliation.name}
                          className="w-6 h-6 rounded"
                          onError={(e) => {
                            // Fallback to icon if image fails
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                        <Users className="w-6 h-6 text-slate-300" />
                      </div>
                    )}
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-white truncate group-hover:text-indigo-400 transition-colors">
                        {affiliation.affiliation.name}
                      </h4>
                      <div className="flex items-center gap-3 text-sm text-slate-400 font-medium">
                        <span>{affiliation.affiliation.member_count} members</span>
                        <span>•</span>
                        <span>{affiliation.dominance_metrics.top_performers_count} top performers</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Dominance Score & Strengths */}
                <div className="text-right min-w-0">
                  <div className="font-mono font-bold text-lg text-white mb-1">
                    {affiliation.dominance_metrics.overall_dominance_score.toFixed(0)}
                  </div>
                  
                  {/* Strength Badges */}
                  <div className="flex flex-wrap justify-end gap-1 mb-2">
                    {affiliation.strengths.slice(0, 3).map(strength => {
                      const Icon = getStrengthIcon(strength);
                      return (
                        <Badge key={strength} variant="secondary" className="text-xs flex items-center gap-1 bg-slate-700/50 text-slate-200 border-slate-600">
                          <Icon className="w-3 h-3" />
                          {strength}
                        </Badge>
                      );
                    })}
                  </div>
                  
                  {/* Cross-platform Masters */}
                  <div className="text-xs text-slate-400 font-medium">
                    {affiliation.dominance_metrics.cross_platform_masters} masters
                  </div>
                </div>
                
                <ChevronRight className="w-5 h-5 text-slate-400 group-hover:text-indigo-400 transition-colors ml-2" />
              </div>
            ))}
            
            {/* Competition Stats */}
            <div className="border-t border-slate-600 pt-4 mt-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-indigo-400 drop-shadow-sm">
                    {affiliationRankings.length}
                  </p>
                  <p className="text-sm text-slate-300 font-medium">Teams Competing</p>
                </div>
                
                <div>
                  <p className="text-2xl font-bold text-emerald-400 drop-shadow-sm">
                    {affiliationRankings.reduce((sum, aff) => sum + aff.affiliation.member_count, 0)}
                  </p>
                  <p className="text-sm text-slate-300 font-medium">Total Members</p>
                </div>
                
                <div>
                  <p className="text-2xl font-bold text-purple-400 drop-shadow-sm">
                    {affiliationRankings.reduce((sum, aff) => sum + aff.dominance_metrics.top_performers_count, 0)}
                  </p>
                  <p className="text-sm text-slate-300 font-medium">Top Performers</p>
                </div>
                
                <div>
                  <p className="text-2xl font-bold text-amber-400 drop-shadow-sm">
                    {affiliationRankings.reduce((sum, aff) => sum + aff.dominance_metrics.total_achievements_unlocked, 0)}
                  </p>
                  <p className="text-sm text-slate-300 font-medium">Achievements</p>
                </div>
              </div>
            </div>
            
            {/* View All Button */}
            <div className="text-center pt-4">
              <Button variant="outline" className="w-full bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white font-semibold">
                View Full Team Rankings
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AffiliationCompetitionPanel;