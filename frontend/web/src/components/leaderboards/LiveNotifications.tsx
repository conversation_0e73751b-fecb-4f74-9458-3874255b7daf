/**
 * LiveNotifications - Real-time Notification System
 * ===============================================
 * 
 * Displays live notifications for ranking changes, achievements, and social events
 * with smooth animations and auto-dismiss functionality.
 */

'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Trophy, 
  Crown, 
  Zap, 
  Users,
  X,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLeaderboardWebSocket } from '@/hooks/useLeaderboardWebSocket';

interface Notification {
  id: string;
  type: 'ranking_change' | 'achievement_unlock' | 'tier_promotion' | 'challenge_start' | 'user_activity';
  title: string;
  message: string;
  user?: {
    username: string;
    avatar_url?: string;
  };
  icon: React.ComponentType<any>;
  iconColor: string;
  timestamp: Date;
  autoCloseMs: number;
  priority: 'low' | 'medium' | 'high';
}

interface LiveNotificationsProps {
  userId?: string;
  maxVisible?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  showUserActivity?: boolean;
}

const LiveNotifications: React.FC<LiveNotificationsProps> = ({
  userId,
  maxVisible = 5,
  position = 'top-right',
  showUserActivity = true
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  const { 
    connectionStatus,
    onRankingUpdate,
    onAchievementUnlock,
    onChallengeUpdate,
    onUserActivity
  } = useLeaderboardWebSocket(userId);

  // Handle ranking updates
  useEffect(() => {
    onRankingUpdate((data) => {
      if (!data) return;
      
      const isImprovement = data.new_rank < data.old_rank;
      const rankDiff = Math.abs(data.new_rank - data.old_rank);
      
      if (rankDiff >= 3 || data.tier_change) { // Only show significant changes
        const notification: Notification = {
          id: `rank-${data.user_id}-${Date.now()}`,
          type: 'ranking_change',
          title: data.tier_change ? '🎉 Tier Promotion!' : (isImprovement ? '📈 Rank Up!' : '📉 Rank Change'),
          message: data.tier_change 
            ? `${data.username} promoted to ${data.tier_change.new_tier}!`
            : `${data.username} ${isImprovement ? 'climbed' : 'dropped'} to rank #${data.new_rank}`,
          user: {
            username: data.username,
            avatar_url: undefined // Will be fetched if needed
          },
          icon: data.tier_change ? Crown : (isImprovement ? TrendingUp : TrendingDown),
          iconColor: data.tier_change ? 'text-yellow-400' : (isImprovement ? 'text-emerald-400' : 'text-red-400'),
          timestamp: new Date(),
          autoCloseMs: data.tier_change ? 8000 : 5000,
          priority: data.tier_change ? 'high' : (rankDiff >= 10 ? 'high' : 'medium')
        };
        
        addNotification(notification);
      }
    });
  }, [onRankingUpdate]);

  // Handle achievement unlocks
  useEffect(() => {
    onAchievementUnlock((data) => {
      if (!data) return;
      
      const notification: Notification = {
        id: `achievement-${data.user_id}-${data.achievement.id}`,
        type: 'achievement_unlock',
        title: `🏆 ${data.achievement.rarity} Achievement!`,
        message: `${data.username} unlocked "${data.achievement.name}"`,
        user: {
          username: data.username,
          avatar_url: undefined
        },
        icon: data.achievement.rarity === 'Legendary' ? Crown : Trophy,
        iconColor: {
          'Common': 'text-slate-400',
          'Rare': 'text-blue-400',
          'Epic': 'text-purple-400',
          'Legendary': 'text-yellow-400'
        }[data.achievement.rarity],
        timestamp: new Date(),
        autoCloseMs: data.achievement.rarity === 'Legendary' ? 10000 : 6000,
        priority: data.achievement.rarity === 'Legendary' ? 'high' : 'medium'
      };
      
      addNotification(notification);
    });
  }, [onAchievementUnlock]);

  // Handle challenge updates
  useEffect(() => {
    onChallengeUpdate((data) => {
      if (!data) return;
      
      if (data.type === 'started') {
        const notification: Notification = {
          id: `challenge-${data.challenge_id}`,
          type: 'challenge_start',
          title: '⚔️ Challenge Started!',
          message: `New challenge between ${data.participants.join(' vs ')}`,
          icon: Zap,
          iconColor: 'text-amber-400',
          timestamp: new Date(),
          autoCloseMs: 4000,
          priority: 'medium'
        };
        
        addNotification(notification);
      }
    });
  }, [onChallengeUpdate]);

  // Handle user activity
  useEffect(() => {
    if (!showUserActivity) return;
    
    onUserActivity((data) => {
      if (!data) return;
      
      if (data.activity_type === 'major_win' || data.activity_type === 'streak_achievement') {
        const notification: Notification = {
          id: `activity-${data.user_id}-${Date.now()}`,
          type: 'user_activity',
          title: data.activity_type === 'major_win' ? '🎯 Major Win!' : '🔥 Streak!',
          message: `${data.username} achieved ${data.details.description}`,
          user: {
            username: data.username,
            avatar_url: undefined
          },
          icon: data.activity_type === 'major_win' ? Star : Zap,
          iconColor: 'text-indigo-500',
          timestamp: new Date(),
          autoCloseMs: 5000,
          priority: 'low'
        };
        
        addNotification(notification);
      }
    });
  }, [showUserActivity, onUserActivity]);

  const addNotification = (notification: Notification) => {
    setNotifications(prev => {
      // Remove oldest if at max capacity
      const filtered = prev.length >= maxVisible 
        ? prev.slice(-(maxVisible - 1))
        : prev;
      
      return [...filtered, notification];
    });

    // Auto-remove after specified time
    setTimeout(() => {
      removeNotification(notification.id);
    }, notification.autoCloseMs);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getPositionClasses = () => {
    const positions = {
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4',
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4'
    };
    return positions[position];
  };

  const sortedNotifications = notifications.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });

  return (
    <>
      {/* Notifications Container */}
      <div className={cn(
        "fixed z-50 flex flex-col gap-3 w-80 max-w-[calc(100vw-2rem)]",
        getPositionClasses()
      )}>
        <AnimatePresence mode="popLayout">
          {sortedNotifications.map((notification) => {
            const Icon = notification.icon;
            
            return (
              <motion.div
                key={notification.id}
                initial={{ 
                  opacity: 0, 
                  scale: 0.8,
                  x: position.includes('right') ? 100 : -100
                }}
                animate={{ 
                  opacity: 1, 
                  scale: 1,
                  x: 0
                }}
                exit={{ 
                  opacity: 0, 
                  scale: 0.8,
                  x: position.includes('right') ? 100 : -100
                }}
                transition={{ 
                  type: "spring", 
                  stiffness: 400, 
                  damping: 25 
                }}
                className={cn(
                  "bg-slate-900/90 backdrop-blur-sm border border-slate-700 rounded-lg shadow-lg p-4 cursor-pointer group relative overflow-hidden",
                  notification.priority === 'high' && "ring-2 ring-yellow-400/50",
                  "hover:shadow-xl transition-shadow"
                )}
                onClick={() => removeNotification(notification.id)}
              >
                {/* Priority Indicator */}
                {notification.priority === 'high' && (
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 to-amber-400"></div>
                )}
                
                <div className="flex items-start gap-3">
                  {/* Icon */}
                  <div className={cn(
                    "p-2 rounded-full bg-slate-800/50 group-hover:scale-110 transition-transform border border-slate-600",
                    notification.iconColor.replace('text-', 'bg-').replace('-400', '-900/20')
                  )}>
                    <Icon className={cn("w-4 h-4", notification.iconColor)} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-semibold text-sm text-white truncate">
                        {notification.title}
                      </h4>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-slate-700 rounded"
                      >
                        <X className="w-3 h-3 text-slate-400" />
                      </button>
                    </div>
                    
                    {/* Message */}
                    <p className="text-xs text-slate-300 font-medium mb-2 line-clamp-2">
                      {notification.message}
                    </p>
                    
                    {/* User Info */}
                    {notification.user && (
                      <div className="flex items-center gap-2">
                        <Avatar className="w-5 h-5">
                          <AvatarImage src={notification.user.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {notification.user.username.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs font-medium text-slate-200">
                          {notification.user.username}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Auto-close Progress Bar */}
                <motion.div
                  className="absolute bottom-0 left-0 h-0.5 bg-slate-600"
                  initial={{ width: "100%" }}
                  animate={{ width: "0%" }}
                  transition={{ 
                    duration: notification.autoCloseMs / 1000,
                    ease: "linear"
                  }}
                />
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </>
  );
};

export default LiveNotifications;