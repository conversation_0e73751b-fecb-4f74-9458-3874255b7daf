/**
 * ActiveChallengesPanel - Social Competition & Challenges Display
 * ==============================================================
 * 
 * Displays active challenges, social competitions, and community recognition features.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Swords, 
  Clock,
  Users,
  Eye,
  Trophy,
  Target,
  Loader2,
  Plus,
  ChevronRight,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Challenge {
  id: string;
  challenge_type: 'direct_duel' | 'accuracy_contest' | 'consistency_challenge' | 'cross_module_showdown';
  participants: {
    challenger: {
      user_id: string;
      username: string;
      avatar_url?: string;
    };
    challenged?: {
      user_id: string;
      username: string;
      avatar_url?: string;
    };
    spectators: number;
  };
  challenge_config: {
    modules_involved: string[];
    duration_hours: number;
    wager_amount?: number;
  };
  status: 'pending' | 'accepted' | 'active' | 'completed';
  timeline: {
    created_at: string;
    started_at?: string;
    expires_at: string;
  };
  current_progress?: {
    challenger_progress: number; // 0-1
    challenged_progress?: number; // 0-1
  };
  community_engagement: {
    spectator_count: number;
    social_buzz_score: number;
  };
}

const ActiveChallengesPanel: React.FC = () => {
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'trending' | 'yours'>('active');

  // Mock data generation
  useEffect(() => {
    const generateMockChallenges = (): Challenge[] => {
      const challengeTypes: Challenge['challenge_type'][] = [
        'direct_duel', 'accuracy_contest', 'consistency_challenge', 'cross_module_showdown'
      ];
      
      const usernames = [
        'GameMaster2025', 'BettingWiz', 'TradePro', 'AnalystKing', 'SkillSeeker',
        'ChallengeAce', 'CompetitorX', 'RivalPlayer', 'ChampionMind', 'ProGamer'
      ];

      const modules = ['gaming', 'betting', 'trading', 'analyst'];

      return Array.from({ length: 6 }, (_, index) => {
        const challengeType = challengeTypes[Math.floor(Math.random() * challengeTypes.length)];
        const challenger = usernames[Math.floor(Math.random() * usernames.length)];
        const challenged = Math.random() > 0.3 ? usernames[Math.floor(Math.random() * usernames.length)] : undefined;
        
        return {
          id: `challenge-${index + 1}`,
          challenge_type: challengeType,
          participants: {
            challenger: {
              user_id: `user-${index * 2 + 1}`,
              username: challenger,
              avatar_url: `/avatars/${challenger.toLowerCase()}.jpg`
            },
            challenged: challenged ? {
              user_id: `user-${index * 2 + 2}`,
              username: challenged,
              avatar_url: `/avatars/${challenged.toLowerCase()}.jpg`
            } : undefined,
            spectators: Math.floor(Math.random() * 50) + 5
          },
          challenge_config: {
            modules_involved: modules.slice(0, Math.floor(Math.random() * 3) + 1),
            duration_hours: [1, 3, 6, 12, 24][Math.floor(Math.random() * 5)],
            wager_amount: Math.random() > 0.5 ? Math.floor(Math.random() * 500) + 50 : undefined
          },
          status: ['pending', 'accepted', 'active', 'completed'][Math.floor(Math.random() * 4)] as Challenge['status'],
          timeline: {
            created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            started_at: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString() : undefined,
            expires_at: new Date(Date.now() + Math.random() * 48 * 60 * 60 * 1000).toISOString()
          },
          current_progress: Math.random() > 0.4 ? {
            challenger_progress: Math.random(),
            challenged_progress: Math.random()
          } : undefined,
          community_engagement: {
            spectator_count: Math.floor(Math.random() * 100) + 10,
            social_buzz_score: Math.floor(Math.random() * 1000) + 100
          }
        };
      });
    };

    // Simulate API call
    setTimeout(() => {
      const mockChallenges = generateMockChallenges();
      setChallenges(mockChallenges);
      setLoading(false);
    }, 900);
  }, []);

  const getChallengeTypeConfig = (type: Challenge['challenge_type']) => {
    const configs = {
      direct_duel: {
        icon: Swords,
        name: 'Direct Duel',
        color: 'text-red-400 bg-red-900/20',
        description: 'Head-to-head competition'
      },
      accuracy_contest: {
        icon: Target,
        name: 'Accuracy Contest',
        color: 'text-blue-400 bg-blue-900/20',
        description: 'Precision-based challenge'
      },
      consistency_challenge: {
        icon: Trophy,
        name: 'Consistency Challenge',
        color: 'text-emerald-400 bg-emerald-900/20',
        description: 'Sustained performance test'
      },
      cross_module_showdown: {
        icon: Zap,
        name: 'Cross-Module Showdown',
        color: 'text-purple-400 bg-purple-900/20',
        description: 'Multi-platform competition'
      }
    };
    return configs[type];
  };

  const getStatusColor = (status: Challenge['status']) => {
    const colors = {
      pending: 'bg-yellow-900/20 text-yellow-400',
      accepted: 'bg-blue-900/20 text-blue-400',
      active: 'bg-emerald-900/20 text-emerald-400',
      completed: 'bg-slate-800/50 text-slate-300'
    };
    return colors[status];
  };

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diffMs = expires.getTime() - now.getTime();
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));
    
    if (diffHours <= 0) return 'Expired';
    if (diffHours < 24) return `${diffHours}h remaining`;
    const diffDays = Math.ceil(diffHours / 24);
    return `${diffDays}d remaining`;
  };

  const filteredChallenges = challenges.filter(challenge => {
    switch (activeTab) {
      case 'active':
        return challenge.status === 'active' || challenge.status === 'accepted';
      case 'trending':
        return challenge.community_engagement.social_buzz_score > 500;
      case 'yours':
        // In a real app, filter by current user's challenges
        return challenge.participants.challenger.username.includes('Game') || 
               challenge.participants.challenged?.username.includes('Game');
      default:
        return true;
    }
  });

  return (
    <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-white font-bold">
              <Swords className="w-5 h-5 text-slate-300" />
              Active Challenges
            </CardTitle>
            <CardDescription className="text-slate-300 font-medium">
              Social competition and direct challenges
            </CardDescription>
          </div>
          
          <Button size="sm" className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-semibold shadow-lg border border-red-400">
            <Plus className="w-4 h-4 mr-2" />
            Create Challenge
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Challenge Type Tabs */}
        <div className="flex gap-1 p-1 bg-slate-800/50 border border-slate-600 rounded-lg">
          {([
            { key: 'active', label: 'Active', icon: Zap },
            { key: 'trending', label: 'Trending', icon: Trophy },
            { key: 'yours', label: 'Yours', icon: Users }
          ] as const).map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={cn(
                  "flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  activeTab === tab.key
                    ? "bg-indigo-600 text-white shadow-sm"
                    : "text-slate-300 hover:text-white font-medium"
                )}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Challenge List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
            <span className="ml-2 text-slate-300 font-medium">Loading challenges...</span>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredChallenges.slice(0, 4).map((challenge) => {
              const typeConfig = getChallengeTypeConfig(challenge.challenge_type);
              const Icon = typeConfig.icon;
              
              return (
                <div key={challenge.id} className="border border-slate-600/30 rounded-lg p-4 bg-slate-800/20 hover:bg-slate-800/40 transition-colors cursor-pointer group">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={cn("p-2 rounded-lg", typeConfig.color)}>
                        <Icon className="w-4 h-4" />
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-white group-hover:text-red-400 transition-colors">
                          {typeConfig.name}
                        </h4>
                        <p className="text-sm text-slate-400 font-medium">{typeConfig.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={cn("text-xs", getStatusColor(challenge.status))}>
                        {challenge.status}
                      </Badge>
                      
                      {challenge.challenge_config.wager_amount && (
                        <Badge variant="outline" className="text-xs">
                          ${challenge.challenge_config.wager_amount}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {/* Participants */}
                  <div className="flex items-center gap-4 mb-3">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={challenge.participants.challenger.avatar_url} />
                        <AvatarFallback>
                          {challenge.participants.challenger.username.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium text-white">{challenge.participants.challenger.username}</span>
                    </div>
                    
                    <Swords className="w-4 h-4 text-slate-400" />
                    
                    {challenge.participants.challenged ? (
                      <div className="flex items-center gap-2">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={challenge.participants.challenged.avatar_url} />
                          <AvatarFallback>
                            {challenge.participants.challenged.username.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-white">{challenge.participants.challenged.username}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-slate-400">
                        <div className="w-8 h-8 border-2 border-dashed border-slate-500 rounded-full flex items-center justify-center">
                          <Plus className="w-4 h-4" />
                        </div>
                        <span className="text-sm font-medium">Open challenge</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Progress Bars (if active) */}
                  {challenge.current_progress && challenge.status === 'active' && (
                    <div className="space-y-2 mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <div className="flex-1">
                          <Progress value={challenge.current_progress.challenger_progress * 100} className="h-2" />
                        </div>
                        <span className="text-xs font-medium text-slate-200">{(challenge.current_progress.challenger_progress * 100).toFixed(0)}%</span>
                      </div>
                      
                      {challenge.current_progress.challenged_progress !== undefined && (
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <div className="flex-1">
                            <Progress value={challenge.current_progress.challenged_progress * 100} className="h-2" />
                          </div>
                          <span className="text-xs font-medium text-slate-200">{(challenge.current_progress.challenged_progress * 100).toFixed(0)}%</span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Challenge Info Footer */}
                  <div className="flex items-center justify-between text-sm text-slate-400">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{challenge.community_engagement.spectator_count}</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{getTimeRemaining(challenge.timeline.expires_at)}</span>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {challenge.challenge_config.modules_involved.map(module => (
                          <Badge key={module} variant="secondary" className="text-xs capitalize bg-slate-700/50 text-slate-200 border-slate-600">
                            {module}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <ChevronRight className="w-4 h-4 group-hover:text-red-400 transition-colors" />
                  </div>
                </div>
              );
            })}
            
            {filteredChallenges.length === 0 && (
              <div className="text-center py-8 text-slate-300">
                <Swords className="w-12 h-12 mx-auto mb-3 text-slate-400" />
                <p className="text-lg font-medium mb-1 text-white">No active challenges</p>
                <p className="text-sm font-medium">Create a challenge to get started!</p>
              </div>
            )}
          </div>
        )}
        
        {/* Quick Stats */}
        {!loading && challenges.length > 0 && (
          <div className="border-t border-slate-600 pt-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-lg font-bold text-red-400 drop-shadow-sm">
                  {challenges.filter(c => c.status === 'active').length}
                </p>
                <p className="text-xs text-slate-300 font-medium">Active Now</p>
              </div>
              
              <div>
                <p className="text-lg font-bold text-blue-400 drop-shadow-sm">
                  {challenges.reduce((sum, c) => sum + c.community_engagement.spectator_count, 0)}
                </p>
                <p className="text-xs text-slate-300 font-medium">Spectators</p>
              </div>
              
              <div>
                <p className="text-lg font-bold text-emerald-400 drop-shadow-sm">
                  ${challenges.reduce((sum, c) => sum + (c.challenge_config.wager_amount || 0), 0)}
                </p>
                <p className="text-xs text-slate-300 font-medium">Total Stakes</p>
              </div>
            </div>
          </div>
        )}
        
        {/* View All Button */}
        <div className="text-center pt-2">
          <Button variant="outline" className="w-full bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white font-semibold">
            View All Challenges
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActiveChallengesPanel;