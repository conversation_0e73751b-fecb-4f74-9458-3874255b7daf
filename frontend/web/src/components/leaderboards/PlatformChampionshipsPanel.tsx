/**
 * PlatformChampionshipsPanel - Major Championship Events Display
 * =============================================================
 * 
 * Displays platform-wide championships, tournaments, and major competitive events
 * spanning all BetBet modules with live standings and prize information.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Crown,
  Medal,
  Calendar,
  Users,
  DollarSign,
  Clock,
  Loader2,
  ChevronRight,
  Star,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Championship {
  id: string;
  name: string;
  description: string;
  championship_type: 'ultimate' | 'seasonal' | 'themed' | 'affiliation_vs_affiliation';
  status: 'registration' | 'qualifying' | 'active' | 'finals' | 'completed';
  
  schedule: {
    registration_opens: string;
    registration_closes: string;
    start_date: string;
    end_date: string;
    finals_date?: string;
  };
  
  participation: {
    total_participants: number;
    max_participants?: number;
    participating_affiliations: number;
  };
  
  prize_structure: {
    total_prize_pool: number;
    currency: string;
    guaranteed: boolean;
  };
  
  current_phase?: {
    name: string;
    progress: number; // 0-1
    next_event?: string;
    next_event_date?: string;
  };
  
  featured_participants?: {
    user_id: string;
    username: string;
    current_rank: number;
    points_earned: number;
  }[];
}

const PlatformChampionshipsPanel: React.FC = () => {
  const [championships, setChampionships] = useState<Championship[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'upcoming'>('active');

  // Mock data generation
  useEffect(() => {
    const generateMockChampionships = (): Championship[] => {
      return [
        {
          id: 'ultimate-2025',
          name: 'BetBet Ultimate Championship 2025',
          description: 'The premier cross-platform competition featuring all six modules',
          championship_type: 'ultimate',
          status: 'active',
          schedule: {
            registration_opens: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            registration_closes: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            start_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
            finals_date: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString()
          },
          participation: {
            total_participants: 1247,
            max_participants: 2000,
            participating_affiliations: 45
          },
          prize_structure: {
            total_prize_pool: 50000,
            currency: 'USD',
            guaranteed: true
          },
          current_phase: {
            name: 'Qualifying Rounds',
            progress: 0.65,
            next_event: 'Semi-Finals',
            next_event_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          featured_participants: [
            { user_id: 'user1', username: 'ChampionMaster', current_rank: 1, points_earned: 15750 },
            { user_id: 'user2', username: 'EliteCompetitor', current_rank: 2, points_earned: 14200 },
            { user_id: 'user3', username: 'PlatformKing', current_rank: 3, points_earned: 13890 }
          ]
        },
        {
          id: 'winter-season',
          name: 'Winter Season Championship',
          description: 'Seasonal competition focusing on consistency and skill',
          championship_type: 'seasonal',
          status: 'finals',
          schedule: {
            registration_opens: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            registration_closes: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000).toISOString(),
            start_date: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            finals_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
          },
          participation: {
            total_participants: 856,
            max_participants: 1000,
            participating_affiliations: 32
          },
          prize_structure: {
            total_prize_pool: 25000,
            currency: 'USD',
            guaranteed: true
          },
          current_phase: {
            name: 'Grand Finals',
            progress: 0.95,
            next_event: 'Championship Match',
            next_event_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
          }
        },
        {
          id: 'affiliations-war',
          name: 'Great Affiliation War',
          description: 'Epic battle between the top affiliations across all modules',
          championship_type: 'affiliation_vs_affiliation',
          status: 'registration',
          schedule: {
            registration_opens: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            registration_closes: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
            start_date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 51 * 24 * 60 * 60 * 1000).toISOString()
          },
          participation: {
            total_participants: 432,
            max_participants: 1500,
            participating_affiliations: 18
          },
          prize_structure: {
            total_prize_pool: 75000,
            currency: 'USD',
            guaranteed: true
          }
        },
        {
          id: 'rookie-rising',
          name: 'Rookie Rising Stars',
          description: 'Championship for new players to showcase their skills',
          championship_type: 'themed',
          status: 'upcoming',
          schedule: {
            registration_opens: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            registration_closes: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
            start_date: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 56 * 24 * 60 * 60 * 1000).toISOString()
          },
          participation: {
            total_participants: 0,
            max_participants: 500,
            participating_affiliations: 0
          },
          prize_structure: {
            total_prize_pool: 15000,
            currency: 'USD',
            guaranteed: true
          }
        }
      ];
    };

    // Simulate API call
    setTimeout(() => {
      const mockChampionships = generateMockChampionships();
      setChampionships(mockChampionships);
      setLoading(false);
    }, 1100);
  }, []);

  const getChampionshipTypeConfig = (type: Championship['championship_type']) => {
    const configs = {
      ultimate: {
        icon: Crown,
        name: 'Ultimate',
        color: 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30',
        gradient: 'from-yellow-400 to-amber-500'
      },
      seasonal: {
        icon: Trophy,
        name: 'Seasonal',
        color: 'text-blue-400 bg-blue-900/20 border-blue-500/30',
        gradient: 'from-blue-400 to-indigo-500'
      },
      themed: {
        icon: Star,
        name: 'Themed',
        color: 'text-purple-400 bg-purple-900/20 border-purple-500/30',
        gradient: 'from-purple-400 to-pink-500'
      },
      affiliation_vs_affiliation: {
        icon: Users,
        name: 'Team Battle',
        color: 'text-red-400 bg-red-900/20 border-red-500/30',
        gradient: 'from-red-400 to-pink-500'
      }
    };
    return configs[type];
  };

  const getStatusConfig = (status: Championship['status']) => {
    const configs = {
      registration: {
        color: 'bg-blue-900/20 text-blue-400',
        label: 'Registration Open'
      },
      qualifying: {
        color: 'bg-yellow-900/20 text-yellow-400',
        label: 'Qualifying'
      },
      active: {
        color: 'bg-emerald-900/20 text-emerald-400',
        label: 'Active'
      },
      finals: {
        color: 'bg-purple-900/20 text-purple-400',
        label: 'Finals'
      },
      completed: {
        color: 'bg-slate-800/50 text-slate-300',
        label: 'Completed'
      }
    };
    return configs[status];
  };

  const getTimeUntilEvent = (dateString: string) => {
    const now = new Date();
    const eventDate = new Date(dateString);
    const diffMs = eventDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Now';
    if (diffDays === 1) return '1 day';
    if (diffDays < 7) return `${diffDays} days`;
    const diffWeeks = Math.ceil(diffDays / 7);
    return `${diffWeeks} week${diffWeeks > 1 ? 's' : ''}`;
  };

  const filteredChampionships = championships.filter(championship => {
    switch (activeFilter) {
      case 'active':
        return ['active', 'finals', 'qualifying'].includes(championship.status);
      case 'upcoming':
        return ['registration'].includes(championship.status);
      default:
        return true;
    }
  });

  return (
    <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-white font-bold">
              <Crown className="w-5 h-5 text-slate-300" />
              Platform Championships
            </CardTitle>
            <CardDescription className="text-slate-300 font-medium">
              Major competitive events and tournaments
            </CardDescription>
          </div>
          
          <div className="flex gap-2">
            {(['all', 'active', 'upcoming'] as const).map(filter => (
              <Button
                key={filter}
                variant={activeFilter === filter ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(filter)}
                className="capitalize bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white data-[state=active]:bg-indigo-600 data-[state=active]:text-white font-semibold"
              >
                {filter}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
            <span className="ml-2 text-slate-300 font-medium">Loading championships...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredChampionships.map((championship) => {
              const typeConfig = getChampionshipTypeConfig(championship.championship_type);
              const statusConfig = getStatusConfig(championship.status);
              const TypeIcon = typeConfig.icon;
              
              return (
                <div key={championship.id} className="border border-slate-600/30 rounded-lg p-6 bg-slate-800/20 hover:bg-slate-800/40 transition-all cursor-pointer group">
                  {/* Championship Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className={cn("p-3 rounded-xl border", typeConfig.color)}>
                        <TypeIcon className="w-6 h-6" />
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-bold text-white group-hover:text-yellow-400 transition-colors">
                          {championship.name}
                        </h3>
                        <p className="text-sm text-slate-400 font-medium mb-2">
                          {championship.description}
                        </p>
                        <div className="flex items-center gap-3">
                          <Badge className={cn("text-xs", statusConfig.color)}>
                            {statusConfig.label}
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-slate-700/50 text-slate-200 border-slate-600">
                            {typeConfig.name}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    {/* Prize Pool */}
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-emerald-400 font-bold text-lg drop-shadow-sm">
                        <DollarSign className="w-5 h-5" />
                        <span>{championship.prize_structure.total_prize_pool.toLocaleString()}</span>
                      </div>
                      <p className="text-xs text-slate-400 font-medium">
                        {championship.prize_structure.guaranteed ? 'Guaranteed' : 'Est.'} Prize Pool
                      </p>
                    </div>
                  </div>
                  
                  {/* Championship Stats */}
                  <div className="grid grid-cols-3 gap-6 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-blue-400 font-semibold">
                        <Users className="w-4 h-4" />
                        <span>{championship.participation.total_participants}</span>
                      </div>
                      <p className="text-xs text-slate-400 font-medium">Participants</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-purple-400 font-semibold">
                        <Trophy className="w-4 h-4" />
                        <span>{championship.participation.participating_affiliations}</span>
                      </div>
                      <p className="text-xs text-slate-400 font-medium">Teams</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-amber-400 font-semibold">
                        <Calendar className="w-4 h-4" />
                        <span>{getTimeUntilEvent(championship.schedule.end_date)}</span>
                      </div>
                      <p className="text-xs text-slate-400 font-medium">Remaining</p>
                    </div>
                  </div>
                  
                  {/* Current Phase Progress */}
                  {championship.current_phase && (
                    <div className="mb-4 p-3 bg-slate-800/30 border border-slate-600/30 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-white">
                          {championship.current_phase.name}
                        </h4>
                        <span className="text-sm text-slate-300 font-medium">
                          {(championship.current_phase.progress * 100).toFixed(0)}% Complete
                        </span>
                      </div>
                      
                      <Progress value={championship.current_phase.progress * 100} className="mb-2" />
                      
                      {championship.current_phase.next_event && (
                        <div className="flex items-center justify-between text-sm text-slate-400 font-medium">
                          <span>Next: {championship.current_phase.next_event}</span>
                          {championship.current_phase.next_event_date && (
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {getTimeUntilEvent(championship.current_phase.next_event_date)}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Featured Participants */}
                  {championship.featured_participants && (
                    <div className="mb-4">
                      <h4 className="font-medium text-white mb-3 flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-400" />
                        Leading Participants
                      </h4>
                      
                      <div className="flex items-center gap-4">
                        {championship.featured_participants.slice(0, 3).map((participant, index) => {
                          const rankColors = ['text-yellow-400', 'text-slate-300', 'text-amber-400'];
                          const RankIcon = [Crown, Medal, Medal][index];
                          
                          return (
                            <div key={participant.user_id} className="flex items-center gap-2">
                              <div className={cn("flex items-center gap-1", rankColors[index])}>
                                <RankIcon className="w-4 h-4" />
                                <span className="font-medium">#{participant.current_rank}</span>
                              </div>
                              <span className="text-sm font-medium text-white">{participant.username}</span>
                              <span className="text-xs text-slate-400 font-medium">
                                {participant.points_earned.toLocaleString()} pts
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  
                  {/* Action Footer */}
                  <div className="flex items-center justify-between pt-4 border-t border-slate-600">
                    <div className="flex items-center gap-4 text-sm text-slate-400 font-medium">
                      {championship.status === 'registration' && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>Registration closes in {getTimeUntilEvent(championship.schedule.registration_closes)}</span>
                        </div>
                      )}
                      
                      {championship.schedule.finals_date && (
                        <div className="flex items-center gap-1">
                          <Zap className="w-4 h-4" />
                          <span>Finals in {getTimeUntilEvent(championship.schedule.finals_date)}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {championship.status === 'registration' && (
                        <Button size="sm" className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold shadow-lg border border-yellow-400">
                          Register Now
                        </Button>
                      )}
                      
                      <Button variant="outline" size="sm" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white group-hover:border-yellow-500 group-hover:text-yellow-400 font-semibold">
                        View Details
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
            
            {filteredChampionships.length === 0 && (
              <div className="text-center py-8 text-slate-300">
                <Crown className="w-12 h-12 mx-auto mb-3 text-slate-400" />
                <p className="text-lg font-medium mb-1 text-white">No championships available</p>
                <p className="text-sm font-medium">Check back soon for exciting new competitions!</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PlatformChampionshipsPanel;