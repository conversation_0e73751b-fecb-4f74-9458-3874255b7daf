/**
 * AchievementCelebration - Celebration Modal for Achievement Unlocks
 * ================================================================
 * 
 * Full-screen celebration modal for major achievement unlocks with animations,
 * confetti effects, and social sharing integration.
 */

'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Trophy, 
  Crown, 
  Star, 
  Share2, 
  Download,
  X,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAchievementCelebration } from '@/hooks/useLeaderboardWebSocket';

interface AchievementCelebrationProps {
  userId?: string;
  onShare?: (achievement: any) => void;
  onDownload?: (achievement: any) => void;
}

const AchievementCelebration: React.FC<AchievementCelebrationProps> = ({
  userId,
  onShare,
  onDownload
}) => {
  const { currentCelebration, celebrationQueue, dismissCelebration } = useAchievementCelebration(userId);
  const [showConfetti, setShowConfetti] = useState(false);

  // Trigger confetti effect on new celebration
  useEffect(() => {
    if (currentCelebration) {
      setShowConfetti(true);
      const timeout = setTimeout(() => setShowConfetti(false), 3000);
      return () => clearTimeout(timeout);
    }
  }, [currentCelebration]);

  if (!currentCelebration) return null;

  const { achievement, user_id, username } = currentCelebration;
  
  const getRarityConfig = (rarity: string) => {
    const configs = {
      Common: {
        bgGradient: 'from-gray-400 to-gray-600',
        textColor: 'text-gray-700',
        glowColor: 'shadow-gray-500/25',
        icon: Trophy
      },
      Rare: {
        bgGradient: 'from-blue-400 to-blue-600',
        textColor: 'text-blue-700',
        glowColor: 'shadow-blue-500/25',
        icon: Trophy
      },
      Epic: {
        bgGradient: 'from-purple-400 to-purple-600',
        textColor: 'text-purple-700',
        glowColor: 'shadow-purple-500/25',
        icon: Star
      },
      Legendary: {
        bgGradient: 'from-yellow-400 to-orange-500',
        textColor: 'text-yellow-700',
        glowColor: 'shadow-yellow-500/25',
        icon: Crown
      }
    };
    return configs[rarity as keyof typeof configs] || configs.Common;
  };

  const rarityConfig = getRarityConfig(achievement.rarity);
  const RarityIcon = rarityConfig.icon;

  return (
    <AnimatePresence>
      <Dialog open={!!currentCelebration} onOpenChange={dismissCelebration}>
        <DialogContent className="max-w-2xl p-0 border-0 bg-transparent shadow-none">
          <motion.div
            initial={{ scale: 0, opacity: 0, rotateY: -180 }}
            animate={{ scale: 1, opacity: 1, rotateY: 0 }}
            exit={{ scale: 0, opacity: 0, rotateY: 180 }}
            transition={{ 
              type: "spring", 
              stiffness: 200, 
              damping: 20,
              duration: 0.8
            }}
            className="relative bg-white rounded-2xl overflow-hidden"
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, currentColor 2px, transparent 2px),
                                 radial-gradient(circle at 75% 75%, currentColor 2px, transparent 2px)`,
                backgroundSize: '50px 50px'
              }} />
            </div>

            {/* Confetti Animation */}
            {showConfetti && (
              <div className="absolute inset-0 pointer-events-none overflow-hidden">
                {Array.from({ length: 50 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className={cn(
                      "absolute w-2 h-2 rounded",
                      ['bg-yellow-400', 'bg-blue-400', 'bg-red-400', 'bg-green-400', 'bg-purple-400'][i % 5]
                    )}
                    initial={{
                      x: Math.random() * window.innerWidth,
                      y: -10,
                      rotate: 0,
                      scale: Math.random() * 0.8 + 0.5
                    }}
                    animate={{
                      y: window.innerHeight + 10,
                      rotate: Math.random() * 720 + 360,
                      x: Math.random() * window.innerWidth
                    }}
                    transition={{
                      duration: Math.random() * 2 + 1,
                      delay: Math.random() * 1,
                      ease: "easeOut"
                    }}
                  />
                ))}
              </div>
            )}

            {/* Close Button */}
            <button
              onClick={dismissCelebration}
              className="absolute top-4 right-4 z-10 p-2 bg-white/80 hover:bg-white rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>

            {/* Header */}
            <div className={cn(
              "relative px-8 pt-8 pb-6 bg-gradient-to-br text-white",
              rarityConfig.bgGradient
            )}>
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-4"
                >
                  <RarityIcon className="w-10 h-10 text-white" />
                </motion.div>
                
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-3xl font-bold mb-2"
                >
                  Achievement Unlocked!
                </motion.h2>
                
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6, type: "spring" }}
                >
                  <Badge className="bg-white/20 text-white border-white/30 text-lg px-4 py-1">
                    {achievement.rarity}
                  </Badge>
                </motion.div>
              </div>

              {/* Sparkle Effects */}
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 8 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    style={{
                      left: `${20 + (i * 10) + Math.random() * 20}%`,
                      top: `${20 + Math.random() * 60}%`
                    }}
                    initial={{ scale: 0, opacity: 0, rotate: 0 }}
                    animate={{ 
                      scale: [0, 1, 0], 
                      opacity: [0, 1, 0],
                      rotate: 360
                    }}
                    transition={{
                      duration: 2,
                      delay: Math.random() * 2,
                      repeat: Infinity,
                      repeatDelay: Math.random() * 3
                    }}
                  >
                    <Sparkles className="w-4 h-4 text-white" />
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Achievement Details */}
            <div className="px-8 py-6">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="text-center mb-6"
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {achievement.name}
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed">
                  {achievement.description}
                </p>
              </motion.div>

              {/* Achievement Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.0 }}
                className="grid grid-cols-2 gap-6 mb-6"
              >
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {achievement.points.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Points Earned</div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {achievement.rarity}
                  </div>
                  <div className="text-sm text-gray-600">Rarity Level</div>
                </div>
              </motion.div>

              {/* Achievement Icon/Image */}
              {achievement.icon_url && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.2, type: "spring" }}
                  className="text-center mb-6"
                >
                  <img
                    src={achievement.icon_url}
                    alt={achievement.name}
                    className={cn(
                      "w-24 h-24 mx-auto rounded-xl shadow-lg",
                      rarityConfig.glowColor
                    )}
                  />
                </motion.div>
              )}

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.4 }}
                className="flex gap-3 justify-center"
              >
                <Button
                  onClick={() => onShare?.(achievement)}
                  className={cn(
                    "flex items-center gap-2 px-6 bg-gradient-to-r text-white",
                    rarityConfig.bgGradient
                  )}
                >
                  <Share2 className="w-4 h-4" />
                  Share Achievement
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => onDownload?.(achievement)}
                  className="flex items-center gap-2 px-6"
                >
                  <Download className="w-4 h-4" />
                  Download
                </Button>
              </motion.div>

              {/* Queue Indicator */}
              {celebrationQueue > 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.6 }}
                  className="text-center mt-4"
                >
                  <Badge variant="secondary" className="text-sm">
                    +{celebrationQueue} more achievement{celebrationQueue > 1 ? 's' : ''} unlocked!
                  </Badge>
                </motion.div>
              )}
            </div>

            {/* Bottom Gradient */}
            <div className={cn(
              "h-2 bg-gradient-to-r",
              rarityConfig.bgGradient
            )}></div>
          </motion.div>
        </DialogContent>
      </Dialog>
    </AnimatePresence>
  );
};

export default AchievementCelebration;