/**
 * LeaderboardTable - Main Leaderboard Display Component
 * ====================================================
 * 
 * Comprehensive table displaying cross-platform leaderboard rankings with real-time updates,
 * tier indicators, performance metrics, and achievement displays.
 */

'use client';

import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Loader2, Trophy as TrophyIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

// Import utility components
import TierBadge from './TierBadge';
import AchievementBadge from './AchievementBadge';
import RankBadge from './RankBadge';
import ScoreDisplay from './ScoreDisplay';
import MiniTrendChart from './MiniTrendChart';

// Type definitions
type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
type LeaderboardView = 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';

interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar_url?: string;
  affiliation?: {
    id: string;
    name: string;
    logo_url?: string;
  };
  
  // Unified Performance
  unified_score: number;
  platform_tier: PlatformTier;
  tier_progress: number;
  
  // Module-specific scores
  gaming_score: number;
  gaming_change: number;
  betting_score: number;
  betting_change: number;
  trading_score: number;
  trading_change: number;
  analyst_score: number;
  analyst_change: number;
  
  // Cross-platform metrics
  cross_platform_consistency: number;
  active_modules: string[];
  
  // Recent achievements
  recent_achievements: Achievement[];
  
  // Performance trends
  score_history: {
    timestamp: string;
    score: number;
  }[];
  rank_change?: number;
}

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  points: number;
  icon_url?: string;
  unlocked_at?: string;
}

interface LeaderboardTableProps {
  data: LeaderboardEntry[];
  view: LeaderboardView;
  isLoading?: boolean;
  currentUserId?: string;
  onUserClick?: (userId: string) => void;
  showAffiliation?: boolean;
  showTrends?: boolean;
}

const LeaderboardTable: React.FC<LeaderboardTableProps> = ({
  data,
  view,
  isLoading = false,
  currentUserId,
  onUserClick,
  showAffiliation = true,
  showTrends = true
}) => {
  const getViewSpecificHeaders = (view: LeaderboardView) => {
    switch (view) {
      case 'unified':
        return (
          <>
            <TableHead className="text-center text-slate-200 font-semibold">Tier</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Unified Score</TableHead>
            <TableHead className="text-right w-24 text-slate-200 font-semibold">Gaming</TableHead>
            <TableHead className="text-right w-24 text-slate-200 font-semibold">Betting</TableHead>
            <TableHead className="text-right w-24 text-slate-200 font-semibold">Trading</TableHead>
            <TableHead className="text-right w-24 text-slate-200 font-semibold">Analysis</TableHead>
          </>
        );
      case 'gaming':
        return (
          <>
            <TableHead className="text-right text-slate-200 font-semibold">Gaming Score</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Games Played</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Win Rate</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Streak</TableHead>
          </>
        );
      case 'betting':
        return (
          <>
            <TableHead className="text-right text-slate-200 font-semibold">Betting Score</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Accuracy</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Volume</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">ROI</TableHead>
          </>
        );
      case 'trading':
        return (
          <>
            <TableHead className="text-right text-slate-200 font-semibold">Trading Score</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">ROI</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Volume</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Sharpe Ratio</TableHead>
          </>
        );
      case 'analyst':
        return (
          <>
            <TableHead className="text-right text-slate-200 font-semibold">Analyst Score</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Accuracy</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Subscribers</TableHead>
            <TableHead className="text-right text-slate-200 font-semibold">Revenue</TableHead>
          </>
        );
    }
  };

  const getViewSpecificCells = (entry: LeaderboardEntry, view: LeaderboardView) => {
    switch (view) {
      case 'unified':
        return (
          <>
            <TableCell className="text-center">
              <TierBadge tier={entry.platform_tier} size="sm" />
            </TableCell>
            
            <TableCell className="text-right">
              <div className="font-mono font-bold text-lg text-white">
                {entry.unified_score.toFixed(1)}
              </div>
              <div className="text-xs text-slate-400 font-medium">
                {entry.tier_progress && (
                  <>{(entry.tier_progress * 100).toFixed(0)}% to next</>
                )}
              </div>
            </TableCell>
            
            <TableCell className="text-right">
              <ScoreDisplay 
                value={entry.gaming_score} 
                change={entry.gaming_change}
                format="decimal"
              />
            </TableCell>
            
            <TableCell className="text-right">
              <ScoreDisplay 
                value={entry.betting_score} 
                change={entry.betting_change}
                format="decimal"
              />
            </TableCell>
            
            <TableCell className="text-right">
              <ScoreDisplay 
                value={entry.trading_score} 
                change={entry.trading_change}
                format="decimal"
              />
            </TableCell>
            
            <TableCell className="text-right">
              <ScoreDisplay 
                value={entry.analyst_score} 
                change={entry.analyst_change}
                format="decimal"
              />
            </TableCell>
          </>
        );
      
      case 'gaming':
        return (
          <>
            <TableCell className="text-right">
              <ScoreDisplay value={entry.gaming_score} change={entry.gaming_change} />
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{Math.floor(entry.gaming_score / 10)}</span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{(Math.random() * 30 + 60).toFixed(1)}%</span>
            </TableCell>
            <TableCell className="text-right">
              <Badge variant="secondary" className="text-xs">
                {Math.floor(Math.random() * 5) + 1}
              </Badge>
            </TableCell>
          </>
        );
      
      case 'betting':
        return (
          <>
            <TableCell className="text-right">
              <ScoreDisplay value={entry.betting_score} change={entry.betting_change} />
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{(Math.random() * 20 + 65).toFixed(1)}%</span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">${(Math.random() * 50000 + 10000).toFixed(0)}</span>
            </TableCell>
            <TableCell className="text-right">
              <span className={cn(
                "font-medium",
                Math.random() > 0.5 ? "text-emerald-400" : "text-red-400"
              )}>
                {((Math.random() - 0.5) * 40).toFixed(1)}%
              </span>
            </TableCell>
          </>
        );
      
      case 'trading':
        return (
          <>
            <TableCell className="text-right">
              <ScoreDisplay value={entry.trading_score} change={entry.trading_change} />
            </TableCell>
            <TableCell className="text-right">
              <span className={cn(
                "font-medium",
                Math.random() > 0.4 ? "text-emerald-400" : "text-red-400"
              )}>
                {((Math.random() - 0.3) * 50).toFixed(1)}%
              </span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">${(Math.random() * 100000 + 25000).toFixed(0)}</span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{(Math.random() * 2 + 0.5).toFixed(2)}</span>
            </TableCell>
          </>
        );
      
      case 'analyst':
        return (
          <>
            <TableCell className="text-right">
              <ScoreDisplay value={entry.analyst_score} change={entry.analyst_change} />
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{(Math.random() * 25 + 60).toFixed(1)}%</span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">{Math.floor(Math.random() * 500 + 50)}</span>
            </TableCell>
            <TableCell className="text-right">
              <span className="font-medium text-slate-200">${(Math.random() * 5000 + 500).toFixed(0)}</span>
            </TableCell>
          </>
        );
    }
  };

  return (
    <div className="leaderboard-table-container">
      <Table>
        <TableHeader>
          <TableRow className="border-b-2 border-slate-600">
            <TableHead className="w-16 text-center text-slate-200 font-semibold">Rank</TableHead>
            <TableHead className="min-w-48 text-slate-200 font-semibold">Player</TableHead>
            {getViewSpecificHeaders(view)}
            {showTrends && (
              <TableHead className="text-center w-32 text-slate-200 font-semibold">Trend</TableHead>
            )}
            <TableHead className="w-40 text-slate-200 font-semibold">Achievements</TableHead>
          </TableRow>
        </TableHeader>
        
        <TableBody>
          {data.map((entry, index) => (
            <TableRow 
              key={entry.user_id}
              className={cn(
                "hover:bg-slate-800/30 transition-colors cursor-pointer group border-b border-slate-700/30",
                entry.user_id === currentUserId && "bg-indigo-900/20 hover:bg-indigo-800/30"
              )}
              onClick={() => onUserClick?.(entry.user_id)}
            >
              <TableCell className="text-center">
                <RankBadge 
                  rank={entry.rank} 
                  change={entry.rank_change}
                  size="sm"
                />
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10 ring-2 ring-white shadow-md">
                    <AvatarImage src={entry.avatar_url} alt={entry.username} />
                    <AvatarFallback className="bg-gradient-to-br from-indigo-400 to-purple-400 text-white font-semibold">
                      {entry.username.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="min-w-0 flex-1">
                    <p className="font-semibold text-white truncate group-hover:text-indigo-400 transition-colors">
                      {entry.username}
                      {entry.user_id === currentUserId && (
                        <Badge variant="secondary" className="ml-2 text-xs bg-indigo-600 text-white border-indigo-500">You</Badge>
                      )}
                    </p>
                    
                    {showAffiliation && entry.affiliation && (
                      <div className="flex items-center gap-1 mt-1">
                        {entry.affiliation.logo_url && (
                          <img 
                            src={entry.affiliation.logo_url} 
                            alt=""
                            className="w-4 h-4 rounded-sm"
                          />
                        )}
                        <p className="text-sm text-slate-400 font-medium truncate">
                          {entry.affiliation.name}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </TableCell>
              
              {getViewSpecificCells(entry, view)}
              
              {showTrends && entry.score_history && (
                <TableCell className="text-center">
                  <MiniTrendChart 
                    data={entry.score_history.slice(-7)}
                    height={32}
                    width={80}
                    showChange
                  />
                </TableCell>
              )}
              
              <TableCell>
                <div className="flex items-center gap-1 flex-wrap">
                  {entry.recent_achievements.slice(0, 3).map(achievement => (
                    <AchievementBadge 
                      key={achievement.id} 
                      achievement={achievement} 
                      size="xs"
                      showTooltip
                    />
                  ))}
                  {entry.recent_achievements.length > 3 && (
                    <Tooltip>
                      <TooltipTrigger>
                        <div className="text-xs text-slate-300 font-medium bg-slate-800/50 border border-slate-600 rounded px-1 py-0.5">
                          +{entry.recent_achievements.length - 3}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        {entry.recent_achievements.length - 3} more achievements
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-slate-300">
            <Loader2 className="w-5 h-5 animate-spin text-indigo-400" />
            <span className="font-medium">Loading leaderboard...</span>
          </div>
        </div>
      )}
      
      {/* Empty State */}
      {!isLoading && data.length === 0 && (
        <div className="text-center py-8 text-slate-300">
          <div className="w-12 h-12 mx-auto mb-3 text-slate-400">
            <TrophyIcon className="w-full h-full" />
          </div>
          <p className="text-lg font-medium mb-1 text-white">No rankings available</p>
          <p className="text-sm font-medium">Check back soon for updated leaderboards</p>
        </div>
      )}
    </div>
  );
};

export default LeaderboardTable;