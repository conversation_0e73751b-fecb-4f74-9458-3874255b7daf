/**
 * MiniPerformanceChart - Compact Performance Trend Visualization
 * ============================================================
 * 
 * Small chart component for showing performance trends in cards and table cells.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ScoreTrend {
  timestamp: string;
  unified_score: number;
  gaming_score: number;
  betting_score: number;
  trading_score: number;
  analyst_score: number;
}

interface MiniPerformanceChartProps {
  data: ScoreTrend[];
  height?: number;
  width?: number;
  showChange?: boolean;
  metric?: 'unified_score' | 'gaming_score' | 'betting_score' | 'trading_score' | 'analyst_score';
  color?: string;
  className?: string;
}

const MiniPerformanceChart: React.FC<MiniPerformanceChartProps> = ({
  data,
  height = 64,
  width = 200,
  showChange = false,
  metric = 'unified_score',
  color = '#6366f1', // indigo-500
  className
}) => {
  if (!data || data.length === 0) {
    return (
      <div 
        className={cn("flex items-center justify-center bg-gray-100 rounded", className)}
        style={{ height, width }}
      >
        <span className="text-xs text-gray-500">No data</span>
      </div>
    );
  }

  // Extract values for the specified metric
  const values = data.map(d => d[metric]).filter(v => v != null && !isNaN(v));
  
  if (values.length === 0) {
    return (
      <div 
        className={cn("flex items-center justify-center bg-gray-100 rounded", className)}
        style={{ height, width }}
      >
        <span className="text-xs text-gray-500">No data</span>
      </div>
    );
  }

  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const range = maxValue - minValue;
  
  // Calculate percentage change
  const firstValue = values[values.length - 1]; // Oldest (reversed array)
  const lastValue = values[0]; // Newest
  const changePercent = firstValue !== 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;
  const isPositive = changePercent >= 0;

  // Create SVG path
  const padding = 4;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);
  
  // Generate path points
  const pathData = values.map((value, index) => {
    const x = padding + (index / (values.length - 1)) * chartWidth;
    const y = padding + ((maxValue - value) / (range || 1)) * chartHeight;
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  // Generate area path for gradient fill
  const areaData = values.map((value, index) => {
    const x = padding + (index / (values.length - 1)) * chartWidth;
    const y = padding + ((maxValue - value) / (range || 1)) * chartHeight;
    return `${x} ${y}`;
  }).join(' L ');
  
  const areaPath = `M ${padding} ${height - padding} L ${areaData} L ${width - padding} ${height - padding} Z`;

  return (
    <div className={cn("relative", className)} style={{ width, height }}>
      <svg
        width={width}
        height={height}
        className="overflow-visible"
        viewBox={`0 0 ${width} ${height}`}
      >
        {/* Gradient definition */}
        <defs>
          <linearGradient id={`gradient-${metric}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.3" />
            <stop offset="100%" stopColor={color} stopOpacity="0.1" />
          </linearGradient>
        </defs>
        
        {/* Area fill */}
        <path
          d={areaPath}
          fill={`url(#gradient-${metric})`}
          className="transition-all duration-300"
        />
        
        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-all duration-300"
        />
        
        {/* Data points */}
        {values.map((value, index) => {
          const x = padding + (index / (values.length - 1)) * chartWidth;
          const y = padding + ((maxValue - value) / (range || 1)) * chartHeight;
          
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              fill={color}
              className="opacity-60 hover:opacity-100 transition-opacity"
            />
          );
        })}
      </svg>
      
      {/* Change indicator */}
      {showChange && (
        <div className={cn(
          "absolute top-1 right-1 text-xs font-medium px-1 py-0.5 rounded",
          isPositive ? "text-green-700 bg-green-100" : "text-red-700 bg-red-100"
        )}>
          {isPositive ? '+' : ''}{changePercent.toFixed(1)}%
        </div>
      )}
      
      {/* Hover tooltip area (future enhancement) */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Space for tooltip on hover */}
      </div>
    </div>
  );
};

export default MiniPerformanceChart;