/**
 * UnifiedPerformanceCard - Cross-Platform Performance Overview
 * ==========================================================
 * 
 * Displays user's unified performance across all BetBet modules with tier progression,
 * score breakdown, and recent achievements.
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  TrendingUp as TrendingUpIcon,
  MoreVertical as MoreVerticalIcon,
  Share as ShareIcon,
  Target as TargetIcon,
  Info as InfoIcon,
  ChevronRight as ChevronRightIcon,
  Gamepad2 as GamepadIcon,
  Trophy as TrophyIcon,
  BarChart3,
  Loader2,
  Star as StarIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import utility components
import TierBadge from './TierBadge';
import PerformanceMetricCard from './PerformanceMetricCard';
import AchievementBadge from './AchievementBadge';
import MiniPerformanceChart from './MiniPerformanceChart';

// Type definitions
interface UnifiedUserPerformance {
  user_id: string;
  global_ranking: {
    unified_rank: number;
    unified_score: number;
    percentile: number;
    total_users: number;
  };
  platform_tier: {
    current: PlatformTier;
    next: PlatformTier;
    progress: number; // 0.0-1.0
    points_to_next: number;
  };
  module_rankings: {
    gaming: ModuleRanking;
    betting: ModuleRanking;
    trading: ModuleRanking;
    analyst: ModuleRanking;
  };
  cross_platform_consistency: number; // 0.0-1.0
  active_modules: string[];
  performance_trends: {
    daily_scores: ScoreTrend[];
    weekly_scores: ScoreTrend[];
    monthly_scores: ScoreTrend[];
  };
  achievement_summary: {
    total_unlocked: number;
    total_available: number;
    recent_unlocks: Achievement[];
    progress_towards_next: AchievementProgress[];
  };
}

interface ModuleRanking {
  rank: number;
  score: number;
  percentile: number;
  change_24h: number;
  change_7d: number;
  best_rank_ever: number;
  best_score_ever: number;
}

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  points: number;
  icon_url?: string;
  unlocked_at?: string;
}

interface ScoreTrend {
  timestamp: string;
  unified_score: number;
  gaming_score: number;
  betting_score: number;
  trading_score: number;
  analyst_score: number;
}

interface AchievementProgress {
  achievement: Achievement;
  progress_percentage: number;
  current_values: Record<string, any>;
  missing_requirements: any[];
  estimated_completion: string;
}

type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';

interface UnifiedPerformanceCardProps {
  userPerformance: UnifiedUserPerformance;
  isLoading?: boolean;
  showDetailed?: boolean;
  onNavigateToModule?: (module: string) => void;
}

// Utility functions
const getTierGradient = (tier: PlatformTier): string => {
  const gradients = {
    Bronze: 'from-amber-100 to-orange-200',
    Silver: 'from-gray-100 to-slate-200', 
    Gold: 'from-yellow-100 to-amber-200',
    Platinum: 'from-slate-100 to-gray-200',
    Diamond: 'from-blue-100 to-indigo-200',
    Master: 'from-purple-100 to-indigo-200',
  };
  return gradients[tier] || 'from-gray-100 to-slate-200';
};

const getTierProgressColor = (tier: PlatformTier): string => {
  const colors = {
    Bronze: 'bg-amber-500',
    Silver: 'bg-gray-500',
    Gold: 'bg-yellow-500', 
    Platinum: 'bg-slate-500',
    Diamond: 'bg-blue-500',
    Master: 'bg-purple-500',
  };
  return colors[tier] || 'bg-gray-500';
};

const getModuleIcon = (module: string) => {
  const icons = {
    gaming: GamepadIcon,
    betting: TrophyIcon,
    trading: TrendingUpIcon,
    analyst: BarChart3,
  };
  return icons[module as keyof typeof icons] || StarIcon;
};

const UnifiedPerformanceCard: React.FC<UnifiedPerformanceCardProps> = ({
  userPerformance,
  isLoading = false,
  showDetailed = true,
  onNavigateToModule
}) => {
  const [trendPeriod, setTrendPeriod] = React.useState<'7D' | '30D' | '90D'>('7D');

  return (
    <Card className="unified-performance-card relative overflow-hidden bg-slate-900/50 border-slate-700 shadow-lg">
      {/* Background Gradient based on tier */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-br opacity-10",
        getTierGradient(userPerformance.platform_tier.current)
      )} />
      
      <CardHeader className="relative z-10">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-1 drop-shadow-sm">
              Platform Mastery
            </h2>
            <p className="text-slate-300 font-medium">
              Your unified performance across all BetBet activities
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-right">
              <TierBadge 
                tier={userPerformance.platform_tier.current} 
                size="lg"
                progress={userPerformance.platform_tier.progress}
              />
              <p className="text-sm text-slate-300 font-medium mt-1">
                Global Rank #{userPerformance.global_ranking.unified_rank.toLocaleString()}
              </p>
              <p className="text-xs text-slate-400 font-medium">
                Top {userPerformance.global_ranking.percentile.toFixed(1)}%
              </p>
            </div>
            
            {/* Quick Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVerticalIcon className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => window.open('/profile/performance', '_blank')}>
                  <TrendingUpIcon className="w-4 h-4 mr-2" />
                  Detailed Analytics
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <ShareIcon className="w-4 h-4 mr-2" />
                  Share Performance
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <TargetIcon className="w-4 h-4 mr-2" />
                  Set Goals
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="relative z-10 space-y-6">
        {/* Main Score Display */}
        <div className="text-center py-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <div className="text-4xl md:text-5xl font-bold text-indigo-400 drop-shadow-sm">
              {userPerformance.global_ranking.unified_score.toFixed(1)}
            </div>
            <div className="text-sm text-slate-300 font-medium">
              / 10000
            </div>
          </div>
          
          {/* Tier Progress Bar */}
          <div className="max-w-md mx-auto">
            <div className="flex items-center justify-between text-sm text-slate-300 font-medium mb-2">
              <span>{userPerformance.platform_tier.current}</span>
              <span>{userPerformance.platform_tier.next}</span>
            </div>
            <Progress 
              value={userPerformance.platform_tier.progress * 100} 
              className="h-3 bg-gray-200"
              // @ts-ignore
              indicatorClassName={getTierProgressColor(userPerformance.platform_tier.current)}
            />
            <p className="text-xs text-slate-300 font-medium mt-1 text-center">
              {userPerformance.platform_tier.points_to_next} points to {userPerformance.platform_tier.next}
            </p>
          </div>
        </div>
        
        {/* Module Performance Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(userPerformance.module_rankings).map(([module, ranking]) => {
            const Icon = getModuleIcon(module);
            return (
              <div
                key={module}
                onClick={() => onNavigateToModule?.(module)}
                className="cursor-pointer hover:bg-slate-800/50 transition-colors rounded-lg p-3 border border-slate-600 bg-slate-800/20"
              >
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Icon className="w-5 h-5 text-slate-300" />
                  </div>
                  <p className="text-xs text-slate-400 font-medium capitalize mb-1">{module}</p>
                  <p className="text-lg font-bold text-white">{ranking.score.toFixed(0)}</p>
                  <div className={cn(
                    "text-xs flex items-center justify-center gap-1",
                    ranking.change_24h >= 0 ? "text-emerald-400" : "text-red-400"
                  )}>
                    <TrendingUpIcon className={cn(
                      "w-3 h-3",
                      ranking.change_24h < 0 && "rotate-180"
                    )} />
                    {Math.abs(ranking.change_24h)}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {showDetailed && (
          <>
            {/* Cross-Platform Consistency */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-white">Cross-Platform Consistency</h4>
                <Tooltip>
                  <TooltipTrigger>
                    <InfoIcon className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    How consistently you perform across all platform modules
                  </TooltipContent>
                </Tooltip>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Progress 
                    value={userPerformance.cross_platform_consistency * 100} 
                    className="h-2"
                  />
                </div>
                <div className="text-sm font-medium text-slate-200">
                  {(userPerformance.cross_platform_consistency * 100).toFixed(0)}%
                </div>
              </div>
              
              <div className="flex flex-wrap gap-1 mt-2">
                {userPerformance.active_modules.map(module => (
                  <Badge key={module} variant="secondary" className="text-xs capitalize">
                    {module}
                  </Badge>
                ))}
              </div>
            </div>
            
            {/* Recent Achievements Preview */}
            {userPerformance.achievement_summary.recent_unlocks.length > 0 && (
              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-white">Recent Achievements</h4>
                  <Button variant="ghost" size="sm" onClick={() => {}}>
                    View All
                    <ChevronRightIcon className="w-4 h-4 ml-1" />
                  </Button>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {userPerformance.achievement_summary.recent_unlocks.slice(0, 4).map(achievement => (
                    <AchievementBadge 
                      key={achievement.id} 
                      achievement={achievement} 
                      size="sm"
                      showTooltip 
                    />
                  ))}
                  {userPerformance.achievement_summary.recent_unlocks.length > 4 && (
                    <div className="flex items-center text-sm text-slate-300 font-medium px-2 py-1 bg-slate-800/50 border border-slate-600 rounded">
                      +{userPerformance.achievement_summary.recent_unlocks.length - 4} more
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Performance Trend Chart */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-white">Performance Trend</h4>
                <div className="flex gap-1">
                  {(['7D', '30D', '90D'] as const).map(period => (
                    <Button 
                      key={period}
                      variant={trendPeriod === period ? "default" : "ghost"} 
                      size="sm" 
                      onClick={() => setTrendPeriod(period)}
                    >
                      {period}
                    </Button>
                  ))}
                </div>
              </div>
              
              <div className="h-24">
                <MiniPerformanceChart 
                  data={userPerformance.performance_trends.daily_scores.slice(-7)}
                  height={96}
                />
              </div>
            </div>
          </>
        )}
      </CardContent>
      
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-slate-900/50 backdrop-blur-sm flex items-center justify-center z-20">
          <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
        </div>
      )}
    </Card>
  );
};

export default UnifiedPerformanceCard;