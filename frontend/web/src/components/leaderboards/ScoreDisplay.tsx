/**
 * ScoreDisplay - Score with Change Indicator
 * ==========================================
 * 
 * Displays numeric scores with visual change indicators (up/down arrows and colors).
 */

'use client';

import React from 'react';
import { TrendingUpIcon, TrendingDownIcon, MinusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScoreDisplayProps {
  value: number;
  change?: number;
  format?: 'integer' | 'decimal' | 'currency' | 'percentage';
  size?: 'sm' | 'md' | 'lg';
  showChange?: boolean;
  className?: string;
}

const formatValue = (value: number, format: 'integer' | 'decimal' | 'currency' | 'percentage') => {
  switch (format) {
    case 'integer':
      return value.toFixed(0);
    case 'decimal':
      return value.toFixed(1);
    case 'currency':
      return `$${value.toLocaleString()}`;
    case 'percentage':
      return `${value.toFixed(1)}%`;
    default:
      return value.toFixed(1);
  }
};

const getChangeIndicator = (change?: number) => {
  if (change === undefined || change === 0) {
    return { 
      icon: MinusIcon, 
      color: 'text-slate-400', 
      bgColor: 'bg-slate-800/50',
      text: '–' 
    };
  }
  if (change > 0) {
    return { 
      icon: TrendingUpIcon, 
      color: 'text-emerald-400', 
      bgColor: 'bg-emerald-900/20',
      text: `+${Math.abs(change)}` 
    };
  }
  return { 
    icon: TrendingDownIcon, 
    color: 'text-red-400', 
    bgColor: 'bg-red-900/20',
    text: `${change}` 
  };
};

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  const sizes = {
    sm: {
      value: 'text-sm font-medium',
      change: 'text-xs',
      icon: 'w-3 h-3',
      container: 'gap-1'
    },
    md: {
      value: 'text-base font-semibold',
      change: 'text-xs',
      icon: 'w-3 h-3',
      container: 'gap-1'
    },
    lg: {
      value: 'text-lg font-bold',
      change: 'text-sm',
      icon: 'w-4 h-4',
      container: 'gap-2'
    }
  };
  return sizes[size];
};

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  value,
  change,
  format = 'decimal',
  size = 'md',
  showChange = true,
  className
}) => {
  const changeIndicator = getChangeIndicator(change);
  const sizeClasses = getSizeClasses(size);
  const formattedValue = formatValue(value, format);

  return (
    <div className={cn("flex flex-col items-end", sizeClasses.container, className)}>
      {/* Main Score Value */}
      <div className={cn("text-white", sizeClasses.value)}>
        {formattedValue}
      </div>
      
      {/* Change Indicator */}
      {showChange && (
        <div className={cn(
          "flex items-center gap-0.5 px-1 py-0.5 rounded-sm",
          changeIndicator.color,
          changeIndicator.bgColor,
          sizeClasses.change
        )}>
          <changeIndicator.icon className={sizeClasses.icon} />
          {change !== 0 && change !== undefined && (
            <span className="font-medium">
              {Math.abs(change)}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default ScoreDisplay;