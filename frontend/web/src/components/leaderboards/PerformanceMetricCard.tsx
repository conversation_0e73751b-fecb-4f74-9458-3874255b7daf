/**
 * PerformanceMetricCard - Individual Module Performance Display
 * ===========================================================
 * 
 * Displays performance metrics for individual modules (gaming, betting, trading, analyst).
 */

'use client';

import React from 'react';
import { TrendingUpIcon, TrendingDownIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModuleRanking {
  rank: number;
  score: number;
  percentile: number;
  change_24h: number;
  change_7d: number;
  best_rank_ever: number;
  best_score_ever: number;
}

interface PerformanceMetricCardProps {
  module: string;
  ranking: ModuleRanking;
  onClick?: () => void;
  className?: string;
}

const PerformanceMetricCard: React.FC<PerformanceMetricCardProps> = ({
  module,
  ranking,
  onClick,
  className
}) => {
  const isPositiveChange = ranking.change_24h >= 0;
  
  return (
    <div 
      className={cn(
        "p-3 rounded-lg border border-gray-100 transition-all duration-200",
        onClick && "cursor-pointer hover:bg-gray-50 hover:border-gray-200 hover:shadow-sm",
        className
      )}
      onClick={onClick}
    >
      <div className="text-center space-y-2">
        {/* Module Name */}
        <p className="text-xs text-gray-500 capitalize font-medium">
          {module}
        </p>
        
        {/* Score */}
        <div className="text-lg font-bold text-gray-900">
          {ranking.score.toFixed(0)}
        </div>
        
        {/* Rank and Percentile */}
        <div className="text-xs text-gray-600">
          Rank #{ranking.rank.toLocaleString()}
        </div>
        
        {/* Change Indicator */}
        <div className={cn(
          "flex items-center justify-center gap-1 text-xs font-medium",
          isPositiveChange ? "text-green-600" : "text-red-600"
        )}>
          {isPositiveChange ? (
            <TrendingUpIcon className="w-3 h-3" />
          ) : (
            <TrendingDownIcon className="w-3 h-3" />
          )}
          <span>
            {Math.abs(ranking.change_24h)}
          </span>
        </div>
        
        {/* Percentile */}
        <div className="text-xs text-gray-500">
          Top {(100 - ranking.percentile).toFixed(0)}%
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetricCard;