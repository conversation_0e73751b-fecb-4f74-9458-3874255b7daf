/**
 * RankBadge - Rank Display with Change Indicator
 * =============================================
 * 
 * Displays user rank with optional rank change indicators and special styling for top ranks.
 */

'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Crown, 
  Medal, 
  Trophy,
  ChevronUp, 
  ChevronDown, 
  Minus 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface RankBadgeProps {
  rank: number;
  change?: number; // positive = rank improved (went down in number), negative = rank worsened
  size?: 'sm' | 'md' | 'lg';
  showChange?: boolean;
  className?: string;
}

const getRankConfig = (rank: number) => {
  switch (rank) {
    case 1:
      return { 
        icon: Crown, 
        color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        gradient: 'from-yellow-400 to-amber-500',
        label: '1st' 
      };
    case 2:
      return { 
        icon: Medal, 
        color: 'bg-slate-600/20 text-slate-300 border-slate-500/30',
        gradient: 'from-gray-400 to-slate-500',
        label: '2nd' 
      };
    case 3:
      return { 
        icon: Trophy, 
        color: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
        gradient: 'from-orange-400 to-amber-500',
        label: '3rd' 
      };
    default:
      return null;
  }
};

const getChangeIndicator = (change?: number) => {
  if (!change || change === 0) {
    return { icon: Minus, color: 'text-slate-400', text: '–' };
  }
  if (change > 0) {
    return { icon: ChevronUp, color: 'text-emerald-400', text: `+${change}` };
  }
  return { icon: ChevronDown, color: 'text-red-400', text: `${change}` };
};

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  const sizes = {
    sm: {
      container: 'w-12 h-8',
      badge: 'text-xs px-2 py-1',
      icon: 'w-3 h-3',
      text: 'text-xs',
      rank: 'text-sm'
    },
    md: {
      container: 'w-16 h-10',
      badge: 'text-sm px-3 py-1.5',
      icon: 'w-4 h-4',
      text: 'text-sm',
      rank: 'text-base'
    },
    lg: {
      container: 'w-20 h-12',
      badge: 'text-base px-4 py-2',
      icon: 'w-5 h-5',
      text: 'text-base',
      rank: 'text-lg'
    }
  };
  return sizes[size];
};

const RankBadge: React.FC<RankBadgeProps> = ({ 
  rank, 
  change, 
  size = 'md', 
  showChange = true,
  className 
}) => {
  const rankConfig = getRankConfig(rank);
  const changeIndicator = getChangeIndicator(change);
  const sizeClasses = getSizeClasses(size);

  return (
    <div className={cn("flex flex-col items-center gap-1", className)}>
      {/* Rank Display */}
      <div className={cn("flex items-center justify-center", sizeClasses.container)}>
        {rankConfig ? (
          <Badge 
            variant="secondary" 
            className={cn(
              "font-bold border flex items-center gap-1.5 shadow-sm",
              rankConfig.color,
              sizeClasses.badge
            )}
          >
            <rankConfig.icon className={sizeClasses.icon} />
            <span>{rankConfig.label}</span>
          </Badge>
        ) : (
          <div className={cn(
            "font-bold text-white text-center",
            sizeClasses.rank
          )}>
            #{rank}
          </div>
        )}
      </div>
      
      {/* Change Indicator */}
      {showChange && change !== undefined && (
        <div className={cn(
          "flex items-center gap-0.5 font-medium",
          changeIndicator.color,
          sizeClasses.text
        )}>
          <changeIndicator.icon className="w-3 h-3" />
          {change !== 0 && (
            <span className="text-xs">
              {Math.abs(change)}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default RankBadge;