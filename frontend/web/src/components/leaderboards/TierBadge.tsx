/**
 * TierBadge - Platform Tier Display Component
 * ==========================================
 * 
 * Displays user's platform tier with appropriate styling and optional progress.
 */

'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Crown, 
  Medal, 
  Shield, 
  Star, 
  Diamond, 
  Gem 
} from 'lucide-react';
import { cn } from '@/lib/utils';

type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';

interface TierBadgeProps {
  tier: PlatformTier;
  size?: 'sm' | 'md' | 'lg';
  progress?: number; // 0.0-1.0, optional progress to next tier
  showProgress?: boolean;
  className?: string;
}

const getTierConfig = (tier: PlatformTier) => {
  const configs = {
    Bronze: {
      icon: Medal,
      color: 'bg-amber-100 text-amber-800 border-amber-200',
      gradient: 'from-amber-400 to-orange-500',
      textColor: 'text-amber-700'
    },
    Silver: {
      icon: Medal,
      color: 'bg-gray-100 text-gray-800 border-gray-300',
      gradient: 'from-gray-400 to-slate-500',
      textColor: 'text-gray-700'
    },
    Gold: {
      icon: Crown,
      color: 'bg-yellow-100 text-yellow-800 border-yellow-300',
      gradient: 'from-yellow-400 to-amber-500',
      textColor: 'text-yellow-700'
    },
    Platinum: {
      icon: Shield,
      color: 'bg-slate-100 text-slate-800 border-slate-300',
      gradient: 'from-slate-400 to-gray-600',
      textColor: 'text-slate-700'
    },
    Diamond: {
      icon: Diamond,
      color: 'bg-blue-100 text-blue-800 border-blue-300',
      gradient: 'from-blue-400 to-indigo-500',
      textColor: 'text-blue-700'
    },
    Master: {
      icon: Gem,
      color: 'bg-purple-100 text-purple-800 border-purple-300',
      gradient: 'from-purple-400 to-indigo-500',
      textColor: 'text-purple-700'
    }
  };
  return configs[tier];
};

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  const sizes = {
    sm: {
      badge: 'text-xs px-2 py-1',
      icon: 'w-3 h-3',
      text: 'text-xs',
      progress: 'h-1'
    },
    md: {
      badge: 'text-sm px-3 py-1.5',
      icon: 'w-4 h-4',
      text: 'text-sm',
      progress: 'h-2'
    },
    lg: {
      badge: 'text-base px-4 py-2',
      icon: 'w-5 h-5',
      text: 'text-base',
      progress: 'h-2.5'
    }
  };
  return sizes[size];
};

const TierBadge: React.FC<TierBadgeProps> = ({ 
  tier, 
  size = 'md', 
  progress, 
  showProgress = false,
  className 
}) => {
  const config = getTierConfig(tier);
  const sizeClasses = getSizeClasses(size);
  const Icon = config.icon;

  return (
    <div className={cn("flex flex-col items-center gap-1", className)}>
      <Badge 
        variant="secondary" 
        className={cn(
          "font-semibold border flex items-center gap-1.5",
          config.color,
          sizeClasses.badge
        )}
      >
        <Icon className={sizeClasses.icon} />
        <span>{tier}</span>
      </Badge>
      
      {(showProgress || progress !== undefined) && progress !== undefined && (
        <div className="w-full max-w-24">
          <Progress 
            value={progress * 100} 
            className={cn("bg-gray-200", sizeClasses.progress)}
          />
          <p className={cn("text-center text-gray-500 mt-0.5", sizeClasses.text)}>
            {(progress * 100).toFixed(0)}%
          </p>
        </div>
      )}
    </div>
  );
};

export default TierBadge;