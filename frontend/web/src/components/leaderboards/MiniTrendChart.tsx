/**
 * MiniTrendChart - Compact Trend Visualization for Table Cells
 * ===========================================================
 * 
 * Ultra-compact chart component for showing trends in table cells.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ScoreHistory {
  timestamp: string;
  score: number;
}

interface MiniTrendChartProps {
  data: ScoreHistory[];
  height?: number;
  width?: number;
  showChange?: boolean;
  color?: string;
  className?: string;
}

const MiniTrendChart: React.FC<MiniTrendChartProps> = ({
  data,
  height = 32,
  width = 80,
  showChange = false,
  color = '#6366f1', // indigo-500
  className
}) => {
  if (!data || data.length === 0) {
    return (
      <div 
        className={cn("flex items-center justify-center bg-gray-100 rounded", className)}
        style={{ height, width }}
      >
        <span className="text-xs text-gray-400">–</span>
      </div>
    );
  }

  // Extract values
  const values = data.map(d => d.score).filter(v => v != null && !isNaN(v));
  
  if (values.length === 0) {
    return (
      <div 
        className={cn("flex items-center justify-center bg-gray-100 rounded", className)}
        style={{ height, width }}
      >
        <span className="text-xs text-gray-400">–</span>
      </div>
    );
  }

  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const range = maxValue - minValue;
  
  // Calculate percentage change
  const firstValue = values[values.length - 1]; // Oldest (reversed array)
  const lastValue = values[0]; // Newest
  const changePercent = firstValue !== 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;
  const isPositive = changePercent >= 0;

  // Create path
  const padding = 2;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);
  
  // Generate path points
  const pathData = values.map((value, index) => {
    const x = padding + (index / (values.length - 1)) * chartWidth;
    const y = padding + ((maxValue - value) / (range || 1)) * chartHeight;
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  return (
    <div className={cn("relative inline-block", className)} style={{ width, height }}>
      <svg
        width={width}
        height={height}
        className="overflow-visible"
        viewBox={`0 0 ${width} ${height}`}
      >        
        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={isPositive ? '#10b981' : '#ef4444'} // green-500 or red-500
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-all duration-300"
        />
        
        {/* End point */}
        {values.length > 0 && (
          <circle
            cx={padding + ((values.length - 1) / (values.length - 1)) * chartWidth}
            cy={padding + ((maxValue - values[0]) / (range || 1)) * chartHeight}
            r="1.5"
            fill={isPositive ? '#10b981' : '#ef4444'}
            className="opacity-80"
          />
        )}
      </svg>
      
      {/* Change indicator overlay */}
      {showChange && Math.abs(changePercent) > 0.1 && (
        <div className={cn(
          "absolute -top-1 -right-1 text-xs font-bold px-1 py-0.5 rounded",
          isPositive 
            ? "text-green-700 bg-green-100" 
            : "text-red-700 bg-red-100"
        )}>
          {isPositive ? '+' : ''}{changePercent.toFixed(0)}%
        </div>
      )}
    </div>
  );
};

export default MiniTrendChart;