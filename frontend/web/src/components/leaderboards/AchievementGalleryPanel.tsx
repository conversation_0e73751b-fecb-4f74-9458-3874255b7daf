/**
 * AchievementGalleryPanel - Cross-Platform Achievement Gallery
 * ==========================================================
 * 
 * Displays achievement gallery with filtering, progress tracking, and unlock animations.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Star, 
  Crown, 
  Trophy, 
  Users,
  Target,
  Loader2,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import utility components
import AchievementBadge from './AchievementBadge';

// Types
type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
type AchievementCategory = 'all' | 'multi_module' | 'specialist' | 'social_leadership' | 'consistency';

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  points: number;
  icon_url?: string;
  unlocked_at?: string;
  modules_required: string[];
}

interface AchievementProgress {
  achievement: Achievement;
  progress_percentage: number;
  current_values: Record<string, any>;
  missing_requirements: string[];
  estimated_completion: string;
}

const AchievementGalleryPanel: React.FC = () => {
  const [filter, setFilter] = useState<AchievementCategory>('all');
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [progressList, setProgressList] = useState<AchievementProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);

  // Mock data generation
  useEffect(() => {
    const generateMockAchievements = (): Achievement[] => {
      const mockAchievements: Achievement[] = [
        {
          id: 'ach1',
          achievement_code: 'TRIPLE_THREAT',
          name: 'Triple Threat',
          description: 'Excel in gaming, betting, and trading simultaneously',
          category: 'multi_module',
          tier: 'Gold',
          rarity: 'Epic',
          points: 500,
          modules_required: ['gaming', 'betting', 'trading'],
          unlocked_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'ach2',
          achievement_code: 'PLATFORM_DOMINATOR',
          name: 'Platform Dominator',
          description: 'Top 10% in at least 4 different modules',
          category: 'multi_module',
          tier: 'Platinum',
          rarity: 'Legendary',
          points: 1000,
          modules_required: ['gaming', 'betting', 'trading', 'analyst'],
          unlocked_at: undefined // Not unlocked yet
        },
        {
          id: 'ach3',
          achievement_code: 'RENAISSANCE_PLAYER',
          name: 'Renaissance Player',
          description: 'Active and successful across all 6 modules',
          category: 'multi_module',
          tier: 'Diamond',
          rarity: 'Legendary',
          points: 2000,
          modules_required: ['gaming', 'betting', 'analyst', 'sports', 'trading', 'leaderboards'],
          unlocked_at: undefined
        },
        {
          id: 'ach4',
          achievement_code: 'CHESS_BETTING_MASTER',
          name: 'Chess Betting Master',
          description: 'High chess rating + high betting accuracy on chess games',
          category: 'specialist',
          tier: 'Silver',
          rarity: 'Rare',
          points: 300,
          modules_required: ['gaming', 'betting'],
          unlocked_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'ach5',
          achievement_code: 'ANALYST_TRADER_COMBO',
          name: 'Analyst Trader Combo',
          description: 'Successful analyst + profitable trader',
          category: 'specialist',
          tier: 'Gold',
          rarity: 'Epic',
          points: 750,
          modules_required: ['analyst', 'trading'],
          unlocked_at: undefined
        },
        {
          id: 'ach6',
          achievement_code: 'AFFILIATION_AMBASSADOR',
          name: 'Affiliation Ambassador',
          description: 'Top performer representing multiple affiliations',
          category: 'social_leadership',
          tier: 'Silver',
          rarity: 'Rare',
          points: 400,
          modules_required: ['gaming', 'betting'],
          unlocked_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'ach7',
          achievement_code: 'MONTHLY_CONSISTENCY',
          name: 'Monthly Consistency',
          description: 'Top performer across modules for 30 consecutive days',
          category: 'consistency',
          tier: 'Gold',
          rarity: 'Epic',
          points: 600,
          modules_required: ['gaming', 'betting', 'trading'],
          unlocked_at: undefined
        },
        {
          id: 'ach8',
          achievement_code: 'COMMUNITY_BUILDER',
          name: 'Community Builder',
          description: 'High engagement across all social features',
          category: 'social_leadership',
          tier: 'Bronze',
          rarity: 'Common',
          points: 200,
          modules_required: ['gaming'],
          unlocked_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      return mockAchievements;
    };

    const generateMockProgress = (achievements: Achievement[]): AchievementProgress[] => {
      return achievements
        .filter(ach => !ach.unlocked_at)
        .slice(0, 3)
        .map(achievement => ({
          achievement,
          progress_percentage: Math.random() * 0.8 + 0.1, // 10-90%
          current_values: {
            gaming_score: 7234,
            betting_accuracy: 0.67,
            modules_active: 3
          },
          missing_requirements: ['Increase trading volume by $5000', 'Maintain consistency for 5 more days'],
          estimated_completion: ['days', 'weeks', 'months'][Math.floor(Math.random() * 3)]
        }));
    };

    // Simulate API call
    setTimeout(() => {
      const mockAchievements = generateMockAchievements();
      const mockProgress = generateMockProgress(mockAchievements);
      
      setAchievements(mockAchievements);
      setProgressList(mockProgress);
      setLoading(false);
    }, 800);
  }, []);

  const filteredAchievements = achievements.filter(achievement => {
    if (filter === 'all') return true;
    return achievement.category === filter;
  });

  const unlockedAchievements = achievements.filter(ach => ach.unlocked_at);
  const totalAchievements = achievements.length;

  const getCategoryIcon = (category: AchievementCategory) => {
    const icons = {
      all: Star,
      multi_module: Crown,
      specialist: Target,
      social_leadership: Users,
      consistency: Trophy
    };
    return icons[category];
  };

  const getCategoryLabel = (category: AchievementCategory) => {
    const labels = {
      all: 'All',
      multi_module: 'Cross-Platform',
      specialist: 'Specialist',
      social_leadership: 'Social',
      consistency: 'Consistency'
    };
    return labels[category];
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-white font-bold">
              <Star className="w-5 h-5 text-slate-300" />
              Achievement Gallery
            </CardTitle>
            <CardDescription className="text-slate-300 font-medium">
              Unlock achievements across all platform activities
            </CardDescription>
          </div>
          
          <Button variant="outline" size="sm" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white font-semibold">
            <Filter className="w-4 h-4 mr-2" />
            View All
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Achievement Progress Overview */}
        <div className="bg-gradient-to-r from-indigo-900/20 to-purple-900/20 rounded-lg p-4 border border-indigo-500/30">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-white">Your Progress</h4>
            <span className="text-sm text-slate-300 font-medium">
              {unlockedAchievements.length} / {totalAchievements}
            </span>
          </div>
          
          <Progress 
            value={(unlockedAchievements.length / totalAchievements) * 100} 
            className="h-3 mb-2"
          />
          
          <div className="flex justify-between text-sm text-slate-300 font-medium">
            <span>{((unlockedAchievements.length / totalAchievements) * 100).toFixed(0)}% Complete</span>
            <span>{unlockedAchievements.reduce((sum, ach) => sum + ach.points, 0)} Points Earned</span>
          </div>
        </div>

        {/* Category Filter */}
        <Tabs value={filter} onValueChange={(value) => setFilter(value as AchievementCategory)}>
          <TabsList className="grid grid-cols-5 w-full bg-slate-800/50 border border-slate-600">
            {(['all', 'multi_module', 'specialist', 'social_leadership', 'consistency'] as const).map(category => {
              const Icon = getCategoryIcon(category);
              return (
                <TabsTrigger 
                  key={category} 
                  value={category} 
                  className="text-xs flex items-center gap-1 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold"
                >
                  <Icon className="w-3 h-3" />
                  <span className="hidden sm:inline">{getCategoryLabel(category)}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>

        {/* Achievement Grid */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
            <span className="ml-2 text-slate-300 font-medium">Loading achievements...</span>
          </div>
        ) : (
          <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
            {filteredAchievements.map(achievement => (
              <div key={achievement.id} className="flex flex-col items-center">
                <AchievementBadge
                  achievement={achievement}
                  size="md"
                  showTooltip
                  showPoints
                  onClick={() => setSelectedAchievement(achievement)}
                />
                
                {/* Achievement Name */}
                <p className="text-xs text-center text-slate-300 font-medium mt-2 line-clamp-2 max-w-20">
                  {achievement.name}
                </p>
              </div>
            ))}
          </div>
        )}

        {/* In-Progress Achievements */}
        {progressList.length > 0 && (
          <div className="border-t pt-6">
            <h4 className="font-semibold text-white mb-4">Close to Unlocking</h4>
            
            <div className="space-y-3">
              {progressList.map(progress => (
                <div key={progress.achievement.id} className="flex items-center gap-3 p-3 bg-slate-800/30 border border-slate-600/30 rounded-lg">
                  <AchievementBadge 
                    achievement={progress.achievement} 
                    size="sm"
                    showTooltip
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-white truncate">
                        {progress.achievement.name}
                      </p>
                      <span className="text-xs text-slate-300 font-medium">
                        {(progress.progress_percentage * 100).toFixed(0)}%
                      </span>
                    </div>
                    
                    <Progress 
                      value={progress.progress_percentage * 100} 
                      className="h-2 mb-1"
                    />
                    
                    <p className="text-xs text-slate-400 font-medium">
                      Est. completion: {progress.estimated_completion}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Rare Achievement Showcase */}
        {unlockedAchievements.filter(ach => ach.rarity === 'Legendary' || ach.rarity === 'Epic').length > 0 && (
          <div className="border-t pt-6">
            <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Crown className="w-4 h-4 text-yellow-500" />
              Rare Achievements
            </h4>
            
            <div className="flex flex-wrap gap-3">
              {unlockedAchievements
                .filter(ach => ach.rarity === 'Legendary' || ach.rarity === 'Epic')
                .map(achievement => (
                  <div key={achievement.id} className="flex items-center gap-2 p-2 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-200">
                    <AchievementBadge 
                      achievement={achievement} 
                      size="sm"
                      showTooltip
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{achievement.name}</p>
                      <p className="text-xs text-gray-600">
                        {achievement.points} pts • {achievement.rarity}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AchievementGalleryPanel;