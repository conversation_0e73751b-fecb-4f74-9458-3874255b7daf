/**
 * Mock Game Store for Testing
 * ===========================
 */

export const mockStore = {
  // Auth state
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  
  // Gaming state
  currentSession: null,
  currentGameState: null,
  participants: [],
  isConnected: false,
  connectionError: null,
  isChatOpen: false,
  isSpectatorMode: false,
  selectedGame: null,
  games: [],
  sessions: [],
  featuredGames: [],
  gamesLoading: false,
  sessionsLoading: false,
  sessionLoading: false,
  
  // Actions (mocked)
  login: jest.fn(),
  logout: jest.fn(),
  updateUser: jest.fn(),
  setLoading: jest.fn(),
  setCurrentSession: jest.fn(),
  setCurrentGameState: jest.fn(),
  setParticipants: jest.fn(),
  updateParticipant: jest.fn(),
  setConnected: jest.fn(),
  setConnectionError: jest.fn(),
  setChatOpen: jest.fn(),
  setSpectatorMode: jest.fn(),
  setSelectedGame: jest.fn(),
  setGames: jest.fn(),
  setSessions: jest.fn(),
  setFeaturedGames: jest.fn(),
  addSession: jest.fn(),
  updateSession: jest.fn(),
  setGamesLoading: jest.fn(),
  setSessionsLoading: jest.fn(),
  setSessionLoading: jest.fn(),
  reset: jest.fn(),
  resetGameState: jest.fn(),
};

export const useGameStore = jest.fn(() => mockStore);

export const useAuth = jest.fn(() => ({
  user: mockStore.user,
  token: mockStore.token,
  isAuthenticated: mockStore.isAuthenticated,
  isLoading: mockStore.isLoading,
  login: mockStore.login,
  logout: mockStore.logout,
  updateUser: mockStore.updateUser,
  setLoading: mockStore.setLoading,
}));

export const useGames = jest.fn(() => ({
  games: mockStore.games,
  sessions: mockStore.sessions,
  featuredGames: mockStore.featuredGames,
  gamesLoading: mockStore.gamesLoading,
  sessionsLoading: mockStore.sessionsLoading,
  setGames: mockStore.setGames,
  setSessions: mockStore.setSessions,
  setFeaturedGames: mockStore.setFeaturedGames,
  setGamesLoading: mockStore.setGamesLoading,
  setSessionsLoading: mockStore.setSessionsLoading,
}));

export const useCurrentSession = jest.fn(() => ({
  session: mockStore.currentSession,
  gameState: mockStore.currentGameState,
  participants: mockStore.participants,
  isConnected: mockStore.isConnected,
  connectionError: mockStore.connectionError,
  isSpectatorMode: mockStore.isSpectatorMode,
  setCurrentSession: mockStore.setCurrentSession,
  setCurrentGameState: mockStore.setCurrentGameState,
  setParticipants: mockStore.setParticipants,
  setConnected: mockStore.setConnected,
  setConnectionError: mockStore.setConnectionError,
  setSpectatorMode: mockStore.setSpectatorMode,
}));