/**
 * Mock Navigation Component for Testing
 * =====================================
 */

import React from 'react';

interface NavigationProps {
  onMobileMenuClick?: () => void;
}

export default function Navigation({ onMobileMenuClick }: NavigationProps) {
  return (
    <nav>
      <div>
        <span>BetBet</span>
        <span>Gaming Engine</span>
      </div>
      <div>
        <a href="/games">Games</a>
        <a href="/sessions">Sessions</a>
        <a href="/tournaments">Tournaments</a>
        <a href="/leaderboard">Leaderboard</a>
      </div>
      <div>
        <input placeholder="Search games, players..." />
      </div>
      <div>
        <a href="/auth/signin">Sign In</a>
        <a href="/auth/signup">Sign Up</a>
        <button>Create Session</button>
        <button onClick={onMobileMenuClick}>Menu</button>
        <div>
          <span>Connected</span>
          <span>Disconnected</span>
        </div>
        <div>
          <button>User Menu</button>
          <div role="menuitem">Log Out</div>
          <span>3</span>
        </div>
      </div>
    </nav>
  );
}