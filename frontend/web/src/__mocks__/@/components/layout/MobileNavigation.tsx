/**
 * Mock Mobile Navigation Component for Testing
 * ============================================
 */

import React from 'react';

export default function MobileNavigation() {
  return (
    <nav className="mobile-nav sm:hidden">
      <a href="/" aria-label="Home" className="touch-target">Home</a>
      <a href="/games" aria-label="Games" className="touch-target">Games</a>
      <a href="/sessions" aria-label="Sessions" className="touch-target">Sessions</a>
      <a href="/tournaments" aria-label="Tournaments" className="touch-target">Tournaments</a>
      <a href="/leaderboard" aria-label="Leaderboard" className="touch-target">Leaderboard</a>
      <a href="/profile" aria-label="Profile" className="touch-target">Profile</a>
    </nav>
  );
}