'use client';

import React from 'react';
import { useUser } from '@clerk/nextjs';
import WalletDashboard from '@/components/wallet/WalletDashboard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Wallet, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';

export default function WalletPage() {
  const { isLoaded, isSignedIn, user } = useUser();

  if (!isLoaded) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AuthenticatedLayout>
    );
  }

  if (!isSignedIn) {
    return (
      <AuthenticatedLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <Wallet className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <CardTitle>Sign In Required</CardTitle>
              <CardDescription>
                You need to sign in to access your wallet
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Link href="/sign-in">
                <Button>Sign In</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href="/dashboard">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2 text-white drop-shadow-sm">
              <Wallet className="h-8 w-8 text-blue-400" />
              My Wallet
            </h1>
            <p className="text-slate-300 font-medium">
              Manage your account balance and transactions
            </p>
          </div>
        </div>
      </div>

      {/* Welcome Message - Improved Contrast */}
      <Card className="mb-6 bg-slate-900/50 border-slate-700 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-white">
                Welcome back, {user.firstName || user.username}!
              </h2>
              <p className="text-slate-300 font-medium">
                Your wallet is ready for gaming, betting, and more.
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-slate-300 font-medium">Account Status</div>
              <div className="text-lg font-bold text-emerald-400">Active</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Wallet Dashboard */}
      <WalletDashboard />

      {/* Security Notice - Improved Contrast */}
      <Card className="mt-6 bg-slate-900/50 border-slate-700 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-600/20 border border-blue-500/30 rounded-lg">
              <Wallet className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-white">Secure & Protected</h3>
              <p className="text-sm text-slate-300 font-medium leading-relaxed">
                Your funds are protected with bank-level security. All transactions are encrypted 
                and we never store your payment information.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </AuthenticatedLayout>
  );
}