/**
 * Expert Discovery & Marketplace Page
 * ===================================
 * 
 * Main marketplace page for discovering and evaluating expert analysts.
 * Features expert search, filtering, leaderboards, and performance analytics.
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  Star,
  Users,
  Trophy,
  Target,
  DollarSign,
  BarChart3,
  Eye,
  ChevronRight
} from 'lucide-react';
import { useMultiServiceApi } from '@/hooks/useApi';
import { ExpertsList } from '@/components/experts/ExpertsList';
import { ExpertPicksPanel } from '@/components/experts/ExpertPicksPanel';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';

// Mock data for development (will be replaced with API calls)
interface Expert {
  id: string;
  name: string;
  bio: string;
  avatar_url: string;
  specializations: string[];
  verification_status: string;
  overall_rating: number;
  total_subscribers: number;
  total_picks: number;
  win_rate: number;
  roi: number;
  streak_type: string;
  current_streak: number;
  subscription_tiers: Array<{
    id: string;
    name: string;
    price: number;
    description: string;
  }>;
  created_at: string;
  updated_at: string;
}

interface ExpertCategory {
  id: string;
  name: string;
  count: number;
}

const mockExperts: Expert[] = [
  {
    id: '1',
    name: 'Michael Johnson',
    bio: 'NFL betting expert with 8+ years experience. Former sports analyst.',
    avatar_url: '/avatars/expert1.jpg',
    specializations: ['NFL', 'College Football'],
    verification_status: 'verified',
    overall_rating: 4.8,
    total_subscribers: 2450,
    total_picks: 1250,
    win_rate: 0.68,
    roi: 0.15,
    streak_type: 'win',
    current_streak: 8,
    subscription_tiers: [
      { id: '1', name: 'Basic', price: 29.99, description: 'Weekly picks and analysis' },
      { id: '2', name: 'Premium', price: 99.99, description: 'Daily picks + live chat access' }
    ],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z'
  },
  {
    id: '2',
    name: 'Sarah Chen',
    bio: 'Crypto trading strategist specializing in DeFi and altcoins.',
    avatar_url: '/avatars/expert2.jpg',
    specializations: ['Crypto', 'DeFi', 'Trading'],
    verification_status: 'verified',
    overall_rating: 4.9,
    total_subscribers: 3200,
    total_picks: 890,
    win_rate: 0.74,
    roi: 0.22,
    streak_type: 'win',
    current_streak: 12,
    subscription_tiers: [
      { id: '3', name: 'Standard', price: 49.99, description: 'Weekly crypto signals' },
      { id: '4', name: 'Pro', price: 149.99, description: 'Daily signals + portfolio review' }
    ],
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-21T00:00:00Z'
  }
];

const categories: ExpertCategory[] = [
  { id: 'all', name: 'All Categories', count: 156 },
  { id: 'sports', name: 'Sports', count: 89 },
  { id: 'crypto', name: 'Cryptocurrency', count: 34 },
  { id: 'stocks', name: 'Stock Market', count: 23 },
  { id: 'forex', name: 'Forex', count: 10 }
];

export default function ExpertsMarketplace() {
  const multiApi = useMultiServiceApi();
  const [loading, setLoading] = useState(false);

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight text-white">Expert Analysts</h1>
        <p className="text-xl text-slate-300 max-w-2xl mx-auto">
          Follow verified experts and access premium predictions from top performers
        </p>
        
        <div className="flex justify-center gap-4">
          <Badge variant="secondary" className="text-sm bg-slate-700 text-slate-200 border-slate-600">
            <Trophy className="h-4 w-4 mr-1 text-yellow-400" />
            24 Verified Experts
          </Badge>
          <Badge variant="secondary" className="text-sm bg-slate-700 text-slate-200 border-slate-600">
            <TrendingUp className="h-4 w-4 mr-1 text-green-400" />
            73.2% Avg Accuracy
          </Badge>
          <Badge variant="secondary" className="text-sm bg-slate-700 text-slate-200 border-slate-600">
            <Users className="h-4 w-4 mr-1 text-blue-400" />
            8,450+ Followers
          </Badge>
        </div>
      </div>

      {/* Platform Value Proposition */}
      <Card className="bg-gradient-to-r from-slate-900/50 to-slate-800/50 border-slate-700">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-900/30 p-3 rounded-full w-fit mx-auto mb-3 border border-blue-600">
                <Target className="h-6 w-6 text-blue-400" />
              </div>
              <h3 className="font-semibold mb-2 text-white">Expert Predictions</h3>
              <p className="text-sm text-slate-300">
                Access predictions from verified analysts with proven track records
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-900/30 p-3 rounded-full w-fit mx-auto mb-3 border border-purple-600">
                <BarChart3 className="h-6 w-6 text-purple-400" />
              </div>
              <h3 className="font-semibold mb-2 text-white">Performance Analytics</h3>
              <p className="text-sm text-slate-300">
                Detailed analytics and performance metrics for every expert
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-green-900/30 p-3 rounded-full w-fit mx-auto mb-3 border border-green-600">
                <Star className="h-6 w-6 text-green-400" />
              </div>
              <h3 className="font-semibold mb-2 text-white">Tiered Access</h3>
              <p className="text-sm text-slate-300">
                Choose from free, basic, or premium subscription tiers
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Expert Picks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <ExpertPicksPanel 
            limit={5}
            showSubscriptionGate={true}
            userSubscriptionTier="free"
          />
        </div>
        
        <div>
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white font-bold">
                <TrendingUp className="h-5 w-5 text-green-400" />
                Top Performers This Week
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { name: 'ProAnalyst', accuracy: 85, picks: 12, roi: '+24.5%' },
                { name: 'SportsMaven', accuracy: 78, picks: 8, roi: '+18.2%' },
                { name: 'DataDriven', accuracy: 82, picks: 15, roi: '+16.7%' }
              ].map((expert, idx) => (
                <div key={idx} className="flex items-center justify-between p-3 bg-slate-800/30 border border-slate-600 rounded">
                  <div>
                    <span className="font-medium text-white">{expert.name}</span>
                    <div className="text-sm text-slate-300">
                      {expert.picks} picks • {expert.accuracy}% accuracy
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-green-400">{expert.roi}</div>
                    <div className="text-xs text-slate-400">ROI</div>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white">
                View Full Leaderboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Expert Analysts List */}
      <ExpertsList 
        showFilters={true}
        layout="grid"
      />

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-slate-900/50 to-slate-800/50 border border-slate-700 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4 text-white">Want to Become an Expert?</h2>
        <p className="text-slate-300 mb-6 max-w-2xl mx-auto">
          Share your expertise and earn from your knowledge. Join our platform as an expert analyst 
          and start building your subscriber base today.
        </p>
        <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
          <Link href="/expert-portal">
            Apply to Become an Expert
            <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
      </div>
    </AuthenticatedLayout>
  );
}