/**
 * Expert Profile Page
 * ===================
 * 
 * Detailed expert profile with performance analytics, subscription tiers,
 * reviews, and Stripe-powered checkout flow.
 */

'use client';

import React, { useState, useEffect } from 'react';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { useParams, useRouter } from 'next/navigation';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import Link from 'next/link';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Button } from '@/components/ui/button';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Badge } from '@/components/ui/badge';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Progress } from '@/components/ui/progress';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Separator } from '@/components/ui/separator';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import {
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
  Star,
  Users,
  Trophy,
  Target,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  DollarSign,
  BarChart3,
  MessageSquare,
  Shield,
  ArrowLeft,
  ChevronRight,
  CheckCircle,
  Award,
  Eye,
  Heart,
  Share2
} from 'lucide-react';
import api from '@/lib/api-client-unified';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
// import type { Expert, ExpertPerformance, SubscriptionTier, Review } from '@/lib/expert-analyst-api'; // TODO: Update types

// Mock data for development
const mockExpert: Expert = {
  id: '1',
  name: 'Michael Johnson',
  bio: 'Professional sports analyst with 8+ years of experience in NFL betting. Former ESPN contributor and current independent analyst specializing in game theory and statistical modeling.',
  avatar_url: '/avatars/expert1.jpg',
  specializations: ['NFL', 'College Football', 'Statistical Analysis', 'Game Theory'],
  verification_status: 'verified',
  overall_rating: 4.8,
  total_subscribers: 2450,
  total_picks: 1250,
  win_rate: 0.68,
  roi: 0.15,
  streak_type: 'win',
  current_streak: 8,
  subscription_tiers: [
    {
      id: '1',
      name: 'Basic',
      price: 29.99,
      description: 'Weekly picks and analysis',
      features: [
        '3-5 weekly picks',
        'Basic analysis',
        'Email notifications',
        'Community access'
      ]
    },
    {
      id: '2', 
      name: 'Premium',
      price: 99.99,
      description: 'Daily picks + live chat access',
      features: [
        'Daily picks and insights',
        'In-depth analysis',
        'Live chat access',
        'Priority support',
        'Mobile alerts',
        'Performance tracking'
      ]
    },
    {
      id: '3',
      name: 'VIP',
      price: 199.99,
      description: 'All access + personal consultation',
      features: [
        'Everything in Premium',
        '1-on-1 consultation calls',
        'Custom analysis requests',
        'Early pick access',
        'Portfolio review',
        'Direct expert contact'
      ]
    }
  ],
  created_at: '2024-01-15T00:00:00Z',
  updated_at: '2024-01-20T00:00:00Z'
};

const mockPerformance: ExpertPerformance = {
  total_picks: 1250,
  winning_picks: 850,
  losing_picks: 400,
  win_rate: 0.68,
  roi: 0.15,
  avg_odds: 1.85,
  profit_loss: 12500,
  best_category: 'NFL',
  worst_category: 'College Football',
  monthly_performance: [
    { month: 'Jan', wins: 45, losses: 15, roi: 0.18 },
    { month: 'Feb', wins: 52, losses: 18, roi: 0.22 },
    { month: 'Mar', wins: 48, losses: 22, roi: 0.16 },
    { month: 'Apr', wins: 55, losses: 15, roi: 0.25 },
    { month: 'May', wins: 42, losses: 28, roi: 0.08 },
    { month: 'Jun', wins: 58, losses: 12, roi: 0.28 }
  ]
};

const mockReviews: Review[] = [
  {
    id: '1',
    user_name: 'John D.',
    rating: 5,
    comment: 'Excellent analysis and consistent wins. Worth every penny!',
    created_at: '2024-01-18T00:00:00Z'
  },
  {
    id: '2', 
    user_name: 'Sarah M.',
    rating: 4,
    comment: 'Great insights but sometimes picks come a bit late.',
    created_at: '2024-01-16T00:00:00Z'
  }
];

export default function ExpertProfile() {
  const params = useParams();
  const router = useRouter();
  const [expert, setExpert] = useState<Expert | null>(mockExpert);
  const [performance, setPerformance] = useState<ExpertPerformance>(mockPerformance);
  const [reviews, setReviews] = useState<Review[]>(mockReviews);
  const [loading, setLoading] = useState(false);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(null);
  const [isFollowing, setIsFollowing] = useState(false);

  const expertId = params.id as string;

  useEffect(() => {
    loadExpertData();
  }, [expertId]);

  const loadExpertData = async () => {
    setLoading(true);
    try {
      // API calls would go here
      // const [expertData, performanceData, reviewsData] = await Promise.all([
      //   api.experts.getExpert(expertId),
      //   api.experts.getExpertPerformance(expertId),
      //   api.experts.getExpertReviews(expertId)
      // ]);
      
      // For now, use mock data
      setExpert(mockExpert);
      setPerformance(mockPerformance);
      setReviews(mockReviews);
    } catch (error) {
      console.error('Failed to load expert data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = (tier: SubscriptionTier) => {
    setSelectedTier(tier);
    // Redirect to Stripe checkout
    router.push(`/experts/${expertId}/subscribe?tier=${tier.id}`);
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // API call to follow/unfollow expert
  };

  if (!expert) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Expert Not Found</h2>
          <Button asChild>
            <Link href="/experts">Back to Experts</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Back Navigation */}
      <Button variant="ghost" asChild className="mb-6">
        <Link href="/experts">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Experts
        </Link>
      </Button>

      {/* Expert Header */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Expert Info */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={expert.avatar_url} />
                  <AvatarFallback className="text-2xl bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                    {expert.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <CardTitle className="text-3xl">{expert.name}</CardTitle>
                    {expert.verification_status === 'verified' && (
                      <Shield className="h-6 w-6 text-blue-500" />
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center gap-1">
                      <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold">{expert.overall_rating}</span>
                      <span className="text-muted-foreground">({reviews.length} reviews)</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-5 w-5 text-muted-foreground" />
                      <span>{expert.total_subscribers.toLocaleString()} subscribers</span>
                    </div>
                  </div>

                  <p className="text-muted-foreground mb-4">{expert.bio}</p>

                  <div className="flex flex-wrap gap-2">
                    {expert.specializations.map((spec) => (
                      <Badge key={spec} variant="secondary">
                        {spec}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button onClick={() => handleSubscribe(expert.subscription_tiers[0])} size="lg">
                  Subscribe Now
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
                
                <Button 
                  variant={isFollowing ? "default" : "outline"} 
                  onClick={handleFollow}
                  size="lg"
                >
                  <Heart className={`mr-2 h-4 w-4 ${isFollowing ? 'fill-current' : ''}`} />
                  {isFollowing ? 'Following' : 'Follow'}
                </Button>

                <Button variant="outline" size="lg">
                  <Share2 className="mr-2 h-4 w-4" />
                  Share
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Performance Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(expert.win_rate * 100).toFixed(0)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Win Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {(expert.roi * 100).toFixed(0)}%
                  </div>
                  <div className="text-sm text-muted-foreground">ROI</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total Picks</span>
                  <span className="font-medium">{expert.total_picks}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Current Streak</span>
                  <div className="flex items-center gap-1">
                    {expert.streak_type === 'win' ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    )}
                    <span className="font-medium">{expert.current_streak}</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="text-center">
                <div className="text-lg font-semibold mb-2">Subscription Plans</div>
                <div className="space-y-2">
                  {expert.subscription_tiers.map((tier) => (
                    <div key={tier.id} className="flex justify-between items-center text-sm">
                      <span>{tier.name}</span>
                      <span className="font-medium">${tier.price}/mo</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Detailed Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">Posted NFL Week 12 analysis</span>
                    <span className="text-xs text-muted-foreground ml-auto">2h ago</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span className="text-sm">Updated season predictions</span>
                    <span className="text-xs text-muted-foreground ml-auto">1d ago</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                    <span className="text-sm">Live chat session completed</span>
                    <span className="text-xs text-muted-foreground ml-auto">2d ago</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Achievements */}
            <Card>
              <CardHeader>
                <CardTitle>Achievements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <Award className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                    <div className="text-sm font-medium">Top Performer</div>
                    <div className="text-xs text-muted-foreground">Q4 2024</div>
                  </div>
                  <div className="text-center">
                    <Trophy className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <div className="text-sm font-medium">Rising Star</div>
                    <div className="text-xs text-muted-foreground">2024</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Win Rate Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performance.monthly_performance.map((month, index) => (
                    <div key={month.month}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{month.month}</span>
                        <span className="text-sm text-muted-foreground">
                          {month.wins}W - {month.losses}L ({((month.wins / (month.wins + month.losses)) * 100).toFixed(0)}%)
                        </span>
                      </div>
                      <Progress 
                        value={(month.wins / (month.wins + month.losses)) * 100} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Detailed Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Detailed Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {performance.winning_picks}
                    </div>
                    <div className="text-sm text-muted-foreground">Winning Picks</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">
                      {performance.losing_picks}
                    </div>
                    <div className="text-sm text-muted-foreground">Losing Picks</div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Average Odds</span>
                    <span className="font-medium">{performance.avg_odds}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Profit/Loss</span>
                    <span className="font-medium text-green-600">
                      +${performance.profit_loss.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Best Category</span>
                    <span className="font-medium">{performance.best_category}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {expert.subscription_tiers.map((tier, index) => (
              <Card key={tier.id} className={`relative ${index === 1 ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
                {index === 1 && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">{tier.name}</CardTitle>
                  <div className="text-3xl font-bold">
                    ${tier.price}
                    <span className="text-lg text-muted-foreground">/month</span>
                  </div>
                  <p className="text-muted-foreground">{tier.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 mb-6">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className="w-full" 
                    variant={index === 1 ? "default" : "outline"}
                    onClick={() => handleSubscribe(tier)}
                  >
                    Subscribe to {tier.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Review Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Review Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-3xl font-bold">{expert.overall_rating}</div>
                  <div className="flex items-center justify-center gap-1 mb-2">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-4 w-4 ${i < Math.floor(expert.overall_rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Based on {reviews.length} reviews
                  </div>
                </div>

                <div className="space-y-2">
                  {[5, 4, 3, 2, 1].map((stars) => (
                    <div key={stars} className="flex items-center gap-2">
                      <span className="text-sm w-2">{stars}</span>
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <Progress value={stars === 5 ? 70 : stars === 4 ? 25 : 5} className="h-2" />
                      <span className="text-xs text-muted-foreground w-8">
                        {stars === 5 ? '70%' : stars === 4 ? '25%' : '5%'}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Reviews */}
            <div className="lg:col-span-2 space-y-4">
              {reviews.map((review) => (
                <Card key={review.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {review.user_name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{review.user_name}</div>
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star 
                                key={i} 
                                className={`h-3 w-3 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {new Date(review.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{review.comment}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}