'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { TradingDashboard } from '@/components/trading/TradingDashboard';
import { useAuth } from '@clerk/nextjs';

export default function TradingPage() {
  const router = useRouter();
  const { isLoaded, userId } = useAuth();
  const [selectedMarketId, setSelectedMarketId] = useState<string | null>(null);

  useEffect(() => {
    if (isLoaded && !userId) {
      router.push('/login');
    }
  }, [isLoaded, userId, router]);

  if (!isLoaded || !userId) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen bg-black">
        <TradingDashboard 
          userId={userId}
          selectedMarketId={selectedMarketId}
          onMarketSelect={setSelectedMarketId}
        />
      </div>
    </AuthenticatedLayout>
  );
}