'use client';

import React, { useEffect, useState } from 'react';

export default function TestApiPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testApi = async () => {
      try {
        console.log('Testing API call...');
        
        const response = await fetch('http://localhost:8000/api/custom-betting/markets?page=1&limit=5');
        console.log('Response status:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        setData(data);
      } catch (err: any) {
        console.error('API test failed:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    testApi();
  }, []);

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API Test Results</h1>
      <div className="bg-gray-100 p-4 rounded">
        <p>Markets found: {Array.isArray(data) ? data.length : 'Not an array'}</p>
        {Array.isArray(data) && data.length > 0 && (
          <div className="mt-4">
            <h3 className="font-bold">First market:</h3>
            <pre className="text-sm mt-2">{JSON.stringify(data[0], null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
}