/**
 * BetBet Chess Game Page
 * ======================
 *
 * Dynamic route for individual chess games with full navigation layout.
 */

'use client';

import React from 'react';
import { ChessGame } from '@/components/chess/ChessGame';
import { MainLayout } from '@/components/layout/MainLayout';
import { useParams } from 'next/navigation';

export default function ChessGameRoute() {
  const params = useParams();
  const gameId = params.gameId as string;

  return (
    <MainLayout showSidebar={false} className="bg-slate-900">
      <div className="min-h-screen">
        <ChessGame
          gameId={gameId}
          mode="play"
        />
      </div>
    </MainLayout>
  );
}