/**
 * Custom Betting Platform - Admin Dashboard Page
 * ==============================================
 * 
 * Main administrative dashboard for platform management.
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { AdminDashboard } from '@/components/custom-betting';

export default function AdminDashboardPage() {
  const { user, isSignedIn } = useAuth();
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (!isSignedIn) {
      router.push('/custom-betting');
      return;
    }
    
    // Check admin status (in a real app, this would be from user metadata or roles)
    // For now, we'll assume certain users are admins
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const userEmail = user?.emailAddresses?.[0]?.emailAddress;
    const userIsAdmin = adminEmails.includes(userEmail || '') || userEmail?.includes('admin') || false;
    
    setIsAdmin(userIsAdmin);
    
    if (!userIsAdmin) {
      router.push('/custom-betting');
    }
  }, [isSignedIn, user, router]);

  if (!isSignedIn || !user) {
    return null;
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Button variant="outline" size="sm" asChild>
          <Link href="/custom-betting" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Platform
          </Link>
        </Button>

        {/* Admin Dashboard Component */}
        <AdminDashboard isAdmin={isAdmin} />
      </div>
    </MainLayout>
  );
}