/**
 * Custom Betting Platform - Admin Settlement Page
 * ==============================================
 * 
 * Administrative interface for settling betting markets.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft,
  Shield,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { useApi } from '@/hooks/useApi';
import { LoadingSpinner, SettlementInterface, DisputeResolution } from '@/components/custom-betting';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AdminSettlementPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isSignedIn } = useAuth();
  const marketId = params.id as string;

  const [market, setMarket] = useState<CustomBet | null>(null);
  const [participants, setParticipants] = useState<BetParticipant[]>([]);
  const [disputes, setDisputes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (!isSignedIn) {
      router.push('/custom-betting');
      return;
    }
    
    // Check admin status (in a real app, this would be from user metadata)
    // For now, we'll assume certain users are admins
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const userEmail = user?.emailAddresses?.[0]?.emailAddress;
    setIsAdmin(adminEmails.includes(userEmail || '') || userEmail?.includes('admin') || false);
    
    if (marketId) {
      loadSettlementData();
    }
  }, [isSignedIn, marketId, user, router]);

  const loadSettlementData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load market details
      const marketData = await customBettingAPI.getBet(marketId);
      setMarket(marketData);

      // Load participants
      const participantsData = await customBettingAPI.getBetParticipants(marketId);
      setParticipants(participantsData);

      // Load disputes (if any)
      try {
        const disputesData = await customBettingAPI.getMarketDisputes(marketId);
        setDisputes(disputesData);
      } catch (disputeErr) {
        // Disputes might not exist, that's okay
        setDisputes([]);
      }

    } catch (err: any) {
      console.error('Failed to load settlement data:', err);
      setError('Failed to load market data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSettled = () => {
    // Reload data after settlement
    loadSettlementData();
  };

  const handleDisputeCreated = () => {
    // Reload disputes after creation
    loadSettlementData();
  };

  const handleDisputeResolved = () => {
    // Reload data after dispute resolution
    loadSettlementData();
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner 
            fullPage 
            text="Loading settlement interface..." 
            size="lg" 
          />
        </div>
      </MainLayout>
    );
  }

  if (!isAdmin) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/custom-betting" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Markets
            </Link>
          </Button>

          <Alert variant="destructive">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Access denied. Administrative privileges required to access settlement interface.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  if (error || !market) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/dashboard" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Admin Dashboard
            </Link>
          </Button>

          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Market not found'}
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  const canSettle = isAdmin || market.creator_user_id === user?.id;
  const hasDisputes = disputes.length > 0;

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/dashboard" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Admin Dashboard
            </Link>
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Shield className="h-6 w-6 text-primary" />
              Market Settlement
            </h1>
            <p className="text-gray-600">Administrative settlement interface</p>
          </div>
        </div>

        {/* Settlement Interface */}
        <Tabs defaultValue={hasDisputes ? "disputes" : "settlement"}>
          <TabsList>
            <TabsTrigger value="settlement">
              Settlement Interface
            </TabsTrigger>
            {hasDisputes && (
              <TabsTrigger value="disputes">
                Disputes ({disputes.length})
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="settlement">
            <SettlementInterface
              market={market}
              participants={participants}
              canSettle={canSettle}
              onSettled={handleSettled}
            />
          </TabsContent>

          {hasDisputes && (
            <TabsContent value="disputes">
              <DisputeResolution
                market={market}
                disputes={disputes}
                canCreateDispute={false} // Admins don't create disputes
                canResolveDisputes={isAdmin}
                onDisputeCreated={handleDisputeCreated}
                onDisputeResolved={handleDisputeResolved}
              />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </MainLayout>
  );
}