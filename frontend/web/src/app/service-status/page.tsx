'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import api from '@/lib/api-client-unified';
import { AuthTestPanel } from '@/components/test/AuthTestPanel';
import { RefreshCw, Check, X, Activity } from 'lucide-react';

interface ServiceHealth {
  [key: string]: boolean;
}

export default function ServiceStatusPage() {
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth>({});
  const [serviceData, setServiceData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<string>('');

  const checkServices = async () => {
    setLoading(true);
    try {
      // Check service health
      const health = await api.system.healthAll();
      setServiceHealth(health);

      // Fetch sample data from each working service
      const data: Record<string, any> = {};

      try {
        const games = await api.gaming.getGames();
        data.gaming = games;
      } catch (e) {
        data.gaming = { error: 'Failed to fetch games' };
      }

      try {
        const leaderboards = await api.leaderboards.getUnifiedLeaderboard();
        data.leaderboards = leaderboards;
      } catch (e) {
        data.leaderboards = { error: 'Failed to fetch leaderboards' };
      }

      try {
        const customBets = await api.customBetting.getMarkets();
        data.customBetting = customBets;
      } catch (e) {
        data.customBetting = { error: 'Failed to fetch custom betting data' };
      }

      try {
        const trading = await api.trading.getMarkets();
        data.trading = trading;
      } catch (e) {
        data.trading = { error: 'Failed to fetch trading data' };
      }

      try {
        const experts = await api.experts.getExperts();
        data.experts = experts;
      } catch (e) {
        data.experts = { error: 'Failed to fetch experts' };
      }

      try {
        const sports = await api.sports.getFixtures();
        data.sports = sports;
      } catch (e) {
        data.sports = { error: 'Failed to fetch sports data' };
      }

      setServiceData(data);
      setLastChecked(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Error checking services:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkServices();
  }, []);

  const getStatusIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <Check className="h-4 w-4 text-green-600" />
    ) : (
      <X className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusBadge = (isHealthy: boolean) => {
    return (
      <Badge variant={isHealthy ? "default" : "destructive"}>
        {isHealthy ? "Healthy" : "Down"}
      </Badge>
    );
  };

  const services = [
    { key: 'gaming', name: 'Gaming Engine', port: '8001', description: 'P2P gaming with real-time multiplayer' },
    { key: 'customBetting', name: 'Custom Betting', port: '8002', description: 'Polymarket-style P2P betting marketplace' },
    { key: 'trading', name: 'Odds Exchange', port: '8003', description: 'Advanced trading platform with matching engine' },
    { key: 'experts', name: 'Expert Analysts', port: '8004', description: 'Expert picks and subscription system' },
    { key: 'sports', name: 'Sports Analysis', port: '8005', description: 'AI prediction engine with RAG' },
    { key: 'leaderboards', name: 'Leaderboards', port: '8006', description: 'Comprehensive ranking system' },
    { key: 'gateway', name: 'API Gateway', port: '8000', description: 'Unified API entry point' }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Activity className="h-8 w-8 text-blue-600" />
              Service Status Dashboard
            </h1>
            <p className="text-gray-600 mt-2">
              Monitor the health and connectivity of all BetBet platform services
            </p>
          </div>
          <Button onClick={checkServices} disabled={loading} className="flex items-center gap-2">
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        
        {lastChecked && (
          <p className="text-sm text-gray-500">
            Last checked: {lastChecked}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {services.map((service) => {
          const isHealthy = serviceHealth[service.key];
          const data = serviceData[service.key];
          
          return (
            <Card key={service.key} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    {getStatusIcon(isHealthy)}
                    {service.name}
                  </CardTitle>
                  {getStatusBadge(isHealthy)}
                </div>
                <CardDescription>
                  Port {service.port} • {service.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Status:</span>
                    <span className={isHealthy ? 'text-green-600' : 'text-red-600'}>
                      {isHealthy ? 'Online' : 'Offline'}
                    </span>
                  </div>
                  
                  {data && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <p className="text-xs text-gray-600 mb-2">Sample Response:</p>
                      <pre className="text-xs overflow-hidden">
                        {JSON.stringify(data, null, 2).substring(0, 200)}
                        {JSON.stringify(data, null, 2).length > 200 ? '...' : ''}
                      </pre>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Service Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Service Integration Test Results</CardTitle>
          <CardDescription>
            Real data fetched from each service to verify connectivity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            
            {/* Gaming Service Results */}
            {serviceData.gaming && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-blue-600">🎮 Gaming Engine</h3>
                {serviceData.gaming.games && serviceData.gaming.games.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {serviceData.gaming.games.slice(0, 3).map((game: any, idx: number) => (
                      <div key={idx} className="p-3 border rounded-md">
                        <h4 className="font-medium">{game.name}</h4>
                        <p className="text-sm text-gray-600">{game.description}</p>
                        <div className="flex gap-2 mt-2">
                          <Badge variant="outline">{game.category}</Badge>
                          {game.is_featured && <Badge>Featured</Badge>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-red-600">No games data available</p>
                )}
              </div>
            )}

            {/* Custom Betting Results */}
            {serviceData.customBetting && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-purple-600">🎯 Custom Betting Marketplace</h3>
                {serviceData.customBetting.bets && serviceData.customBetting.bets.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {serviceData.customBetting.bets.slice(0, 2).map((bet: any, idx: number) => (
                      <div key={idx} className="p-3 border rounded-md">
                        <h4 className="font-medium">{bet.title || bet.description}</h4>
                        <p className="text-sm text-gray-600">{bet.outcome_description}</p>
                        <div className="flex gap-2 mt-2">
                          <Badge variant="outline">{bet.status}</Badge>
                          {bet.total_stake && <Badge variant="secondary">${bet.total_stake}</Badge>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-600">Custom betting marketplace ready for P2P markets</p>
                )}
              </div>
            )}

            {/* Leaderboards Results */}
            {serviceData.leaderboards && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-yellow-600">🏆 Leaderboards</h3>
                {serviceData.leaderboards.leaderboards && serviceData.leaderboards.leaderboards.length > 0 ? (
                  <div className="space-y-2">
                    {serviceData.leaderboards.leaderboards.slice(0, 3).map((player: any, idx: number) => (
                      <div key={idx} className="flex justify-between items-center p-2 border rounded">
                        <div className="flex items-center gap-3">
                          <span className="font-bold text-lg">#{player.rank}</span>
                          <span className="font-medium">{player.username}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{player.score} pts</div>
                          <div className="text-sm text-green-600">${player.earnings}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-600">Mock leaderboard data shown</p>
                )}
              </div>
            )}

          </div>
        </CardContent>
      </Card>

      {/* Authentication Test Panel */}
      <AuthTestPanel />
    </div>
  );
}