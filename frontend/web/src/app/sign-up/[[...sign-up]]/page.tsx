/**
 * BetBet Gaming Engine - Custom Sign Up Page
 * =========================================
 * 
 * Custom sign-up page using Clerk with our branding and UI.
 */

import { SignUp } from '@clerk/nextjs';
import { GamepadIcon } from 'lucide-react';

export default function SignUpPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="w-full max-w-md space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            <GamepadIcon className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold">BetBet</h1>
          </div>
          <p className="text-gray-600">Create your gaming account</p>
        </div>

        {/* Clerk Sign Up Component */}
        <div className="flex justify-center">
          <SignUp 
            appearance={{
              elements: {
                rootBox: "mx-auto",
                card: "shadow-lg border-0",
                headerTitle: "text-2xl font-bold",
                headerSubtitle: "text-gray-600",
                socialButtonsBlockButton: "border border-gray-300 hover:bg-gray-50",
                socialButtonsBlockButtonText: "font-medium",
                formButtonPrimary: "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md",
                formFieldInput: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                footerActionLink: "text-blue-600 hover:text-blue-700 font-medium"
              },
              layout: {
                socialButtonsPlacement: "top"
              }
            }}
            fallbackRedirectUrl="/dashboard"
            signInUrl="/sign-in"
          />
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          <p>
            By signing up, you agree to our{' '}
            <a href="/terms" className="hover:underline">Terms of Service</a>
            {' '}and{' '}
            <a href="/privacy" className="hover:underline">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
}