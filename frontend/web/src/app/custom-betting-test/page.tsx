'use client';

import React, { useEffect, useState } from 'react';

export default function CustomBettingTestPage() {
  const [markets, setMarkets] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadMarkets = async () => {
      try {
        console.log('Loading markets...');
        
        // Direct fetch call
        const response = await fetch('http://localhost:8000/api/custom-betting/markets?page=1&limit=5');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('Markets loaded:', data);
        
        setMarkets(Array.isArray(data) ? data : []);
      } catch (err: any) {
        console.error('Failed to load markets:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadMarkets();
  }, []);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Custom Betting Markets - Test</h1>
      
      {loading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2">Loading markets...</p>
        </div>
      )}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {!loading && !error && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Markets Found: {markets.length}</h2>
          
          {markets.length === 0 ? (
            <p className="text-gray-600">No markets available.</p>
          ) : (
            <div className="space-y-4">
              {markets.map((market, index) => (
                <div key={market.id || index} className="border rounded-lg p-4 bg-white shadow">
                  <h3 className="font-semibold text-lg">{market.title}</h3>
                  <p className="text-gray-600 mt-1">{market.description}</p>
                  <div className="flex gap-4 mt-2 text-sm text-gray-500">
                    <span>Status: {market.status}</span>
                    <span>Type: {market.market_type}</span>
                    <span>Volume: ${market.total_volume}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}