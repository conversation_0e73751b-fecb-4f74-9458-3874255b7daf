/**
 * BetBet Gaming Engine - Live Sessions Page
 * ========================================
 * 
 * Browse and join live gaming sessions with real-time previews.
 */

'use client';

import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { LiveSessionBrowser } from '@/components/games/LiveSessionBrowser';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Activity,
  Zap,
  Users,
  Eye,
  Trophy,
  DollarSign
} from 'lucide-react';

export default function LiveSessionsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold flex items-center justify-center gap-3">
            <Activity className="h-10 w-10 text-primary" />
            Live Gaming Sessions
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Jump into live games or watch exciting matches in progress. 
            Real-time previews show you exactly what's happening right now!
          </p>
        </div>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="flex items-center justify-center mb-2">
                <Zap className="h-8 w-8 text-orange-500" />
              </div>
              <h3 className="font-semibold mb-1">Real-time Updates</h3>
              <p className="text-sm text-gray-600">
                Live game state and score updates every few seconds
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-8 w-8 text-blue-500" />
              </div>
              <h3 className="font-semibold mb-1">Join or Watch</h3>
              <p className="text-sm text-gray-600">
                Join active games or spectate exciting matches
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="flex items-center justify-center mb-2">
                <Trophy className="h-8 w-8 text-yellow-500" />
              </div>
              <h3 className="font-semibold mb-1">Excitement Score</h3>
              <p className="text-sm text-gray-600">
                AI-powered scoring highlights the most thrilling games
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
              <h3 className="font-semibold mb-1">Prize Pools</h3>
              <p className="text-sm text-gray-600">
                From free play to high-stakes competitions
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Live Session Browser */}
        <LiveSessionBrowser 
          showJoinButtons={true}
          maxSessions={50}
          autoRefresh={true}
          refreshInterval={3000}
        />

        {/* How It Works */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">How Live Previews Work</CardTitle>
            <CardDescription>
              Our real-time game preview system brings you closer to the action
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">1</Badge>
                  <h4 className="font-semibold">Live Game States</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Real-time updates show current scores, leaders, recent actions, and game progress
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">2</Badge>
                  <h4 className="font-semibold">Smart Filtering</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Filter by game type, stakes, availability, and excitement level to find your perfect match
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">3</Badge>
                  <h4 className="font-semibold">Instant Action</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Join games with available slots or watch as a spectator with optional betting
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}