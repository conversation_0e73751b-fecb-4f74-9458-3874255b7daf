/**
 * BetBet Gaming Engine - User Profile Page
 * ========================================
 * 
 * User profile management with stats, settings, and gaming history.
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import MainLayout from '@/components/layout/MainLayout';
import {
  User,
  Mail,
  Calendar,
  Trophy,
  GamepadIcon,
  TrendingUp,
  DollarSign,
  Clock,
  Settings,
  Bell,
  Shield,
  Edit3,
  Save,
  X,
  Eye,
  EyeOff,
  Loader2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import { cn } from '@/lib/utils';

interface UserStats {
  total_games_played: number;
  total_wins: number;
  total_earnings: number;
  current_rank: string;
  win_rate: number;
  average_session_duration: number;
  favorite_game_category: string;
  longest_win_streak: number;
}

interface UserSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  marketing_emails: boolean;
  public_profile: boolean;
  show_online_status: boolean;
  allow_friend_requests: boolean;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, isSignedIn, isLoaded } = useAuth();
  const api = useApi();
  
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [settings, setSettings] = useState<UserSettings>({
    email_notifications: true,
    push_notifications: true,
    marketing_emails: false,
    public_profile: true,
    show_online_status: true,
    allow_friend_requests: true,
  });
  
  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    bio: user?.bio || '',
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!isSignedIn) {
      router.push('/login?redirect=/profile');
      return;
    }
  }, [isSignedIn, router]);

  // Load user data
  useEffect(() => {
    const loadUserData = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        
        // Load user stats
        const statsResponse = await apiClient.getUserStats(user.id);
        setUserStats(statsResponse);
        
        // Load user settings
        const settingsResponse = await apiClient.getUserSettings(user.id);
        setSettings(settingsResponse);
        
      } catch (error) {
        console.error('Failed to load user data:', error);
        setError('Failed to load profile data');
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [user]);

  const handleProfileUpdate = async () => {
    try {
      setIsSaving(true);
      setError('');
      setSuccess('');

      const updateData: any = {
        username: profileData.username,
        bio: profileData.bio,
      };

      // Add password change if provided
      if (profileData.new_password) {
        if (profileData.new_password !== profileData.confirm_password) {
          setError('New passwords do not match');
          return;
        }
        updateData.current_password = profileData.current_password;
        updateData.new_password = profileData.new_password;
      }

      const response = await apiClient.updateProfile(updateData);
      updateUser(response.user);
      
      setSuccess('Profile updated successfully');
      setIsEditing(false);
      
      // Clear password fields
      setProfileData(prev => ({
        ...prev,
        current_password: '',
        new_password: '',
        confirm_password: '',
      }));

    } catch (error: any) {
      console.error('Profile update failed:', error);
      setError(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSettingsUpdate = async (key: keyof UserSettings, value: boolean) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      
      await apiClient.updateUserSettings(newSettings);
      
    } catch (error: any) {
      console.error('Settings update failed:', error);
      setError('Failed to update settings');
      // Revert on error
      setSettings(settings);
    }
  };

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  if (!isSignedIn || !user) {
    return null;
  }

  const winRate = userStats ? (userStats.total_wins / userStats.total_games_played * 100) : 0;

  return (
    <MainLayout>
      <div className="space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Profile</h1>
            <p className="text-muted-foreground">
              Manage your account and gaming preferences
            </p>
          </div>
          <Button variant="outline" onClick={handleLogout}>
            <Settings className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Profile Card */}
          <Card className="lg:col-span-1">
            <CardHeader className="text-center">
              <Avatar className="h-24 w-24 mx-auto">
                <AvatarImage src={user.avatar_url} />
                <AvatarFallback className="text-lg">
                  {user.username.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <CardTitle>{user.username}</CardTitle>
              <CardDescription>{user.email}</CardDescription>
              
              {userStats && (
                <div className="flex items-center justify-center space-x-4 pt-2">
                  <div className="text-center">
                    <div className="text-lg font-bold">{userStats.current_rank}</div>
                    <div className="text-xs text-muted-foreground">Rank</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold">{winRate.toFixed(1)}%</div>
                    <div className="text-xs text-muted-foreground">Win Rate</div>
                  </div>
                </div>
              )}
            </CardHeader>
            
            {userStats && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">{userStats.total_wins}</div>
                    <div className="text-xs text-muted-foreground">Wins</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{userStats.total_games_played}</div>
                    <div className="text-xs text-muted-foreground">Games</div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Earnings</span>
                    <span className="text-sm font-medium">${userStats.total_earnings}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Win Streak</span>
                    <span className="text-sm font-medium">{userStats.longest_win_streak}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Avg. Duration</span>
                    <span className="text-sm font-medium">{userStats.average_session_duration}m</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Favorite Category</span>
                    <Badge variant="secondary">{userStats.favorite_game_category}</Badge>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="profile" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
              </TabsList>

              {/* Profile Tab */}
              <TabsContent value="profile" className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Profile Information</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditing(!isEditing)}
                      >
                        {isEditing ? <X className="h-4 w-4" /> : <Edit3 className="h-4 w-4" />}
                        {isEditing ? 'Cancel' : 'Edit'}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="username">Username</Label>
                        <Input
                          id="username"
                          value={profileData.username}
                          onChange={(e) => setProfileData(prev => ({ ...prev, username: e.target.value }))}
                          disabled={!isEditing}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={profileData.email}
                          disabled
                          className="bg-muted"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Input
                        id="bio"
                        placeholder="Tell us about yourself..."
                        value={profileData.bio}
                        onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>

                    {isEditing && (
                      <>
                        <Separator />
                        <div className="space-y-4">
                          <Label className="text-base">Change Password (Optional)</Label>
                          
                          <div className="space-y-2">
                            <Label htmlFor="current_password">Current Password</Label>
                            <Input
                              id="current_password"
                              type="password"
                              value={profileData.current_password}
                              onChange={(e) => setProfileData(prev => ({ ...prev, current_password: e.target.value }))}
                              placeholder="Enter current password"
                            />
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="new_password">New Password</Label>
                              <Input
                                id="new_password"
                                type="password"
                                value={profileData.new_password}
                                onChange={(e) => setProfileData(prev => ({ ...prev, new_password: e.target.value }))}
                                placeholder="Enter new password"
                              />
                            </div>
                            
                            <div className="space-y-2">
                              <Label htmlFor="confirm_password">Confirm Password</Label>
                              <Input
                                id="confirm_password"
                                type="password"
                                value={profileData.confirm_password}
                                onChange={(e) => setProfileData(prev => ({ ...prev, confirm_password: e.target.value }))}
                                placeholder="Confirm new password"
                              />
                            </div>
                          </div>
                        </div>
                        
                        <Button onClick={handleProfileUpdate} disabled={isSaving}>
                          {isSaving ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className="h-4 w-4 mr-2" />
                              Save Changes
                            </>
                          )}
                        </Button>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Preferences</CardTitle>
                    <CardDescription>
                      Choose how you want to be notified about game activities
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">Game results and important updates</p>
                      </div>
                      <Switch
                        checked={settings.email_notifications}
                        onCheckedChange={(checked) => handleSettingsUpdate('email_notifications', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Push Notifications</Label>
                        <p className="text-sm text-muted-foreground">Real-time game invites and messages</p>
                      </div>
                      <Switch
                        checked={settings.push_notifications}
                        onCheckedChange={(checked) => handleSettingsUpdate('push_notifications', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Marketing Emails</Label>
                        <p className="text-sm text-muted-foreground">Updates about new games and tournaments</p>
                      </div>
                      <Switch
                        checked={settings.marketing_emails}
                        onCheckedChange={(checked) => handleSettingsUpdate('marketing_emails', checked)}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Privacy Settings</CardTitle>
                    <CardDescription>
                      Control your profile visibility and social features
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Public Profile</Label>
                        <p className="text-sm text-muted-foreground">Allow others to view your gaming stats</p>
                      </div>
                      <Switch
                        checked={settings.public_profile}
                        onCheckedChange={(checked) => handleSettingsUpdate('public_profile', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Show Online Status</Label>
                        <p className="text-sm text-muted-foreground">Let friends see when you're online</p>
                      </div>
                      <Switch
                        checked={settings.show_online_status}
                        onCheckedChange={(checked) => handleSettingsUpdate('show_online_status', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Allow Friend Requests</Label>
                        <p className="text-sm text-muted-foreground">Enable other players to send friend requests</p>
                      </div>
                      <Switch
                        checked={settings.allow_friend_requests}
                        onCheckedChange={(checked) => handleSettingsUpdate('allow_friend_requests', checked)}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Security Tab */}
              <TabsContent value="security" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Account Security</CardTitle>
                    <CardDescription>
                      Manage your account security and access
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Shield className="h-5 w-5 text-green-600" />
                        <div>
                          <Label>Password Protection</Label>
                          <p className="text-sm text-muted-foreground">Last changed 2 weeks ago</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Change Password
                      </Button>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-blue-600" />
                        <div>
                          <Label>Email Verification</Label>
                          <p className="text-sm text-muted-foreground">Verified</p>
                        </div>
                      </div>
                      <Badge variant="secondary">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-5 w-5 text-purple-600" />
                        <div>
                          <Label>Account Created</Label>
                          <p className="text-sm text-muted-foreground">
                            {new Date(user.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-red-200">
                  <CardHeader>
                    <CardTitle className="text-red-600">Danger Zone</CardTitle>
                    <CardDescription>
                      Irreversible actions that affect your account
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="destructive" size="sm">
                      Delete Account
                    </Button>
                    <p className="text-sm text-muted-foreground mt-2">
                      This action cannot be undone. All your data will be permanently deleted.
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}