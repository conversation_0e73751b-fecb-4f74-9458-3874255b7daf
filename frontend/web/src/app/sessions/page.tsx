/**
 * BetBet Gaming Engine - Sessions Browser
 * ======================================
 * 
 * Live session browser with real-time updates and join functionality.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  Users,
  Clock,
  DollarSign,
  Trophy,
  Eye,
  Play,
  Search,
  Filter,
  RefreshCw,
  Timer,
  GamepadIcon,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import { GameSession, SessionStatus } from '@/lib/api';
import { cn } from '@/lib/utils';

type SessionFilter = 'all' | 'waiting' | 'active' | 'starting_soon' | 'joinable';

export default function SessionsPage() {
  const { isSignedIn } = useAuth();
  
  const [sessions, setSessions] = useState<GameSession[]>([]);
  const [sessionsLoading, setSessionsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<SessionFilter>('joinable');
  const [filteredSessions, setFilteredSessions] = useState<GameSession[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const api = useApi();

  // Load sessions
  const loadSessions = useCallback(async () => {
    try {
      setSessionsLoading(true);
      const params: any = { limit: 50 };
      
      // Apply filter-specific params
      switch (selectedFilter) {
        case 'waiting':
          params.status = SessionStatus.WAITING;
          break;
        case 'active':
          params.status = SessionStatus.ACTIVE;
          break;
        case 'starting_soon':
          params.starting_soon = true;
          break;
        case 'joinable':
          params.has_available_slots = true;
          params.status = SessionStatus.WAITING;
          break;
      }

      const response = await api.getSessions(params);
      setSessions(response.sessions);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setSessionsLoading(false);
    }
  }, [selectedFilter, setSessions, setSessionsLoading, api]);

  // Filter sessions locally
  useEffect(() => {
    let filtered = [...sessions];

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        session =>
          session.session_name.toLowerCase().includes(query) ||
          session.game_id.toLowerCase().includes(query)
      );
    }

    // Sort by priority: waiting sessions first, then by participants
    filtered.sort((a, b) => {
      if (a.status === SessionStatus.WAITING && b.status !== SessionStatus.WAITING) return -1;
      if (b.status === SessionStatus.WAITING && a.status !== SessionStatus.WAITING) return 1;
      return b.current_participants - a.current_participants;
    });

    setFilteredSessions(filtered);
  }, [sessions, searchQuery]);

  // Auto-refresh sessions every 30 seconds
  useEffect(() => {
    loadSessions();
    const interval = setInterval(loadSessions, 30000);
    return () => clearInterval(interval);
  }, [loadSessions]);

  const getStatusColor = (status: SessionStatus) => {
    switch (status) {
      case SessionStatus.WAITING:
        return 'bg-blue-500';
      case SessionStatus.STARTING:
        return 'bg-yellow-500';
      case SessionStatus.ACTIVE:
        return 'bg-green-500';
      case SessionStatus.PAUSED:
        return 'bg-orange-500';
      case SessionStatus.COMPLETED:
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusBadge = (status: SessionStatus) => {
    const variants: Record<SessionStatus, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      [SessionStatus.WAITING]: 'secondary',
      [SessionStatus.STARTING]: 'default',
      [SessionStatus.ACTIVE]: 'default',
      [SessionStatus.PAUSED]: 'outline',
      [SessionStatus.COMPLETED]: 'secondary',
      [SessionStatus.CANCELLED]: 'destructive',
      [SessionStatus.ERROR]: 'destructive',
    };

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const SessionCard = ({ session }: { session: GameSession }) => {
    const canJoin = session.status === SessionStatus.WAITING && 
                   session.current_participants < session.max_participants;
    const isActive = session.status === SessionStatus.ACTIVE;
    
    return (
      <Card className="session-card">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center space-x-2">
                <span>{session.session_name}</span>
                {isActive && (
                  <div className="flex items-center space-x-1">
                    <div className="pulse-dot" />
                    <span className="live-indicator">LIVE</span>
                  </div>
                )}
              </CardTitle>
              <CardDescription>
                Game ID: {session.game_id.slice(0, 8)}...
              </CardDescription>
            </div>
            {getStatusBadge(session.status)}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {session.current_participants}/{session.max_participants}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">${session.entry_fee}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Trophy className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">${session.total_prize_pool}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{session.estimated_duration_minutes}m</span>
            </div>
          </div>

          {session.scheduled_start_time && (
            <div className="flex items-center space-x-2 mb-4 text-sm text-muted-foreground">
              <Timer className="h-4 w-4" />
              <span>
                Starts: {new Date(session.scheduled_start_time).toLocaleString()}
              </span>
            </div>
          )}

          <div className="flex items-center space-x-2 mb-4">
            {session.allow_spectators && (
              <Badge variant="outline">Spectators</Badge>
            )}
            {session.allow_practice_mode && (
              <Badge variant="outline">Practice</Badge>
            )}
            {session.is_tournament_session && (
              <Badge variant="outline">Tournament</Badge>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Created {new Date(session.created_at).toLocaleDateString()}
            </div>
            <div className="flex space-x-2">
              {canJoin && isSignedIn ? (
                <Button size="sm" asChild>
                  <Link href={`/sessions/${session.id}/join`}>
                    <Play className="h-3 w-3 mr-2" />
                    Join
                  </Link>
                </Button>
              ) : (
                <Button size="sm" variant="outline" asChild>
                  <Link href={`/sessions/${session.id}`}>
                    <Eye className="h-3 w-3 mr-2" />
                    {isActive ? 'Spectate' : 'View'}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const filterOptions = [
    { value: 'all', label: 'All Sessions', count: sessions.length },
    { 
      value: 'joinable', 
      label: 'Joinable', 
      count: sessions.filter(s => s.status === SessionStatus.WAITING && s.current_participants < s.max_participants).length 
    },
    { value: 'waiting', label: 'Waiting', count: sessions.filter(s => s.status === SessionStatus.WAITING).length },
    { value: 'active', label: 'Live', count: sessions.filter(s => s.status === SessionStatus.ACTIVE).length },
    { value: 'starting_soon', label: 'Starting Soon', count: 0 }, // TODO: Implement starting soon logic
  ];

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Game Sessions</h1>
            <p className="text-muted-foreground">
              Join live gaming sessions or spectate ongoing matches
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={loadSessions} disabled={sessionsLoading}>
              <RefreshCw className={cn("h-4 w-4 mr-2", sessionsLoading && "animate-spin")} />
              Refresh
            </Button>
            {isSignedIn && (
              <Button asChild>
                <Link href="/games">
                  <GamepadIcon className="h-4 w-4 mr-2" />
                  Create Session
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground transform -translate-y-1/2" />
              <Input
                placeholder="Search sessions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select value={selectedFilter} onValueChange={(value) => setSelectedFilter(value as SessionFilter)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label} ({option.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Filter Tabs */}
        <Tabs value={selectedFilter} onValueChange={(value) => setSelectedFilter(value as SessionFilter)}>
          <TabsList className="w-full justify-start">
            {filterOptions.map((option) => (
              <TabsTrigger key={option.value} value={option.value}>
                {option.label}
                {option.count > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {option.count}
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* Results Info */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {filteredSessions.length} sessions found
          </p>
          <p className="text-xs text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>

        {/* Sessions Grid */}
        {sessionsLoading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="grid grid-cols-4 gap-4">
                      {[...Array(4)].map((_, j) => (
                        <div key={j} className="h-3 bg-muted rounded"></div>
                      ))}
                    </div>
                    <div className="h-8 bg-muted rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No sessions found</h3>
            <p className="text-muted-foreground mb-4">
              {selectedFilter === 'joinable' 
                ? "No joinable sessions available right now" 
                : "Try adjusting your search or filter criteria"
              }
            </p>
            {isSignedIn && (
              <Button asChild>
                <Link href="/games">
                  <GamepadIcon className="h-4 w-4 mr-2" />
                  Create Your Own Session
                </Link>
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredSessions.map((session) => (
              <SessionCard key={session.id} session={session} />
            ))}
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
}