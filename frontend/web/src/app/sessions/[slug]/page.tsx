/**
 * BetBet Gaming Engine - Session Details Page
 * ==========================================
 * 
 * Individual session page with details, joining, and spectating.
 */

'use client';

import { SessionDetails } from '@/components/sessions/SessionDetails';
import MainLayout from '@/components/layout/MainLayout';

interface SessionPageProps {
  params: {
    slug: string;
  };
}

export default function SessionPage({ params }: SessionPageProps) {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <SessionDetails sessionSlug={params.slug} />
      </div>
    </MainLayout>
  );
}