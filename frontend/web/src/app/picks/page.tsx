/**
 * Expert Pick Feed Page
 * =====================
 * 
 * Personalized feed of expert picks with real-time updates,
 * subscription-based access control, and performance tracking.
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import Link from 'next/link';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Button } from '@/components/ui/button';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Input } from '@/components/ui/input';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Badge } from '@/components/ui/badge';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Progress } from '@/components/ui/progress';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Separator } from '@/components/ui/separator';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import {
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  Star,
  Clock,
  Target,
  DollarSign,
  Eye,
  Heart,
  MessageSquare,
  BarChart3,
  Lock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar,
  Trophy,
  Zap,
  ArrowRight,
  ExternalLink
} from 'lucide-react';
import api from '@/lib/api-client-unified';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
// import type { Pick, Expert } from '@/lib/expert-analyst-api'; // TODO: Update types
import { useAuth } from '@clerk/nextjs';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { formatDistanceToNow, format } from 'date-fns';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";

// Mock data for development
const mockPicks: Pick[] = [
  {
    id: '1',
    expert_id: '1',
    expert_name: 'Michael Johnson',
    expert_avatar: '/avatars/expert1.jpg',
    title: 'Chiefs -3.5 vs Bills in AFC Championship',
    description: 'Despite the Bills strong home field advantage, I like the Chiefs to cover the spread. Mahomes has historically performed better in playoff games, and the Chiefs defense has been underrated all season.',
    category: 'NFL',
    subcategory: 'Playoffs',
    confidence_level: 85,
    pick_type: 'Spread',
    odds: 1.91,
    stake_recommendation: 3,
    status: 'pending',
    created_at: '2024-01-21T14:30:00Z',
    event_date: '2024-01-28T18:30:00Z',
    tier_required: 'Premium'
  },
  {
    id: '2',
    expert_id: '2',
    expert_name: 'Sarah Chen',
    expert_avatar: '/avatars/expert2.jpg',
    title: 'Bitcoin to reach $50K by end of February',
    description: 'Technical analysis shows strong support at $42K with bullish momentum building. ETF approval catalysts and institutional adoption continue to drive demand.',
    category: 'Crypto',
    subcategory: 'Bitcoin',
    confidence_level: 78,
    pick_type: 'Price Target',
    odds: 2.1,
    stake_recommendation: 2,
    status: 'pending',
    created_at: '2024-01-21T10:15:00Z',
    event_date: '2024-02-29T23:59:00Z',
    tier_required: 'Basic'
  },
  {
    id: '3',
    expert_id: '1',
    expert_name: 'Michael Johnson',
    expert_avatar: '/avatars/expert1.jpg',
    title: 'Over 47.5 total points - Ravens vs Texans',
    description: 'Both teams have high-powered offenses and questionable defenses. Weather conditions favor the over, and both teams will need to score to keep up.',
    category: 'NFL',
    subcategory: 'Totals',
    confidence_level: 72,
    pick_type: 'Over/Under',
    odds: 1.87,
    stake_recommendation: 2,
    status: 'won',
    outcome: 'Final score 31-20, total 51 points',
    created_at: '2024-01-20T16:45:00Z',
    event_date: '2024-01-20T20:00:00Z',
    tier_required: 'Basic'
  }
];

const categories = ['All', 'NFL', 'NBA', 'Crypto', 'Stocks', 'Soccer'];
const confidenceFilters = [
  { value: 'all', label: 'All Confidence' },
  { value: 'high', label: 'High (80%+)' },
  { value: 'medium', label: 'Medium (60-79%)' },
  { value: 'low', label: 'Low (<60%)' }
];

export default function ExpertPickFeed() {
  const { isSignedIn } = useAuth();
  const [picks, setPicks] = useState<Pick[]>(mockPicks);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [confidenceFilter, setConfidenceFilter] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [showSubscribedOnly, setShowSubscribedOnly] = useState(false);

  // Real-time updates simulation
  useEffect(() => {
    // Simulate WebSocket connection for real-time updates
    const interval = setInterval(() => {
      // Simulate random pick updates
      if (Math.random() > 0.95) {
        console.log('Simulated real-time pick update');
        // Update pick status or add new pick
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    loadPicks();
  }, [selectedCategory, confidenceFilter, sortBy, showSubscribedOnly]);

  const loadPicks = async () => {
    setLoading(true);
    try {
      // API call would go here
      // const data = await api.experts.getPicks({
      //   category: selectedCategory !== 'All' ? selectedCategory : undefined,
      //   confidence_min: getConfidenceMin(confidenceFilter),
      //   subscribed_only: showSubscribedOnly
      // });
      // setPicks(data.picks);
      
      // For now, use mock data with filtering
      let filteredPicks = mockPicks;
      
      if (selectedCategory !== 'All') {
        filteredPicks = filteredPicks.filter(pick => pick.category === selectedCategory);
      }
      
      if (confidenceFilter !== 'all') {
        const minConfidence = getConfidenceMin(confidenceFilter);
        filteredPicks = filteredPicks.filter(pick => pick.confidence_level >= minConfidence);
      }
      
      setPicks(filteredPicks);
    } catch (error) {
      console.error('Failed to load picks:', error);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceMin = (filter: string): number => {
    switch (filter) {
      case 'high': return 80;
      case 'medium': return 60;
      case 'low': return 0;
      default: return 0;
    }
  };

  const filteredPicks = picks.filter(pick =>
    pick.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    pick.expert_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    pick.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-4xl font-bold mb-2">Expert Picks</h1>
            <p className="text-lg text-muted-foreground">
              Premium insights from verified analysts
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="px-3 py-1">
              <Zap className="w-3 h-3 mr-1" />
              Live Updates
            </Badge>
            <Badge variant="secondary" className="px-3 py-1">
              {filteredPicks.length} Picks
            </Badge>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4 items-center mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground transform -translate-y-1/2" />
            <Input
              placeholder="Search picks, experts, categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={confidenceFilter} onValueChange={setConfidenceFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Confidence" />
            </SelectTrigger>
            <SelectContent>
              {confidenceFilters.map((filter) => (
                <SelectItem key={filter.value} value={filter.value}>
                  {filter.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="confidence">Highest Confidence</SelectItem>
              <SelectItem value="odds">Best Odds</SelectItem>
              <SelectItem value="expert_rating">Top Experts</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="w-full max-w-2xl">
            {categories.map((category) => (
              <TabsTrigger key={category} value={category} className="flex-1">
                {category}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Pick Feed */}
      {loading ? (
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-6">
          {filteredPicks.map((pick) => (
            <PickCard key={pick.id} pick={pick} />
          ))}
        </div>
      )}

      {filteredPicks.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🎯</div>
          <h3 className="text-lg font-medium mb-2">No picks found</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your search or filter criteria.
          </p>
          <Button asChild>
            <Link href="/experts">Browse Experts</Link>
          </Button>
        </div>
      )}

      {/* Call to Action for Non-Subscribers */}
      {!isSignedIn && (
        <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Get Access to Premium Picks</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Subscribe to expert analysts for exclusive picks, detailed analysis, 
            and real-time notifications.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/experts">Browse Experts</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/login">Sign In</Link>
            </Button>
          </div>
        </div>
      )}
      </div>
    </AuthenticatedLayout>
  );
}

interface PickCardProps {
  pick: Pick;
}

function PickCard({ pick }: PickCardProps) {
  const { isSignedIn } = useAuth();
  const [isLiked, setIsLiked] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const isLocked = pick.tier_required === 'Premium' && !isSignedIn; // Simplified logic

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'won':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'lost':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'void':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: any } = {
      pending: 'default',
      won: 'default',
      lost: 'destructive',
      void: 'secondary'
    };

    const colors = {
      pending: 'bg-blue-100 text-blue-700 border-blue-200',
      won: 'bg-green-100 text-green-700 border-green-200',
      lost: 'bg-red-100 text-red-700 border-red-200',
      void: 'bg-yellow-100 text-yellow-700 border-yellow-200'
    };

    return (
      <Badge className={colors[status] || colors.pending}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    );
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-4">
        {/* Expert Info */}
        <div className="flex items-center justify-between">
          <Link href={`/experts/${pick.expert_id}`} className="flex items-center space-x-3 hover:opacity-80">
            <Avatar>
              <AvatarImage src={pick.expert_avatar} />
              <AvatarFallback>
                {pick.expert_name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold">{pick.expert_name}</div>
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <span>{formatDistanceToNow(new Date(pick.created_at))} ago</span>
                <span>•</span>
                <Badge variant="outline" className="text-xs">
                  {pick.category}
                </Badge>
              </div>
            </div>
          </Link>

          <div className="flex items-center gap-2">
            {getStatusBadge(pick.status)}
            {isLocked && <Lock className="h-4 w-4 text-muted-foreground" />}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Pick Title */}
        <div>
          <h3 className="text-xl font-semibold mb-2">{pick.title}</h3>
          
          {/* Pick Metrics */}
          <div className="flex flex-wrap items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className={`font-semibold ${getConfidenceColor(pick.confidence_level)}`}>
                {pick.confidence_level}% Confidence
              </span>
            </div>
            
            {pick.odds && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">@{pick.odds}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {pick.stake_recommendation}/5 units
              </span>
            </div>

            {pick.event_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {format(new Date(pick.event_date), 'MMM d, h:mm a')}
                </span>
              </div>
            )}
          </div>

          {/* Confidence Bar */}
          <div className="mb-4">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Confidence Level</span>
              <span>{pick.confidence_level}%</span>
            </div>
            <Progress 
              value={pick.confidence_level} 
              className="h-2"
            />
          </div>
        </div>

        {/* Description */}
        <div className={`${isLocked ? 'relative' : ''}`}>
          <p className={`text-muted-foreground ${isLocked ? 'blur-sm' : ''} ${
            !showFullDescription && pick.description.length > 200 ? 'line-clamp-3' : ''
          }`}>
            {pick.description}
          </p>
          
          {isLocked && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded">
              <div className="text-center">
                <Lock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm font-medium mb-2">Premium Pick</p>
                <Button size="sm" asChild>
                  <Link href={`/experts/${pick.expert_id}`}>
                    Subscribe to View
                  </Link>
                </Button>
              </div>
            </div>
          )}

          {!isLocked && pick.description.length > 200 && (
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowFullDescription(!showFullDescription)}
              className="mt-2 p-0 h-auto"
            >
              {showFullDescription ? 'Show less' : 'Read more'}
            </Button>
          )}
        </div>

        {/* Outcome (for completed picks) */}
        {pick.outcome && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Trophy className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Outcome</span>
            </div>
            <p className="text-sm">{pick.outcome}</p>
          </div>
        )}

        <Separator />

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsLiked(!isLiked)}
              className="text-muted-foreground hover:text-foreground"
            >
              <Heart className={`h-4 w-4 mr-2 ${isLiked ? 'fill-current text-red-500' : ''}`} />
              Like
            </Button>

            <Button 
              variant="ghost" 
              size="sm"
              className="text-muted-foreground hover:text-foreground"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Comment
            </Button>

            <Button 
              variant="ghost" 
              size="sm"
              className="text-muted-foreground hover:text-foreground"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>

          <Button variant="outline" size="sm" asChild>
            <Link href={`/experts/${pick.expert_id}`}>
              View Expert
              <ArrowRight className="ml-2 h-3 w-3" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}