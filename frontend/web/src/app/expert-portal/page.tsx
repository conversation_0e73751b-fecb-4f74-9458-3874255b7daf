/**
 * Expert Creator Portal
 * =====================
 * 
 * Main portal for expert analysts to manage their presence, create picks,
 * track performance, and manage subscribers.
 */

'use client';

import React, { useState, useEffect } from 'react';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import Link from 'next/link';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Button } from '@/components/ui/button';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Badge } from '@/components/ui/badge';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Input } from '@/components/ui/input';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Textarea } from '@/components/ui/textarea';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Progress } from '@/components/ui/progress';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Separator } from '@/components/ui/separator';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import {
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
  AlertCircle,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Edit,
  Eye,
  MoreHorizontal,
  Plus,
  Settings,
  Star,
  Target,
  TrendingUp,
  Users,
  XCircle,
  Trophy,
  Brain,
  Zap,
  Send,
  Save,
  Upload,
  Download,
  Award,
  MessageSquare
} from 'lucide-react';
import api from '@/lib/api-client-unified';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
// import type { Expert, Pick, ExpertAnalytics } from '@/lib/expert-analyst-api'; // TODO: Update types
import { useAuth } from '@clerk/nextjs';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import { format, formatDistanceToNow } from 'date-fns';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";

// Mock data for development
const mockExpertData: Expert = {
  id: 'expert-current',
  name: 'John Doe',
  bio: 'Professional sports analyst with 5+ years experience in NFL and NBA betting.',
  avatar_url: '/avatars/current-expert.jpg',
  specializations: ['NFL', 'NBA', 'Statistical Analysis'],
  verification_status: 'verified',
  overall_rating: 4.6,
  total_subscribers: 1850,
  total_picks: 420,
  win_rate: 0.72,
  roi: 0.18,
  streak_type: 'win',
  current_streak: 6,
  subscription_tiers: [
    {
      id: '1',
      name: 'Basic',
      price: 39.99,
      description: 'Weekly picks and basic analysis',
      features: ['3-5 weekly picks', 'Basic analysis', 'Email notifications']
    },
    {
      id: '2',
      name: 'Premium',
      price: 99.99,
      description: 'Daily picks with detailed analysis',
      features: ['Daily picks', 'In-depth analysis', 'Live chat access', 'Mobile alerts']
    }
  ],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-21T00:00:00Z'
};

const mockAnalytics: ExpertAnalytics = {
  total_revenue: 12450.00,
  monthly_revenue: 3650.00,
  subscriber_growth: 0.15,
  pick_performance: {
    total_picks: 420,
    successful_picks: 302,
    win_rate: 0.72,
    roi: 0.18
  },
  top_categories: [
    { category: 'NFL', performance: 0.78 },
    { category: 'NBA', performance: 0.68 },
    { category: 'MLB', performance: 0.65 }
  ]
};

const mockRecentPicks: Pick[] = [
  {
    id: '1',
    expert_id: 'expert-current',
    expert_name: 'John Doe',
    title: 'Lakers -4.5 vs Warriors',
    description: 'Lakers have been dominant at home and Warriors are missing key players.',
    category: 'NBA',
    confidence_level: 85,
    pick_type: 'Spread',
    odds: 1.91,
    status: 'pending',
    created_at: '2024-01-21T14:30:00Z',
    event_date: '2024-01-22T20:00:00Z',
    tier_required: 'Premium'
  },
  {
    id: '2',
    expert_id: 'expert-current',
    expert_name: 'John Doe',
    title: 'Over 215.5 total points - Celtics vs Heat',
    description: 'Both teams average high scoring games, expect a shootout.',
    category: 'NBA',
    confidence_level: 72,
    pick_type: 'Over/Under',
    odds: 1.87,
    status: 'won',
    outcome: 'Final score 118-108, total 226 points',
    created_at: '2024-01-20T16:45:00Z',
    event_date: '2024-01-20T19:30:00Z',
    tier_required: 'Basic'
  }
];

export default function ExpertPortal() {
  const { isSignedIn, user } = useAuth();
  const [expertData, setExpertData] = useState<Expert>(mockExpertData);
  const [analytics, setAnalytics] = useState<ExpertAnalytics>(mockAnalytics);
  const [recentPicks, setRecentPicks] = useState<Pick[]>(mockRecentPicks);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Pick creation form state
  const [newPick, setNewPick] = useState({
    title: '',
    description: '',
    category: '',
    confidence_level: 75,
    pick_type: '',
    odds: '',
    stake_recommendation: 2,
    event_date: '',
    tier_required: 'Basic'
  });

  useEffect(() => {
    if (isSignedIn) {
      loadExpertData();
    }
  }, [isSignedIn]);

  const loadExpertData = async () => {
    setLoading(true);
    try {
      // API calls would go here
      // const [expertProfile, expertAnalytics, picks] = await Promise.all([
      //   api.experts.getExpert('current'),
      //   api.experts.getExpertAnalytics(),
      //   api.experts.getPicks({ expert_id: 'current' })
      // ]);
      
      // For now, use mock data
      setExpertData(mockExpertData);
      setAnalytics(mockAnalytics);
      setRecentPicks(mockRecentPicks);
    } catch (error) {
      console.error('Failed to load expert data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePick = async () => {
    try {
      // await api.experts.createPick(newPick);
      console.log('Creating pick:', newPick);
      
      // Reset form
      setNewPick({
        title: '',
        description: '',
        category: '',
        confidence_level: 75,
        pick_type: '',
        odds: '',
        stake_recommendation: 2,
        event_date: '',
        tier_required: 'Basic'
      });
      
      // Reload picks
      loadExpertData();
    } catch (error) {
      console.error('Failed to create pick:', error);
    }
  };

  if (!isSignedIn) {
    return (
      <AuthenticatedLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-white">Please Sign In</h2>
          <p className="text-slate-300 mb-6">
            You need to be signed in to access the Expert Portal.
          </p>
          <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white">
            <Link href="/login">Sign In</Link>
          </Button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold mb-2 text-white">Expert Portal</h1>
            <p className="text-lg text-slate-300">
              Manage your expert profile and create premium picks
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-3 py-1 bg-slate-800/50 border-slate-600 text-slate-200">
              <Trophy className="w-3 h-3 mr-1 text-yellow-400" />
              Verified Expert
            </Badge>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Pick
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl bg-slate-900 border-slate-700">
                <DialogHeader>
                  <DialogTitle className="text-white">Create New Pick</DialogTitle>
                  <DialogDescription className="text-slate-300">
                    Share your expert analysis with your subscribers
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Pick Title</label>
                    <Input
                      placeholder="e.g., Lakers -4.5 vs Warriors"
                      value={newPick.title}
                      onChange={(e) => setNewPick(prev => ({ ...prev, title: e.target.value }))}
                      className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Category</label>
                    <Select value={newPick.category} onValueChange={(value) => setNewPick(prev => ({ ...prev, category: value }))}>
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NFL">NFL</SelectItem>
                        <SelectItem value="NBA">NBA</SelectItem>
                        <SelectItem value="MLB">MLB</SelectItem>
                        <SelectItem value="NHL">NHL</SelectItem>
                        <SelectItem value="Crypto">Crypto</SelectItem>
                        <SelectItem value="Stocks">Stocks</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Pick Type</label>
                    <Select value={newPick.pick_type} onValueChange={(value) => setNewPick(prev => ({ ...prev, pick_type: value }))}>
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Spread">Spread</SelectItem>
                        <SelectItem value="Moneyline">Moneyline</SelectItem>
                        <SelectItem value="Over/Under">Over/Under</SelectItem>
                        <SelectItem value="Prop Bet">Prop Bet</SelectItem>
                        <SelectItem value="Futures">Futures</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Odds</label>
                    <Input
                      placeholder="e.g., 1.91"
                      value={newPick.odds}
                      onChange={(e) => setNewPick(prev => ({ ...prev, odds: e.target.value }))}
                      className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Confidence Level (%)</label>
                    <Input
                      type="number"
                      min="1"
                      max="100"
                      value={newPick.confidence_level}
                      onChange={(e) => setNewPick(prev => ({ ...prev, confidence_level: parseInt(e.target.value) || 75 }))}
                      className="bg-slate-800/50 border-slate-600 text-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">Event Date</label>
                    <Input
                      type="datetime-local"
                      value={newPick.event_date}
                      onChange={(e) => setNewPick(prev => ({ ...prev, event_date: e.target.value }))}
                      className="bg-slate-800/50 border-slate-600 text-white"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">Analysis & Reasoning</label>
                  <Textarea
                    placeholder="Provide detailed analysis and reasoning for this pick..."
                    rows={4}
                    value={newPick.description}
                    onChange={(e) => setNewPick(prev => ({ ...prev, description: e.target.value }))}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                </div>
                
                <div className="flex items-center justify-between pt-4">
                  <Select value={newPick.tier_required} onValueChange={(value) => setNewPick(prev => ({ ...prev, tier_required: value }))}>
                    <SelectTrigger className="w-48 bg-slate-800/50 border-slate-600 text-white">
                      <SelectValue placeholder="Tier required" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Basic">Basic Tier</SelectItem>
                      <SelectItem value="Premium">Premium Tier</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" onClick={() => setNewPick({
                      title: '',
                      description: '',
                      category: '',
                      confidence_level: 75,
                      pick_type: '',
                      odds: '',
                      stake_recommendation: 2,
                      event_date: '',
                      tier_required: 'Basic'
                    })}>
                      Reset
                    </Button>
                    <Button onClick={handleCreatePick} disabled={!newPick.title || !newPick.description} className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Send className="mr-2 h-4 w-4" />
                      Publish Pick
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Total Subscribers</CardTitle>
            <Users className="h-4 w-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{expertData.total_subscribers.toLocaleString()}</div>
            <p className="text-xs text-slate-300">
              +{(analytics.subscriber_growth * 100).toFixed(1)}% this month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-emerald-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">${analytics.monthly_revenue.toLocaleString()}</div>
            <p className="text-xs text-slate-300">
              ${analytics.total_revenue.toLocaleString()} total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Win Rate</CardTitle>
            <Target className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">
              {(expertData.win_rate * 100).toFixed(0)}%
            </div>
            <p className="text-xs text-slate-300">
              {analytics.pick_performance.successful_picks}/{analytics.pick_performance.total_picks} picks
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Overall Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{expertData.overall_rating}</div>
            <p className="text-xs text-slate-300">
              Based on subscriber reviews
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-slate-800/50 border border-slate-600">
          <TabsTrigger value="overview" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Overview</TabsTrigger>
          <TabsTrigger value="picks" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">My Picks</TabsTrigger>
          <TabsTrigger value="subscribers" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Subscribers</TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Analytics</TabsTrigger>
          <TabsTrigger value="settings" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Summary */}
            <Card className="lg:col-span-2 bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white font-bold">Expert Profile</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src={expertData.avatar_url} />
                    <AvatarFallback className="text-lg bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                      {expertData.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-xl font-semibold text-white">{expertData.name}</h3>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-slate-200">{expertData.overall_rating}</span>
                      </div>
                      <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">Verified Expert</Badge>
                    </div>
                  </div>
                </div>
                
                <p className="text-slate-300">{expertData.bio}</p>
                
                <div className="flex flex-wrap gap-2">
                  {expertData.specializations.map((spec) => (
                    <Badge key={spec} variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">{spec}</Badge>
                  ))}
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-slate-600">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-400">
                      {expertData.current_streak}
                    </div>
                    <div className="text-sm text-slate-300">Current Streak</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-400">
                      {expertData.total_picks}
                    </div>
                    <div className="text-sm text-slate-300">Total Picks</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white font-bold">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white">
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Pick
                    </Button>
                  </DialogTrigger>
                </Dialog>
                
                <Button variant="outline" className="w-full justify-start bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" asChild>
                  <Link href="/expert-portal?tab=subscribers">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Message Subscribers
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full justify-start bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" asChild>
                  <Link href="/expert-portal?tab=analytics">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    View Analytics
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full justify-start bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" asChild>
                  <Link href="/expert-portal?tab=settings">
                    <Settings className="mr-2 h-4 w-4" />
                    Profile Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Performance */}
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Recent Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {analytics.top_categories.map((category, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold mb-2 text-white">
                      {(category.performance * 100).toFixed(0)}%
                    </div>
                    <div className="text-sm text-slate-300 mb-2">{category.category}</div>
                    <Progress value={category.performance * 100} className="h-2 bg-slate-700" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* My Picks Tab */}
        <TabsContent value="picks" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white">My Picks</h2>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Pick
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          <div className="space-y-4">
            {recentPicks.map((pick) => (
              <Card key={pick.id} className="bg-slate-900/50 border-slate-700 shadow-lg">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold text-lg text-white">{pick.title}</h3>
                        <p className="text-slate-300">{pick.description}</p>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">{pick.category}</Badge>
                        <span className="text-sm text-slate-300">{pick.confidence_level}% confidence</span>
                        {pick.odds && <span className="text-sm text-slate-300">@{pick.odds}</span>}
                        <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">{pick.tier_required}</Badge>
                      </div>
                      
                      {pick.outcome && (
                        <div className="p-3 bg-slate-800/50 border border-slate-600 rounded-lg">
                          <span className="text-sm font-medium text-white">Outcome: </span>
                          <span className="text-sm text-slate-300">{pick.outcome}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="text-right space-y-2">
                      <PickStatusBadge status={pick.status} />
                      <p className="text-sm text-slate-400">
                        {formatDistanceToNow(new Date(pick.created_at))} ago
                      </p>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="bg-slate-800/30 hover:bg-slate-700 text-slate-300 hover:text-white">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-slate-800 border-slate-600">
                          <DropdownMenuItem className="text-slate-200 hover:bg-slate-700">
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Pick
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-slate-200 hover:bg-slate-700">
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {pick.status === 'pending' && (
                            <DropdownMenuItem className="text-slate-200 hover:bg-slate-700">
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Mark as Won
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Subscribers Tab */}
        <TabsContent value="subscribers" className="space-y-6">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Subscriber Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2 text-white">Subscriber Management</h3>
                <p className="text-slate-300 mb-6">
                  View and communicate with your {expertData.total_subscribers.toLocaleString()} subscribers
                </p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Send Message to Subscribers
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white font-bold">Revenue Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Monthly Revenue</span>
                  <span className="font-semibold text-white">${analytics.monthly_revenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Total Revenue</span>
                  <span className="font-semibold text-white">${analytics.total_revenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Avg Revenue per Subscriber</span>
                  <span className="font-semibold text-white">
                    ${(analytics.total_revenue / expertData.total_subscribers).toFixed(2)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white font-bold">Pick Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Total Picks</span>
                  <span className="font-semibold text-white">{analytics.pick_performance.total_picks}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Successful Picks</span>
                  <span className="font-semibold text-green-400">
                    {analytics.pick_performance.successful_picks}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Win Rate</span>
                  <span className="font-semibold text-green-400">
                    {(analytics.pick_performance.win_rate * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">ROI</span>
                  <span className="font-semibold text-green-400">
                    +{(analytics.pick_performance.roi * 100).toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="text-white font-bold">Profile Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-12">
                <Settings className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2 text-white">Profile Settings</h3>
                <p className="text-slate-300">
                  Manage your expert profile, subscription tiers, and notification preferences
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}

function PickStatusBadge({ status }: { status: string }) {
  const variants = {
    pending: { icon: Clock, color: 'bg-blue-900/30 text-blue-400 border-blue-600' },
    won: { icon: CheckCircle, color: 'bg-green-900/30 text-green-400 border-green-600' },
    lost: { icon: XCircle, color: 'bg-red-900/30 text-red-400 border-red-600' },
    void: { icon: AlertCircle, color: 'bg-yellow-900/30 text-yellow-400 border-yellow-600' }
  };

  const variant = variants[status as keyof typeof variants] || variants.pending;
  const Icon = variant.icon;

  return (
    <Badge className={variant.color}>
      <Icon className="h-3 w-3 mr-1" />
      {status}
    </Badge>
  );
}