@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode - keeping existing for compatibility */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  /* Dark mode - BetBet gaming theme */
  .dark, :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  
  /* Default to dark theme */
  body {
    @apply bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Gaming-specific styles */
@layer components {
  /* Enhanced game card with glass morphism */
  .game-card {
    @apply card-gaming hover:scale-102 cursor-pointer;
  }
  
  .session-card {
    @apply glass rounded-xl p-4 sm:p-6 hover:glass-hover transition-all duration-300 cursor-pointer;
  }
  
  .tournament-bracket {
    @apply glass rounded-xl p-4 sm:p-6 border border-white/10;
  }
  
  .bet-slip {
    @apply glass rounded-xl p-4 sm:p-6 border border-purple-500/20 bg-purple-500/5;
  }
  
  .live-indicator {
    @apply inline-flex items-center gap-2 text-xs font-semibold text-gaming-live;
  }
  
  .pulse-dot {
    @apply w-2 h-2 bg-gaming-live rounded-full animate-pulse;
  }
  
  /* Enhanced button styles */
  .btn-primary {
    @apply bg-gradient-primary text-white font-semibold rounded-xl px-6 py-3 transition-all duration-300 hover:scale-105 hover:shadow-glow btn-hover-lift;
  }
  
  .btn-secondary {
    @apply bg-gradient-secondary text-white font-semibold rounded-xl px-6 py-3 transition-all duration-300 hover:scale-105 hover:shadow-glow btn-hover-lift;
  }
  
  .btn-glass {
    @apply glass text-white font-semibold rounded-xl px-6 py-3 transition-all duration-300 hover:glass-hover hover:scale-105 border border-white/10;
  }
  
  /* Status indicators */
  .status-live {
    @apply bg-gaming-live text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse flex items-center gap-1;
  }
  
  .status-win {
    @apply bg-gaming-win text-white text-xs px-3 py-1 rounded-full font-semibold;
  }
  
  .status-pending {
    @apply bg-gaming-pending text-white text-xs px-3 py-1 rounded-full font-semibold;
  }
  
  /* Loading states */
  .loading-card {
    @apply skeleton rounded-xl h-48;
  }
  
  .loading-text {
    @apply skeleton rounded h-4 w-3/4;
  }
  
  /* Enhanced scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-800/50 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gradient-primary rounded-full;
  }
  
  /* Gradient text animation */
  .animate-gradient-text {
    @apply bg-gradient-primary bg-[length:200%_200%] animate-gradient-shift bg-clip-text text-transparent;
  }

  /* Mobile-first responsive utilities */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-background border-t z-50 sm:hidden;
  }
  
  .mobile-nav-item {
    @apply flex flex-col items-center py-2 text-xs text-muted-foreground hover:text-foreground transition-colors;
  }
  
  .mobile-grid {
    @apply grid gap-3 sm:gap-4 lg:gap-6;
  }
  
  .mobile-card {
    @apply bg-card border rounded-lg p-3 sm:p-4 lg:p-6;
  }
  
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .mobile-margin {
    @apply mb-4 sm:mb-6 lg:mb-8;
  }
  
  .mobile-text {
    @apply text-sm sm:text-base;
  }
  
  .mobile-button {
    @apply h-8 sm:h-9 lg:h-10 text-xs sm:text-sm;
  }
  
  .mobile-drawer {
    @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm sm:hidden;
  }
  
  .mobile-drawer-content {
    @apply fixed left-0 top-0 h-full w-3/4 bg-background border-r shadow-lg;
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
  
  .mobile-fab {
    @apply fixed bottom-20 right-4 w-14 h-14 rounded-full bg-primary text-primary-foreground shadow-lg flex items-center justify-center z-40 sm:hidden;
  }

  /* Responsive text scaling */
  .responsive-title {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold;
  }
  
  .responsive-subtitle {
    @apply text-lg sm:text-xl lg:text-2xl font-semibold;
  }
  
  .responsive-body {
    @apply text-sm sm:text-base lg:text-lg;
  }

  /* Safe area for mobile devices */
  .safe-area-top {
    @apply pt-safe-top;
  }
  
  .safe-area-bottom {
    @apply pb-safe-bottom;
  }

  /* Smooth scrolling containers */
  .mobile-scroll {
    @apply overflow-x-auto scrollbar-hide;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Loading states for mobile */
  .mobile-skeleton {
    @apply animate-pulse bg-muted rounded;
  }
  
  .mobile-shimmer {
    @apply relative overflow-hidden bg-muted;
  }
  
  .mobile-shimmer::after {
    @apply absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent;
    content: '';
  }

  /* Responsive spacing utilities */
  .responsive-section {
    @apply py-8 sm:py-12 lg:py-16;
  }
  
  .responsive-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Mobile-optimized forms */
  .mobile-form-field {
    @apply space-y-2 mb-4;
  }
  
  .mobile-input {
    @apply text-base sm:text-sm; /* Prevent zoom on iOS */
  }
}

/* Utility animations */
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Responsive breakpoint indicators (development only) */
@media (max-width: 640px) {
  .debug-breakpoint::before {
    content: 'mobile';
    @apply fixed top-0 right-0 bg-red-500 text-white px-2 py-1 text-xs z-50;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .debug-breakpoint::before {
    content: 'tablet';
    @apply fixed top-0 right-0 bg-yellow-500 text-white px-2 py-1 text-xs z-50;
  }
}

@media (min-width: 769px) {
  .debug-breakpoint::before {
    content: 'desktop';
    @apply fixed top-0 right-0 bg-green-500 text-white px-2 py-1 text-xs z-50;
  }
}

/* Font loading optimization - prevent layout shift */
html {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

.font-geist-sans {
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Prevent text size adjustment on mobile */
html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}