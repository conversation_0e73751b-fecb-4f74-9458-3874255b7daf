/**
 * Custom Betting Platform - Main Market Discovery Page
 * ===================================================
 * 
 * Polymarket-style P2P betting marketplace interface.
 * Features market discovery, browsing, and search functionality.
 */

'use client';

import React, { useState, useEffect } from 'react';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { 
  MarketBrowser,
  TrendingMarkets,
  MarketStats,
  CreateBetButton
} from '@/components/custom-betting';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Search,
  TrendingUp,
  DollarSign,
  Users,
  Filter,
  Plus,
  Star,
  Clock,
  Activity
} from 'lucide-react';

export default function CustomBettingPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [selectedFilters, setSelectedFilters] = useState({
    status: 'all',
    minVolume: '',
    timeFrame: 'all'
  });

  const categories = [
    { id: 'all', name: 'All Markets', icon: Activity },
    { id: 'politics', name: 'Politics', icon: Users },
    { id: 'sports', name: 'Sports', icon: TrendingUp },
    { id: 'entertainment', name: 'Entertainment', icon: Star },
    { id: 'crypto', name: 'Crypto', icon: DollarSign },
    { id: 'tech', name: 'Technology', icon: Activity }
  ];

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">Custom Betting Markets</h1>
            <p className="text-gray-600 mt-2">
              Discover and participate in peer-to-peer betting markets on any topic
            </p>
          </div>
          
          <div className="flex gap-3">
            <CreateBetButton />
          </div>
        </div>

        {/* Platform Statistics */}
        <MarketStats />

        {/* Search and Filter Bar */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search markets, topics, or events..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Category Filter */}
              <div className="flex gap-2 overflow-x-auto">
                {categories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <Button
                      key={category.id}
                      variant={activeCategory === category.id ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCategory(category.id)}
                      className="whitespace-nowrap flex items-center gap-2"
                    >
                      <Icon className="h-4 w-4" />
                      {category.name}
                    </Button>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="browse" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="browse" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Browse
            </TabsTrigger>
            <TabsTrigger value="trending" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Trending
            </TabsTrigger>
            <TabsTrigger value="ending-soon" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Ending Soon
            </TabsTrigger>
            <TabsTrigger value="popular" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Popular
            </TabsTrigger>
          </TabsList>

          <TabsContent value="browse">
            <MarketBrowser
              searchQuery={searchQuery}
              category={activeCategory}
              filters={selectedFilters}
            />
          </TabsContent>

          <TabsContent value="trending">
            <TrendingMarkets />
          </TabsContent>

          <TabsContent value="ending-soon">
            <MarketBrowser
              searchQuery={searchQuery}
              category={activeCategory}
              filters={{ ...selectedFilters, sortBy: 'deadline' }}
              endingSoon={true}
            />
          </TabsContent>

          <TabsContent value="popular">
            <MarketBrowser
              searchQuery={searchQuery}
              category={activeCategory}
              filters={{ ...selectedFilters, sortBy: 'volume' }}
            />
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}