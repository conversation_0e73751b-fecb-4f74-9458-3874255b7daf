/**
 * Custom Betting Platform - Create Bet Page
 * =========================================
 * 
 * Page for creating new custom betting markets.
 */

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { CreateBetWizard } from '@/components/custom-betting';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@clerk/nextjs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

export default function CreateBetPage() {
  const router = useRouter();
  const { isSignedIn } = useAuth();

  const handleComplete = (betId: string) => {
    router.push(`/custom-betting/market/${betId}`);
  };

  const handleCancel = () => {
    router.push('/custom-betting');
  };

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Back Button */}
        <Button variant="outline" size="sm" asChild>
          <Link href="/custom-betting" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Markets
          </Link>
        </Button>

        {/* Auth Check */}
        {!isSignedIn ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please sign in to create a betting market.
            </AlertDescription>
          </Alert>
        ) : (
          /* Create Bet Wizard */
          <CreateBetWizard 
            onComplete={handleComplete}
            onCancel={handleCancel}
          />
        )}
      </div>
    </AuthenticatedLayout>
  );
}