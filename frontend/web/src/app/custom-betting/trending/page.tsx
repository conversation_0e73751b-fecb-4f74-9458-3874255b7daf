/**
 * Custom Betting Platform - Trending Markets Page
 * ==============================================
 * 
 * Dedicated page for trending betting markets.
 */

'use client';

import React from 'react';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { TrendingMarkets } from '@/components/custom-betting';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp,
  Flame,
  Activity
} from 'lucide-react';

export default function TrendingMarketsPage() {
  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-3">
            <Flame className="h-8 w-8 text-red-500" />
            Trending Betting Markets
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover the hottest betting markets with the most activity, 
            highest volume, and recent participant growth.
          </p>
        </div>

        {/* Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-3">
                <Flame className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="font-semibold mb-2">Real-Time Trends</h3>
              <p className="text-sm text-gray-600">
                Markets ranked by recent activity and volume growth over multiple timeframes.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">Growth Metrics</h3>
              <p className="text-sm text-gray-600">
                Based on participant growth, volume increases, and engagement levels.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Live Updates</h3>
              <p className="text-sm text-gray-600">
                Trending rankings update automatically as market activity changes.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Trending Markets Component */}
        <TrendingMarkets limit={20} showControls={true} />
      </div>
    </AuthenticatedLayout>
  );
}