/**
 * Custom Betting Platform - User Dashboard Page
 * ============================================
 * 
 * Dashboard page for managing active bets and monitoring positions.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft,
  Activity,
  TrendingUp,
  DollarSign,
  Target,
  BarChart3,
  RefreshCw,
  Plus,
  User
} from 'lucide-react';
import Link from 'next/link';
import { useApi } from '@/hooks/useApi';
import { LoadingSpinner, UserBetManager } from '@/components/custom-betting';
import { cn } from '@/lib/utils';

interface DashboardStats {
  totalExposure: number;
  potentialProfit: number;
  activeBetsValue: number;
  pendingBetsCount: number;
  matchedBetsCount: number;
  todaysPnL: number;
}

export default function UserDashboardPage() {
  const { user, isSignedIn } = useAuth();
  const router = useRouter();
  
  const [userBets, setUserBets] = useState<BetParticipant[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isSignedIn) {
      router.push('/custom-betting');
      return;
    }
    
    loadDashboardData();
  }, [isSignedIn, router]);

  const loadDashboardData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      else setLoading(true);
      
      setError(null);

      // Load user's active bets
      const betsData = await customBettingAPI.getUserBets();
      const activeBets = betsData.filter(b => b.status === 'active' || b.status === 'pending');
      setUserBets(activeBets);

      // Calculate dashboard statistics
      calculateDashboardStats(activeBets);

    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateDashboardStats = (bets: BetParticipant[]) => {
    const stats: DashboardStats = {
      totalExposure: 0,
      potentialProfit: 0,
      activeBetsValue: 0,
      pendingBetsCount: 0,
      matchedBetsCount: 0,
      todaysPnL: 0
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    bets.forEach(bet => {
      // Calculate exposure (risk)
      if (bet.position_type === 'backing') {
        stats.totalExposure += bet.stake_amount;
        stats.potentialProfit += (bet.stake_amount * bet.desired_odds) - bet.stake_amount;
      } else {
        // For laying, exposure is the liability
        const liability = (bet.desired_odds - 1) * bet.stake_amount;
        stats.totalExposure += liability;
        stats.potentialProfit += bet.stake_amount;
      }

      // Active bets value
      stats.activeBetsValue += bet.stake_amount;

      // Count pending vs matched
      if (bet.status === 'pending') {
        stats.pendingBetsCount++;
      } else if (bet.status === 'active') {
        stats.matchedBetsCount++;
      }

      // Calculate today's P&L (for settled bets today)
      const betDate = new Date(bet.created_at);
      if (betDate >= today) {
        if (bet.status === 'won' && bet.payout_amount) {
          stats.todaysPnL += bet.payout_amount - bet.stake_amount;
        } else if (bet.status === 'lost') {
          stats.todaysPnL -= bet.stake_amount;
        }
      }
    });

    setDashboardStats(stats);
  };

  const handleBetCancelled = (betId: string) => {
    // Remove cancelled bet from the list
    setUserBets(prev => prev.filter(b => b.id !== betId));
    // Recalculate stats
    calculateDashboardStats(userBets.filter(b => b.id !== betId));
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner 
            fullPage 
            text="Loading dashboard..." 
            size="lg" 
          />
        </div>
      </AuthenticatedLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/custom-betting" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back
              </Link>
            </Button>
            
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <Activity className="h-6 w-6 text-primary" />
                Betting Dashboard
              </h1>
              <p className="text-gray-600">Manage your active positions</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/custom-betting/profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => loadDashboardData(true)}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-4">
          <Button asChild>
            <Link href="/custom-betting" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Browse Markets
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/custom-betting/create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Market
            </Link>
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Dashboard Stats */}
        {dashboardStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Exposure */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-red-600" />
                  Total Exposure
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  ${dashboardStats.totalExposure.toFixed(2)}
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  Maximum potential loss
                </p>
              </CardContent>
            </Card>

            {/* Potential Profit */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  Potential Profit
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${dashboardStats.potentialProfit.toFixed(2)}
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  If all bets win
                </p>
              </CardContent>
            </Card>

            {/* Active Bets Value */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-blue-600" />
                  Active Value
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${dashboardStats.activeBetsValue.toFixed(2)}
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  Total stakes in play
                </p>
              </CardContent>
            </Card>

            {/* Today's P&L */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4 text-purple-600" />
                  Today's P&L
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "text-2xl font-bold",
                  dashboardStats.todaysPnL >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {dashboardStats.todaysPnL >= 0 ? '+' : ''}${Math.abs(dashboardStats.todaysPnL).toFixed(2)}
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  Realized today
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Bet Status Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Bet Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-sm">
                  Pending ({dashboardStats?.pendingBetsCount || 0})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">
                  Matched ({dashboardStats?.matchedBetsCount || 0})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm">
                  Total Active ({userBets.length})
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Bet Manager */}
        <UserBetManager 
          userBets={userBets}
          onBetCancelled={handleBetCancelled}
          onRefresh={() => loadDashboardData(true)}
        />
      </div>
    </AuthenticatedLayout>
  );
}