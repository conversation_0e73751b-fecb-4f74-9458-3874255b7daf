/**
 * Custom Betting Platform - Market Detail Page
 * ===========================================
 * 
 * Individual betting market page with detailed information,
 * betting interface, and real-time updates.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { LoadingSpinner, LiveNotifications } from '@/components/custom-betting';
import { MarketHeader } from '@/components/custom-betting/MarketHeader';
import { MarketOutcomes } from '@/components/custom-betting/MarketOutcomes';
import { MarketActivity } from '@/components/custom-betting/MarketActivity';
import { MarketInfo } from '@/components/custom-betting/MarketInfo';
import { BettingInterface } from '@/components/custom-betting/BettingInterface';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { useApi } from '@/hooks/useApi';
import { useAuth } from '@clerk/nextjs';
import { useCustomBettingWebSocket, useMarketUpdates } from '@/hooks/useCustomBettingWebSocket';

export default function MarketDetailPage() {
  const params = useParams();
  const { isSignedIn } = useAuth();
  const marketId = params.id as string;

  const [market, setMarket] = useState<CustomBet | null>(null);
  const [userParticipation, setUserParticipation] = useState<BetParticipant | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // WebSocket connection for real-time updates
  const { isConnected, connectionError } = useCustomBettingWebSocket({ 
    marketId,
    enabled: !!marketId && !!market
  });
  
  // Real-time market updates
  const liveMarketData = useMarketUpdates(marketId);

  const loadMarketData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      else setLoading(true);
      
      setError(null);

      // Load market details
      const marketData = await customBettingAPI.getBet(marketId);
      setMarket(marketData);

      // Load user participation if signed in
      if (isSignedIn) {
        try {
          const participation = await customBettingAPI.getUserParticipation(marketId);
          setUserParticipation(participation);
        } catch (err) {
          // User not participating - this is fine
          setUserParticipation(null);
        }
      }

    } catch (err) {
      console.error('Failed to load market:', err);
      setError('Failed to load market details. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (marketId) {
      loadMarketData();
    }
  }, [marketId, isSignedIn]);

  // Apply real-time updates to market data
  useEffect(() => {
    if (liveMarketData && market) {
      setMarket(prevMarket => {
        if (!prevMarket) return prevMarket;
        
        return {
          ...prevMarket,
          total_stakes: liveMarketData.total_stakes,
          total_participants: liveMarketData.total_participants,
          status: liveMarketData.status,
          outcomes: prevMarket.outcomes.map(outcome => {
            const liveOutcome = liveMarketData.outcomes.find(lo => lo.id === outcome.id);
            return liveOutcome ? { ...outcome, ...liveOutcome } : outcome;
          })
        };
      });
    }
  }, [liveMarketData, market]);

  const handleRefresh = () => {
    loadMarketData(true);
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner 
            fullPage 
            text="Loading market details..." 
            size="lg" 
          />
        </div>
      </AuthenticatedLayout>
    );
  }

  if (error || !market) {
    return (
      <AuthenticatedLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Back Button */}
          <Button variant="outline" size="sm" asChild>
            <Link href="/custom-betting" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Markets
            </Link>
          </Button>

          {/* Error State */}
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-red-600 mb-6">
                <AlertCircle className="h-16 w-16 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">
                  {error || 'Market Not Found'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {error || 'The requested betting market could not be found.'}
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={handleRefresh}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/custom-betting">
                    Browse Markets
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  const isMarketActive = market.status === 'open' || market.status === 'active';
  const canBet = isSignedIn && isMarketActive;

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Back Button and Refresh */}
        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm" asChild>
            <Link href="/custom-betting" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Markets
            </Link>
          </Button>

          <div className="flex items-center gap-2">
            {/* WebSocket Connection Status */}
            {isConnected ? (
              <div className="flex items-center gap-1 text-green-600 text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                Live
              </div>
            ) : connectionError ? (
              <div className="flex items-center gap-1 text-red-600 text-xs">
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                Offline
              </div>
            ) : (
              <div className="flex items-center gap-1 text-gray-500 text-xs">
                <div className="w-2 h-2 bg-gray-400 rounded-full" />
                Connecting...
              </div>
            )}

            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Market Header */}
        <MarketHeader 
          market={market} 
          userParticipation={userParticipation}
          onRefresh={handleRefresh}
        />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Market Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Betting Not Available Alert */}
            {!canBet && isMarketActive && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please sign in to participate in this betting market.
                </AlertDescription>
              </Alert>
            )}

            {/* Market Outcomes */}
            <MarketOutcomes 
              market={market}
              userParticipation={userParticipation}
              canBet={canBet}
              onBetPlaced={() => loadMarketData(true)}
            />

            {/* Market Activity */}
            <MarketActivity 
              marketId={market.id}
            />
          </div>

          {/* Right Column - Betting Interface & Info */}
          <div className="space-y-6">
            {/* Betting Interface */}
            {canBet && (
              <BettingInterface
                market={market}
                userParticipation={userParticipation}
                onBetPlaced={() => loadMarketData(true)}
              />
            )}

            {/* Market Information */}
            <MarketInfo 
              market={market}
            />
          </div>
        </div>

        {/* Live Notifications */}
        <LiveNotifications marketId={market.id} />
      </div>
    </AuthenticatedLayout>
  );
}