/**
 * Custom Betting Platform - User Profile Page
 * ==========================================
 * 
 * User profile page showing betting history, stats, and account management.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft,
  User,
  TrendingUp,
  DollarSign,
  Activity,
  Trophy,
  Target,
  Clock,
  BarChart3,
  Calendar,
  ExternalLink,
  RefreshCw,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { useApi } from '@/hooks/useApi';
import { LoadingSpinner, EmptyState } from '@/components/custom-betting';
import { cn } from '@/lib/utils';

interface UserStats {
  total_bets_placed: number;
  total_bets_won: number;
  total_bets_lost: number;
  total_stake_amount: number;
  total_winnings: number;
  total_losses: number;
  win_rate: number;
  roi: number;
  active_bets: number;
  settled_bets: number;
}

export default function UserProfilePage() {
  const { user, isSignedIn } = useAuth();
  const router = useRouter();
  
  const [userBets, setUserBets] = useState<BetParticipant[]>([]);
  const [createdMarkets, setCreatedMarkets] = useState<CustomBet[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    if (!isSignedIn) {
      router.push('/custom-betting');
      return;
    }
    
    loadUserData();
  }, [isSignedIn, router]);

  const loadUserData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      else setLoading(true);
      
      setError(null);

      // Load user's betting history
      const betsData = await customBettingAPI.getUserBets();
      setUserBets(betsData);

      // Load markets created by user
      const marketsData = await customBettingAPI.getUserCreatedBets();
      setCreatedMarkets(marketsData);

      // Calculate user statistics
      calculateUserStats(betsData);

    } catch (err) {
      console.error('Failed to load user data:', err);
      setError('Failed to load profile data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateUserStats = (bets: BetParticipant[]) => {
    const stats: UserStats = {
      total_bets_placed: bets.length,
      total_bets_won: bets.filter(b => b.status === 'won').length,
      total_bets_lost: bets.filter(b => b.status === 'lost').length,
      total_stake_amount: bets.reduce((sum, b) => sum + b.stake_amount, 0),
      total_winnings: bets.filter(b => b.status === 'won').reduce((sum, b) => sum + (b.payout_amount || 0), 0),
      total_losses: bets.filter(b => b.status === 'lost').reduce((sum, b) => sum + b.stake_amount, 0),
      win_rate: 0,
      roi: 0,
      active_bets: bets.filter(b => b.status === 'active' || b.status === 'pending').length,
      settled_bets: bets.filter(b => b.status === 'won' || b.status === 'lost').length
    };

    // Calculate win rate
    if (stats.settled_bets > 0) {
      stats.win_rate = (stats.total_bets_won / stats.settled_bets) * 100;
    }

    // Calculate ROI
    if (stats.total_stake_amount > 0) {
      const netProfit = stats.total_winnings - stats.total_stake_amount;
      stats.roi = (netProfit / stats.total_stake_amount) * 100;
    }

    setUserStats(stats);
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, { variant: any; className: string }> = {
      'active': { variant: 'default', className: 'bg-blue-100 text-blue-800' },
      'pending': { variant: 'outline', className: 'bg-yellow-100 text-yellow-800' },
      'won': { variant: 'default', className: 'bg-green-100 text-green-800' },
      'lost': { variant: 'outline', className: 'bg-red-100 text-red-800' },
      'cancelled': { variant: 'outline', className: 'bg-gray-100 text-gray-800' }
    };

    const config = variants[status] || variants['active'];
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filterBetsByStatus = (status: string) => {
    switch (status) {
      case 'active':
        return userBets.filter(b => b.status === 'active' || b.status === 'pending');
      case 'won':
        return userBets.filter(b => b.status === 'won');
      case 'lost':
        return userBets.filter(b => b.status === 'lost');
      case 'all':
      default:
        return userBets;
    }
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner 
            fullPage 
            text="Loading profile..." 
            size="lg" 
          />
        </div>
      </AuthenticatedLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm" asChild>
            <Link href="/custom-betting" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Markets
            </Link>
          </Button>

          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => loadUserData(true)}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Profile Header */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-6">
              <Avatar className="h-20 w-20">
                <AvatarImage src={user.imageUrl} />
                <AvatarFallback>
                  <User className="h-10 w-10" />
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <h1 className="text-2xl font-bold">
                  {user.fullName || user.emailAddresses?.[0]?.emailAddress || 'User'}
                </h1>
                <p className="text-gray-600">
                  Member since {new Date(user.createdAt).toLocaleDateString()}
                </p>
              </div>

              <div className="text-right">
                <div className="text-sm text-gray-600">Total Markets</div>
                <div className="text-2xl font-bold">{createdMarkets.length}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        {userStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Win Rate */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Trophy className="h-4 w-4 text-yellow-600" />
                  Win Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {userStats.win_rate.toFixed(1)}%
                </div>
                <Progress 
                  value={userStats.win_rate} 
                  className="mt-2 h-2"
                />
                <p className="text-xs text-gray-600 mt-2">
                  {userStats.total_bets_won} won / {userStats.settled_bets} settled
                </p>
              </CardContent>
            </Card>

            {/* ROI */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  Return on Investment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "text-2xl font-bold",
                  userStats.roi >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {userStats.roi >= 0 ? '+' : ''}{userStats.roi.toFixed(1)}%
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  ${userStats.total_winnings.toFixed(2)} returns
                </p>
              </CardContent>
            </Card>

            {/* Total Stake */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-blue-600" />
                  Total Staked
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${userStats.total_stake_amount.toFixed(2)}
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  Across {userStats.total_bets_placed} bets
                </p>
              </CardContent>
            </Card>

            {/* Active Bets */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4 text-purple-600" />
                  Active Bets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {userStats.active_bets}
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  Currently in progress
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Betting History Tabs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Betting History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="active">
                  Active ({filterBetsByStatus('active').length})
                </TabsTrigger>
                <TabsTrigger value="won">
                  Won ({filterBetsByStatus('won').length})
                </TabsTrigger>
                <TabsTrigger value="lost">
                  Lost ({filterBetsByStatus('lost').length})
                </TabsTrigger>
                <TabsTrigger value="all">
                  All ({userBets.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {filterBetsByStatus(activeTab).length > 0 ? (
                  <div className="space-y-4">
                    {filterBetsByStatus(activeTab).map((bet) => (
                      <div 
                        key={bet.id} 
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">Market #{bet.bet_id}</h4>
                            {getStatusBadge(bet.status)}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              {bet.position_type}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              ${bet.stake_amount}
                            </span>
                            <span className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              {bet.desired_odds}x
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(bet.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                          {bet.status === 'won' && bet.payout_amount && (
                            <div className="text-right">
                              <div className="text-xs text-gray-600">Payout</div>
                              <div className="font-bold text-green-600">
                                +${bet.payout_amount.toFixed(2)}
                              </div>
                            </div>
                          )}
                          
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/custom-betting/market/${bet.bet_id}`}>
                              <ExternalLink className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title={`No ${activeTab === 'all' ? '' : activeTab} bets`}
                    description={`You don't have any ${activeTab === 'all' ? '' : activeTab} bets yet.`}
                    action={
                      <Button asChild>
                        <Link href="/custom-betting">
                          Browse Markets
                        </Link>
                      </Button>
                    }
                  />
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Created Markets */}
        {createdMarkets.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Your Created Markets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {createdMarkets.slice(0, 5).map((market) => (
                  <div 
                    key={market.id} 
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium mb-1">{market.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {market.total_participants} participants
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          ${market.total_stakes.toFixed(2)} volume
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {market.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/custom-betting/market/${market.id}`}>
                        View Market
                      </Link>
                    </Button>
                  </div>
                ))}
                
                {createdMarkets.length > 5 && (
                  <div className="text-center pt-2">
                    <Button variant="outline" size="sm">
                      View All {createdMarkets.length} Markets
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AuthenticatedLayout>
  );
}