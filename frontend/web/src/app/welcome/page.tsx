/**
 * BetBet Gaming Engine - Welcome Page
 * ===================================
 * 
 * Onboarding page for new users with tutorial and quick start.
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  GamepadIcon,
  Users,
  Trophy,
  DollarSign,
  Play,
  Eye,
  CheckCircle,
  ArrowRight,
  Star,
  Zap,
  Target,
  Gift,
  BookOpen,
} from 'lucide-react';
import { useAuth } from '@/store/gameStore';
import { useApi } from '@/hooks/useApi';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  completed: boolean;
  action?: {
    label: string;
    href: string;
  };
}

export default function WelcomePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const api = useApi();
  const [onboardingProgress, setOnboardingProgress] = useState(0);
  const [featuredGames, setFeaturedGames] = useState<any[]>([]);
  
  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
  }, [isAuthenticated, router]);

  // Load featured games for quick start
  useEffect(() => {
    const loadFeaturedGames = async () => {
      try {
        const response = await api.gaming.getGames({ is_featured: true, limit: 3 });
        setFeaturedGames(response);
      } catch (error) {
        console.error('Failed to load featured games:', error);
      }
    };

    loadFeaturedGames();
  }, []);

  const onboardingSteps: OnboardingStep[] = [
    {
      id: 'profile',
      title: 'Complete Your Profile',
      description: 'Add a bio and customize your gaming identity',
      icon: Users,
      completed: !!user?.bio,
      action: {
        label: 'Edit Profile',
        href: '/profile'
      }
    },
    {
      id: 'game',
      title: 'Play Your First Game',
      description: 'Join a session or create your own to start gaming',
      icon: GamepadIcon,
      completed: false, // TODO: Check if user has played a game
      action: {
        label: 'Browse Games',
        href: '/games'
      }
    },
    {
      id: 'tournament',
      title: 'Explore Tournaments',
      description: 'Compete in brackets for bigger prizes and rankings',
      icon: Trophy,
      completed: false, // TODO: Check if user has joined a tournament
      action: {
        label: 'View Tournaments',
        href: '/tournaments'
      }
    },
    {
      id: 'spectate',
      title: 'Try Spectator Betting',
      description: 'Watch live games and bet on your favorite players',
      icon: Eye,
      completed: false, // TODO: Check if user has placed a bet
      action: {
        label: 'Spectate Games',
        href: '/spectate'
      }
    }
  ];

  const completedSteps = onboardingSteps.filter(step => step.completed).length;
  const progressPercentage = (completedSteps / onboardingSteps.length) * 100;

  const tips = [
    {
      icon: Target,
      title: 'Start with Practice Mode',
      description: 'Try games in practice mode before playing for real money'
    },
    {
      icon: Zap,
      title: 'Join Active Sessions',
      description: 'Look for live sessions with available slots for quick games'
    },
    {
      icon: Star,
      title: 'Follow Top Players',
      description: 'Watch skilled players to learn strategies and techniques'
    },
    {
      icon: Gift,
      title: 'Daily Rewards',
      description: 'Log in daily to claim bonuses and free tournament entries'
    }
  ];

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        
        {/* Header */}
        <div className="text-center space-y-4 mb-8">
          <div className="flex items-center justify-center space-x-2">
            <GamepadIcon className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-4xl font-bold">Welcome to BetBet, {user.username}!</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            You're all set to start your gaming journey. Let's get you familiar with the platform.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Onboarding Progress */}
          <div className="lg:col-span-2 space-y-6">
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Getting Started</span>
                </CardTitle>
                <CardDescription>
                  Complete these steps to unlock the full BetBet experience
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-muted-foreground">
                      {completedSteps} of {onboardingSteps.length} completed
                    </span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                </div>

                {/* Onboarding Steps */}
                <div className="space-y-4">
                  {onboardingSteps.map((step, index) => (
                    <div key={step.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className={`p-2 rounded-full ${step.completed ? 'bg-green-100 text-green-600' : 'bg-muted text-muted-foreground'}`}>
                        {step.completed ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <step.icon className="h-5 w-5" />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-semibold">{step.title}</h3>
                        <p className="text-sm text-muted-foreground">{step.description}</p>
                      </div>
                      
                      {step.action && !step.completed && (
                        <Button size="sm" variant="outline" asChild>
                          <Link href={step.action.href}>
                            {step.action.label}
                          </Link>
                        </Button>
                      )}
                      
                      {step.completed && (
                        <Badge variant="secondary">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Done
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>

                {completedSteps === onboardingSteps.length && (
                  <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
                    <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                    <h3 className="text-lg font-semibold text-green-800 mb-2">
                      Congratulations! 🎉
                    </h3>
                    <p className="text-green-700 mb-4">
                      You've completed the onboarding process. You're ready to dominate the gaming arena!
                    </p>
                    <Button asChild>
                      <Link href="/">
                        <GamepadIcon className="h-4 w-4 mr-2" />
                        Start Gaming
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Start Games */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Start</CardTitle>
                <CardDescription>
                  Jump into these popular games to get started
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {featuredGames.slice(0, 3).map((game: any) => (
                    <Card key={game.id} className="game-card">
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <h3 className="font-semibold">{game.name}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {game.description}
                          </p>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{game.min_players}-{game.max_players} players</span>
                            <span>{game.estimated_duration_minutes} min</span>
                          </div>
                          <Button size="sm" className="w-full" asChild>
                            <Link href={`/games/${game.id}`}>
                              <Play className="h-3 w-3 mr-2" />
                              Play Now
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Pro Tips */}
            <Card>
              <CardHeader>
                <CardTitle>Pro Tips</CardTitle>
                <CardDescription>
                  Expert advice to help you succeed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {tips.map((tip, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="p-1 rounded-full bg-primary/10 text-primary mt-1">
                      <tip.icon className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{tip.title}</h4>
                      <p className="text-xs text-muted-foreground">{tip.description}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" asChild>
                  <Link href="/games">
                    <GamepadIcon className="h-4 w-4 mr-2" />
                    Create Session
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/sessions">
                    <Users className="h-4 w-4 mr-2" />
                    Join Session
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/tournaments">
                    <Trophy className="h-4 w-4 mr-2" />
                    View Tournaments
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Welcome Bonus */}
            <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
              <CardContent className="p-6 text-center">
                <Gift className="h-12 w-12 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-purple-800 mb-2">Welcome Bonus!</h3>
                <p className="text-sm text-purple-700 mb-4">
                  Get $10 free credits and 3 tournament entries to start your journey.
                </p>
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  Already Claimed!
                </Badge>
              </CardContent>
            </Card>

            {/* Skip to Dashboard */}
            <Button variant="outline" className="w-full" asChild>
              <Link href="/">
                Skip to Dashboard
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}