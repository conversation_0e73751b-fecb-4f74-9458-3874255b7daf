/**
 * BetBet Gaming Engine - Spectator Hub
 * ====================================
 * 
 * Browse live games available for spectating and betting.
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  Eye,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  Zap,
  Trophy,
  Star,
  BarChart3,
  Activity,
  Timer,
  GamepadIcon,
  Search,
  Filter,
  PlayCircle,
  AlertCircle,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import { GameSession, SessionStatus } from '@/lib/api';
import { cn } from '@/lib/utils';
import { BettingMarkets } from '@/components/betting/BettingMarkets';

// Spectator types
interface SpectatorSession extends GameSession {
  betting_enabled: boolean;
  total_bets: number;
  betting_pool: number;
  viewer_count: number;
  featured: boolean;
  odds?: BettingOdds[];
}

interface BettingOdds {
  participant_id: string;
  participant_name: string;
  odds: number;
  probability: number;
  bet_count: number;
  bet_volume: number;
}

type SpectatorFilter = 'all' | 'live' | 'featured' | 'high_stakes' | 'tournaments';

export default function SpectatePage() {
  const { isSignedIn } = useAuth();
  const api = useApi();
  const [sessions, setSessions] = useState<SpectatorSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<SpectatorFilter>('all');
  const [selectedGame, setSelectedGame] = useState<string>('all');

  // Generate mock spectator sessions
  useEffect(() => {
    const generateMockSessions = (): SpectatorSession[] => {
      const games = ['Trivia Master', 'Chess Pro', 'Quick Draw', 'Poker Night', 'Strategy Wars'];
      const players = [
        'ProGamer123', 'NinjaWarrior', 'StrategyKing', 'QuickShot', 'MindMaster',
        'LuckyAce', 'TacticalGenius', 'SpeedDemon', 'ChessChampion', 'Rookie2Pro'
      ];

      return Array.from({ length: 12 }, (_, i) => {
        const player1 = players[Math.floor(Math.random() * players.length)];
        const player2 = players[Math.floor(Math.random() * players.length)];
        const isLive = i < 8;
        const isTournament = i % 3 === 0;
        
        return {
          id: `spec-session-${i + 1}`,
          game_id: `game-${i % 5}`,
          game_name: games[i % games.length],
          session_name: isTournament 
            ? `Tournament Round ${Math.floor(Math.random() * 3) + 1}`
            : `${player1} vs ${player2}`,
          status: isLive ? SessionStatus.ACTIVE : SessionStatus.WAITING,
          entry_fee: Math.floor(Math.random() * 50) + 10,
          min_participants: 2,
          max_participants: isTournament ? 32 : 2,
          current_participants: isTournament ? 28 : 2,
          current_spectators: Math.floor(Math.random() * 200) + 10,
          scheduled_start_time: new Date(Date.now() + Math.random() * 3600000).toISOString(),
          estimated_duration_minutes: 15,
          total_prize_pool: Math.floor(Math.random() * 1000) + 100,
          allow_spectators: true,
          spectator_chat_enabled: true,
          is_ranked: true,
          is_tournament_session: isTournament,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          // Spectator-specific fields
          betting_enabled: true,
          total_bets: Math.floor(Math.random() * 100) + 20,
          betting_pool: Math.floor(Math.random() * 5000) + 500,
          viewer_count: Math.floor(Math.random() * 500) + 50,
          featured: i < 3,
          odds: [
            {
              participant_id: '1',
              participant_name: player1,
              odds: 1.85,
              probability: 54,
              bet_count: Math.floor(Math.random() * 50) + 10,
              bet_volume: Math.floor(Math.random() * 2000) + 200,
            },
            {
              participant_id: '2',
              participant_name: player2,
              odds: 2.10,
              probability: 46,
              bet_count: Math.floor(Math.random() * 40) + 10,
              bet_volume: Math.floor(Math.random() * 1500) + 200,
            },
          ],
        };
      });
    };

    setTimeout(() => {
      setSessions(generateMockSessions());
      setLoading(false);
    }, 1000);
  }, []);

  const filteredSessions = sessions.filter(session => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!session.session_name.toLowerCase().includes(query) && 
          !session.game_name.toLowerCase().includes(query)) {
        return false;
      }
    }

    switch (selectedFilter) {
      case 'live':
        return session.status === SessionStatus.ACTIVE;
      case 'featured':
        return session.featured;
      case 'high_stakes':
        return session.betting_pool > 2000;
      case 'tournaments':
        return session.is_tournament_session;
      case 'all':
      default:
        return true;
    }
  });

  const SpectatorCard = ({ session }: { session: SpectatorSession }) => {
    const isLive = session.status === SessionStatus.ACTIVE;
    const favoriteOdds = session.odds?.reduce((prev, current) => 
      prev.probability > current.probability ? prev : current
    );

    return (
      <Card className="hover:shadow-lg transition-shadow bg-slate-900/50 border-slate-700">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center space-x-2 text-white font-bold">
                <span>{session.session_name}</span>
                {isLive && (
                  <div className="flex items-center space-x-1">
                    <div className="pulse-dot" />
                    <span className="live-indicator text-red-400 font-medium">LIVE</span>
                  </div>
                )}
              </CardTitle>
              <CardDescription className="flex items-center space-x-2 text-slate-300">
                <GamepadIcon className="h-4 w-4" />
                <span>{session.game_name}</span>
                {session.is_tournament_session && (
                  <>
                    <span className="text-slate-400">•</span>
                    <Trophy className="h-4 w-4 text-yellow-400" />
                    <span>Tournament</span>
                  </>
                )}
              </CardDescription>
            </div>
            {session.featured && (
              <Badge variant="default" className="bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-500">
                <Star className="h-3 w-3 mr-1" />
                Featured
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Viewer Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="flex items-center justify-center space-x-1">
                <Eye className="h-4 w-4 text-blue-400" />
                <span className="text-lg font-semibold text-white">{session.viewer_count}</span>
              </div>
              <p className="text-xs text-slate-400">Viewers</p>
            </div>
            <div>
              <div className="flex items-center justify-center space-x-1">
                <DollarSign className="h-4 w-4 text-emerald-400" />
                <span className="text-lg font-semibold text-white">${session.betting_pool}</span>
              </div>
              <p className="text-xs text-slate-400">Bet Pool</p>
            </div>
            <div>
              <div className="flex items-center justify-center space-x-1">
                <BarChart3 className="h-4 w-4 text-purple-400" />
                <span className="text-lg font-semibold text-white">{session.total_bets}</span>
              </div>
              <p className="text-xs text-slate-400">Bets</p>
            </div>
          </div>

          {/* Betting Odds */}
          {session.odds && session.odds.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-300 font-medium">Current Odds</span>
                {favoriteOdds && (
                  <Badge variant="secondary" className="text-xs bg-slate-700 text-slate-200 border-slate-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {favoriteOdds.participant_name} favored
                  </Badge>
                )}
              </div>
              
              {session.odds.map((odd) => (
                <div key={odd.participant_id} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium text-white">{odd.participant_name}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">{odd.odds.toFixed(2)}x</Badge>
                      <span className="text-xs text-slate-400">
                        {odd.probability}%
                      </span>
                    </div>
                  </div>
                  <Progress value={odd.probability} className="h-2" />
                </div>
              ))}
            </div>
          )}

          {/* Match Info */}
          <div className="flex items-center justify-between text-sm text-slate-300">
            <div className="flex items-center space-x-1">
              <Trophy className="h-3 w-3 text-yellow-400" />
              <span>Prize: ${session.total_prize_pool}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3 text-blue-400" />
              <span>{session.estimated_duration_minutes}m</span>
            </div>
          </div>

          {/* Betting Section */}
          {session.betting_enabled && isSignedIn && (
            <div className="pt-2 border-t border-slate-600">
              <BettingMarkets 
                sessionId={session.id}
                showLiveUpdates={isLive}
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-2">
            <Button variant="outline" size="sm" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" asChild>
              <Link href={`/spectate/${session.id}`}>
                <Activity className="h-4 w-4 mr-2" />
                View Stats
              </Link>
            </Button>
            
            {isLive ? (
              <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white" asChild>
                <Link href={`/spectate/${session.id}/live`}>
                  <Eye className="h-4 w-4 mr-2" />
                  Watch Live
                </Link>
              </Button>
            ) : (
              <div className="text-xs text-slate-400">
                <Timer className="h-3 w-3 inline mr-1" />
                Starts in {Math.floor(Math.random() * 30) + 1}m
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center space-x-2 text-white">
              <Eye className="h-8 w-8 text-blue-400" />
              <span>Spectator Hub</span>
            </h1>
            <p className="text-slate-300">
              Watch live games and place bets on your favorite players
            </p>
          </div>
          
          {!isSignedIn && (
            <div className="flex items-center space-x-2 p-3 bg-yellow-900/30 border border-yellow-600 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-400" />
              <span className="text-sm text-yellow-200">Sign in to place bets</span>
            </div>
          )}
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Live Games</p>
                  <p className="text-2xl font-bold text-white">
                    {sessions.filter(s => s.status === SessionStatus.ACTIVE).length}
                  </p>
                </div>
                <PlayCircle className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Total Viewers</p>
                  <p className="text-2xl font-bold text-white">
                    {sessions.reduce((acc, s) => acc + s.viewer_count, 0).toLocaleString()}
                  </p>
                </div>
                <Eye className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Active Bets</p>
                  <p className="text-2xl font-bold text-white">
                    {sessions.reduce((acc, s) => acc + s.total_bets, 0).toLocaleString()}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Total Pool</p>
                  <p className="text-2xl font-bold text-white">
                    ${sessions.reduce((acc, s) => acc + s.betting_pool, 0).toLocaleString()}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-emerald-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 text-slate-400 transform -translate-y-1/2" />
              <Input
                placeholder="Search games or players..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
              />
            </div>
          </div>
          
          <Select value={selectedGame} onValueChange={setSelectedGame}>
            <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600 text-white">
              <SelectValue placeholder="All Games" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Games</SelectItem>
              <SelectItem value="trivia">Trivia Master</SelectItem>
              <SelectItem value="chess">Chess Pro</SelectItem>
              <SelectItem value="reaction">Quick Draw</SelectItem>
              <SelectItem value="poker">Poker Night</SelectItem>
              <SelectItem value="strategy">Strategy Wars</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Session Tabs */}
        <Tabs value={selectedFilter} onValueChange={(value) => setSelectedFilter(value as SpectatorFilter)}>
          <TabsList className="w-full justify-start bg-slate-800/50 border border-slate-600">
            <TabsTrigger value="all" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">All Sessions</TabsTrigger>
            <TabsTrigger value="live" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">
              <div className="flex items-center space-x-2">
                <div className="pulse-dot" />
                <span>Live Now</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="featured" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">
              <Star className="h-4 w-4 mr-2" />
              Featured
            </TabsTrigger>
            <TabsTrigger value="high_stakes" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">
              <Zap className="h-4 w-4 mr-2" />
              High Stakes
            </TabsTrigger>
            <TabsTrigger value="tournaments" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">
              <Trophy className="h-4 w-4 mr-2" />
              Tournaments
            </TabsTrigger>
          </TabsList>

          <TabsContent value={selectedFilter} className="mt-6">
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse bg-slate-900/50 border-slate-700">
                    <CardHeader>
                      <div className="h-6 bg-slate-700 rounded w-3/4"></div>
                      <div className="h-4 bg-slate-700 rounded w-1/2 mt-2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-4">
                          {[...Array(3)].map((_, j) => (
                            <div key={j} className="h-8 bg-slate-700 rounded"></div>
                          ))}
                        </div>
                        <div className="h-16 bg-slate-700 rounded"></div>
                        <div className="h-8 bg-slate-700 rounded"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredSessions.length === 0 ? (
              <div className="text-center py-12">
                <Eye className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2 text-white">No sessions found</h3>
                <p className="text-slate-300">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredSessions.map((session) => (
                  <SpectatorCard key={session.id} session={session} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}