/**
 * BetBet Gaming Engine - Live Spectator View
 * ==========================================
 * 
 * Real-time game spectating with live betting interface.
 */

'use client';

import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import MainLayout from '@/components/layout/MainLayout';
import {
  Eye,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Clock,
  Zap,
  Trophy,
  MessageSquare,
  Volume2,
  VolumeX,
  Maximize2,
  Activity,
  BarChart3,
  Timer,
  AlertCircle,
  Send,
  Star,
  Award,
  Target,
  ArrowUp,
  ArrowDown,
  Minus,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { cn } from '@/lib/utils';
import { BettingMarkets } from '@/components/betting/BettingMarkets';

// Betting and spectator types
interface LiveGameData {
  session_id: string;
  game_name: string;
  participants: Participant[];
  game_state: any;
  round_number: number;
  time_elapsed: number;
  viewer_count: number;
}

interface Participant {
  id: string;
  username: string;
  avatar?: string;
  score: number;
  status: 'playing' | 'eliminated' | 'winner';
  stats?: {
    wins: number;
    accuracy?: number;
    avg_time?: number;
  };
}

interface BetPosition {
  participant_id: string;
  amount: number;
  odds: number;
  potential_return: number;
  timestamp: string;
}

interface LiveOdds {
  participant_id: string;
  current_odds: number;
  previous_odds: number;
  trend: 'up' | 'down' | 'stable';
  probability: number;
  total_backing: number;
}

interface ChatMessage {
  id: string;
  user_id: string;
  username: string;
  message: string;
  timestamp: string;
  type: 'chat' | 'bet' | 'system';
}

export default function LiveSpectatorPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.id as string;
  const { user, isSignedIn } = useAuth();
  
  const [gameData, setGameData] = useState<LiveGameData | null>(null);
  const [liveOdds, setLiveOdds] = useState<LiveOdds[]>([]);
  const [userBets, setUserBets] = useState<BetPosition[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [betAmount, setBetAmount] = useState('10');
  const [selectedParticipant, setSelectedParticipant] = useState<string | null>(null);
  const [chatInput, setChatInput] = useState('');
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const chatScrollRef = useRef<HTMLDivElement>(null);

  // Generate mock live data
  useEffect(() => {
    const mockGameData: LiveGameData = {
      session_id: sessionId,
      game_name: 'Trivia Master Championship',
      participants: [
        {
          id: 'p1',
          username: 'ProGamer123',
          score: 450,
          status: 'playing',
          stats: { wins: 152, accuracy: 78.5, avg_time: 8.2 }
        },
        {
          id: 'p2',
          username: 'NinjaWarrior',
          score: 380,
          status: 'playing',
          stats: { wins: 98, accuracy: 71.2, avg_time: 9.1 }
        }
      ],
      game_state: {},
      round_number: 3,
      time_elapsed: 240,
      viewer_count: 187
    };

    const mockOdds: LiveOdds[] = [
      {
        participant_id: 'p1',
        current_odds: 1.65,
        previous_odds: 1.75,
        trend: 'down',
        probability: 60.6,
        total_backing: 3250
      },
      {
        participant_id: 'p2',
        current_odds: 2.35,
        previous_odds: 2.20,
        trend: 'up',
        probability: 39.4,
        total_backing: 1820
      }
    ];

    const mockMessages: ChatMessage[] = [
      {
        id: '1',
        user_id: 'u1',
        username: 'SpectatorKing',
        message: 'This is intense!',
        timestamp: new Date().toISOString(),
        type: 'chat'
      },
      {
        id: '2',
        user_id: 'u2',
        username: 'BetMaster',
        message: 'Just placed $50 on ProGamer123',
        timestamp: new Date().toISOString(),
        type: 'bet'
      },
      {
        id: '3',
        user_id: 'system',
        username: 'System',
        message: 'Round 3 starting now!',
        timestamp: new Date().toISOString(),
        type: 'system'
      }
    ];

    setTimeout(() => {
      setGameData(mockGameData);
      setLiveOdds(mockOdds);
      setChatMessages(mockMessages);
      setLoading(false);
    }, 1000);

    // Simulate live updates
    const interval = setInterval(() => {
      setGameData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          viewer_count: prev.viewer_count + Math.floor(Math.random() * 5) - 2,
          time_elapsed: prev.time_elapsed + 1,
          participants: prev.participants.map(p => ({
            ...p,
            score: p.score + Math.floor(Math.random() * 20)
          }))
        };
      });

      setLiveOdds(prev => prev.map(odd => ({
        ...odd,
        previous_odds: odd.current_odds,
        current_odds: odd.current_odds + (Math.random() - 0.5) * 0.1,
        trend: Math.random() > 0.5 ? 'up' : Math.random() > 0.5 ? 'down' : 'stable'
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, [sessionId]);

  // Auto-scroll chat
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [chatMessages]);

  const handlePlaceBet = () => {
    if (!selectedParticipant || !betAmount || !isSignedIn) return;

    const odds = liveOdds.find(o => o.participant_id === selectedParticipant);
    if (!odds) return;

    const newBet: BetPosition = {
      participant_id: selectedParticipant,
      amount: parseFloat(betAmount),
      odds: odds.current_odds,
      potential_return: parseFloat(betAmount) * odds.current_odds,
      timestamp: new Date().toISOString()
    };

    setUserBets([...userBets, newBet]);
    setBetAmount('10');
    setSelectedParticipant(null);

    // Add bet message to chat
    const betMessage: ChatMessage = {
      id: Date.now().toString(),
      user_id: user?.id || '',
      username: user?.firstName || 'You',
      message: `Placed $${betAmount} on ${gameData?.participants.find(p => p.id === selectedParticipant)?.username}`,
      timestamp: new Date().toISOString(),
      type: 'bet'
    };
    setChatMessages([...chatMessages, betMessage]);
  };

  const handleSendMessage = () => {
    if (!chatInput.trim() || !isSignedIn) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      user_id: user?.id || '',
      username: user?.firstName || 'You',
      message: chatInput,
      timestamp: new Date().toISOString(),
      type: 'chat'
    };

    setChatMessages([...chatMessages, newMessage]);
    setChatInput('');
  };

  const getOddsTrend = (trend: string) => {
    switch (trend) {
      case 'up':
        return { icon: ArrowUp, color: 'text-red-600' };
      case 'down':
        return { icon: ArrowDown, color: 'text-green-600' };
      default:
        return { icon: Minus, color: 'text-muted-foreground' };
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <MainLayout showSidebar={false}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Connecting to live stream...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!gameData) {
    return (
      <MainLayout showSidebar={false}>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Session Not Found</h2>
          <Button onClick={() => router.push('/spectate')}>
            Back to Spectator Hub
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout showSidebar={false}>
      <div className="h-[calc(100vh-8rem)]">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 h-full">
          
          {/* Main Game View */}
          <div className="lg:col-span-3 space-y-4">
            
            {/* Game Header */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <span>{gameData.game_name}</span>
                        <div className="flex items-center space-x-1">
                          <div className="pulse-dot" />
                          <span className="live-indicator">LIVE</span>
                        </div>
                      </CardTitle>
                      <CardDescription>
                        Round {gameData.round_number} • {formatTime(gameData.time_elapsed)}
                      </CardDescription>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 text-sm">
                      <Eye className="h-4 w-4 text-muted-foreground" />
                      <span>{gameData.viewer_count} watching</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" onClick={() => setIsMuted(!isMuted)}>
                        {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => setIsFullscreen(!isFullscreen)}>
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Game Stream Area */}
            <Card className="flex-1">
              <CardContent className="p-6 h-96">
                <div className="flex items-center justify-center h-full bg-muted rounded-lg">
                  <div className="text-center">
                    <Activity className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Game Stream</h3>
                    <p className="text-muted-foreground">
                      Live game visualization would appear here
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Participants & Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {gameData.participants.map((participant) => {
                const odds = liveOdds.find(o => o.participant_id === participant.id);
                const trend = odds ? getOddsTrend(odds.trend) : null;
                
                return (
                  <Card key={participant.id} className={cn(
                    "cursor-pointer transition-all",
                    selectedParticipant === participant.id && "ring-2 ring-primary"
                  )} onClick={() => setSelectedParticipant(participant.id)}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback>
                              {participant.username.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold">{participant.username}</h3>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Trophy className="h-3 w-3" />
                              <span>{participant.stats?.wins} wins</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-2xl font-bold">{participant.score}</div>
                          <div className="text-xs text-muted-foreground">points</div>
                        </div>
                      </div>

                      {odds && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Current Odds</span>
                            <div className="flex items-center space-x-2">
                              <Badge variant="secondary">
                                {odds.current_odds.toFixed(2)}x
                              </Badge>
                              {trend && (
                                <trend.icon className={cn("h-3 w-3", trend.color)} />
                              )}
                            </div>
                          </div>
                          
                          <Progress value={odds.probability} className="h-2" />
                          
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{odds.probability.toFixed(1)}% chance</span>
                            <span>${odds.total_backing} backed</span>
                          </div>
                        </div>
                      )}

                      {participant.stats && (
                        <div className="grid grid-cols-2 gap-2 mt-3 pt-3 border-t">
                          <div className="text-center">
                            <div className="text-sm font-medium">
                              {participant.stats.accuracy?.toFixed(1)}%
                            </div>
                            <div className="text-xs text-muted-foreground">Accuracy</div>
                          </div>
                          <div className="text-center">
                            <div className="text-sm font-medium">
                              {participant.stats.avg_time?.toFixed(1)}s
                            </div>
                            <div className="text-xs text-muted-foreground">Avg Time</div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-4">
            
            {/* Betting Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Live Betting</CardTitle>
                <CardDescription>
                  Place bets on the live match
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!isSignedIn ? (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Sign in to place bets on live games
                    </AlertDescription>
                  </Alert>
                ) : (
                  <BettingMarkets 
                    sessionId={sessionId}
                    showLiveUpdates={true}
                  />
                )}
              </CardContent>
            </Card>

            {/* Your Bets */}
            {userBets.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Your Bets</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-48">
                    <div className="space-y-2">
                      {userBets.map((bet, index) => (
                        <div key={index} className="p-2 border rounded-lg">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium">
                              {gameData.participants.find(p => p.id === bet.participant_id)?.username}
                            </span>
                            <Badge variant="secondary">
                              {bet.odds.toFixed(2)}x
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                            <span>${bet.amount}</span>
                            <span className="text-green-600">
                              Returns ${bet.potential_return.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}

            {/* Live Chat */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4" />
                  <span>Live Chat</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ScrollArea className="h-64" ref={chatScrollRef}>
                  <div className="space-y-2">
                    {chatMessages.map((message) => (
                      <div key={message.id} className={cn(
                        "text-sm",
                        message.type === 'system' && "text-center text-muted-foreground italic",
                        message.type === 'bet' && "bg-green-50 dark:bg-green-900/20 p-2 rounded"
                      )}>
                        {message.type !== 'system' && (
                          <span className="font-medium">{message.username}: </span>
                        )}
                        <span>{message.message}</span>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                
                {isSignedIn && (
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Type a message..."
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    />
                    <Button size="sm" onClick={handleSendMessage}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}