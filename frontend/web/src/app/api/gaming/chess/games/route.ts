import { NextRequest, NextResponse } from 'next/server';

const API_GATEWAY_URL = process.env.API_GATEWAY_URL || 'http://betbet-api-gateway:8000';

export async function GET(request: NextRequest) {
  try {
    // Forward the request to the API Gateway
    const url = `${API_GATEWAY_URL}/api/gaming/chess/games`;
    
    // Get headers from the original request
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Forward authorization header if present
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    console.log('Proxying chess games request to:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });
    
    if (!response.ok) {
      console.error('API Gateway error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch chess games' },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    console.log('Chess games data received:', data);
    
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('Error proxying chess games request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
