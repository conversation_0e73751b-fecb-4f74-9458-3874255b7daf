/**
 * BetBet Gaming Engine - Create Session - DEPRECATED
 * ==================================================
 * 
 * This page redirects to the new /games/{game-slug}/create-session URL pattern.
 */

'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { Loader2 } from 'lucide-react';

export default function CreateSessionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const gameParam = searchParams.get('game');
    if (gameParam) {
      // If a game is specified, redirect to the specific game's create session page
      router.replace(`/games/${gameParam}/create-session`);
    } else {
      // Otherwise redirect to the general games page
      router.replace('/games');
    }
  }, [router, searchParams]);

  return (
    <AuthenticatedLayout>
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
      </div>
    </AuthenticatedLayout>
  );
}