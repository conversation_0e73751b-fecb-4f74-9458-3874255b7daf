/**
 * BetBet Gaming Engine - User Dashboard Page
 * ========================================
 * 
 * Main dashboard page for authenticated users.
 */

'use client';

import { useUser } from '@clerk/nextjs';
import { UnifiedDashboard } from '@/components/dashboard/UnifiedDashboard';
import { UserDashboard } from '@/components/dashboard/UserDashboard';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { Loader2, Activity } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function DashboardPage() {
  const { user, isLoaded } = useUser();

  if (!isLoaded) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        <Tabs defaultValue="platform" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-slate-800/50 border-slate-700/50">
            <TabsTrigger 
              value="platform" 
              className="flex items-center gap-2 data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30"
            >
              <Activity className="h-4 w-4" />
              Platform Overview
            </TabsTrigger>
            <TabsTrigger 
              value="personal" 
              className="flex items-center gap-2 data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30"
            >
              <Loader2 className="h-4 w-4" />
              My Dashboard
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="platform">
            <UnifiedDashboard />
          </TabsContent>
          
          <TabsContent value="personal">
            <UserDashboard userId={user?.id} />
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}