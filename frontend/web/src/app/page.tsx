'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { SignedIn, SignedOut, useUser } from '@clerk/nextjs';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  GamepadIcon,
  Users,
  Trophy,
  DollarSign,
  TrendingUp,
  Star,
  ArrowRight,
  Sparkles,
  Target,
  BarChart3,
  Wallet,
  Home,
  Play
} from 'lucide-react';
import { useMultiServiceApi } from '@/hooks/useApi';
import { LandingNavigation } from '@/components/layout/LandingNavigation';

export default function UnifiedHomePage() {
  const { user } = useUser();
  const router = useRouter();
  const multiApi = useMultiServiceApi();
  
  const [email, setEmail] = useState('');
  const [userCount, setUserCount] = useState(0); // Real user count from API
  const [jackpotAmount, setJackpotAmount] = useState(50000);
  const [platformStats, setPlatformStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load platform statistics from all services
  useEffect(() => {
    const loadPlatformData = async () => {
      try {
        setIsLoading(true);
        
        // Get platform overview from gaming service
        const stats = await multiApi.gaming.getOverview();
        setPlatformStats(stats);

        // Update live stats
        if (stats?.total_users) setUserCount(stats.total_users);
        if (stats?.total_betting_volume) setJackpotAmount(stats.total_betting_volume);
        
      } catch (error) {
        console.error('Failed to load platform data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPlatformData();
  }, [multiApi]);

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      router.push('/sign-up');
    }
  };

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <LandingNavigation />
      
      {/* Hero Section with Wheel Background */}
      <section className="relative flex items-center justify-center overflow-hidden" style={{minHeight: '100vh'}}>
        {/* Enhanced Background System */}
        <div className="absolute inset-0 z-0">
          {/* Original wheel background */}
          <img
            src="/wheel.avif"
            alt="Gaming Background"
            className="w-full h-full object-cover object-center"
          />
          {/* Enhanced overlay system */}
          <div className="absolute inset-0 bg-black/60" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-950/50 to-slate-950" />
          {/* Animated particles overlay */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-yellow-500 rounded-full animate-float" />
            <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-purple-500 rounded-full animate-float" style={{animationDelay: '1s'}} />
            <div className="absolute top-1/2 left-1/2 w-1.5 h-1.5 bg-red-500 rounded-full animate-float" style={{animationDelay: '2s'}} />
          </div>
        </div>

        {/* Enhanced Content */}
        <div className="container mx-auto max-w-7xl relative z-10 text-center px-4">
          {/* Live stats badge with glass effect */}
          <div className="inline-flex items-center mb-6 glass rounded-full px-6 py-3 animate-slide-in-down">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-3" />
            <Sparkles className="h-4 w-4 mr-2 text-green-400" />
            <span className="text-white font-medium">
              Join {userCount.toLocaleString()} players winning daily
            </span>
          </div>
          
          {/* Enhanced title with better typography */}
          <h1 className="text-4xl md:text-6xl font-black text-white mb-8 leading-none animate-slide-in-up">
            Where Skill Meets
            <span className="block text-gradient-primary animate-gradient-shift bg-[length:200%_200%]">
              Opportunity
            </span>
          </h1>
          
          {/* Enhanced subtitle */}
          <p className="text-lg md:text-xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in" style={{animationDelay: '0.3s'}}>
            The world's first P2P gaming platform combining skill-based games with live sports betting. 
            <span className="text-gradient-secondary font-semibold"> No house edge, just pure competition.</span>
          </p>

          {/* Enhanced CTA section */}
          <div className="animate-scale-in" style={{animationDelay: '0.6s'}}>
            <SignedOut>
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
                <div className="relative group">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-72 h-14 px-6 glass text-white placeholder:text-slate-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-300 group-hover:glass-hover"
                    required
                  />
                </div>
                <button 
                  type="submit"
                  className="h-14 px-8 bg-gradient-primary text-white rounded-xl font-semibold transition-all duration-300 flex items-center hover:transform hover:scale-105 hover:shadow-glow-lg btn-hover-lift"
                >
                  Start Playing Free
                  <ArrowRight className="ml-3 h-5 w-5" />
                </button>
              </form>
            </SignedOut>

            <SignedIn>
              <div className="mb-16">
                <h2 className="text-3xl font-bold text-white mb-8">
                  Welcome back, {user?.firstName || 'Champion'}!
                </h2>
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <Button 
                    size="lg"
                    className="h-14 px-8 bg-gradient-primary text-white rounded-xl font-semibold transition-all duration-300 flex items-center hover:transform hover:scale-105 hover:shadow-glow-lg btn-hover-lift"
                    onClick={() => router.push('/dashboard')}
                  >
                    <Home className="mr-3 h-5 w-5" />
                    Go to Dashboard
                  </Button>
                  <Button 
                    size="lg"
                    variant="outline"
                    className="h-14 px-8 glass text-white border-white/30 rounded-xl font-semibold transition-all duration-300 flex items-center hover:glass-hover hover:transform hover:scale-105"
                    onClick={() => router.push('/games')}
                  >
                    <Play className="mr-3 h-5 w-5" />
                    Quick Play
                  </Button>
                </div>
              </div>
            </SignedIn>

            {/* Enhanced live stats */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-12">
              <div className="text-center group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-black text-green-500 mb-2 group-hover:scale-110 transition-transform duration-300">
                    ${jackpotAmount.toLocaleString()}
                  </p>
                  <div className="absolute inset-0 bg-green-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                </div>
                <p className="text-slate-400 font-medium">Current Jackpot</p>
              </div>
              
              <div className="h-16 w-px bg-gradient-to-b from-transparent via-slate-600 to-transparent hidden sm:block" />
              
              <div className="text-center group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-black text-purple-500 mb-2 group-hover:scale-110 transition-transform duration-300">
                    {userCount.toLocaleString()}
                  </p>
                  <div className="absolute inset-0 bg-purple-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                </div>
                <p className="text-slate-400 font-medium">Players Online</p>
              </div>
            </div>
          </div>
        </div>

      </section>

      {/* Platform Modules Section */}
      <section className="py-20 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950">
        <div className="container mx-auto max-w-7xl px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Complete Gaming Ecosystem
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Seven integrated modules offering everything from skill-based gaming to expert analysis
            </p>
          </div>

          {/* Platform Modules Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16">
            {/* Gaming Engine */}
            <Link href="/games">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-primary rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <GamepadIcon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-primary transition-colors duration-300">
                    Gaming Engine
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Skill-based multiplayer games with real-time competition
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {platformStats?.active_sessions || 0} Active Sessions
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Custom Betting */}
            <Link href="/custom-betting">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-secondary rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-secondary transition-colors duration-300">
                    Custom Betting
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Create and bet on custom markets with peer-to-peer wagering
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    P2P Markets
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Trading Exchange */}
            <Link href="/trading">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-primary transition-colors duration-300">
                    Odds Exchange
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Trade betting odds like stocks with live order books
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Live Trading
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Expert Analysis */}
            <Link href="/experts">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-primary rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Star className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-primary transition-colors duration-300">
                    Expert Analysis
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Follow expert analysts and access premium insights
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Premium Picks
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Sports Analysis */}
            <Link href="/sports-analysis">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-secondary rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-secondary transition-colors duration-300">
                    Sports Analysis
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    AI-powered sports predictions and event analysis
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    AI Predictions
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Leaderboards */}
            <Link href="/leaderboard">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Trophy className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-primary transition-colors duration-300">
                    Leaderboards
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Global rankings and achievements across all platforms
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Global Rankings
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Wallet Service */}
            <Link href="/wallet">
              <Card className="card-gaming group cursor-pointer h-full">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-primary rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Wallet className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-primary transition-colors duration-300">
                    Wallet Service
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    Secure wallet with deposits, withdrawals, and transfers
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Secure Payments
                  </Badge>
                </CardContent>
              </Card>
            </Link>

            {/* Dashboard - Takes remaining space */}
            <SignedIn>
              <Link href="/dashboard">
                <Card className="card-gaming group cursor-pointer h-full">
                  <CardContent className="p-6 text-center">
                    <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-secondary rounded-xl group-hover:scale-110 transition-transform duration-300">
                      <BarChart3 className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-secondary transition-colors duration-300">
                      Dashboard
                    </h3>
                    <p className="text-slate-400 text-sm mb-4">
                      Your unified control center and analytics
                    </p>
                    <Badge variant="secondary" className="text-xs">
                      Personal Hub
                    </Badge>
                  </CardContent>
                </Card>
              </Link>
            </SignedIn>
          </div>

          {/* Platform Stats Grid */}
          {platformStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="card-gaming p-6 text-center">
                <Users className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white mb-2">{platformStats.total_users || userCount}</p>
                <p className="text-slate-400 text-sm">Total Players</p>
              </div>
              <div className="card-gaming p-6 text-center">
                <GamepadIcon className="h-8 w-8 text-green-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white mb-2">{platformStats.active_sessions || 0}</p>
                <p className="text-slate-400 text-sm">Active Sessions</p>
              </div>
              <div className="card-gaming p-6 text-center">
                <Trophy className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white mb-2">{platformStats.active_tournaments || 0}</p>
                <p className="text-slate-400 text-sm">Tournaments</p>
              </div>
              <div className="card-gaming p-6 text-center">
                <DollarSign className="h-8 w-8 text-purple-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white mb-2">${(platformStats.total_betting_volume || jackpotAmount).toLocaleString()}</p>
                <p className="text-slate-400 text-sm">Volume</p>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-slate-950 via-purple-950/20 to-slate-950">
        <div className="container mx-auto max-w-4xl px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Compete?
          </h2>
          <p className="text-xl text-slate-300 mb-12 max-w-2xl mx-auto">
            Join thousands of players in the ultimate skill-based gaming experience. 
            No house edge, just pure competition.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <SignedOut>
              <Button 
                size="lg"
                className="h-14 px-8 bg-gradient-primary text-white rounded-xl font-semibold transition-all duration-300 flex items-center hover:transform hover:scale-105 hover:shadow-glow-lg btn-hover-lift"
                onClick={() => router.push('/sign-up')}
              >
                <Trophy className="mr-3 h-5 w-5" />
                Start Playing Now
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="h-14 px-8 glass text-white border-white/30 rounded-xl font-semibold transition-all duration-300 flex items-center hover:glass-hover hover:transform hover:scale-105"
                onClick={() => router.push('/leaderboard')}
              >
                <BarChart3 className="mr-3 h-5 w-5" />
                View Leaderboards
              </Button>
            </SignedOut>
            
            <SignedIn>
              <Button 
                size="lg"
                className="h-14 px-8 bg-gradient-primary text-white rounded-xl font-semibold transition-all duration-300 flex items-center hover:transform hover:scale-105 hover:shadow-glow-lg btn-hover-lift"
                onClick={() => router.push('/games')}
              >
                <Play className="mr-3 h-5 w-5" />
                Continue Playing
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="h-14 px-8 glass text-white border-white/30 rounded-xl font-semibold transition-all duration-300 flex items-center hover:glass-hover hover:transform hover:scale-105"
                onClick={() => router.push('/dashboard')}
              >
                <BarChart3 className="mr-3 h-5 w-5" />
                My Dashboard
              </Button>
            </SignedIn>
          </div>
        </div>
      </section>
    </div>
  );
}