/**
 * Individual Game Page - Dynamic Route Handler
 * ============================================
 * 
 * Handles specific game pages like /games/chess, /games/trivia, etc.
 * Uses AuthenticatedLayout for unified navigation and dark theme.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { useMultiServiceApi } from '@/hooks/useApi';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Crown,
  Users,
  Clock,
  Play,
  Eye,
  Trophy,
  Plus,
  Search,
  Loader2,
  Filter,
  ArrowLeft,
  Star,
  Target,
  Gamepad2
} from 'lucide-react';
import { Game } from '@/lib/api';
import { ChessGameCreationModal } from '@/components/chess';
import { ChessJoinGameModal } from '@/components/chess/ChessJoinGameModal';
import { generateChessGameSlug, type ChessGame as ChessGameType } from '@/lib/chess-slug-utils';

interface ChessGame {
  id: string;
  white_player?: {
    id: string;
    username: string;
    rating: number;
  };
  black_player?: {
    id: string;
    username: string;
    rating: number;
  };
  status: 'waiting' | 'active' | 'completed';
  variant: string;
  time_control: string;
  is_rated: boolean;
  spectators_count: number;
  created_at: string;
  result?: string;
}

export default function GamePage() {
  const params = useParams();
  const router = useRouter();
  const { isSignedIn, user, getToken } = useAuth();
  const api = useMultiServiceApi();
  
  const gameSlug = params.slug as string;
  
  const [game, setGame] = useState<Game | null>(null);
  const [chessGames, setChessGames] = useState<ChessGame[]>([]);
  const [loading, setLoading] = useState(true);
  const [gameLoading, setGameLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterVariant, setFilterVariant] = useState('all');
  const [filterTimeControl, setFilterTimeControl] = useState('all');
  const [activeTab, setActiveTab] = useState('active');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [selectedGame, setSelectedGame] = useState<ChessGame | null>(null);
  const [isJoining, setIsJoining] = useState(false);
  const [joinError, setJoinError] = useState<string | null>(null);

  useEffect(() => {
    loadGameDetails();
    if (gameSlug === 'chess') {
      loadChessGames();
      // Set up polling for real-time updates
      const interval = setInterval(loadChessGames, 5000);
      return () => clearInterval(interval);
    } else {
      // For non-chess games, set loading to false immediately
      setLoading(false);
    }
  }, [gameSlug, activeTab, filterVariant, filterTimeControl]);

  const loadGameDetails = async () => {
    try {
      setGameLoading(true);
      console.log('Loading game details for slug:', gameSlug);
      const response = await api.gaming.getGameBySlug(gameSlug);
      console.log('Game details response:', response);
      setGame(response);
    } catch (error) {
      console.error('Failed to load game details:', error);
      console.error('Error details:', error);
      setError('Game not found');
    } finally {
      setGameLoading(false);
    }
  };

  const loadChessGames = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to load chess games even if not signed in (public games)
      let headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add auth token if user is signed in
      if (isSignedIn && getToken) {
        try {
          const token = await getToken();
          if (token) {
            headers['Authorization'] = `Bearer ${token}`;
          }
        } catch (authError) {
          console.warn('Failed to get auth token:', authError);
          // Continue without auth - try to load public games
        }
      }

      // Fetch through Next.js API proxy (which forwards to API Gateway)
      console.log('Fetching chess games through Next.js proxy...');

      const response = await fetch('/api/gaming/chess/games', {
        headers,
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch chess games: ${response.status}`);
      }

      const data = await response.json();

      // Transform API response to match expected format
      const transformedGames = data.games.map((game: any) => ({
        id: game.id,
        white_player: game.white_player,
        black_player: game.black_player,
        status: game.status,
        variant: game.variant,
        time_control: game.time_control,
        is_rated: game.is_rated,
        spectators_count: 0, // TODO: Add spectator count from API
        created_at: game.created_at,
        description: game.description,
        wager_amount: game.wager_amount,
        initial_time_seconds: game.initial_time_seconds,
        increment_seconds: game.increment_seconds,
      }));

      setChessGames(transformedGames);

    } catch (err) {
      console.error('Error loading chess games:', err);
      setError(err instanceof Error ? err.message : 'Failed to load chess games');
      // Fallback to empty array on error
      setChessGames([]);
    } finally {
      setLoading(false);
    }
  };

  const createGame = async () => {
    if (gameSlug === 'chess') {
      setShowCreateModal(true);
    } else {
      router.push(`/games/${gameSlug}/create-session`);
    }
  };

  const handleGameCreated = (sessionId: string) => {
    // For new games, we'll need to fetch the game data to generate the slug
    // For now, redirect using the sessionId (UUID) - this will be handled by the component
    router.push(`/chess/${sessionId}`);
  };

  const joinGame = async (game: ChessGame) => {
    // Show join confirmation modal
    setSelectedGame(game);
    setShowJoinModal(true);
    setJoinError(null);
  };

  const convertChessGameToSession = (game: ChessGame) => {
    return {
      id: game.id,
      slug: generateChessGameSlug(game as ChessGameType),
      title: `${game.variant} Chess - ${game.time_control}`,
      gameType: 'chess',
      mode: game.variant,
      status: game.status as 'waiting' | 'running' | 'finished',
      players: [
        game.white_player?.username || 'Unknown',
        ...(game.black_player ? [game.black_player.username] : [])
      ],
      maxPlayers: 2,
      spectators: 0,
      stakes: '$10', // TODO: Get from game data
      createdAt: game.created_at,
      estimatedDuration: '15 min',
      gameState: {
        // TODO: Add actual game state when available
        currentPlayer: 'white' as 'white' | 'black'
      },
      config: {
        timeControl: game.time_control,
        wagerAmount: 10, // TODO: Get from game data
        playerColor: 'random' as 'white' | 'black' | 'random',
        allowSpectators: true,
        isPrivate: false,
        variant: game.variant,
        requirePassword: false
      }
    };
  };

  const handleJoinGameConfirmed = async (gameId: string, password?: string) => {
    if (!selectedGame) return;

    try {
      setIsJoining(true);
      setJoinError(null);

      // TODO: Implement actual join game API call
      // For now, simulate the join process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate slug for the game and redirect
      const slug = generateChessGameSlug(selectedGame as ChessGameType);

      // Close modal and redirect
      setShowJoinModal(false);
      router.push(`/chess/${slug}`);

    } catch (err) {
      console.error('Error joining game:', err);
      setJoinError('Failed to join game. Please try again.');
    } finally {
      setIsJoining(false);
    }
  };

  const spectateGame = (game: ChessGame) => {
    // Generate slug for the game and redirect
    const slug = generateChessGameSlug(game as ChessGameType);
    router.push(`/chess/${slug}`);
  };

  const renderChessGameCard = (game: ChessGame) => {
    const isWaiting = game.status === 'waiting';
    const canJoin = isWaiting && !game.black_player;
    const timeControlDisplay = game.time_control.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <Card key={game.id} className="hover:shadow-lg transition-all duration-200 bg-slate-900/50 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2 text-white">
              <Crown className="h-5 w-5 text-yellow-400" />
              {game.variant === 'standard' ? 'Standard Chess' : game.variant.replace('_', ' ')}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={game.is_rated ? 'default' : 'secondary'} 
                     className={game.is_rated ? 'bg-blue-600 text-white' : 'bg-slate-700 text-slate-200'}>
                {game.is_rated ? 'Rated' : 'Casual'}
              </Badge>
              <Badge variant={isWaiting ? 'outline' : 'default'} 
                     className={isWaiting ? 'bg-slate-800/50 border-slate-600 text-slate-200' : 'bg-green-600 text-white'}>
                {game.status}
              </Badge>
            </div>
          </div>
          <CardDescription className="flex items-center gap-4 text-slate-300">
            <span className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {timeControlDisplay}
            </span>
            <span className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              {game.spectators_count} spectators
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-white border border-slate-300 rounded-sm" />
                  <span className="font-medium text-white">
                    {game.white_player?.username || 'Waiting for player...'}
                  </span>
                  {game.white_player && (
                    <Badge variant="outline" className="text-xs bg-slate-800/50 border-slate-600 text-slate-200">
                      {game.white_player.rating}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-slate-800 border border-slate-300 rounded-sm" />
                  <span className="font-medium text-white">
                    {game.black_player?.username || 'Waiting for player...'}
                  </span>
                  {game.black_player && (
                    <Badge variant="outline" className="text-xs bg-slate-800/50 border-slate-600 text-slate-200">
                      {game.black_player.rating}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                {canJoin ? (
                  <Button
                    size="sm"
                    onClick={() => joinGame(game)}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => spectateGame(game)}
                    className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Watch
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderGenericGameContent = () => (
    <div className="text-center py-12">
      <Gamepad2 className="h-16 w-16 text-slate-400 mx-auto mb-4" />
      <h2 className="text-2xl font-bold mb-4 text-white">Coming Soon</h2>
      <p className="text-slate-300 mb-6 max-w-md mx-auto">
        {game?.name} gameplay is currently in development. Check back soon for an amazing gaming experience!
      </p>
      <Button 
        onClick={() => router.push('/games')}
        className="bg-blue-600 hover:bg-blue-700 text-white"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Games
      </Button>
    </div>
  );

  const filteredGames = chessGames.filter(game => {
    // Filter by active tab
    if (activeTab === 'active' && game.status !== 'active') return false;
    if (activeTab === 'waiting' && game.status !== 'waiting') return false;
    if (activeTab === 'recent' && !['finished', 'completed'].includes(game.status)) return false;

    // Filter by variant
    if (filterVariant !== 'all' && game.variant !== filterVariant) return false;

    // Filter by time control
    if (filterTimeControl !== 'all' && game.time_control !== filterTimeControl) return false;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const playerMatch =
        game.white_player?.username.toLowerCase().includes(query) ||
        game.black_player?.username.toLowerCase().includes(query);
      const descriptionMatch = game.description?.toLowerCase().includes(query);
      if (!playerMatch && !descriptionMatch) return false;
    }

    return true;
  });

  if (!isSignedIn) {
    return (
      <AuthenticatedLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <Crown className="h-16 w-16 text-slate-400 mb-4" />
          <h2 className="text-2xl font-bold mb-2 text-white">Sign in to Play Games</h2>
          <p className="text-slate-300 mb-4">Join the BetBet gaming community</p>
          <Button onClick={() => router.push('/sign-in')} size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
            Sign In
          </Button>
        </div>
      </AuthenticatedLayout>
    );
  }

  if (gameLoading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
        </div>
      </AuthenticatedLayout>
    );
  }

  // For chess games, provide fallback data if game details failed to load
  const currentGame = gameSlug === 'chess' && !game ? {
    id: 'chess',
    name: 'Chess',
    slug: 'chess',
    description: 'Classic chess game with real-time multiplayer',
    category: 'strategy',
    is_featured: true,
    is_active: true,
    min_players: 2,
    max_players: 2
  } : game;

  if (gameSlug !== 'chess' && (error || !currentGame)) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <Gamepad2 className="h-16 w-16 text-slate-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4 text-white">Game Not Found</h2>
          <p className="text-slate-300 mb-6">The game you're looking for doesn't exist or isn't available.</p>
          <Button
            onClick={() => router.push('/games')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>
      </AuthenticatedLayout>
    );
  }

  const getGameIcon = () => {
    switch (gameSlug) {
      case 'chess': return <Crown className="h-8 w-8 text-yellow-400" />;
      case 'trivia': return <Trophy className="h-8 w-8 text-blue-400" />;
      case 'strategy': return <Target className="h-8 w-8 text-purple-400" />;
      default: return <Gamepad2 className="h-8 w-8 text-green-400" />;
    }
  };

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/games')}
              className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              All Games
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-3 text-white">
                {getGameIcon()}
                {currentGame.name}
              </h1>
              <div className="text-slate-300 mt-1 flex items-center gap-4">
                <span>{currentGame.description}</span>
                <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">
                  {currentGame.category}
                </Badge>
                {currentGame.is_featured && (
                  <Badge className="bg-yellow-600 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <Button
            size="lg"
            onClick={createGame}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-5 w-5 mr-2" />
            {gameSlug === 'chess' ? 'Create Game' : 'Play Now'}
          </Button>
        </div>

        {/* Game Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <Users className="h-6 w-6 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{currentGame.min_players}-{currentGame.max_players}</div>
              <div className="text-sm text-slate-300">Players</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <Clock className="h-6 w-6 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{currentGame.estimated_duration_minutes || 30}</div>
              <div className="text-sm text-slate-300">Minutes</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <Trophy className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{currentGame.total_sessions_played || 0}</div>
              <div className="text-sm text-slate-300">Sessions Played</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <Star className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{Number(currentGame.popularity_score || 95.5).toFixed(1)}</div>
              <div className="text-sm text-slate-300">Rating</div>
            </CardContent>
          </Card>
        </div>

        {/* Game Content */}
        {gameSlug === 'chess' ? (
          <>
            {/* Chess Filters */}
            <Card className="bg-slate-900/50 border-slate-700">
              <CardContent className="p-4">
                <div className="flex flex-wrap gap-4">
                  <div className="flex-1 min-w-[200px]">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                      <Input
                        placeholder="Search by player name..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                      />
                    </div>
                  </div>
                  <Select value={filterVariant} onValueChange={setFilterVariant}>
                    <SelectTrigger className="w-[180px] bg-slate-800/50 border-slate-600 text-white">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Variant" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Variants</SelectItem>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="chess960">Chess960</SelectItem>
                      <SelectItem value="king_of_the_hill">King of the Hill</SelectItem>
                      <SelectItem value="three_check">Three-check</SelectItem>
                      <SelectItem value="antichess">Antichess</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filterTimeControl} onValueChange={setFilterTimeControl}>
                    <SelectTrigger className="w-[180px] bg-slate-800/50 border-slate-600 text-white">
                      <Clock className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Time Control" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time Controls</SelectItem>
                      <SelectItem value="bullet">Bullet</SelectItem>
                      <SelectItem value="blitz">Blitz</SelectItem>
                      <SelectItem value="rapid">Rapid</SelectItem>
                      <SelectItem value="classical">Classical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Chess Games List */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 border border-slate-600">
                <TabsTrigger value="active" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 text-slate-300">
                  Active Games ({chessGames.filter(g => g.status === 'active').length})
                </TabsTrigger>
                <TabsTrigger value="waiting" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 text-slate-300">
                  Join a Game ({chessGames.filter(g => g.status === 'waiting').length})
                </TabsTrigger>
                <TabsTrigger value="recent" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 text-slate-300">
                  Recent Games ({chessGames.filter(g => g.status === 'completed' || g.status === 'abandoned').length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-4">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
                  </div>
                ) : error ? (
                  <div className="text-center py-12">
                    <p className="text-red-400">{error}</p>
                    <Button onClick={loadChessGames} className="mt-4 bg-blue-600 hover:bg-blue-700 text-white">
                      Retry
                    </Button>
                  </div>
                ) : filteredGames.length === 0 ? (
                  <div className="text-center py-12">
                    <Crown className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    {(() => {
                      const totalGames = chessGames.length;
                      const activeGames = chessGames.filter(g => g.status === 'active').length;
                      const waitingGames = chessGames.filter(g => g.status === 'waiting').length;

                      if (totalGames === 0) {
                        return (
                          <>
                            <p className="text-slate-300 mb-4">No chess games available</p>
                            <p className="text-slate-400 text-sm mb-4">Be the first to create a game!</p>
                          </>
                        );
                      } else if (activeTab === 'active' && activeGames === 0 && waitingGames > 0) {
                        return (
                          <>
                            <p className="text-slate-300 mb-2">No active games</p>
                            <p className="text-slate-400 text-sm mb-4">
                              {waitingGames} game{waitingGames > 1 ? 's' : ''} waiting for players in the "Join a Game" tab
                            </p>
                          </>
                        );
                      } else if (activeTab === 'waiting' && waitingGames === 0 && activeGames > 0) {
                        return (
                          <>
                            <p className="text-slate-300 mb-2">No games waiting for players</p>
                            <p className="text-slate-400 text-sm mb-4">
                              {activeGames} active game{activeGames > 1 ? 's' : ''} in progress in the "Active Games" tab
                            </p>
                          </>
                        );
                      } else {
                        return <p className="text-slate-300 mb-4">No games match your current filters</p>;
                      }
                    })()}
                    <Button onClick={createGame} className="mt-4 bg-blue-600 hover:bg-blue-700 text-white">
                      Create a Game
                    </Button>
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredGames.map(renderChessGameCard)}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </>
        ) : (
          renderGenericGameContent()
        )}
      </div>

      {/* Chess Game Creation Modal */}
      {gameSlug === 'chess' && (
        <ChessGameCreationModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          gameId={game?.id}
          onSuccess={handleGameCreated}
        />
      )}

      {/* Chess Game Join Modal */}
      {gameSlug === 'chess' && selectedGame && (
        <ChessJoinGameModal
          isOpen={showJoinModal}
          onClose={() => {
            setShowJoinModal(false);
            setSelectedGame(null);
            setJoinError(null);
          }}
          gameSession={convertChessGameToSession(selectedGame)}
          onJoinGame={handleJoinGameConfirmed}
          isJoining={isJoining}
          error={joinError}
          userBalance={1000} // TODO: Get from wallet API
        />
      )}
    </AuthenticatedLayout>
  );
}