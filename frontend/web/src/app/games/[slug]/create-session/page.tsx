/**
 * Create Session for Specific Game - Dynamic Route Handler
 * ======================================================
 * 
 * Handles creating sessions for specific games like /games/chess/create-session.
 * Uses AuthenticatedLayout for unified navigation and dark theme.
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  GamepadIcon,
  Users,
  DollarSign,
  Clock,
  Calendar,
  Trophy,
  Eye,
  Shield,
  Settings,
  Info,
  AlertCircle,
  Check,
  Loader2,
  Zap,
  ArrowLeft,
  Crown,
  Star,
  Target
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import { Game } from '@/lib/api';
import { cn } from '@/lib/utils';

interface SessionSettings {
  game_id: string;
  session_name: string;
  entry_fee: number;
  min_participants: number;
  max_participants: number;
  scheduled_start_time?: string;
  estimated_duration_minutes: number;
  description?: string;
  allow_spectators: boolean;
  spectator_chat_enabled: boolean;
  spectator_betting_enabled: boolean;
  allow_practice_mode: boolean;
  is_private: boolean;
  password?: string;
  auto_start_when_full: boolean;
  tournament_mode: boolean;
  skill_level_required?: number;
}

export default function CreateGameSessionPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isSignedIn, isLoaded } = useAuth();
  const api = useApi();
  
  const gameSlug = params.slug as string;
  
  const [game, setGame] = useState<Game | null>(null);
  const [gameLoading, setGameLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  const [settings, setSettings] = useState<SessionSettings>({
    game_id: '',
    session_name: '',
    entry_fee: 0,
    min_participants: 2,
    max_participants: 8,
    estimated_duration_minutes: 15,
    allow_spectators: true,
    spectator_chat_enabled: true,
    spectator_betting_enabled: true,
    allow_practice_mode: false,
    is_private: false,
    auto_start_when_full: true,
    tournament_mode: false,
  });

  // Handle authentication
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push(`/sign-in?redirect_url=/games/${gameSlug}/create-session`);
      return;
    }
  }, [isLoaded, isSignedIn, router, gameSlug]);

  // Load game details
  useEffect(() => {
    const loadGameDetails = async () => {
      try {
        setGameLoading(true);
        const response = await api.getGameBySlug(gameSlug);
        setGame(response);
        
        // Update settings with game defaults
        setSettings(prev => ({
          ...prev,
          game_id: response.id,
          min_participants: response.min_players,
          max_participants: response.max_players,
          estimated_duration_minutes: response.estimated_duration_minutes,
          allow_practice_mode: response.allows_practice_mode,
          spectator_betting_enabled: response.has_spectator_betting,
          session_name: `${user?.username || 'Player'}'s ${response.name} Session`
        }));
      } catch (error) {
        console.error('Failed to load game details:', error);
        setError('Game not found');
      } finally {
        setGameLoading(false);
      }
    };

    if (isSignedIn && gameSlug) {
      loadGameDetails();
    }
  }, [isSignedIn, gameSlug, api, user?.username]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!settings.game_id) {
      setError('Game not loaded properly');
      return;
    }
    
    if (!settings.session_name.trim()) {
      setError('Please enter a session name');
      return;
    }

    try {
      setSubmitting(true);
      setError('');

      // Here you would call the API to create the session
      // const response = await api.createSession(settings);
      
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSuccess(true);
      
      // Redirect to the session page
      setTimeout(() => {
        router.push(`/games/${gameSlug}`);
      }, 1000);

    } catch (error: any) {
      console.error('Failed to create session:', error);
      setError(error.message || 'Failed to create session. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getGameIcon = () => {
    switch (gameSlug) {
      case 'chess': return <Crown className="h-8 w-8 text-yellow-400" />;
      case 'trivia': return <Trophy className="h-8 w-8 text-blue-400" />;
      case 'strategy': return <Target className="h-8 w-8 text-purple-400" />;
      default: return <GamepadIcon className="h-8 w-8 text-green-400" />;
    }
  };

  // Show loading while auth is loading
  if (!isLoaded) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
        </div>
      </AuthenticatedLayout>
    );
  }

  // Don't render if not signed in (redirect will happen)
  if (!isSignedIn) {
    return null;
  }

  if (gameLoading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
        </div>
      </AuthenticatedLayout>
    );
  }

  if (error && !game) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <GamepadIcon className="h-16 w-16 text-slate-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4 text-white">Game Not Found</h2>
          <p className="text-slate-300 mb-6">The game you're trying to create a session for doesn't exist.</p>
          <Button 
            onClick={() => router.push('/games')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push(`/games/${gameSlug}`)}
              className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to {game?.name}
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-3 text-white">
                {getGameIcon()}
                Create {game?.name} Session
              </h1>
              <p className="text-slate-300 mt-1">
                Set up a new gaming session and invite players to compete
              </p>
            </div>
          </div>
        </div>

        {/* Success Alert */}
        {success && (
          <Alert className="bg-green-900/30 border-green-600 text-green-400">
            <Check className="h-4 w-4" />
            <AlertDescription>
              Session created successfully! Redirecting to game lobby...
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert className="bg-red-900/30 border-red-600 text-red-400">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          
          {/* Game Info Card */}
          {game && (
            <Card className="bg-slate-900/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-white">
                  {getGameIcon()}
                  {game.name}
                  {game.is_featured && (
                    <Badge className="bg-yellow-600 text-white">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription className="text-slate-300">
                  {game.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                    <Users className="h-5 w-5 text-blue-400 mx-auto mb-1" />
                    <div className="text-white font-medium">{game.min_players}-{game.max_players}</div>
                    <div className="text-slate-400">Players</div>
                  </div>
                  <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                    <Clock className="h-5 w-5 text-green-400 mx-auto mb-1" />
                    <div className="text-white font-medium">{game.estimated_duration_minutes}</div>
                    <div className="text-slate-400">Minutes</div>
                  </div>
                  <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                    <Trophy className="h-5 w-5 text-yellow-400 mx-auto mb-1" />
                    <div className="text-white font-medium">{game.total_sessions_played}</div>
                    <div className="text-slate-400">Sessions</div>
                  </div>
                  <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                    <Star className="h-5 w-5 text-purple-400 mx-auto mb-1" />
                    <div className="text-white font-medium">{Number(game.popularity_score).toFixed(1)}</div>
                    <div className="text-slate-400">Rating</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Session Settings */}
          <Card className="bg-slate-900/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Basic Settings</CardTitle>
              <CardDescription className="text-slate-300">
                Configure your gaming session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              
              <div className="space-y-2">
                <Label htmlFor="session_name" className="text-slate-200">Session Name</Label>
                <Input
                  id="session_name"
                  placeholder={`e.g., ${user?.username || 'Player'}'s ${game?.name} Battle`}
                  value={settings.session_name}
                  onChange={(e) => setSettings(prev => ({ ...prev, session_name: e.target.value }))}
                  className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="entry_fee" className="text-slate-200">Entry Fee ($)</Label>
                  <Input
                    id="entry_fee"
                    type="number"
                    min="0"
                    max="100"
                    step="5"
                    value={settings.entry_fee}
                    onChange={(e) => setSettings(prev => ({ ...prev, entry_fee: parseFloat(e.target.value) || 0 }))}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                  <p className="text-xs text-slate-400">
                    Set to 0 for free play
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration" className="text-slate-200">Duration (minutes)</Label>
                  <Select
                    value={settings.estimated_duration_minutes.toString()}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, estimated_duration_minutes: parseInt(value) }))}
                  >
                    <SelectTrigger id="duration" className="bg-slate-800/50 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 minutes</SelectItem>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="20">20 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="45">45 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min_players" className="text-slate-200">Minimum Players</Label>
                  <Input
                    id="min_players"
                    type="number"
                    min={game?.min_players}
                    max={settings.max_participants}
                    value={settings.min_participants}
                    onChange={(e) => setSettings(prev => ({ ...prev, min_participants: parseInt(e.target.value) || 2 }))}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_players" className="text-slate-200">Maximum Players</Label>
                  <Input
                    id="max_players"
                    type="number"
                    min={settings.min_participants}
                    max={game?.max_players}
                    value={settings.max_participants}
                    onChange={(e) => setSettings(prev => ({ ...prev, max_participants: parseInt(e.target.value) || 8 }))}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-slate-200">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Add any special rules or information about your session..."
                  value={settings.description}
                  onChange={(e) => setSettings(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-900/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Advanced Options</CardTitle>
              <CardDescription className="text-slate-300">
                Fine-tune your session settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-200">Allow Spectators</Label>
                  <p className="text-sm text-slate-400">
                    Let others watch your game live
                  </p>
                </div>
                <Switch
                  checked={settings.allow_spectators}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allow_spectators: checked }))}
                />
              </div>

              {settings.allow_spectators && (
                <>
                  <div className="flex items-center justify-between pl-6">
                    <div className="space-y-0.5">
                      <Label className="text-slate-200">Spectator Chat</Label>
                      <p className="text-sm text-slate-400">
                        Allow spectators to chat
                      </p>
                    </div>
                    <Switch
                      checked={settings.spectator_chat_enabled}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, spectator_chat_enabled: checked }))}
                    />
                  </div>

                  {game?.has_spectator_betting && (
                    <div className="flex items-center justify-between pl-6">
                      <div className="space-y-0.5">
                        <Label className="text-slate-200">Spectator Betting</Label>
                        <p className="text-sm text-slate-400">
                          Allow betting on match outcomes
                        </p>
                      </div>
                      <Switch
                        checked={settings.spectator_betting_enabled}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, spectator_betting_enabled: checked }))}
                      />
                    </div>
                  )}
                </>
              )}

              <Separator className="border-slate-600" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-200">Auto-Start When Full</Label>
                  <p className="text-sm text-slate-400">
                    Start automatically when max players join
                  </p>
                </div>
                <Switch
                  checked={settings.auto_start_when_full}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, auto_start_when_full: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-200">Tournament Mode</Label>
                  <p className="text-sm text-slate-400">
                    Part of a larger tournament bracket
                  </p>
                </div>
                <Switch
                  checked={settings.tournament_mode}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, tournament_mode: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-200">Private Session</Label>
                  <p className="text-sm text-slate-400">
                    Require password to join
                  </p>
                </div>
                <Switch
                  checked={settings.is_private}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, is_private: checked }))}
                />
              </div>

              {settings.is_private && (
                <div className="space-y-2 pl-6">
                  <Label htmlFor="password" className="text-slate-200">Session Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter password for private session"
                    value={settings.password}
                    onChange={(e) => setSettings(prev => ({ ...prev, password: e.target.value }))}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Summary */}
          <Card className="bg-slate-900/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Session Summary</CardTitle>
              <CardDescription className="text-slate-300">
                Review your session settings before creating
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">Game</span>
                  <span className="font-medium text-white">{game?.name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">Players</span>
                  <span className="font-medium text-white">
                    {settings.min_participants}-{settings.max_participants}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">Entry Fee</span>
                  <span className="font-medium text-white">
                    {settings.entry_fee === 0 ? 'Free' : `$${settings.entry_fee}`}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">Duration</span>
                  <span className="font-medium text-white">{settings.estimated_duration_minutes} minutes</span>
                </div>
                {settings.entry_fee > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-400">Estimated Prize Pool</span>
                    <span className="font-medium text-green-400">
                      ${(settings.entry_fee * settings.max_participants * 0.9).toFixed(2)}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                {settings.allow_spectators && (
                  <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">
                    <Eye className="h-3 w-3 mr-1" />
                    Spectators
                  </Badge>
                )}
                {settings.spectator_betting_enabled && (
                  <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">
                    <DollarSign className="h-3 w-3 mr-1" />
                    Betting
                  </Badge>
                )}
                {settings.tournament_mode && (
                  <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">
                    <Trophy className="h-3 w-3 mr-1" />
                    Tournament
                  </Badge>
                )}
                {settings.is_private && (
                  <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">
                    <Shield className="h-3 w-3 mr-1" />
                    Private
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/games/${gameSlug}`)}
              disabled={submitting}
              className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={submitting}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating Session...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Create Session
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
}