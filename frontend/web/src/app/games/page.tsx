/**
 * BetBet Gaming Engine - Games Browser
 * ===================================
 * 
 * Comprehensive games browser with filtering, search, and categories.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  GamepadIcon,
  Search,
  Filter,
  Star,
  Clock,
  Users,
  Play,
  Eye,
  TrendingUp,
  Grid3X3,
  List,
  Crown,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import { Game } from '@/lib/api';
import { cn } from '@/lib/utils';
import { ChessGameCard } from '@/components/games/ChessGameCard';

type ViewMode = 'grid' | 'list';
type SortOption = 'popularity' | 'name' | 'newest' | 'sessions';

export default function GamesPage() {
  const { isSignedIn } = useAuth();
  const api = useApi();
  
  const [games, setGames] = useState<Game[]>([]);
  const [gamesLoading, setGamesLoading] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<SortOption>('popularity');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [filteredGames, setFilteredGames] = useState<Game[]>([]);

  // Load games
  const loadGames = useCallback(async () => {
    try {
      setGamesLoading(true);
      const response = await api.getGames({
        limit: 50,
        is_featured: showFeaturedOnly || undefined,
        category: selectedCategory === 'all' ? undefined : selectedCategory,
        search: searchQuery || undefined,
        is_active: true
      });
      setGames(response.games || []);
    } catch (error) {
      console.error('Failed to load games:', error);
      setGames([]);
    } finally {
      setGamesLoading(false);
    }
  }, [api, selectedCategory, showFeaturedOnly, searchQuery]);

  // Filter and sort games
  useEffect(() => {
    let filtered = [...games];

    // Apply local filtering
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        game =>
          game.name.toLowerCase().includes(query) ||
          game.description?.toLowerCase().includes(query) ||
          game.category.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popularity':
          return Number(b.popularity_score) - Number(a.popularity_score);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'sessions':
          return b.total_sessions_played - a.total_sessions_played;
        default:
          return 0;
      }
    });

    setFilteredGames(filtered);
  }, [games, searchQuery, sortBy]);

  // Load games on component mount and filter changes
  useEffect(() => {
    loadGames();
  }, [loadGames]);

  const categories = [
    { value: 'all', label: 'All Games', count: games.length },
    { value: 'trivia', label: 'Trivia', count: games.filter(g => g.category === 'trivia').length },
    { value: 'strategy', label: 'Strategy', count: games.filter(g => g.category === 'strategy').length },
    { value: 'reaction_time', label: 'Reaction', count: games.filter(g => g.category === 'reaction_time').length },
    { value: 'sports', label: 'Sports', count: games.filter(g => g.category === 'sports').length },
    { value: 'puzzle', label: 'Puzzle', count: games.filter(g => g.category === 'puzzle').length },
  ];

  const GameCard = ({ game }: { game: Game }) => (
    <Card className="game-card group bg-slate-900/50 border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="group-hover:text-blue-400 transition-colors text-white">
              {game.name}
            </CardTitle>
            <CardDescription className="flex items-center space-x-2 text-slate-300">
              <span>{game.category}</span>
              {game.is_featured && <Star className="h-3 w-3 text-yellow-400" />}
              {game.is_beta && <Badge variant="destructive" className="text-xs bg-red-900/30 text-red-400 border-red-600">Beta</Badge>}
            </CardDescription>
          </div>
          <div className="text-right text-sm text-slate-300">
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-3 w-3 text-green-400" />
              <span>{Number(game.popularity_score).toFixed(1)}</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-slate-300 mb-4 line-clamp-2">
          {game.description}
        </p>
        
        <div className="grid grid-cols-2 gap-4 text-xs text-slate-400 mb-4">
          <div className="flex items-center space-x-1">
            <Users className="h-3 w-3" />
            <span>{game.min_players}-{game.max_players} players</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{game.estimated_duration_minutes} min</span>
          </div>
        </div>

        <div className="flex items-center space-x-2 mb-4">
          <Badge variant="secondary" className="bg-slate-700 text-slate-200 border-slate-600">{game.scoring_system}</Badge>
          {game.has_spectator_betting && (
            <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">Betting</Badge>
          )}
          {game.allows_practice_mode && (
            <Badge variant="outline" className="bg-slate-800/50 border-slate-600 text-slate-200">Practice</Badge>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="text-xs text-slate-400">
            {game.total_sessions_played} sessions played
          </div>
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" asChild className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white">
              <Link href={`/games/${game.slug}`}>
                <Eye className="h-3 w-3 mr-2" />
                View
              </Link>
            </Button>
            <Button size="sm" asChild className="bg-blue-600 hover:bg-blue-700 text-white">
              <Link href={`/games/${game.slug}`}>
                <Play className="h-3 w-3 mr-2" />
                Play
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const GameListItem = ({ game }: { game: Game }) => (
    <Card className="session-card bg-slate-900/50 border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div>
                <h3 className="font-semibold text-white">{game.name}</h3>
                <p className="text-sm text-slate-300">
                  {game.category} • {game.min_players}-{game.max_players} players • {game.estimated_duration_minutes} min
                </p>
              </div>
              {game.is_featured && <Star className="h-4 w-4 text-yellow-400" />}
            </div>
            <p className="text-sm text-slate-300 mt-2 line-clamp-1">
              {game.description}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-sm font-semibold text-white">{game.total_sessions_played}</div>
              <div className="text-xs text-slate-400">Sessions</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-semibold text-white">{Number(game.popularity_score).toFixed(1)}</div>
              <div className="text-xs text-slate-400">Rating</div>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" asChild className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white">
                <Link href={`/games/${game.slug}`}>View</Link>
              </Button>
              <Button size="sm" asChild className="bg-blue-600 hover:bg-blue-700 text-white">
                <Link href={`/games/${game.slug}`}>Play</Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Games</h1>
            <p className="text-slate-300">
              Discover and play skill-based multiplayer games
            </p>
          </div>
          <div className="flex gap-3">
            <Button asChild className="bg-yellow-600 hover:bg-yellow-700 text-white">
              <Link href="/games/chess">
                <Crown className="h-4 w-4 mr-2" />
                Chess Games
              </Link>
            </Button>
            <Button asChild variant="outline" className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700">
              <Link href="/games">
                <GamepadIcon className="h-4 w-4 mr-2" />
                All Games
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 text-slate-400 transform -translate-y-1/2" />
              <Input
                placeholder="Search games..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600 text-white">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label} ({category.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
              <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600 text-white">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popularity">Popularity</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="sessions">Most Played</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                className={viewMode === 'grid' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white'}
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                className={viewMode === 'list' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white'}
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="w-full justify-start bg-slate-800/50 border border-slate-600">
            {categories.map((category) => (
              <TabsTrigger key={category.value} value={category.value} className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">
                {category.label}
                {category.count > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs bg-slate-700 text-slate-200 border-slate-600">
                    {category.count}
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* Results Count */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-slate-300">
            {filteredGames.length} games found
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant={showFeaturedOnly ? 'default' : 'outline'}
              size="sm"
              className={showFeaturedOnly ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white'}
              onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            >
              <Star className="h-4 w-4 mr-2" />
              Featured Only
            </Button>
          </div>
        </div>

        {/* Featured Chess Section */}
        <Card className="bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-500/30">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Crown className="h-6 w-6 text-yellow-400" />
                <div>
                  <CardTitle className="text-xl text-white">Chess Games</CardTitle>
                  <CardDescription className="text-slate-300">
                    Join live chess matches or create your own game
                  </CardDescription>
                </div>
              </div>
              <Button asChild className="bg-yellow-600 hover:bg-yellow-700 text-white">
                <Link href="/games/chess">
                  View All Chess Games
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg">
                <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="font-medium text-white">Active Games</p>
                  <p className="text-sm text-slate-400">Join ongoing matches</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg">
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <p className="font-medium text-white">Quick Match</p>
                  <p className="text-sm text-slate-400">Find opponents fast</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg">
                <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <Eye className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <p className="font-medium text-white">Spectate</p>
                  <p className="text-sm text-slate-400">Watch and bet on games</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Games Grid/List */}
        {gamesLoading ? (
          <div className={cn(
            "grid gap-6",
            viewMode === 'grid' 
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
              : "grid-cols-1"
          )}>
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse bg-slate-900/50 border-slate-700">
                <CardHeader>
                  <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-slate-700 rounded"></div>
                    <div className="h-3 bg-slate-700 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredGames.length === 0 ? (
          <div className="text-center py-12">
            <GamepadIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-white">No games found</h3>
            <p className="text-slate-300">
              Try adjusting your search or filter criteria
            </p>
          </div>
        ) : (
          <div className={cn(
            "grid gap-6",
            viewMode === 'grid' 
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
              : "grid-cols-1"
          )}>
            {filteredGames.map((game) => 
              viewMode === 'grid' ? (
                game.slug === 'chess' ? (
                  <ChessGameCard key={game.id} game={game} />
                ) : (
                  <GameCard key={game.id} game={game} />
                )
              ) : (
                <GameListItem key={game.id} game={game} />
              )
            )}
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
}