/**
 * BetBet Platform - Chess Game Test Page
 * ======================================
 * 
 * Test page to verify chess game functionality and WebSocket integration
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Gamepad2, 
  Users, 
  Clock,
  Trophy,
  Play,
  Eye
} from 'lucide-react';

interface ChessGame {
  id: string;
  name: string;
  status: string;
  white_player?: any;
  black_player?: any;
  spectators_count: number;
  created_at: string;
  time_control?: string;
}

export default function ChessTestPage() {
  const api = useApi();
  const { isSignedIn, user } = useAuth();
  const [games, setGames] = useState<ChessGame[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [wsStatus, setWsStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [gameId, setGameId] = useState('');

  // Test WebSocket connection
  const testWebSocket = () => {
    setWsStatus('connecting');
    const ws = new WebSocket('ws://localhost:8000/ws');
    
    ws.onopen = () => {
      console.log('WebSocket connected');
      setWsStatus('connected');
      
      // Send a test message
      ws.send(JSON.stringify({ type: 'ping' }));
      
      // Close after 3 seconds
      setTimeout(() => {
        ws.close();
        setWsStatus('disconnected');
      }, 3000);
    };

    ws.onmessage = (event) => {
      console.log('WebSocket message:', event.data);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setWsStatus('disconnected');
    };

    ws.onclose = () => {
      console.log('WebSocket closed');
      setWsStatus('disconnected');
    };
  };

  // Load chess games
  const loadChessGames = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Test the chess games endpoint
      const response = await fetch('http://localhost:8000/api/gaming/chess/games', {
        headers: {
          'Authorization': `Bearer ${await api.getToken?.()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setGames(data.games || []);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err: any) {
      console.error('Failed to load chess games:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Create a new chess game
  const createChessGame = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:8000/api/gaming/chess/games', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await api.getToken?.()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          time_control: '10+0',
          is_rated: false,
          is_private: false
        })
      });

      if (response.ok) {
        const newGame = await response.json();
        setGames(prev => [newGame, ...prev]);
        setGameId(newGame.id);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err: any) {
      console.error('Failed to create chess game:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Join a chess game
  const joinChessGame = async (gameId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`http://localhost:8000/api/gaming/chess/games/${gameId}/join`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await api.getToken?.()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const updatedGame = await response.json();
        setGames(prev => prev.map(game => 
          game.id === gameId ? updatedGame : game
        ));
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err: any) {
      console.error('Failed to join chess game:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getWsStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
        return 'text-yellow-600';
      case 'disconnected':
        return 'text-red-600';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Chess Game Testing</h1>
        <p className="text-gray-600">
          Test chess game functionality and WebSocket integration
        </p>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="flex items-center justify-between p-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Authentication</p>
              <p className="text-lg font-semibold">
                {isSignedIn ? 'Signed In' : 'Not Signed In'}
              </p>
            </div>
            {isSignedIn ? (
              <CheckCircle className="h-8 w-8 text-green-500" />
            ) : (
              <XCircle className="h-8 w-8 text-red-500" />
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center justify-between p-4">
            <div>
              <p className="text-sm font-medium text-gray-600">WebSocket</p>
              <p className={`text-lg font-semibold ${getWsStatusColor(wsStatus)}`}>
                {wsStatus.charAt(0).toUpperCase() + wsStatus.slice(1)}
              </p>
            </div>
            <Button onClick={testWebSocket} size="sm" disabled={wsStatus === 'connecting'}>
              {wsStatus === 'connecting' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Test'
              )}
            </Button>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center justify-between p-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Chess Games</p>
              <p className="text-lg font-semibold">{games.length}</p>
            </div>
            <Gamepad2 className="h-8 w-8 text-blue-500" />
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex flex-wrap gap-4">
        <Button onClick={loadChessGames} disabled={loading || !isSignedIn}>
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <Eye className="h-4 w-4 mr-2" />
              Load Chess Games
            </>
          )}
        </Button>
        
        <Button onClick={createChessGame} disabled={loading || !isSignedIn} variant="outline">
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Create New Game
            </>
          )}
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <p className="text-red-800 font-medium">Error</p>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Chess Games List */}
      {games.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Chess Games</h2>
          <div className="grid gap-4">
            {games.map((game) => (
              <Card key={game.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{game.name || `Game ${game.id.slice(0, 8)}`}</CardTitle>
                    <Badge className={getStatusColor(game.status)}>
                      {game.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    Created: {new Date(game.created_at).toLocaleString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span>White: {game.white_player?.username || 'Waiting'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span>Black: {game.black_player?.username || 'Waiting'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4 text-gray-500" />
                      <span>{game.spectators_count} spectators</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span>{game.time_control || 'No limit'}</span>
                    </div>
                  </div>
                  
                  {game.status === 'waiting' && (
                    <div className="mt-4">
                      <Button 
                        onClick={() => joinChessGame(game.id)}
                        disabled={loading}
                        size="sm"
                      >
                        Join Game
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Game ID Display */}
      {gameId && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <p className="text-green-800 font-medium">Game Created Successfully</p>
            </div>
            <p className="text-green-700 mt-1">Game ID: {gameId}</p>
            <div className="mt-2">
              <Label htmlFor="gameId">Game ID (for testing):</Label>
              <Input 
                id="gameId"
                value={gameId} 
                readOnly 
                className="mt-1 bg-white"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {!isSignedIn && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-yellow-500" />
              <p className="text-yellow-800 font-medium">Authentication Required</p>
            </div>
            <p className="text-yellow-700 mt-1">
              Please sign in to test chess game functionality.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
