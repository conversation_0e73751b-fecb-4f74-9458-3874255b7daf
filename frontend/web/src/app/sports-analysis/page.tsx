/**
 * Sports Analysis & AI Chat Page
 * ==============================
 * 
 * Complete frontend integration for the Sports Analysis service,
 * featuring AI-powered chat, real-time analysis, and PPV streaming.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare, 
  Send, 
  TrendingUp, 
  BarChart3, 
  PlayCircle,
  Brain,
  Search,
  Calendar,
  Users,
  Star
} from 'lucide-react';

import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { sportsApi } from '@/lib/unified-api-client';
import { useUnifiedWebSocket } from '@/hooks/useUnifiedWebSocket';
import { useUser } from '@clerk/nextjs';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  confidence?: number;
  sources?: string[];
}

interface Analysis {
  id: string;
  title: string;
  summary: string;
  insights: string[];
  confidence_score: number;
  created_at: string;
  tags: string[];
  match_data?: any;
}

interface Prediction {
  id: string;
  match_info: string;
  prediction_type: string;
  predicted_outcome: string;
  confidence: number;
  reasoning: string;
  created_at: string;
}

interface StreamingEvent {
  id: string;
  title: string;
  description: string;
  start_time: string;
  price: number;
  thumbnail_url?: string;
  is_live: boolean;
}

export default function SportsAnalysisPage() {
  const { user } = useUser();
  const { connectionStatus, subscribeToModule } = useUnifiedWebSocket(user?.id);

  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isChatLoading, setIsChatLoading] = useState(false);

  // Analysis state
  const [analyses, setAnalyses] = useState<Analysis[]>([]);
  const [isAnalysesLoading, setIsAnalysesLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Predictions state
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [isPredictionsLoading, setIsPredictionsLoading] = useState(false);

  // Streaming state
  const [streamingEvents, setStreamingEvents] = useState<StreamingEvent[]>([]);
  const [isStreamingLoading, setIsStreamingLoading] = useState(false);

  // Active tab
  const [activeTab, setActiveTab] = useState('chat');

  /**
   * Subscribe to WebSocket events on mount
   */
  useEffect(() => {
    if (connectionStatus.connected) {
      subscribeToModule('sports');
    }
  }, [connectionStatus.connected, subscribeToModule]);

  /**
   * Load initial data
   */
  useEffect(() => {
    loadAnalyses();
    loadPredictions();
    loadStreamingEvents();
  }, []);

  /**
   * Load analyses from API
   */
  const loadAnalyses = async (search?: string) => {
    setIsAnalysesLoading(true);
    try {
      const filters = search ? { search } : {};
      const data = await sportsApi.getAnalysis(filters);
      setAnalyses(data.analyses || []);
    } catch (error) {
      console.error('Failed to load analyses:', error);
    } finally {
      setIsAnalysesLoading(false);
    }
  };

  /**
   * Load predictions from API
   */
  const loadPredictions = async () => {
    setIsPredictionsLoading(true);
    try {
      const data = await sportsApi.getPredictions();
      setPredictions(data.predictions || []);
    } catch (error) {
      console.error('Failed to load predictions:', error);
    } finally {
      setIsPredictionsLoading(false);
    }
  };

  /**
   * Load streaming events
   */
  const loadStreamingEvents = async () => {
    setIsStreamingLoading(true);
    try {
      const data = await sportsApi.getStreamingContent();
      setStreamingEvents(data.events || []);
    } catch (error) {
      console.error('Failed to load streaming events:', error);
    } finally {
      setIsStreamingLoading(false);
    }
  };

  /**
   * Send chat message to AI
   */
  const handleSendMessage = async () => {
    if (!chatInput.trim() || isChatLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput.trim(),
      timestamp: new Date().toISOString(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsChatLoading(true);

    try {
      const response = await sportsApi.getChatCompletion(userMessage.content);
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.response || 'I apologize, but I couldn\'t process your request.',
        timestamp: new Date().toISOString(),
        confidence: response.confidence,
        sources: response.sources,
      };

      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Chat error:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'I\'m sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date().toISOString(),
      };

      setChatMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsChatLoading(false);
    }
  };

  /**
   * Handle search for analyses
   */
  const handleSearchAnalyses = () => {
    loadAnalyses(searchQuery);
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  /**
   * Get confidence color
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Sports Analysis & AI Chat</h1>
          <p className="text-gray-600">
            Advanced analytics, AI-powered insights, and real-time sports intelligence
          </p>
        </div>

        {/* Connection Status */}
        {!connectionStatus.connected && (
          <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded-lg">
            <p className="text-yellow-800">
              Real-time updates unavailable. Check your connection.
            </p>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare size={16} />
              AI Chat
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <BarChart3 size={16} />
              Analysis
            </TabsTrigger>
            <TabsTrigger value="predictions" className="flex items-center gap-2">
              <TrendingUp size={16} />
              Predictions
            </TabsTrigger>
            <TabsTrigger value="streaming" className="flex items-center gap-2">
              <PlayCircle size={16} />
              Streaming
            </TabsTrigger>
          </TabsList>

          {/* AI Chat Tab */}
          <TabsContent value="chat">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Sports Analyst
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Chat Messages */}
                <div className="h-96 overflow-y-auto border rounded-lg p-4 space-y-4">
                  {chatMessages.length === 0 && (
                    <div className="text-center text-gray-500 mt-16">
                      <Brain className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <p>Ask me anything about sports analysis, predictions, or statistics!</p>
                      <p className="text-sm mt-2">
                        Try: "What are the key factors for today's NBA games?"
                      </p>
                    </div>
                  )}
                  
                  {chatMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.type === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        {message.confidence && (
                          <div className="mt-2 flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <div 
                                className={`w-2 h-2 rounded-full ${getConfidenceColor(message.confidence)}`}
                              />
                              <span className="text-xs opacity-75">
                                {Math.round(message.confidence * 100)}% confident
                              </span>
                            </div>
                          </div>
                        )}
                        <p className="text-xs opacity-75 mt-1">
                          {formatDate(message.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                  
                  {isChatLoading && (
                    <div className="flex justify-start">
                      <div className="bg-gray-100 px-4 py-2 rounded-lg">
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                          <span>Analyzing...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Chat Input */}
                <div className="flex gap-2">
                  <Textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="Ask about sports analysis, predictions, or statistics..."
                    className="flex-1 min-h-[50px] max-h-[100px]"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button 
                    onClick={handleSendMessage}
                    disabled={!chatInput.trim() || isChatLoading}
                    className="px-4"
                  >
                    <Send size={16} />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis">
            <div className="space-y-6">
              {/* Search */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex gap-2">
                    <Input
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search analyses by team, player, or topic..."
                      className="flex-1"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleSearchAnalyses();
                        }
                      }}
                    />
                    <Button onClick={handleSearchAnalyses} disabled={isAnalysesLoading}>
                      <Search size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Analysis Results */}
              <div className="grid gap-6">
                {isAnalysesLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Loading analyses...</p>
                  </div>
                ) : analyses.length === 0 ? (
                  <Card>
                    <CardContent className="text-center py-8">
                      <BarChart3 className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-gray-500">No analyses found</p>
                    </CardContent>
                  </Card>
                ) : (
                  analyses.map((analysis) => (
                    <Card key={analysis.id}>
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg">{analysis.title}</CardTitle>
                            <p className="text-sm text-gray-600 mt-1">
                              {formatDate(analysis.created_at)}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <div 
                              className={`w-3 h-3 rounded-full ${getConfidenceColor(analysis.confidence_score)}`}
                            />
                            <span className="text-sm font-medium">
                              {Math.round(analysis.confidence_score * 100)}%
                            </span>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-700">{analysis.summary}</p>
                        
                        {analysis.insights.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-2">Key Insights:</h4>
                            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                              {analysis.insights.map((insight, index) => (
                                <li key={index}>{insight}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {analysis.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {analysis.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>

          {/* Predictions Tab */}
          <TabsContent value="predictions">
            <div className="space-y-6">
              <div className="grid gap-4">
                {isPredictionsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Loading predictions...</p>
                  </div>
                ) : predictions.length === 0 ? (
                  <Card>
                    <CardContent className="text-center py-8">
                      <TrendingUp className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-gray-500">No predictions available</p>
                    </CardContent>
                  </Card>
                ) : (
                  predictions.map((prediction) => (
                    <Card key={prediction.id}>
                      <CardContent className="pt-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="font-medium">{prediction.match_info}</h3>
                            <p className="text-sm text-gray-600">
                              {prediction.prediction_type}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-2">
                              <div 
                                className={`w-3 h-3 rounded-full ${getConfidenceColor(prediction.confidence)}`}
                              />
                              <span className="text-sm font-medium">
                                {Math.round(prediction.confidence * 100)}%
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDate(prediction.created_at)}
                            </p>
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 p-4 rounded-lg mb-4">
                          <h4 className="font-medium text-green-700 mb-2">Predicted Outcome:</h4>
                          <p className="font-semibold">{prediction.predicted_outcome}</p>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-2">Reasoning:</h4>
                          <p className="text-sm text-gray-700">{prediction.reasoning}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>

          {/* Streaming Tab */}
          <TabsContent value="streaming">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {isStreamingLoading ? (
                  <div className="col-span-full text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Loading streaming events...</p>
                  </div>
                ) : streamingEvents.length === 0 ? (
                  <div className="col-span-full">
                    <Card>
                      <CardContent className="text-center py-8">
                        <PlayCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                        <p className="text-gray-500">No streaming events available</p>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  streamingEvents.map((event) => (
                    <Card key={event.id}>
                      <div className="relative">
                        {event.thumbnail_url && (
                          <img 
                            src={event.thumbnail_url} 
                            alt={event.title}
                            className="w-full h-48 object-cover rounded-t-lg"
                          />
                        )}
                        {event.is_live && (
                          <Badge className="absolute top-2 right-2 bg-red-500">
                            LIVE
                          </Badge>
                        )}
                      </div>
                      <CardContent className="p-4">
                        <h3 className="font-semibold mb-2">{event.title}</h3>
                        <p className="text-sm text-gray-600 mb-4">{event.description}</p>
                        
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-500">
                            <div className="flex items-center gap-1 mb-1">
                              <Calendar size={14} />
                              {formatDate(event.start_time)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Star size={14} />
                              ${event.price}
                            </div>
                          </div>
                          
                          <Button size="sm">
                            {event.is_live ? 'Watch Now' : 'Buy Access'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}