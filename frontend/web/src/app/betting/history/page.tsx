/**
 * BetBet Gaming Engine - Betting History Page
 * ==========================================
 * 
 * User's betting history with statistics and detailed bet information.
 */

'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { 
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Target,
  Trophy,
  Eye,
  Clock,
  Search,
  Filter,
  Download,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Minus,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { DateRange } from 'react-day-picker';

interface UserBet {
  id: string;
  market_id: string;
  market_name: string;
  outcome_id: string;
  outcome_name: string;
  session_id?: string;
  session_name?: string;
  tournament_id?: string;
  tournament_name?: string;
  bet_amount: number;
  odds: number;
  potential_return: number;
  actual_return?: number;
  status: 'pending' | 'won' | 'lost' | 'void' | 'cancelled';
  placed_at: string;
  settled_at?: string;
  market_type: string;
  game_name?: string;
}

interface BettingStats {
  total_bets: number;
  total_wagered: number;
  total_returned: number;
  net_profit: number;
  win_rate: number;
  average_odds: number;
  biggest_win: number;
  longest_streak: number;
  favorite_market_type: string;
  total_pending: number;
}

type BetStatus = 'all' | 'pending' | 'won' | 'lost' | 'void';
type SortBy = 'date' | 'amount' | 'odds' | 'return';

export default function BettingHistoryPage() {
  const { isSignedIn, isLoaded } = useAuth();
  const api = useApi();
  
  const [bets, setBets] = useState<UserBet[]>([]);
  const [stats, setStats] = useState<BettingStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<BetStatus>('all');
  const [marketTypeFilter, setMarketTypeFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [sortBy, setSortBy] = useState<SortBy>('date');
  
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      loadBettingHistory();
    }
  }, [isLoaded, isSignedIn]);

  const loadMockData = () => {
    // Mock data for UI testing - only accessible via ?demo=true
    const mockBets: UserBet[] = Array.from({ length: 25 }, (_, i) => {
      const statuses: Array<UserBet['status']> = ['won', 'lost', 'pending', 'void'];
      const marketTypes = ['match_winner', 'tournament_winner', 'player_performance', 'special_events'];
      const games = ['Trivia Master', 'Chess Pro', 'Quick Draw', 'Poker Night', 'Strategy Wars'];
      
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const betAmount = Math.floor(Math.random() * 200) + 10;
      const odds = 1.5 + Math.random() * 3;
      const potentialReturn = betAmount * odds;
      
      return {
        id: `bet-${i + 1}`,
        market_id: `market-${i + 1}`,
        market_name: `${games[i % games.length]} - Match Winner`,
        outcome_id: `outcome-${i + 1}`,
        outcome_name: `Player ${Math.floor(Math.random() * 4) + 1}`,
        session_id: `session-${i + 1}`,
        session_name: `Game Session ${i + 1}`,
        bet_amount: betAmount,
        odds: odds,
        potential_return: potentialReturn,
        actual_return: status === 'won' ? potentialReturn : status === 'lost' ? 0 : undefined,
        status: status,
        placed_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        settled_at: status !== 'pending' ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() : undefined,
        market_type: marketTypes[Math.floor(Math.random() * marketTypes.length)],
        game_name: games[i % games.length]
      };
    });

    // Generate mock stats
    const totalWagered = mockBets.reduce((sum, bet) => sum + bet.bet_amount, 0);
    const totalReturned = mockBets.filter(bet => bet.status === 'won').reduce((sum, bet) => sum + (bet.actual_return || 0), 0);
    const wonBets = mockBets.filter(bet => bet.status === 'won').length;
    const settledBets = mockBets.filter(bet => bet.status !== 'pending').length;
    
    const mockStats: BettingStats = {
      total_bets: mockBets.length,
      total_wagered: totalWagered,
      total_returned: totalReturned,
      net_profit: totalReturned - totalWagered,
      win_rate: settledBets > 0 ? (wonBets / settledBets) * 100 : 0,
      average_odds: mockBets.reduce((sum, bet) => sum + bet.odds, 0) / mockBets.length,
      biggest_win: Math.max(...mockBets.filter(bet => bet.actual_return).map(bet => bet.actual_return!), 0),
      longest_streak: 5,
      favorite_market_type: 'match_winner',
      total_pending: mockBets.filter(bet => bet.status === 'pending').length
    };

    setBets(mockBets);
    setStats(mockStats);
    setLoading(false);
  };

  const loadBettingHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if demo mode is requested via URL parameter
      const urlParams = new URLSearchParams(window.location.search);
      const isDemoMode = urlParams.get('demo') === 'true';
      
      if (isDemoMode) {
        console.log('🎭 Demo mode: Loading mock data for UI testing');
        loadMockData();
        return;
      }
      
      // TODO: Replace with actual user ID from Clerk Auth
      const userId = 'user-123'; // Placeholder - will be replaced with real auth integration
      
      try {
        // Attempt to load real betting history and stats from API
        const [bettingHistoryResponse, bettingStatsResponse] = await Promise.all([
          api.customBetting.getMarkets(), // TODO: Replace with actual betting history endpoint
          api.wallet.getAnalytics() // TODO: Replace with actual betting stats endpoint
        ]);

        setBets(bettingHistoryResponse.bets || []);
        setStats(bettingStatsResponse);
        
      } catch (apiError) {
        console.error('Failed to load betting history from API:', apiError);
        
        // Always show the actual error to developers for debugging
        setBets([]);
        setStats({
          total_bets: 0,
          total_wagered: 0,
          total_returned: 0,
          net_profit: 0,
          win_rate: 0,
          average_odds: 0,
          biggest_win: 0,
          longest_streak: 0,
          favorite_market_type: '',
          total_pending: 0
        });
        
        // Provide detailed error information for debugging
        const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown API error';
        setError(`API Error: ${errorMessage}. Check console for details.`);
        
        // In development, provide additional debugging info
        if (process.env.NODE_ENV === 'development') {
          console.group('🐛 Betting History API Debug Info');
          console.log('User ID:', userId);
          console.log('API Error:', apiError);
          console.log('Filters:', { statusFilter, marketTypeFilter });
          console.log('Tip: Use /betting/history?demo=true for UI testing with mock data');
          console.groupEnd();
        }
      }
    } catch (err) {
      console.error('Failed to load betting history:', err);
      setError('Failed to load betting history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: UserBet['status']) => {
    switch (status) {
      case 'won':
        return 'bg-green-500';
      case 'lost':
        return 'bg-red-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'void':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: UserBet['status']) => {
    switch (status) {
      case 'won':
        return <CheckCircle className="h-4 w-4" />;
      case 'lost':
        return <XCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'void':
        return <Minus className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Minus className="h-4 w-4" />;
    }
  };

  const getMarketTypeLabel = (type: string) => {
    switch (type) {
      case 'match_winner':
        return 'Match Winner';
      case 'tournament_winner':
        return 'Tournament Winner';
      case 'player_performance':
        return 'Player Performance';
      case 'special_events':
        return 'Special Events';
      default:
        return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const filteredBets = bets.filter(bet => {
    if (statusFilter !== 'all' && bet.status !== statusFilter) return false;
    if (marketTypeFilter !== 'all' && bet.market_type !== marketTypeFilter) return false;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!bet.market_name.toLowerCase().includes(query) && 
          !bet.outcome_name.toLowerCase().includes(query) &&
          !bet.game_name?.toLowerCase().includes(query)) {
        return false;
      }
    }
    if (dateRange?.from || dateRange?.to) {
      const betDate = new Date(bet.placed_at);
      if (dateRange.from && betDate < dateRange.from) return false;
      if (dateRange.to && betDate > dateRange.to) return false;
    }
    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.placed_at).getTime() - new Date(a.placed_at).getTime();
      case 'amount':
        return b.bet_amount - a.bet_amount;
      case 'odds':
        return b.odds - a.odds;
      case 'return':
        return (b.actual_return || 0) - (a.actual_return || 0);
      default:
        return 0;
    }
  });

  if (!isLoaded) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </MainLayout>
    );
  }

  if (!isSignedIn) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
          <p className="text-gray-600 mb-6">You need to be signed in to view your betting history.</p>
          <Button onClick={() => window.location.href = '/sign-in'}>
            Sign In
          </Button>
        </div>
      </MainLayout>
    );
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">Error Loading History</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-left max-w-md mx-auto">
              <h3 className="font-semibold text-blue-800 mb-2">🔧 Developer Options:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <a href="/betting/history?demo=true" className="underline">View with mock data</a></li>
                <li>• Check the API Debug Panel (bottom-right)</li>
                <li>• Verify backend is running on port 8000</li>
                <li>• Check console for detailed error logs</li>
              </ul>
            </div>
          )}
          <div className="space-x-2">
            <Button onClick={loadBettingHistory}>
              Try Again
            </Button>
            {process.env.NODE_ENV === 'development' && (
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/betting/history?demo=true'}
              >
                Load Demo Data
              </Button>
            )}
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center space-x-2">
              <BarChart3 className="h-8 w-8 text-primary" />
              <span>Betting History</span>
            </h1>
            <p className="text-muted-foreground">
              Track your betting performance and history
            </p>
          </div>
          
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>

        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  <Activity className="h-4 w-4 text-blue-600" />
                  <span className="text-2xl font-bold">{stats.total_bets}</span>
                </div>
                <p className="text-xs text-muted-foreground">Total Bets</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  <DollarSign className="h-4 w-4 text-orange-600" />
                  <span className="text-2xl font-bold">${stats.total_wagered.toLocaleString()}</span>
                </div>
                <p className="text-xs text-muted-foreground">Wagered</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-2xl font-bold">${stats.total_returned.toLocaleString()}</span>
                </div>
                <p className="text-xs text-muted-foreground">Returned</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className={`flex items-center justify-center space-x-1 mb-2 ${stats.net_profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.net_profit >= 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                  <span className="text-2xl font-bold">${Math.abs(stats.net_profit).toLocaleString()}</span>
                </div>
                <p className="text-xs text-muted-foreground">Net {stats.net_profit >= 0 ? 'Profit' : 'Loss'}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  <Target className="h-4 w-4 text-purple-600" />
                  <span className="text-2xl font-bold">{stats.win_rate.toFixed(1)}%</span>
                </div>
                <p className="text-xs text-muted-foreground">Win Rate</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-2xl font-bold">{stats.total_pending}</span>
                </div>
                <p className="text-xs text-muted-foreground">Pending</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={statusFilter} onValueChange={(value: BetStatus) => setStatusFilter(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="won">Won</SelectItem>
                    <SelectItem value="lost">Lost</SelectItem>
                    <SelectItem value="void">Void</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Market Type</label>
                <Select value={marketTypeFilter} onValueChange={setMarketTypeFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Markets</SelectItem>
                    <SelectItem value="match_winner">Match Winner</SelectItem>
                    <SelectItem value="tournament_winner">Tournament Winner</SelectItem>
                    <SelectItem value="player_performance">Player Performance</SelectItem>
                    <SelectItem value="special_events">Special Events</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <Select value={sortBy} onValueChange={(value: SortBy) => setSortBy(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="amount">Bet Amount</SelectItem>
                    <SelectItem value="odds">Odds</SelectItem>
                    <SelectItem value="return">Return</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground transform -translate-y-1/2" />
                  <Input
                    placeholder="Search bets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Betting History Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Betting History ({filteredBets.length} bets)</span>
              <Badge variant="outline">{filteredBets.length} results</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredBets.length === 0 ? (
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No bets found</h3>
                <p className="text-gray-600">Try adjusting your filters or place some bets to see your history.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredBets.map((bet) => (
                  <div key={bet.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                    <div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
                      <div className="space-y-2 flex-1">
                        <div className="flex flex-wrap items-center gap-2">
                          <Badge 
                            variant="secondary"
                            className={`${getStatusColor(bet.status)} text-white flex items-center space-x-1`}
                          >
                            {getStatusIcon(bet.status)}
                            <span className="capitalize">{bet.status}</span>
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {getMarketTypeLabel(bet.market_type)}
                          </Badge>
                          {bet.game_name && (
                            <Badge variant="outline" className="text-xs">
                              {bet.game_name}
                            </Badge>
                          )}
                        </div>
                        
                        <div>
                          <h4 className="font-medium">{bet.market_name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Selected: <span className="font-medium">{bet.outcome_name}</span>
                          </p>
                          {bet.session_name && (
                            <p className="text-sm text-muted-foreground">
                              Session: {bet.session_name}
                            </p>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span className="hidden sm:inline">
                              {new Date(bet.placed_at).toLocaleDateString()}
                            </span>
                            <span className="sm:hidden">
                              {new Date(bet.placed_at).toLocaleDateString(undefined, { 
                                month: 'short', 
                                day: 'numeric' 
                              })}
                            </span>
                          </span>
                          {bet.settled_at && (
                            <span className="flex items-center space-x-1">
                              <CheckCircle className="h-3 w-3" />
                              <span className="hidden sm:inline">
                                Settled {new Date(bet.settled_at).toLocaleDateString()}
                              </span>
                              <span className="sm:hidden">
                                Settled
                              </span>
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="lg:text-right">
                        <div className="grid grid-cols-2 lg:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Amount</p>
                            <p className="font-medium">${bet.bet_amount}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Odds</p>
                            <p className="font-medium">{bet.odds.toFixed(2)}x</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Potential</p>
                            <p className="font-medium">${bet.potential_return.toFixed(2)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Return</p>
                            <p className={`font-medium ${
                              bet.status === 'won' ? 'text-green-600' : 
                              bet.status === 'lost' ? 'text-red-600' : 
                              'text-muted-foreground'
                            }`}>
                              {bet.actual_return !== undefined 
                                ? `$${bet.actual_return.toFixed(2)}` 
                                : 'Pending'
                              }
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}