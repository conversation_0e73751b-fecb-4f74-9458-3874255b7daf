/**
 * BetBet Gaming Engine - Tournaments Page
 * ======================================
 * 
 * Browse and join tournaments with brackets, prizes, and leaderboards.
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  Trophy,
  Users,
  DollarSign,
  Calendar,
  Clock,
  ChevronRight,
  Search,
  Filter,
  TrendingUp,
  Crown,
  Medal,
  Timer,
  Zap,
  Award,
  Target,
  Gamepad2,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useApi } from '@/hooks/useApi';
import { cn } from '@/lib/utils';

// Tournament types
export interface Tournament {
  id: string;
  name: string;
  game_id: string;
  game_name: string;
  status: TournamentStatus;
  format: TournamentFormat;
  entry_fee: number;
  prize_pool: number;
  max_participants: number;
  current_participants: number;
  start_time: string;
  end_time?: string;
  rounds: number;
  current_round: number;
  description?: string;
  rules?: string;
  prizes: Prize[];
  created_at: string;
}

export enum TournamentStatus {
  UPCOMING = 'upcoming',
  REGISTRATION = 'registration',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum TournamentFormat {
  SINGLE_ELIMINATION = 'single_elimination',
  DOUBLE_ELIMINATION = 'double_elimination',
  ROUND_ROBIN = 'round_robin',
  SWISS = 'swiss',
  LADDER = 'ladder',
}

export interface Prize {
  position: number;
  amount: number;
  description?: string;
}

type TournamentFilter = 'all' | 'upcoming' | 'active' | 'registration' | 'my_tournaments';

export default function TournamentsPage() {
  const { isSignedIn } = useAuth();
  const api = useApi();
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<TournamentFilter>('all');
  const [selectedFormat, setSelectedFormat] = useState<string>('all');

  // Mock data for demonstration
  useEffect(() => {
    const mockTournaments: Tournament[] = [
      {
        id: '1',
        name: 'Friday Night Trivia Championship',
        game_id: 'trivia-master',
        game_name: 'Trivia Master',
        status: TournamentStatus.REGISTRATION,
        format: TournamentFormat.SINGLE_ELIMINATION,
        entry_fee: 10,
        prize_pool: 500,
        max_participants: 32,
        current_participants: 24,
        start_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        rounds: 5,
        current_round: 0,
        description: 'Test your knowledge in our weekly trivia tournament!',
        prizes: [
          { position: 1, amount: 250 },
          { position: 2, amount: 150 },
          { position: 3, amount: 100 },
        ],
        created_at: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Chess Masters Weekend',
        game_id: 'chess-pro',
        game_name: 'Chess Pro',
        status: TournamentStatus.IN_PROGRESS,
        format: TournamentFormat.SWISS,
        entry_fee: 25,
        prize_pool: 1000,
        max_participants: 64,
        current_participants: 64,
        start_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        rounds: 7,
        current_round: 3,
        description: 'Elite chess tournament for serious players.',
        prizes: [
          { position: 1, amount: 500 },
          { position: 2, amount: 300 },
          { position: 3, amount: 200 },
        ],
        created_at: new Date().toISOString(),
      },
      {
        id: '3',
        name: 'Reaction Time Ladder',
        game_id: 'quick-draw',
        game_name: 'Quick Draw',
        status: TournamentStatus.REGISTRATION,
        format: TournamentFormat.LADDER,
        entry_fee: 5,
        prize_pool: 200,
        max_participants: 100,
        current_participants: 45,
        start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        rounds: 0, // Ladder format is ongoing
        current_round: 0,
        description: 'Climb the ladder in this ongoing reaction time competition.',
        prizes: [
          { position: 1, amount: 100, description: 'Weekly top player' },
          { position: 2, amount: 60, description: 'Weekly runner-up' },
          { position: 3, amount: 40, description: 'Weekly third place' },
        ],
        created_at: new Date().toISOString(),
      },
    ];

    setTimeout(() => {
      setTournaments(mockTournaments);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: TournamentStatus) => {
    switch (status) {
      case TournamentStatus.UPCOMING:
        return 'text-blue-400 bg-blue-900/30 border-blue-600';
      case TournamentStatus.REGISTRATION:
        return 'text-green-400 bg-green-900/30 border-green-600';
      case TournamentStatus.IN_PROGRESS:
        return 'text-yellow-400 bg-yellow-900/30 border-yellow-600';
      case TournamentStatus.COMPLETED:
        return 'text-slate-400 bg-slate-800/30 border-slate-600';
      case TournamentStatus.CANCELLED:
        return 'text-red-400 bg-red-900/30 border-red-600';
    }
  };

  const getFormatIcon = (format: TournamentFormat) => {
    switch (format) {
      case TournamentFormat.SINGLE_ELIMINATION:
        return Trophy;
      case TournamentFormat.DOUBLE_ELIMINATION:
        return Medal;
      case TournamentFormat.ROUND_ROBIN:
        return Users;
      case TournamentFormat.SWISS:
        return Target;
      case TournamentFormat.LADDER:
        return TrendingUp;
    }
  };

  const filteredTournaments = tournaments.filter(tournament => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!tournament.name.toLowerCase().includes(query) && 
          !tournament.game_name.toLowerCase().includes(query)) {
        return false;
      }
    }

    if (selectedFilter !== 'all') {
      switch (selectedFilter) {
        case 'upcoming':
          if (tournament.status !== TournamentStatus.UPCOMING) return false;
          break;
        case 'active':
          if (tournament.status !== TournamentStatus.IN_PROGRESS) return false;
          break;
        case 'registration':
          if (tournament.status !== TournamentStatus.REGISTRATION) return false;
          break;
      }
    }

    if (selectedFormat !== 'all' && tournament.format !== selectedFormat) {
      return false;
    }

    return true;
  });

  const TournamentCard = ({ tournament }: { tournament: Tournament }) => {
    const FormatIcon = getFormatIcon(tournament.format);
    const participationProgress = (tournament.current_participants / tournament.max_participants) * 100;
    const isRegistrationOpen = tournament.status === TournamentStatus.REGISTRATION;
    const canJoin = isRegistrationOpen && tournament.current_participants < tournament.max_participants;

    return (
      <Card className="hover:shadow-lg transition-shadow bg-slate-900/50 border-slate-700">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center space-x-2 text-white font-bold">
                <Trophy className="h-5 w-5 text-yellow-400" />
                <span>{tournament.name}</span>
              </CardTitle>
              <CardDescription className="flex items-center space-x-2 mt-1 text-slate-300">
                <Gamepad2 className="h-4 w-4" />
                <span>{tournament.game_name}</span>
                <span className="text-slate-400">•</span>
                <FormatIcon className="h-4 w-4" />
                <span>{tournament.format.replace('_', ' ')}</span>
              </CardDescription>
            </div>
            <Badge className={cn("ml-2 border", getStatusColor(tournament.status))}>
              {tournament.status.replace('_', ' ')}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <p className="text-sm text-slate-300">
            {tournament.description}
          </p>

          {/* Participants Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Participants</span>
              <span className="font-medium text-white">
                {tournament.current_participants}/{tournament.max_participants}
              </span>
            </div>
            <Progress value={participationProgress} className="h-2" />
          </div>

          {/* Tournament Info Grid */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-emerald-400" />
              <div>
                <span className="text-slate-400">Entry: </span>
                <span className="font-medium text-white">${tournament.entry_fee}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Trophy className="h-4 w-4 text-yellow-400" />
              <div>
                <span className="text-slate-400">Prize: </span>
                <span className="font-medium text-white">${tournament.prize_pool}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-400" />
              <div>
                <span className="text-slate-400">Starts: </span>
                <span className="font-medium text-white">
                  {new Date(tournament.start_time).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-purple-400" />
              <div>
                <span className="text-slate-400">Time: </span>
                <span className="font-medium text-white">
                  {new Date(tournament.start_time).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
              </div>
            </div>
          </div>

          {/* Prizes */}
          <div className="space-y-2">
            <div className="text-sm font-medium text-white">Prize Distribution</div>
            <div className="space-y-1">
              {tournament.prizes.map((prize, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    {prize.position === 1 && <Crown className="h-4 w-4 text-yellow-400" />}
                    {prize.position === 2 && <Medal className="h-4 w-4 text-slate-400" />}
                    {prize.position === 3 && <Medal className="h-4 w-4 text-orange-400" />}
                    <span className="text-slate-300">
                      {prize.position === 1 ? '1st' : prize.position === 2 ? '2nd' : '3rd'} Place
                    </span>
                  </div>
                  <span className="font-medium text-white">${prize.amount}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-2">
            <Button variant="outline" size="sm" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white" asChild>
              <Link href={`/tournaments/${tournament.id}`}>
                View Details
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
            
            {isSignedIn && canJoin && (
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white" asChild>
                <Link href={`/tournaments/${tournament.id}/register`}>
                  <Zap className="h-4 w-4 mr-2" />
                  Join Tournament
                </Link>
              </Button>
            )}
            
            {tournament.status === TournamentStatus.IN_PROGRESS && (
              <Button size="sm" variant="secondary" className="bg-slate-700 hover:bg-slate-600 text-white" asChild>
                <Link href={`/tournaments/${tournament.id}/bracket`}>
                  <Trophy className="h-4 w-4 mr-2" />
                  View Bracket
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Tournaments</h1>
            <p className="text-slate-300">
              Compete in structured competitions for bigger prizes
            </p>
          </div>
          {isSignedIn && (
            <Button className="bg-blue-600 hover:bg-blue-700 text-white" asChild>
              <Link href="/tournaments/create">
                <Trophy className="h-4 w-4 mr-2" />
                Create Tournament
              </Link>
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Active Tournaments</p>
                  <p className="text-2xl font-bold text-white">12</p>
                </div>
                <Trophy className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Total Prize Pool</p>
                  <p className="text-2xl font-bold text-white">$4,520</p>
                </div>
                <DollarSign className="h-8 w-8 text-emerald-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Players Competing</p>
                  <p className="text-2xl font-bold text-white">347</p>
                </div>
                <Users className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-900/50 border-slate-700 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-300">Starting Soon</p>
                  <p className="text-2xl font-bold text-white">5</p>
                </div>
                <Timer className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 text-slate-400 transform -translate-y-1/2" />
              <Input
                placeholder="Search tournaments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select value={selectedFormat} onValueChange={setSelectedFormat}>
              <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600 text-white">
                <SelectValue placeholder="Format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Formats</SelectItem>
                <SelectItem value={TournamentFormat.SINGLE_ELIMINATION}>Single Elimination</SelectItem>
                <SelectItem value={TournamentFormat.DOUBLE_ELIMINATION}>Double Elimination</SelectItem>
                <SelectItem value={TournamentFormat.ROUND_ROBIN}>Round Robin</SelectItem>
                <SelectItem value={TournamentFormat.SWISS}>Swiss</SelectItem>
                <SelectItem value={TournamentFormat.LADDER}>Ladder</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tournament Tabs */}
        <Tabs value={selectedFilter} onValueChange={(value) => setSelectedFilter(value as TournamentFilter)}>
          <TabsList className="w-full justify-start bg-slate-800/50 border border-slate-600">
            <TabsTrigger value="all" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">All Tournaments</TabsTrigger>
            <TabsTrigger value="registration" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Open Registration</TabsTrigger>
            <TabsTrigger value="active" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">In Progress</TabsTrigger>
            <TabsTrigger value="upcoming" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">Upcoming</TabsTrigger>
            {isSignedIn && (
              <TabsTrigger value="my_tournaments" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 data-[state=active]:border-blue-500/30 text-slate-300">My Tournaments</TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value={selectedFilter} className="mt-6">
            {loading ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse bg-slate-900/50 border-slate-700">
                    <CardHeader>
                      <div className="h-6 bg-slate-700 rounded w-3/4"></div>
                      <div className="h-4 bg-slate-700 rounded w-1/2 mt-2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="h-4 bg-slate-700 rounded"></div>
                        <div className="h-4 bg-slate-700 rounded w-2/3"></div>
                        <div className="h-8 bg-slate-700 rounded"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredTournaments.length === 0 ? (
              <div className="text-center py-12">
                <Trophy className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2 text-white">No tournaments found</h3>
                <p className="text-slate-300 mb-4">
                  Try adjusting your search or filter criteria
                </p>
                {isSignedIn && (
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white" asChild>
                    <Link href="/tournaments/create">
                      <Trophy className="h-4 w-4 mr-2" />
                      Create Your Own Tournament
                    </Link>
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredTournaments.map((tournament) => (
                  <TournamentCard key={tournament.id} tournament={tournament} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}