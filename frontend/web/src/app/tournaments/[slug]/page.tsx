/**
 * BetBet Gaming Engine - Tournament Details Page
 * =============================================
 * 
 * Individual tournament page with details, registration, bracket, and betting.
 */

'use client';

import { TournamentDetails } from '@/components/tournaments/TournamentDetails';
import MainLayout from '@/components/layout/MainLayout';

interface TournamentPageProps {
  params: {
    slug: string;
  };
}

export default function TournamentPage({ params }: TournamentPageProps) {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <TournamentDetails tournamentSlug={params.slug} />
      </div>
    </MainLayout>
  );
}