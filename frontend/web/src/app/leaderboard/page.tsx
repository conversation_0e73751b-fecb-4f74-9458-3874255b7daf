/**
 * BetBet Cross-Platform Leaderboards & Social Competition System
 * =============================================================
 * 
 * Unified rankings across all BetBet gaming and betting activities.
 * Features cross-platform achievements, affiliation competition, and social challenges.
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import {
  Trophy,
  Crown,
  Medal,
  TrendingUp,
  TrendingDown,
  Minus,
  Star,
  Target,
  Zap,
  Award,
  Users,
  GamepadIcon,
  Clock,
  DollarSign,
  ChevronUp,
  ChevronDown,
  Filter,
  BarChart3,
  Swords,
  Globe,
  TrophyIcon,
  DiamondIcon,
  FlameIcon,
  ShieldIcon,
  Loader2,
  InfoIcon,
  ChevronRightIcon,
  MoreVerticalIcon,
  ShareIcon,
  TargetIcon,
} from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { cn } from '@/lib/utils';

// Import our custom components
import UnifiedPerformanceCard from '@/components/leaderboards/UnifiedPerformanceCard';
import LeaderboardTable from '@/components/leaderboards/LeaderboardTable';
import AchievementGalleryPanel from '@/components/leaderboards/AchievementGalleryPanel';
import AffiliationCompetitionPanel from '@/components/leaderboards/AffiliationCompetitionPanel';
import ActiveChallengesPanel from '@/components/leaderboards/ActiveChallengesPanel';
import PlatformChampionshipsPanel from '@/components/leaderboards/PlatformChampionshipsPanel';
import LiveNotifications from '@/components/leaderboards/LiveNotifications';
import AchievementCelebration from '@/components/leaderboards/AchievementCelebration';

// Import API hooks and WebSocket hooks
import { useLeaderboardData, useUserPerformance } from '@/hooks/useLeaderboardApi';
import { useLiveLeaderboardData } from '@/hooks/useLeaderboardWebSocket';

// Type definitions for the new cross-platform system
interface UnifiedUserPerformance {
  user_id: string;
  global_ranking: {
    unified_rank: number;
    unified_score: number;
    percentile: number;
    total_users: number;
  };
  platform_tier: {
    current: PlatformTier;
    next: PlatformTier;
    progress: number; // 0.0-1.0
    points_to_next: number;
  };
  module_rankings: {
    gaming: ModuleRanking;
    betting: ModuleRanking;
    trading: ModuleRanking;
    analyst: ModuleRanking;
  };
  cross_platform_consistency: number; // 0.0-1.0
  active_modules: string[];
  performance_trends: {
    daily_scores: ScoreTrend[];
    weekly_scores: ScoreTrend[];
    monthly_scores: ScoreTrend[];
  };
  achievement_summary: {
    total_unlocked: number;
    total_available: number;
    recent_unlocks: Achievement[];
    progress_towards_next: AchievementProgress[];
  };
}

interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar_url?: string;
  affiliation?: {
    id: string;
    name: string;
    logo_url?: string;
  };
  
  // Unified Performance
  unified_score: number; // 0-10000
  platform_tier: PlatformTier;
  tier_progress: number; // 0.0-1.0
  
  // Module-specific scores
  gaming_score: number;
  gaming_change: number;
  betting_score: number;
  betting_change: number;
  trading_score: number;
  trading_change: number;
  analyst_score: number;
  analyst_change: number;
  
  // Cross-platform metrics
  cross_platform_consistency: number;
  active_modules: string[];
  
  // Recent achievements (last 3)
  recent_achievements: Achievement[];
  
  // Performance trends
  score_history: {
    timestamp: string;
    score: number;
  }[];
}

interface ModuleRanking {
  rank: number;
  score: number;
  percentile: number;
  change_24h: number;
  change_7d: number;
  best_rank_ever: number;
  best_score_ever: number;
}

interface Achievement {
  id: string;
  achievement_code: string;
  name: string;
  description: string;
  category: string;
  tier: PlatformTier;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  points: number;
  icon_url?: string;
  unlocked_at?: string;
}

interface ScoreTrend {
  timestamp: string;
  unified_score: number;
  gaming_score: number;
  betting_score: number;
  trading_score: number;
  analyst_score: number;
}

interface AchievementProgress {
  achievement: Achievement;
  progress_percentage: number;
  current_values: Record<string, any>;
  missing_requirements: any[];
  estimated_completion: string;
}

type PlatformTier = 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond' | 'Master';
type LeaderboardView = 'unified' | 'gaming' | 'betting' | 'trading' | 'analyst';
type TimeRange = 'daily' | 'weekly' | 'monthly' | 'all_time';

export default function CrossPlatformLeaderboardPage() {
  const { isSignedIn, userId } = useAuth();
  
  // State for leaderboard configuration
  const [currentView, setCurrentView] = useState<LeaderboardView>('unified');
  const [timeRange, setTimeRange] = useState<TimeRange>('all_time');
  const [affiliationFilter, setAffiliationFilter] = useState<string | null>(null);
  
  // Data state
  const [userPerformance, setUserPerformance] = useState<UnifiedUserPerformance | null>(null);
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Live data with WebSocket integration
  const { 
    liveData, 
    connectionStatus, 
    setLiveData 
  } = useLiveLeaderboardData(currentView, userId);

  // Real leaderboard data loading
  useEffect(() => {
    const loadRealLeaderboardData = async (): Promise<void> => {
      try {
        setLoading(true);
        setError(null);

        // Import the real leaderboard API
        const { realLeaderboardAPI } = await import('@/lib/leaderboard-api');

        // Load real leaderboard data
        const leaderboardResult = await api.leaderboards.getLeaderboard({
          view: currentView,
          timeRange: timeRange,
          affiliationFilter: affiliationFilter !== 'all' ? affiliationFilter : undefined,
          limit: 50,
          offset: 0
        });

        setLeaderboardData(leaderboardResult.entries);
        setLiveData(leaderboardResult.entries);

        // Load user performance if signed in
        if (isSignedIn && userId) {
          try {
            const userPerformanceData = await api.leaderboards.getUserPerformance(userId);
            setUserPerformance(userPerformanceData);
          } catch (userError) {
            console.warn('Could not load user performance:', userError);
            // Don't fail the whole page if user performance fails
          }
        }

        setLoading(false);

      } catch (err: any) {
        console.error('Failed to load leaderboard data:', err);
        setError(`Failed to load leaderboard: ${err.message}`);
        setLoading(false);

        // Set empty state instead of fake data
        setLeaderboardData([]);
        setLiveData([]);
        setUserPerformance(null);
      }
    };

    loadRealLeaderboardData();
  }, [isSignedIn, userId, currentView, timeRange, affiliationFilter]);

  if (error) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="text-red-500">
                  <Trophy className="h-12 w-12 mx-auto mb-3" />
                </div>
                <h3 className="text-lg font-semibold">Unable to load leaderboards</h3>
                <p className="text-sm text-gray-600">{error}</p>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <TooltipProvider>
      <AuthenticatedLayout>
        <div className="leaderboard-dashboard max-w-7xl mx-auto px-4 py-6 space-y-8">
          
          {/* Header Section - Improved Contrast */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2 flex items-center gap-3 drop-shadow-sm">
                  <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl text-white shadow-lg">
                    <Trophy className="h-8 w-8" />
                  </div>
                  Platform Leaderboards
                </h1>
                <p className="text-slate-300 text-lg font-medium">
                  Compete across all BetBet gaming and betting activities
                </p>
              </div>
              
              {/* Global Controls - Enhanced Styling */}
              <div className="flex items-center gap-3">
                <Select value={timeRange} onValueChange={(value) => setTimeRange(value as TimeRange)}>
                  <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600 text-white">
                    <Clock className="h-4 w-4 mr-2 text-slate-300" />
                    <SelectValue placeholder="Time Range" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-600">
                    <SelectItem value="daily" className="text-white hover:bg-slate-700">Daily</SelectItem>
                    <SelectItem value="weekly" className="text-white hover:bg-slate-700">Weekly</SelectItem>
                    <SelectItem value="monthly" className="text-white hover:bg-slate-700">Monthly</SelectItem>
                    <SelectItem value="all_time" className="text-white hover:bg-slate-700">All Time</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button variant="outline" size="sm" className="bg-slate-800/50 border-slate-600 text-slate-200 hover:bg-slate-700 hover:text-white font-semibold">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </div>
          </div>

          {/* Personal Performance Overview */}
          {isSignedIn && userPerformance && (
            <div className="mb-8">
              <UnifiedPerformanceCard 
                userPerformance={userPerformance}
                isLoading={loading}
                showDetailed={true}
                onNavigateToModule={(module) => setCurrentView(module as LeaderboardView)}
              />
            </div>
          )}

          {/* Main Leaderboard Section - Enhanced Tabs */}
          <div className="mb-8">
            <Tabs value={currentView} onValueChange={(value) => setCurrentView(value as LeaderboardView)}>
              <TabsList className="grid grid-cols-5 w-full max-w-2xl mx-auto mb-6 bg-slate-800/50 border border-slate-600 p-1">
                <TabsTrigger value="unified" className="flex items-center gap-2 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">
                  <Star className="h-4 w-4" />
                  <span className="hidden sm:inline">Unified</span>
                </TabsTrigger>
                <TabsTrigger value="gaming" className="flex items-center gap-2 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">
                  <GamepadIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Gaming</span>
                </TabsTrigger>
                <TabsTrigger value="betting" className="flex items-center gap-2 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">
                  <TrophyIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Betting</span>
                </TabsTrigger>
                <TabsTrigger value="trading" className="flex items-center gap-2 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">Trading</span>
                </TabsTrigger>
                <TabsTrigger value="analyst" className="flex items-center gap-2 data-[state=active]:bg-indigo-600 data-[state=active]:text-white text-slate-300 font-semibold">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Analysis</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value={currentView}>
                <LeaderboardTable
                  data={liveData.length > 0 ? liveData : leaderboardData}
                  view={currentView}
                  isLoading={loading}
                  currentUserId={userId}
                  onUserClick={(userId) => {
                    // Navigate to user profile
                    window.open(`/profile/${userId}`, '_blank');
                  }}
                  showAffiliation={true}
                  showTrends={true}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Social Competition Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <AffiliationCompetitionPanel />
            <ActiveChallengesPanel />
          </div>

          {/* Achievement Gallery */}
          <div className="mb-8">
            <AchievementGalleryPanel />
          </div>

          {/* Platform Championships */}
          <div>
            <PlatformChampionshipsPanel />
          </div>

          {/* Loading Overlay */}
          {loading && (
            <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
              <Card className="p-6">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
                  <span className="text-lg font-medium">Loading platform leaderboards...</span>
                </div>
              </Card>
            </div>
          )}

        </div>
        
        {/* Live Notifications */}
        <LiveNotifications 
          userId={userId}
          maxVisible={5}
          position="top-right"
          showUserActivity={true}
        />
        
        {/* Achievement Celebration Modal */}
        <AchievementCelebration 
          userId={userId}
          onShare={(achievement) => {
            // Implement sharing functionality
            if (navigator.share) {
              navigator.share({
                title: `Achievement Unlocked: ${achievement.name}`,
                text: `I just unlocked "${achievement.name}" on BetBet! ${achievement.description}`,
                url: window.location.href
              });
            }
          }}
          onDownload={(achievement) => {
            // Implement certificate download
            console.log('Download achievement certificate:', achievement);
          }}
        />
      </AuthenticatedLayout>
    </TooltipProvider>
  );
}