/**
 * BetBet Gaming Engine - Global State Management
 * =============================================
 * 
 * Zustand store for managing gaming session state, user data, and UI state.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Game, GameSession, SessionParticipant, SessionStatus, User } from '@/lib/api';
import { GameStateData } from '@/lib/websocket';

// Auth state interface
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Gaming state interface
interface GamingState {
  // Current session
  currentSession: GameSession | null;
  currentGameState: GameStateData | null;
  participants: SessionParticipant[];
  
  // WebSocket connection
  isConnected: boolean;
  connectionError: string | null;
  
  // UI state
  isChatOpen: boolean;
  isSpectatorMode: boolean;
  selectedGame: Game | null;
  
  // Games and sessions
  games: Game[];
  sessions: GameSession[];
  featuredGames: Game[];
  
  // Loading states
  gamesLoading: boolean;
  sessionsLoading: boolean;
  sessionLoading: boolean;
}

// Combined store interface
interface AppStore extends AuthState, GamingState {
  // Auth actions
  login: (user: User, token: string) => void;
  logout: () => void;
  updateUser: (user: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
  
  // Gaming actions
  setCurrentSession: (session: GameSession | null) => void;
  setCurrentGameState: (gameState: GameStateData | null) => void;
  setParticipants: (participants: SessionParticipant[]) => void;
  updateParticipant: (participant: SessionParticipant) => void;
  
  // WebSocket actions
  setConnected: (connected: boolean) => void;
  setConnectionError: (error: string | null) => void;
  
  // UI actions
  setChatOpen: (open: boolean) => void;
  setSpectatorMode: (mode: boolean) => void;
  setSelectedGame: (game: Game | null) => void;
  
  // Data actions
  setGames: (games: Game[]) => void;
  setSessions: (sessions: GameSession[]) => void;
  setFeaturedGames: (games: Game[]) => void;
  addSession: (session: GameSession) => void;
  updateSession: (sessionId: string, updates: Partial<GameSession>) => void;
  
  // Loading actions
  setGamesLoading: (loading: boolean) => void;
  setSessionsLoading: (loading: boolean) => void;
  setSessionLoading: (loading: boolean) => void;
  
  // Utility actions
  reset: () => void;
  resetGameState: () => void;
}

// Initial state
const initialAuthState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
};

const initialGamingState: GamingState = {
  currentSession: null,
  currentGameState: null,
  participants: [],
  isConnected: false,
  connectionError: null,
  isChatOpen: false,
  isSpectatorMode: false,
  selectedGame: null,
  games: [],
  sessions: [],
  featuredGames: [],
  gamesLoading: false,
  sessionsLoading: false,
  sessionLoading: false,
};

// Create the store
export const useGameStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        ...initialAuthState,
        ...initialGamingState,

        // Auth actions
        login: (user: User, token: string) =>
          set(
            { 
              token, 
              user, 
              isAuthenticated: true, 
              isLoading: false 
            },
            false,
            'auth/login'
          ),

        logout: () =>
          set(
            {
              ...initialAuthState,
              ...initialGamingState,
            },
            false,
            'auth/logout'
          ),

        updateUser: (userUpdates: Partial<User>) =>
          set(
            (state) => ({
              user: state.user ? { ...state.user, ...userUpdates } : null,
            }),
            false,
            'auth/updateUser'
          ),

        setLoading: (loading: boolean) =>
          set({ isLoading: loading }, false, 'auth/setLoading'),

        // Gaming actions
        setCurrentSession: (session: GameSession | null) =>
          set({ currentSession: session }, false, 'gaming/setCurrentSession'),

        setCurrentGameState: (gameState: GameStateData | null) =>
          set({ currentGameState: gameState }, false, 'gaming/setCurrentGameState'),

        setParticipants: (participants: SessionParticipant[]) =>
          set({ participants }, false, 'gaming/setParticipants'),

        updateParticipant: (updatedParticipant: SessionParticipant) =>
          set(
            (state) => ({
              participants: state.participants.map((p) =>
                p.id === updatedParticipant.id ? updatedParticipant : p
              ),
            }),
            false,
            'gaming/updateParticipant'
          ),

        // WebSocket actions
        setConnected: (connected: boolean) =>
          set({ isConnected: connected }, false, 'websocket/setConnected'),

        setConnectionError: (error: string | null) =>
          set({ connectionError: error }, false, 'websocket/setConnectionError'),

        // UI actions
        setChatOpen: (open: boolean) =>
          set({ isChatOpen: open }, false, 'ui/setChatOpen'),

        setSpectatorMode: (mode: boolean) =>
          set({ isSpectatorMode: mode }, false, 'ui/setSpectatorMode'),

        setSelectedGame: (game: Game | null) =>
          set({ selectedGame: game }, false, 'ui/setSelectedGame'),

        // Data actions
        setGames: (games: Game[]) =>
          set({ games }, false, 'data/setGames'),

        setSessions: (sessions: GameSession[]) =>
          set({ sessions }, false, 'data/setSessions'),

        setFeaturedGames: (games: Game[]) =>
          set({ featuredGames: games }, false, 'data/setFeaturedGames'),

        addSession: (session: GameSession) =>
          set(
            (state) => ({
              sessions: [session, ...state.sessions],
            }),
            false,
            'data/addSession'
          ),

        updateSession: (sessionId: string, updates: Partial<GameSession>) =>
          set(
            (state) => ({
              sessions: state.sessions.map((s) =>
                s.id === sessionId ? { ...s, ...updates } : s
              ),
              currentSession:
                state.currentSession?.id === sessionId
                  ? { ...state.currentSession, ...updates }
                  : state.currentSession,
            }),
            false,
            'data/updateSession'
          ),

        // Loading actions
        setGamesLoading: (loading: boolean) =>
          set({ gamesLoading: loading }, false, 'loading/setGamesLoading'),

        setSessionsLoading: (loading: boolean) =>
          set({ sessionsLoading: loading }, false, 'loading/setSessionsLoading'),

        setSessionLoading: (loading: boolean) =>
          set({ sessionLoading: loading }, false, 'loading/setSessionLoading'),

        // Utility actions
        reset: () =>
          set(
            {
              ...initialAuthState,
              ...initialGamingState,
            },
            false,
            'utility/reset'
          ),

        resetGameState: () =>
          set(
            {
              currentSession: null,
              currentGameState: null,
              participants: [],
              isConnected: false,
              connectionError: null,
              isChatOpen: false,
              isSpectatorMode: false,
            },
            false,
            'utility/resetGameState'
          ),
      }),
      {
        name: 'betbet-game-store',
        partialize: (state) => ({
          // Only persist auth state and user preferences
          token: state.token,
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          isChatOpen: state.isChatOpen,
        }),
      }
    ),
    {
      name: 'BetBet Game Store',
    }
  )
);

// Selector hooks for common patterns
export const useAuth = () => {
  const store = useGameStore();
  return {
    user: store.user,
    token: store.token,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    login: store.login,
    logout: store.logout,
    updateUser: store.updateUser,
    setLoading: store.setLoading,
  };
};

export const useCurrentSession = () => {
  const store = useGameStore();
  return {
    session: store.currentSession,
    gameState: store.currentGameState,
    participants: store.participants,
    isConnected: store.isConnected,
    connectionError: store.connectionError,
    isSpectatorMode: store.isSpectatorMode,
    setCurrentSession: store.setCurrentSession,
    setCurrentGameState: store.setCurrentGameState,
    setParticipants: store.setParticipants,
    updateParticipant: store.updateParticipant,
    setConnected: store.setConnected,
    setConnectionError: store.setConnectionError,
    setSpectatorMode: store.setSpectatorMode,
  };
};

export const useGames = () => {
  const store = useGameStore();
  return {
    games: store.games,
    sessions: store.sessions,
    featuredGames: store.featuredGames,
    selectedGame: store.selectedGame,
    gamesLoading: store.gamesLoading,
    sessionsLoading: store.sessionsLoading,
    sessionLoading: store.sessionLoading,
    setGames: store.setGames,
    setSessions: store.setSessions,
    setFeaturedGames: store.setFeaturedGames,
    setSelectedGame: store.setSelectedGame,
    addSession: store.addSession,
    updateSession: store.updateSession,
    setGamesLoading: store.setGamesLoading,
    setSessionsLoading: store.setSessionsLoading,
    setSessionLoading: store.setSessionLoading,
  };
};

export default useGameStore;