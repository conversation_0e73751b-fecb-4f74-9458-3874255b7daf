import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface PriceLevel {
  price: number;
  quantity: number;
  orderCount: number;
}

export interface OrderBook {
  marketId: string | null;
  bids: PriceLevel[];
  asks: PriceLevel[];
  bestBid: number | null;
  bestAsk: number | null;
  spread: number | null;
  timestamp: string;
}

export interface Position {
  id: string;
  marketId: string;
  marketName: string;
  side: 'LONG' | 'SHORT';
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  unrealizedPnl: number;
  unrealizedPnlPercentage: number;
  marginUsed: number;
}

export interface Order {
  id: string;
  marketId: string;
  marketName: string;
  side: 'buy' | 'sell';
  orderType: 'market' | 'limit' | 'stop_loss' | 'take_profit';
  quantity: number;
  price: number | null;
  filledQuantity: number;
  remainingQuantity: number;
  status: 'pending' | 'partial' | 'filled' | 'cancelled' | 'rejected';
  timestamp: string;
}

export interface Market {
  id: string;
  name: string;
  symbol: string;
  lastPrice: number | null;
  bestBid: number | null;
  bestAsk: number | null;
  spread: number | null;
  spreadPercentage: number | null;
  volume24h: number;
  priceChange24h: number | null;
  priceChangePercentage24h: number | null;
  high24h: number | null;
  low24h: number | null;
}

export interface MarketTicker {
  marketId: string;
  symbol: string;
  lastPrice: number | null;
  priceChange24h: number | null;
  priceChangePercentage24h: number | null;
  volume24h: number;
}

export interface TradingState {
  // Connection state
  connected: boolean;
  
  // Market data
  selectedMarket: string | null;
  markets: Market[];
  marketTickers: MarketTicker[];
  orderBook: OrderBook;
  
  // User data
  positions: Position[];
  orders: Order[];
  
  // Portfolio summary
  totalUnrealizedPnl: number;
  totalMarginUsed: number;
  availableBalance: number;
  
  // UI state
  activeOrderTab: 'pending' | 'filled' | 'cancelled';
  showAdvancedOrderTypes: boolean;
  
  // Actions
  setConnected: (connected: boolean) => void;
  setSelectedMarket: (marketId: string) => void;
  updateMarkets: (markets: Market[]) => void;
  updateMarketTickers: (tickers: MarketTicker[]) => void;
  updateOrderBook: (orderBook: Partial<OrderBook>) => void;
  updatePositions: (positions: Position[]) => void;
  updateOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  removeOrder: (orderId: string) => void;
  updatePortfolioSummary: (summary: {
    totalUnrealizedPnl: number;
    totalMarginUsed: number;
    availableBalance: number;
  }) => void;
  setActiveOrderTab: (tab: 'pending' | 'filled' | 'cancelled') => void;
  setShowAdvancedOrderTypes: (show: boolean) => void;
  
  // Market data helpers
  getMarketById: (marketId: string) => Market | undefined;
  getPositionsByMarket: (marketId: string) => Position[];
  getOrdersByMarket: (marketId: string) => Order[];
  getOrdersByStatus: (status: string) => Order[];
}

export const useTradingStore = create<TradingState>()(
  devtools(
    (set, get) => ({
      // Initial state
      connected: false,
      selectedMarket: null,
      markets: [],
      marketTickers: [],
      orderBook: {
        marketId: null,
        bids: [],
        asks: [],
        bestBid: null,
        bestAsk: null,
        spread: null,
        timestamp: new Date().toISOString()
      },
      positions: [],
      orders: [],
      totalUnrealizedPnl: 0,
      totalMarginUsed: 0,
      availableBalance: 10000, // Mock initial balance
      activeOrderTab: 'pending',
      showAdvancedOrderTypes: false,

      // Actions
      setConnected: (connected) => set({ connected }),
      
      setSelectedMarket: (marketId) => set({ selectedMarket: marketId }),
      
      updateMarkets: (markets) => set({ markets }),
      
      updateMarketTickers: (marketTickers) => set({ marketTickers }),
      
      updateOrderBook: (orderBookUpdate) => 
        set((state) => ({
          orderBook: {
            ...state.orderBook,
            ...orderBookUpdate,
            timestamp: new Date().toISOString()
          }
        })),
      
      updatePositions: (positions) => {
        const totalUnrealizedPnl = positions.reduce(
          (sum, pos) => sum + pos.unrealizedPnl, 
          0
        );
        const totalMarginUsed = positions.reduce(
          (sum, pos) => sum + pos.marginUsed, 
          0
        );
        
        set({ 
          positions, 
          totalUnrealizedPnl, 
          totalMarginUsed 
        });
      },
      
      updateOrders: (orders) => set({ orders }),
      
      addOrder: (order) => 
        set((state) => ({
          orders: [...state.orders, order]
        })),
      
      updateOrder: (orderId, updates) =>
        set((state) => ({
          orders: state.orders.map(order =>
            order.id === orderId ? { ...order, ...updates } : order
          )
        })),
      
      removeOrder: (orderId) =>
        set((state) => ({
          orders: state.orders.filter(order => order.id !== orderId)
        })),
      
      updatePortfolioSummary: (summary) => set(summary),
      
      setActiveOrderTab: (activeOrderTab) => set({ activeOrderTab }),
      
      setShowAdvancedOrderTypes: (showAdvancedOrderTypes) => 
        set({ showAdvancedOrderTypes }),

      // Helpers
      getMarketById: (marketId) => {
        const { markets } = get();
        return markets.find(market => market.id === marketId);
      },
      
      getPositionsByMarket: (marketId) => {
        const { positions } = get();
        return positions.filter(position => position.marketId === marketId);
      },
      
      getOrdersByMarket: (marketId) => {
        const { orders } = get();
        return orders.filter(order => order.marketId === marketId);
      },
      
      getOrdersByStatus: (status) => {
        const { orders } = get();
        return orders.filter(order => order.status === status);
      }
    }),
    {
      name: 'trading-store'
    }
  )
);

// Selectors for optimized re-renders
export const useOrderBookData = () => useTradingStore((state) => state.orderBook);
export const usePositionsData = () => useTradingStore((state) => state.positions);
export const useOrdersData = () => useTradingStore((state) => state.orders);
export const useMarketData = () => useTradingStore((state) => ({
  markets: state.markets,
  selectedMarket: state.selectedMarket,
  marketTickers: state.marketTickers
}));
export const usePortfolioSummary = () => useTradingStore((state) => ({
  totalUnrealizedPnl: state.totalUnrealizedPnl,
  totalMarginUsed: state.totalMarginUsed,
  availableBalance: state.availableBalance
}));