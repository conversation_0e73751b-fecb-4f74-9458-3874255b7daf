# BetBet Gaming Engine - Frontend Web Application

A high-performance Next.js 14 frontend application for the BetBet Gaming Engine, featuring real-time game previews, comprehensive betting interfaces, and a responsive mobile-first design.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Backend API running on `http://localhost:8000`
- Clerk authentication configured

### Installation
```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env.local

# Configure your .env.local with:
# - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
# - CLERK_SECRET_KEY
# - NEXT_PUBLIC_API_BASE_URL (default: http://localhost:8000)
# - NEXT_PUBLIC_WS_URL (default: ws://localhost:8000)
```

### Development
```bash
# Run development server
npm run dev

# Server starts on http://localhost:3000 (or 3001 if 3000 is in use)
```

### Production Build
```bash
# Build for production
npm run build

# Start production server
npm run start

# Analyze bundle size
ANALYZE=true npm run build
```

## 🎮 Key Features

### Real-time Game Preview System
- **Live Session Browser**: Browse active gaming sessions with real-time state updates
- **Game Previews**: Lightweight preview cards showing:
  - Current game state and scores
  - Participant and spectator counts
  - Recent game actions and events
  - Excitement scoring for surfacing interesting games
  - Real-time updates via WebSocket (2-3 second intervals)

### Comprehensive Betting System
- **Betting Markets**: Display live betting markets with real-time odds
- **Bet Placement**: Intuitive modal interface for placing bets
- **Betting History**: Track user betting performance and statistics
- **WebSocket Updates**: Real-time odds changes and market updates
- **Mobile Responsive**: Optimized for all device sizes

### Performance Optimizations
- **Error Boundaries**: Graceful error handling in production
- **Performance Monitoring**: Built-in performance tracking utilities
- **Bundle Optimization**: Code splitting and lazy loading
- **Image Optimization**: WebP/AVIF support with responsive loading
- **Caching Strategy**: Aggressive caching for static assets

## 📁 Project Structure

```
src/
├── app/                      # Next.js 14 app directory
│   ├── betting/             # Betting-related pages
│   │   ├── history/         # User betting history
│   │   └── test/           # Betting system test suite
│   ├── live-sessions/       # Live session browser page
│   └── ...                  # Other pages
├── components/
│   ├── betting/             # Betting components
│   │   ├── BettingMarkets.tsx
│   │   ├── BetPlacementModal.tsx
│   │   └── AdminBettingMarketManager.tsx
│   ├── games/               # Game components
│   │   ├── GameCard.tsx
│   │   ├── GamePreview.tsx      # Real-time preview cards
│   │   └── LiveSessionBrowser.tsx # Session browser with filtering
│   ├── common/              # Common components
│   │   └── ErrorBoundary.tsx
│   └── layout/              # Layout components
├── hooks/
│   ├── useApi.ts           # API client hook
│   └── useWebSocket.ts     # WebSocket connection hook
├── lib/
│   ├── api.ts              # API client and types
│   └── performance.ts      # Performance monitoring utilities
└── styles/                 # Global styles

```

## 🔌 API Integration

### REST API Endpoints
- Games: `/api/v1/gaming/games`
- Sessions: `/api/v1/gaming/sessions`
- Betting Markets: `/api/v1/gaming/betting-markets`
- User Profile: `/api/v1/users/profile`
- Betting History: `/api/v1/gaming/users/{userId}/betting-history`

### WebSocket Events
- `game_state_update`: Real-time game state changes
- `participant_update`: Player join/leave events
- `odds_update`: Betting odds changes
- `new_bet`: New bet notifications
- `market_status_change`: Market open/close events

## 🧪 Testing

### Betting System Test Suite
Navigate to `/betting/test` to run comprehensive API and component tests:
- API health checks
- Games and sessions connectivity
- Betting markets functionality
- WebSocket connection testing
- Component integration verification

### Manual Testing Checklist
- [ ] Game browsing and filtering works
- [ ] Real-time previews update correctly
- [ ] Betting markets display with live odds
- [ ] Bet placement modal functions properly
- [ ] Betting history loads (with fallback to mock data)
- [ ] Mobile responsive design works across devices
- [ ] WebSocket connections establish and maintain
- [ ] Error boundaries catch and display errors gracefully

## 🚦 Quality Gates

### Code Quality
- TypeScript strict mode compliance
- ESLint rules pass (currently disabled in build)
- No console errors in development
- Proper error handling with try-catch blocks

### Performance
- Initial page load < 3 seconds
- Time to Interactive < 5 seconds
- API requests < 2 seconds (configurable timeout)
- WebSocket connection < 5 seconds
- Memory usage remains stable over time

### Security
- All API requests use authentication tokens
- CORS properly configured
- Security headers implemented (CSP, X-Frame-Options, etc.)
- No sensitive data in client-side code
- Input validation on all forms

### Accessibility
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Proper ARIA labels and roles

## 🔧 Configuration

### Environment Variables
```env
# Authentication (Required)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# Optional
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_SENTRY_DSN=
```

### Production Optimizations
The `next.config.mjs` includes:
- Image optimization with WebP/AVIF
- Security headers
- Caching strategies
- Bundle splitting
- CSS optimization
- Package import optimization

## 🚀 Deployment

### Prerequisites
1. Configure environment variables for production
2. Ensure backend API is accessible
3. Set up Clerk authentication for production

### Deployment Steps
```bash
# Build the application
npm run build

# Test the production build locally
npm run start

# Deploy to your platform
# Vercel: vercel deploy
# Docker: docker build -t betbet-frontend .
# Traditional: Copy .next, public, package.json to server
```

### Health Checks
- Frontend: `GET /` should return 200
- API Connection: Check `/betting/test` page
- WebSocket: Monitor browser console for connection status

## 📚 Development Guidelines

### Component Development
- Use TypeScript interfaces for all props
- Implement error boundaries for critical components
- Add loading states for async operations
- Include proper accessibility attributes
- Follow mobile-first responsive design

### State Management
- Use React hooks for local state
- API data managed via custom hooks
- WebSocket state handled in dedicated hook
- No global state management library (intentional)

### Performance Best Practices
- Lazy load heavy components
- Implement virtual scrolling for long lists
- Use React.memo for expensive renders
- Optimize images with Next.js Image component
- Monitor performance with built-in utilities

## 🐛 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Frontend tries 3001 if 3000 is taken
# Or manually specify: PORT=3002 npm run dev
```

**API Connection Failed**
- Verify backend is running on port 8000
- Check NEXT_PUBLIC_API_BASE_URL in .env.local
- Ensure CORS is configured on backend

**WebSocket Connection Issues**
- Check NEXT_PUBLIC_WS_URL configuration
- Verify WebSocket endpoint on backend
- Check browser console for connection errors

**Build Errors**
- Clear `.next` folder: `rm -rf .next`
- Clear node_modules: `rm -rf node_modules && npm install`
- Check for TypeScript errors: `npm run type-check`

## 📈 Monitoring

### Development
- Browser DevTools for network requests
- React Developer Tools for component debugging
- Performance tab for runtime analysis
- Console logs for WebSocket events

### Production
- Implement error tracking (Sentry integration ready)
- Monitor API response times
- Track WebSocket connection stability
- Analyze bundle sizes regularly

## 🤝 Contributing

1. Follow existing code patterns and conventions
2. Ensure all TypeScript types are properly defined
3. Test on multiple screen sizes
4. Run build before submitting changes
5. Document any new environment variables
6. Update this README with significant changes

## 📄 License

This project is part of the BetBet Gaming Engine platform.
