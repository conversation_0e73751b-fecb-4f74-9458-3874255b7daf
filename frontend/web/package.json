{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPatterns=\"(unit|components)\" --testPathIgnorePatterns=\"integration|e2e|accessibility|performance\"", "test:integration": "jest --testPathPatterns=\"integration\" --testTimeout=10000", "test:accessibility": "jest --testPathPatterns=\"accessibility\" --testTimeout=15000", "test:performance": "jest --testPathPatterns=\"performance\" --testTimeout=30000", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test:unit && npm run test:integration && npm run test:accessibility && npm run test:performance && npm run test:e2e", "test:expert-analyst": "jest --testPathPatterns=\"expert-analyst\" --coverage", "test:expert-analyst:unit": "jest --testPathPatterns=\"expert-analyst.*(api|components)\"", "test:expert-analyst:integration": "jest --testPathPatterns=\"expert-analyst.*integration\" --testTimeout=10000", "test:expert-analyst:e2e": "playwright test --grep=\"expert-analyst\"", "test:expert-analyst:security": "jest --testPath<PERSON>atterns=\"expert-analyst.*security\"", "test:expert-analyst:accessibility": "jest --testPathPatterns=\"expert-analyst.*accessibility\"", "test:expert-analyst:performance": "jest --testPath<PERSON>atterns=\"expert-analyst.*performance\"", "test:expert-analyst:all": "npm run test:expert-analyst:unit && npm run test:expert-analyst:integration && npm run test:expert-analyst:security && npm run test:expert-analyst:accessibility && npm run test:expert-analyst:performance && npm run test:expert-analyst:e2e", "test:coverage-threshold": "jest --coverage --coverageThreshold='{\"global\":{\"statements\":90,\"branches\":85,\"functions\":90,\"lines\":90}}'", "test:performance-benchmarks": "node scripts/validate-performance.js", "analyze": "@next/bundle-analyzer", "lighthouse": "lhci autorun", "lighthouse:ci": "lhci autorun --config=.lighthouserc.js"}, "dependencies": {"@clerk/nextjs": "^6.25.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "lucide-react": "^0.525.0", "next": "^14.2.30", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@lhci/cli": "^0.12.0", "@next/bundle-analyzer": "^14.0.0", "@playwright/test": "^1.40.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "axe-core": "^4.8.0", "eslint": "^8", "eslint-config-next": "14.2.30", "jest": "^30.0.4", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^30.0.4", "jest-html-reporters": "^3.1.0", "msw": "^2.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lighthouserc": {"ci": {"collect": {"url": ["http://localhost:3000"], "startServerCommand": "npm start", "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.8}]}}, "upload": {"target": "temporary-public-storage"}}}}