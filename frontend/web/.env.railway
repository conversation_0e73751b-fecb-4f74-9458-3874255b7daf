# BetBet Gaming Engine - Railway Production Environment
# ===================================================

# Application Environment
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Railway Configuration
RAILWAY_ENVIRONMENT=production

# API Configuration (UPDATE WITH YOUR RAILWAY BACKEND URL)
NEXT_PUBLIC_API_BASE_URL=https://gaming-engine-production.up.railway.app
NEXT_PUBLIC_WS_URL=wss://gaming-engine-production.up.railway.app

# Feature Flags
NEXT_PUBLIC_ENABLE_DEBUG_PANEL=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_ENABLE_REAL_TIME_PREVIEWS=true

# Authentication (Clerk - UPDATE WITH YOUR KEYS)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_RAILWAY_KEY
CLERK_SECRET_KEY=sk_live_YOUR_RAILWAY_SECRET

# Security
NEXT_PUBLIC_SECURE_COOKIES=true
NEXT_PUBLIC_SAME_SITE=strict

# WebSocket Configuration for Railway
NEXT_PUBLIC_WS_RECONNECT_INTERVAL=5000
NEXT_PUBLIC_WS_MAX_RECONNECT_ATTEMPTS=5
NEXT_PUBLIC_WS_HEARTBEAT_INTERVAL=30000

# Performance
NEXT_PUBLIC_CACHE_TTL=300
NEXT_PUBLIC_API_RATE_LIMIT=100

# Railway Specific
PORT=3000
RAILWAY_STATIC_URL=$RAILWAY_STATIC_URL
RAILWAY_PUBLIC_DOMAIN=$RAILWAY_PUBLIC_DOMAIN