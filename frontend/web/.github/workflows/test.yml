name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  NEXT_PUBLIC_API_URL: 'http://localhost:8000'

jobs:
  # Unit and Integration Tests
  unit-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        test-type: [unit, integration]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run unit tests
      if: matrix.test-type == 'unit'
      working-directory: ./frontend/web
      run: |
        npm run test -- --coverage --watchAll=false --testPathPattern="(unit|components)" --testPathIgnorePatterns="integration|e2e|accessibility|performance"
        
    - name: Run integration tests
      if: matrix.test-type == 'integration'
      working-directory: ./frontend/web
      run: |
        npm run test -- --coverage --watchAll=false --testPathPattern="integration" --testTimeout=10000
        
    - name: Upload test coverage
      if: matrix.test-type == 'unit'
      uses: codecov/codecov-action@v3
      with:
        directory: ./frontend/web/coverage
        fail_ci_if_error: false
        verbose: true

  # Accessibility Tests
  accessibility-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run accessibility tests
      working-directory: ./frontend/web
      run: |
        npm run test -- --watchAll=false --testPathPattern="accessibility" --testTimeout=15000
        
    - name: Generate accessibility report
      working-directory: ./frontend/web
      run: |
        npm run test -- --watchAll=false --testPathPattern="accessibility" --reporters=jest-html-reporters
        
    - name: Upload accessibility report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: accessibility-report
        path: ./frontend/web/jest_html_reporters.html

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run performance tests
      working-directory: ./frontend/web
      run: |
        npm run test -- --watchAll=false --testPathPattern="performance" --testTimeout=30000
        
    - name: Generate performance report
      working-directory: ./frontend/web
      run: |
        echo "Performance test results:" > performance-report.md
        npm run test -- --watchAll=false --testPathPattern="performance" --verbose >> performance-report.md
        
    - name: Upload performance report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-report
        path: ./frontend/web/performance-report.md

  # E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Install Playwright Browsers
      working-directory: ./frontend/web
      run: npx playwright install --with-deps ${{ matrix.browser }}
      
    - name: Build application
      working-directory: ./frontend/web
      run: npm run build
      
    - name: Start application
      working-directory: ./frontend/web
      run: |
        npm start &
        sleep 30  # Wait for app to start
        
    - name: Run E2E tests
      working-directory: ./frontend/web
      run: npx playwright test --project=${{ matrix.browser }}
      
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: ./frontend/web/playwright-report/
        retention-days: 30

  # Lighthouse Performance Audit
  lighthouse:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Build application
      working-directory: ./frontend/web
      run: npm run build
      
    - name: Start application
      working-directory: ./frontend/web
      run: |
        npm start &
        sleep 30
        
    - name: Run Lighthouse CI
      working-directory: ./frontend/web
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun || echo "Lighthouse CI failed, but continuing..."
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        
    - name: Upload Lighthouse report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: lighthouse-report
        path: ./frontend/web/.lighthouseci

  # Type Checking
  type-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run TypeScript check
      working-directory: ./frontend/web
      run: npm run type-check
      
    - name: Run linting
      working-directory: ./frontend/web
      run: npm run lint

  # Security Audit
  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run security audit
      working-directory: ./frontend/web
      run: npm audit --audit-level=moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high --file=frontend/web/package.json

  # Bundle Analysis
  bundle-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Build and analyze bundle
      working-directory: ./frontend/web
      run: |
        npm run build
        npx @next/bundle-analyzer
        
    - name: Upload bundle analysis
      uses: actions/upload-artifact@v3
      with:
        name: bundle-analysis
        path: ./frontend/web/.next/analyze/

  # Test Summary
  test-summary:
    runs-on: ubuntu-latest
    needs: [unit-tests, accessibility-tests, performance-tests, e2e-tests, type-check, security-audit]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Create test summary
      run: |
        echo "# Test Suite Summary" > test-summary.md
        echo "" >> test-summary.md
        echo "## Test Results" >> test-summary.md
        echo "- Unit Tests: ${{ needs.unit-tests.result }}" >> test-summary.md
        echo "- Accessibility Tests: ${{ needs.accessibility-tests.result }}" >> test-summary.md
        echo "- Performance Tests: ${{ needs.performance-tests.result }}" >> test-summary.md
        echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> test-summary.md
        echo "- Type Check: ${{ needs.type-check.result }}" >> test-summary.md
        echo "- Security Audit: ${{ needs.security-audit.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Artifacts" >> test-summary.md
        echo "Test reports and artifacts are available in the workflow run." >> test-summary.md
        
    - name: Upload test summary
      uses: actions/upload-artifact@v3
      with:
        name: test-summary
        path: test-summary.md
        
    - name: Comment PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('test-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });

# Workflow configuration for different environments
---
name: Test Suite - Staging

on:
  push:
    branches: [ staging ]

env:
  NODE_VERSION: '18'
  NEXT_PUBLIC_API_URL: 'https://api-staging.betbet.com'

jobs:
  staging-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run smoke tests against staging
      working-directory: ./frontend/web
      run: |
        npm run test -- --watchAll=false --testPathPattern="e2e/smoke" --testTimeout=60000
      env:
        TEST_ENV: staging
        BASE_URL: https://staging.betbet.com

---
name: Test Suite - Production Smoke Tests

on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  NEXT_PUBLIC_API_URL: 'https://api.betbet.com'

jobs:
  production-smoke-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'frontend/web/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./frontend/web
      run: npm ci
      
    - name: Run production smoke tests
      working-directory: ./frontend/web
      run: |
        npm run test -- --watchAll=false --testPathPattern="e2e/smoke" --testTimeout=120000
      env:
        TEST_ENV: production
        BASE_URL: https://betbet.com
        
    - name: Notify on failure
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: 'Production Smoke Tests Failed',
            body: 'Production smoke tests are failing. Please investigate immediately.',
            labels: ['bug', 'production', 'urgent']
          });