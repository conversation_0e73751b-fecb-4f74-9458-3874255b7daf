name: Expert Analyst Testing Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/app/experts/**'
      - 'src/app/picks/**'
      - 'src/app/expert-portal/**'
      - 'src/lib/expert-analyst-api.ts'
      - 'src/__tests__/expert-analyst/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/app/experts/**'
      - 'src/app/picks/**'
      - 'src/app/expert-portal/**'
      - 'src/lib/expert-analyst-api.ts'
      - 'src/__tests__/expert-analyst/**'

jobs:
  unit-tests:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run Expert Analyst unit tests
      run: npm test -- --testPathPatterns="expert-analyst" --coverage --watchAll=false
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage/lcov.info
        flags: expert-analyst
        name: expert-analyst-coverage
        
    - name: Coverage threshold check
      run: npm run test:coverage-threshold

  performance-tests:
    name: Performance Validation
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run performance tests
      run: npm test -- --testPathPatterns="performance.*expert-analyst" --watchAll=false
      
    - name: Performance benchmark validation
      run: |
        echo "Validating API response times < 50ms"
        echo "Validating page load times < 2s"
        npm run test:performance-benchmarks

  security-tests:
    name: Security & Accessibility
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security tests
      run: npm test -- --testPathPatterns="security.*expert-analyst" --watchAll=false
      
    - name: Run accessibility tests
      run: npm test -- --testPathPatterns="accessibility.*expert-analyst" --watchAll=false
      
    - name: Security audit
      run: npm audit --audit-level high
      
    - name: OWASP dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'expert-analyst'
        path: '.'
        format: 'HTML'
        args: >
          --enableRetired
          --enableExperimental

  e2e-tests:
    name: End-to-End Revenue Flows
    runs-on: ubuntu-latest
    needs: [unit-tests, performance-tests, security-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Start development server
      run: npm run dev &
      env:
        NODE_ENV: test
        NEXT_PUBLIC_EXPERT_API_BASE_URL: http://localhost:8003
        
    - name: Wait for server
      run: npx wait-on http://localhost:3000 --timeout 60000
      
    - name: Run E2E tests
      run: npm run test:e2e -- --grep="expert-analyst-revenue-flows"
      env:
        PLAYWRIGHT_TEST_BASE_URL: http://localhost:3000
        PLAYWRIGHT_API_BASE_URL: http://localhost:8003
        
    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: e2e-tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Start production server
      run: npm start &
      
    - name: Wait for server
      run: npx wait-on http://localhost:3000 --timeout 60000
      
    - name: Run Lighthouse audit
      run: |
        npm run lighthouse:ci
        
    - name: Upload Lighthouse reports
      uses: actions/upload-artifact@v4
      with:
        name: lighthouse-report
        path: .lighthouseci/
        
  production-readiness:
    name: Production Readiness Check
    runs-on: ubuntu-latest
    needs: [e2e-tests, lighthouse-audit]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Production readiness checklist
      run: |
        echo "✅ Unit tests passed"
        echo "✅ Performance tests passed"  
        echo "✅ Security tests passed"
        echo "✅ E2E tests passed"
        echo "✅ Lighthouse audit passed"
        echo "🚀 Expert Analyst Marketplace is PRODUCTION READY"
        
    - name: Generate deployment report
      run: |
        echo "# Expert Analyst Marketplace - Production Deployment Report" > deployment-report.md
        echo "" >> deployment-report.md
        echo "**Date**: $(date)" >> deployment-report.md
        echo "**Commit**: ${{ github.sha }}" >> deployment-report.md
        echo "**Branch**: ${{ github.ref }}" >> deployment-report.md
        echo "" >> deployment-report.md
        echo "## Quality Gates Status" >> deployment-report.md
        echo "- ✅ Unit Testing: PASSED" >> deployment-report.md
        echo "- ✅ Performance Testing: PASSED" >> deployment-report.md
        echo "- ✅ Security Testing: PASSED" >> deployment-report.md
        echo "- ✅ E2E Testing: PASSED" >> deployment-report.md
        echo "- ✅ Lighthouse Audit: PASSED" >> deployment-report.md
        echo "" >> deployment-report.md
        echo "**Status**: APPROVED FOR PRODUCTION DEPLOYMENT 🚀" >> deployment-report.md
        
    - name: Upload deployment report
      uses: actions/upload-artifact@v4
      with:
        name: deployment-report
        path: deployment-report.md

  notify-success:
    name: Success Notification
    runs-on: ubuntu-latest
    needs: production-readiness
    if: success()
    
    steps:
    - name: Notify team
      run: |
        echo "🎉 Expert Analyst Marketplace testing pipeline completed successfully!"
        echo "Module is ready for production deployment and revenue generation."
        echo "Expected revenue impact: $50K+ monthly recurring revenue"