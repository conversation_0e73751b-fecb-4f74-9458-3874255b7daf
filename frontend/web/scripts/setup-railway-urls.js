#!/usr/bin/env node
/**
 * Railway URL Configuration Helper
 * ===============================
 * 
 * This script helps identify and configure the correct Railway URLs for your deployment.
 * Run this with your Railway service URLs to update the environment configuration.
 */

const fs = require('fs');
const path = require('path');

function updateRailwayConfig(backendUrl, frontendUrl) {
  console.log('🚀 Configuring Railway URLs...\n');
  
  // Parse URLs
  const apiBaseUrl = backendUrl.replace(/\/$/, ''); // Remove trailing slash
  const wsUrl = apiBaseUrl.replace(/^https?:/, 'wss:'); // Convert to WebSocket URL
  
  console.log('📡 Backend Configuration:');
  console.log(`   API Base URL: ${apiBaseUrl}`);
  console.log(`   WebSocket URL: ${wsUrl}`);
  console.log(`   Frontend URL: ${frontendUrl}\n`);
  
  // Update .env.railway file
  const envPath = path.join(__dirname, '../.env.railway');
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  envContent = envContent
    .replace(/NEXT_PUBLIC_API_BASE_URL=.*/, `NEXT_PUBLIC_API_BASE_URL=${apiBaseUrl}`)
    .replace(/NEXT_PUBLIC_WS_URL=.*/, `NEXT_PUBLIC_WS_URL=${wsUrl}`);
  
  fs.writeFileSync(envPath, envContent);
  
  console.log('✅ Updated .env.railway with correct URLs');
  
  // Create Railway environment variables list
  const railwayVars = `
# Copy these environment variables to your Railway service:
# ======================================================

NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_API_BASE_URL=${apiBaseUrl}
NEXT_PUBLIC_WS_URL=${wsUrl}
NEXT_PUBLIC_ENABLE_DEBUG_PANEL=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_ENABLE_REAL_TIME_PREVIEWS=true
NEXT_PUBLIC_SECURE_COOKIES=true
NEXT_PUBLIC_SAME_SITE=strict
NEXT_PUBLIC_WS_RECONNECT_INTERVAL=5000
NEXT_PUBLIC_WS_MAX_RECONNECT_ATTEMPTS=5
NEXT_PUBLIC_WS_HEARTBEAT_INTERVAL=30000
NEXT_PUBLIC_CACHE_TTL=300
NEXT_PUBLIC_API_RATE_LIMIT=100
PORT=3000
`;

  fs.writeFileSync(path.join(__dirname, '../railway-env-vars.txt'), railwayVars);
  console.log('✅ Created railway-env-vars.txt with environment variables');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Copy the environment variables from railway-env-vars.txt to your Railway service');
  console.log('2. Add your Clerk authentication keys (NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY, CLERK_SECRET_KEY)');
  console.log('3. Test the connection at: ${frontendUrl}/api/health');
  console.log('4. Verify WebSocket connection at: ${frontendUrl}/live-sessions');
  
  return {
    apiBaseUrl,
    wsUrl,
    frontendUrl
  };
}

// Command line usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('Usage: node setup-railway-urls.js <backend-url> <frontend-url>');
    console.log('');
    console.log('Example:');
    console.log('  node setup-railway-urls.js https://your-backend.railway.app https://your-frontend.railway.app');
    process.exit(1);
  }
  
  const [backendUrl, frontendUrl] = args;
  updateRailwayConfig(backendUrl, frontendUrl);
}

module.exports = { updateRailwayConfig };