/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Admin-specific configuration
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },

  // Environment variables for admin
  env: {
    ADMIN_DASHBOARD_URL: process.env.ADMIN_DASHBOARD_URL || 'http://localhost:3001',
    API_GATEWAY_URL: process.env.API_GATEWAY_URL || 'http://localhost:8000',
    WEBSOCKET_URL: process.env.WEBSOCKET_URL || 'ws://localhost:8080',
  },

  // Admin-specific rewrites for API routes
  async rewrites() {
    return [
      {
        source: '/api/platform/:path*',
        destination: `${process.env.API_GATEWAY_URL || 'http://localhost:8000'}/api/v1/:path*`,
      },
    ];
  },

  // Security headers for admin dashboard
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-Admin-Dashboard',
            value: 'BetBet-Admin-v1.0',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;