# BetBet Admin Dashboard

The BetBet Admin Dashboard provides comprehensive monitoring and management capabilities for the BetBet platform's microservices architecture.

## Features

### 📊 Dashboard Overview
- **Platform Statistics**: Real-time metrics from all services
- **Service Health Monitoring**: Live status of all microservices
- **Revenue Tracking**: Financial metrics and trends
- **User Analytics**: Active users and engagement metrics

### 🔧 Module Overview
- **Service Status**: Real-time health checks for all services
- **Performance Metrics**: Response times and availability
- **Service Logs**: View recent logs from each service (mock data in containerized environment)
- **Service Management**: Restart controls with external script integration

### 🚀 Available Services

| Service | Description | Port | Status Endpoint |
|---------|-------------|------|-----------------|
| API Gateway | Central routing and authentication | 8000 | `/health` |
| Gaming Engine | Game sessions and tournaments | 8001 | `/api/gaming/health` |
| Custom Betting | P2P prediction markets | 8002 | `/api/custom-betting/health` |
| Odds Exchange | Trading platform | 8003 | `/api/trading/health` |
| Expert Analysis | Expert predictions | 8004 | `/api/experts/health` |
| Sports Analysis | Sports data and predictions | 8005 | `/api/sports/health` |
| Leaderboards | Rankings and achievements | 8006 | `/api/leaderboards/health` |
| Wallet Service | Financial transactions | 8007 | `/api/wallet/health` |

## Service Management

### Using the Admin Dashboard
1. Navigate to the **Module Overview** tab
2. Click on any service card to expand details
3. View service status, response times, and recent logs
4. Use the **Refresh All** button to update all service statuses

### Using the Command Line Script
For actual Docker container management, use the provided script:

```bash
# Show status of all services
./scripts/manage-services.sh status

# Restart a specific service
./scripts/manage-services.sh restart gaming

# View logs for a service
./scripts/manage-services.sh logs api-gateway

# Stop a service
./scripts/manage-services.sh stop custom-betting

# Start a service
./scripts/manage-services.sh start custom-betting

# Restart all services
./scripts/manage-services.sh restart-all
```

## API Endpoints

### Service Status API
```
GET /api/services
```
Returns status of all services with health checks and response times.

### Service Logs API
```
GET /api/services/logs?service=<service-name>&lines=<number>
```
Returns recent log entries for a specific service.

### Service Management API
```
POST /api/services
{
  "action": "restart|stop|start",
  "service": "<service-name>"
}
```
Manages service lifecycle (requires external orchestration in containerized environments).

## Development

### Running Locally
```bash
cd frontend/admin
npm install
npm run dev
```

### Environment Variables
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_key
CLERK_SECRET_KEY=your_clerk_secret
```

### Docker Development
```bash
docker-compose up admin-dashboard
```

## Architecture

### Service Discovery
The admin dashboard uses the API Gateway as a proxy to reach all backend services. This ensures:
- Consistent authentication and authorization
- Centralized logging and monitoring
- Load balancing and failover
- CORS handling

### Health Checks
Services are monitored using their analytics endpoints:
- `/api/{service}/analytics/overview` for service-specific health
- Response time monitoring
- Error rate tracking
- Availability status

### Logging
In production environments, integrate with:
- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Grafana + Loki**
- **Datadog** or **New Relic**
- **AWS CloudWatch** or **Azure Monitor**

## Security

### Authentication
- Clerk-based authentication for admin access
- JWT tokens for API communication
- Role-based access control (RBAC)

### Network Security
- All services communicate through the API Gateway
- CORS policies configured for admin dashboard
- Rate limiting on API endpoints

## Monitoring & Alerts

### Metrics Tracked
- Service availability and response times
- Error rates and success rates
- Resource utilization
- Business metrics (users, revenue, transactions)

### Alerting
Configure alerts for:
- Service downtime
- High error rates
- Performance degradation
- Resource exhaustion

## Troubleshooting

### Common Issues

1. **Services showing as offline**
   - Check if Docker containers are running: `docker ps`
   - Verify API Gateway is accessible: `curl http://localhost:8000/health`
   - Check network connectivity between containers

2. **CORS errors**
   - Ensure admin dashboard URL is in CORS whitelist
   - Verify API Gateway CORS configuration

3. **Authentication issues**
   - Check Clerk configuration
   - Verify environment variables are set correctly

4. **Service restart not working**
   - Use the command line script: `./scripts/manage-services.sh restart <service>`
   - Check Docker daemon is running
   - Verify container names match configuration

### Logs and Debugging
```bash
# View admin dashboard logs
docker logs betbet-admin

# View API Gateway logs
docker logs betbet-api-gateway

# View all service logs
./scripts/manage-services.sh logs <service-name>
```

## Contributing

1. Follow the existing code structure and patterns
2. Add proper TypeScript types for new features
3. Include error handling and loading states
4. Test with both mock and real data
5. Update documentation for new features

## License

This admin dashboard is part of the BetBet platform and follows the same licensing terms.
