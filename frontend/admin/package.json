{"name": "betbet-admin-dashboard", "version": "1.0.0", "description": "BetBet Platform Admin Dashboard - Isolated Super Admin Interface", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@clerk/nextjs": "^6.26.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.292.0", "next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.19.9", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "keywords": ["admin", "dashboard", "betbet", "management", "analytics", "platform"]}