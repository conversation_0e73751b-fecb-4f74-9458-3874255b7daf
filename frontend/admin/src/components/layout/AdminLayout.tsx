/**
 * BetBet Admin Layout Component
 * =============================
 * 
 * Main layout wrapper for the admin dashboard providing
 * sidebar navigation, header, and content structure.
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  BarChart3,
  Users,
  Settings,
  Database,
  Gamepad2,
  LineChart,
  Shield,
  Menu,
  X,
  LogOut,
  Bell,
  Search,
  Crown,
  Activity,
  Globe,
  Zap,
  Target,
  Percent
} from 'lucide-react';

const navigationItems = [
  {
    name: 'Overview',
    href: '/',
    icon: BarChart3,
    description: 'Platform overview and key metrics'
  },
  {
    name: 'Betting Markets',
    href: '/betting',
    icon: Target,
    description: 'Betting market management and settlement'
  },
  {
    name: 'Modules',
    href: '/modules',
    icon: Gamepad2,
    description: 'Gaming, betting, trading modules'
  },
  {
    name: 'Users',
    href: '/users',
    icon: Users,
    description: 'User management and analytics'
  },
  {
    name: 'System',
    href: '/system',
    icon: Database,
    description: 'System health and monitoring'
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: LineChart,
    description: 'Advanced platform analytics'
  },
  {
    name: 'Security',
    href: '/security',
    icon: Shield,
    description: 'Security monitoring and logs'
  },
  {
    name: 'Platform Settings',
    href: '/platform-settings',
    icon: Percent,
    description: 'Fee management & platform config'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'General platform configuration'
  },
];

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const NavItem = ({ item }: { item: typeof navigationItems[0] }) => {
    const isActive = pathname === item.href;
    const Icon = item.icon;

    return (
      <Link
        href={item.href}
        className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 group ${
          isActive
            ? 'bg-admin-accent text-white shadow-lg'
            : 'text-gray-300 hover:bg-admin-secondary hover:text-white'
        }`}
      >
        <Icon className={`h-5 w-5 ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}`} />
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">{item.name}</div>
          <div className="text-xs text-gray-400 truncate">{item.description}</div>
        </div>
      </Link>
    );
  };

  return (
    <div className="flex h-screen bg-admin-primary">
      {/* Sidebar */}
      <div className={`${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } fixed inset-y-0 left-0 z-50 w-72 bg-admin-primary border-r border-admin-secondary transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-6 border-b border-admin-secondary">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-admin-accent rounded-lg flex items-center justify-center">
              <Crown className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">BetBet Admin</h1>
              <p className="text-xs text-gray-400">Super Dashboard</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigationItems.map((item) => (
            <NavItem key={item.name} item={item} />
          ))}
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-admin-secondary">
          <div className="flex items-center gap-3 px-3 py-2">
            <div className="w-8 h-8 bg-admin-success rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-white truncate">
                {user ? `${user.first_name} ${user.last_name}` : 'Admin User'}
              </div>
              <div className="text-xs text-gray-400 truncate">
                {user?.roles.includes('super_admin') ? 'Super Administrator' : 'Administrator'}
              </div>
            </div>
            <button
              onClick={logout}
              className="text-gray-400 hover:text-white"
              title="Logout"
            >
              <LogOut className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-admin-primary border-b border-admin-secondary px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white"
              >
                <Menu className="h-6 w-6" />
              </button>
              
              {/* Search */}
              <div className="relative hidden md:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search admin panel..."
                  className="pl-10 pr-4 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-transparent w-64"
                />
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* System Status */}
              <div className="hidden md:flex items-center gap-2 px-3 py-1 bg-admin-success/20 border border-admin-success/30 rounded-full">
                <div className="w-2 h-2 bg-admin-success rounded-full animate-pulse"></div>
                <span className="text-xs text-admin-success font-medium">All Systems Operational</span>
              </div>

              {/* Quick Stats */}
              <div className="hidden lg:flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Activity className="h-4 w-4" />
                  <span>47.8K Users</span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>99.9% Uptime</span>
                </div>
                <div className="flex items-center gap-1">
                  <Zap className="h-4 w-4" />
                  <span>45ms Response</span>
                </div>
              </div>

              {/* Notifications */}
              <button className="relative text-gray-400 hover:text-white">
                <Bell className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-admin-danger rounded-full flex items-center justify-center">
                  <span className="text-xs text-white">3</span>
                </span>
              </button>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto bg-admin-primary">
          <div className="container mx-auto px-6 py-8">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}