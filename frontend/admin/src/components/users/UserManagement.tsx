/**
 * BetBet Admin User Management Component
 * ====================================
 * 
 * User management interface with Clerk integration
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Ban,
  Shield,
  Mail,
  Calendar,
  Activity,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Clock,
  UserCheck,
  UserX,
  Check,
  Trash2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';

interface User {
  id: string;
  clerk_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  avatar_url?: string;
  status: 'active' | 'suspended' | 'banned';
  role: 'user' | 'admin' | 'moderator';
  created_at: string;
  last_login?: string;
  total_bets: number;
  total_volume: number;
  verification_status: 'verified' | 'pending' | 'rejected';
}

interface UserStats {
  total_users: number;
  active_users: number;
  new_users_today: number;
  verified_users: number;
  suspended_users: number;
  banned_users: number;
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats>({
    total_users: 0,
    active_users: 0,
    new_users_today: 0,
    verified_users: 0,
    suspended_users: 0,
    banned_users: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }
      
      setUsers(data.users || []);
      setStats(data.stats || stats);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      setError(error.message || 'Failed to load user data');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: 'suspend' | 'activate' | 'ban' | 'verify') => {
    try {
      const response = await fetch('/api/users/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, action })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ${action} user`);
      }
      
      // Refresh users list
      await fetchUsers();
    } catch (error) {
      console.error(`Error ${action}ing user:`, error);
      setError(`Failed to ${action} user`);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(new Set(filteredUsers.map(u => u.id)));
      setShowBulkActions(true);
    } else {
      setSelectedUsers(new Set());
      setShowBulkActions(false);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    const newSelected = new Set(selectedUsers);
    if (checked) {
      newSelected.add(userId);
    } else {
      newSelected.delete(userId);
    }
    setSelectedUsers(newSelected);
    setShowBulkActions(newSelected.size > 0);
  };

  const handleBulkAction = async (action: string) => {
    // Implement bulk actions
    console.log('Bulk action:', action, 'on users:', Array.from(selectedUsers));
    // After action, clear selection
    setSelectedUsers(new Set());
    setShowBulkActions(false);
    await fetchUsers();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-admin-success" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-admin-warning" />;
      case 'banned':
        return <XCircle className="h-4 w-4 text-admin-danger" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-admin-success/20 text-admin-success';
      case 'suspended':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'banned':
        return 'bg-admin-danger/20 text-admin-danger';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-admin-success/20 text-admin-success';
      case 'pending':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'rejected':
        return 'bg-admin-danger/20 text-admin-danger';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">User Management</h2>
          <p className="text-gray-400 mt-1">
            Manage platform users with Clerk integration
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button 
            onClick={fetchUsers} 
            disabled={loading}
            className="admin-button-secondary"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button className="admin-button-primary">
            <Users className="h-4 w-4 mr-2" />
            Export Users
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="admin-card border-admin-danger/50 bg-admin-danger/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-admin-danger">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* User Stats */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total</p>
                <p className="text-2xl font-bold text-white">{stats.total_users.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-admin-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active</p>
                <p className="text-2xl font-bold text-admin-success">{stats.active_users.toLocaleString()}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-admin-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">New</p>
                <p className="text-2xl font-bold text-admin-accent">{stats.new_users_today.toLocaleString()}</p>
              </div>
              <Calendar className="h-8 w-8 text-admin-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Verified</p>
                <p className="text-2xl font-bold text-admin-info">{stats.verified_users.toLocaleString()}</p>
              </div>
              <Shield className="h-8 w-8 text-admin-info" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Suspended</p>
                <p className="text-2xl font-bold text-admin-warning">{stats.suspended_users.toLocaleString()}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-admin-warning" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Banned</p>
                <p className="text-2xl font-bold text-admin-danger">{stats.banned_users.toLocaleString()}</p>
              </div>
              <XCircle className="h-8 w-8 text-admin-danger" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="admin-card">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search users by email, username, or name..."
                className="w-full pl-10 pr-4 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-admin-accent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              {showBulkActions && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="admin-button-secondary">
                      Bulk Actions ({selectedUsers.size})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-admin-secondary border-admin-secondary">
                    <DropdownMenuItem onClick={() => handleBulkAction('suspend')} className="text-admin-warning hover:bg-admin-accent/20">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Suspend Selected
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('ban')} className="text-admin-danger hover:bg-admin-accent/20">
                      <Ban className="h-4 w-4 mr-2" />
                      Ban Selected
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-admin-accent/20" />
                    <DropdownMenuItem onClick={() => handleBulkAction('delete')} className="text-admin-danger hover:bg-admin-accent/20">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Selected
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <select
                className="px-4 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="banned">Banned</option>
              </select>
              <Button variant="outline" className="admin-button-secondary">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-admin-accent" />
            Users ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Activity className="h-8 w-8 text-admin-accent animate-spin mx-auto mb-4" />
                <p className="text-gray-400">Loading users...</p>
              </div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">No users found</p>
                <p className="text-gray-500 text-sm mt-2">
                  {searchTerm ? 'Try adjusting your search criteria' : error ? 'Check Clerk configuration' : 'User data will appear here when available'}
                </p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-admin-secondary">
                    <th className="w-12 py-3 px-4">
                      <Checkbox 
                        checked={selectedUsers.size === filteredUsers.length && filteredUsers.length > 0}
                        onCheckedChange={handleSelectAll}
                        className="border-gray-400"
                      />
                    </th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Verification</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Activity</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Volume</th>
                    <th className="text-left py-3 px-4 text-gray-400 font-medium">Joined</th>
                    <th className="w-16 py-3 px-4"></th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b border-admin-secondary/50 hover:bg-admin-secondary/30">
                      <td className="py-4 px-4">
                        <Checkbox 
                          checked={selectedUsers.has(user.id)}
                          onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                          className="border-gray-400"
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-admin-accent/20 flex items-center justify-center text-admin-accent font-semibold">
                            {user.avatar_url ? (
                              <img src={user.avatar_url} alt="" className="w-10 h-10 rounded-full" />
                            ) : (
                              (user.first_name?.[0] || user.email[0]).toUpperCase()
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-white">
                              {user.first_name && user.last_name ? 
                                `${user.first_name} ${user.last_name}` : 
                                user.username || 'Anonymous'
                              }
                            </p>
                            <p className="text-sm text-gray-400">{user.email}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-1">
                          {user.status === 'active' ? (
                            <CheckCircle className="h-4 w-4 text-admin-success" />
                          ) : user.status === 'suspended' ? (
                            <AlertTriangle className="h-4 w-4 text-admin-warning" />
                          ) : (
                            <XCircle className="h-4 w-4 text-admin-danger" />
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-1">
                          {user.verification_status === 'verified' ? (
                            <CheckCircle className="h-4 w-4 text-admin-success" />
                          ) : user.verification_status === 'pending' ? (
                            <Clock className="h-4 w-4 text-admin-warning" />
                          ) : (
                            <XCircle className="h-4 w-4 text-admin-danger" />
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <p className="text-white">{user.total_bets} bets</p>
                          <p className="text-gray-400">
                            {user.last_login ? 
                              `Last: ${new Date(user.last_login).toLocaleDateString()}` : 
                              'Never logged in'
                            }
                          </p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-white font-medium">${user.total_volume.toLocaleString()}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-gray-400 text-sm">
                          {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </td>
                      <td className="py-4 px-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="h-8 w-8 p-0 hover:bg-admin-secondary"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="bg-admin-secondary border-admin-accent/20">
                            <DropdownMenuItem className="hover:bg-admin-accent/20">
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem className="hover:bg-admin-accent/20">
                              <Mail className="h-4 w-4 mr-2" />
                              Send Email
                            </DropdownMenuItem>
                            <DropdownMenuSeparator className="bg-admin-accent/20" />
                            
                            {user.status === 'active' ? (
                              <DropdownMenuItem 
                                onClick={() => handleUserAction(user.id, 'suspend')}
                                className="text-admin-warning hover:bg-admin-accent/20"
                              >
                                <AlertTriangle className="h-4 w-4 mr-2" />
                                Suspend User
                              </DropdownMenuItem>
                            ) : user.status === 'suspended' ? (
                              <DropdownMenuItem 
                                onClick={() => handleUserAction(user.id, 'activate')}
                                className="text-admin-success hover:bg-admin-accent/20"
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Activate User
                              </DropdownMenuItem>
                            ) : null}
                            
                            {user.status !== 'banned' && (
                              <DropdownMenuItem 
                                onClick={() => handleUserAction(user.id, 'ban')}
                                className="text-admin-danger hover:bg-admin-accent/20"
                              >
                                <Ban className="h-4 w-4 mr-2" />
                                Ban User
                              </DropdownMenuItem>
                            )}
                            
                            {user.verification_status !== 'verified' && (
                              <DropdownMenuItem 
                                onClick={() => handleUserAction(user.id, 'verify')}
                                className="text-admin-info hover:bg-admin-accent/20"
                              >
                                <Shield className="h-4 w-4 mr-2" />
                                Verify User
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}