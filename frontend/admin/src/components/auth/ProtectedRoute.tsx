'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
}

export default function ProtectedRoute({ 
  children, 
  requiredRoles = ['admin'], 
  requiredPermissions = [] 
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login');
        return;
      }

      // Check if user has required roles
      if (requiredRoles.length > 0 && user) {
        const hasRequiredRole = requiredRoles.some(role => user.roles.includes(role));
        if (!hasRequiredRole) {
          console.error('Access denied: Missing required roles', requiredRoles);
          router.push('/login');
          return;
        }
      }

      // Check if user has required permissions
      if (requiredPermissions.length > 0 && user) {
        const hasRequiredPermission = requiredPermissions.some(permission => 
          user.permissions.includes(permission)
        );
        if (!hasRequiredPermission) {
          console.error('Access denied: Missing required permissions', requiredPermissions);
          router.push('/login');
          return;
        }
      }
    }
  }, [isLoading, isAuthenticated, user, router, requiredRoles, requiredPermissions]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-admin-primary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-accent mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return <>{children}</>;
}
