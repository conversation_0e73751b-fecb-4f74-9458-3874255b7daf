/**
 * Metric Card Component
 * ====================
 * 
 * Reusable metric display card for admin dashboard
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export function MetricCard({ title, value, icon: Icon, trend, className = '' }: MetricCardProps) {
  return (
    <Card className={`admin-card ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="metric-label">{title}</div>
            <div className="metric-value">{value}</div>
            {trend && (
              <div className={`text-xs mt-1 ${trend.isPositive ? 'metric-trend-up' : 'metric-trend-down'}`}>
                {trend.isPositive ? '+' : ''}{trend.value}%
              </div>
            )}
          </div>
          <Icon className="h-8 w-8 text-admin-accent" />
        </div>
      </CardContent>
    </Card>
  );
}

export default MetricCard;
