/**
 * Chart Components for Admin Dashboard
 * ===================================
 * 
 * Lightweight chart components using pure CSS and SVG
 */

import React from 'react';

interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

interface LineChartProps {
  data: ChartDataPoint[];
  width?: number;
  height?: number;
  className?: string;
  showGrid?: boolean;
  animate?: boolean;
}

interface BarChartProps {
  data: ChartDataPoint[];
  width?: number;
  height?: number;
  className?: string;
  horizontal?: boolean;
  animate?: boolean;
}

interface AreaChartProps {
  data: ChartDataPoint[];
  width?: number;
  height?: number;
  className?: string;
  fillColor?: string;
  strokeColor?: string;
  animate?: boolean;
}

// Line Chart Component
export function LineChart({ 
  data, 
  width = 400, 
  height = 200, 
  className = '',
  showGrid = true,
  animate = true
}: LineChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width, height }}>
        <p className="text-gray-400 text-sm">No data available</p>
      </div>
    );
  }

  // Sanitize data to ensure all values are valid numbers
  const sanitizedData = data.map(item => ({
    ...item,
    value: typeof item.value === 'number' && !isNaN(item.value) ? item.value : 0
  }));

  const maxValue = Math.max(...sanitizedData.map(d => d.value), 1);
  const minValue = Math.min(...sanitizedData.map(d => d.value), 0);
  const valueRange = maxValue - minValue || 1;
  
  const padding = 20;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const points = sanitizedData.map((point, index) => {
    const x = padding + (index / (sanitizedData.length - 1)) * chartWidth;
    const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
    return { x, y };
  });
  
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');

  return (
    <div className={className}>
      <svg width={width} height={height} className="overflow-visible">
        {/* Gradient Definitions */}
        <defs>
          <linearGradient id={`lineGradient-${Math.random()}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#1d4ed8" stopOpacity="1" />
          </linearGradient>
          <filter id={`glow-${Math.random()}`}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Grid */}
        {showGrid && (
          <g className="opacity-10">
            {[0.25, 0.5, 0.75].map((ratio, i) => (
              <line
                key={`grid-h-${i}`}
                x1={padding}
                y1={padding + ratio * chartHeight}
                x2={width - padding}
                y2={padding + ratio * chartHeight}
                stroke="#374151"
                strokeWidth="1"
                strokeDasharray="2,2"
              />
            ))}
          </g>
        )}
        
        {/* Line with gradient */}
        <path
          d={pathData}
          fill="none"
          stroke="url(#lineGradient)"
          strokeWidth="3"
          filter="url(#glow)"
          style={{
            strokeDasharray: animate ? `${pathData.length}` : 'none',
            strokeDashoffset: animate ? `${pathData.length}` : '0',
            animation: animate ? 'drawLine 2s ease-out forwards' : 'none'
          }}
        />
        
        {/* Points with glow effect */}
        {points.map((point, index) => (
          <g key={index}>
            <circle
              cx={point.x}
              cy={point.y}
              r="6"
              fill="#3b82f6"
              fillOpacity="0.2"
              style={{
                opacity: animate ? '0' : '1',
                animation: animate ? `fadeIn 0.5s ease-out ${index * 0.1 + 1}s forwards` : 'none'
              }}
            />
            <circle
              cx={point.x}
              cy={point.y}
              r="3"
              fill="#3b82f6"
              style={{
                opacity: animate ? '0' : '1',
                animation: animate ? `fadeIn 0.5s ease-out ${index * 0.1 + 1}s forwards` : 'none'
              }}
            />
          </g>
        ))}
        
        {/* Labels */}
        {data.map((point, index) => (
          <text
            key={index}
            x={points[index].x}
            y={height - 5}
            textAnchor="middle"
            className="text-xs text-gray-500 fill-current font-medium"
          >
            {point.label}
          </text>
        ))}
      </svg>
      
      <style jsx>{`
        @keyframes drawLine {
          to {
            stroke-dashoffset: 0;
          }
        }
        @keyframes fadeIn {
          to {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}

// Bar Chart Component
export function BarChart({ 
  data, 
  width = 400, 
  height = 200, 
  className = '',
  horizontal = false,
  animate = true
}: BarChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width, height }}>
        <p className="text-gray-400 text-sm">No data available</p>
      </div>
    );
  }

  // Sanitize data to ensure all values are valid numbers
  const sanitizedData = data.map(item => ({
    ...item,
    value: typeof item.value === 'number' && !isNaN(item.value) ? item.value : 0
  }));

  const maxValue = Math.max(...sanitizedData.map(d => d.value), 1); // Ensure maxValue is at least 1
  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const barWidth = chartWidth / data.length * 0.8;
  const barSpacing = chartWidth / data.length * 0.2;

  return (
    <div className={className}>
      <svg width={width} height={height}>
        {/* Gradient Definitions */}
        <defs>
          <linearGradient id={`barGradient-${Math.random()}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="1" />
            <stop offset="100%" stopColor="#1e40af" stopOpacity="0.8" />
          </linearGradient>
          <linearGradient id={`barGradientHover-${Math.random()}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#60a5fa" stopOpacity="1" />
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.9" />
          </linearGradient>
        </defs>

        {/* Background grid */}
        <g className="opacity-5">
          {[0.25, 0.5, 0.75].map((ratio, i) => (
            <line
              key={`grid-h-${i}`}
              x1={padding}
              y1={padding + ratio * chartHeight}
              x2={width - padding}
              y2={padding + ratio * chartHeight}
              stroke="#374151"
              strokeWidth="1"
              strokeDasharray="2,2"
            />
          ))}
        </g>

        {sanitizedData.map((item, index) => {
          const barHeight = Math.max(0, (item.value / maxValue) * chartHeight);
          const x = padding + index * (chartWidth / sanitizedData.length) + barSpacing / 2;
          const y = height - padding - barHeight;
          
          return (
            <g key={index}>
              {/* Bar shadow */}
              <rect
                x={x + 2}
                y={y + 2}
                width={barWidth}
                height={barHeight}
                fill="black"
                fillOpacity="0.1"
                rx="2"
                style={{
                  transform: animate ? 'scaleY(0)' : 'scaleY(1)',
                  transformOrigin: 'bottom',
                  animation: animate ? `scaleUp 0.8s ease-out ${index * 0.1}s forwards` : 'none'
                }}
              />
              
              {/* Main Bar */}
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill="url(#barGradient)"
                rx="2"
                className="transition-all duration-200 hover:fill-url(#barGradientHover)"
                style={{
                  transform: animate ? 'scaleY(0)' : 'scaleY(1)',
                  transformOrigin: 'bottom',
                  animation: animate ? `scaleUp 0.8s ease-out ${index * 0.1}s forwards` : 'none'
                }}
              />
              
              {/* Highlight bar */}
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={Math.max(4, barHeight * 0.3)}
                fill="url(#barGradientHover)"
                fillOpacity="0.6"
                rx="2"
                style={{
                  transform: animate ? 'scaleY(0)' : 'scaleY(1)',
                  transformOrigin: 'bottom',
                  animation: animate ? `scaleUp 1s ease-out ${index * 0.1 + 0.2}s forwards` : 'none'
                }}
              />
              
              {/* Label */}
              <text
                x={x + barWidth / 2}
                y={height - 10}
                textAnchor="middle"
                className="text-xs text-gray-500 fill-current font-medium"
              >
                {item.label}
              </text>
              
              {/* Value */}
              <text
                x={x + barWidth / 2}
                y={y - 8}
                textAnchor="middle"
                className="text-xs text-white fill-current font-semibold"
                style={{
                  opacity: animate ? '0' : '1',
                  animation: animate ? `fadeIn 0.5s ease-out ${index * 0.1 + 1}s forwards` : 'none'
                }}
              >
                {item.value}
              </text>
            </g>
          );
        })}
      </svg>
      
      <style jsx>{`
        @keyframes scaleUp {
          to {
            transform: scaleY(1);
          }
        }
      `}</style>
    </div>
  );
}

// Area Chart Component
export function AreaChart({ 
  data, 
  width = 400, 
  height = 200, 
  className = '',
  fillColor = 'rgba(59, 130, 246, 0.1)',
  strokeColor = '#3b82f6',
  animate = true
}: AreaChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width, height }}>
        <p className="text-gray-400 text-sm">No data available</p>
      </div>
    );
  }

  // Sanitize data to ensure all values are valid numbers
  const sanitizedData = data.map(item => ({
    ...item,
    value: typeof item.value === 'number' && !isNaN(item.value) ? item.value : 0
  }));

  const maxValue = Math.max(...sanitizedData.map(d => d.value), 1);
  const minValue = Math.min(...sanitizedData.map(d => d.value), 0);
  const valueRange = maxValue - minValue || 1;
  
  const padding = 20;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const points = sanitizedData.map((point, index) => {
    const x = padding + (index / (sanitizedData.length - 1)) * chartWidth;
    const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
    return { x, y };
  });
  
  const lineData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');
  
  const areaData = `${lineData} L ${width - padding} ${height - padding} L ${padding} ${height - padding} Z`;

  return (
    <div className={className}>
      <svg width={width} height={height}>
        {/* Area */}
        <path
          d={areaData}
          fill={fillColor}
          className="text-admin-accent"
          style={{
            opacity: animate ? '0' : '0.3',
            animation: animate ? 'fadeIn 1.5s ease-out 0.5s forwards' : 'none'
          }}
        />
        
        {/* Line */}
        <path
          d={lineData}
          fill="none"
          stroke={strokeColor}
          strokeWidth="2"
          className="text-admin-accent"
          style={{
            strokeDasharray: animate ? `${lineData.length}` : 'none',
            strokeDashoffset: animate ? `${lineData.length}` : '0',
            animation: animate ? 'drawLine 2s ease-out forwards' : 'none'
          }}
        />
        
        {/* Points */}
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="3"
            fill={strokeColor}
            className="text-admin-accent"
            style={{
              opacity: animate ? '0' : '1',
              animation: animate ? `fadeIn 0.5s ease-out ${index * 0.1 + 1}s forwards` : 'none'
            }}
          />
        ))}
      </svg>
      
      <style jsx>{`
        @keyframes drawLine {
          to {
            stroke-dashoffset: 0;
          }
        }
        @keyframes fadeIn {
          to {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}

// Donut Chart Component
export function DonutChart({ 
  data, 
  width = 200, 
  height = 200, 
  className = '',
  animate = true
}: { data: ChartDataPoint[]; width?: number; height?: number; className?: string; animate?: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width, height }}>
        <p className="text-gray-400 text-sm">No data available</p>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = Math.min(width, height) / 2 - 20;
  const innerRadius = radius * 0.6;
  
  let cumulativeAngle = 0;
  
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
  ];

  return (
    <div className={className}>
      <svg width={width} height={height}>
        {data.map((item, index) => {
          const angle = (item.value / total) * 2 * Math.PI;
          const startAngle = cumulativeAngle;
          const endAngle = cumulativeAngle + angle;
          
          const x1 = centerX + Math.cos(startAngle) * radius;
          const y1 = centerY + Math.sin(startAngle) * radius;
          const x2 = centerX + Math.cos(endAngle) * radius;
          const y2 = centerY + Math.sin(endAngle) * radius;
          
          const x3 = centerX + Math.cos(endAngle) * innerRadius;
          const y3 = centerY + Math.sin(endAngle) * innerRadius;
          const x4 = centerX + Math.cos(startAngle) * innerRadius;
          const y4 = centerY + Math.sin(startAngle) * innerRadius;
          
          const largeArcFlag = angle > Math.PI ? 1 : 0;
          
          const pathData = [
            `M ${x1} ${y1}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            `L ${x3} ${y3}`,
            `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
            'Z'
          ].join(' ');
          
          cumulativeAngle += angle;
          
          return (
            <path
              key={index}
              d={pathData}
              fill={item.color || colors[index % colors.length]}
              style={{
                opacity: animate ? '0' : '1',
                animation: animate ? `fadeIn 0.8s ease-out ${index * 0.2}s forwards` : 'none'
              }}
            />
          );
        })}
        
        {/* Center text */}
        <text
          x={centerX}
          y={centerY}
          textAnchor="middle"
          dominantBaseline="middle"
          className="text-white text-lg font-bold fill-current"
        >
          {total}
        </text>
      </svg>
      
      <style jsx>{`
        @keyframes fadeIn {
          to {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}