/**
 * Service Management Component
 * ===========================
 * 
 * Provides service restart controls, log monitoring, and operational management.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Server,
  RefreshCw,
  Play,
  Square,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Download,
  Terminal,
  Clock,
  Activity,
  Zap
} from 'lucide-react';

interface ServiceStatus {
  name: string;
  container: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  uptime: string;
  responseTime: number;
  lastRestart?: string;
}

interface LogEntry {
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG' | 'UNKNOWN';
  message: string;
  raw: string;
}

export { ServiceManagement };

export default function ServiceManagement() {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [selectedService, setSelectedService] = useState<string>('gaming-engine');
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRestarting, setIsRestarting] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string>('');

  const serviceList = [
    { id: 'gaming-engine', name: 'Gaming Engine', container: 'betbet-gaming' },
    { id: 'custom-betting', name: 'Custom Betting', container: 'betbet-custom-betting' },
    { id: 'trading-engine', name: 'Trading Engine', container: 'betbet-trading' },
    { id: 'expert-analysis', name: 'Expert Analysis', container: 'betbet-experts' },
    { id: 'sports-analysis', name: 'Sports Analysis', container: 'betbet-sports' },
    { id: 'leaderboards', name: 'Leaderboards', container: 'betbet-leaderboards' },
    { id: 'wallet-service', name: 'Wallet Service', container: 'betbet-wallet' },
    { id: 'websocket-manager', name: 'WebSocket Manager', container: 'betbet-websocket' },
    { id: 'api-gateway', name: 'API Gateway', container: 'betbet-api-gateway' },
  ];

  useEffect(() => {
    loadServiceStatus();
    loadServiceLogs(selectedService);
  }, [selectedService]);

  const loadServiceStatus = async () => {
    try {
      setIsLoading(true);
      
      // Get service health from our analytics API
      const response = await fetch('/api/analytics/system-metrics');
      const data = await response.json();
      
      if (data.system?.services) {
        const serviceStatuses = serviceList.map(service => {
          const healthData = data.system.services.find((s: any) => 
            s.name.toLowerCase().replace(/\s+/g, '-') === service.id
          );
          
          return {
            name: service.name,
            container: service.container,
            status: healthData?.status || 'unknown',
            uptime: healthData ? `${Math.floor(healthData.uptime)}%` : 'Unknown',
            responseTime: healthData?.responseTime || 0,
            lastRestart: undefined, // Would come from restart logs in production
          };
        });
        
        setServices(serviceStatuses);
        setLastUpdated(new Date().toLocaleTimeString());
      }
    } catch (error) {
      console.error('Failed to load service status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceLogs = async (serviceId: string, lines: number = 100) => {
    try {
      const response = await fetch(`/api/services/${serviceId}/logs?lines=${lines}&since=1h`);
      const data = await response.json();
      
      if (data.logs) {
        setLogs(data.logs);
      }
    } catch (error) {
      console.error(`Failed to load logs for ${serviceId}:`, error);
      setLogs([{
        timestamp: new Date().toISOString(),
        level: 'ERROR',
        message: `Failed to load logs: ${error}`,
        raw: `ERROR: Failed to load logs for ${serviceId}`,
      }]);
    }
  };

  const restartService = async (serviceId: string) => {
    try {
      setIsRestarting(serviceId);
      
      const response = await fetch(`/api/services/${serviceId}/restart`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const result = await response.json();
      
      if (result.result?.success) {
        // Refresh service status after restart
        setTimeout(() => {
          loadServiceStatus();
          if (selectedService === serviceId) {
            loadServiceLogs(serviceId);
          }
        }, 3000);
        
        alert(`Service ${serviceId} restarted successfully`);
      } else {
        alert(`Failed to restart ${serviceId}: ${result.result?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Failed to restart ${serviceId}:`, error);
      alert(`Failed to restart ${serviceId}: ${error}`);
    } finally {
      setIsRestarting(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-admin-success" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-admin-warning" />;
      case 'critical':
        return <XCircle className="h-4 w-4 text-admin-danger" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-admin-success/20 text-admin-success';
      case 'warning':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'critical':
        return 'bg-admin-danger/20 text-admin-danger';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'ERROR':
        return 'text-admin-danger';
      case 'WARN':
        return 'text-admin-warning';
      case 'INFO':
        return 'text-admin-info';
      case 'DEBUG':
        return 'text-gray-400';
      default:
        return 'text-white';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Service Management</h2>
          <p className="text-gray-400 mt-1">
            Monitor, restart, and manage platform services
            {lastUpdated && (
              <span className="text-xs ml-2">
                • Last updated: {lastUpdated}
              </span>
            )}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={loadServiceStatus}
            disabled={isLoading}
            className="admin-button-secondary"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="services" className="space-y-6">
        <TabsList className="bg-admin-secondary border border-admin-secondary">
          <TabsTrigger value="services" className="data-[state=active]:bg-admin-accent">
            <Server className="h-4 w-4 mr-2" />
            Services
          </TabsTrigger>
          <TabsTrigger value="logs" className="data-[state=active]:bg-admin-accent">
            <Terminal className="h-4 w-4 mr-2" />
            Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="services">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {services.map((service) => (
              <Card key={service.name} className="admin-card">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{service.name}</CardTitle>
                    {getStatusIcon(service.status)}
                  </div>
                  <Badge className={getStatusColor(service.status)}>
                    {service.status}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Uptime:</span>
                      <span className="text-white">{service.uptime}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Response:</span>
                      <span className="text-white">{service.responseTime}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Container:</span>
                      <span className="text-white text-xs">{service.container}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedService(serviceList.find(s => s.name === service.name)?.id || '');
                      }}
                      className="admin-button-secondary flex-1"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Logs
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => restartService(serviceList.find(s => s.name === service.name)?.id || '')}
                      disabled={isRestarting === serviceList.find(s => s.name === service.name)?.id}
                      className="admin-button-primary flex-1"
                    >
                      {isRestarting === serviceList.find(s => s.name === service.name)?.id ? (
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                      ) : (
                        <Zap className="h-3 w-3 mr-1" />
                      )}
                      Restart
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="logs">
          <Card className="admin-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Service Logs</CardTitle>
                <div className="flex items-center gap-2">
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="px-3 py-1 bg-admin-secondary border border-admin-secondary rounded text-white text-sm"
                  >
                    {serviceList.map(service => (
                      <option key={service.id} value={service.id}>
                        {service.name}
                      </option>
                    ))}
                  </select>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => loadServiceLogs(selectedService)}
                    className="admin-button-secondary"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {logs.length > 0 ? (
                  logs.map((log, index) => (
                    <div key={index} className="mb-1 flex gap-2">
                      <span className="text-gray-500 text-xs">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <span className={`text-xs font-bold ${getLogLevelColor(log.level)}`}>
                        [{log.level}]
                      </span>
                      <span className="text-gray-300 flex-1">{log.message}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    No logs available for {selectedService}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
