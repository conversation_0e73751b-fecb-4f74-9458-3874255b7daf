/**
 * User Error Monitoring Component
 * ==============================
 * 
 * Monitors and displays user-facing errors for customer support.
 * Provides filtering, search, and error resolution tracking.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertTriangle,
  XCircle,
  Clock,
  User,
  Mail,
  Search,
  Filter,
  RefreshCw,
  Eye,
  CheckCircle,
  AlertCircle,
  Info,
  Download
} from 'lucide-react';

interface UserError {
  id: string;
  userId?: string;
  userEmail?: string;
  service: string;
  errorType: string;
  errorMessage: string;
  stackTrace?: string;
  userAgent?: string;
  url?: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'new' | 'investigating' | 'resolved' | 'ignored';
  tags: string[];
}

interface ErrorSummary {
  newErrors: number;
  criticalErrors: number;
  errorsByService: Record<string, number>;
  errorsByType: Record<string, number>;
}

export default function UserErrorMonitoring() {
  const [errors, setErrors] = useState<UserError[]>([]);
  const [summary, setSummary] = useState<ErrorSummary>({
    newErrors: 0,
    criticalErrors: 0,
    errorsByService: {},
    errorsByType: {},
  });
  const [selectedError, setSelectedError] = useState<UserError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState({
    service: '',
    severity: '',
    status: '',
    search: '',
  });

  useEffect(() => {
    loadUserErrors();
  }, [filters]);

  const loadUserErrors = async () => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams();
      if (filters.service) params.append('service', filters.service);
      if (filters.severity) params.append('severity', filters.severity);
      if (filters.status) params.append('status', filters.status);
      params.append('limit', '50');
      
      const response = await fetch(`/api/user-errors?${params}`);
      const data = await response.json();
      
      if (data.errors) {
        let filteredErrors = data.errors;
        
        // Apply search filter
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredErrors = filteredErrors.filter((error: UserError) =>
            error.errorMessage.toLowerCase().includes(searchLower) ||
            error.userEmail?.toLowerCase().includes(searchLower) ||
            error.errorType.toLowerCase().includes(searchLower)
          );
        }
        
        setErrors(filteredErrors);
        setSummary(data.summary);
      }
    } catch (error) {
      console.error('Failed to load user errors:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-admin-danger" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-admin-warning" />;
      case 'medium':
        return <AlertCircle className="h-4 w-4 text-admin-info" />;
      case 'low':
        return <Info className="h-4 w-4 text-gray-400" />;
      default:
        return <Info className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-admin-danger/20 text-admin-danger';
      case 'high':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'medium':
        return 'bg-admin-info/20 text-admin-info';
      case 'low':
        return 'bg-gray-500/20 text-gray-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'investigating':
        return 'bg-admin-info/20 text-admin-info';
      case 'resolved':
        return 'bg-admin-success/20 text-admin-success';
      case 'ignored':
        return 'bg-gray-500/20 text-gray-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">User Error Monitoring</h2>
          <p className="text-gray-400 mt-1">
            Track and resolve user-facing errors for customer support
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={loadUserErrors}
            disabled={isLoading}
            className="admin-button-secondary"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" className="admin-button-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">New Errors</p>
                <p className="text-2xl font-bold text-admin-warning">{summary.newErrors}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-admin-warning" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Critical Errors</p>
                <p className="text-2xl font-bold text-admin-danger">{summary.criticalErrors}</p>
              </div>
              <XCircle className="h-8 w-8 text-admin-danger" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Errors</p>
                <p className="text-2xl font-bold text-white">{errors.length}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-admin-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Services Affected</p>
                <p className="text-2xl font-bold text-white">
                  {Object.keys(summary.errorsByService).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-admin-success" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="admin-card">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <input
                type="text"
                placeholder="Search errors..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded text-white text-sm"
              />
            </div>
            <div>
              <select
                value={filters.service}
                onChange={(e) => setFilters({ ...filters, service: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded text-white text-sm"
              >
                <option value="">All Services</option>
                <option value="gaming-engine">Gaming Engine</option>
                <option value="custom-betting">Custom Betting</option>
                <option value="trading-engine">Trading Engine</option>
                <option value="expert-analysis">Expert Analysis</option>
                <option value="wallet-service">Wallet Service</option>
              </select>
            </div>
            <div>
              <select
                value={filters.severity}
                onChange={(e) => setFilters({ ...filters, severity: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded text-white text-sm"
              >
                <option value="">All Severities</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <div>
              <select
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded text-white text-sm"
              >
                <option value="">All Statuses</option>
                <option value="new">New</option>
                <option value="investigating">Investigating</option>
                <option value="resolved">Resolved</option>
                <option value="ignored">Ignored</option>
              </select>
            </div>
            <div>
              <Button
                onClick={() => setFilters({ service: '', severity: '', status: '', search: '' })}
                variant="outline"
                className="admin-button-secondary w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error List */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle>Recent User Errors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {errors.length > 0 ? (
              errors.map((error) => (
                <div
                  key={error.id}
                  className="p-4 bg-admin-secondary/50 rounded-lg border border-admin-secondary hover:bg-admin-secondary/70 transition-colors cursor-pointer"
                  onClick={() => setSelectedError(error)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getSeverityIcon(error.severity)}
                        <Badge className={getSeverityColor(error.severity)}>
                          {error.severity}
                        </Badge>
                        <Badge className={getStatusColor(error.status)}>
                          {error.status}
                        </Badge>
                        <span className="text-sm text-gray-400">{error.service}</span>
                      </div>
                      <h4 className="text-white font-medium mb-1">{error.errorType}</h4>
                      <p className="text-gray-300 text-sm mb-2">{error.errorMessage}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-400">
                        {error.userEmail && (
                          <span className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {error.userEmail}
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(error.timestamp).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" className="admin-button-secondary">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-400">
                {isLoading ? 'Loading errors...' : 'No errors found'}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
