/**
 * BetBet Admin Module Overview Component
 * =====================================
 * 
 * Displays overview and management interface for all platform modules:
 * Gaming, Betting, Trading, Experts, Sports Analysis, Leaderboards
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Gamepad2,
  Target,
  TrendingUp,
  Crown,
  BarChart3,
  Trophy,
  Users,
  DollarSign,
  Activity,
  Settings,
  PlayCircle,
  PauseCircle,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Info
} from 'lucide-react';

interface ModuleStats {
  users: number;
  revenue: number;
  growth: number;
}

interface ModuleOverviewProps {
  moduleStats: {
    gaming: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    betting: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    trading: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    experts: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    sports: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    leaderboards: ModuleStats & { status?: 'active' | 'error' | 'offline' };
    serviceStatus?: Record<string, 'active' | 'error' | 'offline'>;
  };
}

const moduleConfig = {
  gaming: {
    name: 'Gaming Engine',
    icon: Gamepad2,
    description: 'Core gaming mechanics and tournaments',
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20',
    borderColor: 'border-purple-500/30',
  },
  betting: {
    name: 'Betting Platform',
    icon: Target,
    description: 'Sports betting and odds management',
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    borderColor: 'border-green-500/30',
  },
  trading: {
    name: 'Odds Exchange',
    icon: TrendingUp,
    description: 'P2P trading and market making',
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    borderColor: 'border-blue-500/30',
  },
  experts: {
    name: 'Expert Analysis',
    icon: Crown,
    description: 'Expert predictions and insights',
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    borderColor: 'border-yellow-500/30',
  },
  sports: {
    name: 'Sports Analysis',
    icon: BarChart3,
    description: 'AI-powered sports data analysis',
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/20',
    borderColor: 'border-orange-500/30',
  },
  leaderboards: {
    name: 'Leaderboards',
    icon: Trophy,
    description: 'Rankings and achievement system',
    color: 'text-red-400',
    bgColor: 'bg-red-500/20',
    borderColor: 'border-red-500/30',
  },
};

export default function ModuleOverview({ moduleStats }: ModuleOverviewProps) {
  const [selectedModule, setSelectedModule] = useState<string | null>(null);
  const [serviceStatuses, setServiceStatuses] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<Record<string, any>>({});

  // Map module keys to service keys for API calls
  const moduleToServiceMap: Record<string, string> = {
    gaming: 'gaming',
    betting: 'custom-betting',
    trading: 'odds-exchange',
    experts: 'expert-analysts',
    sports: 'sports-analysis',
    leaderboards: 'leaderboards',
    wallet: 'wallet'
  };

  const fetchServiceStatuses = async () => {
    try {
      const response = await fetch('/api/services');
      const data = await response.json();
      
      if (data.services) {
        const statusMap: Record<string, any> = {};
        data.services.forEach((service: any) => {
          statusMap[service.service] = service;
        });
        setServiceStatuses(statusMap);
      }
    } catch (error) {
      console.error('Failed to fetch service statuses:', error);
    }
  };

  const fetchServiceLogs = async (serviceKey?: string) => {
    try {
      const url = serviceKey ? `/api/services/logs?service=${serviceKey}&lines=20` : '/api/services/logs';
      const response = await fetch(url);
      const data = await response.json();
      
      if (serviceKey) {
        setLogs(prev => ({ ...prev, [serviceKey]: data }));
      } else {
        setLogs(data.services || {});
      }
    } catch (error) {
      console.error('Failed to fetch service logs:', error);
    }
  };

  const handleServiceAction = async (action: string, moduleKey: string) => {
    const serviceKey = moduleToServiceMap[moduleKey];
    if (!serviceKey) return;

    setLoading(true);
    try {
      const response = await fetch('/api/services', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, service: serviceKey })
      });
      
      const result = await response.json();
      console.log(`${action} result:`, result);
      
      // Refresh service status after action
      setTimeout(() => {
        fetchServiceStatuses();
      }, 2000);
      
    } catch (error) {
      console.error(`Failed to ${action} service:`, error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServiceStatuses();
    fetchServiceLogs();
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchServiceStatuses();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-admin-success" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4 text-admin-warning" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-admin-danger" />;
      default:
        return <RefreshCw className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'maintenance':
        return 'Maintenance';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  const ModuleCard = ({ moduleKey, config, stats }: {
    moduleKey: string;
    config: typeof moduleConfig.gaming;
    stats: ModuleStats & { status?: 'active' | 'error' | 'offline' };
  }) => {
    const Icon = config.icon;
    const serviceKey = moduleToServiceMap[moduleKey];
    const realServiceStatus = serviceStatuses[serviceKey];
    const serviceStatus = realServiceStatus?.status || stats.status || 'offline';
    const responseTime = realServiceStatus?.responseTime;
    
    return (
      <Card className={`admin-card border-l-4 ${config.borderColor} hover:shadow-lg transition-all duration-200 cursor-pointer`}
            onClick={() => setSelectedModule(selectedModule === moduleKey ? null : moduleKey)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${config.bgColor}`}>
                <Icon className={`h-6 w-6 ${config.color}`} />
              </div>
              <div>
                <CardTitle className="text-lg text-white">{config.name}</CardTitle>
                <p className="text-sm text-gray-400 mt-1">{config.description}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(serviceStatus)}
              <span className={`text-xs px-2 py-1 rounded-full ${
                serviceStatus === 'active' ? 'bg-admin-success/20 text-admin-success' :
                serviceStatus === 'error' ? 'bg-admin-warning/20 text-admin-warning' :
                'bg-admin-danger/20 text-admin-danger'
              }`}>
                {getStatusText(serviceStatus)}
              </span>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-gray-400 text-xs mb-1">
                <Users className="h-3 w-3" />
                USERS
              </div>
              <div className="text-lg font-bold text-white">{stats.users.toLocaleString()}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-gray-400 text-xs mb-1">
                <DollarSign className="h-3 w-3" />
                REVENUE
              </div>
              <div className="text-lg font-bold text-white">${stats.revenue.toLocaleString()}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-gray-400 text-xs mb-1">
                <Activity className="h-3 w-3" />
                GROWTH
              </div>
              <div className={`text-lg font-bold ${stats.growth > 0 ? 'text-admin-success' : 'text-admin-danger'}`}>
                +{stats.growth}%
              </div>
            </div>
          </div>

          {/* Module Info */}
          <div className="grid grid-cols-2 gap-4 pt-3 border-t border-admin-secondary/50">
            <div>
              <div className="text-xs text-gray-400 uppercase tracking-wide">Status</div>
              <div className={`text-sm font-medium ${
                serviceStatus === 'active' ? 'text-admin-success' :
                serviceStatus === 'error' ? 'text-admin-warning' :
                'text-admin-danger'
              }`}>
                {serviceStatus === 'active' ? 'Online' : 
                 serviceStatus === 'error' ? 'Service Error' : 
                 'Offline'}
              </div>
              {responseTime && (
                <div className="text-xs text-gray-500">{responseTime}ms</div>
              )}
            </div>
            <div>
              <div className="text-xs text-gray-400 uppercase tracking-wide">Data Source</div>
              <div className="text-sm text-white font-medium">
                {serviceStatus === 'active' ? 'Live Backend' : 'No Connection'}
              </div>
              {realServiceStatus?.version && (
                <div className="text-xs text-gray-500">v{realServiceStatus.version}</div>
              )}
            </div>
          </div>

          {/* Expanded Details */}
          {selectedModule === moduleKey && (
            <div className="mt-4 pt-4 border-t border-admin-secondary/50 space-y-3">
              {serviceStatus !== 'active' && (
                <div className="flex items-center gap-2 p-2 rounded bg-admin-warning/10 border border-admin-warning/20">
                  <AlertTriangle className="h-4 w-4 text-admin-warning" />
                  <span className="text-sm text-admin-warning">
                    {serviceStatus === 'error' ? 'Service experiencing errors' : 'Service is offline - monitoring data unavailable'}
                  </span>
                </div>
              )}

              <div className="flex items-center gap-2 p-2 rounded bg-blue-500/10 border border-blue-500/20">
                <Info className="h-4 w-4 text-blue-400" />
                <div className="text-sm text-blue-400">
                  <div className="font-medium">Docker Management</div>
                  <div className="text-xs text-blue-300 mt-1">
                    For actual service restarts, use: <code className="bg-gray-800 px-1 rounded">./scripts/manage-services.sh restart {moduleKey}</code>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="admin-button-secondary"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Configuration could open a modal or navigate to settings
                    console.log(`Configure ${moduleKey}`);
                  }}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                {serviceStatus === 'active' ? (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="admin-button-secondary"
                    disabled={loading}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleServiceAction('stop', moduleKey);
                    }}
                  >
                    <PauseCircle className="h-4 w-4 mr-2" />
                    {loading ? 'Stopping...' : 'Stop'}
                  </Button>
                ) : (
                  <Button 
                    size="sm" 
                    className="admin-button-primary"
                    disabled={loading}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleServiceAction('start', moduleKey);
                    }}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" />
                    {loading ? 'Starting...' : 'Start'}
                  </Button>
                )}
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="admin-button-secondary"
                  disabled={loading}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleServiceAction('restart', moduleKey);
                  }}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  {loading ? 'Restarting...' : 'Restart'}
                </Button>
              </div>

              {/* Service Logs */}
              {logs[serviceKey] && (
                <div className="mt-4 p-3 bg-gray-900 rounded border">
                  <div className="text-xs text-gray-400 uppercase tracking-wide mb-2">Recent Logs</div>
                  <div className="text-xs font-mono text-gray-300 space-y-1 max-h-32 overflow-y-auto">
                    {logs[serviceKey].recent?.map((log: any, idx: number) => (
                      <div key={idx} className={`${
                        log.level === 'error' ? 'text-red-400' :
                        log.level === 'warn' ? 'text-yellow-400' :
                        'text-gray-300'
                      }`}>
                        {log.message.length > 80 ? log.message.substring(0, 80) + '...' : log.message}
                      </div>
                    ))}
                  </div>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2 text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      fetchServiceLogs(serviceKey);
                    }}
                  >
                    Refresh Logs
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Module Overview</h2>
          <p className="text-gray-400 mt-1">
            Monitor and manage all platform modules and services
          </p>
        </div>
        <Button 
          className="admin-button-primary"
          disabled={loading}
          onClick={() => {
            fetchServiceStatuses();
            fetchServiceLogs();
          }}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh All'}
        </Button>
      </div>

      {/* Module Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(moduleConfig).map(([key, config]) => (
          <ModuleCard
            key={key}
            moduleKey={key}
            config={config}
            stats={moduleStats[key as keyof typeof moduleStats]}
          />
        ))}
      </div>

      {/* Summary Stats */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-admin-accent" />
            Module Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Total Users</div>
              <div className="text-2xl font-bold text-white">
                {(() => {
                  const modules = Object.entries(moduleConfig);
                  const totalUsers = modules.reduce((sum, [key]) => {
                    const moduleKey = key as keyof typeof moduleStats;
                    const stat = moduleStats[moduleKey];
                    return sum + (stat?.users || 0);
                  }, 0);
                  return totalUsers.toLocaleString();
                })()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Total Revenue</div>
              <div className="text-2xl font-bold text-white">
                {(() => {
                  const modules = Object.entries(moduleConfig);
                  const totalRevenue = modules.reduce((sum, [key]) => {
                    const moduleKey = key as keyof typeof moduleStats;
                    const stat = moduleStats[moduleKey];
                    return sum + (stat?.revenue || 0);
                  }, 0);
                  return `$${totalRevenue.toLocaleString()}`;
                })()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Avg Growth</div>
              <div className="text-2xl font-bold text-admin-success">
                {(() => {
                  const modules = Object.entries(moduleConfig);
                  const growthValues = modules.map(([key]) => {
                    const moduleKey = key as keyof typeof moduleStats;
                    const stat = moduleStats[moduleKey];
                    return stat?.growth || 0;
                  }).filter(growth => growth > 0);
                  const avgGrowth = growthValues.length > 0 ? 
                    growthValues.reduce((sum, growth) => sum + growth, 0) / growthValues.length : 0;
                  return `+${avgGrowth.toFixed(1)}%`;
                })()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Active Modules</div>
              <div className="text-2xl font-bold text-white">
                {(() => {
                  const modules = Object.entries(moduleConfig);
                  const activeCount = modules.filter(([key]) => {
                    const serviceKey = moduleToServiceMap[key];
                    const realServiceStatus = serviceStatuses[serviceKey];
                    const status = realServiceStatus?.status || 'offline';
                    return status === 'active';
                  }).length;
                  return `${activeCount}/${modules.length}`;
                })()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}