'use client';

import React, { useState } from 'react';

interface CreateMarketModalProps {
  isOpen: boolean;
  onClose: () => void;
  onMarketCreated: () => void;
}

interface MarketFormData {
  title: string;
  description: string;
  category_id?: string;
  market_type: string;
  minimum_stake: number;
  maximum_stake?: number;
  closes_at?: string;
  resolves_at?: string;
  resolution_criteria: string;
  outcomes: Array<{
    title: string;
    description?: string;
  }>;
  tags: string[];
}

export default function CreateMarketModal({ isOpen, onClose, onMarketCreated }: CreateMarketModalProps) {
  const [formData, setFormData] = useState<MarketFormData>({
    title: '',
    description: '',
    market_type: 'binary',
    minimum_stake: 1,
    resolution_criteria: '',
    outcomes: [
      { title: 'Yes', description: '' },
      { title: 'No', description: '' }
    ],
    tags: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/betting/markets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const newMarket = await response.json();
      console.log('Market created successfully:', newMarket);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        market_type: 'binary',
        minimum_stake: 1,
        resolution_criteria: '',
        outcomes: [
          { title: 'Yes', description: '' },
          { title: 'No', description: '' }
        ],
        tags: []
      });

      onMarketCreated();
      onClose();
    } catch (err: any) {
      console.error('Failed to create market:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOutcomeChange = (index: number, field: 'title' | 'description', value: string) => {
    const newOutcomes = [...formData.outcomes];
    newOutcomes[index] = { ...newOutcomes[index], [field]: value };
    setFormData({ ...formData, outcomes: newOutcomes });
  };

  const addOutcome = () => {
    setFormData({
      ...formData,
      outcomes: [...formData.outcomes, { title: '', description: '' }]
    });
  };

  const removeOutcome = (index: number) => {
    if (formData.outcomes.length > 2) {
      const newOutcomes = formData.outcomes.filter((_, i) => i !== index);
      setFormData({ ...formData, outcomes: newOutcomes });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-admin-primary border border-admin-secondary rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Create New Market</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Market Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
              placeholder="e.g., Will Bitcoin reach $100k by end of 2024?"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Description *
            </label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
              placeholder="Detailed description of the market and what it's about..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Market Type
              </label>
              <select
                value={formData.market_type}
                onChange={(e) => setFormData({ ...formData, market_type: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
              >
                <option value="binary">Binary (Yes/No)</option>
                <option value="multiple_choice">Multiple Choice</option>
                <option value="scalar">Scalar</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Minimum Stake ($)
              </label>
              <input
                type="number"
                min="0.01"
                step="0.01"
                value={formData.minimum_stake}
                onChange={(e) => setFormData({ ...formData, minimum_stake: parseFloat(e.target.value) })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Resolution Criteria *
            </label>
            <textarea
              required
              value={formData.resolution_criteria}
              onChange={(e) => setFormData({ ...formData, resolution_criteria: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
              placeholder="Clear criteria for how this market will be resolved..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Outcomes
            </label>
            <div className="space-y-2">
              {formData.outcomes.map((outcome, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    required
                    value={outcome.title}
                    onChange={(e) => handleOutcomeChange(index, 'title', e.target.value)}
                    className="flex-1 px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
                    placeholder={`Outcome ${index + 1}`}
                  />
                  {formData.outcomes.length > 2 && (
                    <button
                      type="button"
                      onClick={() => removeOutcome(index)}
                      className="px-3 py-2 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 hover:bg-red-500/30"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
              {formData.market_type === 'multiple_choice' && (
                <button
                  type="button"
                  onClick={addOutcome}
                  className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-gray-400 hover:text-white hover:bg-admin-secondary/80"
                >
                  + Add Outcome
                </button>
              )}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white hover:bg-admin-secondary/80"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-admin-accent text-white rounded-lg hover:bg-admin-accent/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create Market'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
