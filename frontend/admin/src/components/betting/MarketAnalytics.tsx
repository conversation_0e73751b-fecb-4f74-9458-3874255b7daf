/**
 * BetBet Admin - Market Analytics Component
 * ========================================
 * 
 * Advanced analytics dashboard for market performance and user behavior insights.
 * Migrated from main web app for proper admin separation.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Activity,
  Calendar,
  Target,
  Zap,
  PieChart,
  LineChart,
  Clock,
  RefreshCw,
  Download,
  Filter,
  Settings
} from 'lucide-react';

interface AnalyticsData {
  marketPerformance: {
    totalVolume: number;
    averageVolume: number;
    totalMarkets: number;
    activeMarkets: number;
    settledMarkets: number;
    averageParticipants: number;
    volumeGrowth: number;
    participantGrowth: number;
  };
  topCategories: Array<{
    category: string;
    marketCount: number;
    totalVolume: number;
    averageVolume: number;
    percentage: number;
  }>;
  recentTrends: Array<{
    date: string;
    newMarkets: number;
    totalVolume: number;
    activeUsers: number;
  }>;
  userEngagement: {
    totalUsers: number;
    activeUsers: number;
    averageBetsPerUser: number;
    averageStakeSize: number;
    retentionRate: number;
  };
  settlementStats: {
    averageSettlementTime: number;
    disputeRate: number;
    accurateSettlements: number;
    totalDisputes: number;
  };
}

interface MarketAnalyticsProps {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  className?: string;
}

export default function MarketAnalytics({ timeRange = '30d', className }: MarketAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, [selectedTimeRange]);

  const loadAnalytics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      else setLoading(true);
      
      setError(null);

      // Mock analytics data - in real implementation, this would fetch from analytics API
      const mockAnalytics: AnalyticsData = {
        marketPerformance: {
          totalVolume: 125750.50,
          averageVolume: 2515.01,
          totalMarkets: 50,
          activeMarkets: 12,
          settledMarkets: 35,
          averageParticipants: 8.5,
          volumeGrowth: 15.2,
          participantGrowth: 23.1
        },
        topCategories: [
          { category: 'Sports', marketCount: 18, totalVolume: 45200.00, averageVolume: 2511.11, percentage: 35.9 },
          { category: 'Politics', marketCount: 12, totalVolume: 32500.00, averageVolume: 2708.33, percentage: 25.9 },
          { category: 'Crypto', marketCount: 8, totalVolume: 28100.00, averageVolume: 3512.50, percentage: 22.4 },
          { category: 'Entertainment', marketCount: 7, totalVolume: 12200.00, averageVolume: 1742.86, percentage: 9.7 },
          { category: 'Business', marketCount: 3, totalVolume: 5500.00, averageVolume: 1833.33, percentage: 4.4 },
          { category: 'Other', marketCount: 2, totalVolume: 2250.50, averageVolume: 1125.25, percentage: 1.8 }
        ],
        recentTrends: [], // No fake trend data - real analytics not implemented
        userEngagement: {
          totalUsers: 0, // Real user tracking not implemented
          activeUsers: 0,
          averageBetsPerUser: 0,
          averageStakeSize: 0,
          retentionRate: 0
        },
        settlementStats: {
          averageSettlementTime: 2.3,
          disputeRate: 4.2,
          accurateSettlements: 95.8,
          totalDisputes: 7
        }
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalytics(mockAnalytics);

    } catch (err: any) {
      console.error('Failed to load analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number, showSign = false) => {
    const sign = showSign && value > 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center py-12`}>
        <div className="text-center">
          <RefreshCw className="h-12 w-12 text-gray-400 mx-auto mb-3 animate-spin" />
          <h3 className="text-lg font-medium text-white mb-2">Loading Analytics</h3>
          <p className="text-gray-400">Fetching market performance data...</p>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className={`${className} text-center py-8`}>
        <p className="text-admin-danger mb-4">{error || 'Failed to load analytics'}</p>
        <Button variant="outline" onClick={() => loadAnalytics()} className="admin-button-secondary">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2 text-white">
            <BarChart3 className="h-6 w-6 text-admin-accent" />
            Betting Analytics
          </h2>
          <p className="text-gray-400 mt-1">Market performance and user behavior insights</p>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => loadAnalytics(true)}
            disabled={refreshing}
            className="admin-button-secondary"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>

          <Button 
            variant="outline" 
            size="sm"
            className="admin-button-secondary"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="admin-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <DollarSign className="h-4 w-4 text-admin-success" />
              Total Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-admin-success">
              {formatCurrency(analytics.marketPerformance.totalVolume)}
            </div>
            <div className="flex items-center gap-1 text-xs mt-1">
              <TrendingUp className="h-3 w-3 text-admin-success" />
              <span className="text-admin-success">
                {formatPercentage(analytics.marketPerformance.volumeGrowth, true)}
              </span>
              <span className="text-gray-400">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <Target className="h-4 w-4 text-admin-accent" />
              Active Markets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-admin-accent">
              {analytics.marketPerformance.activeMarkets}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              of {analytics.marketPerformance.totalMarkets} total markets
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <Users className="h-4 w-4 text-admin-info" />
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-admin-info">
              {analytics.userEngagement.activeUsers.toLocaleString()}
            </div>
            <div className="flex items-center gap-1 text-xs mt-1">
              <TrendingUp className="h-3 w-3 text-admin-success" />
              <span className="text-admin-success">
                {formatPercentage(analytics.marketPerformance.participantGrowth, true)}
              </span>
              <span className="text-gray-400">growth</span>
            </div>
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-400">
              <Activity className="h-4 w-4 text-admin-warning" />
              Avg Settlement Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-admin-warning">
              {analytics.settlementStats.averageSettlementTime} days
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {formatPercentage(analytics.settlementStats.accurateSettlements)} accuracy
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="categories" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-admin-secondary">
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="users">User Engagement</TabsTrigger>
          <TabsTrigger value="settlement">Settlement Stats</TabsTrigger>
        </TabsList>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-6">
          <Card className="admin-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <PieChart className="h-5 w-5 text-admin-accent" />
                Market Categories Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analytics.topCategories.map((category) => (
                <div key={category.category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-white">{category.category}</h4>
                      <span className="px-2 py-1 text-xs rounded border bg-admin-accent/20 text-admin-accent border-admin-accent/30">
                        {category.marketCount} markets
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-white">{formatCurrency(category.totalVolume)}</div>
                      <div className="text-sm text-gray-400">
                        avg: {formatCurrency(category.averageVolume)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="w-full bg-admin-secondary rounded-full h-2">
                    <div
                      className="bg-admin-accent h-2 rounded-full transition-all duration-300"
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                  
                  <div className="text-xs text-gray-400">
                    {formatPercentage(category.percentage)} of total volume
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-6">
          <Card className="admin-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <LineChart className="h-5 w-5 text-admin-accent" />
                Platform Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-400">Daily Volume</div>
                  <div className="text-2xl font-bold text-white">
                    {formatCurrency(analytics.recentTrends.slice(-1)[0]?.totalVolume || 0)}
                  </div>
                  <div className="text-xs text-gray-400">Latest day</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-400">New Markets</div>
                  <div className="text-2xl font-bold text-white">
                    {analytics.recentTrends.reduce((sum, day) => sum + day.newMarkets, 0)}
                  </div>
                  <div className="text-xs text-gray-400">This period</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-400">Peak Active Users</div>
                  <div className="text-2xl font-bold text-white">
                    {Math.max(...analytics.recentTrends.map(day => day.activeUsers))}
                  </div>
                  <div className="text-xs text-gray-400">Single day record</div>
                </div>
              </div>
              
              {/* Trend visualization placeholder */}
              <div className="mt-6 p-8 bg-admin-secondary/30 rounded-lg text-center text-gray-400">
                <LineChart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-white">Chart visualization would be displayed here</p>
                <p className="text-xs">Integration with charting library needed</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Engagement Tab */}
        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Users className="h-5 w-5 text-admin-accent" />
                  User Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Users</span>
                  <span className="font-bold text-white">{analytics.userEngagement.totalUsers.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Active Users</span>
                  <span className="font-bold text-white">{analytics.userEngagement.activeUsers.toLocaleString()}</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Retention Rate</span>
                    <span className="font-bold text-white">{formatPercentage(analytics.userEngagement.retentionRate)}</span>
                  </div>
                  <div className="w-full bg-admin-secondary rounded-full h-2">
                    <div
                      className="bg-admin-success h-2 rounded-full transition-all duration-300"
                      style={{ width: `${analytics.userEngagement.retentionRate}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Activity className="h-5 w-5 text-admin-accent" />
                  Engagement Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Bets per User</span>
                  <span className="font-bold text-white">{analytics.userEngagement.averageBetsPerUser}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Stake Size</span>
                  <span className="font-bold text-white">{formatCurrency(analytics.userEngagement.averageStakeSize)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Participants/Market</span>
                  <span className="font-bold text-white">{analytics.marketPerformance.averageParticipants}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settlement Stats Tab */}
        <TabsContent value="settlement" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Clock className="h-5 w-5 text-admin-accent" />
                  Settlement Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Average Time</span>
                  <span className="font-bold text-white">{analytics.settlementStats.averageSettlementTime} days</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Settlement Accuracy</span>
                    <span className="font-bold text-admin-success">
                      {formatPercentage(analytics.settlementStats.accurateSettlements)}
                    </span>
                  </div>
                  <div className="w-full bg-admin-secondary rounded-full h-2">
                    <div
                      className="bg-admin-success h-2 rounded-full transition-all duration-300"
                      style={{ width: `${analytics.settlementStats.accurateSettlements}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Markets Settled</span>
                  <span className="font-bold text-white">{analytics.marketPerformance.settledMarkets}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Zap className="h-5 w-5 text-admin-accent" />
                  Dispute Resolution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Disputes</span>
                  <span className="font-bold text-white">{analytics.settlementStats.totalDisputes}</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Dispute Rate</span>
                    <span className={`font-bold ${
                      analytics.settlementStats.disputeRate > 5 ? 'text-admin-danger' : 'text-admin-success'
                    }`}>
                      {formatPercentage(analytics.settlementStats.disputeRate)}
                    </span>
                  </div>
                  <div className="w-full bg-admin-secondary rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        analytics.settlementStats.disputeRate > 5 ? 'bg-admin-danger' : 'bg-admin-success'
                      }`}
                      style={{ width: `${Math.min(analytics.settlementStats.disputeRate * 2, 100)}%` }}
                    />
                  </div>
                </div>
                
                <div className="text-xs text-gray-400">
                  Lower dispute rates indicate better settlement quality
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}