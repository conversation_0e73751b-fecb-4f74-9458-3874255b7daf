/**
 * BetBet Admin - Custom Betting Dashboard Component
 * ===============================================
 * 
 * Migrated admin dashboard for managing betting markets and platform operations.
 * Provides comprehensive market management, statistics, and administrative controls.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CreateMarketModal from './CreateMarketModal';
import { 
  Shield,
  BarChart3,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Activity,
  Search,
  Filter,
  Eye,
  Gavel,
  Ban,
  RefreshCw,
  Download,
  Play,
  Pause,
  Settings
} from 'lucide-react';

interface CustomBet {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'active' | 'settling' | 'settled' | 'cancelled' | 'suspended';
  creator_user_id: string;
  total_stakes: number | string; // API returns string, but we handle both
  total_participants: number;
  created_at: string;
  deadline: string;
  verification_deadline: string;
}

interface AdminStats {
  totalMarkets: number;
  activeMarkets: number;
  settledMarkets: number;
  pendingSettlement: number;
  totalVolume: number;
  totalUsers: number;
  recentActivity: number;
  platformFees: number;
}

interface BettingAdminDashboardProps {
  isAdmin?: boolean;
}

// No mock data - using real database data only

// Helper function to safely convert string/number to number
const safeNumber = (value: string | number | undefined | null): number => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') return parseFloat(value) || 0;
  return 0;
};

// Helper function to format currency
const formatCurrency = (value: string | number | undefined | null): string => {
  return `$${safeNumber(value).toFixed(2)}`;
};

export default function BettingAdminDashboard({ isAdmin = true }: BettingAdminDashboardProps) {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [markets, setMarkets] = useState<CustomBet[]>([]);
  const [filteredMarkets, setFilteredMarkets] = useState<CustomBet[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    if (isAdmin) {
      loadAdminData();
    }
  }, [isAdmin]);

  useEffect(() => {
    filterMarkets();
  }, [markets, searchTerm, statusFilter]);

  const loadAdminData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data using admin API routes (server-side)
      const [marketsResponse, overviewResponse] = await Promise.all([
        fetch('/api/betting/markets', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }),
        fetch('/api/betting/overview', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      ]);

      if (!marketsResponse.ok) {
        throw new Error(`HTTP error! status: ${marketsResponse.status}`);
      }

      const marketsData = await marketsResponse.json();
      console.log('Fetched markets data:', marketsData);

      // Update markets state with real data
      if (marketsData && Array.isArray(marketsData)) {
        setMarkets(marketsData);
        calculateStats(marketsData);
        console.log(`Loaded ${marketsData.length} real markets from database`);
      } else {
        // No markets found - this is normal for a new platform
        setMarkets([]);
        calculateStats([]);
        console.log('No markets found in database');
      }

    } catch (err: any) {
      console.error('Failed to load admin data:', err);
      setError(`Failed to load admin dashboard: ${err.message}`);
      // Set empty state on error
      setMarkets([]);
      calculateStats([]);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (marketsData: CustomBet[]) => {
    const adminStats: AdminStats = {
      totalMarkets: marketsData.length,
      activeMarkets: marketsData.filter(m => m.status === 'open' || m.status === 'active').length,
      settledMarkets: marketsData.filter(m => m.status === 'settled').length,
      pendingSettlement: marketsData.filter(m =>
        m.status === 'settling' ||
        (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
      ).length,
      totalVolume: marketsData.reduce((sum, m) => sum + safeNumber(m.total_stakes), 0),
      totalUsers: new Set(marketsData.map(m => m.creator_user_id)).size,
      recentActivity: marketsData.filter(m => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return new Date(m.created_at) > oneDayAgo;
      }).length,
      platformFees: marketsData.reduce((sum, m) => sum + (safeNumber(m.total_stakes) * 0.02), 0) // Assuming 2% fee
    };

    setStats(adminStats);
  };

  const filterMarkets = () => {
    let filtered = markets;

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(m => 
        m.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      if (statusFilter === 'needs_settlement') {
        filtered = filtered.filter(m => 
          m.status === 'settling' || 
          (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
        );
      } else {
        filtered = filtered.filter(m => m.status === statusFilter);
      }
    }

    setFilteredMarkets(filtered);
  };

  const handleMarketAction = async (marketId: string, action: 'suspend' | 'cancel' | 'force_settle') => {
    try {
      setActionLoading(marketId);
      setError(null);

      // Call the admin API for market actions
      const response = await fetch(`/api/betting/markets/${marketId}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: action === 'force_settle' ? 'settle' : action,
          winning_outcome_id: action === 'force_settle' ? 'outcome-1' : undefined, // In real app, this would be selected by admin
          resolution_notes: `Market ${action} by admin`
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} market: ${response.status}`);
      }

      const result = await response.json();
      console.log(`Market ${action} successful:`, result);

      // Update local state
      setMarkets(prev => prev.map(m =>
        m.id === marketId
          ? { ...m, status: action === 'suspend' ? 'suspended' : action === 'cancel' ? 'cancelled' : 'settled' as any }
          : m
      ));

    } catch (err: any) {
      setError(err.message || `Failed to ${action} market`);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'open': { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30' },
      'active': { color: 'bg-green-500/20 text-green-400 border-green-500/30' },
      'settling': { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' },
      'settled': { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30' },
      'cancelled': { color: 'bg-red-500/20 text-red-400 border-red-500/30' },
      'suspended': { color: 'bg-orange-500/20 text-orange-400 border-orange-500/30' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['open'];
    
    return (
      <span className={`px-2 py-1 text-xs rounded border ${config.color}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const exportData = () => {
    // Generate CSV export
    const csvData = filteredMarkets.map(m => ({
      ID: m.id,
      Title: m.title,
      Status: m.status,
      'Total Stakes': m.total_stakes,
      'Total Participants': m.total_participants,
      'Created At': m.created_at,
      'Deadline': m.deadline
    }));

    console.log('Export data:', csvData);
    // In real implementation, would generate and download CSV file
  };

  if (!isAdmin) {
    return (
      <Card className="admin-card border-admin-danger/50">
        <CardContent className="p-6">
          <div className="flex items-center gap-3 text-admin-danger">
            <Shield className="h-5 w-5" />
            <div>
              <h3 className="font-semibold">Access Denied</h3>
              <p className="text-sm text-gray-400">Admin privileges required to view this dashboard.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-admin-accent" />
            Betting Markets Dashboard
          </h2>
          <p className="text-gray-400 mt-1">Manage custom betting markets and platform operations</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadAdminData} className="admin-button-secondary">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportData} className="admin-button-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowCreateModal(true)} className="admin-button-primary">
            <Play className="h-4 w-4 mr-2" />
            Create Market
          </Button>
          <Button className="admin-button-primary">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="admin-card border-admin-danger/50 bg-admin-danger/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-admin-danger">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Admin Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="admin-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-admin-accent" />
                Total Markets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.totalMarkets}</div>
              <p className="text-xs text-gray-400 mt-1">
                {stats.activeMarkets} active
              </p>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-admin-warning" />
                Pending Settlement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-admin-warning">
                {stats.pendingSettlement}
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Requires attention
              </p>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-admin-success" />
                Total Volume
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-admin-success">
                {formatCurrency(stats.totalVolume)}
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Platform fees: {formatCurrency(stats.platformFees)}
              </p>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Activity className="h-4 w-4 text-admin-info" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.recentActivity}</div>
              <p className="text-xs text-gray-400 mt-1">
                New markets (24h)
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Market Management */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="text-white">Market Management</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <div className="flex items-center justify-between mb-4">
              <TabsList className="bg-admin-secondary">
                <TabsTrigger value="all">All Markets</TabsTrigger>
                <TabsTrigger value="pending">Pending Settlement</TabsTrigger>
                <TabsTrigger value="flagged">Flagged Issues</TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    placeholder="Search markets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64 pl-10 pr-4 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
                  />
                </div>
                
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                >
                  <option value="all">All Status</option>
                  <option value="open">Open</option>
                  <option value="active">Active</option>
                  <option value="settling">Settling</option>
                  <option value="settled">Settled</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="needs_settlement">Needs Settlement</option>
                </select>
              </div>
            </div>

            <TabsContent value="all" className="space-y-4">
              {filteredMarkets.length > 0 ? (
                <div className="space-y-4">
                  {filteredMarkets.map((market) => {
                    const needsSettlement = new Date() > new Date(market.verification_deadline) && market.status !== 'settled';
                    
                    return (
                      <div 
                        key={market.id}
                        className={`flex items-center justify-between p-4 border border-admin-secondary rounded-lg hover:bg-admin-secondary/30 transition-colors ${
                          needsSettlement ? 'border-admin-warning/50 bg-admin-warning/10' : ''
                        }`}
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-white">{market.title}</h4>
                            {getStatusBadge(market.status)}
                            {needsSettlement && (
                              <span className="px-2 py-1 text-xs rounded border bg-admin-warning/20 text-admin-warning border-admin-warning/30">
                                Overdue Settlement
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-400">
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              {formatCurrency(market.total_stakes)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {market.total_participants} participants
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Deadline: {market.verification_deadline.split('T')[0]}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="admin-button-secondary">
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {needsSettlement && (
                            <Button variant="outline" size="sm" className="admin-button-primary">
                              <Gavel className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {market.status === 'open' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleMarketAction(market.id, 'suspend')}
                              disabled={actionLoading === market.id}
                              className="admin-button-secondary"
                            >
                              <Ban className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-white">No markets found</h3>
                  <p className="mt-1 text-sm text-gray-400">No markets match your current filters.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {/* Markets that need settlement */}
              {filteredMarkets.filter(m => 
                m.status === 'settling' || 
                (new Date() > new Date(m.verification_deadline) && m.status !== 'settled')
              ).map((market) => (
                <Card key={market.id} className="admin-card border-admin-warning/50 bg-admin-warning/10">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-white">{market.title}</h4>
                        <p className="text-sm text-gray-400 mt-1">
                          Settlement overdue by {Math.ceil((Date.now() - new Date(market.verification_deadline).getTime()) / (1000 * 60 * 60 * 24))} days
                        </p>
                      </div>
                      
                      <Button className="admin-button-primary">
                        <Gavel className="h-4 w-4 mr-2" />
                        Settle Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="flagged" className="space-y-4">
              <div className="text-center py-12">
                <CheckCircle className="mx-auto h-12 w-12 text-admin-success" />
                <h3 className="mt-2 text-sm font-medium text-white">No flagged issues</h3>
                <p className="mt-1 text-sm text-gray-400">All markets are operating normally.</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create Market Modal */}
      <CreateMarketModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onMarketCreated={loadAdminData}
      />
    </div>
  );
}