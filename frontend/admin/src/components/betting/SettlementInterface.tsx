/**
 * BetBet Admin - Settlement Interface Component
 * ===========================================
 * 
 * Administrative interface for settling betting markets and determining outcomes.
 * Migrated from main web app for proper admin separation.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Gavel,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Users,
  FileText,
  Eye,
  Loader2,
  Info,
  ExternalLink,
  TrendingUp,
  Play,
  Pause
} from 'lucide-react';

interface CustomBet {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'active' | 'settling' | 'settled' | 'cancelled' | 'suspended';
  creator_user_id: string;
  total_stakes: number;
  total_participants: number;
  created_at: string;
  deadline: string;
  verification_deadline: string;
  event_criteria: string;
  settlement_type?: string;
  winning_outcome_id?: string;
  outcomes: Array<{
    id: string;
    outcome_text: string;
    current_odds: number;
  }>;
}

interface BetParticipant {
  id: string;
  user_id: string;
  outcome_id: string;
  stake_amount: number;
  position_type: 'backing' | 'laying';
  desired_odds: number;
}

interface SettlementInterfaceProps {
  market: CustomBet;
  participants: BetParticipant[];
  canSettle: boolean;
  onSettled?: () => void;
}

interface SettlementData {
  winning_outcome_id: string;
  settlement_notes: string;
  evidence_urls: string[];
  settlement_method: 'manual' | 'oracle' | 'consensus';
}

// Mock data for demonstration
const mockMarket: CustomBet = {
  id: '1',
  title: 'Bitcoin will reach $100k by end of 2024',
  description: 'Prediction market on Bitcoin price target',
  status: 'settling',
  creator_user_id: 'creator1',
  total_stakes: 25420.75,
  total_participants: 143,
  created_at: '2024-01-15T00:00:00Z',
  deadline: '2024-12-31T23:59:59Z',
  verification_deadline: '2025-01-07T23:59:59Z',
  event_criteria: 'Bitcoin (BTC) must reach or exceed $100,000 USD on any major exchange (Binance, Coinbase, Kraken) before 11:59 PM UTC on December 31, 2024. Price will be verified using CoinGecko API.',
  settlement_type: 'manual',
  outcomes: [
    { id: 'yes', outcome_text: 'Yes, BTC reaches $100k', current_odds: 2.5 },
    { id: 'no', outcome_text: 'No, BTC stays below $100k', current_odds: 1.8 }
  ]
};

const mockParticipants: BetParticipant[] = [
  { id: '1', user_id: 'user1', outcome_id: 'yes', stake_amount: 1000, position_type: 'backing', desired_odds: 2.5 },
  { id: '2', user_id: 'user2', outcome_id: 'no', stake_amount: 1500, position_type: 'backing', desired_odds: 1.8 },
  { id: '3', user_id: 'user3', outcome_id: 'yes', stake_amount: 750, position_type: 'laying', desired_odds: 2.2 }
];

export default function SettlementInterface({ 
  market = mockMarket, 
  participants = mockParticipants, 
  canSettle = true, 
  onSettled 
}: Partial<SettlementInterfaceProps>) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isSettling, setIsSettling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settlementData, setSettlementData] = useState<SettlementData>({
    winning_outcome_id: '',
    settlement_notes: '',
    evidence_urls: [],
    settlement_method: (market?.settlement_type as any) || 'manual'
  });

  // Calculate settlement statistics
  const calculateSettlementStats = () => {
    if (!market || !participants) return null;

    const stats = {
      totalParticipants: participants.length,
      totalStakes: market.total_stakes,
      outcomeBreakdown: market.outcomes.map(outcome => {
        const outcomeParticipants = participants.filter(p => p.outcome_id === outcome.id);
        const totalStaked = outcomeParticipants.reduce((sum, p) => sum + p.stake_amount, 0);
        
        return {
          outcome,
          participantCount: outcomeParticipants.length,
          totalStaked,
          percentage: market.total_stakes > 0 ? (totalStaked / market.total_stakes) * 100 : 0
        };
      }),
      estimatedPayouts: market.outcomes.map(outcome => {
        if (settlementData.winning_outcome_id !== outcome.id) return null;
        
        const winners = participants.filter(p => 
          p.outcome_id === outcome.id && p.position_type === 'backing'
        );
        
        const totalWinnerStakes = winners.reduce((sum, p) => sum + p.stake_amount, 0);
        const totalLoserStakes = market.total_stakes - totalWinnerStakes;
        
        return {
          outcome,
          winnerCount: winners.length,
          totalWinnerStakes,
          totalPayouts: totalWinnerStakes + totalLoserStakes,
          averagePayout: winners.length > 0 ? (totalWinnerStakes + totalLoserStakes) / winners.length : 0
        };
      }).filter(Boolean)[0]
    };

    return stats;
  };

  const handleSettlement = async () => {
    if (!settlementData.winning_outcome_id) {
      setError('Please select a winning outcome.');
      return;
    }

    try {
      setIsSettling(true);
      setError(null);

      // In real implementation, would call API
      // await customBettingAPI.settleBet(market.id, settlementData);

      console.log('Settling market:', market?.id, settlementData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      onSettled?.();

    } catch (err: any) {
      setError(err.message || 'Failed to settle market');
    } finally {
      setIsSettling(false);
    }
  };

  const addEvidenceUrl = () => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: [...prev.evidence_urls, '']
    }));
  };

  const updateEvidenceUrl = (index: number, url: string) => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: prev.evidence_urls.map((u, i) => i === index ? url : u)
    }));
  };

  const removeEvidenceUrl = (index: number) => {
    setSettlementData(prev => ({
      ...prev,
      evidence_urls: prev.evidence_urls.filter((_, i) => i !== index)
    }));
  };

  const stats = calculateSettlementStats();
  const isSettled = market?.status === 'settled';
  const isSettling_status = market?.status === 'settling';

  if (!market) {
    return (
      <Card className="admin-card">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-white mb-2">Market Not Found</h3>
          <p className="text-gray-400">The requested market could not be loaded.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Settlement Status Header */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Gavel className="h-5 w-5 text-admin-accent" />
            Market Settlement
            <span className={`px-2 py-1 text-xs rounded border ${
              isSettled 
                ? 'bg-admin-success/20 text-admin-success border-admin-success/30'
                : isSettling_status 
                ? 'bg-admin-warning/20 text-admin-warning border-admin-warning/30'
                : 'bg-admin-secondary/50 text-gray-400 border-admin-secondary'
            }`}>
              {market.status.charAt(0).toUpperCase() + market.status.slice(1)}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium mb-1 text-white">{market.title}</h3>
              <p className="text-sm text-gray-400">
                Settlement deadline: {new Date(market.verification_deadline).toLocaleString()}
              </p>
            </div>
            
            {isSettled && market.winning_outcome_id && (
              <div className="text-right">
                <div className="text-sm text-gray-400">Winning Outcome</div>
                <div className="font-medium text-admin-success">
                  {market.outcomes.find(o => o.id === market.winning_outcome_id)?.outcome_text}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="admin-card border-admin-danger/50 bg-admin-danger/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-admin-danger">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settlement Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 bg-admin-secondary">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="outcomes">Outcomes</TabsTrigger>
          <TabsTrigger value="participants">Participants</TabsTrigger>
          <TabsTrigger value="settle">Settlement</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="admin-card">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-admin-accent">
                  {stats?.totalParticipants || 0}
                </div>
                <div className="text-sm text-gray-400">Total Participants</div>
              </CardContent>
            </Card>
            
            <Card className="admin-card">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-admin-success">
                  ${stats?.totalStakes.toFixed(2) || '0.00'}
                </div>
                <div className="text-sm text-gray-400">Total Volume</div>
              </CardContent>
            </Card>
            
            <Card className="admin-card">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-admin-info">
                  {market.outcomes.length}
                </div>
                <div className="text-sm text-gray-400">Possible Outcomes</div>
              </CardContent>
            </Card>
          </div>

          {/* Market Criteria */}
          <Card className="admin-card">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2 text-white">
                <FileText className="h-5 w-5 text-admin-accent" />
                Settlement Criteria
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-admin-accent/10 border border-admin-accent/30 rounded-lg p-4">
                <p className="text-white">{market.event_criteria}</p>
              </div>
            </CardContent>
          </Card>

          {/* Settlement Timeline */}
          <Card className="admin-card">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2 text-white">
                <Clock className="h-5 w-5 text-admin-accent" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-admin-success rounded-full"></div>
                <div>
                  <div className="font-medium text-white">Market Created</div>
                  <div className="text-sm text-gray-400">
                    {new Date(market.created_at).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  new Date() > new Date(market.deadline) ? 'bg-admin-success' : 'bg-gray-500'
                }`}></div>
                <div>
                  <div className="font-medium text-white">Betting Deadline</div>
                  <div className="text-sm text-gray-400">
                    {new Date(market.deadline).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  isSettled 
                    ? 'bg-admin-success' 
                    : new Date() > new Date(market.verification_deadline) 
                    ? 'bg-admin-danger' 
                    : 'bg-admin-warning'
                }`}></div>
                <div>
                  <div className="font-medium text-white">Settlement Deadline</div>
                  <div className="text-sm text-gray-400">
                    {new Date(market.verification_deadline).toLocaleString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Outcomes Tab */}
        <TabsContent value="outcomes" className="space-y-4">
          {stats?.outcomeBreakdown.map((item) => (
            <Card key={item.outcome.id} className="admin-card">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-white">{item.outcome.outcome_text}</h4>
                    <span className="px-2 py-1 text-xs rounded border bg-admin-accent/20 text-admin-accent border-admin-accent/30">
                      {item.outcome.current_odds}x odds
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-400">Participants</div>
                      <div className="font-medium text-white">{item.participantCount}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Total Staked</div>
                      <div className="font-medium text-white">${item.totalStaked.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Market Share</div>
                      <div className="font-medium text-white">{item.percentage.toFixed(1)}%</div>
                    </div>
                  </div>
                  
                  <div className="w-full bg-admin-secondary rounded-full h-2">
                    <div
                      className="bg-admin-accent h-2 rounded-full transition-all duration-300"
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Participants Tab */}
        <TabsContent value="participants" className="space-y-4">
          {participants && participants.length > 0 ? (
            <div className="space-y-2">
              {participants.map((participant) => (
                <div 
                  key={participant.id}
                  className="flex items-center justify-between p-3 bg-admin-secondary/50 rounded-lg border border-admin-secondary"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium font-mono text-sm text-white">
                        {participant.user_id.slice(-8)}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded border ${
                        participant.position_type === 'backing'
                          ? 'bg-admin-success/20 text-admin-success border-admin-success/30'
                          : 'bg-admin-danger/20 text-admin-danger border-admin-danger/30'
                      }`}>
                        {participant.position_type}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Outcome: {participant.outcome_id}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-medium text-white">${participant.stake_amount.toFixed(2)}</div>
                    <div className="text-sm text-gray-400">{participant.desired_odds}x</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No participants in this market</p>
            </div>
          )}
        </TabsContent>

        {/* Settlement Tab */}
        <TabsContent value="settle" className="space-y-6">
          {!canSettle ? (
            <Card className="admin-card border-admin-warning/50 bg-admin-warning/10">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-admin-warning">
                  <Shield className="h-4 w-4" />
                  <span className="text-sm">
                    You don't have permission to settle this market. Only the market creator or admin can perform settlement.
                  </span>
                </div>
              </CardContent>
            </Card>
          ) : isSettled ? (
            <Card className="admin-card border-admin-success/50 bg-admin-success/10">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-admin-success">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">This market has already been settled.</span>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Winning Outcome Selection */}
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Select Winning Outcome</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {market.outcomes.map((outcome) => (
                    <div key={outcome.id} className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id={outcome.id}
                        name="winning_outcome"
                        value={outcome.id}
                        checked={settlementData.winning_outcome_id === outcome.id}
                        onChange={(e) => setSettlementData(prev => ({ ...prev, winning_outcome_id: e.target.value }))}
                        className="text-admin-accent focus:ring-admin-accent"
                      />
                      <label htmlFor={outcome.id} className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <span className="text-white">{outcome.outcome_text}</span>
                          <span className="text-sm text-gray-400">
                            {outcome.current_odds}x odds
                          </span>
                        </div>
                      </label>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Settlement Notes */}
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Settlement Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div>
                    <label htmlFor="notes" className="text-sm text-gray-400">Explain the settlement decision</label>
                    <textarea
                      id="notes"
                      placeholder="Provide details about how and why this outcome was determined..."
                      value={settlementData.settlement_notes}
                      onChange={(e) => setSettlementData(prev => ({ ...prev, settlement_notes: e.target.value }))}
                      rows={4}
                      className="w-full mt-2 px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Evidence URLs */}
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2 text-white">
                    <ExternalLink className="h-5 w-5 text-admin-accent" />
                    Supporting Evidence (Optional)
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {settlementData.evidence_urls.map((url, index) => (
                    <div key={index} className="flex gap-2">
                      <input
                        type="url"
                        placeholder="https://example.com/evidence"
                        value={url}
                        onChange={(e) => updateEvidenceUrl(index, e.target.value)}
                        className="flex-1 px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeEvidenceUrl(index)}
                        className="admin-button-secondary"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <Button variant="outline" onClick={addEvidenceUrl} className="admin-button-secondary">
                    Add Evidence URL
                  </Button>
                </CardContent>
              </Card>

              {/* Estimated Payouts */}
              {settlementData.winning_outcome_id && stats?.estimatedPayouts && (
                <Card className="admin-card">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2 text-white">
                      <DollarSign className="h-5 w-5 text-admin-success" />
                      Estimated Payouts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-400">Winners</div>
                        <div className="text-xl font-bold text-admin-success">
                          {stats.estimatedPayouts.winnerCount}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-400">Total Payouts</div>
                        <div className="text-xl font-bold text-white">
                          ${stats.estimatedPayouts.totalPayouts.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Settlement Actions */}
              <div className="flex justify-end gap-4 pt-6 border-t border-admin-secondary">
                <Button
                  onClick={handleSettlement}
                  disabled={isSettling || !settlementData.winning_outcome_id}
                  size="lg"
                  className="admin-button-primary"
                >
                  {isSettling ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Settling Market...
                    </>
                  ) : (
                    <>
                      <Gavel className="h-4 w-4 mr-2" />
                      Settle Market
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}