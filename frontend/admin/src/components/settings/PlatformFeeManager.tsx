/**
 * BetBet Admin - Platform Fee Manager
 * ==================================
 * 
 * Advanced fee configuration with conditional rules, A/B testing, and promotional settings.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Percent,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Settings,
  Plus,
  Edit,
  Trash2,
  Target,
  Zap,
  Clock,
  Users,
  Crown,
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface FeeSettings {
  gaming: {
    standard: number;
    premium: number;
    promotional: number;
    highVolume: number;
  };
  trading: {
    standard: number;
    maker: number;
    taker: number;
    premium: number;
  };
  subscriptions: {
    expert: number;
    premium: number;
    enterprise: number;
  };
}

interface PlatformFeeManagerProps {
  settings: FeeSettings;
  onUpdate: (category: string, key: string, value: number) => void;
}

export default function PlatformFeeManager({ settings, onUpdate }: PlatformFeeManagerProps) {
  const [activePromo, setActivePromo] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Mock promotional campaigns
  const promotionalCampaigns = [
    {
      id: 'newuser',
      name: 'New User Welcome',
      description: '50% off platform fees for first 30 days',
      status: 'active',
      reduction: 50,
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      eligibleUsers: 'new_users',
      currentUsage: 0, // Real usage data needed
      maxUsage: 5000
    },
    {
      id: 'highvolume',
      name: 'High Volume Traders',
      description: 'Reduced fees for users with >$10K monthly volume',
      status: 'active',
      reduction: 30,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      eligibleUsers: 'high_volume',
      currentUsage: 89,
      maxUsage: 500
    },
    {
      id: 'weekend',
      name: 'Weekend Gaming Special',
      description: '25% off gaming fees on weekends',
      status: 'scheduled',
      reduction: 25,
      startDate: '2024-02-01',
      endDate: '2024-04-01',
      eligibleUsers: 'all_users',
      currentUsage: 0,
      maxUsage: null
    }
  ];

  const FeeControl = ({ 
    label, 
    value, 
    category, 
    key,
    description,
    recommended,
    impact
  }: {
    label: string;
    value: number;
    category: string;
    key: string;
    description: string;
    recommended?: number;
    impact?: 'high' | 'medium' | 'low';
  }) => {
    const handleChange = (newValue: number) => {
      onUpdate(category, key, newValue);
    };

    const getImpactColor = () => {
      switch (impact) {
        case 'high': return 'text-red-400';
        case 'medium': return 'text-yellow-400';
        case 'low': return 'text-green-400';
        default: return 'text-gray-400';
      }
    };

    const getDifference = () => {
      if (!recommended) return null;
      const diff = value - recommended;
      const isHigher = diff > 0;
      return {
        value: Math.abs(diff),
        isHigher,
        percentage: Math.abs((diff / recommended) * 100)
      };
    };

    const difference = getDifference();

    return (
      <div className="space-y-3 p-4 bg-admin-secondary/30 rounded-lg border border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <label className="block text-sm font-medium text-white">{label}</label>
            <p className="text-xs text-gray-400 mt-1">{description}</p>
          </div>
          {impact && (
            <div className={`text-xs ${getImpactColor()} flex items-center gap-1`}>
              <Target className="h-3 w-3" />
              {impact.toUpperCase()} IMPACT
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="0"
                max="25"
                step="0.1"
                value={value}
                onChange={(e) => handleChange(Number(e.target.value))}
                className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="w-20">
                <div className="flex items-center space-x-1">
                  <input
                    type="number"
                    min="0"
                    max="25"
                    step="0.1"
                    value={value}
                    onChange={(e) => handleChange(Number(e.target.value))}
                    className="w-full px-2 py-1 text-sm bg-admin-secondary border border-gray-600 rounded text-white text-center"
                  />
                  <Percent className="h-3 w-3 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {recommended && difference && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Recommended: {recommended}%</span>
            <span className={difference.isHigher ? 'text-red-400' : 'text-green-400'}>
              {difference.isHigher ? '+' : '-'}{difference.value.toFixed(1)}% 
              ({difference.isHigher ? '+' : '-'}{difference.percentage.toFixed(1)}%)
            </span>
          </div>
        )}

        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center">
            <div className="text-gray-400">Daily Revenue</div>
            <div className="text-white font-medium">
              $0 {/* Real revenue calculation needed */}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-400">User Impact</div>
            <div className={value > 12 ? 'text-red-400' : value > 8 ? 'text-yellow-400' : 'text-green-400'}>
              {value > 12 ? 'High' : value > 8 ? 'Medium' : 'Low'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-400">Competition</div>
            <div className={value > 10 ? 'text-red-400' : value > 7 ? 'text-yellow-400' : 'text-green-400'}>
              {value > 10 ? 'Above' : value > 7 ? 'Match' : 'Below'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const PromoCampaignCard = ({ campaign }: { campaign: typeof promotionalCampaigns[0] }) => (
    <Card className="admin-card">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-white">{campaign.name}</CardTitle>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 text-xs rounded-full ${
              campaign.status === 'active' ? 'bg-green-500/20 text-green-400' :
              campaign.status === 'scheduled' ? 'bg-blue-500/20 text-blue-400' :
              'bg-gray-500/20 text-gray-400'
            }`}>
              {campaign.status.toUpperCase()}
            </span>
            <Button size="sm" variant="outline" className="h-6 px-2">
              <Edit className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <p className="text-xs text-gray-400">{campaign.description}</p>
        
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <div className="text-gray-400">Reduction</div>
            <div className="text-white font-medium">{campaign.reduction}%</div>
          </div>
          <div>
            <div className="text-gray-400">Duration</div>
            <div className="text-white font-medium">
              {new Date(campaign.startDate).toLocaleDateString()} - {new Date(campaign.endDate).toLocaleDateString()}
            </div>
          </div>
        </div>

        {campaign.maxUsage && (
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-400">Usage</span>
              <span className="text-white">{campaign.currentUsage} / {campaign.maxUsage}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-admin-accent h-2 rounded-full transition-all duration-300"
                style={{ width: `${(campaign.currentUsage / campaign.maxUsage) * 100}%` }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Gaming Fees */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-admin-accent" />
            Gaming Platform Fees
          </CardTitle>
          <p className="text-sm text-gray-400">
            Fees applied to chess games, tournaments, and other gaming activities
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <FeeControl
            label="Standard Games"
            value={settings.gaming.standard}
            category="gaming"
            key="standard"
            description="Default fee for regular chess games and tournaments"
            recommended={10}
            impact="high"
          />
          <FeeControl
            label="Premium Games"
            value={settings.gaming.premium}
            category="gaming"
            key="premium"
            description="Fee for premium games with enhanced features"
            recommended={8}
            impact="medium"
          />
          <FeeControl
            label="Promotional Rate"
            value={settings.gaming.promotional}
            category="gaming"
            key="promotional"
            description="Special promotional rate for campaigns and events"
            recommended={5}
            impact="low"
          />
          <FeeControl
            label="High Volume"
            value={settings.gaming.highVolume}
            category="gaming"
            key="highVolume"
            description="Reduced rate for users with high gaming volume"
            recommended={7}
            impact="medium"
          />
        </CardContent>
      </Card>

      {/* Trading Fees */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-admin-accent" />
            Trading & Odds Exchange Fees
          </CardTitle>
          <p className="text-sm text-gray-400">
            Fees for odds exchange trading and market making
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <FeeControl
            label="Standard Trading"
            value={settings.trading.standard}
            category="trading"
            key="standard"
            description="Default fee for regular trading activities"
            recommended={0.5}
            impact="high"
          />
          <FeeControl
            label="Maker Fee"
            value={settings.trading.maker}
            category="trading"
            key="maker"
            description="Fee for users who provide liquidity (make orders)"
            recommended={0.3}
            impact="medium"
          />
          <FeeControl
            label="Taker Fee"
            value={settings.trading.taker}
            category="trading"
            key="taker"
            description="Fee for users who take liquidity (fill orders)"
            recommended={0.7}
            impact="medium"
          />
          <FeeControl
            label="Premium Trading"
            value={settings.trading.premium}
            category="trading"
            key="premium"
            description="Reduced fee for premium account holders"
            recommended={0.4}
            impact="low"
          />
        </CardContent>
      </Card>

      {/* Subscription Fees */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-admin-accent" />
            Subscription Service Fees
          </CardTitle>
          <p className="text-sm text-gray-400">
            Platform fees on expert picks and premium services
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <FeeControl
            label="Expert Picks"
            value={settings.subscriptions.expert}
            category="subscriptions"
            key="expert"
            description="Platform fee on expert analyst subscriptions"
            recommended={15}
            impact="medium"
          />
          <FeeControl
            label="Premium Content"
            value={settings.subscriptions.premium}
            category="subscriptions"
            key="premium"
            description="Fee on premium sports analysis and content"
            recommended={20}
            impact="medium"
          />
          <FeeControl
            label="Enterprise"
            value={settings.subscriptions.enterprise}
            category="subscriptions"
            key="enterprise"
            description="Fee for enterprise and institutional services"
            recommended={25}
            impact="low"
          />
        </CardContent>
      </Card>

      {/* Promotional Campaigns */}
      <Card className="admin-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-admin-accent" />
                Active Promotional Campaigns
              </CardTitle>
              <p className="text-sm text-gray-400 mt-1">
                Manage fee reductions and promotional offers
              </p>
            </div>
            <Button className="admin-button-primary">
              <Plus className="h-4 w-4 mr-2" />
              New Campaign
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {promotionalCampaigns.map((campaign) => (
              <PromoCampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card className="admin-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-admin-accent" />
              Advanced Fee Configuration
            </CardTitle>
            <Button 
              variant="outline"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="admin-button-secondary"
            >
              {showAdvanced ? 'Hide' : 'Show'} Advanced
            </Button>
          </div>
        </CardHeader>
        {showAdvanced && (
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  Minimum Fee ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  placeholder="0.01"
                  className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  Maximum Fee ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  placeholder="1000.00"
                  className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  Volume Threshold ($)
                </label>
                <input
                  type="number"
                  placeholder="10000"
                  className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  VIP Tier Discount (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  placeholder="20"
                  className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input type="checkbox" className="rounded" />
                <span className="text-sm text-gray-300">Enable dynamic fee adjustment based on market conditions</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="checkbox" className="rounded" />
                <span className="text-sm text-gray-300">Apply volume-based fee scaling</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="checkbox" className="rounded" />
                <span className="text-sm text-gray-300">Enable A/B testing for fee optimization</span>
              </label>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}