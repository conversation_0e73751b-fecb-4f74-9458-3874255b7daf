/**
 * BetBet Admin Platform Metrics Component
 * ======================================
 * 
 * Interactive analytics dashboard with performance monitoring and charts
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  Database,
  Activity,
  LineChart,
  PieChart,
  Globe,
  Monitor,
  Zap
} from 'lucide-react';

import PlatformPerformance from './PlatformPerformance';

export default function PlatformMetrics() {
  const [activeTab, setActiveTab] = useState('performance');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Platform Analytics</h2>
          <p className="text-gray-400 mt-1">
            Interactive analytics dashboard with performance monitoring and charts
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="admin-button-secondary"
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Analytics
          </Button>
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-admin-secondary">
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Monitor size={16} />
            Performance
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2">
            <TrendingUp size={16} />
            Business
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Activity size={16} />
            Users
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Zap size={16} />
            System
          </TabsTrigger>
        </TabsList>

        {/* Performance Analytics Tab */}
        <TabsContent value="performance">
          <PlatformPerformance />
        </TabsContent>

        {/* Business Analytics Tab */}
        <TabsContent value="business">
          <Card className="admin-card border-admin-warning/50 bg-admin-warning/10">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <TrendingUp className="h-6 w-6 text-admin-warning flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-admin-warning mb-2">Business Analytics Coming Soon</h3>
                  <p className="text-gray-300 mb-4">Business analytics dashboard will include revenue tracking, transaction volume analysis, and profit margin calculations.</p>
                  <div className="text-sm text-gray-400">
                    <p>Planned features:</p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Revenue tracking by service and time period</li>
                      <li>Transaction volume and betting activity analysis</li>
                      <li>User acquisition and retention metrics</li>
                      <li>Profit margin and ROI calculations</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Analytics Tab */}
        <TabsContent value="users">
          <Card className="admin-card border-admin-info/50 bg-admin-info/10">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Activity className="h-6 w-6 text-admin-info flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-admin-info mb-2">User Analytics Coming Soon</h3>
                  <p className="text-gray-300 mb-4">User analytics dashboard will provide insights into user behavior, engagement patterns, and lifecycle metrics.</p>
                  <div className="text-sm text-gray-400">
                    <p>Planned features:</p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>User engagement and session duration analysis</li>
                      <li>Behavioral flow and interaction patterns</li>
                      <li>User lifetime value and retention cohorts</li>
                      <li>Geographic and demographic segmentation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Analytics Tab */}
        <TabsContent value="system">
          <Card className="admin-card border-admin-accent/50 bg-admin-accent/10">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Zap className="h-6 w-6 text-admin-accent flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-admin-accent mb-2">System Analytics Coming Soon</h3>
                  <p className="text-gray-300 mb-4">System analytics dashboard will provide comprehensive infrastructure monitoring and capacity planning insights.</p>
                  <div className="text-sm text-gray-400">
                    <p>Planned features:</p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Infrastructure utilization and capacity planning</li>
                      <li>Database performance and query optimization</li>
                      <li>Security audit logs and threat detection</li>
                      <li>Cost analysis and resource optimization</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}