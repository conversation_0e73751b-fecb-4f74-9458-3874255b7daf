/**
 * Platform Performance Analytics Component
 * =======================================
 * 
 * Interactive performance monitoring with charts and real-time metrics
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LineChart, BarChart, AreaChart, DonutChart } from '@/components/ui/chart';
import analyticsClient, { ServiceHealth, PerformanceMetrics } from '@/lib/analytics-client';
import {
  Activity,
  Database,
  Zap,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Clock,
  Server,
  Wifi,
  HardDrive
} from 'lucide-react';

// Using types from analytics client

export default function PlatformPerformance() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    apiResponseTime: [],
    websocketLatency: [],
    databaseQueries: [],
    errorRate: [],
    cpuUsage: [],
    memoryUsage: [],
    diskUsage: [],
    networkLatency: []
  });

  const [serviceHealth, setServiceHealth] = useState<ServiceHealth[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string>('');

  // Load real performance data
  const loadPerformanceData = async () => {
    try {
      setIsLoading(true);

      // Get system metrics from our analytics API
      const systemMetrics = await analyticsClient.getSystemMetrics();

      setMetrics(systemMetrics.performance);
      setServiceHealth(systemMetrics.services);
      setLastUpdated(systemMetrics.timestamp);

    } catch (error) {
      console.error('Failed to load performance data:', error);

      // Show error state instead of fake data
      setMetrics(null);
      setServiceHealth([]);
      setLastUpdated(new Date().toISOString());
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPerformanceData();

    // Set up real-time updates if enabled
    let interval: NodeJS.Timeout;
    if (realTimeEnabled) {
      interval = setInterval(loadPerformanceData, 30000); // Update every 30 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [realTimeEnabled]);

  // Helper function to safely get the latest metric value
  const getLatestMetric = (data: any[]): number => {
    if (!data || data.length === 0) return 0;

    const lastItem = data[data.length - 1];

    // Handle both object with value property and direct number
    if (typeof lastItem === 'object' && lastItem !== null && 'value' in lastItem) {
      return typeof lastItem.value === 'number' ? lastItem.value : 0;
    }

    // Handle direct number
    if (typeof lastItem === 'number') {
      return lastItem;
    }

    return 0;
  };

  // Helper function to format metric values safely
  const formatMetric = (data: any[], decimals: number = 0): string => {
    const value = getLatestMetric(data);
    return decimals > 0 ? value.toFixed(decimals) : Math.round(value).toString();
  };

  // Helper function to safely get chart data
  const getChartData = (data: any[], fallbackLength: number = 12): any[] => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      // Return empty array with proper structure for charts
      return Array.from({ length: fallbackLength }, (_, i) => ({
        label: `${i}:00`,
        value: 0,
        timestamp: new Date().toISOString(),
      }));
    }
    return data.slice(-fallbackLength);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-admin-success';
      case 'warning':
        return 'text-admin-warning';
      case 'critical':
        return 'text-admin-danger';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <TrendingUp className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'critical':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  // Show loading state while data is being fetched
  if (isLoading && (!metrics.apiResponseTime || metrics.apiResponseTime.length === 0)) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-admin-accent mx-auto mb-4" />
            <p className="text-gray-400">Loading performance metrics...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Platform Performance</h2>
          <p className="text-gray-400 mt-1">
            Real-time monitoring and performance analytics
            {lastUpdated && (
              <span className="text-xs ml-2">
                • Last updated: {new Date(lastUpdated).toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className={`admin-button-secondary ${realTimeEnabled ? 'bg-admin-accent/20 border-admin-accent' : ''}`}
            onClick={() => setRealTimeEnabled(!realTimeEnabled)}
          >
            <Activity className={`h-4 w-4 mr-2 ${realTimeEnabled ? 'animate-pulse' : ''}`} />
            {realTimeEnabled ? 'Live' : 'Enable Live'}
          </Button>
          <Button
            variant="outline"
            className="admin-button-secondary"
            onClick={loadPerformanceData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Core Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="admin-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              API Response Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white mb-2">
              {formatMetric(metrics.apiResponseTime)}ms
            </div>
            <LineChart
              data={getChartData(metrics.apiResponseTime)}
              width={200}
              height={60}
              showGrid={false}
              animate={false}
            />
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400 flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              WebSocket Latency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white mb-2">
              {formatMetric(metrics.websocketLatency)}ms
            </div>
            <AreaChart
              data={getChartData(metrics.websocketLatency)}
              width={200}
              height={60}
              fillColor="rgba(16, 185, 129, 0.1)"
              strokeColor="#10b981"
              animate={false}
            />
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400 flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database Queries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white mb-2">
              {formatMetric(metrics.databaseQueries)}/sec
            </div>
            <BarChart
              data={metrics.databaseQueries.slice(-8).map((d, i) => ({ ...d, label: `${i+1}` }))}
              width={200}
              height={60}
              animate={false}
            />
          </CardContent>
        </Card>

        <Card className="admin-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Error Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white mb-2">
              {formatMetric(metrics.errorRate, 2)}%
            </div>
            <LineChart
              data={getChartData(metrics.errorRate)}
              width={200}
              height={60}
              showGrid={false}
              animate={false}
            />
          </CardContent>
        </Card>
      </div>

      {/* Detailed Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Trends */}
        <Card className="admin-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-admin-accent" />
              Response Time Trends (24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="text-sm text-gray-400 mb-2">API Response Time</div>
                <LineChart
                  data={metrics.apiResponseTime}
                  width={400}
                  height={120}
                  className="mb-4"
                />
              </div>
              <div>
                <div className="text-sm text-gray-400 mb-2">WebSocket Latency</div>
                <AreaChart
                  data={metrics.websocketLatency}
                  width={400}
                  height={100}
                  fillColor="rgba(16, 185, 129, 0.1)"
                  strokeColor="#10b981"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Resource Usage */}
        <Card className="admin-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5 text-admin-accent" />
              System Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Resource Gauges */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-sm text-gray-400 mb-2">CPU Usage</div>
                  <div className="relative w-16 h-16 mx-auto">
                    <DonutChart
                      data={[
                        { label: 'Used', value: getLatestMetric(metrics.cpuUsage), color: '#3b82f6' },
                        { label: 'Free', value: Math.max(0, 100 - getLatestMetric(metrics.cpuUsage)), color: '#374151' }
                      ]}
                      width={64}
                      height={64}
                      animate={false}
                    />
                  </div>
                  <div className="text-lg font-bold text-white mt-2">
                    {formatMetric(metrics.cpuUsage)}%
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-sm text-gray-400 mb-2">Memory</div>
                  <div className="relative w-16 h-16 mx-auto">
                    <DonutChart
                      data={[
                        { label: 'Used', value: getLatestMetric(metrics.memoryUsage), color: '#10b981' },
                        { label: 'Free', value: Math.max(0, 100 - getLatestMetric(metrics.memoryUsage)), color: '#374151' }
                      ]}
                      width={64}
                      height={64}
                      animate={false}
                    />
                  </div>
                  <div className="text-lg font-bold text-white mt-2">
                    {formatMetric(metrics.memoryUsage)}%
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-sm text-gray-400 mb-2">Disk</div>
                  <div className="relative w-16 h-16 mx-auto">
                    <DonutChart
                      data={[
                        { label: 'Used', value: getLatestMetric(metrics.diskUsage), color: '#f59e0b' },
                        { label: 'Free', value: Math.max(0, 100 - getLatestMetric(metrics.diskUsage)), color: '#374151' }
                      ]}
                      width={64}
                      height={64}
                      animate={false}
                    />
                  </div>
                  <div className="text-lg font-bold text-white mt-2">
                    {formatMetric(metrics.diskUsage)}%
                  </div>
                </div>
              </div>

              {/* Resource Trend */}
              <div>
                <div className="text-sm text-gray-400 mb-2">Resource Usage Trends</div>
                <LineChart
                  data={getChartData(metrics.cpuUsage)}
                  width={400}
                  height={80}
                  showGrid={false}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Health Dashboard */}
      <Card className="admin-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5 text-admin-accent" />
            Service Health Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {serviceHealth.map((service, index) => (
              <div
                key={index}
                className="p-4 rounded-lg bg-admin-secondary/50 border border-admin-secondary"
              >
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-white">{service.name}</h3>
                  <div className={`flex items-center gap-1 ${getStatusColor(service.status)}`}>
                    {getStatusIcon(service.status)}
                    <span className="text-sm capitalize">{service.status}</span>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Response Time:</span>
                    <span className="text-white">{Math.round(service.responseTime)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Uptime:</span>
                    <span className="text-white">{(service.uptime || 0).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Requests/min:</span>
                    <span className="text-white">{service.requests}</span>
                  </div>
                </div>
                
                {/* Mini health indicator */}
                <div className="mt-3 h-1 bg-admin-secondary rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-1000 ${
                      service.status === 'healthy' ? 'bg-admin-success' :
                      service.status === 'warning' ? 'bg-admin-warning' : 'bg-admin-danger'
                    }`}
                    style={{ width: `${service.uptime}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}