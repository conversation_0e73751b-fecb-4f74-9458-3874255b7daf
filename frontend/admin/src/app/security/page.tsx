/**
 * BetBet Admin - Security Monitoring
 * =================================
 * 
 * Security monitoring, audit logs, and threat detection.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  Shield,
  AlertTriangle,
  Eye,
  Lock,
  Activity,
  Users,
  Globe,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download
} from 'lucide-react';

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock security data
  const securityMetrics = {
    threatLevel: 'low',
    activeThreats: 2,
    blockedAttempts: 147,
    securityScore: 94,
    lastScan: '2 minutes ago'
  };

  const recentEvents = [
    {
      id: 1,
      type: 'login_attempt',
      severity: 'medium',
      description: 'Multiple failed login attempts from IP *************',
      timestamp: '2 minutes ago',
      status: 'blocked'
    },
    {
      id: 2,
      type: 'suspicious_activity',
      severity: 'low',
      description: 'Unusual betting pattern detected for user ID 12345',
      timestamp: '15 minutes ago',
      status: 'monitoring'
    },
    {
      id: 3,
      type: 'system_access',
      severity: 'high',
      description: 'Admin access from new device location',
      timestamp: '1 hour ago',
      status: 'verified'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-admin-danger/20 text-admin-danger';
      case 'medium':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'low':
        return 'bg-admin-info/20 text-admin-info';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'blocked':
        return 'bg-admin-danger/20 text-admin-danger';
      case 'monitoring':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'verified':
        return 'bg-admin-success/20 text-admin-success';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white">Security Monitoring</h1>
              <p className="text-gray-400 mt-2">
                Monitor security threats, audit logs, and system access
              </p>
            </div>
            <Button className="admin-button-primary">
              <Download className="h-4 w-4 mr-2" />
              Export Security Report
            </Button>
          </div>

          {/* Security Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="admin-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Security Score</p>
                    <p className="text-3xl font-bold text-admin-success">{securityMetrics.securityScore}%</p>
                  </div>
                  <Shield className="h-8 w-8 text-admin-success" />
                </div>
              </CardContent>
            </Card>

            <Card className="admin-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Active Threats</p>
                    <p className="text-3xl font-bold text-admin-warning">{securityMetrics.activeThreats}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-admin-warning" />
                </div>
              </CardContent>
            </Card>

            <Card className="admin-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Blocked Today</p>
                    <p className="text-3xl font-bold text-admin-danger">{securityMetrics.blockedAttempts}</p>
                  </div>
                  <Lock className="h-8 w-8 text-admin-danger" />
                </div>
              </CardContent>
            </Card>

            <Card className="admin-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Last Scan</p>
                    <p className="text-lg font-bold text-white">{securityMetrics.lastScan}</p>
                  </div>
                  <Activity className="h-8 w-8 text-admin-accent" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Security Events */}
          <Card className="admin-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-admin-accent" />
                Recent Security Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-4 bg-admin-secondary/50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(event.severity)}>
                          {event.severity}
                        </Badge>
                        <Badge className={getStatusColor(event.status)}>
                          {event.status}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-white font-medium">{event.description}</p>
                        <p className="text-sm text-gray-400 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {event.timestamp}
                        </p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" className="admin-button-secondary">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
