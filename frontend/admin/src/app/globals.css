@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 4.9%;
    --secondary: 217 32% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217 32% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 32% 17.5%;
    --input: 217 32% 17.5%;
    --ring: 217 91% 60%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Admin-specific styles */
@layer components {
  .admin-sidebar {
    @apply bg-admin-primary border-r border-admin-secondary;
  }
  
  .admin-header {
    @apply bg-admin-primary border-b border-admin-secondary;
  }
  
  .admin-card {
    @apply bg-card border border-admin-secondary rounded-lg p-6;
  }
  
  .admin-button-primary {
    @apply bg-admin-accent hover:bg-admin-accent/90 text-white;
  }
  
  .admin-button-secondary {
    @apply bg-admin-secondary hover:bg-admin-secondary/90 text-white;
  }
  
  .admin-input {
    @apply bg-admin-secondary border-admin-secondary text-white placeholder:text-gray-400;
  }
  
  .admin-table {
    @apply border border-admin-secondary;
  }
  
  .admin-table th {
    @apply bg-admin-secondary text-white border-b border-admin-secondary;
  }
  
  .admin-table td {
    @apply border-b border-admin-secondary/50;
  }
  
  .status-active {
    @apply bg-admin-success/20 text-admin-success border border-admin-success/30;
  }
  
  .status-inactive {
    @apply bg-admin-danger/20 text-admin-danger border border-admin-danger/30;
  }
  
  .status-pending {
    @apply bg-admin-warning/20 text-admin-warning border border-admin-warning/30;
  }
  
  .metric-card {
    @apply admin-card hover:shadow-lg transition-shadow duration-200;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-white;
  }
  
  .metric-label {
    @apply text-sm text-gray-400 uppercase tracking-wide;
  }
  
  .metric-trend-up {
    @apply text-admin-success;
  }
  
  .metric-trend-down {
    @apply text-admin-danger;
  }
  
  .admin-navigation {
    @apply flex items-center space-x-1 text-sm text-gray-400;
  }
  
  .admin-navigation a {
    @apply hover:text-white transition-colors duration-150;
  }
  
  .admin-navigation .separator {
    @apply text-gray-600 mx-2;
  }
}