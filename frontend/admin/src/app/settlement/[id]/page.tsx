/**
 * BetBet Admin - Market Settlement Page
 * ===================================
 * 
 * Administrative page for settling specific betting markets.
 * Migrated from main web app for proper admin separation.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import SettlementInterface from '@/components/betting/SettlementInterface';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, RefreshCw, AlertTriangle } from 'lucide-react';
import Link from 'next/link';

// Mock data - in real implementation, these would come from API calls
interface CustomBet {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'active' | 'settling' | 'settled' | 'cancelled' | 'suspended';
  creator_user_id: string;
  total_stakes: number;
  total_participants: number;
  created_at: string;
  deadline: string;
  verification_deadline: string;
  event_criteria: string;
  settlement_type?: string;
  winning_outcome_id?: string;
  outcomes: Array<{
    id: string;
    outcome_text: string;
    current_odds: number;
  }>;
}

interface BetParticipant {
  id: string;
  user_id: string;
  outcome_id: string;
  stake_amount: number;
  position_type: 'backing' | 'laying';
  desired_odds: number;
}

const mockMarkets: Record<string, CustomBet> = {
  '1': {
    id: '1',
    title: 'Bitcoin will reach $100k by end of 2024',
    description: 'Prediction market on Bitcoin price target',
    status: 'settling',
    creator_user_id: 'creator1',
    total_stakes: 25420.75,
    total_participants: 143,
    created_at: '2024-01-15T00:00:00Z',
    deadline: '2024-12-31T23:59:59Z',
    verification_deadline: '2025-01-07T23:59:59Z',
    event_criteria: 'Bitcoin (BTC) must reach or exceed $100,000 USD on any major exchange (Binance, Coinbase, Kraken) before 11:59 PM UTC on December 31, 2024. Price will be verified using CoinGecko API.',
    settlement_type: 'manual',
    outcomes: [
      { id: 'yes', outcome_text: 'Yes, BTC reaches $100k', current_odds: 2.5 },
      { id: 'no', outcome_text: 'No, BTC stays below $100k', current_odds: 1.8 }
    ]
  },
  '2': {
    id: '2',
    title: 'Tesla stock performance Q4 2024',
    description: 'Will TSLA outperform S&P 500 in Q4?',
    status: 'settling',
    creator_user_id: 'creator2',
    total_stakes: 18920.50,
    total_participants: 87,
    created_at: '2024-02-10T00:00:00Z',
    deadline: '2024-12-31T23:59:59Z',
    verification_deadline: '2024-12-20T23:59:59Z',
    event_criteria: 'Tesla (TSLA) stock must outperform the S&P 500 index during Q4 2024 (October 1 - December 31, 2024). Performance calculated as percentage change from market close on September 30, 2024 to market close on December 31, 2024.',
    settlement_type: 'manual',
    outcomes: [
      { id: 'outperform', outcome_text: 'TSLA outperforms S&P 500', current_odds: 1.9 },
      { id: 'underperform', outcome_text: 'TSLA underperforms S&P 500', current_odds: 2.1 }
    ]
  }
};

const mockParticipants: Record<string, BetParticipant[]> = {
  '1': [
    { id: '1', user_id: 'user1abc', outcome_id: 'yes', stake_amount: 1000, position_type: 'backing', desired_odds: 2.5 },
    { id: '2', user_id: 'user2def', outcome_id: 'no', stake_amount: 1500, position_type: 'backing', desired_odds: 1.8 },
    { id: '3', user_id: 'user3ghi', outcome_id: 'yes', stake_amount: 750, position_type: 'backing', desired_odds: 2.2 },
    { id: '4', user_id: 'user4jkl', outcome_id: 'no', stake_amount: 2000, position_type: 'backing', desired_odds: 1.7 }
  ],
  '2': [
    { id: '5', user_id: 'user5mno', outcome_id: 'outperform', stake_amount: 850, position_type: 'backing', desired_odds: 1.9 },
    { id: '6', user_id: 'user6pqr', outcome_id: 'underperform', stake_amount: 1200, position_type: 'backing', desired_odds: 2.1 }
  ]
};

export default function MarketSettlementPage() {
  const params = useParams();
  const marketId = params.id as string;
  
  const [market, setMarket] = useState<CustomBet | null>(null);
  const [participants, setParticipants] = useState<BetParticipant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMarketData();
  }, [marketId]);

  const loadMarketData = async () => {
    try {
      setLoading(true);
      setError(null);

      // In real implementation, these would be API calls
      // const marketData = await customBettingAPI.getBetById(marketId);
      // const participantsData = await customBettingAPI.getBetParticipants(marketId);

      // Mock data
      const marketData = mockMarkets[marketId];
      const participantsData = mockParticipants[marketId] || [];

      if (!marketData) {
        setError('Market not found');
        return;
      }

      setMarket(marketData);
      setParticipants(participantsData);

    } catch (err: any) {
      console.error('Failed to load market data:', err);
      setError(err.message || 'Failed to load market data');
    } finally {
      setLoading(false);
    }
  };

  const handleMarketSettled = () => {
    // Refresh market data after settlement
    loadMarketData();
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild className="admin-button-secondary">
              <Link href="/betting">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Markets
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-white">Market Settlement</h1>
              <p className="text-gray-400 mt-1">
                Settle market outcomes and distribute payouts
              </p>
            </div>
          </div>
          
          <Button 
            variant="outline" 
            onClick={loadMarketData}
            disabled={loading}
            className="admin-button-secondary"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Error State */}
        {error && (
          <Card className="admin-card border-admin-danger/50 bg-admin-danger/10">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-admin-danger mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">Error Loading Market</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={loadMarketData} className="admin-button-primary">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && (
          <Card className="admin-card">
            <CardContent className="p-6 text-center">
              <RefreshCw className="h-12 w-12 text-gray-400 mx-auto mb-3 animate-spin" />
              <h3 className="text-lg font-medium text-white mb-2">Loading Market Data</h3>
              <p className="text-gray-400">Please wait while we fetch the market details...</p>
            </CardContent>
          </Card>
        )}

        {/* Settlement Interface */}
        {market && !loading && !error && (
          <SettlementInterface
            market={market}
            participants={participants}
            canSettle={true} // In real implementation, check user permissions
            onSettled={handleMarketSettled}
          />
        )}
      </div>
    </AdminLayout>
  );
}