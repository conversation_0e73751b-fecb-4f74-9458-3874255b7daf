/**
 * BetBet Admin - Modules Management
 * ================================
 * 
 * Manage and monitor platform modules including gaming, betting, trading, and analytics.
 */

'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import ModuleOverview from '@/components/modules/ModuleOverview';

export default function ModulesPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white">Platform Modules</h1>
              <p className="text-gray-400 mt-2">
                Monitor and manage gaming, betting, trading, and analytics modules
              </p>
            </div>
          </div>
          
          <ModuleOverview />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
