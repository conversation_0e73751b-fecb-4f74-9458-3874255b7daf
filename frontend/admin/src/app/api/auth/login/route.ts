/**
 * Admin Authentication API Route
 * ==============================
 * 
 * Handles admin login authentication against the database.
 * Validates credentials and returns a session token.
 */

import { NextRequest, NextResponse } from 'next/server';

interface LoginRequest {
  email: string;
  password: string;
}

interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  roles: string[];
  permissions: string[];
  password_hash: string;
  is_active: boolean;
  is_verified: boolean;
}

// Simple session token for development (in production, use proper JWT)
const generateSessionToken = () => {
  return 'admin-session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
};
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://betbet-api-gateway:8000';

export async function POST(request: NextRequest) {
  try {
    const { email, password }: LoginRequest = await request.json();
    
    console.log('[Admin Auth] Login attempt for:', email);

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // For now, we'll connect directly to the database through a user service
    // In production, this would go through a proper authentication service
    
    // Try to authenticate through the gaming engine user endpoint
    try {
      const userResponse = await fetch(`${API_BASE_URL}/api/gaming/users/authenticate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        
        // Check if user has admin role
        if (!userData.roles || !userData.roles.includes('admin')) {
          return NextResponse.json(
            { error: 'Access denied. Admin privileges required.' },
            { status: 403 }
          );
        }

        // Generate session token
        const token = generateSessionToken();

        console.log('[Admin Auth] Login successful for:', email);

        return NextResponse.json({
          success: true,
          token,
          user: {
            id: userData.id,
            username: userData.username,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            roles: userData.roles,
            permissions: userData.permissions
          }
        });
      }
    } catch (serviceError) {
      console.log('[Admin Auth] Service authentication failed, trying direct database approach');
    }

    // Fallback: Direct database authentication for development
    // This is a simplified approach for demo purposes
    if (email === '<EMAIL>' && password === 'Pass1234') {
      const adminUser = {
        id: 'b0f9bda6-99a7-4287-ab80-f9af47c93547',
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'Super',
        last_name: 'Admin',
        roles: ['admin', 'super_admin', 'user'],
        permissions: ['create_markets', 'manage_users', 'settle_markets', 'platform_admin']
      };

      const token = generateSessionToken();

      console.log('[Admin Auth] Fallback login successful for:', email);

      return NextResponse.json({
        success: true,
        token,
        user: adminUser
      });
    }

    // Authentication failed
    console.log('[Admin Auth] Login failed for:', email);
    return NextResponse.json(
      { error: 'Invalid email or password' },
      { status: 401 }
    );

  } catch (error) {
    console.error('[Admin Auth] Login error:', error);
    return NextResponse.json(
      { 
        error: 'Authentication failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
