/**
 * Admin Logout API Route
 * ======================
 * 
 * Handles admin logout and session cleanup.
 */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('[Admin Auth] Logout request received');

    // In a production system, you would:
    // 1. Invalidate the JWT token in a blacklist
    // 2. Clear any server-side session data
    // 3. Log the logout event for security auditing

    // For now, we'll just return success
    // The client will handle clearing localStorage

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('[Admin Auth] Logout error:', error);
    return NextResponse.json(
      { 
        error: 'Logout failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
