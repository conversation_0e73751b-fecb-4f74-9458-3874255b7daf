/**
 * Test Platform Stats API Route
 * =============================
 * 
 * Test endpoint to debug platform statistics issues
 */

import { NextRequest, NextResponse } from 'next/server';
import adminAPI from '@/lib/api-client';

export async function GET(request: NextRequest) {
  try {
    console.log('[Test API] Testing platform stats...');
    
    const stats = await adminAPI.getPlatformStats();
    
    console.log('[Test API] Platform stats retrieved successfully:', stats);
    
    return NextResponse.json({
      success: true,
      data: stats,
      message: 'Platform stats retrieved successfully'
    });
    
  } catch (error) {
    console.error('[Test API] Platform stats error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 });
  }
}
