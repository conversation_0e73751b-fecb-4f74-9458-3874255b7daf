/**
 * BetBet Admin User Actions API
 * ============================
 * 
 * API endpoints for user moderation actions via Clerk
 */

import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const { userId, action } = await request.json();

    if (!userId || !action) {
      return NextResponse.json(
        { error: 'User ID and action are required' },
        { status: 400 }
      );
    }

    console.log(`[Admin API] Executing action '${action}' on user ${userId}`);

    let result;
    
    const clerk = await clerkClient();
    
    switch (action) {
      case 'suspend':
        // Lock the user in Clerk
        result = await clerk.users.lockUser(userId);
        console.log(`[Admin API] User ${userId} suspended successfully`);
        break;

      case 'activate':
        // Unlock the user in Clerk
        result = await clerk.users.unlockUser(userId);
        console.log(`[Admin API] User ${userId} activated successfully`);
        break;

      case 'ban':
        // Ban the user in Clerk
        result = await clerk.users.banUser(userId);
        console.log(`[Admin API] User ${userId} banned successfully`);
        break;

      case 'verify':
        // Mark user as verified (if using custom verification)
        // For now, we'll just update the user's metadata
        result = await clerk.users.updateUserMetadata(userId, {
          publicMetadata: {
            verified: true,
            verifiedAt: new Date().toISOString()
          }
        });
        console.log(`[Admin API] User ${userId} verified successfully`);
        break;

      default:
        return NextResponse.json(
          { error: `Invalid action: ${action}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      action,
      userId,
      result: {
        id: result.id,
        status: result.banned ? 'banned' : result.locked ? 'suspended' : 'active'
      }
    });

  } catch (error) {
    console.error('[Admin API] Error executing user action:', error);
    
    return NextResponse.json({
      success: false,
      error: process.env.NODE_ENV === 'development' ? error.message : 'Failed to execute action'
    }, { status: 500 });
  }
}