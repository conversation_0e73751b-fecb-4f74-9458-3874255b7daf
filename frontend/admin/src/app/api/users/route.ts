/**
 * BetBet Admin User Management API
 * ===============================
 * 
 * API endpoints for user management with Clerk integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

interface ClerkUser {
  id: string;
  emailAddresses: Array<{ emailAddress: string }>;
  firstName?: string;
  lastName?: string;
  username?: string;
  imageUrl?: string;
  createdAt: number;
  lastSignInAt?: number;
  banned?: boolean;
  locked?: boolean;
}

interface UserStats {
  total_users: number;
  active_users: number;
  new_users_today: number;
  verified_users: number;
  suspended_users: number;
  banned_users: number;
}

// Convert Clerk user to our User interface
function convertClerkUser(clerkUser: ClerkUser) {
  const status = clerkUser.banned ? 'banned' : 
                 clerkUser.locked ? 'suspended' : 'active';
  
  return {
    id: clerkUser.id,
    clerk_id: clerkUser.id,
    email: clerkUser.emailAddresses[0]?.emailAddress || '',
    first_name: clerkUser.firstName || '',
    last_name: clerkUser.lastName || '',
    username: clerkUser.username || '',
    avatar_url: clerkUser.imageUrl || '',
    status,
    role: 'user', // Default role, could be enhanced
    created_at: new Date(clerkUser.createdAt).toISOString(),
    last_login: clerkUser.lastSignInAt ? new Date(clerkUser.lastSignInAt).toISOString() : undefined,
    total_bets: 0, // Would come from betting service
    total_volume: 0, // Would come from betting service
    verification_status: 'verified' // Clerk handles verification
  };
}

export async function GET(request: NextRequest) {
  try {
    console.log('[Admin API] Fetching users from Clerk...');
    
    // Get all users from Clerk
    const clerk = await clerkClient();
    const clerkUsers = await clerk.users.getUserList({
      limit: 100, // Adjust as needed
      orderBy: '-created_at'
    });

    console.log(`[Admin API] Found ${clerkUsers.totalCount} users in Clerk`);

    // Convert Clerk users to our format
    const users = clerkUsers.data.map(convertClerkUser);

    // Calculate stats
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const stats: UserStats = {
      total_users: clerkUsers.totalCount || 0,
      active_users: users.filter(u => u.status === 'active').length,
      new_users_today: users.filter(u => new Date(u.created_at) >= today).length,
      verified_users: users.filter(u => u.verification_status === 'verified').length,
      suspended_users: users.filter(u => u.status === 'suspended').length,
      banned_users: users.filter(u => u.status === 'banned').length
    };

    console.log('[Admin API] User stats:', stats);

    return NextResponse.json({
      users,
      stats,
      total: clerkUsers.totalCount
    });

  } catch (error) {
    console.error('[Admin API] Error fetching users:', error);
    
    // Return empty state with error info for development
    return NextResponse.json({
      users: [],
      stats: {
        total_users: 0,
        active_users: 0,
        new_users_today: 0,
        verified_users: 0,
        suspended_users: 0,
        banned_users: 0
      },
      total: 0,
      error: process.env.NODE_ENV === 'development' ? error.message : 'Failed to fetch users'
    }, { status: 500 });
  }
}