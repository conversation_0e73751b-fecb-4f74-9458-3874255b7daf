/**
 * Admin Market Actions API Routes
 * ===============================
 * 
 * Server-side API routes for performing administrative actions on betting markets.
 * Includes suspend, cancel, settle, and feature operations.
 */

import { NextRequest, NextResponse } from 'next/server';

interface MarketActionRequest {
  action: 'suspend' | 'cancel' | 'settle' | 'feature' | 'unfeature';
  winning_outcome_id?: string;
  resolution_notes?: string;
  featured?: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://betbet-api-gateway:8000';

export async function POST(
  request: NextRequest,
  { params }: { params: { marketId: string } }
) {
  try {
    const { marketId } = params;
    const actionData: MarketActionRequest = await request.json();
    
    console.log(`[Admin API] Performing action ${actionData.action} on market ${marketId}`);

    // Default admin user ID (in production, this would come from Clerk authentication)
    // Using a consistent UUID for the admin user in development
    const adminUserId = '550e8400-e29b-41d4-a716-************';

    let response: Response;

    switch (actionData.action) {
      case 'suspend':
        // For now, we'll update the market status directly
        // In a full implementation, there would be a dedicated suspend endpoint
        response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'suspended',
            updated_by: adminUserId
          }),
        });
        break;

      case 'cancel':
        // Similar to suspend, update status to cancelled
        response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'cancelled',
            updated_by: adminUserId
          }),
        });
        break;

      case 'settle':
        if (!actionData.winning_outcome_id) {
          return NextResponse.json(
            { error: 'winning_outcome_id is required for settlement' },
            { status: 400 }
          );
        }

        response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}/settle?admin_user_id=${adminUserId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            winning_outcome_id: actionData.winning_outcome_id,
            resolution_notes: actionData.resolution_notes
          }),
        });
        break;

      case 'feature':
      case 'unfeature':
        const featured = actionData.action === 'feature';
        response = await fetch(`${API_BASE_URL}/api/custom-betting/admin/markets/${marketId}/feature?featured=${featured}&admin_user_id=${adminUserId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${actionData.action}` },
          { status: 400 }
        );
    }

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`[Admin API] Failed to ${actionData.action} market:`, response.status, errorData);
      throw new Error(`Failed to ${actionData.action} market: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[Admin API] Successfully performed ${actionData.action} on market ${marketId}`);

    return NextResponse.json({
      success: true,
      action: actionData.action,
      market_id: marketId,
      result
    });

  } catch (error) {
    console.error('[Admin API] Error performing market action:', error);
    return NextResponse.json(
      { 
        error: 'Failed to perform market action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { marketId: string } }
) {
  try {
    const { marketId } = params;
    
    console.log(`[Admin API] Fetching market details for ${marketId}`);

    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('[Admin API] Failed to fetch market details:', response.status, response.statusText);
      throw new Error(`Failed to fetch market: ${response.status} ${response.statusText}`);
    }

    const market = await response.json();
    console.log(`[Admin API] Successfully fetched market ${marketId}`);

    return NextResponse.json(market);

  } catch (error) {
    console.error('[Admin API] Error fetching market details:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch market details',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
