/**
 * Admin Market Details API Route
 * =============================
 * 
 * Server-side API route for fetching detailed information about a specific betting market.
 * Used in the admin dashboard for market management.
 */

import { NextRequest, NextResponse } from 'next/server';

interface CustomBetMarket {
  id: string;
  title: string;
  description: string;
  slug: string;
  market_type: string;
  creator_type: string;
  creator_user_id: string;
  minimum_stake: number;
  maximum_stake?: number;
  total_volume: number;
  total_participants: number;
  status: string;
  closes_at?: string;
  resolves_at?: string;
  resolution_criteria: string;
  is_featured: boolean;
  tags?: string[];
  created_at: string;
  updated_at: string;
  outcomes: Array<{
    id: string;
    title: string;
    description?: string;
    total_stakes: number;
    total_bets: number;
    implied_probability: number;
    is_active: boolean;
    is_winning: boolean;
    created_at: string;
  }>;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { marketId: string } }
) {
  try {
    const { marketId } = params;
    
    console.log(`[Admin API] Fetching market details for ${marketId}`);
    
    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('[Admin API] Failed to fetch market details:', response.status, response.statusText);
      throw new Error(`Failed to fetch market: ${response.status} ${response.statusText}`);
    }

    const market: CustomBetMarket = await response.json();
    console.log(`[Admin API] Successfully fetched market ${marketId}`);
    
    return NextResponse.json(market);
    
  } catch (error) {
    console.error('[Admin API] Error fetching market details:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch market details',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { marketId: string } }
) {
  try {
    const { marketId } = params;
    const updateData = await request.json();
    
    console.log(`[Admin API] Updating market ${marketId}`, updateData);
    
    // Default admin user ID (in production, this would come from authentication)
    const adminUserId = 'admin-user-123';
    
    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...updateData,
        updated_by: adminUserId
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[Admin API] Failed to update market:', response.status, errorData);
      throw new Error(`Failed to update market: ${response.status} ${response.statusText}`);
    }

    const updatedMarket = await response.json();
    console.log(`[Admin API] Successfully updated market ${marketId}`);
    
    return NextResponse.json(updatedMarket);
    
  } catch (error) {
    console.error('[Admin API] Error updating market:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update market',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { marketId: string } }
) {
  try {
    const { marketId } = params;
    
    console.log(`[Admin API] Deleting market ${marketId}`);
    
    // Default admin user ID (in production, this would come from authentication)
    const adminUserId = 'admin-user-123';
    
    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets/${marketId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        deleted_by: adminUserId
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[Admin API] Failed to delete market:', response.status, errorData);
      throw new Error(`Failed to delete market: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[Admin API] Successfully deleted market ${marketId}`);
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('[Admin API] Error deleting market:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete market',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}