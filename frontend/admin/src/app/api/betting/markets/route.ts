/**
 * Admin Betting Markets API Routes
 * ================================
 * 
 * Server-side API routes for managing custom betting markets in the admin dashboard.
 * Provides CRUD operations and admin-specific functionality.
 */

import { NextRequest, NextResponse } from 'next/server';

interface CustomBetMarket {
  id: string;
  title: string;
  description: string;
  slug: string;
  market_type: string;
  creator_type: string;
  creator_user_id: string;
  minimum_stake: number;
  maximum_stake?: number;
  total_volume: number;
  total_participants: number;
  status: string;
  closes_at?: string;
  resolves_at?: string;
  resolution_criteria: string;
  is_featured: boolean;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

interface MarketCreateRequest {
  title: string;
  description: string;
  category_id?: string;
  market_type?: string;
  minimum_stake?: number;
  maximum_stake?: number;
  closes_at?: string;
  resolves_at?: string;
  resolution_source?: string;
  resolution_criteria: string;
  outcomes: Array<{
    title: string;
    description?: string;
  }>;
  tags?: string[];
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://betbet-api-gateway:8000';
console.log('[Admin API] Environment check:', {
  NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
  API_BASE_URL,
  NODE_ENV: process.env.NODE_ENV
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';
    const status = searchParams.get('status');
    const category_id = searchParams.get('category_id');
    const creator_type = searchParams.get('creator_type');
    const featured_only = searchParams.get('featured_only') || 'false';

    // Build query parameters
    const params = new URLSearchParams({
      page,
      limit,
      featured_only
    });

    if (status) params.append('status', status);
    if (category_id) params.append('category_id', category_id);
    if (creator_type) params.append('creator_type', creator_type);

    console.log('[Admin API] Fetching markets from custom betting service...');
    console.log('[Admin API] Request URL:', `${API_BASE_URL}/api/custom-betting/markets?${params}`);

    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('[Admin API] Failed to fetch markets:', response.status, response.statusText);
      throw new Error(`Failed to fetch markets: ${response.status} ${response.statusText}`);
    }

    console.log('[Admin API] Response status:', response.status);
    const markets: CustomBetMarket[] = await response.json();
    console.log(`[Admin API] Successfully fetched ${markets.length} markets`);

    // Transform the data to match the admin dashboard format
    const transformedMarkets = markets.map(market => ({
      id: market.id,
      title: market.title,
      description: market.description,
      status: market.status,
      creator_user_id: market.creator_user_id,
      total_stakes: market.total_volume,
      total_participants: market.total_participants,
      created_at: market.created_at,
      deadline: market.closes_at || market.resolves_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      verification_deadline: market.resolves_at || new Date(Date.now() + 37 * 24 * 60 * 60 * 1000).toISOString(),
      market_type: market.market_type,
      minimum_stake: market.minimum_stake,
      maximum_stake: market.maximum_stake,
      is_featured: market.is_featured,
      tags: market.tags
    }));

    return NextResponse.json(transformedMarkets);

  } catch (error) {
    console.error('[Admin API] Error fetching markets:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch markets',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const marketData: MarketCreateRequest = await request.json();
    
    console.log('[Admin API] Creating new market:', marketData.title);

    // Validate required fields
    if (!marketData.title || !marketData.description || !marketData.resolution_criteria) {
      return NextResponse.json(
        { error: 'Missing required fields: title, description, resolution_criteria' },
        { status: 400 }
      );
    }

    // Real admin user ID from database (<EMAIL>)
    // In production, this would come from authentication context
    const adminUserId = 'b0f9bda6-99a7-4287-ab80-f9af47c93547';

    const response = await fetch(`${API_BASE_URL}/api/custom-betting/markets?creator_user_id=${adminUserId}&creator_type=admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(marketData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[Admin API] Failed to create market:', response.status, errorData);
      throw new Error(`Failed to create market: ${response.status} ${response.statusText}`);
    }

    const newMarket = await response.json();
    console.log('[Admin API] Successfully created market:', newMarket.id);

    return NextResponse.json(newMarket, { status: 201 });

  } catch (error) {
    console.error('[Admin API] Error creating market:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create market',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
