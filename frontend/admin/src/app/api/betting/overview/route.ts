/**
 * Admin Betting Overview API Route
 * ================================
 * 
 * Server-side API route for fetching betting platform overview statistics.
 * Provides aggregated data for the admin dashboard.
 */

import { NextRequest, NextResponse } from 'next/server';

interface AdminOverview {
  market_stats: {
    total_markets: number;
    active_markets: number;
    disputed_markets: number;
    total_volume: number;
  };
  position_stats: {
    total_positions: number;
    average_position_size: number;
  };
  dispute_stats: {
    open_disputes: number;
    dispute_rate: number;
  };
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export async function GET(request: NextRequest) {
  try {
    console.log('[Admin API] Fetching betting overview statistics...');

    // Real admin user ID from database (<EMAIL>)
    // In production, this would come from authentication context
    const adminUserId = 'b0f9bda6-99a7-4287-ab80-f9af47c93547';

    const response = await fetch(`${API_BASE_URL}/api/custom-betting/admin/overview?admin_user_id=${adminUserId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('[Admin API] Failed to fetch overview:', response.status, response.statusText);
      
      // If the admin endpoint fails, try to get basic analytics
      try {
        const analyticsResponse = await fetch(`${API_BASE_URL}/api/custom-betting/analytics/overview`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (analyticsResponse.ok) {
          const analyticsData = await analyticsResponse.json();
          console.log('[Admin API] Using analytics data as fallback');
          
          // Transform analytics data to admin overview format
          const fallbackOverview = {
            market_stats: {
              total_markets: analyticsData.active_markets || 0,
              active_markets: analyticsData.active_markets || 0,
              disputed_markets: 0,
              total_volume: analyticsData.total_volume || 0
            },
            position_stats: {
              total_positions: analyticsData.total_participants || 0,
              average_position_size: analyticsData.total_participants > 0 
                ? (analyticsData.total_volume || 0) / analyticsData.total_participants 
                : 0
            },
            dispute_stats: {
              open_disputes: 0,
              dispute_rate: 0
            }
          };

          return NextResponse.json(fallbackOverview);
        }
      } catch (fallbackError) {
        console.error('[Admin API] Fallback analytics also failed:', fallbackError);
      }

      throw new Error(`Failed to fetch overview: ${response.status} ${response.statusText}`);
    }

    const overview: AdminOverview = await response.json();
    console.log('[Admin API] Successfully fetched overview statistics');

    return NextResponse.json(overview);

  } catch (error) {
    console.error('[Admin API] Error fetching overview:', error);
    
    // Return empty stats as final fallback
    const emptyOverview = {
      market_stats: {
        total_markets: 0,
        active_markets: 0,
        disputed_markets: 0,
        total_volume: 0
      },
      position_stats: {
        total_positions: 0,
        average_position_size: 0
      },
      dispute_stats: {
        open_disputes: 0,
        dispute_rate: 0
      }
    };

    return NextResponse.json(emptyOverview);
  }
}
