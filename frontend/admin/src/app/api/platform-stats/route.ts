/**
 * Platform Statistics API Route
 * ==============================
 * 
 * Server-side API route for fetching platform-wide statistics.
 * Aggregates data from multiple services with graceful error handling.
 */

import { NextRequest, NextResponse } from 'next/server';

interface PlatformStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  systemUptime: number;
  apiResponseTime: number;
  serviceErrors?: string[];
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://betbet-api-gateway:8000';

export async function GET(request: NextRequest) {
  try {
    console.log('[Platform Stats] Fetching platform statistics...');

    const serviceErrors: string[] = [];
    let totalUsers = 0;
    let activeUsers = 0;
    let totalRevenue = 0;
    let systemUptime = 100; // Default uptime percentage
    let apiResponseTime = 0;

    // Try to get real data from each service with individual error handling
    const services = [
      { name: 'Gaming', endpoint: '/api/gaming/analytics/overview' },
      { name: 'Custom Betting', endpoint: '/api/custom-betting/analytics/overview' },
      { name: 'Wallet', endpoint: '/api/wallet/analytics/overview' },
      { name: 'Trading', endpoint: '/api/trading/analytics/overview' },
      { name: 'Experts', endpoint: '/api/experts/analytics/platform' },
      { name: 'Sports', endpoint: '/api/sports/analytics/overview' },
      { name: 'Leaderboards', endpoint: '/api/leaderboards/analytics/overview' }
    ];

    const servicePromises = services.map(async (service) => {
      try {
        const startTime = Date.now();
        const response = await fetch(`${API_BASE_URL}${service.endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: AbortSignal.timeout(5000), // 5 second timeout per service
        });

        const responseTime = Date.now() - startTime;
        apiResponseTime += responseTime;

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`[Platform Stats] ${service.name} service responded in ${responseTime}ms`);

        return {
          service: service.name,
          data,
          responseTime,
          success: true
        };
      } catch (error) {
        console.error(`[Platform Stats] ${service.name} service failed:`, error);
        serviceErrors.push(`${service.name} service unavailable`);
        return {
          service: service.name,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        };
      }
    });

    const results = await Promise.allSettled(servicePromises);
    let successfulServices = 0;

    // Process results from each service
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value.success) {
        const { service, data } = result.value;
        successfulServices++;

        // Aggregate data based on service type
        switch (service) {
          case 'Gaming':
            totalUsers = Math.max(totalUsers, data.total_users || 0);
            activeUsers += data.active_sessions || 0;
            totalRevenue += data.total_betting_volume || 0;
            break;

          case 'Custom Betting':
            totalUsers = Math.max(totalUsers, data.total_participants || 0);
            activeUsers += data.active_markets || 0;
            totalRevenue += data.total_volume || 0;
            break;

          case 'Wallet':
            totalRevenue += data.total_transactions_volume || 0;
            break;

          case 'Trading':
            totalUsers = Math.max(totalUsers, data.total_traders || 0);
            totalRevenue += data.total_trading_volume || 0;
            break;

          case 'Experts':
            totalUsers = Math.max(totalUsers, data.total_experts || 0);
            totalRevenue += data.total_expert_revenue || 0;
            break;

          case 'Sports':
            totalRevenue += data.total_sports_betting_volume || 0;
            break;

          case 'Leaderboards':
            // Leaderboards typically don't contribute to revenue
            break;
        }
      }
    }

    // Calculate average API response time
    if (successfulServices > 0) {
      apiResponseTime = Math.round(apiResponseTime / successfulServices);
    }

    // Calculate system uptime based on service availability
    systemUptime = Math.round((successfulServices / services.length) * 100);

    const stats: PlatformStats = {
      totalUsers,
      activeUsers,
      totalRevenue,
      systemUptime,
      apiResponseTime,
      serviceErrors: serviceErrors.length > 0 ? serviceErrors : undefined
    };

    console.log('[Platform Stats] Aggregated statistics:', {
      totalUsers,
      activeUsers,
      totalRevenue,
      systemUptime,
      apiResponseTime,
      serviceErrorCount: serviceErrors.length
    });

    return NextResponse.json(stats);

  } catch (error) {
    console.error('[Platform Stats] Error fetching platform statistics:', error);
    
    // Return default stats with error information
    const fallbackStats: PlatformStats = {
      totalUsers: 0,
      activeUsers: 0,
      totalRevenue: 0,
      systemUptime: 0,
      apiResponseTime: 0,
      serviceErrors: ['Platform statistics service unavailable']
    };

    return NextResponse.json(fallbackStats);
  }
}
