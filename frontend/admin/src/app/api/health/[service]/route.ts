import { NextRequest, NextResponse } from 'next/server';

// Service port mapping
const SERVICE_PORTS: Record<string, number> = {
  'gaming': 8001,
  'custom-betting': 8002,
  'trading': 8003,
  'experts': 8004,
  'sports': 8005,
  'leaderboards': 8006,
  'wallet': 8007,
  'websocket': 8008,
};

// Service name mapping for display
const SERVICE_NAMES: Record<string, string> = {
  'gaming': 'Gaming Engine',
  'custom-betting': 'Custom Betting',
  'trading': 'Trading Engine',
  'experts': 'Expert Analysis',
  'sports': 'Sports Analysis',
  'leaderboards': 'Leaderboards',
  'wallet': 'Wallet Service',
  'websocket': 'WebSocket Manager',
};

// Docker container name mapping
const SERVICE_HOSTS: Record<string, string> = {
  'gaming': 'gaming-engine',
  'custom-betting': 'custom-betting',
  'trading': 'odds-exchange',
  'experts': 'expert-analysis',
  'sports': 'sports-analysis',
  'leaderboards': 'leaderboards-service',
  'wallet': 'wallet-service',
  'websocket': 'websocket-manager',
};

function getServiceHost(service: string): string {
  return SERVICE_HOSTS[service] || service;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { service: string } }
) {
  try {
    const { service } = params;
    
    // Validate service name
    if (!SERVICE_PORTS[service]) {
      return NextResponse.json(
        { 
          error: 'Invalid service name',
          available_services: Object.keys(SERVICE_PORTS)
        },
        { status: 400 }
      );
    }

    const port = SERVICE_PORTS[service];
    const serviceName = SERVICE_NAMES[service];
    const startTime = Date.now();

    try {
      // Try to reach the service health endpoint using Docker network names
      const serviceHost = getServiceHost(service);
      const response = await fetch(`http://${serviceHost}:${port}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const healthData = await response.json();
        
        return NextResponse.json({
          name: serviceName,
          status: 'healthy',
          port: port,
          responseTime: responseTime,
          details: healthData,
          timestamp: new Date().toISOString(),
        });
      } else {
        return NextResponse.json({
          name: serviceName,
          status: 'unhealthy',
          port: port,
          responseTime: responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      return NextResponse.json({
        name: serviceName,
        status: 'unavailable',
        port: port,
        responseTime: responseTime,
        error: error.message || 'Service unreachable',
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error: any) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { 
        error: 'Health check failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Also support POST for consistency
export async function POST(
  request: NextRequest,
  { params }: { params: { service: string } }
) {
  return GET(request, { params });
}
