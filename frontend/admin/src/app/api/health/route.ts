/**
 * Health Check API Route - Admin Dashboard
 * ========================================
 * 
 * Provides health status for the admin dashboard and backend connectivity.
 * Used by monitoring systems to verify service health.
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Check backend connectivity
    // Use internal Docker service name when running in container
    const backendUrl = process.env.DOCKER_ENV ? 'http://api-gateway:8000' : 
                      (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000');
    let backendHealthy = false;
    let backendResponseTime = null;
    let backendError = null;
    
    try {
      const backendStart = Date.now();
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 5 second timeout for health check
        signal: AbortSignal.timeout(5000),
      });
      
      backendResponseTime = Date.now() - backendStart;
      backendHealthy = response.ok;
      
      if (!response.ok) {
        backendError = `Backend returned ${response.status}: ${response.statusText}`;
      }
    } catch (error: any) {
      backendError = error.message || 'Backend connection failed';
    }
    
    const responseTime = Date.now() - startTime;
    
    const healthData = {
      status: backendHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      frontend: {
        status: 'healthy',
        version: process.env.NEXT_PUBLIC_BUILD_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        responseTime: `${responseTime}ms`
      },
      backend: {
        status: backendHealthy ? 'healthy' : 'unhealthy',
        url: backendUrl,
        responseTime: backendResponseTime ? `${backendResponseTime}ms` : null,
        error: backendError
      },
      dependencies: {
        websocket: {
          url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080',
          status: 'unknown' // WebSocket health check would require client-side testing
        }
      },
      admin: {
        environment: process.env.NODE_ENV || 'development',
        publicDomain: process.env.NEXT_PUBLIC_APP_URL || null,
        staticUrl: process.env.NEXT_PUBLIC_STATIC_URL || null
      }
    };
    
    return NextResponse.json(healthData, { 
      status: backendHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error: any) {
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message || 'Health check failed',
      frontend: {
        status: 'error',
        environment: process.env.NODE_ENV || 'development'
      }
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}
