import { NextRequest, NextResponse } from 'next/server';

// Service configuration with Docker container names
const SERVICES = [
  { key: 'gaming', name: 'Gaming Engine', port: 8001, host: 'gaming-engine' },
  { key: 'custom-betting', name: 'Custom Betting', port: 8002, host: 'custom-betting' },
  { key: 'trading', name: 'Trading Engine', port: 8003, host: 'odds-exchange' },
  { key: 'experts', name: 'Expert Analysis', port: 8004, host: 'expert-analysis' },
  { key: 'sports', name: 'Sports Analysis', port: 8005, host: 'sports-analysis' },
  { key: 'leaderboards', name: 'Leaderboards', port: 8006, host: 'leaderboards-service' },
  { key: 'wallet', name: 'Wallet Service', port: 8007, host: 'wallet-service' },
  { key: 'websocket', name: 'WebSocket Manager', port: 8008, host: 'websocket-manager' },
];

async function checkServiceHealth(service: { key: string; name: string; port: number; host: string }) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`http://${service.host}:${service.port}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      const healthData = await response.json();
      
      return {
        name: service.name,
        key: service.key,
        status: 'healthy',
        port: service.port,
        responseTime: responseTime,
        details: healthData,
        timestamp: new Date().toISOString(),
      };
    } else {
      return {
        name: service.name,
        key: service.key,
        status: 'unhealthy',
        port: service.port,
        responseTime: responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`,
        timestamp: new Date().toISOString(),
      };
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    return {
      name: service.name,
      key: service.key,
      status: 'unavailable',
      port: service.port,
      responseTime: responseTime,
      error: error.message || 'Service unreachable',
      timestamp: new Date().toISOString(),
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('Checking health of all services...');
    
    // Check all services in parallel
    const healthChecks = await Promise.allSettled(
      SERVICES.map(service => checkServiceHealth(service))
    );

    // Process results
    const results = healthChecks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const service = SERVICES[index];
        return {
          name: service.name,
          key: service.key,
          status: 'error',
          port: service.port,
          responseTime: 0,
          error: result.reason?.message || 'Unknown error',
          timestamp: new Date().toISOString(),
        };
      }
    });

    // Calculate summary statistics
    const healthyServices = results.filter(r => r.status === 'healthy');
    const unhealthyServices = results.filter(r => r.status === 'unhealthy');
    const unavailableServices = results.filter(r => r.status === 'unavailable');
    const errorServices = results.filter(r => r.status === 'error');

    const summary = {
      total: results.length,
      healthy: healthyServices.length,
      unhealthy: unhealthyServices.length,
      unavailable: unavailableServices.length,
      errors: errorServices.length,
      overall_status: healthyServices.length === results.length ? 'healthy' : 
                     healthyServices.length > results.length / 2 ? 'degraded' : 'critical',
      average_response_time: results.reduce((sum, r) => sum + r.responseTime, 0) / results.length,
    };

    console.log(`Health check complete: ${healthyServices.length}/${results.length} services healthy`);

    return NextResponse.json({
      summary,
      services: results,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { 
        error: 'Health check failed',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Also support POST for consistency
export async function POST(request: NextRequest) {
  return GET(request);
}
