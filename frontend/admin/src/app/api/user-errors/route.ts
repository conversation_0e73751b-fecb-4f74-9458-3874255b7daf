/**
 * User Errors API Route
 * =====================
 * 
 * Tracks and retrieves user-facing errors for customer support.
 * Aggregates errors from all services and provides filtering capabilities.
 */

import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

interface UserError {
  id: string;
  userId?: string;
  userEmail?: string;
  service: string;
  errorType: string;
  errorMessage: string;
  stackTrace?: string;
  userAgent?: string;
  url?: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'new' | 'investigating' | 'resolved' | 'ignored';
  tags: string[];
}

interface UserErrorsResponse {
  errors: UserError[];
  totalCount: number;
  summary: {
    newErrors: number;
    criticalErrors: number;
    errorsByService: Record<string, number>;
    errorsByType: Record<string, number>;
  };
  timestamp: string;
}

// REMOVED: Mock user errors generation
// This was dangerous fake data that masked the need for real error logging
function getRealUserErrors(): UserError[] {
  // TODO: Implement real error logging integration
  // Return empty array - error logging system not implemented yet
  // This shows the real state: no error logging infrastructure exists
  return [];
}

function getErrorMessage(errorType: string, service: string): string {
  const messages: Record<string, string> = {
    'Authentication Failed': 'User authentication failed during login attempt',
    'Payment Processing Error': 'Credit card transaction was declined by payment processor',
    'Market Access Denied': 'User does not have permission to access this betting market',
    'Insufficient Balance': 'User attempted to place bet with insufficient account balance',
    'Network Timeout': 'Request timed out while connecting to external service',
    'Validation Error': 'Invalid input data provided by user',
    'Server Error': `Internal server error in ${service} service`,
    'Rate Limit Exceeded': 'User exceeded maximum allowed requests per minute',
  };
  
  return messages[errorType] || 'Unknown error occurred';
}

function getSeverity(errorType: string): 'low' | 'medium' | 'high' | 'critical' {
  const severityMap: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
    'Authentication Failed': 'medium',
    'Payment Processing Error': 'high',
    'Market Access Denied': 'medium',
    'Insufficient Balance': 'low',
    'Network Timeout': 'medium',
    'Validation Error': 'low',
    'Server Error': 'critical',
    'Rate Limit Exceeded': 'low',
  };
  
  return severityMap[errorType] || 'medium';
}

function getStatus(): 'new' | 'investigating' | 'resolved' | 'ignored' {
  const statuses: ('new' | 'investigating' | 'resolved' | 'ignored')[] = ['new', 'investigating', 'resolved', 'ignored'];
  const weights = [0.4, 0.2, 0.3, 0.1]; // 40% new, 20% investigating, 30% resolved, 10% ignored
  
  const random = Math.random();
  let cumulative = 0;
  
  for (let i = 0; i < weights.length; i++) {
    cumulative += weights[i];
    if (random <= cumulative) {
      return statuses[i];
    }
  }
  
  return 'new';
}

function getTags(errorType: string, service: string): string[] {
  const baseTags = [service];
  
  if (errorType.includes('Payment')) baseTags.push('payment', 'financial');
  if (errorType.includes('Authentication')) baseTags.push('auth', 'security');
  if (errorType.includes('Network')) baseTags.push('network', 'connectivity');
  if (errorType.includes('Server')) baseTags.push('backend', 'infrastructure');
  if (errorType.includes('Validation')) baseTags.push('frontend', 'user-input');
  
  return baseTags;
}

function generateStackTrace(service: string): string {
  return `
Error: ${service} service error
    at processRequest (/${service}/src/handlers/request.js:45:12)
    at async handleUserAction (/${service}/src/controllers/user.js:123:5)
    at async Router.handle (/${service}/src/routes/api.js:67:3)
    at async Server.handleRequest (/${service}/src/server.js:89:7)
  `.trim();
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const service = searchParams.get('service');
    const severity = searchParams.get('severity');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get user errors (real implementation needed - no mock data)
    let errors = getRealUserErrors();
    
    // Apply filters
    if (service) {
      errors = errors.filter(error => error.service === service);
    }
    
    if (severity) {
      errors = errors.filter(error => error.severity === severity);
    }
    
    if (status) {
      errors = errors.filter(error => error.status === status);
    }
    
    // Apply pagination
    const totalCount = errors.length;
    const paginatedErrors = errors.slice(offset, offset + limit);
    
    // Generate summary statistics
    const summary = {
      newErrors: errors.filter(e => e.status === 'new').length,
      criticalErrors: errors.filter(e => e.severity === 'critical').length,
      errorsByService: errors.reduce((acc, error) => {
        acc[error.service] = (acc[error.service] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      errorsByType: errors.reduce((acc, error) => {
        acc[error.errorType] = (acc[error.errorType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
    
    const response: UserErrorsResponse = {
      errors: paginatedErrors,
      totalCount,
      summary,
      timestamp: new Date().toISOString(),
    };
    
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
    
  } catch (error: any) {
    console.error('[User Errors API] Error:', error);
    
    return NextResponse.json({
      error: 'Failed to retrieve user errors',
      message: error.message,
      timestamp: new Date().toISOString(),
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
}
