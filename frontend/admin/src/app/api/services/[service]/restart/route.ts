/**
 * Service Restart API Route
 * ========================
 * 
 * Provides service restart capabilities for the admin dashboard.
 * Handles Docker container restarts and service management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Map service names to Docker container names
const SERVICE_CONTAINERS: Record<string, string> = {
  'gaming-engine': 'betbet-gaming',
  'custom-betting': 'betbet-custom-betting',
  'trading-engine': 'betbet-trading',
  'expert-analysis': 'betbet-experts',
  'sports-analysis': 'betbet-sports',
  'leaderboards': 'betbet-leaderboards',
  'wallet-service': 'betbet-wallet',
  'websocket-manager': 'betbet-websocket',
  'api-gateway': 'betbet-api-gateway',
};

interface RestartResult {
  success: boolean;
  message: string;
  containerName: string;
  timestamp: string;
  logs?: string[];
}

async function restartDockerContainer(containerName: string): Promise<RestartResult> {
  const timestamp = new Date().toISOString();
  
  try {
    // Check if container exists and is running
    const { stdout: containerStatus } = await execAsync(
      `docker ps -a --filter name=${containerName} --format "{{.Status}}"`
    );
    
    if (!containerStatus.trim()) {
      return {
        success: false,
        message: `Container ${containerName} not found`,
        containerName,
        timestamp,
      };
    }

    // Restart the container
    console.log(`[Service Management] Restarting container: ${containerName}`);
    await execAsync(`docker restart ${containerName}`);
    
    // Wait a moment for the container to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Get recent logs to verify restart
    const { stdout: logs } = await execAsync(
      `docker logs ${containerName} --tail 10 --since 30s`
    );
    
    // Check if container is now running
    const { stdout: newStatus } = await execAsync(
      `docker ps --filter name=${containerName} --format "{{.Status}}"`
    );
    
    const isRunning = newStatus.trim().includes('Up');
    
    return {
      success: isRunning,
      message: isRunning 
        ? `Container ${containerName} restarted successfully`
        : `Container ${containerName} restart failed - not running`,
      containerName,
      timestamp,
      logs: logs.split('\n').filter(line => line.trim()),
    };
    
  } catch (error: any) {
    console.error(`[Service Management] Failed to restart ${containerName}:`, error);
    return {
      success: false,
      message: `Failed to restart ${containerName}: ${error.message}`,
      containerName,
      timestamp,
    };
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { service: string } }
) {
  try {
    const { service } = params;
    
    // Validate service name
    if (!SERVICE_CONTAINERS[service]) {
      return NextResponse.json({
        error: 'Invalid service name',
        availableServices: Object.keys(SERVICE_CONTAINERS),
      }, { status: 400 });
    }
    
    const containerName = SERVICE_CONTAINERS[service];
    
    // Log the restart attempt
    console.log(`[Service Management] Admin requested restart of service: ${service} (${containerName})`);
    
    // Perform the restart
    const result = await restartDockerContainer(containerName);
    
    // Return appropriate status code
    const statusCode = result.success ? 200 : 500;
    
    return NextResponse.json({
      service,
      container: containerName,
      result,
      timestamp: new Date().toISOString(),
    }, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
    
  } catch (error: any) {
    console.error('[Service Management] Restart API error:', error);
    
    return NextResponse.json({
      error: 'Service restart failed',
      message: error.message,
      timestamp: new Date().toISOString(),
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
}
