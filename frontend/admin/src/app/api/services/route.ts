/**
 * Service Management API Routes - Admin Dashboard
 * ===============================================
 * 
 * Provides service management endpoints for the admin dashboard:
 * - Get service status
 * - Restart services  
 * - View service logs
 * - Health monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface ServiceConfig {
  name: string;
  port: number;
  healthEndpoint: string;
  logFile?: string;
  baseUrl: string;
  dockerContainer: string;
}

const SERVICES: Record<string, ServiceConfig> = {
  'api-gateway': {
    name: 'API Gateway',
    port: 8000,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/health',
    dockerContainer: 'betbet-api-gateway'
  },
  'gaming': {
    name: 'Gaming Engine',
    port: 8001,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/gaming/health',
    logFile: 'gaming-engine.log',
    dockerContainer: 'betbet-gaming'
  },
  'custom-betting': {
    name: 'Custom Betting',
    port: 8002,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/custom-betting/health',
    logFile: 'custom-betting.log',
    dockerContainer: 'betbet-custom-betting'
  },
  'odds-exchange': {
    name: 'Odds Exchange',
    port: 8003,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/trading/health',
    logFile: 'odds-exchange.log',
    dockerContainer: 'betbet-trading'
  },
  'expert-analysts': {
    name: 'Expert Analysts',
    port: 8004,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/experts/health',
    logFile: 'expert-analysis.log',
    dockerContainer: 'betbet-experts'
  },
  'sports-analysis': {
    name: 'Sports Analysis',
    port: 8005,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/sports/health',
    logFile: 'sports-analysis.log',
    dockerContainer: 'betbet-sports'
  },
  'leaderboards': {
    name: 'Leaderboards',
    port: 8006,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/leaderboards/health',
    logFile: 'leaderboards.log',
    dockerContainer: 'betbet-leaderboards'
  },
  'wallet': {
    name: 'Wallet Service',
    port: 8007,
    baseUrl: 'http://betbet-api-gateway:8000',
    healthEndpoint: '/api/wallet/health',
    logFile: 'wallet.log',
    dockerContainer: 'betbet-wallet'
  }
};

async function checkServiceHealth(service: ServiceConfig): Promise<{
  status: 'active' | 'error' | 'offline';
  responseTime?: number;
  error?: string;
  version?: string;
}> {
  try {
    const startTime = Date.now();

    // For services other than API gateway, use analytics endpoint to check health
    let healthEndpoint = service.healthEndpoint;
    if (service.name !== 'API Gateway') {
      healthEndpoint = service.healthEndpoint.replace('/health', '/analytics/overview');
    }

    const response = await fetch(`${service.baseUrl}${healthEndpoint}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      const healthData = await response.json();
      return {
        status: 'active',
        responseTime,
        version: healthData.version || '1.0.0'
      };
    } else {
      return {
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
  } catch (error: any) {
    return {
      status: 'offline',
      error: error.message || 'Connection failed'
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const serviceKey = searchParams.get('service');
    
    if (serviceKey) {
      // Get status for a specific service
      const service = SERVICES[serviceKey];
      if (!service) {
        return NextResponse.json({ error: 'Service not found' }, { status: 404 });
      }
      
      const health = await checkServiceHealth(service);
      return NextResponse.json({
        service: serviceKey,
        name: service.name,
        port: service.port,
        ...health
      });
    }
    
    // Get status for all services
    const serviceStatuses = await Promise.all(
      Object.entries(SERVICES).map(async ([key, service]) => {
        const health = await checkServiceHealth(service);
        return {
          service: key,
          name: service.name,
          port: service.port,
          ...health
        };
      })
    );
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      services: serviceStatuses
    });
    
  } catch (error: any) {
    return NextResponse.json({
      error: 'Failed to check service status',
      message: error.message
    }, { status: 500 });
  }
}

async function executeDockerCommand(command: string): Promise<{ success: boolean; output: string; error?: string }> {
  // Docker is not available inside the admin container
  // In production, this would integrate with container orchestration APIs
  return {
    success: false,
    output: '',
    error: 'Docker commands not available in containerized environment. Use external orchestration tools.'
  };
}

export async function POST(request: NextRequest) {
  try {
    const { action, service } = await request.json();

    if (!service || !SERVICES[service]) {
      return NextResponse.json({ error: 'Invalid service' }, { status: 400 });
    }

    const serviceConfig = SERVICES[service];
    const containerName = serviceConfig.dockerContainer;

    switch (action) {
      case 'restart':
        const restartResult = await executeDockerCommand(`docker restart ${containerName}`);
        return NextResponse.json({
          success: restartResult.success,
          message: restartResult.success
            ? `${serviceConfig.name} restarted successfully`
            : `Failed to restart ${serviceConfig.name}`,
          output: restartResult.output,
          error: restartResult.error
        });

      case 'stop':
        const stopResult = await executeDockerCommand(`docker stop ${containerName}`);
        return NextResponse.json({
          success: stopResult.success,
          message: stopResult.success
            ? `${serviceConfig.name} stopped successfully`
            : `Failed to stop ${serviceConfig.name}`,
          output: stopResult.output,
          error: stopResult.error
        });

      case 'start':
        const startResult = await executeDockerCommand(`docker start ${containerName}`);
        return NextResponse.json({
          success: startResult.success,
          message: startResult.success
            ? `${serviceConfig.name} started successfully`
            : `Failed to start ${serviceConfig.name}`,
          output: startResult.output,
          error: startResult.error
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error: any) {
    return NextResponse.json({
      error: 'Service management failed',
      message: error.message
    }, { status: 500 });
  }
}