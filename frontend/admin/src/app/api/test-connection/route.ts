import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    console.log('[Test] Testing connection to API gateway...');
    
    const response = await axios.get('http://betbet-api-gateway:8000/health', {
      timeout: 5000,
    });
    
    console.log('[Test] API Gateway response:', response.status, response.data);
    
    return NextResponse.json({
      success: true,
      gateway_status: response.data,
      message: 'Connection successful'
    });
    
  } catch (error) {
    console.error('[Test] Connection failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }, { status: 500 });
  }
}
