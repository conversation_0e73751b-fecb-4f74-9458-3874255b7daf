/**
 * BetBet Admin User Management Page
 * ================================
 * 
 * User management interface with Clerk integration
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Ban,
  Shield,
  Mail,
  Calendar,
  Activity,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface User {
  id: string;
  clerk_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  avatar_url?: string;
  status: 'active' | 'suspended' | 'banned';
  role: 'user' | 'admin' | 'moderator';
  created_at: string;
  last_login?: string;
  total_bets: number;
  total_volume: number;
  verification_status: 'verified' | 'pending' | 'rejected';
}

interface UserStats {
  total_users: number;
  active_users: number;
  new_users_today: number;
  verified_users: number;
  suspended_users: number;
  banned_users: number;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats>({
    total_users: 0,
    active_users: 0,
    new_users_today: 0,
    verified_users: 0,
    suspended_users: 0,
    banned_users: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      setUsers(data.users || []);
      setStats(data.stats || stats);
    } catch (error) {
      console.error('Error fetching users:', error);
      // For now, show empty state with error message
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: 'suspend' | 'activate' | 'ban' | 'verify') => {
    try {
      const response = await fetch('/api/users/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, action })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ${action} user`);
      }
      
      // Refresh users list
      await fetchUsers();
    } catch (error) {
      console.error(`Error ${action}ing user:`, error);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-admin-success" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-admin-warning" />;
      case 'banned':
        return <XCircle className="h-4 w-4 text-admin-danger" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-admin-success/20 text-admin-success';
      case 'suspended':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'banned':
        return 'bg-admin-danger/20 text-admin-danger';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-admin-success/20 text-admin-success';
      case 'pending':
        return 'bg-admin-warning/20 text-admin-warning';
      case 'rejected':
        return 'bg-admin-danger/20 text-admin-danger';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">User Management</h1>
            <p className="text-gray-400 mt-2">
              Manage platform users, permissions, and account statuses
            </p>
          </div>
          <Button className="admin-button-primary">
            <Users className="h-4 w-4 mr-2" />
            Export Users
          </Button>
        </div>

        {/* User Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Users</p>
                  <p className="text-2xl font-bold text-white">{stats.total_users.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-admin-accent" />
              </div>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Active</p>
                  <p className="text-2xl font-bold text-admin-success">{stats.active_users.toLocaleString()}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-admin-success" />
              </div>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">New Today</p>
                  <p className="text-2xl font-bold text-admin-accent">{stats.new_users_today.toLocaleString()}</p>
                </div>
                <Calendar className="h-8 w-8 text-admin-accent" />
              </div>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Verified</p>
                  <p className="text-2xl font-bold text-admin-info">{stats.verified_users.toLocaleString()}</p>
                </div>
                <Shield className="h-8 w-8 text-admin-info" />
              </div>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Suspended</p>
                  <p className="text-2xl font-bold text-admin-warning">{stats.suspended_users.toLocaleString()}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-admin-warning" />
              </div>
            </CardContent>
          </Card>

          <Card className="admin-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Banned</p>
                  <p className="text-2xl font-bold text-admin-danger">{stats.banned_users.toLocaleString()}</p>
                </div>
                <XCircle className="h-8 w-8 text-admin-danger" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="admin-card mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users by email, username, or name..."
                  className="w-full pl-10 pr-4 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-admin-accent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <select
                  className="px-4 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="suspended">Suspended</option>
                  <option value="banned">Banned</option>
                </select>
                <Button variant="outline" className="admin-button-secondary">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card className="admin-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-admin-accent" />
              Users ({filteredUsers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Activity className="h-8 w-8 text-admin-accent animate-spin mx-auto mb-4" />
                  <p className="text-gray-400">Loading users...</p>
                </div>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">No users found</p>
                  <p className="text-gray-500 text-sm mt-2">
                    {searchTerm ? 'Try adjusting your search criteria' : 'User data will appear here when available'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-admin-secondary">
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">User</th>
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">Status</th>
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">Verification</th>
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">Activity</th>
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">Volume</th>
                      <th className="text-left py-3 px-4 text-gray-400 font-medium">Joined</th>
                      <th className="text-right py-3 px-4 text-gray-400 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b border-admin-secondary/50 hover:bg-admin-secondary/30">
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-admin-accent/20 flex items-center justify-center text-admin-accent font-semibold">
                              {user.avatar_url ? (
                                <img src={user.avatar_url} alt="" className="w-10 h-10 rounded-full" />
                              ) : (
                                (user.first_name?.[0] || user.email[0]).toUpperCase()
                              )}
                            </div>
                            <div>
                              <p className="font-medium text-white">
                                {user.first_name && user.last_name ? 
                                  `${user.first_name} ${user.last_name}` : 
                                  user.username || 'Anonymous'
                                }
                              </p>
                              <p className="text-sm text-gray-400">{user.email}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(user.status)}
                            <Badge className={getStatusColor(user.status)}>
                              {user.status}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={getVerificationColor(user.verification_status)}>
                            {user.verification_status}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="text-white">{user.total_bets} bets</p>
                            <p className="text-gray-400">
                              {user.last_login ? 
                                `Last: ${new Date(user.last_login).toLocaleDateString()}` : 
                                'Never logged in'
                              }
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <p className="text-white font-medium">${user.total_volume.toLocaleString()}</p>
                        </td>
                        <td className="py-4 px-4">
                          <p className="text-gray-400 text-sm">
                            {new Date(user.created_at).toLocaleDateString()}
                          </p>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2 justify-end">
                            <Button size="sm" variant="outline" className="admin-button-secondary">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="admin-button-secondary">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}