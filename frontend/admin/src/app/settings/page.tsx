/**
 * BetBet Admin - General Settings
 * ==============================
 * 
 * General platform configuration and administrative settings.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  Settings,
  Save,
  RotateCcw,
  Bell,
  Mail,
  Globe,
  Database,
  Key,
  Users,
  Shield,
  Zap,
  Clock
} from 'lucide-react';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    general: {
      platformName: 'BetBet',
      platformDescription: 'Advanced Betting & Trading Platform',
      timezone: 'UTC',
      defaultLanguage: 'en',
      maintenanceMode: false
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      adminAlerts: true
    },
    security: {
      twoFactorRequired: true,
      sessionTimeout: 30,
      passwordComplexity: 'high',
      ipWhitelist: false
    }
  });

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving settings:', settings);
  };

  const handleReset = () => {
    // Reset to defaults logic here
    console.log('Resetting settings to defaults');
  };

  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white">General Settings</h1>
              <p className="text-gray-400 mt-2">
                Configure general platform settings and administrative preferences
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleReset} className="admin-button-secondary">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button onClick={handleSave} className="admin-button-primary">
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>

          <Tabs defaultValue="general" className="space-y-6">
            <TabsList className="bg-admin-secondary border border-admin-secondary">
              <TabsTrigger value="general" className="data-[state=active]:bg-admin-accent">
                <Globe className="h-4 w-4 mr-2" />
                General
              </TabsTrigger>
              <TabsTrigger value="notifications" className="data-[state=active]:bg-admin-accent">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="security" className="data-[state=active]:bg-admin-accent">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general">
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle>General Platform Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Platform Name
                      </label>
                      <input
                        type="text"
                        value={settings.general.platformName}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, platformName: e.target.value }
                        })}
                        className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Default Timezone
                      </label>
                      <select
                        value={settings.general.timezone}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, timezone: e.target.value }
                        })}
                        className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                      >
                        <option value="UTC">UTC</option>
                        <option value="EST">Eastern Time</option>
                        <option value="PST">Pacific Time</option>
                        <option value="GMT">Greenwich Mean Time</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Platform Description
                    </label>
                    <textarea
                      value={settings.general.platformDescription}
                      onChange={(e) => setSettings({
                        ...settings,
                        general: { ...settings.general, platformDescription: e.target.value }
                      })}
                      rows={3}
                      className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-admin-secondary/50 rounded-lg">
                    <div>
                      <h3 className="text-white font-medium">Maintenance Mode</h3>
                      <p className="text-sm text-gray-400">Temporarily disable platform access for maintenance</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.general.maintenanceMode}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, maintenanceMode: e.target.checked }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-admin-accent/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-admin-accent"></div>
                    </label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications">
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle>Notification Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(settings.notifications).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-4 bg-admin-secondary/50 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </h3>
                        <p className="text-sm text-gray-400">
                          {key === 'emailNotifications' && 'Receive notifications via email'}
                          {key === 'pushNotifications' && 'Browser push notifications'}
                          {key === 'smsNotifications' && 'SMS text message notifications'}
                          {key === 'adminAlerts' && 'Critical admin alerts and warnings'}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => setSettings({
                            ...settings,
                            notifications: { ...settings.notifications, [key]: e.target.checked }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-admin-accent/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-admin-accent"></div>
                      </label>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <Card className="admin-card">
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between p-4 bg-admin-secondary/50 rounded-lg">
                    <div>
                      <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                      <p className="text-sm text-gray-400">Require 2FA for all admin accounts</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.security.twoFactorRequired}
                        onChange={(e) => setSettings({
                          ...settings,
                          security: { ...settings.security, twoFactorRequired: e.target.checked }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-admin-accent/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-admin-accent"></div>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Session Timeout (minutes)
                    </label>
                    <input
                      type="number"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => setSettings({
                        ...settings,
                        security: { ...settings.security, sessionTimeout: parseInt(e.target.value) }
                      })}
                      className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Password Complexity
                    </label>
                    <select
                      value={settings.security.passwordComplexity}
                      onChange={(e) => setSettings({
                        ...settings,
                        security: { ...settings.security, passwordComplexity: e.target.value }
                      })}
                      className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-admin-accent"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
