/**
 * BetBet Admin - Betting Dashboard Page
 * ===================================
 * 
 * Administrative interface for managing betting markets and operations.
 * Migrated from main web app for proper admin separation.
 */

'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import BettingAdminDashboard from '@/components/betting/AdminBettingDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function BettingDashboardPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']} requiredPermissions={['create_markets']}>
      <AdminLayout>
        <BettingAdminDashboard isAdmin={true} />
      </AdminLayout>
    </ProtectedRoute>
  );
}