'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function AdminLogin() {
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();
      
      // Store the session token
      localStorage.setItem('admin_token', data.token);
      localStorage.setItem('admin_user', JSON.stringify(data.user));
      
      // Redirect to dashboard
      router.push('/');
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-admin-primary flex items-center justify-center p-4">
      <Card className="w-full max-w-md admin-card">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-admin-accent rounded-lg flex items-center justify-center mb-4">
            <svg 
              className="h-8 w-8 text-white" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14" 
              />
            </svg>
          </div>
          <CardTitle className="text-2xl font-bold text-white">
            BetBet Admin
          </CardTitle>
          <p className="text-gray-400">
            Sign in to access the admin dashboard
          </p>
        </CardHeader>
        
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 text-sm">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Email Address
              </label>
              <input
                type="email"
                required
                value={credentials.email}
                onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Password
              </label>
              <input
                type="password"
                required
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                className="w-full px-3 py-2 bg-admin-secondary border border-admin-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-transparent"
                placeholder="Enter your password"
              />
            </div>
            
            <Button
              type="submit"
              disabled={loading}
              className="w-full admin-button-primary"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign In'
              )}
            </Button>
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-400">
              Demo Credentials:<br />
              Email: <EMAIL><br />
              Password: Pass1234
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
