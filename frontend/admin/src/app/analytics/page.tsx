/**
 * BetBet Admin - Advanced Analytics
 * ================================
 * 
 * Advanced platform analytics, reporting, and business intelligence.
 */

'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import PlatformMetrics from '@/components/analytics/PlatformMetrics';
import PlatformPerformance from '@/components/analytics/PlatformPerformance';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AnalyticsPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white">Advanced Analytics</h1>
              <p className="text-gray-400 mt-2">
                Comprehensive platform analytics, reporting, and business intelligence
              </p>
            </div>
          </div>
          
          <Tabs defaultValue="metrics" className="space-y-6">
            <TabsList className="bg-admin-secondary border border-admin-secondary">
              <TabsTrigger value="metrics" className="data-[state=active]:bg-admin-accent">
                Platform Metrics
              </TabsTrigger>
              <TabsTrigger value="performance" className="data-[state=active]:bg-admin-accent">
                Performance Analytics
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="metrics">
              <PlatformMetrics />
            </TabsContent>
            
            <TabsContent value="performance">
              <PlatformPerformance />
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
