import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { AuthProvider } from '@/contexts/AuthContext';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'BetBet Admin Dashboard',
  description: 'Super Admin Dashboard for BetBet Platform Management',
  robots: 'noindex, nofollow', // Prevent search engine indexing
  viewport: 'width=device-width, initial-scale=1',
  icons: {
    icon: '/favicon.svg',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <AuthProvider>
          <div className="min-h-screen bg-admin-primary text-white">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}