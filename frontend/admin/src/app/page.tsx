/**
 * BetBet Admin Dashboard - Main Page
 * ==================================
 * 
 * Super Admin dashboard providing comprehensive platform management,
 * analytics, user management, and system monitoring.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { 
  Users, 
  Activity, 
  DollarSign, 
  TrendingUp,
  Shield,
  Settings,
  Database,
  AlertTriangle,
  BarChart3,
  Monitor,
  UserCheck,
  Gamepad2,
  Target,
  Crown,
  LineChart as LineChartIcon,
  TrendingDown,
  Zap
} from 'lucide-react';

import AdminLayout from '@/components/layout/AdminLayout';
import PlatformMetrics from '@/components/analytics/PlatformMetrics';
import UserManagement from '@/components/users/UserManagement';
import SystemMonitoring from '@/components/system/SystemMonitoring';
import ModuleOverview from '@/components/modules/ModuleOverview';
import { LineChart, BarChart } from '@/components/ui/chart';

// Import API client
import adminAPI, { PlatformStats, ModuleStats } from '@/lib/api-client';

// Helper function to format revenue with appropriate scaling
const formatRevenue = (revenue: number): string => {
  if (revenue === 0) return '0';
  if (revenue < 1000) return `$${revenue}`;
  if (revenue < 1000000) return `$${(revenue / 1000).toFixed(0)}k`;
  return `$${(revenue / 1000000).toFixed(1)}M`;
};

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [platformStats, setPlatformStats] = useState<PlatformStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalRevenue: 0,
    systemUptime: 0,
    apiResponseTime: 0
  });
  const [moduleStats, setModuleStats] = useState<ModuleStats | null>(null);
  const [systemAlerts, setSystemAlerts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [serviceErrors, setServiceErrors] = useState<string[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();

    // Set up real-time updates every 30 seconds
    const interval = setInterval(() => {
      loadDashboardData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setError(null);

      // Load platform stats, module stats, alerts, and performance metrics in parallel
      const [platformData, moduleData, alertsData, metricsData] = await Promise.allSettled([
        adminAPI.getPlatformStats(),
        adminAPI.getModuleStats(),
        adminAPI.getSystemAlerts(),
        fetch('/api/platform-metrics').then(res => res.json())
      ]);

      if (platformData.status === 'fulfilled') {
        const stats = platformData.value;
        setPlatformStats(stats);
        // Check for service errors in the response
        if ((stats as any).serviceErrors?.length > 0) {
          setServiceErrors((stats as any).serviceErrors);
        }
      } else {
        // Show the actual error message from the API
        const errorMessage = platformData.reason?.message || 'Failed to load platform statistics';
        console.error('[Dashboard] Platform stats error:', platformData.reason);
        setError(errorMessage);
      }

      if (moduleData.status === 'fulfilled') {
        setModuleStats(moduleData.value);
      } else {
        // Show the actual error message from the module stats API
        const moduleError = moduleData.reason?.message || 'Failed to load module statistics';
        console.error('[Dashboard] Module stats error:', moduleData.reason);
        setError(prev => prev ? `${prev}\n\n${moduleError}` : moduleError);
      }

      if (alertsData.status === 'fulfilled') {
        setSystemAlerts(alertsData.value);
      }

      if (metricsData.status === 'fulfilled') {
        setPerformanceMetrics(metricsData.value);
      } else {
        console.error('Failed to load performance metrics:', metricsData.reason);
      }

    } catch (err: any) {
      console.error('Failed to load dashboard data:', err);
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    setIsLoading(true);
    loadDashboardData();
  };

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    trend = 'up',
    suffix = '' 
  }: {
    title: string;
    value: number | string;
    change?: number;
    icon: React.ComponentType<any>;
    trend?: 'up' | 'down' | 'neutral';
    suffix?: string;
  }) => (
    <Card className="metric-card">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="metric-label">{title}</CardTitle>
        <Icon className="h-5 w-5 text-admin-accent" />
      </CardHeader>
      <CardContent>
        <div className="metric-value">
          {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
        </div>
        {change !== undefined && (
          <div className={`flex items-center space-x-1 text-sm mt-1 ${
            trend === 'up' ? 'metric-trend-up' : 
            trend === 'down' ? 'metric-trend-down' : 
            'text-gray-400'
          }`}>
            {trend === 'up' ? (
              <TrendingUp className="h-3 w-3" />
            ) : trend === 'down' ? (
              <TrendingDown className="h-3 w-3" />
            ) : null}
            <span>{change > 0 ? '+' : ''}{change}% from last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const AlertItem = ({ alert }: { alert: any }) => (
    <div className="flex items-start space-x-3 p-3 rounded-lg bg-admin-secondary/50">
      <div className={`p-1 rounded-full ${
        alert.type === 'error' ? 'bg-admin-danger/20' :
        alert.type === 'warning' ? 'bg-admin-warning/20' :
        'bg-admin-info/20'
      }`}>
        <AlertTriangle className={`h-4 w-4 ${
          alert.type === 'error' ? 'text-admin-danger' :
          alert.type === 'warning' ? 'text-admin-warning' :
          'text-admin-info'
        }`} />
      </div>
      <div className="flex-1">
        <p className="text-sm text-white">{alert.message}</p>
        <p className="text-xs text-gray-400 mt-1">
          {alert.timestamp ? new Date(alert.timestamp).toLocaleTimeString() : 'Recently'}
        </p>
      </div>
    </div>
  );

  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Platform Overview</h1>
            <p className="text-gray-400 mt-1">
              Monitor and manage the entire BetBet ecosystem
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              className="admin-button-secondary"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <Monitor className="h-4 w-4 mr-2" />
              {isLoading ? 'Refreshing...' : 'Refresh Data'}
            </Button>
            <Button className="admin-button-primary">
              <Settings className="h-4 w-4 mr-2" />
              Platform Settings
            </Button>
          </div>
        </div>

        {/* Navigation Breadcrumb */}
        <nav className="admin-navigation">
          <a href="#" className="font-medium">Dashboard</a>
          <span className="separator">/</span>
          <span className="text-white">Overview</span>
        </nav>

        {/* Error Display */}
        {error && (
          <Card className="admin-card border-admin-danger/50 bg-admin-danger/10">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-admin-danger flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-admin-danger mb-2">System Error</h3>
                  <pre className="text-xs text-admin-danger/80 whitespace-pre-wrap font-mono bg-admin-danger/5 p-2 rounded border">
                    {error}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Service Errors Display */}
        {serviceErrors.length > 0 && (
          <Card className="admin-card border-admin-warning/50 bg-admin-warning/10">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-admin-warning flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-admin-warning mb-2">Service Issues Detected</h3>
                  <ul className="space-y-1">
                    {serviceErrors.map((err, idx) => (
                      <li key={idx} className="text-sm text-gray-300 flex items-center gap-2">
                        <span className="w-1.5 h-1.5 bg-admin-warning rounded-full"></span>
                        {err}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-admin-secondary">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 size={16} />
              Overview
            </TabsTrigger>
            <TabsTrigger value="modules" className="flex items-center gap-2">
              <Gamepad2 size={16} />
              Modules
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users size={16} />
              Users
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Database size={16} />
              System
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <LineChartIcon size={16} />
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Total Users"
                value={platformStats.totalUsers}
                icon={Users}
                trend="neutral"
              />
              <MetricCard
                title="Active Users"
                value={platformStats.activeUsers}
                icon={UserCheck}
                trend="neutral"
              />
              <MetricCard
                title="Total Revenue"
                value={formatRevenue(platformStats.totalRevenue)}
                icon={DollarSign}
                trend="neutral"
              />
              <MetricCard
                title="System Uptime"
                value={platformStats.systemUptime === 0 ? 'No Data' : platformStats.systemUptime}
                icon={Zap}
                trend="neutral"
                suffix={platformStats.systemUptime === 0 ? '' : '%'}
              />
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="admin-card col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-admin-accent" />
                    Platform Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {performanceMetrics ? (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="p-3 bg-admin-secondary/50 rounded">
                        <div className="text-admin-accent font-medium">API Response Time</div>
                        <div className="text-white text-lg font-semibold">
                          {Math.round(performanceMetrics.apiResponseTime?.reduce((sum: number, item: any) => sum + item.value, 0) / (performanceMetrics.apiResponseTime?.length || 1))}ms
                        </div>
                        <div className="text-gray-400 text-xs">Average across services</div>
                      </div>
                      <div className="p-3 bg-admin-secondary/50 rounded">
                        <div className="text-admin-accent font-medium">Active Users</div>
                        <div className="text-white text-lg font-semibold">
                          {performanceMetrics.activeUsers?.[performanceMetrics.activeUsers.length - 1]?.value || 0}
                        </div>
                        <div className="text-gray-400 text-xs">Currently online</div>
                      </div>
                      <div className="p-3 bg-admin-secondary/50 rounded">
                        <div className="text-admin-accent font-medium">Database Queries</div>
                        <div className="text-white text-lg font-semibold">
                          {performanceMetrics.databaseQueries?.[performanceMetrics.databaseQueries.length - 1]?.value || 0}/sec
                        </div>
                        <div className="text-gray-400 text-xs">Current rate</div>
                      </div>
                      <div className="p-3 bg-admin-secondary/50 rounded">
                        <div className="text-admin-accent font-medium">Revenue</div>
                        <div className="text-white text-lg font-semibold">
                          ${(performanceMetrics.revenueTrend?.[performanceMetrics.revenueTrend.length - 1]?.value || 0).toLocaleString()}
                        </div>
                        <div className="text-gray-400 text-xs">This month</div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-admin-accent mx-auto mb-2"></div>
                        <p className="text-gray-400 text-sm">Loading metrics...</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="admin-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-admin-warning" />
                    Recent Alerts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {systemAlerts.length > 0 ? (
                    systemAlerts.map((alert, index) => (
                      <AlertItem key={index} alert={alert} />
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-400 text-sm">No recent alerts</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Modules Tab */}
          <TabsContent value="modules">
            {moduleStats ? (
              <ModuleOverview moduleStats={moduleStats} />
            ) : error ? (
              <div className="text-center py-8">
                <div className="flex items-center justify-center gap-2 text-admin-danger mb-2">
                  <AlertTriangle className="h-5 w-5" />
                  <p className="text-lg font-medium">Module Data Unavailable</p>
                </div>
                <p className="text-gray-400">{error}</p>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-400">Loading module statistics...</p>
              </div>
            )}
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <UserManagement />
          </TabsContent>

          {/* System Tab */}
          <TabsContent value="system">
            <SystemMonitoring />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <PlatformMetrics />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
    </ProtectedRoute>
  );
}