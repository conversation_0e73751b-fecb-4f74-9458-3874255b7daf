/**
 * BetBet Admin - System Monitoring
 * ===============================
 * 
 * System health monitoring, database status, and infrastructure management.
 */

'use client';

import React from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import SystemMonitoring from '@/components/system/SystemMonitoring';
import ServiceManagement from '@/components/system/ServiceManagement';
import UserErrorMonitoring from '@/components/system/UserErrorMonitoring';
import {
  Server,
  Terminal,
  AlertTriangle,
  Activity
} from 'lucide-react';

export default function SystemPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminLayout>
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white">System Operations</h1>
              <p className="text-gray-400 mt-2">
                Monitor system health, manage services, and track user errors
              </p>
            </div>
          </div>

          <Tabs defaultValue="monitoring" className="space-y-6">
            <TabsList className="bg-admin-secondary border border-admin-secondary">
              <TabsTrigger value="monitoring" className="data-[state=active]:bg-admin-accent">
                <Activity className="h-4 w-4 mr-2" />
                System Monitoring
              </TabsTrigger>
              <TabsTrigger value="services" className="data-[state=active]:bg-admin-accent">
                <Server className="h-4 w-4 mr-2" />
                Service Management
              </TabsTrigger>
              <TabsTrigger value="errors" className="data-[state=active]:bg-admin-accent">
                <AlertTriangle className="h-4 w-4 mr-2" />
                User Errors
              </TabsTrigger>
            </TabsList>

            <TabsContent value="monitoring">
              <SystemMonitoring />
            </TabsContent>

            <TabsContent value="services">
              <ServiceManagement />
            </TabsContent>

            <TabsContent value="errors">
              <UserErrorMonitoring />
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
