/**
 * BetBet Admin - Platform Settings
 * ================================
 * 
 * Configure platform-wide settings including fees, limits, and operational parameters.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings,
  Percent,
  DollarSign,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  Target,
  Zap,
  Info
} from 'lucide-react';

import AdminLayout from '@/components/layout/AdminLayout';
import PlatformFeeManager from '@/components/settings/PlatformFeeManager';

// Mock current platform settings
const mockCurrentSettings = {
  fees: {
    gaming: {
      standard: 10.0,
      premium: 8.0,
      promotional: 5.0,
      highVolume: 7.0
    },
    trading: {
      standard: 0.5,
      maker: 0.3,
      taker: 0.7,
      premium: 0.4
    },
    subscriptions: {
      expert: 15.0,
      premium: 20.0,
      enterprise: 25.0
    }
  },
  limits: {
    maxBetAmount: 50000,
    minBetAmount: 1,
    maxWithdrawal: 100000,
    dailyLimits: true
  },
  operations: {
    maintenanceMode: false,
    newUserRegistration: true,
    bonusProgram: true,
    autoSettlement: true
  }
};

export default function PlatformSettingsPage() {
  const [settings, setSettings] = useState(mockCurrentSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Track changes
  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // TODO: API call to save settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasChanges(false);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings(mockCurrentSettings);
    setHasChanges(true);
  };

  const StatsCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon,
    trend = 'neutral'
  }: {
    title: string;
    value: string;
    change?: string;
    icon: React.ComponentType<any>;
    trend?: 'up' | 'down' | 'neutral';
  }) => (
    <Card className="admin-card">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-300">{title}</CardTitle>
        <Icon className="h-4 w-4 text-admin-accent" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-white">{value}</div>
        {change && (
          <div className={`flex items-center space-x-1 text-xs mt-1 ${
            trend === 'up' ? 'text-green-400' : 
            trend === 'down' ? 'text-red-400' : 
            'text-gray-400'
          }`}>
            {trend === 'up' ? (
              <TrendingUp className="h-3 w-3" />
            ) : trend === 'down' ? (
              <TrendingDown className="h-3 w-3" />
            ) : null}
            <span>{change}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center gap-3">
              <Settings className="h-8 w-8 text-admin-accent" />
              Platform Settings
            </h1>
            <p className="text-gray-400 mt-1">
              Configure platform fees, limits, and operational parameters
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasChanges && (
              <Button 
                variant="outline" 
                onClick={resetToDefaults}
                className="admin-button-secondary"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            )}
            
            <Button 
              onClick={saveSettings}
              disabled={!hasChanges || saving}
              className="admin-button-primary"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Status Bar */}
        {hasChanges && (
          <div className="bg-admin-warning/10 border border-admin-warning/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-admin-warning" />
              <span className="text-admin-warning font-medium">You have unsaved changes</span>
              <span className="text-gray-400">•</span>
              <span className="text-gray-400">Remember to save your configuration</span>
            </div>
          </div>
        )}

        {lastSaved && (
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-green-400 font-medium">Settings saved successfully</span>
              <span className="text-gray-400">•</span>
              <span className="text-gray-400">
                Last saved: {lastSaved.toLocaleTimeString()}
              </span>
            </div>
          </div>
        )}

        {/* Impact Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatsCard
            title="Current Revenue Rate"
            value="$45.2K/day"
            change="+12.3% from last week"
            icon={DollarSign}
            trend="up"
          />
          <StatsCard
            title="Average Fee %"
            value="8.7%"
            change="+0.2% from last month"
            icon={Percent}
            trend="up"
          />
          <StatsCard
            title="Active Promotions"
            value="3"
            change="2 ending this week"
            icon={Target}
            trend="neutral"
          />
          <StatsCard
            title="Fee Collections"
            value="$156.8K"
            change="+18.5% this month"
            icon={TrendingUp}
            trend="up"
          />
        </div>

        {/* Main Settings Tabs */}
        <Tabs defaultValue="fees" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-admin-secondary">
            <TabsTrigger value="fees" className="flex items-center gap-2">
              <Percent size={16} />
              Platform Fees
            </TabsTrigger>
            <TabsTrigger value="limits" className="flex items-center gap-2">
              <Target size={16} />
              Limits & Controls
            </TabsTrigger>
            <TabsTrigger value="operations" className="flex items-center gap-2">
              <Zap size={16} />
              Operations
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2">
              <Calendar size={16} />
              Scheduled Changes
            </TabsTrigger>
          </TabsList>

          {/* Platform Fees Tab */}
          <TabsContent value="fees">
            <PlatformFeeManager 
              settings={settings.fees}
              onUpdate={(category, key, value) => updateSetting('fees', `${category}.${key}`, value)}
            />
          </TabsContent>

          {/* Limits & Controls Tab */}
          <TabsContent value="limits" className="space-y-6">
            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-admin-accent" />
                  Transaction Limits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Maximum Bet Amount ($)
                    </label>
                    <input
                      type="number"
                      value={settings.limits.maxBetAmount}
                      onChange={(e) => updateSetting('limits', 'maxBetAmount', Number(e.target.value))}
                      className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Minimum Bet Amount ($)
                    </label>
                    <input
                      type="number"
                      value={settings.limits.minBetAmount}
                      onChange={(e) => updateSetting('limits', 'minBetAmount', Number(e.target.value))}
                      className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Maximum Daily Withdrawal ($)
                  </label>
                  <input
                    type="number"
                    value={settings.limits.maxWithdrawal}
                    onChange={(e) => updateSetting('limits', 'maxWithdrawal', Number(e.target.value))}
                    className="w-full px-3 py-2 bg-admin-secondary border border-gray-600 rounded-md text-white"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Operations Tab */}
          <TabsContent value="operations" className="space-y-6">
            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-admin-accent" />
                  Platform Operations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  {Object.entries(settings.operations).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <label className="text-sm font-medium text-gray-300 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </label>
                      <button
                        onClick={() => updateSetting('operations', key, !value)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          value ? 'bg-admin-accent' : 'bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            value ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scheduled Changes Tab */}
          <TabsContent value="schedule" className="space-y-6">
            <Card className="admin-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-admin-accent" />
                  Scheduled Fee Changes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400">No scheduled changes</p>
                  <Button className="mt-4 admin-button-primary">
                    Schedule Fee Change
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}