/**
 * Admin Dashboard API Client
 * ==========================
 * 
 * Centralized API client for the admin dashboard to communicate with backend services.
 * Handles authentication, error handling, and service communication.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface ServiceResponse<T = any> {
  data: T;
  service: string;
  timestamp: string;
}

export interface PlatformStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  systemUptime: number;
  apiResponseTime: number;
}

export interface ModuleStats {
  gaming: { users: number; revenue: number; growth: number };
  betting: { users: number; revenue: number; growth: number };
  trading: { users: number; revenue: number; growth: number };
  experts: { users: number; revenue: number; growth: number };
  sports: { users: number; revenue: number; growth: number };
  leaderboards: { users: number; revenue: number; growth: number };
}

class AdminAPIClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    // Use environment variable or fallback to localhost
    this.baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[Admin API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[Admin API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`[Admin API] Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('[Admin API] Response error:', error.response?.status, error.response?.data);
        return Promise.reject(error);
      }
    );
  }

  // Health check for all services through API Gateway
  async checkServiceHealth(): Promise<Record<string, any>> {
    try {
      const services = [
        { name: 'gaming', url: `${this.baseURL}/api/gaming/health` },
        { name: 'custom-betting', url: `${this.baseURL}/api/custom-betting/health` },
        { name: 'trading', url: `${this.baseURL}/api/trading/health` },
        { name: 'experts', url: `${this.baseURL}/api/experts/health` },
        { name: 'sports', url: `${this.baseURL}/api/sports/health` },
        { name: 'leaderboards', url: `${this.baseURL}/api/leaderboards/health` },
        { name: 'wallet', url: `${this.baseURL}/api/wallet/health` }
      ];
      const healthChecks = await Promise.allSettled(
        services.map(service => 
          axios.get(service.url)
        )
      );

      const healthStatus: Record<string, any> = {};
      services.forEach((service, index) => {
        const result = healthChecks[index];
        if (result.status === 'fulfilled') {
          healthStatus[service.name] = result.value.data;
        } else {
          healthStatus[service.name] = { status: 'unhealthy', error: result.reason.message };
        }
      });

      return healthStatus;
    } catch (error) {
      console.error('Failed to check service health:', error);
      throw error;
    }
  }

  // Get platform overview statistics
  async getPlatformStats(): Promise<PlatformStats> {
    try {
      console.log('[Admin API] Loading platform statistics...');

      // Get analytics from available services with shorter timeout
      const serviceRequests = [
        { name: 'gaming', request: this.client.get('/api/gaming/analytics/overview', { timeout: 3000 }) },
        { name: 'customBetting', request: this.client.get('/api/custom-betting/analytics/overview', { timeout: 3000 }) },
        { name: 'wallet', request: this.client.get('/api/wallet/analytics/overview', { timeout: 3000 }) },
        { name: 'trading', request: this.client.get('/api/trading/analytics/overview', { timeout: 3000 }) },
        { name: 'experts', request: this.client.get('/api/experts/analytics/overview', { timeout: 3000 }) },
        { name: 'sports', request: this.client.get('/api/sports/analytics/overview', { timeout: 3000 }) },
        { name: 'leaderboards', request: this.client.get('/api/leaderboards/analytics/overview', { timeout: 3000 }) }
      ];

      const results = await Promise.allSettled(serviceRequests.map(s => s.request));

      // Track service errors and successes
      const errors: string[] = [];
      const availableServices: string[] = [];

      // Aggregate data from all services
      let totalUsers = 0;
      let activeUsers = 0;
      let totalRevenue = 0;

      // Process each service result
      results.forEach((result, index) => {
        const serviceName = serviceRequests[index].name;

        if (result.status === 'fulfilled') {
          availableServices.push(serviceName);
          const data = result.value.data;

          console.log(`[Admin API] ✅ ${serviceName} service SUCCESS:`, {
            status: result.value.status,
            data: data
          });

          // Aggregate data based on service type
          switch (serviceName) {
            case 'gaming':
              totalUsers = Math.max(totalUsers, data.total_users || 0);
              activeUsers += data.active_sessions || 0;
              totalRevenue += data.total_betting_volume || 0;
              break;
            case 'customBetting':
              totalUsers = Math.max(totalUsers, data.total_participants || 0);
              activeUsers += data.active_markets || 0;
              totalRevenue += data.total_volume || 0;
              break;
            case 'wallet':
              totalRevenue += data.total_transactions_volume || 0;
              break;
            case 'trading':
              totalUsers = Math.max(totalUsers, data.total_traders || 0);
              totalRevenue += data.total_trading_volume || 0;
              break;
            default:
              // For other services, add any revenue data available
              totalRevenue += data.total_volume || data.revenue || 0;
              break;
          }
        } else {
          errors.push(`${serviceName} service unavailable`);
          console.error(`[Admin API] ❌ ${serviceName} service FAILED:`, {
            message: result.reason?.message || 'Unknown error',
            code: result.reason?.code,
            status: result.reason?.response?.status,
            statusText: result.reason?.response?.statusText,
            data: result.reason?.response?.data
          });
        }
      });

      console.log(`[Admin API] Platform stats aggregated:`, {
        totalUsers,
        activeUsers,
        totalRevenue,
        availableServices,
        errors
      });

      // Always show real errors - no fallback data
      if (errors.length > 0) {
        const errorDetails = errors.map(err => `• ${err}`).join('\n');
        const errorMessage = `Service failures detected:\n${errorDetails}\n\nAvailable services: ${availableServices.join(', ') || 'None'}`;
        console.error('[Admin API] Service errors:', errorMessage);
        throw new Error(errorMessage);
      }

      return {
        totalUsers,
        activeUsers,
        totalRevenue,
        systemUptime: 0,
        apiResponseTime: 0
      } as PlatformStats;
    } catch (error) {
      console.error('[Admin API] Failed to get platform stats:', error);
      // Re-throw with detailed context
      if (error instanceof Error) {
        throw new Error(`Platform Statistics Error: ${error.message}`);
      }
      throw new Error('Platform Statistics Error: Unknown error occurred');
    }
  }

  // Get module-specific statistics
  async getModuleStats(): Promise<ModuleStats & { serviceStatus?: Record<string, 'active' | 'error' | 'offline'> }> {
    try {
      const [gaming, betting, trading, experts, sports, leaderboards] = await Promise.allSettled([
        this.client.get('/api/gaming/analytics/overview'),
        this.client.get('/api/custom-betting/analytics/overview'),
        this.client.get('/api/trading/analytics/overview'),
        this.client.get('/api/experts/analytics/overview'),
        this.client.get('/api/sports/analytics/overview'),
        this.client.get('/api/leaderboards/analytics/overview')
      ]);

      // Track service status
      const serviceStatus: Record<string, 'active' | 'error' | 'offline'> = {
        gaming: gaming.status === 'fulfilled' ? 'active' : 'error',
        betting: betting.status === 'fulfilled' ? 'active' : 'error',
        trading: trading.status === 'fulfilled' ? 'active' : 'offline',
        experts: experts.status === 'fulfilled' ? 'active' : 'offline',
        sports: sports.status === 'fulfilled' ? 'active' : 'offline',
        leaderboards: leaderboards.status === 'fulfilled' ? 'active' : 'offline'
      };

      // Extract real data, don't fake it
      const gamingData = gaming.status === 'fulfilled' ? gaming.value.data : null;
      const bettingData = betting.status === 'fulfilled' ? betting.value.data : null;
      const tradingData = trading.status === 'fulfilled' ? trading.value.data : null;
      const expertsData = experts.status === 'fulfilled' ? experts.value.data : null;
      const sportsData = sports.status === 'fulfilled' ? sports.value.data : null;
      const leaderboardsData = leaderboards.status === 'fulfilled' ? leaderboards.value.data : null;

      return {
        gaming: {
          users: gamingData?.total_users || 0,
          revenue: gamingData?.total_betting_volume || 0,
          growth: gamingData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.gaming
        },
        betting: {
          users: bettingData?.total_participants || 0,
          revenue: bettingData?.total_volume || 0,
          growth: bettingData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.betting
        },
        trading: {
          users: tradingData?.active_traders || 0,
          revenue: tradingData?.total_volume || 0,
          growth: tradingData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.trading
        },
        experts: {
          users: expertsData?.active_followers || 0,
          revenue: expertsData?.revenue || 0,
          growth: expertsData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.experts
        },
        sports: {
          users: sportsData?.active_users || 0,
          revenue: sportsData?.revenue || 0,
          growth: sportsData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.sports
        },
        leaderboards: {
          users: leaderboardsData?.total_users || 0,
          revenue: leaderboardsData?.revenue || 0,
          growth: leaderboardsData?.growth_percentage || 0, // Use real growth data from backend
          status: serviceStatus.leaderboards
        },
        serviceStatus
      } as ModuleStats & { serviceStatus: Record<string, 'active' | 'error' | 'offline'> };
    } catch (error) {
      console.error('Failed to get module stats:', error);
      throw error; // Re-throw to surface the error
    }
  }

  // Get recent system alerts
  async getSystemAlerts(): Promise<any[]> {
    try {
      // This would come from a monitoring/alerting service
      return [
        {
          id: 1,
          type: 'warning',
          message: 'High API response time in trading module',
          timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          type: 'info',
          message: 'Scheduled maintenance completed successfully',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          type: 'error',
          message: 'Payment gateway timeout resolved',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
        }
      ];
    } catch (error) {
      console.error('Failed to get system alerts:', error);
      return [];
    }
  }

  // Generic API call method
  async apiCall<T = any>(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any): Promise<T> {
    try {
      const response = await this.client.request({
        url: endpoint,
        method,
        data
      });
      return response.data;
    } catch (error) {
      console.error(`API call failed: ${method} ${endpoint}`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const adminAPI = new AdminAPIClient();
export default adminAPI;
