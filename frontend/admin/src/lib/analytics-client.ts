/**
 * Analytics Data Client
 * ====================
 * 
 * Centralized client for fetching real analytics data from various platform services.
 * Provides type-safe interfaces and error handling for all analytics endpoints.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Type definitions for analytics data
export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  responseTime: number;
  uptime: number;
  requests: number;
  lastCheck: string;
}

export interface PerformanceMetrics {
  apiResponseTime: Array<{ label: string; value: number; timestamp: string }>;
  websocketLatency: Array<{ label: string; value: number; timestamp: string }>;
  databaseQueries: Array<{ label: string; value: number; timestamp: string }>;
  errorRate: Array<{ label: string; value: number; timestamp: string }>;
  cpuUsage: Array<{ label: string; value: number; timestamp: string }>;
  memoryUsage: Array<{ label: string; value: number; timestamp: string }>;
  diskUsage: Array<{ label: string; value: number; timestamp: string }>;
  networkLatency: Array<{ label: string; value: number; timestamp: string }>;
}

export interface PlatformAnalytics {
  totalUsers: number;
  activeUsers: number;
  totalVolume: number;
  totalMarkets: number;
  totalExperts: number;
  totalTransactions: number;
  revenue: number;
  growth: {
    users: number;
    volume: number;
    markets: number;
  };
}

export interface SystemMetrics {
  services: ServiceHealth[];
  performance: PerformanceMetrics;
  analytics: PlatformAnalytics;
  timestamp: string;
}

class AnalyticsClient {
  private async fetchWithTimeout(url: string, timeout: number = 10000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async getServiceHealth(): Promise<ServiceHealth[]> {
    try {
      // Use the new health check API that proxies to all services
      const response = await this.fetchWithTimeout('/api/health/all', 10000);

      if (response.ok) {
        const data = await response.json();

        // Transform the response to match our ServiceHealth interface
        return data.services.map((service: any) => ({
          name: service.name,
          status: service.status === 'healthy' ? 'healthy' :
                  service.status === 'unhealthy' ? 'warning' : 'critical',
          responseTime: service.responseTime,
          details: service.details || { error: service.error },
        }));
      } else {
        console.error('Health check API failed:', response.status, response.statusText);
        throw new Error(`Health check API failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to get service health:', error);
      throw error; // Re-throw to let UI handle the error properly
    }
  }

  // REMOVED: getFallbackServiceHealth() - No more mock data fallbacks!
  // Errors should be thrown and handled by the UI to show real error states

  // Legacy method - keeping for backward compatibility but simplified
  private async legacyGetServiceHealth(): Promise<ServiceHealth[]> {
    const services = [
      { name: 'Gaming Engine', endpoint: '/api/health/gaming' },
      { name: 'Custom Betting', endpoint: '/api/health/custom-betting' },
      { name: 'Trading Engine', endpoint: '/api/health/trading' },
      { name: 'Expert Analysis', endpoint: '/api/health/experts' },
      { name: 'Sports Analysis', endpoint: '/api/health/sports' },
      { name: 'Leaderboards', endpoint: '/api/health/leaderboards' },
      { name: 'Wallet Service', endpoint: '/api/health/wallet' },
      { name: 'WebSocket Manager', endpoint: '/api/health/websocket' },
    ];

    const healthChecks = await Promise.allSettled(
      services.map(async (service) => {
        const startTime = Date.now();
        try {
          const response = await this.fetchWithTimeout(service.endpoint, 5000);
          const responseTime = Date.now() - startTime;
          
          if (response.ok) {
            const data = await response.json();
            return {
              name: service.name,
              status: 'healthy' as const,
              responseTime,
              uptime: data.uptime || 99.9,
              requests: data.requests || 0, // No fake random data
              lastCheck: new Date().toISOString(),
            };
          } else {
            return {
              name: service.name,
              status: 'warning' as const,
              responseTime,
              uptime: 0,
              requests: 0,
              lastCheck: new Date().toISOString(),
            };
          }
        } catch (error) {
          return {
            name: service.name,
            status: 'critical' as const,
            responseTime: Date.now() - startTime,
            uptime: 0,
            requests: 0,
            lastCheck: new Date().toISOString(),
          };
        }
      })
    );

    return healthChecks.map((result, index) => 
      result.status === 'fulfilled' ? result.value : {
        name: services[index].name,
        status: 'unknown' as const,
        responseTime: 0,
        uptime: 0,
        requests: 0,
        lastCheck: new Date().toISOString(),
      }
    );
  }

  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      const response = await this.fetchWithTimeout('/api/platform-metrics');
      if (response.ok) {
        const data = await response.json();
        return data;
      }
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
      throw new Error(`Performance metrics unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getPlatformAnalytics(): Promise<PlatformAnalytics> {
    try {
      // Try to get real analytics from multiple sources
      const [bettingStats, expertStats, userStats] = await Promise.allSettled([
        this.fetchWithTimeout(`${API_BASE_URL}/api/custom-betting/analytics/overview`),
        this.fetchWithTimeout(`${API_BASE_URL}/api/experts/analytics/platform`),
        this.fetchWithTimeout('/api/platform-stats'),
      ]);

      let analytics: Partial<PlatformAnalytics> = {};

      // Process betting statistics
      if (bettingStats.status === 'fulfilled' && bettingStats.value.ok) {
        const bettingData = await bettingStats.value.json();
        analytics.totalMarkets = bettingData.total_markets || 0;
        analytics.totalVolume = parseFloat(bettingData.total_volume || '0');
      }

      // Process expert statistics
      if (expertStats.status === 'fulfilled' && expertStats.value.ok) {
        const expertData = await expertStats.value.json();
        analytics.totalExperts = expertData.total_experts || 0;
      }

      // Process user statistics
      if (userStats.status === 'fulfilled' && userStats.value.ok) {
        const userData = await userStats.value.json();
        analytics.totalUsers = userData.total_users || 0;
        analytics.activeUsers = userData.active_users || 0;
      }

      // Return only real data - no fake defaults
      return {
        totalUsers: analytics.totalUsers || 0,
        activeUsers: analytics.activeUsers || 0,
        totalVolume: analytics.totalVolume || 0,
        totalMarkets: analytics.totalMarkets || 0,
        totalExperts: analytics.totalExperts || 0,
        totalTransactions: 0, // No real data source yet
        revenue: (analytics.totalVolume || 0) * 0.02, // 2% platform fee on real volume
        growth: {
          users: 0, // No growth tracking implemented yet
          volume: 0,
          markets: 0,
        },
      };
    } catch (error) {
      console.error('Failed to fetch platform analytics:', error);
      throw new Error(`Platform analytics unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    const [services, performance, analytics] = await Promise.all([
      this.getServiceHealth(),
      this.getPerformanceMetrics(),
      this.getPlatformAnalytics(),
    ]);

    // Ensure performance metrics have all required properties
    const safePerformance: PerformanceMetrics = {
      apiResponseTime: performance.apiResponseTime || [],
      websocketLatency: performance.websocketLatency || [],
      databaseQueries: performance.databaseQueries || [],
      errorRate: performance.errorRate || [],
      cpuUsage: performance.cpuUsage || [],
      memoryUsage: performance.memoryUsage || [],
      diskUsage: performance.diskUsage || [],
      networkLatency: performance.networkLatency || [],
    };

    return {
      services,
      performance: safePerformance,
      analytics,
      timestamp: new Date().toISOString(),
    };
  }

  // REMOVED: All mock data generation functions
  // These were dangerous fallbacks that masked real errors with fake data
  // Errors should now be thrown and handled transparently by the UI
}

export const analyticsClient = new AnalyticsClient();
export default analyticsClient;
