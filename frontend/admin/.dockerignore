# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js build outputs
.next/
out/
build/
dist/

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output/
__tests__/
*.test.js
*.test.jsx
*.test.ts
*.test.tsx
*.spec.js
*.spec.jsx
*.spec.ts
*.spec.tsx
test/
tests/
jest.config.js
playwright.config.ts
.coverage
htmlcov/

# Development tools
.git/
.gitignore
.eslintrc*
.prettierrc*
.stylelintrc*
.editorconfig
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
*.log

# Documentation
README.md
CHANGELOG.md
*.md
docs/

# Development configs
next.config.dev.js
webpack.config.js
rollup.config.js
tsconfig.tsbuildinfo

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Temporary files
tmp/
temp/
.tmp/

# Development scripts and configs
scripts/

# Mock files
__mocks__/
*.mock.js
*.mock.ts

# Development Dockerfiles
Dockerfile.dev