# BetBet Platform - Development Makefile
# Provides convenient commands for development workflow

.PHONY: help setup-dev start-services stop-services restart-services clean
.PHONY: test-all test-unit test-integration test-e2e lint format
.PHONY: db-migrate db-seed db-reset logs health-check
.PHONY: build-all deploy-staging deploy-production
.DEFAULT_GOAL := help

# Configuration
DOCKER_COMPOSE = docker-compose
PYTHON = python3
NODE = npm

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

help: ## Show this help message
	@echo "$(BLUE)BetBet Platform Development Commands$(NC)"
	@echo "======================================"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development Environment Setup
setup-dev: ## Initial development environment setup
	@echo "$(BLUE)Setting up BetBet development environment...$(NC)"
	@echo "$(YELLOW)Creating environment files...$(NC)"
	@cp config/environments/development.env.example .env || echo "Environment file already exists"
	@echo "$(YELLOW)Installing Python dependencies...$(NC)"
	@cd services/shared && pip install -r requirements/base.txt
	@echo "$(YELLOW)Installing frontend dependencies...$(NC)"
	@cd frontend/web && npm install
	@echo "$(YELLOW)Setting up pre-commit hooks...$(NC)"
	@pip install pre-commit
	@pre-commit install
	@echo "$(GREEN)Development environment setup complete!$(NC)"
	@echo "$(BLUE)Run 'make start-services' to start all services$(NC)"

start-services: ## Start all development services
	@echo "$(BLUE)Starting BetBet development services...$(NC)"
	@$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)All services started!$(NC)"
	@echo "$(YELLOW)Services available at:$(NC)"
	@echo "  Gaming Engine:    http://localhost:8001"
	@echo "  Custom Betting:   http://localhost:8002"  
	@echo "  Leaderboards:     http://localhost:8006"
	@echo "  WebSocket:        ws://localhost:8080"
	@echo "  Frontend:         http://localhost:3000"
	@echo "  PostgreSQL:       localhost:5432"
	@echo "  Redis:            localhost:6379"
	@echo "  Prometheus:       http://localhost:9090"
	@echo "  Grafana:          http://localhost:3001 (admin/admin)"
	@echo "  PgAdmin:          http://localhost:5050"
	@echo "  Redis Commander:  http://localhost:8081"

stop-services: ## Stop all development services
	@echo "$(BLUE)Stopping BetBet development services...$(NC)"
	@$(DOCKER_COMPOSE) down
	@echo "$(GREEN)All services stopped!$(NC)"

restart-services: ## Restart all development services
	@echo "$(BLUE)Restarting BetBet development services...$(NC)"
	@$(DOCKER_COMPOSE) down
	@$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)All services restarted!$(NC)"

clean: ## Clean up containers, volumes, and networks
	@echo "$(RED)Cleaning up development environment...$(NC)"
	@$(DOCKER_COMPOSE) down -v --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)Cleanup complete!$(NC)"

# Database Management
db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	@cd services/gaming-engine && alembic upgrade head
	@cd services/custom-betting && alembic upgrade head
	@cd services/leaderboards && alembic upgrade head
	@echo "$(GREEN)Database migrations complete!$(NC)"

db-seed: ## Seed database with development data
	@echo "$(BLUE)Seeding database with development data...$(NC)"
	@$(PYTHON) scripts/database/seed-data.py --environment=development
	@echo "$(GREEN)Database seeding complete!$(NC)"

db-reset: ## Reset database to clean state
	@echo "$(RED)Resetting database...$(NC)"
	@$(DOCKER_COMPOSE) down postgres
	@docker volume rm betbet_postgres_data || true
	@$(DOCKER_COMPOSE) up -d postgres
	@sleep 10
	@make db-migrate
	@make db-seed
	@echo "$(GREEN)Database reset complete!$(NC)"

# Testing
test-all: ## Run all tests (unit, integration, e2e)
	@echo "$(BLUE)Running all tests...$(NC)"
	@make test-unit
	@make test-integration
	@echo "$(GREEN)All tests completed!$(NC)"

test-unit: ## Run unit tests
	@echo "$(BLUE)Running unit tests...$(NC)"
	@cd services/gaming-engine && pytest tests/unit/ -v --cov=app --cov-report=term-missing
	@cd services/custom-betting && pytest tests/unit/ -v --cov=app --cov-report=term-missing
	@cd services/leaderboards && pytest tests/unit/ -v --cov=app --cov-report=term-missing
	@cd frontend/web && npm test -- --coverage --watchAll=false
	@echo "$(GREEN)Unit tests completed!$(NC)"

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	@cd tests/integration && pytest -v
	@echo "$(GREEN)Integration tests completed!$(NC)"

test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running end-to-end tests...$(NC)"
	@cd tests/e2e && npm test
	@echo "$(GREEN)End-to-end tests completed!$(NC)"

# Code Quality
lint: ## Run linters on all code
	@echo "$(BLUE)Running linters...$(NC)"
	@cd services/gaming-engine && flake8 app/ && mypy app/
	@cd services/custom-betting && flake8 app/ && mypy app/
	@cd services/leaderboards && flake8 app/ && mypy app/
	@cd frontend/web && npm run lint
	@echo "$(GREEN)Linting completed!$(NC)"

format: ## Format all code
	@echo "$(BLUE)Formatting code...$(NC)"
	@cd services/gaming-engine && black app/ && isort app/
	@cd services/custom-betting && black app/ && isort app/
	@cd services/leaderboards && black app/ && isort app/
	@cd frontend/web && npm run format
	@echo "$(GREEN)Code formatting completed!$(NC)"

# Monitoring and Debugging
logs: ## Show logs from all services
	@$(DOCKER_COMPOSE) logs -f

logs-service: ## Show logs from specific service (make logs-service SERVICE=gaming-engine)
	@$(DOCKER_COMPOSE) logs -f $(SERVICE)

health-check: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(NC)"
	@$(PYTHON) scripts/utilities/health-check.py
	@echo "$(GREEN)Health check completed!$(NC)"

# Development Utilities
generate-module: ## Generate new module (make generate-module MODULE_NAME=new-feature)
	@echo "$(BLUE)Generating new module: $(MODULE_NAME)$(NC)"
	@$(PYTHON) scripts/ai-agents/generate-module.py --name=$(MODULE_NAME)
	@echo "$(GREEN)Module $(MODULE_NAME) generated!$(NC)"

api-docs: ## Generate API documentation
	@echo "$(BLUE)Generating API documentation...$(NC)"
	@$(PYTHON) scripts/utilities/api-docs-generator.py
	@echo "$(GREEN)API documentation generated!$(NC)"

performance-test: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(NC)"
	@$(PYTHON) scripts/testing/load-tests.py --duration=60 --users=100
	@echo "$(GREEN)Performance tests completed!$(NC)"

security-scan: ## Run security scans
	@echo "$(BLUE)Running security scans...$(NC)"
	@$(PYTHON) scripts/testing/security-scan.py
	@echo "$(GREEN)Security scanning completed!$(NC)"

# Deployment
build-all: ## Build all Docker images
	@echo "$(BLUE)Building all Docker images...$(NC)"
	@$(DOCKER_COMPOSE) build
	@echo "$(GREEN)All images built!$(NC)"

deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(NC)"
	@$(PYTHON) scripts/deployment/deploy-staging.sh
	@echo "$(GREEN)Staging deployment completed!$(NC)"

deploy-production: ## Deploy to production environment
	@echo "$(RED)Deploying to production...$(NC)"
	@echo "$(YELLOW)This will deploy to production. Are you sure? [y/N]$(NC)"
	@read -r REPLY; if [ "$$REPLY" = "y" ] || [ "$$REPLY" = "Y" ]; then \
		$(PYTHON) scripts/deployment/deploy-production.sh; \
		echo "$(GREEN)Production deployment completed!$(NC)"; \
	else \
		echo "$(YELLOW)Production deployment cancelled.$(NC)"; \
	fi

# AI Development Workflow
coord-status: ## Show AI coordination status
	@echo "$(BLUE)BetBet AI Coordination Status$(NC)"
	@echo "=============================="
	@cat ai-development/coordination/master-coordinator.md | grep -A 20 "Current Project Status"

coord-handoff: ## Execute AI agent handoff (make coord-handoff FROM=master TO=claude-db)
	@echo "$(BLUE)Executing handoff: $(FROM) → $(TO)$(NC)"
	@$(PYTHON) scripts/ai-agents/coordinate-development.py --handoff --from=$(FROM) --to=$(TO)

coord-update: ## Update coordination status
	@echo "$(BLUE)Updating coordination status...$(NC)"
	@$(PYTHON) scripts/ai-agents/coordinate-development.py --update-status

# Quality Gates
quality-check: ## Run all quality gate checks
	@echo "$(BLUE)Running quality gate checks...$(NC)"
	@make lint
	@make test-unit
	@make test-integration
	@make security-scan
	@$(PYTHON) scripts/ai-agents/quality-check.py
	@echo "$(GREEN)All quality gates passed!$(NC)"

template-validate: ## Validate template compliance
	@echo "$(BLUE)Validating template compliance...$(NC)"
	@$(PYTHON) scripts/ai-agents/validate-patterns.py
	@echo "$(GREEN)Template validation completed!$(NC)"

# Environment Information
info: ## Show environment information
	@echo "$(BLUE)BetBet Development Environment Info$(NC)"
	@echo "===================================="
	@echo "Docker version: $$(docker --version)"
	@echo "Docker Compose version: $$(docker-compose --version)"
	@echo "Python version: $$( python3 --version)"
	@echo "Node version: $$(node --version)"
	@echo "NPM version: $$(npm --version)"
	@echo ""
	@echo "$(YELLOW)Current containers:$(NC)"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"