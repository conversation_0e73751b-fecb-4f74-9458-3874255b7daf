-- BetBet Sports Analysis Database Schema
-- ======================================

\c betbet_sports;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Sports data sources and API configurations
CREATE TABLE data_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    provider VARCHAR(100) NOT NULL,
    api_endpoint TEXT,
    api_key_hash VARCHAR(255),
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    is_active BOOLEAN DEFAULT true,
    last_sync TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sports statistics and data
CREATE TABLE sport_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sport_id UUID, -- References sport from betting DB
    league_id UUID, -- References league from betting DB
    team_id UUID, -- References team from betting DB
    player_id UUID, -- References player (external)
    event_id UUID, -- References event from betting DB
    stat_type VARCHAR(100) NOT NULL, -- 'goals', 'assists', 'shots', 'possession', etc.
    stat_category VARCHAR(50) NOT NULL, -- 'team', 'player', 'match'
    stat_value DECIMAL(15,4) NOT NULL,
    stat_period VARCHAR(50), -- 'season', 'match', 'half', 'quarter'
    date_recorded DATE,
    match_minute INTEGER, -- For in-game stats
    data_source_id UUID REFERENCES data_sources(id),
    raw_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Team performance metrics and analytics
CREATE TABLE team_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID NOT NULL, -- References team from betting DB
    season VARCHAR(50) NOT NULL,
    matches_played INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    draws INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    goals_for INTEGER DEFAULT 0,
    goals_against INTEGER DEFAULT 0,
    goal_difference INTEGER DEFAULT 0,
    points INTEGER DEFAULT 0,
    home_record JSONB, -- {wins, draws, losses, goals_for, goals_against}
    away_record JSONB,
    form_last_5 VARCHAR(5), -- 'WWDLL' format
    form_last_10 VARCHAR(10),
    average_possession DECIMAL(5,2),
    average_shots_per_game DECIMAL(5,2),
    average_corners_per_game DECIMAL(5,2),
    clean_sheets INTEGER DEFAULT 0,
    goals_per_game DECIMAL(5,4) DEFAULT 0.0000,
    goals_conceded_per_game DECIMAL(5,4) DEFAULT 0.0000,
    win_percentage DECIMAL(5,4) DEFAULT 0.0000,
    strength_rating DECIMAL(5,2) DEFAULT 0.00, -- 0-100 scale
    attack_strength DECIMAL(5,2) DEFAULT 0.00,
    defense_strength DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(team_id, season)
);

-- Player performance and statistics
CREATE TABLE player_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id UUID NOT NULL, -- External player ID
    team_id UUID NOT NULL, -- References team from betting DB
    season VARCHAR(50) NOT NULL,
    position VARCHAR(50),
    appearances INTEGER DEFAULT 0,
    starts INTEGER DEFAULT 0,
    minutes_played INTEGER DEFAULT 0,
    goals INTEGER DEFAULT 0,
    assists INTEGER DEFAULT 0,
    yellow_cards INTEGER DEFAULT 0,
    red_cards INTEGER DEFAULT 0,
    shots_total INTEGER DEFAULT 0,
    shots_on_target INTEGER DEFAULT 0,
    pass_accuracy DECIMAL(5,2) DEFAULT 0.00,
    dribbles_successful INTEGER DEFAULT 0,
    tackles_successful INTEGER DEFAULT 0,
    interceptions INTEGER DEFAULT 0,
    blocks INTEGER DEFAULT 0,
    clearances INTEGER DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    market_value DECIMAL(15,2), -- Player market value
    injury_status VARCHAR(50) DEFAULT 'fit', -- 'fit', 'injured', 'doubtful'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(player_id, team_id, season)
);

-- Match analysis and predictions
CREATE TABLE match_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL, -- References event from betting DB
    home_team_id UUID NOT NULL,
    away_team_id UUID NOT NULL,
    analysis_type VARCHAR(50) NOT NULL, -- 'pre_match', 'live', 'post_match'
    predicted_outcome VARCHAR(20), -- 'home_win', 'draw', 'away_win'
    confidence_score DECIMAL(5,4) CHECK (confidence_score >= 0.0000 AND confidence_score <= 1.0000),
    predicted_score_home INTEGER,
    predicted_score_away INTEGER,
    over_under_2_5 VARCHAR(10), -- 'over', 'under'
    both_teams_score BOOLEAN,
    home_win_probability DECIMAL(5,4),
    draw_probability DECIMAL(5,4),
    away_win_probability DECIMAL(5,4),
    key_factors TEXT[],
    weather_conditions JSONB,
    referee_stats JSONB,
    head_to_head JSONB,
    form_comparison JSONB,
    injury_report JSONB,
    model_version VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Live match tracking and real-time data
CREATE TABLE live_match_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL, -- References event from betting DB
    minute INTEGER NOT NULL,
    period VARCHAR(20) NOT NULL, -- 'first_half', 'half_time', 'second_half', 'extra_time', 'penalties'
    home_score INTEGER DEFAULT 0,
    away_score INTEGER DEFAULT 0,
    match_events JSONB, -- Goals, cards, substitutions, etc.
    live_stats JSONB, -- Possession, shots, corners, etc.
    momentum_home DECIMAL(5,4) DEFAULT 0.5000, -- 0-1 scale
    momentum_away DECIMAL(5,4) DEFAULT 0.5000,
    injury_time INTEGER DEFAULT 0,
    data_source_id UUID REFERENCES data_sources(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Weather data for matches
CREATE TABLE weather_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL, -- References event from betting DB
    venue_location POINT, -- Geographic coordinates
    temperature_celsius DECIMAL(5,2),
    humidity_percentage DECIMAL(5,2),
    wind_speed_kmh DECIMAL(5,2),
    wind_direction INTEGER, -- Degrees 0-360
    precipitation_mm DECIMAL(5,2),
    weather_condition VARCHAR(50), -- 'sunny', 'cloudy', 'rainy', 'snowy'
    visibility_km DECIMAL(5,2),
    pressure_hpa DECIMAL(7,2),
    forecast_accuracy DECIMAL(5,4),
    data_source_id UUID REFERENCES data_sources(id),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Historical trends and patterns
CREATE TABLE trend_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_type VARCHAR(100) NOT NULL, -- 'team_form', 'head_to_head', 'seasonal_pattern'
    entity_type VARCHAR(50) NOT NULL, -- 'team', 'league', 'player', 'matchup'
    entity_id UUID NOT NULL, -- ID of the entity being analyzed
    trend_description TEXT NOT NULL,
    pattern_data JSONB NOT NULL,
    confidence_level DECIMAL(5,4) NOT NULL,
    sample_size INTEGER NOT NULL,
    time_period VARCHAR(50) NOT NULL,
    significance_score DECIMAL(5,4), -- Statistical significance
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI model performance and tracking
CREATE TABLE model_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    prediction_type VARCHAR(50) NOT NULL, -- 'match_outcome', 'total_goals', 'btts'
    total_predictions INTEGER DEFAULT 0,
    correct_predictions INTEGER DEFAULT 0,
    accuracy DECIMAL(5,4) DEFAULT 0.0000,
    precision_score DECIMAL(5,4) DEFAULT 0.0000,
    recall_score DECIMAL(5,4) DEFAULT 0.0000,
    f1_score DECIMAL(5,4) DEFAULT 0.0000,
    roc_auc DECIMAL(5,4) DEFAULT 0.0000,
    log_loss DECIMAL(8,6) DEFAULT 0.000000,
    calibration_score DECIMAL(5,4) DEFAULT 0.0000,
    profit_loss DECIMAL(15,2) DEFAULT 0.00, -- If used for betting
    roi DECIMAL(8,4) DEFAULT 0.0000,
    evaluation_period VARCHAR(50),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Machine learning features for models
CREATE TABLE ml_features (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL, -- References event from betting DB
    home_team_id UUID NOT NULL,
    away_team_id UUID NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_value DECIMAL(15,6) NOT NULL,
    feature_category VARCHAR(50), -- 'team_stats', 'head_to_head', 'form', 'market'
    importance_score DECIMAL(5,4), -- Feature importance from ML models
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_sport_statistics_sport_id ON sport_statistics(sport_id);
CREATE INDEX idx_sport_statistics_league_id ON sport_statistics(league_id);
CREATE INDEX idx_sport_statistics_team_id ON sport_statistics(team_id);
CREATE INDEX idx_sport_statistics_event_id ON sport_statistics(event_id);
CREATE INDEX idx_sport_statistics_stat_type ON sport_statistics(stat_type);
CREATE INDEX idx_sport_statistics_date_recorded ON sport_statistics(date_recorded);

CREATE INDEX idx_team_analytics_team_id ON team_analytics(team_id);
CREATE INDEX idx_team_analytics_season ON team_analytics(season);

CREATE INDEX idx_player_analytics_player_id ON player_analytics(player_id);
CREATE INDEX idx_player_analytics_team_id ON player_analytics(team_id);
CREATE INDEX idx_player_analytics_season ON player_analytics(season);

CREATE INDEX idx_match_analysis_event_id ON match_analysis(event_id);
CREATE INDEX idx_match_analysis_analysis_type ON match_analysis(analysis_type);
CREATE INDEX idx_match_analysis_confidence_score ON match_analysis(confidence_score);

CREATE INDEX idx_live_match_data_event_id ON live_match_data(event_id);
CREATE INDEX idx_live_match_data_created_at ON live_match_data(created_at);

CREATE INDEX idx_weather_data_event_id ON weather_data(event_id);
CREATE INDEX idx_weather_data_recorded_at ON weather_data(recorded_at);

CREATE INDEX idx_trend_analysis_entity_type ON trend_analysis(entity_type);
CREATE INDEX idx_trend_analysis_entity_id ON trend_analysis(entity_id);
CREATE INDEX idx_trend_analysis_trend_type ON trend_analysis(trend_type);
CREATE INDEX idx_trend_analysis_is_active ON trend_analysis(is_active);

CREATE INDEX idx_model_performance_model_name ON model_performance(model_name);
CREATE INDEX idx_model_performance_prediction_type ON model_performance(prediction_type);
CREATE INDEX idx_model_performance_accuracy ON model_performance(accuracy);

CREATE INDEX idx_ml_features_event_id ON ml_features(event_id);
CREATE INDEX idx_ml_features_feature_name ON ml_features(feature_name);
CREATE INDEX idx_ml_features_feature_category ON ml_features(feature_category);

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_data_sources_updated_at 
    BEFORE UPDATE ON data_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_analytics_updated_at 
    BEFORE UPDATE ON team_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_player_analytics_updated_at 
    BEFORE UPDATE ON player_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_match_analysis_updated_at 
    BEFORE UPDATE ON match_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trend_analysis_updated_at 
    BEFORE UPDATE ON trend_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();