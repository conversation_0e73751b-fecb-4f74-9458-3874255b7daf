-- BetBet Platform Database Initialization Script
-- ===============================================
-- Creates schemas for each service within betbet_db database

-- Connect to betbet_db (this is already the default database from environment)

-- Create schemas for each service
CREATE SCHEMA IF NOT EXISTS gaming;
CREATE SCHEMA IF NOT EXISTS betting;  
CREATE SCHEMA IF NOT EXISTS trading;
CREATE SCHEMA IF NOT EXISTS experts;
CREATE SCHEMA IF NOT EXISTS sports;
CREATE SCHEMA IF NOT EXISTS leaderboards;
CREATE SCHEMA IF NOT EXISTS wallet;

-- Grant all privileges on schemas to postgres user
<PERSON><PERSON><PERSON> ALL ON SCHEMA gaming TO postgres;
GRANT ALL ON SCHEMA betting TO postgres;
GRANT ALL ON SCHEMA trading TO postgres;
GRANT ALL ON SCHEMA experts TO postgres;
GRANT ALL ON SCHEMA sports TO postgres;
GRANT ALL ON SCHEMA leaderboards TO postgres;
GRANT ALL ON SCHEMA wallet TO postgres;