-- BetBet Platform Sample Data
-- ===========================
-- Insert initial/sample data for development and testing

-- Core platform data
\c betbet_platform;

-- Insert sample users
INSERT INTO users (id, email, username, password_hash, first_name, last_name, role, status, email_verified)
VALUES 
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'admin_user', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewCm6gKdOGhO8.6u', 'Admin', 'User', 'super_admin', 'active', true),
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'john_trader', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewCm6gKdOGhO8.6u', '<PERSON>', '<PERSON>', 'user', 'active', true),
    ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'sarah_expert', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewCm6gKdOGhO8.6u', 'Sarah', 'Johnson', 'expert', 'active', true),
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'mike_vip', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewCm6gKdOGhO8.6u', 'Mike', 'Wilson', 'vip', 'active', true),
    ('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'lisa_admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewCm6gKdOGhO8.6u', 'Lisa', 'Brown', 'admin', 'active', true);

-- Insert user balances
INSERT INTO user_balances (user_id, currency, available_balance, total_deposited)
VALUES 
    ('550e8400-e29b-41d4-a716-************', 'USD', 0.00, 0.00),
    ('550e8400-e29b-41d4-a716-************', 'USD', 5000.00, 5000.00),
    ('550e8400-e29b-41d4-a716-446655440003', 'USD', 3500.00, 5000.00),
    ('550e8400-e29b-41d4-a716-************', 'USD', 15000.00, 20000.00),
    ('550e8400-e29b-41d4-a716-446655440005', 'USD', 0.00, 0.00);

-- Insert system settings
INSERT INTO system_settings (key, value, description, category, is_public)
VALUES 
    ('platform_name', '"BetBet"', 'Platform name', 'general', true),
    ('platform_version', '"1.0.0"', 'Platform version', 'general', true),
    ('maintenance_mode', 'false', 'Maintenance mode flag', 'system', false),
    ('max_bet_amount', '10000.00', 'Maximum bet amount in USD', 'betting', false),
    ('min_bet_amount', '1.00', 'Minimum bet amount in USD', 'betting', true),
    ('default_commission_rate', '0.05', 'Default commission rate (5%)', 'betting', false),
    ('jwt_expiry_hours', '24', 'JWT token expiry in hours', 'auth', false);

-- Gaming data
\c betbet_gaming;

-- Insert game categories
INSERT INTO game_categories (id, name, description, sort_order)
VALUES 
    ('660e8400-e29b-41d4-a716-************', 'Slots', 'Slot machine games', 1),
    ('660e8400-e29b-41d4-a716-************', 'Table Games', 'Blackjack, Roulette, Poker', 2),
    ('660e8400-e29b-41d4-a716-446655440003', 'Live Casino', 'Live dealer games', 3),
    ('660e8400-e29b-41d4-a716-************', 'Tournaments', 'Competitive gaming events', 4);

-- Insert sample games
INSERT INTO games (id, category_id, name, description, game_type, min_bet, max_bet, rtp, house_edge)
VALUES 
    ('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Lucky Fortune', 'Classic fruit slot machine', 'slot', 0.10, 100.00, 0.9600, 0.0400),
    ('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Blackjack Classic', 'Traditional blackjack game', 'table', 1.00, 1000.00, 0.9950, 0.0050),
    ('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-************', 'European Roulette', 'Single zero roulette', 'table', 1.00, 500.00, 0.9730, 0.0270),
    ('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440003', 'Live Blackjack VIP', 'High stakes live blackjack', 'live', 25.00, 5000.00, 0.9950, 0.0050);

-- Betting data
\c betbet_betting;

-- Insert sports
INSERT INTO sports (id, name, slug, description)
VALUES 
    ('880e8400-e29b-41d4-a716-************', 'Football', 'football', 'Association Football/Soccer'),
    ('880e8400-e29b-41d4-a716-************', 'Basketball', 'basketball', 'Professional Basketball'),
    ('880e8400-e29b-41d4-a716-446655440003', 'Tennis', 'tennis', 'Professional Tennis'),
    ('880e8400-e29b-41d4-a716-************', 'American Football', 'american-football', 'NFL and College Football');

-- Insert leagues
INSERT INTO leagues (id, sport_id, name, slug, country, level)
VALUES 
    ('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Premier League', 'premier-league', 'England', 1),
    ('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'La Liga', 'la-liga', 'Spain', 1),
    ('990e8400-e29b-41d4-a716-446655440003', '880e8400-e29b-41d4-a716-************', 'NBA', 'nba', 'USA', 1),
    ('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'NFL', 'nfl', 'USA', 1);

-- Insert teams
INSERT INTO teams (id, name, short_name, slug, sport_id, country)
VALUES 
    ('aa0e8400-e29b-41d4-a716-************', 'Manchester United', 'MAN UTD', 'manchester-united', '880e8400-e29b-41d4-a716-************', 'England'),
    ('aa0e8400-e29b-41d4-a716-************', 'Liverpool FC', 'LIV', 'liverpool', '880e8400-e29b-41d4-a716-************', 'England'),
    ('aa0e8400-e29b-41d4-a716-446655440003', 'Real Madrid', 'RM', 'real-madrid', '880e8400-e29b-41d4-a716-************', 'Spain'),
    ('aa0e8400-e29b-41d4-a716-************', 'Barcelona', 'BAR', 'barcelona', '880e8400-e29b-41d4-a716-************', 'Spain'),
    ('aa0e8400-e29b-41d4-a716-446655440005', 'Los Angeles Lakers', 'LAL', 'lakers', '880e8400-e29b-41d4-a716-************', 'USA'),
    ('aa0e8400-e29b-41d4-a716-446655440006', 'Boston Celtics', 'BOS', 'celtics', '880e8400-e29b-41d4-a716-************', 'USA');

-- Insert sample events
INSERT INTO events (id, sport_id, league_id, home_team_id, away_team_id, name, start_time, status)
VALUES 
    ('bb0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'aa0e8400-e29b-41d4-a716-************', 'aa0e8400-e29b-41d4-a716-************', 'Manchester United vs Liverpool', '2024-02-01 15:30:00+00', 'scheduled'),
    ('bb0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'aa0e8400-e29b-41d4-a716-446655440003', 'aa0e8400-e29b-41d4-a716-************', 'Real Madrid vs Barcelona', '2024-02-03 20:00:00+00', 'scheduled'),
    ('bb0e8400-e29b-41d4-a716-446655440003', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-446655440003', 'aa0e8400-e29b-41d4-a716-446655440005', 'aa0e8400-e29b-41d4-a716-446655440006', 'Lakers vs Celtics', '2024-02-02 02:00:00+00', 'scheduled');

-- Insert sample custom markets
INSERT INTO custom_markets (id, creator_user_id, title, description, event_criteria, deadline, verification_deadline, status)
VALUES 
    ('cc0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440003', 'Bitcoin will reach $100k by end of 2024', 'Prediction market on Bitcoin reaching $100,000', 'Bitcoin (BTC) must reach or exceed $100,000 USD on any major exchange before Dec 31, 2024', '2024-12-31 23:59:59+00', '2025-01-07 23:59:59+00', 'open'),
    ('cc0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Tesla stock performance Q4 2024', 'Will TSLA outperform S&P 500 in Q4 2024?', 'Tesla stock must outperform S&P 500 index during Q4 2024', '2024-12-31 23:59:59+00', '2025-01-07 23:59:59+00', 'open');

-- Insert custom market outcomes
INSERT INTO custom_market_outcomes (id, market_id, outcome_text, current_odds, sort_order)
VALUES 
    ('dd0e8400-e29b-41d4-a716-************', 'cc0e8400-e29b-41d4-a716-************', 'Yes, BTC reaches $100k', 2.50, 1),
    ('dd0e8400-e29b-41d4-a716-************', 'cc0e8400-e29b-41d4-a716-************', 'No, BTC stays below $100k', 1.80, 2),
    ('dd0e8400-e29b-41d4-a716-446655440003', 'cc0e8400-e29b-41d4-a716-************', 'TSLA outperforms S&P 500', 1.90, 1),
    ('dd0e8400-e29b-41d4-a716-************', 'cc0e8400-e29b-41d4-a716-************', 'TSLA underperforms S&P 500', 2.10, 2);

-- Experts data
\c betbet_experts;

-- Insert expert profiles
INSERT INTO experts (id, user_id, display_name, bio, specialties, tier, rating, total_predictions, correct_predictions, accuracy_rate)
VALUES 
    ('ee0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440003', 'Sarah the Predictor', 'Professional sports analyst with 10+ years experience', ARRAY['football', 'basketball'], 'professional', 4.2, 150, 98, 0.6533),
    ('ee0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Mike Market Master', 'Trading expert specializing in crypto and stocks', ARRAY['crypto', 'stocks', 'trading'], 'semi_pro', 3.8, 75, 52, 0.6933);

-- Insert sample predictions
INSERT INTO predictions (id, expert_id, title, description, prediction_text, category, confidence_level, recommended_odds, status)
VALUES 
    ('ff0e8400-e29b-41d4-a716-************', 'ee0e8400-e29b-41d4-a716-************', 'Manchester United to Win', 'Strong prediction for United vs Liverpool match', 'Manchester United will win against Liverpool based on current form and home advantage', 'football', 4, 2.10, 'active'),
    ('ff0e8400-e29b-41d4-a716-************', 'ee0e8400-e29b-41d4-a716-************', 'Bitcoin Bull Run Incoming', 'Crypto market analysis for Bitcoin', 'Bitcoin showing strong technical indicators for upward movement', 'crypto', 3, 1.75, 'active');

-- Trading data
\c betbet_trading;

-- Insert sample trading accounts
INSERT INTO trading_accounts (id, user_id, balance, lifetime_pnl, total_traded_volume)
VALUES 
    ('1100e840-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 5000.00, 250.00, 25000.00),
    ('1100e840-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 15000.00, 1250.00, 75000.00);

-- Sports analysis data
\c betbet_sports;

-- Insert data sources
INSERT INTO data_sources (id, name, provider, is_active)
VALUES 
    ('1200e840-e29b-41d4-a716-************', 'SportsAPI Pro', 'SportsData Inc.', true),
    ('1200e840-e29b-41d4-a716-************', 'Weather Service', 'WeatherAPI Ltd.', true);

-- Leaderboards data
\c betbet_leaderboards;

-- Insert leaderboard categories
INSERT INTO leaderboard_categories (id, name, description, category_type, sort_order)
VALUES 
    ('1300e840-e29b-41d4-a716-************', 'Betting Champions', 'Top betting performers', 'betting', 1),
    ('1300e840-e29b-41d4-a716-************', 'Gaming Masters', 'Best gaming performance', 'gaming', 2),
    ('1300e840-e29b-41d4-a716-446655440003', 'Trading Experts', 'Top trading performers', 'trading', 3),
    ('1300e840-e29b-41d4-a716-************', 'Prediction Gurus', 'Best expert predictors', 'expert', 4);

-- Insert sample leaderboards
INSERT INTO leaderboards (id, category_id, name, description, metric_type, time_period, max_participants)
VALUES 
    ('1400e840-e29b-41d4-a716-************', '1300e840-e29b-41d4-a716-************', 'Weekly Winners', 'Top weekly betting profits', 'total_winnings', 'weekly', 100),
    ('1400e840-e29b-41d4-a716-************', '1300e840-e29b-41d4-a716-************', 'Monthly Gaming Champions', 'Best monthly gaming performance', 'total_winnings', 'monthly', 50),
    ('1400e840-e29b-41d4-a716-446655440003', '1300e840-e29b-41d4-a716-446655440003', 'Trading Hall of Fame', 'All-time best traders', 'roi', 'all_time', 25);

-- Insert sample achievements
INSERT INTO achievements (id, name, description, category, type, criteria, points_value)
VALUES 
    ('1500e840-e29b-41d4-a716-************', 'First Bet', 'Place your first bet', 'betting', 'milestone', '{"first_bet": true}', 10),
    ('1500e840-e29b-41d4-a716-************', 'Winning Streak', 'Win 5 bets in a row', 'betting', 'streak', '{"consecutive_wins": 5}', 50),
    ('1500e840-e29b-41d4-a716-446655440003', 'High Roller', 'Place a bet worth $1000 or more', 'betting', 'milestone', '{"min_bet_amount": 1000}', 100),
    ('1500e840-e29b-41d4-a716-************', 'Game Explorer', 'Try 10 different games', 'gaming', 'participation', '{"unique_games": 10}', 25),
    ('1500e840-e29b-41d4-a716-446655440005', 'Expert Follower', 'Follow your first expert', 'expert', 'participation', '{"first_follow": true}', 15);

-- Insert user points
INSERT INTO user_points (user_id, total_points, lifetime_points)
VALUES 
    ('550e8400-e29b-41d4-a716-************', 85, 85),
    ('550e8400-e29b-41d4-a716-446655440003', 150, 150),
    ('550e8400-e29b-41d4-a716-************', 275, 275);