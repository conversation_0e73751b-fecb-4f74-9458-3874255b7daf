-- Chess Game Configuration for BetBet Gaming Engine
-- Uses existing gaming_engine schema to support chess
-- All chess data is stored using the generic game_states and events tables

-- First check if Chess game already exists
DO $$
DECLARE
    chess_game_id UUID;
BEGIN
    IF NOT EXISTS (SELECT 1 FROM gaming_engine.games WHERE name = 'Chess') THEN
        -- Chess-specific game configuration
        INSERT INTO gaming_engine.games (
            name, slug, category, description, min_players, max_players, is_active, game_config
        ) VALUES (
            'Chess',
            'chess',
            'strategy',
            'The classic strategy game with multiple variants and time controls',
            2, 2, true,
            '{
                "game_type": "chess",
                "variants": ["standard", "chess960", "king_of_the_hill", "three_check", "antichess"],
                "time_controls": {
                    "bullet": {"time": 60, "increment": 0},
                    "blitz": {"time": 300, "increment": 3},
                    "rapid": {"time": 900, "increment": 10},
                    "classical": {"time": 1800, "increment": 30},
                    "correspondence": {"time": 86400, "increment": 0}
                },
                "rating_categories": ["bullet", "blitz", "rapid", "classical", "chess960"],
                "spectator_betting": true,
                "ai_analysis": true,
                "move_validation": "server_side",
                "anti_cheat": "advanced",
                "move_time_limits": {
                    "bullet": 10,
                    "blitz": 30,
                    "rapid": 60,
                    "classical": 180
                },
                "game_state_structure": {
                    "fen": "string",
                    "moves": "array",
                    "captured_pieces": "object",
                    "time_remaining": "object",
                    "draw_offers": "array",
                    "opening_name": "string",
                    "evaluation": "object"
                }
            }'::jsonb
        ) RETURNING id INTO chess_game_id;
        
        RAISE NOTICE 'Chess game created with ID: %', chess_game_id;
    ELSE
        RAISE NOTICE 'Chess game already exists in the games table';
    END IF;
END $$;

-- Chess will use the existing gaming_engine tables:
-- 1. game_sessions - for individual chess matches
-- 2. game_states - for storing chess positions (FEN), moves, time, etc.
-- 3. events - for move history and game events
-- 4. session_participants - for player information
-- 5. spectator_bets - for live betting on chess games
-- 6. tournaments - for chess tournaments
-- 7. user_bets - for player statistics and ratings

-- Example of how chess data will be stored:
-- game_states.state will contain:
-- {
--   "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
--   "moves": ["e4", "e5", "Nf3", "Nc6"],
--   "white_time": 300,
--   "black_time": 295,
--   "current_turn": "black",
--   "last_move": "Nc6",
--   "captured_pieces": {"white": [], "black": []},
--   "is_check": false,
--   "is_checkmate": false,
--   "draw_offered_by": null,
--   "opening": "Italian Game",
--   "evaluation": {"score": 0.1, "depth": 20}
-- }

-- events.event_data will contain chess moves:
-- {
--   "move_uci": "e2e4",
--   "move_san": "e4",
--   "time_taken": 5000,
--   "position_before": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
--   "position_after": "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
--   "is_capture": false,
--   "is_check": false,
--   "is_castle": false,
--   "promotion": null
-- }