-- Create unified user performance table for leaderboards
-- ======================================================

-- Create the unified_user_performance table
CREATE TABLE IF NOT EXISTS leaderboards.unified_user_performance (
    user_id UUID PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
    gaming_stats JSONB DEFAULT '{}',
    betting_stats JSONB DEFAULT '{}',
    trading_performance JSONB DEFAULT '{}',
    analyst_metrics JSONB DEFAULT '{}',
    unified_platform_score DECIMAL(10,2) DEFAULT 0.0,
    platform_tier VARCHAR(20) DEFAULT 'Bronze',
    cross_platform_consistency_score DECIMAL(5,4) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_unified_user_performance_score 
ON leaderboards.unified_user_performance(unified_platform_score DESC);

CREATE INDEX IF NOT EXISTS idx_unified_user_performance_tier 
ON leaderboards.unified_user_performance(platform_tier);

CREATE INDEX IF NOT EXISTS idx_unified_user_performance_updated 
ON leaderboards.unified_user_performance(updated_at DESC);

-- Add comments for documentation
COMMENT ON TABLE leaderboards.unified_user_performance IS 'Unified performance metrics across all platform modules';
COMMENT ON COLUMN leaderboards.unified_user_performance.gaming_stats IS 'JSON object containing gaming performance metrics';
COMMENT ON COLUMN leaderboards.unified_user_performance.betting_stats IS 'JSON object containing betting performance metrics';
COMMENT ON COLUMN leaderboards.unified_user_performance.trading_performance IS 'JSON object containing trading performance metrics';
COMMENT ON COLUMN leaderboards.unified_user_performance.analyst_metrics IS 'JSON object containing analyst performance metrics';
COMMENT ON COLUMN leaderboards.unified_user_performance.unified_platform_score IS 'Calculated unified score across all modules';
COMMENT ON COLUMN leaderboards.unified_user_performance.platform_tier IS 'User tier: Bronze, Silver, Gold, Platinum, Diamond, Master';
COMMENT ON COLUMN leaderboards.unified_user_performance.cross_platform_consistency_score IS 'Consistency score across different modules (0-1)';

-- Insert some sample data for testing
INSERT INTO leaderboards.unified_user_performance (
    user_id,
    gaming_stats,
    betting_stats,
    trading_performance,
    analyst_metrics,
    unified_platform_score,
    platform_tier,
    cross_platform_consistency_score
)
SELECT
    u.id,
    ('{"chess": {"rating": ' || (1200 + (random() * 1200)::int) || ', "games_played": ' || (10 + (random() * 490)::int) || ', "wins": ' || (5 + (random() * 245)::int) || '}}')::jsonb,
    ('{"total_bets": ' || (20 + (random() * 480)::int) || ', "wins": ' || (10 + (random() * 240)::int) || ', "total_winnings": ' || (random() * 5000 - 1000)::decimal(10,2) || '}')::jsonb,
    ('{"total_trades": ' || (10 + (random() * 290)::int) || ', "total_pnl": ' || (random() * 10000 - 2000)::decimal(10,2) || ', "roi": ' || (random() * 0.8 - 0.2)::decimal(5,3) || '}')::jsonb,
    ('{"predictions_made": ' || (10 + (random() * 190)::int) || ', "accuracy": ' || (0.5 + random() * 0.35)::decimal(5,3) || ', "reputation_score": ' || (1.0 + random() * 4.0)::decimal(3,2) || '}')::jsonb,
    (random() * 8000 + 1000)::decimal(10,2),
    CASE
        WHEN random() < 0.4 THEN 'Bronze'
        WHEN random() < 0.65 THEN 'Silver'
        WHEN random() < 0.8 THEN 'Gold'
        WHEN random() < 0.9 THEN 'Platinum'
        WHEN random() < 0.97 THEN 'Diamond'
        ELSE 'Master'
    END,
    random()::decimal(5,4)
FROM public.users u
WHERE u.is_active = true
ON CONFLICT (user_id) DO NOTHING;
