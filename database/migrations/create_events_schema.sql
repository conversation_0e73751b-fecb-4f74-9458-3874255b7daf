-- Create events schema and tables
-- ================================

-- Create events schema
CREATE SCHEMA IF NOT EXISTS events;

-- Create platform_events table
CREATE TABLE IF NOT EXISTS events.platform_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL,
    user_id VARCHAR(255),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    data JSONB DEFAULT '{}',
    priority VARCHAR(20) DEFAULT 'medium',
    channels JSONB DEFAULT '["in_app"]',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_platform_events_user_id ON events.platform_events(user_id);
CREATE INDEX IF NOT EXISTS idx_platform_events_event_type ON events.platform_events(event_type);
CREATE INDEX IF NOT EXISTS idx_platform_events_created_at ON events.platform_events(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_platform_events_is_read ON events.platform_events(is_read);
CREATE INDEX IF NOT EXISTS idx_platform_events_expires_at ON events.platform_events(expires_at);

-- Create notification_preferences table
CREATE TABLE IF NOT EXISTS events.notification_preferences (
    user_id VARCHAR(255) PRIMARY KEY,
    email_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    event_types JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_activity_log table for tracking platform activities
CREATE TABLE IF NOT EXISTS events.user_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    module VARCHAR(50) NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for activity log
CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON events.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON events.user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_module ON events.user_activity_log(module);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_at ON events.user_activity_log(created_at DESC);

-- Create system_events table for platform-wide events
CREATE TABLE IF NOT EXISTS events.system_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) DEFAULT 'info',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    affected_services JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for system events
CREATE INDEX IF NOT EXISTS idx_system_events_event_type ON events.system_events(event_type);
CREATE INDEX IF NOT EXISTS idx_system_events_severity ON events.system_events(severity);
CREATE INDEX IF NOT EXISTS idx_system_events_resolved ON events.system_events(resolved);
CREATE INDEX IF NOT EXISTS idx_system_events_created_at ON events.system_events(created_at DESC);

-- Add comments for documentation
COMMENT ON SCHEMA events IS 'Event service schema for notifications and platform activities';
COMMENT ON TABLE events.platform_events IS 'User-specific platform events and notifications';
COMMENT ON TABLE events.notification_preferences IS 'User notification preferences and settings';
COMMENT ON TABLE events.user_activity_log IS 'Log of user activities across all platform modules';
COMMENT ON TABLE events.system_events IS 'System-wide events and alerts';

-- Insert some sample events for testing
INSERT INTO events.platform_events (event_type, user_id, title, description, data, priority, channels) VALUES
('user_registration', 'user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', 'Welcome to BetBet!', 'Your account has been successfully created. Start exploring our platform!', '{"welcome_bonus": 100}', 'high', '["in_app", "websocket"]'),
('achievement_unlocked', 'user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', 'First Login Achievement', 'Congratulations! You have unlocked the "First Steps" achievement.', '{"achievement_id": "first_login", "points": 50}', 'medium', '["in_app", "websocket"]'),
('system_maintenance', NULL, 'Scheduled Maintenance', 'Platform maintenance scheduled for tonight at 2 AM UTC.', '{"duration": "2 hours", "affected_services": ["trading", "betting"]}', 'medium', '["in_app", "websocket"]');

-- Insert sample notification preferences
INSERT INTO events.notification_preferences (user_id, email_enabled, push_enabled, in_app_enabled, event_types) VALUES
('user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', true, true, true, '["bet_won", "bet_lost", "achievement_unlocked", "tier_upgraded"]');

-- Insert sample activity log entries
INSERT INTO events.user_activity_log (user_id, activity_type, module, description, metadata) VALUES
('user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', 'login', 'auth', 'User logged in successfully', '{"login_method": "clerk", "device": "web"}'),
('user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', 'page_view', 'dashboard', 'User viewed dashboard', '{"page": "/dashboard", "session_duration": 120}'),
('user_30H2Dg6hkdPgDhF8O7lJk2WPRnR', 'page_view', 'leaderboard', 'User viewed leaderboard', '{"page": "/leaderboard", "view_type": "unified"}');

-- Insert sample system events
INSERT INTO events.system_events (event_type, severity, title, description, affected_services, metadata) VALUES
('service_startup', 'info', 'Event Service Started', 'Event service has been successfully started and is ready to handle notifications.', '["events"]', '{"version": "1.0.0", "startup_time": "2025-07-28T12:00:00Z"}'),
('database_migration', 'info', 'Database Schema Updated', 'Event service database schema has been updated with new tables.', '["events", "database"]', '{"migration_version": "001", "tables_created": 4}');
