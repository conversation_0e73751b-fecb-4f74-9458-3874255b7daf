-- Migration: Update gaming engine user_id columns from UUID to VARCHAR for Clerk compatibility
-- This allows storing Clerk user IDs like "user_2qIy5SgNvKI8DXpR8EvDVAyQQQI"

BEGIN;

-- Drop foreign key constraints that reference the UUID columns
ALTER TABLE gaming_engine.session_participants
DROP CONSTRAINT IF EXISTS session_participants_user_id_fkey;

ALTER TABLE gaming_engine.session_participants
DROP CONSTRAINT IF EXISTS session_participants_created_by_fkey;

ALTER TABLE gaming_engine.session_participants
DROP CONSTRAINT IF EXISTS session_participants_updated_by_fkey;

ALTER TABLE gaming_engine.tournament_participants
DROP CONSTRAINT IF EXISTS tournament_participants_user_id_fkey;

ALTER TABLE gaming_engine.user_bets
DROP CONSTRAINT IF EXISTS user_bets_user_id_fkey;

ALTER TABLE gaming_engine.spectator_bets
DROP CONSTRAINT IF EXISTS spectator_bets_user_id_fkey;

-- Drop unique constraint that includes user_id
ALTER TABLE gaming_engine.session_participants
DROP CONSTRAINT IF EXISTS session_participants_session_id_user_id_key;

-- Clear existing data (since we're changing from UUID to string)
TRUNCATE TABLE gaming_engine.session_participants CASCADE;
TRUNCATE TABLE gaming_engine.tournament_participants CASCADE;
TRUNCATE TABLE gaming_engine.user_bets CASCADE;
TRUNCATE TABLE gaming_engine.spectator_bets CASCADE;

-- Update session_participants table
ALTER TABLE gaming_engine.session_participants
ALTER COLUMN user_id TYPE VARCHAR(255);

ALTER TABLE gaming_engine.session_participants
ALTER COLUMN created_by TYPE VARCHAR(255);

ALTER TABLE gaming_engine.session_participants
ALTER COLUMN updated_by TYPE VARCHAR(255);

-- Update tournament_participants table
ALTER TABLE gaming_engine.tournament_participants
ALTER COLUMN user_id TYPE VARCHAR(255);

-- Update user_bets table (check if column exists first)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_schema = 'gaming_engine'
               AND table_name = 'user_bets'
               AND column_name = 'user_id') THEN
        ALTER TABLE gaming_engine.user_bets
        ALTER COLUMN user_id TYPE VARCHAR(255);
    END IF;
END $$;

-- Update spectator_bets table
ALTER TABLE gaming_engine.spectator_bets
ALTER COLUMN user_id TYPE VARCHAR(255);

-- Recreate unique constraint
ALTER TABLE gaming_engine.session_participants
ADD CONSTRAINT session_participants_session_id_user_id_key UNIQUE (session_id, user_id);

-- Add comments for documentation
COMMENT ON COLUMN gaming_engine.session_participants.user_id IS 'Clerk user ID (string format)';
COMMENT ON COLUMN gaming_engine.tournament_participants.user_id IS 'Clerk user ID (string format)';
COMMENT ON COLUMN gaming_engine.spectator_bets.user_id IS 'Clerk user ID (string format)';

COMMIT;
