-- Migration: Update wallet user_id columns from UUID to VARCHAR for Clerk compatibility
-- This allows storing Clerk user IDs like "user_2qIy5SgNvKI8DXpR8EvDVAyQQQI"

BEGIN;

-- Drop existing constraints and indexes that depend on the UUID columns
ALTER TABLE wallet.user_balances DROP CONSTRAINT IF EXISTS user_balances_user_id_key;
DROP INDEX IF EXISTS wallet.idx_user_balances_user_id;

ALTER TABLE wallet.transactions DROP INDEX IF EXISTS wallet.idx_transactions_user_id;

-- Update user_balances table
-- First, clear any existing data (since we're changing from UUID to string)
TRUNCATE TABLE wallet.user_balances CASCADE;
TRUNCATE TABLE wallet.transactions CASCADE;

-- Change user_id column type in user_balances
ALTER TABLE wallet.user_balances 
ALTER COLUMN user_id TYPE VARCHAR(255);

-- Change user_id column type in transactions
ALTER TABLE wallet.transactions 
ALTER COLUMN user_id TYPE VARCHAR(255);

-- Recreate constraints and indexes
ALTER TABLE wallet.user_balances 
ADD CONSTRAINT user_balances_user_id_key UNIQUE (user_id);

CREATE INDEX idx_user_balances_user_id ON wallet.user_balances(user_id);
CREATE INDEX idx_transactions_user_id ON wallet.transactions(user_id);

COMMIT;