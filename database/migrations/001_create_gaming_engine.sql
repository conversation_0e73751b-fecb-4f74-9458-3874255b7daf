-- BetBet Platform - Gaming Engine Migration Script
-- =================================================
-- Migration 001: Create Gaming Engine Schema and Tables
-- Timestamp: 2025-07-19

BEGIN;

-- Record migration start
INSERT INTO public.migration_log (migration_id, migration_name, started_at)
VALUES ('001', 'create_gaming_engine', NOW());

-- Create migration log table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.migration_log (
    id SERIAL PRIMARY KEY,
    migration_id VARCHAR(50) UNIQUE NOT NULL,
    migration_name VARCHAR(200) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    rollback_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'rolled_back'))
);

-- Execute the main schema creation
\i database/schemas/gaming_engine.sql

-- Execute indexes and security
\i database/schemas/gaming_engine_indexes.sql

-- Execute helper functions
\i database/schemas/gaming_engine_functions.sql

-- Create initial admin user if not exists
INSERT INTO public.users (
    id,
    username, 
    email, 
    password_hash,
    first_name,
    last_name,
    balance,
    is_active,
    is_verified,
    roles,
    permissions,
    created_by,
    updated_by
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK', -- password: admin123
    'System',
    'Administrator',
    0.00,
    true,
    true,
    ARRAY['admin', 'user'],
    ARRAY['admin', 'user_management', 'game_management', 'financial_management'],
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
) ON CONFLICT (id) DO NOTHING;

-- Create test user if not exists
INSERT INTO public.users (
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    balance,
    is_active,
    is_verified,
    created_by,
    updated_by
) VALUES (
    '00000000-0000-0000-0000-000000000002',
    'testuser',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK', -- password: admin123
    'Test',
    'User',
    1000.00,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
) ON CONFLICT (id) DO NOTHING;

-- Update migration status
UPDATE public.migration_log 
SET completed_at = NOW(), status = 'completed'
WHERE migration_id = '001';

COMMIT;

-- Verification
DO $$
BEGIN
    RAISE NOTICE '======================================================';
    RAISE NOTICE 'Gaming Engine Migration 001 - COMPLETED SUCCESSFULLY';
    RAISE NOTICE '======================================================';
    RAISE NOTICE 'Created:';
    RAISE NOTICE '✓ gaming_engine schema with all tables';
    RAISE NOTICE '✓ Performance indexes for real-time gaming';
    RAISE NOTICE '✓ Row Level Security policies';
    RAISE NOTICE '✓ Audit logging and event tracking';
    RAISE NOTICE '✓ Helper functions for business logic';
    RAISE NOTICE '✓ Admin and test users';
    RAISE NOTICE '';
    RAISE NOTICE 'Database ready for Gaming Engine API development';
    RAISE NOTICE 'Next: Hand off to Claude-API for FastAPI implementation';
    RAISE NOTICE '======================================================';
END;
$$;