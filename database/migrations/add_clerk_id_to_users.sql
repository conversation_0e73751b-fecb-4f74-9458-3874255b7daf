-- Add Clerk ID to users table for integration
-- ============================================

-- Add clerk_id column to users table if it doesn't exist
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS clerk_id VARCHAR(255) UNIQUE;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON public.users(clerk_id);

-- Update existing users to have proper constraints
ALTER TABLE public.users 
ALTER COLUMN password_hash DROP NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.users.clerk_id IS 'Clerk authentication service user ID for SSO integration';
