-- Chess Game Configuration for BetBet Gaming Engine
-- Uses existing gaming_engine schema to support chess

-- First check if Chess game already exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM gaming_engine.games WHERE name = 'Chess') THEN
        -- Chess-specific game configuration
        INSERT INTO gaming_engine.games (
            name, category, description, min_players, max_players, is_active, game_config
        ) VALUES (
            'Chess',
            'strategy',
            'The classic strategy game with multiple variants and time controls',
            2, 2, true,
            '{
                "game_type": "chess",
                "variants": ["standard", "chess960", "king_of_the_hill", "three_check", "antichess"],
                "time_controls": {
                    "bullet": {"time": 60, "increment": 0},
                    "blitz": {"time": 300, "increment": 3},
                    "rapid": {"time": 900, "increment": 10},
                    "classical": {"time": 1800, "increment": 30},
                    "correspondence": {"time": 86400, "increment": 0}
                },
                "rating_categories": ["bullet", "blitz", "rapid", "classical", "chess960"],
                "spectator_betting": true,
                "ai_analysis": true,
                "move_validation": "server_side",
                "anti_cheat": "advanced"
            }'::jsonb
        );
    END IF;
END $$;

-- Chess games with detailed tracking
CREATE TABLE chess_engine.chess_games (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES gaming_engine.game_sessions(id),
    
    -- Game setup
    variant VARCHAR(30) NOT NULL DEFAULT 'standard' CHECK (variant IN (
        'standard', 'chess960', 'king_of_the_hill', 'three_check', 'antichess', 'atomic'
    )),
    time_control VARCHAR(20) NOT NULL CHECK (time_control IN (
        'bullet', 'blitz', 'rapid', 'classical', 'correspondence'
    )),
    initial_time_seconds INTEGER NOT NULL,
    increment_seconds INTEGER NOT NULL DEFAULT 0,
    
    -- Players
    white_player_id UUID NOT NULL,
    black_player_id UUID NOT NULL,
    white_rating INTEGER DEFAULT 1500,
    black_rating INTEGER DEFAULT 1500,
    rating_category VARCHAR(20) NOT NULL,
    
    -- Game state
    current_fen TEXT NOT NULL DEFAULT 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    current_turn VARCHAR(5) NOT NULL DEFAULT 'white' CHECK (current_turn IN ('white', 'black')),
    move_number INTEGER NOT NULL DEFAULT 1,
    halfmove_clock INTEGER NOT NULL DEFAULT 0,
    
    -- Time tracking
    white_time_remaining INTEGER NOT NULL,
    black_time_remaining INTEGER NOT NULL,
    last_move_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Game result
    result VARCHAR(20) CHECK (result IN (
        'white_wins', 'black_wins', 'draw', 'white_timeout', 'black_timeout', 
        'white_resignation', 'black_resignation', 'stalemate', 'insufficient_material',
        'threefold_repetition', 'fifty_move_rule', 'draw_agreement', 'aborted'
    )),
    result_reason TEXT,
    finished_at TIMESTAMP WITH TIME ZONE,
    
    -- Game metadata
    starting_position TEXT, -- For Chess960
    game_metadata JSONB DEFAULT '{}',
    
    -- Spectator betting pool
    spectator_pool DECIMAL(12,2) DEFAULT 0,
    total_spectators INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CHECK (white_player_id != black_player_id)
);

-- Individual chess moves with detailed analysis
CREATE TABLE chess_engine.chess_moves (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID NOT NULL REFERENCES chess_engine.chess_games(id),
    
    -- Move details
    move_number INTEGER NOT NULL,
    color VARCHAR(5) NOT NULL CHECK (color IN ('white', 'black')),
    move_notation VARCHAR(20) NOT NULL, -- SAN notation (e.g., 'Nf3', 'O-O', 'exd5+')
    move_uci VARCHAR(10) NOT NULL, -- UCI notation (e.g., 'e2e4', 'e1g1')
    
    -- Position before move
    position_before TEXT NOT NULL, -- FEN
    position_after TEXT NOT NULL, -- FEN
    
    -- Time tracking
    time_taken_ms INTEGER NOT NULL,
    time_remaining_after INTEGER NOT NULL,
    
    -- Move analysis
    is_check BOOLEAN DEFAULT false,
    is_checkmate BOOLEAN DEFAULT false,
    is_capture BOOLEAN DEFAULT false,
    is_castling BOOLEAN DEFAULT false,
    is_en_passant BOOLEAN DEFAULT false,
    is_promotion BOOLEAN DEFAULT false,
    promotion_piece VARCHAR(1) CHECK (promotion_piece IN ('Q', 'R', 'B', 'N')),
    
    -- AI evaluation (if enabled)
    engine_evaluation INTEGER, -- Centipawn evaluation
    engine_depth INTEGER, -- Search depth
    best_move_uci VARCHAR(10), -- Engine's suggested best move
    evaluation_time_ms INTEGER,
    
    -- Move quality assessment
    move_quality VARCHAR(20) CHECK (move_quality IN (
        'brilliant', 'great', 'best', 'excellent', 'good', 'inaccuracy', 'mistake', 'blunder'
    )),
    accuracy_loss INTEGER DEFAULT 0, -- Centipawns lost
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(game_id, move_number, color)
);

-- Chess player ratings and statistics
CREATE TABLE chess_engine.chess_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    rating_category VARCHAR(20) NOT NULL,
    
    -- Current rating
    current_rating INTEGER NOT NULL DEFAULT 1500,
    peak_rating INTEGER NOT NULL DEFAULT 1500,
    rating_deviation DECIMAL(6,2) DEFAULT 350.0, -- For Glicko-2 system
    volatility DECIMAL(8,6) DEFAULT 0.06,
    
    -- Game statistics
    games_played INTEGER DEFAULT 0,
    games_won INTEGER DEFAULT 0,
    games_drawn INTEGER DEFAULT 0,
    games_lost INTEGER DEFAULT 0,
    
    -- Performance metrics
    win_streak INTEGER DEFAULT 0,
    longest_win_streak INTEGER DEFAULT 0,
    average_game_length INTEGER DEFAULT 0, -- In moves
    average_move_time INTEGER DEFAULT 0, -- In seconds
    
    -- Opening statistics
    favorite_opening JSONB DEFAULT '{}',
    opening_success_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Time-based performance
    time_control_preference VARCHAR(20),
    blitz_rating INTEGER DEFAULT 1500,
    rapid_rating INTEGER DEFAULT 1500,
    classical_rating INTEGER DEFAULT 1500,
    
    -- Last activity
    last_game_at TIMESTAMP WITH TIME ZONE,
    last_rating_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, rating_category)
);

-- Chess spectator betting
CREATE TABLE chess_engine.chess_spectator_bets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID NOT NULL REFERENCES chess_engine.chess_games(id),
    user_id UUID NOT NULL,
    
    -- Bet types
    bet_type VARCHAR(30) NOT NULL CHECK (bet_type IN (
        'game_winner', 'total_moves', 'game_duration', 'first_capture', 
        'castling_race', 'queen_trade', 'pawn_promotion', 'next_move'
    )),
    
    -- Bet details
    bet_prediction JSONB NOT NULL, -- Flexible structure for different bet types
    stake_amount DECIMAL(10,2) NOT NULL CHECK (stake_amount > 0),
    odds_at_bet DECIMAL(6,4) NOT NULL,
    potential_payout DECIMAL(12,2) NOT NULL,
    
    -- Timing
    placed_at_move INTEGER, -- Which move the bet was placed
    bet_deadline_move INTEGER, -- Latest move for bet resolution
    
    -- Resolution
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'voided', 'pushed')),
    actual_payout DECIMAL(12,2),
    settled_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(game_id, user_id, bet_type) -- One bet per type per game per user
);

-- Chess opening database
CREATE TABLE chess_engine.chess_openings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Opening identification
    name VARCHAR(100) NOT NULL,
    eco_code VARCHAR(5), -- Encyclopedia of Chess Openings code
    variation VARCHAR(200),
    
    -- Opening moves
    moves_pgn TEXT NOT NULL, -- PGN notation
    moves_uci TEXT NOT NULL, -- UCI notation
    final_fen TEXT NOT NULL,
    move_count INTEGER NOT NULL,
    
    -- Statistics
    games_played INTEGER DEFAULT 0,
    white_wins INTEGER DEFAULT 0,
    draws INTEGER DEFAULT 0,
    black_wins INTEGER DEFAULT 0,
    win_rate_white DECIMAL(5,4) GENERATED ALWAYS AS (
        CASE WHEN games_played > 0 THEN white_wins::DECIMAL / games_played ELSE 0 END
    ) STORED,
    
    -- Difficulty and popularity
    difficulty_level INTEGER DEFAULT 5 CHECK (difficulty_level BETWEEN 1 AND 10),
    popularity_score INTEGER DEFAULT 0,
    
    -- Metadata
    opening_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- PERFORMANCE INDEXES
-- ================================================================

-- Game lookup indexes
CREATE INDEX idx_chess_games_session ON chess_engine.chess_games(session_id);
CREATE INDEX idx_chess_games_players ON chess_engine.chess_games(white_player_id, black_player_id);
CREATE INDEX idx_chess_games_active ON chess_engine.chess_games(created_at DESC) 
    WHERE result IS NULL;
CREATE INDEX idx_chess_games_variant_time ON chess_engine.chess_games(variant, time_control);

-- Move analysis indexes
CREATE INDEX idx_chess_moves_game_sequence ON chess_engine.chess_moves(game_id, move_number, color);
CREATE INDEX idx_chess_moves_analysis ON chess_engine.chess_moves(move_quality, accuracy_loss) 
    WHERE engine_evaluation IS NOT NULL;
CREATE INDEX idx_chess_moves_time_taken ON chess_engine.chess_moves(time_taken_ms DESC);

-- Rating system indexes
CREATE INDEX idx_chess_ratings_user_category ON chess_engine.chess_ratings(user_id, rating_category);
CREATE INDEX idx_chess_ratings_leaderboard ON chess_engine.chess_ratings(rating_category, current_rating DESC);
CREATE INDEX idx_chess_ratings_active ON chess_engine.chess_ratings(last_game_at DESC) 
    WHERE last_game_at > NOW() - INTERVAL '30 days';

-- Spectator betting indexes
CREATE INDEX idx_chess_spectator_bets_game ON chess_engine.chess_spectator_bets(game_id, bet_type);
CREATE INDEX idx_chess_spectator_bets_user ON chess_engine.chess_spectator_bets(user_id, status);
CREATE INDEX idx_chess_spectator_bets_active ON chess_engine.chess_spectator_bets(placed_at_move, bet_deadline_move) 
    WHERE status = 'active';

-- Opening database indexes
CREATE INDEX idx_chess_openings_eco ON chess_engine.chess_openings(eco_code);
CREATE INDEX idx_chess_openings_popularity ON chess_engine.chess_openings(popularity_score DESC);
CREATE INDEX idx_chess_openings_moves ON chess_engine.chess_openings USING GIN(moves_uci);

-- Insert popular chess openings
INSERT INTO chess_engine.chess_openings (name, eco_code, moves_pgn, moves_uci, final_fen, move_count) VALUES
('Italian Game', 'C50', '1.e4 e5 2.Nf3 Nc6 3.Bc4', 'e2e4 e7e5 g1f3 b8c6 f1c4', 'r1bqkbnr/pppp1ppp/2n5/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 3 3', 3),
('Ruy Lopez', 'C60', '1.e4 e5 2.Nf3 Nc6 3.Bb5', 'e2e4 e7e5 g1f3 b8c6 f1b5', 'r1bqkbnr/pppp1ppp/2n5/1B2p3/4P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 3 3', 3),
('Queen''s Gambit', 'D06', '1.d4 d5 2.c4', 'd2d4 d7d5 c2c4', 'rnbqkbnr/ppp1pppp/8/3p4/2PP4/8/PP2PPPP/RNBQKBNR b KQkq c3 0 2', 2),
('Sicilian Defense', 'B20', '1.e4 c5', 'e2e4 c7c5', 'rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq c6 0 2', 1),
('French Defense', 'C00', '1.e4 e6', 'e2e4 e7e6', 'rnbqkbnr/pppp1ppp/4p3/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2', 1);