-- Enhanced Chess Tournament and Invitation System
-- =================================================
-- Extends chess game support with tournaments, invitations, wager matching, and advanced betting

-- Chess game invitations and challenges
CREATE TABLE IF NOT EXISTS gaming_engine.chess_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Invitation details
    challenger_id UUID NOT NULL REFERENCES public.users(id),
    challenged_id UUID REFERENCES public.users(id), -- NULL for public challenges
    invitation_type VARCHAR(20) NOT NULL CHECK (invitation_type IN ('direct', 'public', 'friends', 'league', 'tournament')),
    
    -- Game configuration
    chess_variant VARCHAR(30) NOT NULL DEFAULT 'standard' CHECK (chess_variant IN (
        'standard', 'chess960', 'king_of_the_hill', 'three_check', 'antichess', 'atomic'
    )),
    time_control VARCHAR(20) NOT NULL CHECK (time_control IN (
        'bullet', 'blitz', 'rapid', 'classical', 'correspondence'
    )),
    initial_time_seconds INTEGER NOT NULL,
    increment_seconds INTEGER NOT NULL DEFAULT 0,
    
    -- Wager system
    challenger_wager DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (challenger_wager >= 0),
    challenged_wager DECIMAL(10,2) DEFAULT NULL, -- Can be different for unmatched wagers
    wager_type VARCHAR(20) DEFAULT 'matched' CHECK (wager_type IN ('matched', 'unmatched', 'none')),
    total_prize_pool DECIMAL(10,2) GENERATED ALWAYS AS (
        COALESCE(challenger_wager, 0) + COALESCE(challenged_wager, 0)
    ) STORED,
    
    -- Visibility and access control
    is_public BOOLEAN DEFAULT false,
    is_rated BOOLEAN DEFAULT true,
    allow_spectators BOOLEAN DEFAULT true,
    allow_spectator_betting BOOLEAN DEFAULT true,
    password_hash VARCHAR(255), -- For private challenges
    
    -- League/Tournament context
    league_id UUID, -- Reference to league/club system
    tournament_id UUID REFERENCES gaming_engine.tournaments(id),
    tournament_round INTEGER,
    
    -- Invitation status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending', 'accepted', 'declined', 'cancelled', 'expired', 'completed'
    )),
    
    -- Response and timing
    message TEXT, -- Optional challenge message
    response_message TEXT, -- Challenger's response
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    accepted_at TIMESTAMP WITH TIME ZONE,
    game_id UUID, -- Populated when game starts
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- Chess game instances with enhanced metadata
CREATE TABLE IF NOT EXISTS gaming_engine.chess_game_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Game identification with searchable slug
    game_slug VARCHAR(200) UNIQUE NOT NULL, -- e.g., chess-blitz-player1-vs-player2-*************
    invitation_id UUID REFERENCES gaming_engine.chess_invitations(id),
    session_id UUID NOT NULL REFERENCES gaming_engine.game_sessions(id),
    
    -- Players
    white_player_id UUID NOT NULL REFERENCES public.users(id),
    black_player_id UUID NOT NULL REFERENCES public.users(id),
    white_player_username VARCHAR(50) NOT NULL,
    black_player_username VARCHAR(50) NOT NULL,
    
    -- Game configuration
    variant VARCHAR(30) NOT NULL,
    time_control VARCHAR(20) NOT NULL,
    initial_time_seconds INTEGER NOT NULL,
    increment_seconds INTEGER NOT NULL,
    
    -- Game state
    current_fen TEXT NOT NULL DEFAULT 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    move_history JSONB DEFAULT '[]'::jsonb, -- Array of move objects
    current_turn VARCHAR(5) NOT NULL DEFAULT 'white',
    move_number INTEGER NOT NULL DEFAULT 1,
    
    -- Time tracking
    white_time_remaining INTEGER NOT NULL,
    black_time_remaining INTEGER NOT NULL,
    last_move_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Game status
    status VARCHAR(20) DEFAULT 'waiting' CHECK (status IN (
        'waiting', 'active', 'paused', 'completed', 'aborted', 'forfeit'
    )),
    result VARCHAR(20) CHECK (result IN (
        'white_wins', 'black_wins', 'draw', 'white_timeout', 'black_timeout',
        'white_resignation', 'black_resignation', 'stalemate', 'insufficient_material',
        'threefold_repetition', 'fifty_move_rule', 'draw_agreement', 'aborted'
    )),
    result_reason TEXT,
    
    -- Wager information
    white_wager DECIMAL(10,2) NOT NULL DEFAULT 0,
    black_wager DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_prize_pool DECIMAL(10,2) GENERATED ALWAYS AS (white_wager + black_wager) STORED,
    winner_payout DECIMAL(10,2) DEFAULT 0,
    is_wager_settled BOOLEAN DEFAULT false,
    
    -- Spectator information
    spectator_count INTEGER DEFAULT 0,
    max_spectators INTEGER DEFAULT 1000,
    spectator_betting_pool DECIMAL(12,2) DEFAULT 0,
    total_spectator_bets INTEGER DEFAULT 0,
    
    -- Rating impact
    white_rating_before INTEGER,
    black_rating_before INTEGER,
    white_rating_after INTEGER,
    black_rating_after INTEGER,
    rating_change_white INTEGER DEFAULT 0,
    rating_change_black INTEGER DEFAULT 0,
    
    -- Game analysis
    opening_name VARCHAR(100),
    opening_eco VARCHAR(5),
    total_moves INTEGER DEFAULT 0,
    game_duration_seconds INTEGER,
    average_move_time_white DECIMAL(8,2),
    average_move_time_black DECIMAL(8,2),
    
    -- Archive and search
    pgn_data TEXT, -- Full PGN for analysis
    is_archived BOOLEAN DEFAULT false,
    archive_date TIMESTAMP WITH TIME ZONE,
    tags JSONB DEFAULT '{}'::jsonb, -- Searchable tags
    
    -- Timestamps
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CHECK (white_player_id != black_player_id),
    CHECK (white_player_username != black_player_username)
);

-- Enhanced spectator betting with in-game outcomes
CREATE TABLE IF NOT EXISTS gaming_engine.chess_spectator_betting_markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Market identification  
    game_id UUID NOT NULL REFERENCES gaming_engine.chess_game_instances(id),
    market_type VARCHAR(50) NOT NULL CHECK (market_type IN (
        'game_winner', 'first_capture', 'first_check', 'castling_race', 
        'queen_trade', 'first_queen_loss', 'pawn_promotion', 'opening_type',
        'total_moves_range', 'game_duration', 'checkmate_vs_other',
        'material_advantage', 'first_blunder', 'time_advantage'
    )),
    market_name VARCHAR(200) NOT NULL,
    market_description TEXT,
    
    -- Market status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN (
        'active', 'suspended', 'settled', 'cancelled', 'voided'
    )),
    
    -- Betting parameters
    opens_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closes_at TIMESTAMP WITH TIME ZONE, -- Some markets close during game
    settlement_criteria JSONB NOT NULL, -- Conditions for settlement
    
    -- Market data
    total_pool DECIMAL(12,2) DEFAULT 0,
    total_bets INTEGER DEFAULT 0,
    house_edge DECIMAL(5,4) DEFAULT 0.05,
    
    -- Settlement
    winning_outcome VARCHAR(100),
    settled_at TIMESTAMP WITH TIME ZONE,
    settlement_data JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Betting outcomes for each market
CREATE TABLE IF NOT EXISTS gaming_engine.chess_betting_outcomes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    market_id UUID NOT NULL REFERENCES gaming_engine.chess_spectator_betting_markets(id) ON DELETE CASCADE,
    
    -- Outcome details
    outcome_name VARCHAR(100) NOT NULL,
    outcome_description TEXT,
    outcome_data JSONB DEFAULT '{}'::jsonb, -- Specific outcome parameters
    
    -- Odds and probability
    current_odds DECIMAL(8,4) NOT NULL CHECK (current_odds > 0),
    opening_odds DECIMAL(8,4) NOT NULL,
    implied_probability DECIMAL(5,4) GENERATED ALWAYS AS (1.0 / current_odds) STORED,
    
    -- Betting volume
    total_bet_amount DECIMAL(12,2) DEFAULT 0,
    bet_count INTEGER DEFAULT 0,
    largest_bet DECIMAL(10,2) DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_winning_outcome BOOLEAN DEFAULT false,
    
    -- Settlement
    payout_multiplier DECIMAL(8,4),
    total_payout DECIMAL(12,2) DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(market_id, outcome_name)
);

-- Individual spectator bets
CREATE TABLE IF NOT EXISTS gaming_engine.chess_spectator_bets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- References
    game_id UUID NOT NULL REFERENCES gaming_engine.chess_game_instances(id),
    market_id UUID NOT NULL REFERENCES gaming_engine.chess_spectator_betting_markets(id),
    outcome_id UUID NOT NULL REFERENCES gaming_engine.chess_betting_outcomes(id),
    user_id UUID NOT NULL REFERENCES public.users(id),
    
    -- Bet details
    stake_amount DECIMAL(10,2) NOT NULL CHECK (stake_amount > 0),
    odds_at_placement DECIMAL(8,4) NOT NULL,
    potential_payout DECIMAL(12,2) NOT NULL,
    
    -- Timing and context
    placed_at_move INTEGER, -- Which move number when bet was placed
    game_state_at_placement JSONB, -- Game state when bet was placed
    
    -- Settlement
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN (
        'active', 'won', 'lost', 'voided', 'partially_won'
    )),
    actual_payout DECIMAL(12,2) DEFAULT 0,
    settled_at TIMESTAMP WITH TIME ZONE,
    settlement_notes TEXT,
    
    -- Risk management
    is_suspicious BOOLEAN DEFAULT false,
    risk_score DECIMAL(5,2) DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CHECK (potential_payout >= stake_amount)
);

-- Chess tournaments enhanced
CREATE TABLE IF NOT EXISTS gaming_engine.chess_tournaments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Tournament identification
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    tournament_type VARCHAR(30) NOT NULL CHECK (tournament_type IN (
        'single_elimination', 'double_elimination', 'round_robin', 'swiss', 'arena'
    )),
    
    -- Chess specific settings
    chess_variant VARCHAR(30) NOT NULL DEFAULT 'standard',
    time_control VARCHAR(20) NOT NULL,
    initial_time_seconds INTEGER NOT NULL,
    increment_seconds INTEGER NOT NULL DEFAULT 0,
    
    -- Entry and participation
    entry_fee DECIMAL(10,2) NOT NULL CHECK (entry_fee >= 0),
    max_participants INTEGER NOT NULL CHECK (max_participants > 1),
    current_participants INTEGER DEFAULT 0,
    min_participants INTEGER DEFAULT 4,
    
    -- Rating restrictions
    min_rating INTEGER,
    max_rating INTEGER,
    rating_category VARCHAR(20) NOT NULL,
    
    -- Prize structure
    prize_pool DECIMAL(10,2) NOT NULL DEFAULT 0,
    prize_distribution JSONB, -- Percentage or fixed amounts by position
    
    -- Tournament status
    status VARCHAR(20) DEFAULT 'planned' CHECK (status IN (
        'planned', 'registration_open', 'registration_closed', 'active', 
        'paused', 'completed', 'cancelled'
    )),
    
    -- Scheduling
    registration_opens_at TIMESTAMP WITH TIME ZONE,
    registration_closes_at TIMESTAMP WITH TIME ZONE,
    starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
    estimated_ends_at TIMESTAMP WITH TIME ZONE,
    actual_ended_at TIMESTAMP WITH TIME ZONE,
    
    -- Tournament configuration
    rounds_total INTEGER,
    current_round INTEGER DEFAULT 0,
    time_between_rounds INTEGER DEFAULT 300, -- seconds
    
    -- Visibility and access
    is_public BOOLEAN DEFAULT true,
    is_rated BOOLEAN DEFAULT true,
    organizer_id UUID REFERENCES public.users(id),
    
    -- Results
    champion_id UUID REFERENCES public.users(id),
    runner_up_id UUID REFERENCES public.users(id),
    final_standings JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1
);

-- Chess game archive with full-text search
CREATE TABLE IF NOT EXISTS gaming_engine.chess_game_archive (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Original game reference
    original_game_id UUID NOT NULL, -- References chess_game_instances(id)
    game_slug VARCHAR(200) NOT NULL, -- Preserved for search
    
    -- Player information
    white_player_username VARCHAR(50) NOT NULL,
    black_player_username VARCHAR(50) NOT NULL,
    white_rating INTEGER,
    black_rating INTEGER,
    
    -- Game metadata
    variant VARCHAR(30) NOT NULL,
    time_control VARCHAR(20) NOT NULL,
    result VARCHAR(20) NOT NULL,
    total_moves INTEGER NOT NULL,
    game_duration_seconds INTEGER,
    
    -- Full game data
    pgn_data TEXT NOT NULL, -- Complete PGN
    move_history JSONB NOT NULL, -- Complete move analysis
    final_position TEXT NOT NULL, -- Final FEN
    
    -- Analysis data
    opening_name VARCHAR(100),
    opening_eco VARCHAR(5),
    engine_analysis JSONB, -- Post-game computer analysis
    critical_moments JSONB, -- Key positions and blunders
    
    -- Search metadata
    game_date DATE NOT NULL,
    tags TEXT[], -- Array of searchable tags
    search_vector tsvector, -- Full-text search
    
    -- Archive metadata
    archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archive_reason VARCHAR(50) DEFAULT 'completed',
    retention_until TIMESTAMP WITH TIME ZONE, -- For data retention policies
    
    -- Constraints
    CHECK (total_moves > 0)
);

-- ================================================================
-- INDEXES FOR PERFORMANCE
-- ================================================================

-- Chess invitations indexes
CREATE INDEX IF NOT EXISTS idx_chess_invitations_challenger ON gaming_engine.chess_invitations(challenger_id);
CREATE INDEX IF NOT EXISTS idx_chess_invitations_challenged ON gaming_engine.chess_invitations(challenged_id);
CREATE INDEX IF NOT EXISTS idx_chess_invitations_status ON gaming_engine.chess_invitations(status);
CREATE INDEX IF NOT EXISTS idx_chess_invitations_public ON gaming_engine.chess_invitations(is_public, status) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_chess_invitations_expires ON gaming_engine.chess_invitations(expires_at);

-- Chess game instances indexes
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_slug ON gaming_engine.chess_game_instances(game_slug);
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_players ON gaming_engine.chess_game_instances(white_player_id, black_player_id);
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_status ON gaming_engine.chess_game_instances(status);
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_time_control ON gaming_engine.chess_game_instances(time_control, variant);
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_created ON gaming_engine.chess_game_instances(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chess_game_instances_search ON gaming_engine.chess_game_instances USING GIN(tags);

-- Spectator betting indexes
CREATE INDEX IF NOT EXISTS idx_chess_betting_markets_game ON gaming_engine.chess_spectator_betting_markets(game_id, status);
CREATE INDEX IF NOT EXISTS idx_chess_betting_outcomes_market ON gaming_engine.chess_betting_outcomes(market_id, is_active);
CREATE INDEX IF NOT EXISTS idx_chess_spectator_bets_user ON gaming_engine.chess_spectator_bets(user_id, status);
CREATE INDEX IF NOT EXISTS idx_chess_spectator_bets_game ON gaming_engine.chess_spectator_bets(game_id, status);

-- Tournament indexes
CREATE INDEX IF NOT EXISTS idx_chess_tournaments_status ON gaming_engine.chess_tournaments(status);
CREATE INDEX IF NOT EXISTS idx_chess_tournaments_registration ON gaming_engine.chess_tournaments(registration_opens_at, registration_closes_at);
CREATE INDEX IF NOT EXISTS idx_chess_tournaments_time_control ON gaming_engine.chess_tournaments(time_control, chess_variant);

-- Archive indexes
CREATE INDEX IF NOT EXISTS idx_chess_archive_slug ON gaming_engine.chess_game_archive(game_slug);
CREATE INDEX IF NOT EXISTS idx_chess_archive_players ON gaming_engine.chess_game_archive(white_player_username, black_player_username);
CREATE INDEX IF NOT EXISTS idx_chess_archive_date ON gaming_engine.chess_game_archive(game_date DESC);
CREATE INDEX IF NOT EXISTS idx_chess_archive_opening ON gaming_engine.chess_game_archive(opening_eco, opening_name);
CREATE INDEX IF NOT EXISTS idx_chess_archive_search ON gaming_engine.chess_game_archive USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_chess_archive_tags ON gaming_engine.chess_game_archive USING GIN(tags);

-- ================================================================
-- FUNCTIONS AND TRIGGERS
-- ================================================================

-- Function to generate game slugs
CREATE OR REPLACE FUNCTION generate_chess_game_slug(
    p_variant VARCHAR,
    p_time_control VARCHAR,
    p_white_username VARCHAR,
    p_black_username VARCHAR,
    p_created_at TIMESTAMP WITH TIME ZONE
) RETURNS VARCHAR AS $$
DECLARE
    slug VARCHAR(200);
    date_part VARCHAR(50);
    time_part VARCHAR(10);
BEGIN
    -- Format: chess-{variant}-{time_control}-{player1}-vs-{player2}-{DDMMYYYY}-{HHMM}
    date_part := TO_CHAR(p_created_at, 'DDMMYYYY');
    time_part := TO_CHAR(p_created_at, 'HH24MI');
    
    slug := LOWER(
        'chess-' || 
        REPLACE(p_variant, '_', '-') || '-' ||
        p_time_control || '-' ||
        REPLACE(LOWER(p_white_username), ' ', '-') || '-vs-' ||
        REPLACE(LOWER(p_black_username), ' ', '-') || '-' ||
        date_part || '-' || time_part
    );
    
    -- Ensure slug is unique by appending counter if needed
    WHILE EXISTS (SELECT 1 FROM gaming_engine.chess_game_instances WHERE game_slug = slug) LOOP
        slug := slug || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
    END LOOP;
    
    RETURN slug;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate game slugs
CREATE OR REPLACE FUNCTION auto_generate_chess_game_slug()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.game_slug IS NULL OR NEW.game_slug = '' THEN
        NEW.game_slug := generate_chess_game_slug(
            NEW.variant,
            NEW.time_control,
            NEW.white_player_username,
            NEW.black_player_username,
            NEW.created_at
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_generate_chess_game_slug
    BEFORE INSERT ON gaming_engine.chess_game_instances
    FOR EACH ROW EXECUTE FUNCTION auto_generate_chess_game_slug();

-- Function to update archive search vector
CREATE OR REPLACE FUNCTION update_chess_archive_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english',
        COALESCE(NEW.white_player_username, '') || ' ' ||
        COALESCE(NEW.black_player_username, '') || ' ' ||
        COALESCE(NEW.opening_name, '') || ' ' ||
        COALESCE(NEW.opening_eco, '') || ' ' ||
        COALESCE(NEW.variant, '') || ' ' ||
        COALESCE(NEW.time_control, '') || ' ' ||
        COALESCE(NEW.result, '') || ' ' ||
        COALESCE(array_to_string(NEW.tags, ' '), '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_chess_archive_search_vector
    BEFORE INSERT OR UPDATE ON gaming_engine.chess_game_archive
    FOR EACH ROW EXECUTE FUNCTION update_chess_archive_search_vector();

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Enhanced Chess Tournament and Invitation System created successfully:';
    RAISE NOTICE '- Chess invitations with wager matching';
    RAISE NOTICE '- Game instances with searchable slugs';
    RAISE NOTICE '- Advanced spectator betting markets';
    RAISE NOTICE '- Tournament system integration';
    RAISE NOTICE '- Game archive with full-text search';
    RAISE NOTICE '- All indexes and triggers configured';
END;
$$;