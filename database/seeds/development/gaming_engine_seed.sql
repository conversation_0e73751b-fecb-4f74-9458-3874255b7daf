-- BetBet Platform - Gaming Engine Development Seed Data
-- ======================================================
-- Sample data for development and testing

-- Insert sample games
INSERT INTO gaming_engine.games (
    id,
    name,
    slug,
    category,
    description,
    game_config,
    min_players,
    max_players,
    scoring_system,
    has_spectator_betting,
    is_active,
    is_featured,
    created_by,
    updated_by
) VALUES 
(
    '10000000-0000-0000-0000-000000000001',
    'Sports Trivia Challenge',
    'sports-trivia',
    'trivia',
    'Test your sports knowledge with rapid-fire questions covering all major sports.',
    '{"rounds": 5, "questions_per_round": 10, "time_per_question": 15, "categories": ["football", "basketball", "baseball", "hockey"]}',
    2,
    8,
    'points',
    true,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
),
(
    '10000000-0000-0000-0000-000000000002',
    'Lightning Reflexes',
    'lightning-reflexes',
    'reaction_time',
    'Test your reaction speed in this fast-paced clicking challenge.',
    '{"rounds": 3, "targets_per_round": 20, "target_display_time": 2, "difficulty": "medium"}',
    2,
    6,
    'time',
    true,
    true,
    false,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
),
(
    '10000000-0000-0000-0000-000000000003',
    'Word Scramble Master',
    'word-scramble',
    'puzzle',
    'Unscramble words as fast as you can in this vocabulary challenge.',
    '{"rounds": 4, "words_per_round": 15, "time_per_word": 30, "difficulty_levels": ["easy", "medium", "hard"]}',
    2,
    10,
    'points',
    true,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
),
(
    '10000000-0000-0000-0000-000000000004',
    'Strategy Showdown',
    'strategy-showdown',
    'strategy',
    'Outsmart your opponents in this turn-based strategy game.',
    '{"board_size": "8x8", "piece_types": 5, "max_turns": 50, "win_conditions": ["elimination", "territory"]}',
    2,
    4,
    'custom',
    true,
    true,
    false,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
);

-- Insert more development users for testing
INSERT INTO public.users (
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    balance,
    is_active,
    is_verified,
    created_by,
    updated_by
) VALUES 
(
    '00000000-0000-0000-0000-000000000003',
    'player1',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK',
    'Player',
    'One',
    500.00,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
),
(
    '00000000-0000-0000-0000-000000000004',
    'player2',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK',
    'Player',
    'Two',
    750.00,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
),
(
    '00000000-0000-0000-0000-000000000005',
    'player3',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK',
    'Player',
    'Three',
    300.00,
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
) ON CONFLICT (id) DO NOTHING;

-- Insert sample game sessions for testing
INSERT INTO gaming_engine.game_sessions (
    id,
    game_id,
    host_id,
    session_name,
    session_code,
    max_participants,
    entry_fee,
    prize_pool,
    state,
    created_by,
    updated_by
) VALUES 
(
    '20000000-0000-0000-0000-000000000001',
    '10000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000002',
    'Trivia Night Championship',
    'TRIVIA1',
    4,
    10.00,
    36.00,
    'waiting',
    '00000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000002'
),
(
    '20000000-0000-0000-0000-000000000002',
    '10000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000003',
    'Speed Challenge',
    'SPEED1',
    6,
    5.00,
    27.00,
    'waiting',
    '00000000-0000-0000-0000-000000000003',
    '00000000-0000-0000-0000-000000000003'
);

-- Insert sample tournament
INSERT INTO gaming_engine.tournaments (
    id,
    name,
    slug,
    description,
    game_id,
    tournament_type,
    entry_fee,
    max_participants,
    prize_pool,
    registration_opens_at,
    registration_closes_at,
    starts_at,
    estimated_ends_at,
    status,
    is_public,
    is_featured,
    organizer_id,
    created_by,
    updated_by
) VALUES (
    '30000000-0000-0000-0000-000000000001',
    'Weekly Trivia Championship',
    'weekly-trivia-championship',
    'Compete against the best trivia players for the weekly championship title and prizes.',
    '10000000-0000-0000-0000-000000000001',
    'single_elimination',
    25.00,
    16,
    360.00,
    NOW() - INTERVAL '1 day',
    NOW() + INTERVAL '2 days',
    NOW() + INTERVAL '3 days',
    NOW() + INTERVAL '4 days',
    'registration_open',
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001'
);

-- Insert sample performance metrics
INSERT INTO gaming_engine.performance_metrics (
    metric_name,
    metric_value,
    metric_unit,
    metric_tags,
    created_at
) VALUES 
('avg_session_duration', 15.5, 'minutes', '{"game_type": "trivia"}', NOW() - INTERVAL '1 hour'),
('concurrent_players', 45, 'count', '{"peak_time": true}', NOW() - INTERVAL '30 minutes'),
('api_response_time', 23.5, 'milliseconds', '{"endpoint": "join_session"}', NOW() - INTERVAL '15 minutes'),
('websocket_latency', 8.2, 'milliseconds', '{"connection_type": "game_update"}', NOW() - INTERVAL '5 minutes');

-- Insert sample events for real-time testing
INSERT INTO gaming_engine.events (
    event_type,
    event_source,
    event_data,
    session_id,
    created_at
) VALUES 
('session_created', 'gaming_engine', '{"session_id": "20000000-0000-0000-0000-000000000001", "game_type": "trivia"}', '20000000-0000-0000-0000-000000000001', NOW() - INTERVAL '2 hours'),
('session_created', 'gaming_engine', '{"session_id": "20000000-0000-0000-0000-000000000002", "game_type": "reaction_time"}', '20000000-0000-0000-0000-000000000002', NOW() - INTERVAL '1 hour');

-- Verify seed data
DO $$
DECLARE
    game_count INTEGER;
    user_count INTEGER;
    session_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO game_count FROM gaming_engine.games WHERE deleted_at IS NULL;
    SELECT COUNT(*) INTO user_count FROM public.users WHERE deleted_at IS NULL;
    SELECT COUNT(*) INTO session_count FROM gaming_engine.game_sessions WHERE deleted_at IS NULL;
    
    RAISE NOTICE '======================================================';
    RAISE NOTICE 'Gaming Engine Development Seed Data - LOADED';
    RAISE NOTICE '======================================================';
    RAISE NOTICE 'Sample Data Created:';
    RAISE NOTICE '✓ % games (trivia, reaction_time, puzzle, strategy)', game_count;
    RAISE NOTICE '✓ % users (admin, testuser, player1-3)', user_count;
    RAISE NOTICE '✓ % game sessions (waiting for players)', session_count;
    RAISE NOTICE '✓ 1 tournament (registration open)';
    RAISE NOTICE '✓ Performance metrics for monitoring';
    RAISE NOTICE '✓ Real-time events for WebSocket testing';
    RAISE NOTICE '';
    RAISE NOTICE 'Test Credentials:';
    RAISE NOTICE '- Admin: <EMAIL> / admin123';
    RAISE NOTICE '- Test User: <EMAIL> / admin123';
    RAISE NOTICE '- Player 1: <EMAIL> / admin123';
    RAISE NOTICE '- Player 2: <EMAIL> / admin123';
    RAISE NOTICE '- Player 3: <EMAIL> / admin123';
    RAISE NOTICE '';
    RAISE NOTICE 'Ready for API development and testing!';
    RAISE NOTICE '======================================================';
END;
$$;