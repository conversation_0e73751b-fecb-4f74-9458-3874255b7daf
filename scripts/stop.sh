#!/bin/bash

# BetBet Platform Stop Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

stop_services() {
    print_status "Stopping BetBet Platform services..."
    
    # Stop all services
    docker-compose down
    
    print_success "All services stopped successfully!"
}

stop_and_remove() {
    print_status "Stopping and removing all containers, networks, and volumes..."
    
    # Stop and remove everything
    docker-compose down -v --remove-orphans
    
    print_success "All containers, networks, and volumes removed!"
}

clean_system() {
    print_status "Cleaning up Docker system..."
    
    # Remove unused containers, networks, images, and build cache
    docker system prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    print_success "Docker system cleaned!"
}

show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  (no option)    Stop all services"
    echo "  --remove       Stop services and remove containers/volumes"
    echo "  --clean        Stop services and clean Docker system"
    echo "  --help         Show this help message"
}

main() {
    echo ""
    print_status "🛑 Stopping BetBet Platform 🛑"
    echo ""
    
    case "${1:-}" in
        --remove)
            stop_and_remove
            ;;
        --clean)
            stop_and_remove
            clean_system
            ;;
        --help)
            show_usage
            exit 0
            ;;
        "")
            stop_services
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
    print_success "✅ BetBet Platform stopped successfully!"
}

main "$@"