#!/usr/bin/env python3
"""
Seed Chess Games Database
========================

Creates real chess games in the database for testing purposes.
This replaces mock data with actual database entries.
"""

import asyncio
import asyncpg
import uuid
from datetime import datetime, timezone
from decimal import Decimal
import os
import sys

# Database connection settings
DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://postgres:123Bubblegums@localhost:5432/betbet_db')

async def create_test_users(conn):
    """Create test users for chess games"""
    # Use consistent UUIDs for test users
    users = [
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'username': 'ChessMaster',
            'email': '<EMAIL>',
            'rating': 1650,
            'games_played': 150,
            'wins': 90,
            'losses': 45,
            'draws': 15
        },
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'username': 'QueenGambit',
            'email': '<EMAIL>',
            'rating': 1820,
            'games_played': 200,
            'wins': 130,
            'losses': 50,
            'draws': 20
        },
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'username': 'KnightRider',
            'email': '<EMAIL>',
            'rating': 1790,
            'games_played': 180,
            'wins': 110,
            'losses': 55,
            'draws': 15
        },
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'username': 'PawnStorm',
            'email': '<EMAIL>',
            'rating': 1450,
            'games_played': 100,
            'wins': 45,
            'losses': 40,
            'draws': 15
        }
    ]
    
    # Ensure chess_engine schema exists
    try:
        await conn.execute("CREATE SCHEMA IF NOT EXISTS chess_engine")
        print("✅ Created chess_engine schema")
    except Exception as e:
        print(f"❌ Failed to create chess_engine schema: {e}")

    # Create users table in chess_engine schema
    try:
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS chess_engine.users (
                id UUID PRIMARY KEY,
                username VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                rating INTEGER DEFAULT 1200,
                games_played INTEGER DEFAULT 0,
                wins INTEGER DEFAULT 0,
                losses INTEGER DEFAULT 0,
                draws INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        print("✅ Created chess_engine.users table")
    except Exception as e:
        print(f"❌ Failed to create users table: {e}")
    
    # Insert users
    for user in users:
        try:
            await conn.execute("""
                INSERT INTO chess_engine.users (id, username, email, rating, games_played, wins, losses, draws)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (username) DO UPDATE SET
                    rating = EXCLUDED.rating,
                    games_played = EXCLUDED.games_played,
                    wins = EXCLUDED.wins,
                    losses = EXCLUDED.losses,
                    draws = EXCLUDED.draws,
                    updated_at = NOW()
            """, user['id'], user['username'], user['email'], user['rating'],
                 user['games_played'], user['wins'], user['losses'], user['draws'])
            print(f"✅ Created/updated user: {user['username']} (Rating: {user['rating']})")
        except Exception as e:
            print(f"❌ Failed to create user {user['username']}: {e}")
    
    return users

async def create_chess_games(conn, users):
    """Create test chess games"""
    
    # Ensure chess_engine schema exists
    await conn.execute("CREATE SCHEMA IF NOT EXISTS chess_engine")
    
    # Create chess_games table if it doesn't exist
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS chess_engine.chess_games (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            session_id UUID,
            variant VARCHAR(30) NOT NULL DEFAULT 'standard',
            time_control VARCHAR(20) NOT NULL,
            initial_time_seconds INTEGER NOT NULL,
            increment_seconds INTEGER DEFAULT 0,
            white_player_id UUID REFERENCES chess_engine.users(id),
            black_player_id UUID REFERENCES chess_engine.users(id),
            status VARCHAR(20) NOT NULL DEFAULT 'waiting',
            is_rated BOOLEAN DEFAULT true,
            is_private BOOLEAN DEFAULT false,
            allow_spectators BOOLEAN DEFAULT true,
            wager_amount DECIMAL(10,2) DEFAULT 0,
            description TEXT,
            current_fen TEXT DEFAULT 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
            move_count INTEGER DEFAULT 0,
            white_time_remaining INTEGER,
            black_time_remaining INTEGER,
            result VARCHAR(20),
            result_reason TEXT,
            finished_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
    """)
    
    # Use consistent UUIDs for test games
    chess_games = [
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'variant': 'standard',
            'time_control': 'blitz',
            'initial_time_seconds': 300,
            'increment_seconds': 3,
            'white_player_id': users[0]['id'],  # ChessMaster
            'black_player_id': None,  # Waiting for opponent
            'status': 'waiting',
            'is_rated': True,
            'is_private': False,
            'allow_spectators': True,
            'wager_amount': Decimal('0'),
            'description': 'Quick blitz game - all welcome!',
            'white_time_remaining': 300,
            'black_time_remaining': 300
        },
        {
            'id': '550e8400-e29b-41d4-a716-************',
            'variant': 'standard',
            'time_control': 'rapid',
            'initial_time_seconds': 900,
            'increment_seconds': 10,
            'white_player_id': users[1]['id'],  # QueenGambit
            'black_player_id': users[2]['id'],  # KnightRider
            'status': 'active',
            'is_rated': True,
            'is_private': False,
            'allow_spectators': True,
            'wager_amount': Decimal('50'),
            'description': 'Rapid game with wager',
            'white_time_remaining': 850,
            'black_time_remaining': 890,
            'move_count': 8
        },
        {
            'id': '550e8400-e29b-41d4-a716-446655441003',
            'variant': 'chess960',
            'time_control': 'classical',
            'initial_time_seconds': 1800,
            'increment_seconds': 30,
            'white_player_id': users[3]['id'],  # PawnStorm
            'black_player_id': None,  # Waiting for opponent
            'status': 'waiting',
            'is_rated': False,
            'is_private': False,
            'allow_spectators': True,
            'wager_amount': Decimal('25'),
            'description': 'Chess960 variant - casual game',
            'white_time_remaining': 1800,
            'black_time_remaining': 1800
        }
    ]
    
    # Insert chess games
    for game in chess_games:
        try:
            await conn.execute("""
                INSERT INTO chess_engine.chess_games (
                    id, variant, time_control, initial_time_seconds, increment_seconds,
                    white_player_id, black_player_id, status, is_rated, is_private,
                    allow_spectators, wager_amount, description, white_time_remaining,
                    black_time_remaining, move_count
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                ON CONFLICT (id) DO UPDATE SET
                    status = EXCLUDED.status,
                    move_count = EXCLUDED.move_count,
                    updated_at = NOW()
            """, 
                game['id'], game['variant'], game['time_control'], 
                game['initial_time_seconds'], game['increment_seconds'],
                game['white_player_id'], game['black_player_id'], game['status'],
                game['is_rated'], game['is_private'], game['allow_spectators'],
                game['wager_amount'], game['description'], 
                game['white_time_remaining'], game['black_time_remaining'],
                game.get('move_count', 0))
            
            status_emoji = "⏳" if game['status'] == 'waiting' else "♟️"
            opponent = "vs " + next(u['username'] for u in users if u['id'] == game['black_player_id']) if game['black_player_id'] else "waiting for opponent"
            white_player = next(u['username'] for u in users if u['id'] == game['white_player_id'])
            
            print(f"{status_emoji} Created chess game: {white_player} {opponent}")
            print(f"   {game['variant']} • {game['time_control']} • ${game['wager_amount']}")
            
        except Exception as e:
            print(f"❌ Failed to create chess game: {e}")
    
    return chess_games

async def main():
    """Main seeding function"""
    print("🏁 Seeding Chess Games Database")
    print("=" * 40)
    
    try:
        # Connect to database
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected to database")

        # Start transaction
        async with conn.transaction():
            # Create test users
            print("\n👥 Creating test users...")
            users = await create_test_users(conn)

            # Create chess games
            print("\n♟️ Creating chess games...")
            games = await create_chess_games(conn, users)
        
        print(f"\n🎉 Successfully seeded database!")
        print(f"   • {len(users)} users created")
        print(f"   • {len(games)} chess games created")
        print("\n📝 You can now test chess games with real database data!")
        
        # Show game IDs for testing
        print("\n🔗 Game URLs for testing:")
        for game in games:
            print(f"   http://localhost:3000/chess/{game['id']}")
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        sys.exit(1)
    finally:
        if 'conn' in locals():
            await conn.close()

if __name__ == "__main__":
    asyncio.run(main())
