#!/bin/bash

# BetBet Platform Startup Script with Full Monitoring
# Optimized for slow networks (Zimbabwe-friendly)
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_progress() {
    echo -e "${CYAN}[PROGRESS]${NC} $1"
}

# Show a progress indicator for slow operations
show_progress() {
    local duration=$1
    local message=$2
    
    echo -n -e "${CYAN}[PROGRESS]${NC} $message"
    for ((i=1; i<=duration; i++)); do
        echo -n "."
        sleep 1
    done
    echo " Done!"
}

# Check if <PERSON><PERSON> and <PERSON><PERSON> Compose are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Create .env file if it doesn't exist
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please update the .env file with your actual configuration."
    else
        print_success "Environment file exists"
    fi
}

# Build all services with progress indication
build_services() {
    print_status "Building Docker images (this may take 10-30 minutes on slow networks)..."
    print_warning "Please be patient - Zimbabwe networks require extra time for downloads"
    
    # Build in stages to show progress
    print_progress "Building infrastructure services..."
    docker-compose build postgres redis
    
    print_progress "Building backend services..."
    docker-compose build api-gateway custom-betting gaming-engine trading-engine expert-analysis sports-analysis leaderboards wallet-service websocket-manager
    
    print_progress "Building frontend services..."
    docker-compose build web admin
    
    print_progress "Building monitoring services (Grafana & Prometheus)..."
    docker-compose build prometheus grafana
    
    print_success "All Docker images built successfully!"
}

# Start services in stages with proper waiting
start_platform() {
    print_status "Starting BetBet Platform with full monitoring..."
    
    # Stage 1: Infrastructure
    print_progress "Starting infrastructure (PostgreSQL, Redis)..."
    docker-compose up -d postgres redis
    show_progress 15 "Waiting for database to initialize"
    
    # Stage 2: Core backend services
    print_progress "Starting core backend services..."
    docker-compose up -d api-gateway custom-betting gaming-engine trading-engine expert-analysis sports-analysis leaderboards wallet-service websocket-manager
    show_progress 20 "Waiting for backend services to start"
    
    # Stage 3: Frontend services
    print_progress "Starting frontend services..."
    docker-compose up -d web admin
    show_progress 10 "Waiting for frontend to compile"
    
    # Stage 4: Monitoring (may take longest due to image downloads)
    print_progress "Starting monitoring services (Prometheus & Grafana)..."
    print_warning "This step may take 5-15 minutes on slow networks - downloading monitoring images"
    docker-compose up -d prometheus grafana pgadmin
    show_progress 30 "Waiting for monitoring services to download and start"
    
    print_success "BetBet Platform with full monitoring started successfully!"
}

# Display comprehensive service information
show_services() {
    echo ""
    print_success "🎉 === BetBet Platform with Monitoring === 🎉"
    echo ""
    print_status "🌐 Frontend Services:"
    echo "  📱 Web App:      http://localhost:3000"
    echo "  ⚙️  Admin Panel:  http://localhost:3001"
    echo ""
    print_status "🔌 API Services:"
    echo "  🚪 API Gateway:  http://localhost:8000"
    echo "  📡 WebSocket:    ws://localhost:8008/ws"
    echo ""
    print_status "🎮 Backend Services:"
    echo "  🎮 Gaming:       http://localhost:8001"
    echo "  🎯 Custom Betting: http://localhost:8002"
    echo "  📈 Trading:      http://localhost:8003"
    echo "  👨‍💼 Experts:      http://localhost:8004"
    echo "  ⚽ Sports:       http://localhost:8005"
    echo "  🏆 Leaderboards: http://localhost:8006"
    echo "  💰 Wallet:       http://localhost:8007"
    echo ""
    print_status "📊 Monitoring & Analytics:"
    echo "  🗄️  PostgreSQL:   localhost:5432"
    echo "  🔴 Redis:        localhost:6379"
    echo "  📊 PgAdmin:      http://localhost:5050 (<EMAIL>/admin123)"
    echo "  📈 Prometheus:   http://localhost:9090"
    echo "  📊 Grafana:      http://localhost:3002 (admin/admin123)"
    echo ""
    print_status "📈 Real-Time Metrics Available:"
    echo "  🎯 Custom Betting Metrics: http://localhost:8002/metrics"
    echo "  📊 Platform Analytics:     http://localhost:3001/analytics"
    echo "  🎮 Betting Dashboard:      http://localhost:3001/betting"
    echo "  ⚙️  System Management:     http://localhost:3001/system"
    echo ""
    print_status "🔍 Quick Health Checks:"
    echo "  Platform Overview: curl http://localhost:3001/api/betting/overview"
    echo "  Service Metrics:   curl http://localhost:8002/metrics"
    echo "  System Status:     curl http://localhost:3001/api/analytics/system-metrics"
    echo ""
}

# Enhanced health check
check_health() {
    print_status "Performing comprehensive health checks..."
    
    # Check core services
    services=(
        "web:3000"
        "admin:3001"
        "api-gateway:8000"
        "custom-betting:8002"
        "gaming-engine:8001"
        "trading-engine:8003"
        "expert-analysis:8004"
        "sports-analysis:8005"
        "leaderboards:8006"
        "wallet-service:8007"
        "websocket-manager:8008"
    )
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if curl -f -s "http://localhost:$port" > /dev/null 2>&1; then
            print_success "✅ $name is healthy"
        else
            print_warning "⚠️  $name health check failed (may still be starting)"
        fi
    done
    
    # Check monitoring services
    print_status "Checking monitoring services..."
    
    if curl -f -s "http://localhost:9090" > /dev/null 2>&1; then
        print_success "✅ Prometheus is healthy"
    else
        print_warning "⚠️  Prometheus may still be starting"
    fi
    
    if curl -f -s "http://localhost:3002" > /dev/null 2>&1; then
        print_success "✅ Grafana is healthy"
    else
        print_warning "⚠️  Grafana may still be starting (check in 2-3 minutes)"
    fi
    
    # Check real data
    print_status "Checking real data integration..."
    
    if curl -f -s "http://localhost:3001/api/betting/markets" > /dev/null 2>&1; then
        market_count=$(curl -s "http://localhost:3001/api/betting/markets" | jq '. | length' 2>/dev/null || echo "unknown")
        print_success "✅ Real market data available ($market_count markets)"
    else
        print_warning "⚠️  Market data API may still be starting"
    fi
    
    if curl -f -s "http://localhost:8002/metrics" > /dev/null 2>&1; then
        print_success "✅ Prometheus metrics available"
    else
        print_warning "⚠️  Metrics endpoint may still be starting"
    fi
}

# Main execution
main() {
    echo ""
    print_status "🚀 Starting BetBet Platform with Full Monitoring 🚀"
    print_warning "⏰ Optimized for slow networks - please be patient!"
    echo ""
    
    check_dependencies
    setup_environment
    
    # Ask user if they want to build (for slow networks)
    echo ""
    read -p "Do you want to build Docker images? This may take 10-30 minutes on slow networks. (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_services
    else
        print_warning "Skipping build - using existing images"
    fi
    
    start_platform
    show_services
    
    print_status "⏰ Waiting for all services to fully initialize..."
    show_progress 30 "Final startup checks"
    
    check_health
    
    echo ""
    print_success "🎉 BetBet Platform with Full Monitoring is Ready! 🎉"
    echo ""
    print_status "📊 To view your statistics:"
    echo "  1. Grafana Dashboards: http://localhost:3002 (admin/admin123)"
    echo "  2. Admin Dashboard:    http://localhost:3001"
    echo "  3. Prometheus Metrics: http://localhost:9090"
    echo ""
    print_status "🔧 Management Commands:"
    echo "  View logs:    docker-compose logs -f [service-name]"
    echo "  Stop all:     ./scripts/stop.sh"
    echo "  Check status: ./scripts/status.sh"
    echo ""
    print_warning "💡 If Grafana shows 'loading' - wait 2-3 more minutes for full initialization"
}

# Handle script arguments
case "${1:-}" in
    --build-only)
        check_dependencies
        setup_environment
        build_services
        ;;
    --no-build)
        check_dependencies
        setup_environment
        start_platform
        show_services
        ;;
    --health-check)
        check_health
        ;;
    *)
        main
        ;;
esac
