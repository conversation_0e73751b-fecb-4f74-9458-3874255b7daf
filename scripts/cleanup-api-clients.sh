#!/bin/bash

# BetBet Platform - API Client Cleanup Script
# ===========================================
# 
# This script removes old API client files and updates imports
# to use the unified API client pattern.

set -e

echo "🧹 Starting BetBet API Client Cleanup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}📁 Working directory: $PROJECT_ROOT${NC}"

# Step 1: Remove deprecated API client files
echo -e "\n${YELLOW}Step 1: Removing deprecated API client files...${NC}"

DEPRECATED_FILES=(
    "frontend/web/src/lib/expert-analyst-api.ts"
    "frontend/web/src/lib/wallet-api.ts"
    "frontend/web/src/lib/leaderboard-api.ts"
    "frontend/web/src/lib/sports-api.ts"
    "frontend/web/src/lib/trading-api.ts"
    "frontend/web/src/lib/gaming-api.ts"
    "frontend/web/src/lib/multi-service-api.ts"
    "frontend/web/src/lib/unified-api-client.ts"
    "frontend/admin/src/lib/old-api-client.ts"
    "docs/BETBET_API_FRONTEND_MAPPING.md"
)

for file in "${DEPRECATED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${RED}🗑️  Removing: $file${NC}"
        rm "$file"
    else
        echo -e "${GREEN}✅ Already removed: $file${NC}"
    fi
done

# Step 2: Find and report remaining direct service references
echo -e "\n${YELLOW}Step 2: Scanning for direct service references...${NC}"

echo -e "${BLUE}🔍 Searching for localhost:8001-8009 references...${NC}"
if grep -r "localhost:800[1-9]" frontend/ 2>/dev/null; then
    echo -e "${RED}⚠️  Found direct service references that need manual cleanup${NC}"
else
    echo -e "${GREEN}✅ No direct service references found${NC}"
fi

echo -e "${BLUE}🔍 Searching for old API client imports...${NC}"
OLD_IMPORTS=(
    "expert-analyst-api"
    "wallet-api"
    "leaderboard-api"
    "sports-api"
    "trading-api"
    "gaming-api"
    "multi-service-api"
)

FOUND_OLD_IMPORTS=false
for import in "${OLD_IMPORTS[@]}"; do
    if grep -r "from.*$import" frontend/ 2>/dev/null; then
        echo -e "${RED}⚠️  Found old import: $import${NC}"
        FOUND_OLD_IMPORTS=true
    fi
done

if [ "$FOUND_OLD_IMPORTS" = false ]; then
    echo -e "${GREEN}✅ No old API client imports found${NC}"
fi

# Step 3: Check environment variables
echo -e "\n${YELLOW}Step 3: Checking environment variables...${NC}"

ENV_FILES=(
    "frontend/web/.env.local"
    "frontend/web/.env"
    "frontend/admin/.env.local"
    "frontend/admin/.env"
    ".env"
)

DEPRECATED_ENV_VARS=(
    "NEXT_PUBLIC_GAMING_API_URL"
    "NEXT_PUBLIC_WALLET_API_URL"
    "NEXT_PUBLIC_EXPERT_API_URL"
    "NEXT_PUBLIC_SPORTS_API_URL"
    "NEXT_PUBLIC_TRADING_API_URL"
    "NEXT_PUBLIC_CUSTOM_BETTING_API_URL"
    "NEXT_PUBLIC_LEADERBOARDS_API_URL"
    "NEXT_PUBLIC_WS_BASE_URL"
    "NEXT_PUBLIC_GAMING_WS_URL"
)

for env_file in "${ENV_FILES[@]}"; do
    if [ -f "$env_file" ]; then
        echo -e "${BLUE}📄 Checking: $env_file${NC}"
        for var in "${DEPRECATED_ENV_VARS[@]}"; do
            if grep -q "^$var=" "$env_file" 2>/dev/null; then
                echo -e "${RED}⚠️  Found deprecated variable: $var in $env_file${NC}"
            fi
        done
    fi
done

# Step 4: Verify unified API client usage
echo -e "\n${YELLOW}Step 4: Verifying unified API client usage...${NC}"

UNIFIED_CLIENT_FILES=(
    "frontend/web/src/lib/api-client-unified.ts"
    "frontend/web/src/lib/unified-api-routes.ts"
    "frontend/web/src/lib/clerk-api.ts"
)

for file in "${UNIFIED_CLIENT_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ Found: $file${NC}"
    else
        echo -e "${RED}❌ Missing: $file${NC}"
    fi
done

# Step 5: Generate cleanup report
echo -e "\n${YELLOW}Step 5: Generating cleanup report...${NC}"

REPORT_FILE="api-cleanup-report.md"
cat > "$REPORT_FILE" << EOF
# BetBet API Client Cleanup Report
Generated: $(date)

## Files Removed
EOF

for file in "${DEPRECATED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "- ✅ $file" >> "$REPORT_FILE"
    else
        echo "- ❌ $file (still exists)" >> "$REPORT_FILE"
    fi
done

cat >> "$REPORT_FILE" << EOF

## Remaining Issues

### Direct Service References
\`\`\`
$(grep -r "localhost:800[1-9]" frontend/ 2>/dev/null || echo "None found")
\`\`\`

### Old API Client Imports
\`\`\`
$(for import in "${OLD_IMPORTS[@]}"; do grep -r "from.*$import" frontend/ 2>/dev/null; done || echo "None found")
\`\`\`

### Deprecated Environment Variables
EOF

for env_file in "${ENV_FILES[@]}"; do
    if [ -f "$env_file" ]; then
        echo "#### $env_file" >> "$REPORT_FILE"
        for var in "${DEPRECATED_ENV_VARS[@]}"; do
            if grep -q "^$var=" "$env_file" 2>/dev/null; then
                echo "- ❌ $var" >> "$REPORT_FILE"
            fi
        done
    fi
done

cat >> "$REPORT_FILE" << EOF

## Next Steps

1. **Update remaining imports** to use unified API client:
   \`\`\`typescript
   // ❌ Old
   import { expertApi } from './expert-analyst-api';
   
   // ✅ New
   import api from './api-client-unified';
   \`\`\`

2. **Remove deprecated environment variables** from .env files

3. **Update component imports** to use unified patterns

4. **Test all API functionality** with unified client

## Verification Commands

\`\`\`bash
# Check for remaining issues
grep -r "localhost:800[1-9]" frontend/
grep -r "expert-analyst-api\|wallet-api\|sports-api" frontend/

# Verify unified client usage
grep -r "api-client-unified" frontend/
\`\`\`
EOF

echo -e "${GREEN}📊 Report generated: $REPORT_FILE${NC}"

# Step 6: Summary
echo -e "\n${GREEN}🎉 API Client Cleanup Complete!${NC}"
echo -e "\n${BLUE}📋 Summary:${NC}"
echo -e "✅ Removed deprecated API client files"
echo -e "✅ Scanned for direct service references"
echo -e "✅ Checked environment variables"
echo -e "✅ Verified unified client files"
echo -e "✅ Generated cleanup report"

echo -e "\n${YELLOW}📖 Next Steps:${NC}"
echo -e "1. Review the cleanup report: ${BLUE}$REPORT_FILE${NC}"
echo -e "2. Update any remaining old imports manually"
echo -e "3. Remove deprecated environment variables"
echo -e "4. Test the application with unified API client"

echo -e "\n${GREEN}🚀 Your API architecture is now clean and unified!${NC}"
