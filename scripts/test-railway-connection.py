#!/usr/bin/env python3
"""
Test Railway database connection and API deployment
"""

import asyncio
import aiohttp
import os
import sys

async def test_api_health():
    """Test API health endpoint"""
    api_url = "https://gaming-engine-production.up.railway.app"
    
    print(f"🔍 Testing API at {api_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test health endpoint
            async with session.get(f"{api_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health check: {data.get('status', 'unknown')}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return False

async def test_api_endpoints():
    """Test available API endpoints"""
    api_url = "https://gaming-engine-production.up.railway.app"
    
    endpoints = [
        "/",
        "/health", 
        "/ready",
        "/docs",
        "/api/v1/gaming/games",
        "/api/v1/gaming/sessions"
    ]
    
    print(f"\n🔍 Testing API endpoints...")
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            try:
                async with session.get(f"{api_url}{endpoint}") as response:
                    if response.status < 400:
                        print(f"✅ {endpoint} - Status: {response.status}")
                    else:
                        print(f"⚠️  {endpoint} - Status: {response.status}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")

async def main():
    """Main test function"""
    print("🎮 BetBet Gaming Engine - Railway Connection Test")
    print("=" * 50)
    
    # Test API health
    api_healthy = await test_api_health()
    
    if api_healthy:
        print("✅ API is responding")
        await test_api_endpoints()
    else:
        print("❌ API is not responding")
        sys.exit(1)
    
    print("\n🎉 Railway deployment test completed!")

if __name__ == "__main__":
    asyncio.run(main())