#!/bin/bash

# BetBet Platform Startup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON>er and Docker Compose are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Create .env file if it doesn't exist
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please update the .env file with your actual configuration before running the services."
        print_warning "Pay special attention to JWT_SECRET, database passwords, and API keys."
    else
        print_success "Environment file exists"
    fi
}

# Build all services
build_services() {
    print_status "Building Docker images..."
    docker-compose build --parallel
    print_success "Docker images built successfully"
}

# Start the platform
start_platform() {
    print_status "Starting BetBet Platform..."
    
    # Start infrastructure services first
    print_status "Starting infrastructure services (PostgreSQL, Redis)..."
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Start backend services
    print_status "Starting backend services..."
    docker-compose up -d api-gateway websocket-manager gaming-engine odds-exchange expert-analysis sports-analysis leaderboards-service
    
    # Wait for backend services to be ready
    print_status "Waiting for backend services to be ready..."
    sleep 15
    
    # Start frontend services
    print_status "Starting frontend services..."
    docker-compose up -d web-frontend admin-dashboard
    
    # Start monitoring and analytics
    print_status "Starting monitoring services (this may take time on slow networks)..."
    docker-compose up -d pgadmin prometheus grafana
    
    print_success "BetBet Platform started successfully!"
}

# Display service information
show_services() {
    echo ""
    print_success "=== BetBet Platform Services ==="
    echo ""
    print_status "Frontend Services:"
    echo "  🌐 Web App:      http://localhost:3000"
    echo "  ⚙️  Admin Panel: http://localhost:3001"
    echo ""
    print_status "API Services:"
    echo "  🔌 API Gateway: http://localhost:8000"
    echo "  📡 WebSocket:   ws://localhost:8007/ws"
    echo ""
    print_status "Backend Services:"
    echo "  🎮 Gaming:      http://localhost:8001"
    echo "  🎯 Betting:     http://localhost:8002"
    echo "  📈 Trading:     http://localhost:8003"
    echo "  👨‍💼 Experts:     http://localhost:8004"
    echo "  ⚽ Sports:      http://localhost:8005"
    echo "  🏆 Leaderboards: http://localhost:8006"
    echo ""
    print_status "Database & Monitoring:"
    echo "  🗄️  PostgreSQL:  localhost:5432"
    echo "  🔴 Redis:       localhost:6379"
    echo "  📊 PgAdmin:     http://localhost:5050"
    echo "  📈 Prometheus:  http://localhost:9090"
    echo "  📊 Grafana:     http://localhost:3002 (admin/admin123)"
    echo ""
    print_status "Health Checks:"
    echo "  All services have health check endpoints at /health"
    echo ""
}

# Check service health
check_health() {
    print_status "Checking service health..."
    
    services=(
        "api-gateway:8000"
        "websocket-manager:8080"
        "gaming-engine:8001"
        "odds-exchange:8003"
        "expert-analysis:8004"
        "sports-analysis:8005"
        "leaderboards-service:8006"
    )
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if curl -f -s "http://localhost:$port/health" > /dev/null; then
            print_success "$name is healthy"
        else
            print_warning "$name health check failed"
        fi
    done
}

# Main execution
main() {
    echo ""
    print_status "🚀 Starting BetBet Platform 🚀"
    echo ""
    
    check_dependencies
    setup_environment
    build_services
    start_platform
    
    echo ""
    show_services
    
    print_status "Waiting for all services to be ready..."
    sleep 20
    
    check_health
    
    echo ""
    print_success "🎉 BetBet Platform is now running! 🎉"
    print_status "Use 'docker-compose logs -f [service-name]' to view logs"
    print_status "Use './scripts/stop.sh' to stop all services"
    print_status "Use './scripts/status.sh' to check service status"
}

# Handle script arguments
case "${1:-}" in
    --build-only)
        check_dependencies
        setup_environment
        build_services
        ;;
    --no-build)
        check_dependencies
        setup_environment
        start_platform
        show_services
        ;;
    --health-check)
        check_health
        ;;
    *)
        main
        ;;
esac