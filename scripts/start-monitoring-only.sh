#!/bin/bash

# Start only Grafana and Prometheus for existing BetBet installations
# Perfect for slow networks - just adds monitoring to running platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_progress() {
    echo -e "${CYAN}[PROGRESS]${NC} $1"
}

# Show progress for slow downloads
show_progress() {
    local duration=$1
    local message=$2
    
    echo -n -e "${CYAN}[PROGRESS]${NC} $message"
    for ((i=1; i<=duration; i++)); do
        echo -n "."
        sleep 1
    done
    echo " Done!"
}

echo ""
print_status "🔧 Adding Grafana & Prometheus to BetBet Platform"
print_warning "⏰ Optimized for slow networks (Zimbabwe-friendly)"
echo ""

# Check if platform is running
print_status "Checking if BetBet platform is running..."

if ! docker ps | grep -q "betbet-"; then
    print_warning "BetBet platform doesn't seem to be running."
    echo ""
    read -p "Do you want to start the full platform first? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Starting full platform..."
        ./scripts/start-with-monitoring.sh
        exit 0
    else
        print_warning "Starting monitoring services anyway..."
    fi
else
    print_success "BetBet platform is running"
fi

# Start monitoring services
print_status "Starting monitoring services..."
print_warning "This may take 5-15 minutes on slow networks - downloading Grafana & Prometheus images"

print_progress "Downloading and starting Prometheus..."
docker-compose up -d prometheus

print_progress "Downloading and starting Grafana (this is the big one)..."
docker-compose up -d grafana

print_status "Waiting for services to initialize..."
show_progress 30 "Monitoring services starting up"

# Check if services are running
print_status "Checking monitoring services..."

if docker ps | grep -q "betbet-prometheus"; then
    print_success "✅ Prometheus is running"
else
    print_warning "⚠️  Prometheus failed to start"
fi

if docker ps | grep -q "betbet-grafana"; then
    print_success "✅ Grafana is running"
else
    print_warning "⚠️  Grafana failed to start"
fi

# Test endpoints
print_status "Testing monitoring endpoints..."

if curl -f -s "http://localhost:9090" > /dev/null 2>&1; then
    print_success "✅ Prometheus accessible at http://localhost:9090"
else
    print_warning "⚠️  Prometheus not yet accessible (may need more time)"
fi

if curl -f -s "http://localhost:3002" > /dev/null 2>&1; then
    print_success "✅ Grafana accessible at http://localhost:3002"
else
    print_warning "⚠️  Grafana not yet accessible (may need 2-3 more minutes)"
fi

# Test metrics endpoint
if curl -f -s "http://localhost:8002/metrics" > /dev/null 2>&1; then
    print_success "✅ BetBet metrics available for Prometheus"
else
    print_warning "⚠️  BetBet metrics not available (custom-betting service may not be running)"
fi

echo ""
print_success "🎉 Monitoring Services Added Successfully! 🎉"
echo ""
print_status "📊 Access Your Monitoring:"
echo "  🔍 Prometheus:  http://localhost:9090"
echo "  📊 Grafana:     http://localhost:3002"
echo "  👤 Grafana Login: admin / admin123"
echo ""
print_status "📈 Available Dashboards:"
echo "  📊 BetBet Platform Overview"
echo "  🔧 BetBet Service Health"
echo "  📈 Custom Business Metrics"
echo ""
print_status "🎯 Real-Time Metrics:"
echo "  🎯 Custom Betting: http://localhost:8002/metrics"
echo "  📊 Platform Stats: http://localhost:3001/api/betting/overview"
echo "  ⚙️  System Health:  http://localhost:3001/api/analytics/system-metrics"
echo ""
print_warning "💡 If Grafana shows 'loading' - wait 2-3 more minutes for full initialization"
print_status "🔧 Use 'docker-compose logs grafana' to check Grafana startup logs"
echo ""
