#!/usr/bin/env python3
"""
Verify Gaming Engine Schema Deployment on Railway PostgreSQL
============================================================

This script connects to Railway's PostgreSQL database and verifies
that all expected tables, indexes, and functions are properly deployed.
"""

import asyncio
import os
import sys
from pathlib import Path
import structlog

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


async def verify_schema_deployment():
    """Verify that the gaming_engine schema is properly deployed"""
    try:
        from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
        
        # Create database settings
        settings = DatabaseSettings()
        
        # Initialize database manager
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        
        logger.info("🔗 Connected to Railway PostgreSQL")
        
        # Check if gaming_engine schema exists
        schema_exists = await db_manager.execute_query(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'gaming_engine')",
            fetch_one=True
        )
        
        if not schema_exists or not schema_exists[0]:
            logger.error("❌ gaming_engine schema does not exist!")
            return False
        
        logger.info("✅ gaming_engine schema exists")
        
        # Check all expected tables
        expected_tables = [
            'games', 'game_sessions', 'session_participants', 
            'game_states', 'spectator_bets', 'tournaments',
            'tournament_participants', 'audit_logs', 'events',
            'performance_metrics', 'transactions'
        ]
        
        tables_found = []
        tables_missing = []
        
        for table in expected_tables:
            exists = await db_manager.execute_query(
                "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'gaming_engine' AND table_name = $1)",
                table,
                fetch_one=True
            )
            
            if exists and exists[0]:
                tables_found.append(table)
                logger.info(f"✅ Table found: gaming_engine.{table}")
            else:
                tables_missing.append(table)
                logger.error(f"❌ Table missing: gaming_engine.{table}")
        
        # Check for users table in public schema
        users_exists = await db_manager.execute_query(
            "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users')",
            fetch_one=True
        )
        
        if users_exists and users_exists[0]:
            logger.info("✅ public.users table exists")
        else:
            logger.warning("⚠️ public.users table missing")
        
        # Check for key indexes
        key_indexes = [
            'idx_games_category_active',
            'idx_game_sessions_state_game', 
            'idx_session_participants_session_user',
            'idx_transactions_user_type'
        ]
        
        indexes_found = []
        indexes_missing = []
        
        for index in key_indexes:
            exists = await db_manager.execute_query(
                "SELECT EXISTS(SELECT 1 FROM pg_indexes WHERE indexname = $1)",
                index,
                fetch_one=True
            )
            
            if exists and exists[0]:
                indexes_found.append(index)
                logger.info(f"✅ Index found: {index}")
            else:
                indexes_missing.append(index)
                logger.warning(f"⚠️ Index missing: {index}")
        
        # Summary
        logger.info("📊 Schema Verification Summary:")
        logger.info(f"Tables found: {len(tables_found)}/{len(expected_tables)}")
        logger.info(f"Indexes found: {len(indexes_found)}/{len(key_indexes)}")
        
        if tables_missing:
            logger.error(f"Missing tables: {tables_missing}")
            
        if indexes_missing:
            logger.warning(f"Missing indexes: {indexes_missing}")
        
        # Test basic functionality
        try:
            # Try to insert and query a test record
            test_game_id = await db_manager.execute_query(
                """
                INSERT INTO gaming_engine.games (name, slug, category, description, min_players, max_players)
                VALUES ('Test Game', 'test-game', 'trivia', 'Test game for verification', 2, 4)
                RETURNING id
                """,
                fetch_one=True
            )
            
            if test_game_id:
                logger.info("✅ Insert operation successful")
                
                # Clean up test record
                await db_manager.execute_query(
                    "DELETE FROM gaming_engine.games WHERE id = $1",
                    test_game_id[0]
                )
                logger.info("✅ Delete operation successful")
                
        except Exception as e:
            logger.error("❌ Database operations failed", error=str(e))
            return False
        
        await db_manager.shutdown()
        
        schema_complete = len(tables_missing) == 0
        
        if schema_complete:
            logger.info("🎉 Schema verification SUCCESSFUL - All tables deployed!")
            return True
        else:
            logger.error("❌ Schema verification FAILED - Missing tables!")
            return False
        
    except Exception as e:
        logger.error("❌ Schema verification failed", error=str(e))
        return False


async def main():
    """Main verification function"""
    logger.info("🔍 Starting Railway PostgreSQL schema verification...")
    
    success = await verify_schema_deployment()
    
    if success:
        logger.info("✅ VERIFICATION COMPLETE: Schema is properly deployed!")
        print("\n🎉 ALL TABLES VERIFIED ON RAILWAY POSTGRESQL!")
        print("Database schema deployment confirmed successful.")
        print("Claude-API can proceed with confidence.")
    else:
        logger.error("❌ VERIFICATION FAILED: Schema deployment incomplete!")
        print("\n🚨 SCHEMA DEPLOYMENT VERIFICATION FAILED!")
        print("Tables are missing from Railway PostgreSQL.")
        print("Claude-DB needs to re-deploy schema before handoff.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)