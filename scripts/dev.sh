#!/bin/bash

# BetBet Platform Development Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Install dependencies for all services
install_dependencies() {
    print_status "Installing dependencies for all services..."
    
    # API Gateway
    if [ -d "services/api-gateway" ]; then
        print_status "Installing API Gateway dependencies..."
        cd services/api-gateway && npm install && cd ../..
    fi
    
    # WebSocket Manager
    if [ -d "services/websocket-manager" ]; then
        print_status "Installing WebSocket Manager dependencies..."
        cd services/websocket-manager && npm install && cd ../..
    fi
    
    # Frontend dependencies
    if [ -d "frontend/web" ]; then
        print_status "Installing Web Frontend dependencies..."
        cd frontend/web && npm install && cd ../..
    fi
    
    if [ -d "frontend/admin" ]; then
        print_status "Installing Admin Frontend dependencies..."
        cd frontend/admin && npm install && cd ../..
    fi
    
    if [ -d "frontend/mobile" ]; then
        print_status "Installing Mobile App dependencies..."
        cd frontend/mobile && npm install && cd ../..
    fi
    
    print_success "All dependencies installed!"
}

# Start development environment
start_dev() {
    print_status "Starting development environment..."
    
    # Start infrastructure services
    print_status "Starting PostgreSQL and Redis..."
    docker-compose up -d postgres redis
    
    # Wait for database
    print_status "Waiting for database to be ready..."
    sleep 5
    
    print_success "Development infrastructure is ready!"
    print_status "You can now start individual services:"
    echo "  - API Gateway:      cd services/api-gateway && npm run dev"
    echo "  - WebSocket:        cd services/websocket-manager && npm run dev"
    echo "  - Web Frontend:     cd frontend/web && npm run dev"
    echo "  - Admin Frontend:   cd frontend/admin && npm run dev"
    echo "  - Mobile App:       cd frontend/mobile && npm start"
}

# Stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    docker-compose down
    print_success "Development environment stopped!"
}

# Reset development environment
reset_dev() {
    print_warning "This will remove all containers, volumes, and data!"
    read -p "Are you sure? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting development environment..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_success "Development environment reset!"
    else
        print_status "Reset cancelled."
    fi
}

# Run database migrations
migrate_db() {
    print_status "Running database migrations..."
    
    # Check if PostgreSQL is running
    if ! docker-compose ps postgres | grep -q "Up"; then
        print_status "Starting PostgreSQL..."
        docker-compose up -d postgres
        sleep 5
    fi
    
    # Run migrations by recreating the database init
    print_status "Applying database schema..."
    docker-compose exec postgres psql -U betbet_user -d postgres -c "SELECT version();"
    
    print_success "Database migrations completed!"
}

# Run tests
run_tests() {
    local service=$1
    
    if [ -z "$service" ]; then
        print_status "Running all tests..."
        
        # Test API Gateway
        if [ -d "services/api-gateway" ]; then
            print_status "Testing API Gateway..."
            cd services/api-gateway && npm test && cd ../..
        fi
        
        # Test WebSocket Manager
        if [ -d "services/websocket-manager" ]; then
            print_status "Testing WebSocket Manager..."
            cd services/websocket-manager && npm test && cd ../..
        fi
        
        # Test Frontend
        if [ -d "frontend/web" ]; then
            print_status "Testing Web Frontend..."
            cd frontend/web && npm test && cd ../..
        fi
        
        if [ -d "frontend/admin" ]; then
            print_status "Testing Admin Frontend..."
            cd frontend/admin && npm test && cd ../..
        fi
        
    else
        print_status "Running tests for $service..."
        case $service in
            "api-gateway")
                cd services/api-gateway && npm test && cd ../..
                ;;
            "websocket-manager")
                cd services/websocket-manager && npm test && cd ../..
                ;;
            "web")
                cd frontend/web && npm test && cd ../..
                ;;
            "admin")
                cd frontend/admin && npm test && cd ../..
                ;;
            *)
                print_error "Unknown service: $service"
                exit 1
                ;;
        esac
    fi
    
    print_success "Tests completed!"
}

# Lint code
lint_code() {
    local service=$1
    
    if [ -z "$service" ]; then
        print_status "Linting all code..."
        
        # Lint API Gateway
        if [ -d "services/api-gateway" ]; then
            print_status "Linting API Gateway..."
            cd services/api-gateway && npm run lint && cd ../..
        fi
        
        # Lint WebSocket Manager
        if [ -d "services/websocket-manager" ]; then
            print_status "Linting WebSocket Manager..."
            cd services/websocket-manager && npm run lint && cd ../..
        fi
        
        # Lint Frontend
        if [ -d "frontend/web" ]; then
            print_status "Linting Web Frontend..."
            cd frontend/web && npm run lint && cd ../..
        fi
        
        if [ -d "frontend/admin" ]; then
            print_status "Linting Admin Frontend..."
            cd frontend/admin && npm run lint && cd ../..
        fi
        
    else
        print_status "Linting $service..."
        case $service in
            "api-gateway")
                cd services/api-gateway && npm run lint && cd ../..
                ;;
            "websocket-manager")
                cd services/websocket-manager && npm run lint && cd ../..
                ;;
            "web")
                cd frontend/web && npm run lint && cd ../..
                ;;
            "admin")
                cd frontend/admin && npm run lint && cd ../..
                ;;
            *)
                print_error "Unknown service: $service"
                exit 1
                ;;
        esac
    fi
    
    print_success "Linting completed!"
}

# Show development info
show_dev_info() {
    echo ""
    print_status "🔧 BetBet Platform Development Environment 🔧"
    echo ""
    print_status "Available Commands:"
    echo "  ./scripts/dev.sh install     - Install all dependencies"
    echo "  ./scripts/dev.sh start       - Start development infrastructure"
    echo "  ./scripts/dev.sh stop        - Stop development infrastructure"
    echo "  ./scripts/dev.sh reset       - Reset development environment"
    echo "  ./scripts/dev.sh migrate     - Run database migrations"
    echo "  ./scripts/dev.sh test [service] - Run tests"
    echo "  ./scripts/dev.sh lint [service] - Lint code"
    echo ""
    print_status "Development Workflow:"
    echo "  1. ./scripts/dev.sh install    # Install dependencies"
    echo "  2. ./scripts/dev.sh start      # Start infrastructure"
    echo "  3. Start individual services in separate terminals"
    echo "  4. Make changes and test"
    echo "  5. ./scripts/dev.sh lint       # Check code quality"
    echo "  6. ./scripts/dev.sh test       # Run tests"
    echo ""
    print_status "Service URLs (when running):"
    echo "  Web Frontend:    http://localhost:3000"
    echo "  Admin Frontend:  http://localhost:3001"
    echo "  API Gateway:     http://localhost:8000"
    echo "  WebSocket:       ws://localhost:8007/ws"
    echo "  PostgreSQL:      localhost:5432"
    echo "  Redis:           localhost:6379"
    echo ""
}

show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  install        Install dependencies for all services"
    echo "  start          Start development infrastructure"
    echo "  stop           Stop development infrastructure"
    echo "  reset          Reset development environment"
    echo "  migrate        Run database migrations"
    echo "  test [service] Run tests (optionally for specific service)"
    echo "  lint [service] Lint code (optionally for specific service)"
    echo "  info           Show development information"
    echo "  help           Show this help message"
}

main() {
    case "${1:-}" in
        install)
            install_dependencies
            ;;
        start)
            start_dev
            ;;
        stop)
            stop_dev
            ;;
        reset)
            reset_dev
            ;;
        migrate)
            migrate_db
            ;;
        test)
            run_tests $2
            ;;
        lint)
            lint_code $2
            ;;
        info)
            show_dev_info
            ;;
        help|--help)
            show_usage
            exit 0
            ;;
        "")
            show_dev_info
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

main "$@"