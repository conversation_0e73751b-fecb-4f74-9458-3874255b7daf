#!/bin/bash

echo "=== Custom Betting Platform Test Suite ==="
echo

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    response=$(curl -s -w "\n%{http_code}" "$url")
    status=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n-1)
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓${NC} $name: Status $status"
        if [ -n "$body" ] && [ "$body" != "[]" ] && [ "$body" != "{}" ]; then
            echo "  Sample response: $(echo $body | jq -r '.[0] // . | if type == "object" then {id: .id, title: (.title // .name // "N/A")} else . end' 2>/dev/null || echo "$body" | head -c 100)"
        fi
    else
        echo -e "${RED}✗${NC} $name: Status $status (expected $expected_status)"
    fi
}

echo "1. Testing Service Health"
echo "------------------------"
test_endpoint "API Gateway Health" "http://localhost:8000/health"
test_endpoint "Custom Betting Health" "http://localhost:8000/api/custom-betting/health"

echo -e "\n2. Testing Market Discovery"
echo "-------------------------"
test_endpoint "Categories" "http://localhost:8000/api/custom-betting/categories"
test_endpoint "Markets List" "http://localhost:8000/api/custom-betting/markets?page=1&limit=5"
test_endpoint "Analytics Overview" "http://localhost:8000/api/custom-betting/analytics/overview"

echo -e "\n3. Testing Frontend Endpoints"
echo "----------------------------"
test_endpoint "Web App" "http://localhost:3000/custom-betting" 200
test_endpoint "Create Market Page" "http://localhost:3000/custom-betting/create" 200

echo -e "\n4. Market Creation Test"
echo "---------------------"
# Create a test market
MARKET_DATA='{
  "title": "Test Market from Script",
  "description": "This is a test market created by the test script",
  "resolution_criteria": "This market will resolve based on test results",
  "category_id": "e802d8f1-d85c-4f1f-88e3-5261d725f29f",
  "market_type": "binary",
  "minimum_stake": 1,
  "maximum_stake": 100,
  "outcomes": [
    {"title": "Yes", "description": "Test passes"},
    {"title": "No", "description": "Test fails"}
  ],
  "tags": ["test", "automated"]
}'

echo "Creating test market..."
CREATE_RESPONSE=$(curl -s -X POST \
  "http://localhost:8000/api/custom-betting/markets?creator_user_id=test-user-123&creator_type=admin" \
  -H "Content-Type: application/json" \
  -d "$MARKET_DATA")

if echo "$CREATE_RESPONSE" | jq -e '.id' >/dev/null 2>&1; then
    MARKET_ID=$(echo "$CREATE_RESPONSE" | jq -r '.id')
    echo -e "${GREEN}✓${NC} Market created successfully! ID: $MARKET_ID"
    
    # Test retrieving the created market
    test_endpoint "Retrieve Created Market" "http://localhost:8000/api/custom-betting/markets/$MARKET_ID"
else
    echo -e "${RED}✗${NC} Failed to create market"
    echo "Response: $CREATE_RESPONSE"
fi

echo -e "\n5. Summary Statistics"
echo "-------------------"
STATS=$(curl -s "http://localhost:8000/api/custom-betting/analytics/overview")
if [ -n "$STATS" ]; then
    echo "Active Markets: $(echo $STATS | jq -r '.active_markets')"
    echo "Total Volume: $$(echo $STATS | jq -r '.total_volume')"
    echo "Total Participants: $(echo $STATS | jq -r '.total_participants')"
    echo "Markets Settled Today: $(echo $STATS | jq -r '.markets_settled_today')"
fi

echo -e "\n${GREEN}=== Test Complete ===${NC}"