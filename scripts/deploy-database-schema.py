#!/usr/bin/env python3
"""
BetBet Platform - Database Schema Deployment Script
==================================================

Deploys the Gaming Engine database schema to Railway PostgreSQL.
This script connects directly to Railway's database and executes
the schema files in the correct order.
"""

import asyncio
import asyncpg
import os
import sys
from pathlib import Path
import structlog

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


class DatabaseSchemaDeployer:
    """Deploy Gaming Engine database schema to Railway PostgreSQL"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.schema_files = [
            project_root / "database" / "schemas" / "gaming_engine.sql",
            project_root / "database" / "schemas" / "gaming_engine_indexes.sql",
        ]
    
    async def connect(self):
        """Connect to Railway PostgreSQL database"""
        try:
            logger.info("Connecting to Railway PostgreSQL database...")
            
            # Convert asyncpg URL format if needed
            db_url = self.database_url
            if db_url.startswith("postgresql+asyncpg://"):
                db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
            
            self.connection = await asyncpg.connect(db_url)
            logger.info("✅ Connected to Railway PostgreSQL successfully")
            
            # Test connection
            result = await self.connection.fetchval("SELECT version()")
            logger.info("Database version", version=result)
            
        except Exception as e:
            logger.error("❌ Failed to connect to Railway PostgreSQL", error=str(e))
            raise
    
    async def check_schema_exists(self):
        """Check if gaming_engine schema already exists"""
        try:
            exists = await self.connection.fetchval(
                "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'gaming_engine')"
            )
            return exists
        except Exception as e:
            logger.error("Error checking schema existence", error=str(e))
            return False
    
    async def execute_sql_file(self, file_path: Path):
        """Execute a SQL file with error handling and logging"""
        try:
            logger.info(f"📄 Executing SQL file: {file_path.name}")
            
            # Read the SQL file
            sql_content = file_path.read_text()
            
            # Execute the SQL content
            await self.connection.execute(sql_content)
            
            logger.info(f"✅ Successfully executed: {file_path.name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to execute: {file_path.name}", error=str(e))
            raise
    
    async def verify_deployment(self):
        """Verify that the schema was deployed correctly"""
        try:
            logger.info("🔍 Verifying schema deployment...")
            
            # Check if schema exists
            schema_exists = await self.connection.fetchval(
                "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'gaming_engine')"
            )
            
            if not schema_exists:
                raise Exception("gaming_engine schema was not created")
            
            # Check for core tables
            core_tables = [
                'games', 'game_sessions', 'session_participants', 
                'game_states', 'spectator_bets', 'tournaments',
                'tournament_participants', 'audit_logs', 'events',
                'performance_metrics', 'transactions'
            ]
            
            for table in core_tables:
                exists = await self.connection.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'gaming_engine' AND table_name = $1)",
                    table
                )
                if not exists:
                    raise Exception(f"Table gaming_engine.{table} was not created")
                logger.info(f"✅ Table verified: gaming_engine.{table}")
            
            # Check for indexes (sample a few key ones)
            key_indexes = [
                'idx_games_category_active',
                'idx_game_sessions_state_game',
                'idx_session_participants_session_user',
                'idx_spectator_bets_session_active'
            ]
            
            for index in key_indexes:
                exists = await self.connection.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM pg_indexes WHERE indexname = $1)",
                    index
                )
                if not exists:
                    logger.warning(f"⚠️ Index not found: {index}")
                else:
                    logger.info(f"✅ Index verified: {index}")
            
            # Test database functions
            try:
                await self.connection.fetchval("SELECT current_user_id()")
                logger.info("✅ Helper functions working")
            except Exception as e:
                logger.warning("⚠️ Helper functions may need configuration", error=str(e))
            
            logger.info("🎉 Schema deployment verification completed successfully")
            
        except Exception as e:
            logger.error("❌ Schema verification failed", error=str(e))
            raise
    
    async def deploy_schema(self):
        """Deploy the complete Gaming Engine schema"""
        try:
            logger.info("🚀 Starting Gaming Engine schema deployment to Railway...")
            
            # Check if schema already exists
            if await self.check_schema_exists():
                logger.info("⚠️ gaming_engine schema already exists. Continuing with deployment...")
            
            # Execute schema files in order
            for schema_file in self.schema_files:
                if not schema_file.exists():
                    raise FileNotFoundError(f"Schema file not found: {schema_file}")
                
                await self.execute_sql_file(schema_file)
            
            # Verify deployment
            await self.verify_deployment()
            
            logger.info("✅ Gaming Engine schema deployment completed successfully!")
            
        except Exception as e:
            logger.error("❌ Schema deployment failed", error=str(e))
            raise
    
    async def close(self):
        """Close database connection"""
        if hasattr(self, 'connection') and self.connection:
            await self.connection.close()
            logger.info("Database connection closed")


async def main():
    """Main deployment function"""
    try:
        # Get database URL from environment
        database_url = os.environ.get("DATABASE_URL")
        
        if not database_url:
            # Try to get from Railway environment or fallback
            from services.gaming_engine.app.dependencies import get_settings
            settings = get_settings()
            database_url = settings.database_url
        
        if not database_url or database_url == "postgresql://localhost/betbet":
            logger.error("❌ DATABASE_URL not found. Please set Railway DATABASE_URL environment variable.")
            sys.exit(1)
        
        logger.info("🎯 Starting BetBet Gaming Engine database schema deployment")
        logger.info("Target database", url=database_url.split('@')[1] if '@' in database_url else "Railway PostgreSQL")
        
        # Create deployer and connect
        deployer = DatabaseSchemaDeployer(database_url)
        await deployer.connect()
        
        try:
            # Deploy schema
            await deployer.deploy_schema()
            
            logger.info("🎉 Database schema deployment completed successfully!")
            logger.info("💡 Next steps:")
            logger.info("  1. Test API endpoints: https://gaming-engine-production.up.railway.app/ready")
            logger.info("  2. Verify database health check shows 'healthy'")
            logger.info("  3. Test gaming endpoints return proper data")
            
        finally:
            await deployer.close()
    
    except Exception as e:
        logger.error("❌ Deployment failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())