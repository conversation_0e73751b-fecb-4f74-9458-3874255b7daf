#!/usr/bin/env python3
import asyncio
import sys
from pathlib import Path

# Add the services directory to path
services_dir = Path(__file__).parent.parent / 'services'
sys.path.insert(0, str(services_dir))

import os
os.chdir(str(services_dir / 'custom-betting'))

from main import app, get_database_write, MarketCategory
from sqlalchemy import select, text
from fastapi.testclient import TestClient

async def debug_categories():
    print("Debugging categories...")
    
    # Get database session
    async for db in get_database_write():
        try:
            # Raw SQL query
            result = await db.execute(text("SELECT COUNT(*) FROM custom_betting.market_categories"))
            count = result.scalar()
            print(f"Raw SQL count: {count}")
            
            # ORM query
            query = select(MarketCategory)
            result = await db.execute(query)
            categories = result.scalars().all()
            print(f"ORM query count: {len(categories)}")
            
            for cat in categories[:3]:
                print(f"  - {cat.name} (id: {cat.id})")
            
        finally:
            break

if __name__ == "__main__":
    asyncio.run(debug_categories())