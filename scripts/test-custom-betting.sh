#!/bin/bash

echo "Testing Custom Betting Service..."

# Test API Gateway Health
echo -e "\n1. Testing API Gateway..."
curl -s http://localhost:8000/health | jq '.' || echo "API Gateway not running"

# Test Custom Betting Service Health via API Gateway
echo -e "\n2. Testing Custom Betting Service via API Gateway..."
curl -s http://localhost:8000/api/custom-betting/health | jq '.' || echo "Custom Betting service not accessible"

# Test Categories Endpoint
echo -e "\n3. Testing Categories Endpoint..."
curl -s http://localhost:8000/api/custom-betting/categories | jq '.' || echo "Categories endpoint failed"

# Test Markets Endpoint
echo -e "\n4. Testing Markets Endpoint..."
curl -s "http://localhost:8000/api/custom-betting/markets?page=1&limit=10" | jq '.' || echo "Markets endpoint failed"

# Test Analytics Overview
echo -e "\n5. Testing Analytics Overview..."
curl -s http://localhost:8000/api/custom-betting/analytics/overview | jq '.' || echo "Analytics endpoint failed"

echo -e "\nTesting complete!"