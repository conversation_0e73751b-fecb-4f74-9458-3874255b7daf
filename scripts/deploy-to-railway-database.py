#!/usr/bin/env python3
"""
Deploy Schema to Railway PostgreSQL Database
============================================

This script specifically connects to Railway's PostgreSQL database
and deploys the complete gaming_engine schema.
"""

import asyncio
import asyncpg
import os
import sys
import requests
from pathlib import Path
import structlog

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


async def get_railway_database_url():
    """Get the Railway DATABASE_URL from the deployed API"""
    try:
        logger.info("🔍 Getting Railway DATABASE_URL from deployed API...")
        
        response = requests.get("https://gaming-engine-production.up.railway.app/debug/env", timeout=10)
        
        if response.status_code == 200:
            env_data = response.json()
            if env_data.get("database_url_set"):
                # We can't get the full URL from the API for security reasons
                # But we know Railway is providing it
                logger.info("✅ Railway DATABASE_URL is available")
                return "RAILWAY_PROVIDED"
            else:
                logger.error("❌ DATABASE_URL not set in Railway environment")
                return None
        else:
            logger.error("❌ Failed to reach Railway API", status=response.status_code)
            return None
            
    except Exception as e:
        logger.error("❌ Error checking Railway DATABASE_URL", error=str(e))
        return None


async def deploy_via_railway_api():
    """Deploy schema by creating an endpoint in the Railway-deployed API"""
    try:
        logger.info("🚀 Deploying schema via Railway API...")
        
        # Read the schema files
        project_root = Path(__file__).parent.parent
        schema_sql = (project_root / "database" / "schemas" / "gaming_engine.sql").read_text()
        
        # Create a deployment payload (we'll need to add an endpoint to the API)
        logger.info("📝 Schema ready for deployment")
        logger.info(f"Schema size: {len(schema_sql)} characters")
        
        return True
        
    except Exception as e:
        logger.error("❌ Failed to deploy via Railway API", error=str(e))
        return False


async def create_railway_deployment_endpoint():
    """Create a temporary endpoint in the API for schema deployment"""
    
    # First, let's create a simple deployment endpoint in the main.py
    endpoint_code = '''
@app.post("/admin/deploy-schema", tags=["admin"])
async def deploy_schema_to_railway():
    """Deploy Gaming Engine schema to Railway PostgreSQL (admin only)"""
    try:
        from services.shared.core.database.connection import _db_manager
        
        if not _db_manager:
            return {"error": "Database manager not initialized"}
        
        # Schema deployment SQL
        schema_sql = """
        -- Enable extensions
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        CREATE SCHEMA IF NOT EXISTS gaming_engine;
        
        -- Create users table if not exists
        CREATE TABLE IF NOT EXISTS public.users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            avatar_url TEXT,
            balance DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT true,
            is_verified BOOLEAN DEFAULT false,
            roles TEXT[] DEFAULT ARRAY['user']::TEXT[],
            permissions TEXT[] DEFAULT ARRAY[]::TEXT[],
            failed_login_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP WITH TIME ZONE,
            mfa_secret VARCHAR(255),
            mfa_enabled BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_by UUID,
            updated_by UUID,
            version INTEGER DEFAULT 1,
            deleted_at TIMESTAMP WITH TIME ZONE,
            deleted_by UUID
        );
        """
        
        # Execute schema
        async with _db_manager.get_raw_connection() as conn:
            await conn.execute(schema_sql)
        
        return {"success": True, "message": "Schema deployment started"}
        
    except Exception as e:
        return {"error": str(e)}
'''
    
    logger.info("📝 Deployment endpoint code ready")
    return endpoint_code


async def main():
    """Main deployment function"""
    try:
        logger.info("🎯 Starting Railway PostgreSQL schema deployment")
        
        # Check if Railway DATABASE_URL is available
        railway_url_status = await get_railway_database_url()
        
        if railway_url_status != "RAILWAY_PROVIDED":
            logger.error("❌ Railway DATABASE_URL not available")
            logger.info("💡 Make sure the Railway service is connected to PostgreSQL")
            return False
        
        # Create deployment strategy
        logger.info("📋 Creating deployment strategy...")
        logger.info("🔧 We need to add a deployment endpoint to the Railway API")
        
        endpoint_code = await create_railway_deployment_endpoint()
        
        logger.info("📝 Deployment endpoint created")
        logger.info("🚀 Next steps:")
        logger.info("  1. Add the deployment endpoint to services/gaming-engine/app/main.py")
        logger.info("  2. Push to git to trigger Railway deployment")
        logger.info("  3. Call POST https://gaming-engine-production.up.railway.app/admin/deploy-schema")
        logger.info("  4. Verify deployment with GET /ready endpoint")
        
        return True
        
    except Exception as e:
        logger.error("❌ Railway deployment preparation failed", error=str(e))
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)