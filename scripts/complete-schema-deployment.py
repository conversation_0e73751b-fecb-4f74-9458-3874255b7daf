#!/usr/bin/env python3
"""
Complete Gaming Engine Schema Deployment
========================================

Deploy the remaining missing tables to Railway PostgreSQL.
"""

import asyncio
import sys
from pathlib import Path
import structlog

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


async def deploy_missing_tables():
    """Deploy the missing tables to complete the schema"""
    try:
        from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
        
        # Create database settings
        settings = DatabaseSettings()
        
        # Initialize database manager
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        
        logger.info("🔗 Connected to Railway PostgreSQL")
        
        # Deploy remaining tables
        missing_tables = [
            # Session participants table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.session_participants (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                session_id UUID NOT NULL REFERENCES gaming_engine.game_sessions(id) ON DELETE CASCADE,
                user_id UUID NOT NULL REFERENCES public.users(id),
                stake_amount DECIMAL(10,2) NOT NULL CHECK (stake_amount >= 0),
                join_order INTEGER NOT NULL CHECK (join_order > 0),
                current_score DECIMAL(10,4) DEFAULT 0,
                final_score DECIMAL(10,4),
                final_position INTEGER,
                performance_data JSONB DEFAULT '{}',
                joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                left_at TIMESTAMP WITH TIME ZONE,
                total_play_time INTERVAL,
                payout_amount DECIMAL(10,2) DEFAULT 0 CHECK (payout_amount >= 0),
                bonus_amount DECIMAL(10,2) DEFAULT 0,
                payout_processed BOOLEAN DEFAULT false,
                payout_processed_at TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN DEFAULT true,
                is_ready BOOLEAN DEFAULT false,
                connection_status VARCHAR(20) DEFAULT 'connected' CHECK (connection_status IN ('connected', 'disconnected', 'reconnecting')),
                last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                validation_status VARCHAR(20) DEFAULT 'pending' CHECK (validation_status IN ('pending', 'validated', 'flagged', 'disqualified')),
                validation_data JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                UNIQUE(session_id, user_id),
                CHECK (final_position IS NULL OR final_position > 0)
            );
            """,
            
            # Game states table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.game_states (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                session_id UUID NOT NULL REFERENCES gaming_engine.game_sessions(id) ON DELETE CASCADE,
                state_type VARCHAR(30) NOT NULL CHECK (state_type IN ('initial', 'round_start', 'player_action', 'round_end', 'game_end', 'pause', 'resume')),
                state_data JSONB NOT NULL,
                sequence_number INTEGER NOT NULL,
                round_number INTEGER,
                affected_player_id UUID REFERENCES public.users(id),
                state_hash VARCHAR(64),
                previous_state_id UUID REFERENCES gaming_engine.game_states(id),
                processing_time_ms DECIMAL(8,3),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Spectator bets table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.spectator_bets (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                session_id UUID NOT NULL REFERENCES gaming_engine.game_sessions(id),
                user_id UUID NOT NULL REFERENCES public.users(id),
                bet_type VARCHAR(50) NOT NULL CHECK (bet_type IN ('winner', 'score_prediction', 'performance_metric', 'round_outcome', 'custom')),
                bet_data JSONB NOT NULL,
                stake_amount DECIMAL(10,2) NOT NULL CHECK (stake_amount > 0),
                potential_payout DECIMAL(10,2) NOT NULL CHECK (potential_payout >= stake_amount),
                actual_payout DECIMAL(10,2) DEFAULT 0,
                odds_at_placement DECIMAL(8,4),
                market_data JSONB,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'won', 'lost', 'cancelled', 'void')),
                placed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                settled_at TIMESTAMP WITH TIME ZONE,
                settlement_data JSONB,
                is_market_maker_bet BOOLEAN DEFAULT false,
                matched_bet_id UUID REFERENCES gaming_engine.spectator_bets(id),
                risk_category VARCHAR(20) DEFAULT 'standard' CHECK (risk_category IN ('low', 'standard', 'high', 'premium')),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                deleted_at TIMESTAMP WITH TIME ZONE,
                deleted_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Tournaments table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.tournaments (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name VARCHAR(200) NOT NULL,
                slug VARCHAR(100) UNIQUE NOT NULL,
                description TEXT,
                game_id UUID NOT NULL REFERENCES gaming_engine.games(id),
                tournament_type VARCHAR(30) NOT NULL CHECK (tournament_type IN ('single_elimination', 'double_elimination', 'round_robin', 'swiss', 'ladder', 'custom')),
                entry_fee DECIMAL(10,2) NOT NULL CHECK (entry_fee >= 0),
                max_participants INTEGER NOT NULL CHECK (max_participants > 1),
                current_participants INTEGER DEFAULT 0 CHECK (current_participants >= 0),
                min_participants INTEGER DEFAULT 4 CHECK (min_participants > 1),
                prize_pool DECIMAL(10,2) NOT NULL DEFAULT 0,
                prize_distribution JSONB,
                guaranteed_prize_pool DECIMAL(10,2) DEFAULT 0,
                registration_opens_at TIMESTAMP WITH TIME ZONE,
                registration_closes_at TIMESTAMP WITH TIME ZONE,
                starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
                estimated_ends_at TIMESTAMP WITH TIME ZONE,
                actual_ended_at TIMESTAMP WITH TIME ZONE,
                status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'registration_open', 'registration_closed', 'active', 'completed', 'cancelled')),
                current_round INTEGER DEFAULT 0,
                total_rounds INTEGER,
                tournament_config JSONB DEFAULT '{}',
                bracket_data JSONB,
                is_public BOOLEAN DEFAULT true,
                is_featured BOOLEAN DEFAULT false,
                skill_level_restriction VARCHAR(20) CHECK (skill_level_restriction IN ('beginner', 'intermediate', 'advanced', 'expert', 'any')),
                organizer_id UUID REFERENCES public.users(id),
                sponsor_data JSONB,
                champion_id UUID REFERENCES public.users(id),
                final_standings JSONB,
                tournament_statistics JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                deleted_at TIMESTAMP WITH TIME ZONE,
                deleted_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Tournament participants table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.tournament_participants (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                tournament_id UUID NOT NULL REFERENCES gaming_engine.tournaments(id) ON DELETE CASCADE,
                user_id UUID NOT NULL REFERENCES public.users(id),
                entry_fee_paid DECIMAL(10,2) NOT NULL,
                seed_number INTEGER,
                bracket_position INTEGER,
                current_round INTEGER DEFAULT 0,
                wins INTEGER DEFAULT 0,
                losses INTEGER DEFAULT 0,
                total_score DECIMAL(10,4) DEFAULT 0,
                final_position INTEGER,
                prize_amount DECIMAL(10,2) DEFAULT 0,
                prize_paid BOOLEAN DEFAULT false,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'eliminated', 'advanced', 'champion', 'disqualified')),
                eliminated_in_round INTEGER,
                registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                eliminated_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                UNIQUE(tournament_id, user_id)
            );
            """
        ]
        
        # Deploy each table
        for i, table_sql in enumerate(missing_tables, 1):
            try:
                await db_manager.execute_query(table_sql)
                logger.info(f"✅ Deployed table {i}/{len(missing_tables)}")
            except Exception as e:
                logger.error(f"❌ Failed to deploy table {i}", error=str(e))
                
        # Create essential indexes
        logger.info("📄 Creating essential indexes...")
        
        essential_indexes = [
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_session_participants_session_user ON gaming_engine.session_participants(session_id, user_id);",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_states_session_sequence ON gaming_engine.game_states(session_id, sequence_number DESC);",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spectator_bets_session_active ON gaming_engine.spectator_bets(session_id, status, placed_at DESC) WHERE deleted_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tournaments_status_start ON gaming_engine.tournaments(status, starts_at) WHERE deleted_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tournament_participants_tournament ON gaming_engine.tournament_participants(tournament_id, status);"
        ]
        
        for index_sql in essential_indexes:
            try:
                await db_manager.execute_query(index_sql)
                logger.info("✅ Index created successfully")
            except Exception as e:
                logger.warning("Index creation warning", error=str(e))
        
        await db_manager.shutdown()
        
        logger.info("🎉 Missing tables deployment completed!")
        return True
        
    except Exception as e:
        logger.error("❌ Table deployment failed", error=str(e))
        return False


async def main():
    """Main deployment function"""
    logger.info("🚀 Deploying missing gaming_engine tables to Railway...")
    
    success = await deploy_missing_tables()
    
    if success:
        logger.info("✅ All missing tables deployed successfully!")
        
        logger.info("🎉 COMPLETE SUCCESS: All missing tables deployed!")
        return True
    else:
        logger.error("❌ Table deployment failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)