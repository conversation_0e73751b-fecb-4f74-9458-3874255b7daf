#!/bin/bash

# Test script for chess WebSocket connections
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo ""
print_status "🎮 Testing Chess WebSocket Connections"
echo ""

# Test 1: Check if all services are running
print_status "Checking required services..."

required_services=("betbet-api-gateway" "betbet-gaming" "betbet-websocket" "betbet-web")
all_running=true

for service in "${required_services[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$service"; then
        print_success "$service is running"
    else
        print_error "$service is NOT running"
        all_running=false
    fi
done

if [ "$all_running" = false ]; then
    print_error "Not all required services are running!"
    exit 1
fi

# Test 2: Check API Gateway WebSocket routing
print_status "Testing API Gateway WebSocket routing..."

# Test general WebSocket endpoint
general_ws_test=$(timeout 5 curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ws || echo "TIMEOUT")
if [ "$general_ws_test" = "400" ] || [ "$general_ws_test" = "426" ]; then
    print_success "General WebSocket endpoint accessible (HTTP $general_ws_test - expected for WebSocket upgrade)"
else
    print_warning "General WebSocket endpoint returned: $general_ws_test"
fi

# Test chess WebSocket endpoint
chess_ws_test=$(timeout 5 curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ws/chess/test-game || echo "TIMEOUT")
if [ "$chess_ws_test" = "400" ] || [ "$chess_ws_test" = "426" ]; then
    print_success "Chess WebSocket endpoint accessible (HTTP $chess_ws_test - expected for WebSocket upgrade)"
else
    print_warning "Chess WebSocket endpoint returned: $chess_ws_test"
fi

# Test 3: Check gaming engine WebSocket endpoints directly
print_status "Testing Gaming Engine WebSocket endpoints..."

gaming_ws_test=$(timeout 5 curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/ws/chess/test-game || echo "TIMEOUT")
if [ "$gaming_ws_test" = "400" ] || [ "$gaming_ws_test" = "426" ]; then
    print_success "Gaming Engine chess WebSocket accessible (HTTP $gaming_ws_test)"
else
    print_warning "Gaming Engine chess WebSocket returned: $gaming_ws_test"
fi

# Test 4: Check WebSocket Manager
print_status "Testing WebSocket Manager..."

ws_manager_test=$(timeout 5 curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health || echo "TIMEOUT")
if [ "$ws_manager_test" = "200" ]; then
    print_success "WebSocket Manager is healthy"
else
    print_warning "WebSocket Manager health check returned: $ws_manager_test"
fi

# Test 5: Check environment variables
print_status "Checking environment variables..."

if [ -f .env ]; then
    if grep -q "NEXT_PUBLIC_WS_URL=ws://localhost:8000" .env; then
        print_success "NEXT_PUBLIC_WS_URL is correctly set"
    else
        print_error "NEXT_PUBLIC_WS_URL is not set correctly"
    fi
    
    if grep -q "NEXT_PUBLIC_GAMING_ENGINE_WS_URL=ws://localhost:8000" .env; then
        print_success "NEXT_PUBLIC_GAMING_ENGINE_WS_URL is correctly set"
    else
        print_error "NEXT_PUBLIC_GAMING_ENGINE_WS_URL is not set correctly"
    fi
    
    if grep -q "NEXT_PUBLIC_GAMING_ENGINE_URL=http://localhost:8000" .env; then
        print_success "NEXT_PUBLIC_GAMING_ENGINE_URL is correctly set"
    else
        print_error "NEXT_PUBLIC_GAMING_ENGINE_URL is not set correctly"
    fi
else
    print_error ".env file not found"
fi

# Test 6: Check API Gateway logs for WebSocket routing
print_status "Checking API Gateway WebSocket configuration..."

api_gateway_logs=$(docker-compose logs api-gateway --tail=20 2>/dev/null | grep -i websocket | head -3 || echo "No WebSocket logs found")
if [[ "$api_gateway_logs" != "No WebSocket logs found" ]]; then
    print_success "API Gateway has WebSocket configuration"
else
    print_warning "No WebSocket logs found in API Gateway"
fi

# Test 7: Check Gaming Engine WebSocket routes
print_status "Checking Gaming Engine WebSocket routes..."

gaming_logs=$(docker-compose logs gaming-engine --tail=20 2>/dev/null | grep -i "websocket\|chess" | head -3 || echo "No chess WebSocket logs found")
if [[ "$gaming_logs" != "No chess WebSocket logs found" ]]; then
    print_success "Gaming Engine has chess WebSocket configuration"
else
    print_warning "No chess WebSocket logs found in Gaming Engine"
fi

echo ""
print_status "🎯 Chess WebSocket Test Summary:"
echo ""

if [ "$all_running" = true ]; then
    print_success "✅ All required services are running"
    
    if [[ "$chess_ws_test" = "400" || "$chess_ws_test" = "426" ]] && [[ "$gaming_ws_test" = "400" || "$gaming_ws_test" = "426" ]]; then
        print_success "✅ Chess WebSocket routing is configured"
        echo ""
        print_status "🚀 Chess WebSocket should now work!"
        echo ""
        echo "✅ WebSocket URLs configured:"
        echo "   - General WebSocket: ws://localhost:8000/ws"
        echo "   - Chess WebSocket: ws://localhost:8000/ws/chess/{game_id}"
        echo ""
        echo "✅ Routing configured:"
        echo "   - API Gateway routes /ws/chess/* to gaming-engine:8001"
        echo "   - API Gateway routes /ws/* to websocket-manager:8080"
        echo ""
        echo "🎮 Test chess gameplay:"
        echo "   1. Visit: http://localhost:3000/chess/chess-{session_id}"
        echo "   2. WebSocket should connect to: ws://localhost:8000/ws/chess/chess-{session_id}"
        echo "   3. Real-time chess moves should work"
        
    else
        print_warning "⚠️ WebSocket routing may need verification"
        echo ""
        echo "🔧 Debugging steps:"
        echo "1. Check API Gateway logs: docker-compose logs api-gateway"
        echo "2. Check Gaming Engine logs: docker-compose logs gaming-engine"
        echo "3. Verify WebSocket proxy configuration in API Gateway"
        echo "4. Test WebSocket connection in browser developer tools"
    fi
    
else
    print_error "❌ Some services are not running"
    echo ""
    echo "🔧 Fix required services first:"
    echo "1. Start missing services: docker-compose up -d"
    echo "2. Check service health: docker-compose ps"
    echo "3. Review service logs for errors"
fi

echo ""
