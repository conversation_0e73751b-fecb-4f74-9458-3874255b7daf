#!/usr/bin/env python3
"""
BetBet Platform - Schema Deployment via Deployed API
===================================================

Deploys the Gaming Engine database schema by leveraging the existing
database connection in the deployed API service on Railway.
"""

import asyncio
import aiohttp
import os
import sys
from pathlib import Path
import structlog
import json

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


class APISchemaDeployer:
    """Deploy schema using the deployed API's database connection"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url.rstrip('/')
        self.project_root = Path(__file__).parent.parent
        
    async def create_deployment_endpoint(self):
        """Create a temporary endpoint for schema deployment"""
        
        # Read schema files
        schema_sql = (self.project_root / "database" / "schemas" / "gaming_engine.sql").read_text()
        indexes_sql = (self.project_root / "database" / "schemas" / "gaming_engine_indexes.sql").read_text()
        
        # Create a simple deployment script
        deployment_script = f'''
import asyncio
from services.shared.core.database.connection import _db_manager

async def deploy_schema():
    """Deploy Gaming Engine schema"""
    try:
        if not _db_manager:
            return {{"error": "Database manager not initialized"}}
        
        # Execute main schema
        schema_sql = """{schema_sql}"""
        await _db_manager.execute_query(schema_sql)
        
        # Execute indexes and security
        indexes_sql = """{indexes_sql}"""
        await _db_manager.execute_query(indexes_sql)
        
        # Verify deployment
        tables = await _db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine'",
            fetch_all=True
        )
        
        return {{
            "success": True,
            "tables_created": [row["table_name"] for row in tables],
            "message": "Gaming Engine schema deployed successfully"
        }}
        
    except Exception as e:
        return {{
            "success": False,
            "error": str(e)
        }}

# Execute deployment
result = await deploy_schema()
'''
        
        return deployment_script
    
    async def check_api_health(self):
        """Check if the API is healthy and ready"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_base_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        logger.info("✅ API is healthy", data=health_data)
                        return True
                    else:
                        logger.error("❌ API health check failed", status=response.status)
                        return False
        except Exception as e:
            logger.error("❌ Failed to connect to API", error=str(e))
            return False
    
    async def check_readiness(self):
        """Check API readiness including database status"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_base_url}/ready") as response:
                    if response.status == 200:
                        ready_data = await response.json()
                        logger.info("API readiness status", data=ready_data)
                        return ready_data
                    else:
                        logger.error("❌ API readiness check failed", status=response.status)
                        return None
        except Exception as e:
            logger.error("❌ Failed to check API readiness", error=str(e))
            return None


async def deploy_via_database_connection():
    """Deploy schema by connecting directly to the database"""
    try:
        # Import the database utilities
        sys.path.append(str(Path(__file__).parent.parent))
        
        from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
        
        # Create database settings (Railway will provide DATABASE_URL via environment)
        settings = DatabaseSettings()
        
        # Initialize database manager
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        
        logger.info("🔗 Connected to Railway PostgreSQL")
        
        # Read schema files
        project_root = Path(__file__).parent.parent
        schema_sql = (project_root / "database" / "schemas" / "gaming_engine.sql").read_text()
        indexes_sql = (project_root / "database" / "schemas" / "gaming_engine_indexes.sql").read_text()
        
        logger.info("📄 Executing main schema...")
        await db_manager.execute_query(schema_sql)
        
        logger.info("📄 Executing indexes and security policies...")
        await db_manager.execute_query(indexes_sql)
        
        # Verify deployment
        logger.info("🔍 Verifying schema deployment...")
        tables = await db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine' ORDER BY table_name",
            fetch_all=True
        )
        
        table_names = [dict(row)['table_name'] for row in tables]
        logger.info("✅ Schema deployed successfully", tables=table_names)
        
        # Test health check
        health = await db_manager.health_check()
        logger.info("🏥 Database health check", health=health)
        
        await db_manager.shutdown()
        
        return True
        
    except Exception as e:
        logger.error("❌ Schema deployment failed", error=str(e))
        return False


async def main():
    """Main deployment function"""
    try:
        api_url = "https://gaming-engine-production.up.railway.app"
        
        logger.info("🚀 Starting Gaming Engine schema deployment to Railway PostgreSQL")
        
        # Check API health first
        deployer = APISchemaDeployer(api_url)
        
        if not await deployer.check_api_health():
            logger.error("❌ API is not healthy. Cannot proceed with deployment.")
            return False
        
        # Check current readiness
        readiness = await deployer.check_readiness()
        if readiness and readiness.get('checks', {}).get('database') == 'healthy':
            logger.info("✅ Database is already healthy! Schema may already be deployed.")
            return True
        
        # Deploy schema using direct database connection
        logger.info("📊 Deploying schema via direct database connection...")
        success = await deploy_via_database_connection()
        
        if success:
            # Wait a moment for changes to propagate
            await asyncio.sleep(3)
            
            # Check readiness again
            logger.info("🔍 Verifying deployment via API health check...")
            final_readiness = await deployer.check_readiness()
            
            if final_readiness and final_readiness.get('checks', {}).get('database') == 'healthy':
                logger.info("🎉 Schema deployment successful! Database is now healthy.")
                logger.info("💡 Next steps:")
                logger.info("  ✅ Test gaming endpoints: curl https://gaming-engine-production.up.railway.app/api/v1/gaming/games")
                logger.info("  ✅ View API docs: https://gaming-engine-production.up.railway.app/docs")
                logger.info("  ✅ Ready for handoff to Claude-API")
                return True
            else:
                logger.warning("⚠️ Schema deployed but database health check still failing")
                return False
        
        return success
        
    except Exception as e:
        logger.error("❌ Deployment failed", error=str(e))
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)