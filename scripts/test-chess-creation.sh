#!/bin/bash

# Comprehensive test script for chess game creation and WebSocket issues
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo ""
print_status "🎮 Testing Chess Game Creation & WebSocket Issues"
echo ""

# Test 1: Check all required services are running
print_status "Checking required services..."

required_services=("betbet-api-gateway" "betbet-gaming" "betbet-websocket")
all_running=true

for service in "${required_services[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$service"; then
        print_success "$service is running"
    else
        print_error "$service is NOT running"
        all_running=false
    fi
done

if [ "$all_running" = false ]; then
    print_error "Not all required services are running!"
    exit 1
fi

# Test 2: Check API Gateway health
print_status "Testing API Gateway health..."
if curl -f -s http://localhost:8000/health > /dev/null; then
    print_success "API Gateway is healthy"
else
    print_error "API Gateway health check failed"
    exit 1
fi

# Test 3: Check Gaming Engine health
print_status "Testing Gaming Engine health..."
if curl -f -s http://localhost:8001/health > /dev/null; then
    print_success "Gaming Engine is healthy"
else
    print_error "Gaming Engine health check failed"
    exit 1
fi

# Test 4: Check Gaming Engine through API Gateway
print_status "Testing Gaming Engine through API Gateway..."
if curl -f -s http://localhost:8000/api/gaming/health > /dev/null; then
    print_success "Gaming Engine accessible through API Gateway"
else
    print_error "Gaming Engine NOT accessible through API Gateway"
fi

# Test 5: Check if chess game exists in database
print_status "Checking if chess game exists in database..."
chess_game_response=$(curl -s http://localhost:8001/api/v1/gaming/games | jq '.games[] | select(.id == "10000000-0000-0000-0000-000000000001")' 2>/dev/null || echo "null")

if [ "$chess_game_response" != "null" ] && [ "$chess_game_response" != "" ]; then
    print_success "Chess game found in database"
else
    print_warning "Chess game not found in database - this might cause session creation to fail"
fi

# Test 6: Test session creation with minimal data
print_status "Testing session creation with minimal data..."

session_response=$(timeout 10 curl -X POST http://localhost:8000/api/gaming/sessions \
  -H "Content-Type: application/json" \
  -d '{
    "game_id": "10000000-0000-0000-0000-000000000001",
    "session_name": "Test Chess Session",
    "entry_fee": 10,
    "min_participants_to_start": 2,
    "max_participants": 2
  }' 2>/dev/null || echo "TIMEOUT_OR_ERROR")

if [ "$session_response" = "TIMEOUT_OR_ERROR" ]; then
    print_error "Session creation timed out or failed"
    
    # Check API Gateway logs for clues
    print_status "Checking API Gateway logs..."
    docker-compose logs api-gateway --tail=5 | grep -E "(gaming|session|error)" || echo "No relevant logs found"
    
    # Check Gaming Engine logs for clues
    print_status "Checking Gaming Engine logs..."
    docker-compose logs gaming-engine --tail=5 | grep -E "(session|error|POST)" || echo "No relevant logs found"
    
else
    if echo "$session_response" | jq -e '.id' > /dev/null 2>&1; then
        session_id=$(echo "$session_response" | jq -r '.id')
        print_success "Session created successfully! ID: $session_id"
    else
        print_error "Session creation returned unexpected response: $session_response"
    fi
fi

# Test 7: Check WebSocket connection
print_status "Testing WebSocket connection..."

# Test WebSocket Manager directly
if curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
    print_success "WebSocket Manager is healthy"
else
    print_warning "WebSocket Manager health check failed"
fi

# Test WebSocket through API Gateway (this will fail for HTTP, but we can check if the route exists)
ws_test_response=$(curl -s http://localhost:8000/ws 2>&1 || echo "connection_failed")
if [[ "$ws_test_response" == *"connection_failed"* ]]; then
    print_warning "WebSocket endpoint not accessible via HTTP (expected - WebSocket needs WS protocol)"
else
    print_success "WebSocket route exists in API Gateway"
fi

# Test 8: Check container networking
print_status "Testing container networking..."

# Check if API Gateway can reach Gaming Engine
api_to_gaming=$(docker exec betbet-api-gateway wget -q -O- http://gaming-engine:8001/health 2>/dev/null || echo "NETWORK_ERROR")
if [ "$api_to_gaming" != "NETWORK_ERROR" ]; then
    print_success "API Gateway can reach Gaming Engine via Docker network"
else
    print_error "API Gateway CANNOT reach Gaming Engine via Docker network"
fi

# Check if API Gateway can reach WebSocket Manager
api_to_ws=$(docker exec betbet-api-gateway wget -q -O- http://websocket-manager:8080/health 2>/dev/null || echo "NETWORK_ERROR")
if [ "$api_to_ws" != "NETWORK_ERROR" ]; then
    print_success "API Gateway can reach WebSocket Manager via Docker network"
else
    print_error "API Gateway CANNOT reach WebSocket Manager via Docker network"
fi

echo ""
print_status "🎯 Summary of Issues Found:"
echo ""

# Summarize findings
if [ "$all_running" = true ]; then
    print_success "✅ All required services are running"
else
    print_error "❌ Some required services are missing"
fi

if [ "$session_response" != "TIMEOUT_OR_ERROR" ]; then
    print_success "✅ Session creation is working"
else
    print_error "❌ Session creation is failing"
    echo ""
    print_status "🔧 Possible solutions for session creation:"
    echo "1. Check if the chess game exists in the database"
    echo "2. Verify API Gateway can reach Gaming Engine"
    echo "3. Check for authentication issues"
    echo "4. Review Gaming Engine logs for errors"
fi

if [[ "$ws_test_response" != *"connection_failed"* ]]; then
    print_success "✅ WebSocket routing appears to be configured"
else
    print_warning "⚠️ WebSocket routing needs verification"
    echo ""
    print_status "🔧 WebSocket connection issues:"
    echo "1. Frontend should connect to: ws://localhost:8000/ws"
    echo "2. API Gateway should proxy to: websocket-manager:8080"
    echo "3. Check WebSocket Manager is running and healthy"
fi

echo ""
print_status "🚀 Chess Game Creation Status:"
echo ""

if [ "$all_running" = true ] && [ "$session_response" != "TIMEOUT_OR_ERROR" ]; then
    print_success "✅ CHESS GAME CREATION SHOULD NOW WORK!"
    echo ""
    echo "✅ All components are working:"
    echo "   - API Gateway: Running and routing correctly"
    echo "   - Gaming Engine: Healthy with authentication"
    echo "   - WebSocket Manager: Accessible for real-time features"
    echo "   - Chess Game: Exists in database"
    echo "   - Session Creation: Working with proper authentication"
    echo ""
    echo "🎮 Test chess game creation:"
    echo "   1. Visit: http://localhost:3000/games/chess"
    echo "   2. Click 'Create Game Session'"
    echo "   3. Fill in session details"
    echo "   4. Session should be created with slug like:"
    echo "      chess-blitz-{username}-vs-waiting-2025-07-27-15-43"
    echo ""
    echo "🔗 WebSocket connection should work:"
    echo "   - Frontend connects to: ws://localhost:8000/ws"
    echo "   - API Gateway proxies to: websocket-manager:8080"
    echo "   - Real-time leaderboard updates should work"

elif [ "$session_response" = "TIMEOUT_OR_ERROR" ]; then
    print_warning "⚠️ Session creation still has issues"
    echo ""
    echo "🔧 Debugging steps:"
    echo "1. Check gaming service logs: docker-compose logs gaming-engine"
    echo "2. Verify Clerk authentication is working"
    echo "3. Test with a valid Clerk JWT token"
    echo "4. Check database connection in gaming service"

else
    print_error "❌ Some services still need attention"
    echo ""
    echo "🔧 Fix required services first:"
    echo "1. Ensure all Docker containers are running"
    echo "2. Check service health endpoints"
    echo "3. Verify container networking"
fi

echo ""
