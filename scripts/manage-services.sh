#!/bin/bash

# BetBet Platform Service Management Script
# ========================================
#
# This script provides Docker-based service management for the BetBet platform.
# Use this script to restart, stop, start services and view logs from the command line.

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service to container mapping function
get_container() {
    case "$1" in
        "api-gateway") echo "betbet-api-gateway" ;;
        "gaming") echo "betbet-gaming" ;;
        "custom-betting") echo "betbet-custom-betting" ;;
        "trading") echo "betbet-trading" ;;
        "experts") echo "betbet-experts" ;;
        "sports") echo "betbet-sports" ;;
        "leaderboards") echo "betbet-leaderboards" ;;
        "wallet") echo "betbet-wallet" ;;
        "admin") echo "betbet-admin" ;;
        "web") echo "betbet-web" ;;
        *) echo "" ;;
    esac
}

# Available services
AVAILABLE_SERVICES="api-gateway gaming custom-betting trading experts sports leaderboards wallet admin web"

# Function to print usage
usage() {
    echo -e "${BLUE}BetBet Platform Service Management${NC}"
    echo ""
    echo "Usage: $0 <action> [service]"
    echo ""
    echo "Actions:"
    echo "  status          - Show status of all services"
    echo "  restart <svc>   - Restart a specific service"
    echo "  stop <svc>      - Stop a specific service"
    echo "  start <svc>     - Start a specific service"
    echo "  logs <svc>      - View logs for a specific service"
    echo "  restart-all     - Restart all services"
    echo ""
    echo "Services:"
    echo "  $AVAILABLE_SERVICES"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 restart gaming"
    echo "  $0 logs api-gateway"
    echo "  $0 restart-all"
}

# Function to check if service exists
check_service() {
    local service=$1
    local container=$(get_container "$service")
    if [[ -z "$container" ]]; then
        echo -e "${RED}Error: Unknown service '$service'${NC}"
        echo "Available services: $AVAILABLE_SERVICES"
        exit 1
    fi
}

# Function to show service status
show_status() {
    echo -e "${BLUE}BetBet Platform Service Status${NC}"
    echo "================================"

    for service in $AVAILABLE_SERVICES; do
        container=$(get_container "$service")
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{print $2, $3, $4}')
            echo -e "${GREEN}✓${NC} $service ($container): $status"
        else
            echo -e "${RED}✗${NC} $service ($container): Stopped"
        fi
    done
}

# Function to restart service
restart_service() {
    local service=$1
    local container=$(get_container "$service")

    echo -e "${YELLOW}Restarting $service ($container)...${NC}"
    docker restart "$container"
    echo -e "${GREEN}✓ $service restarted successfully${NC}"
}

# Function to stop service
stop_service() {
    local service=$1
    local container=$(get_container "$service")

    echo -e "${YELLOW}Stopping $service ($container)...${NC}"
    docker stop "$container"
    echo -e "${GREEN}✓ $service stopped successfully${NC}"
}

# Function to start service
start_service() {
    local service=$1
    local container=$(get_container "$service")

    echo -e "${YELLOW}Starting $service ($container)...${NC}"
    docker start "$container"
    echo -e "${GREEN}✓ $service started successfully${NC}"
}

# Function to view logs
view_logs() {
    local service=$1
    local container=$(get_container "$service")

    echo -e "${BLUE}Viewing logs for $service ($container)${NC}"
    echo "Press Ctrl+C to exit"
    echo "================================"
    docker logs -f --tail 50 "$container"
}

# Function to restart all services
restart_all() {
    echo -e "${YELLOW}Restarting all BetBet services...${NC}"

    # Restart in dependency order
    local ordered_services="api-gateway gaming custom-betting trading experts sports leaderboards wallet admin web"

    for service in $ordered_services; do
        container=$(get_container "$service")
        if [[ -n "$container" ]]; then
            restart_service "$service"
            sleep 2  # Brief pause between restarts
        fi
    done

    echo -e "${GREEN}✓ All services restarted${NC}"
}

# Main script logic
case "${1:-}" in
    "status")
        show_status
        ;;
    "restart")
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Service name required${NC}"
            usage
            exit 1
        fi
        check_service "$2"
        restart_service "$2"
        ;;
    "stop")
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Service name required${NC}"
            usage
            exit 1
        fi
        check_service "$2"
        stop_service "$2"
        ;;
    "start")
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Service name required${NC}"
            usage
            exit 1
        fi
        check_service "$2"
        start_service "$2"
        ;;
    "logs")
        if [[ -z "${2:-}" ]]; then
            echo -e "${RED}Error: Service name required${NC}"
            usage
            exit 1
        fi
        check_service "$2"
        view_logs "$2"
        ;;
    "restart-all")
        restart_all
        ;;
    *)
        usage
        exit 1
        ;;
esac
