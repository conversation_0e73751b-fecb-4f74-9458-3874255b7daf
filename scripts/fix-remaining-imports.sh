#!/bin/bash

# BetBet Platform - Fix Remaining Import Issues
# ============================================
# 
# This script automatically fixes remaining old API client imports
# and replaces them with the unified API client.

set -e

echo "🔧 Fixing remaining API client imports..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}📁 Working directory: $PROJECT_ROOT${NC}"

# Function to replace imports in a file
fix_imports_in_file() {
    local file="$1"
    echo -e "${YELLOW}🔧 Fixing imports in: $file${NC}"
    
    # Replace old API client imports with unified client
    sed -i '' "s|import { expertAnalystApi } from '@/lib/expert-analyst-api';|import api from '@/lib/api-client-unified';|g" "$file" 2>/dev/null || true
    sed -i '' "s|import { walletApi } from '@/lib/wallet-api';|import api from '@/lib/api-client-unified';|g" "$file" 2>/dev/null || true
    sed -i '' "s|import { multiServiceAPI } from '@/lib/multi-service-api';|import api from '@/lib/api-client-unified';|g" "$file" 2>/dev/null || true
    sed -i '' "s|import { realLeaderboardAPI } from '@/lib/leaderboard-api';|import api from '@/lib/api-client-unified';|g" "$file" 2>/dev/null || true
    
    # Replace type imports (keep them for now, but comment them out)
    sed -i '' "s|import type { Pick, Expert } from '@/lib/expert-analyst-api';|// import type { Pick, Expert } from '@/lib/expert-analyst-api'; // TODO: Update types|g" "$file" 2>/dev/null || true
    sed -i '' "s|import type { Expert, Pick, ExpertAnalytics } from '@/lib/expert-analyst-api';|// import type { Expert, Pick, ExpertAnalytics } from '@/lib/expert-analyst-api'; // TODO: Update types|g" "$file" 2>/dev/null || true
    sed -i '' "s|import type { Expert, ExpertPerformance, SubscriptionTier, Review } from '@/lib/expert-analyst-api';|// import type { Expert, ExpertPerformance, SubscriptionTier, Review } from '@/lib/expert-analyst-api'; // TODO: Update types|g" "$file" 2>/dev/null || true
    
    # Replace API calls (basic patterns)
    sed -i '' "s|expertAnalystApi\.|api.experts.|g" "$file" 2>/dev/null || true
    sed -i '' "s|walletApi\.|api.wallet.|g" "$file" 2>/dev/null || true
    sed -i '' "s|multiServiceAPI\.|api.|g" "$file" 2>/dev/null || true
    sed -i '' "s|realLeaderboardAPI\.|api.leaderboards.|g" "$file" 2>/dev/null || true
    
    echo -e "${GREEN}✅ Fixed imports in: $file${NC}"
}

# Find all TypeScript/TSX files with old imports
echo -e "\n${YELLOW}Step 1: Finding files with old API client imports...${NC}"

FILES_TO_FIX=(
    "frontend/web/src/app/picks/page.tsx"
    "frontend/web/src/app/leaderboard/page.tsx"
    "frontend/web/src/app/expert-portal/page.tsx"
    "frontend/web/src/app/experts/[id]/page.tsx"
)

# Fix each file
for file in "${FILES_TO_FIX[@]}"; do
    if [ -f "$file" ]; then
        fix_imports_in_file "$file"
    else
        echo -e "${RED}❌ File not found: $file${NC}"
    fi
done

# Step 2: Create temporary stub files for missing types
echo -e "\n${YELLOW}Step 2: Creating temporary type stubs...${NC}"

# Create a temporary types file to prevent TypeScript errors
cat > "frontend/web/src/lib/temp-types.ts" << 'EOF'
/**
 * Temporary type definitions to prevent TypeScript errors
 * TODO: Replace with proper types from unified API client
 */

export interface Expert {
  id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  specializations: string[];
  verification_status: 'pending' | 'verified' | 'rejected';
  overall_rating: number;
  total_subscribers: number;
  total_picks: number;
  win_rate: number;
  roi: number;
  streak_type: 'win' | 'loss';
  current_streak: number;
  created_at: string;
  updated_at: string;
}

export interface Pick {
  id: string;
  expert_id: string;
  title: string;
  description: string;
  sport: string;
  league: string;
  match_date: string;
  pick_type: string;
  confidence: number;
  odds: number;
  stake_recommendation: number;
  status: 'pending' | 'won' | 'lost' | 'void';
  created_at: string;
  updated_at: string;
}

export interface ExpertAnalytics {
  total_picks: number;
  won_picks: number;
  lost_picks: number;
  win_rate: number;
  roi: number;
  total_profit: number;
  average_odds: number;
  best_sport: string;
  current_streak: number;
  monthly_performance: any[];
}

export interface ExpertPerformance {
  win_rate: number;
  roi: number;
  total_picks: number;
  profit_loss: number;
  average_odds: number;
  best_category: string;
  recent_form: string;
  monthly_stats: any[];
}

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number;
  duration: number;
  features: string[];
}

export interface Review {
  id: string;
  user_id: string;
  expert_id: string;
  rating: number;
  comment: string;
  created_at: string;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  expert_id: string;
  tier_id: string;
  status: string;
  created_at: string;
  expires_at: string;
}
EOF

echo -e "${GREEN}✅ Created temporary types file${NC}"

# Step 3: Update import statements to use temp types
echo -e "\n${YELLOW}Step 3: Updating type imports...${NC}"

for file in "${FILES_TO_FIX[@]}"; do
    if [ -f "$file" ]; then
        # Add temp types import if the file has commented type imports
        if grep -q "// TODO: Update types" "$file" 2>/dev/null; then
            # Add import at the top of the file (after existing imports)
            sed -i '' '/^import/a\
import type { Expert, Pick, ExpertAnalytics, ExpertPerformance, SubscriptionTier, Review, PaymentIntent, Subscription } from "@/lib/temp-types";
' "$file" 2>/dev/null || true
            echo -e "${GREEN}✅ Added temp types import to: $file${NC}"
        fi
    fi
done

# Step 4: Generate summary report
echo -e "\n${YELLOW}Step 4: Generating fix report...${NC}"

REPORT_FILE="import-fix-report.md"
cat > "$REPORT_FILE" << EOF
# Import Fix Report
Generated: $(date)

## Files Fixed
EOF

for file in "${FILES_TO_FIX[@]}"; do
    if [ -f "$file" ]; then
        echo "- ✅ $file" >> "$REPORT_FILE"
    else
        echo "- ❌ $file (not found)" >> "$REPORT_FILE"
    fi
done

cat >> "$REPORT_FILE" << EOF

## Changes Made

### Import Replacements
- \`expertAnalystApi\` → \`api.experts\`
- \`walletApi\` → \`api.wallet\`
- \`multiServiceAPI\` → \`api\`
- \`realLeaderboardAPI\` → \`api.leaderboards\`

### Type Imports
- Commented out old type imports
- Added temporary type definitions
- Created \`frontend/web/src/lib/temp-types.ts\`

## Next Steps

1. **Test the application** to ensure no import errors
2. **Update API calls** to match unified client methods
3. **Replace temporary types** with proper types from unified client
4. **Remove temp-types.ts** once proper types are in place

## Verification Commands

\`\`\`bash
# Check for remaining old imports
grep -r "expert-analyst-api\|wallet-api\|multi-service-api" frontend/web/src/

# Check for TypeScript errors
npm run type-check
\`\`\`
EOF

echo -e "${GREEN}📊 Report generated: $REPORT_FILE${NC}"

# Step 5: Summary
echo -e "\n${GREEN}🎉 Import fixes complete!${NC}"
echo -e "\n${BLUE}📋 Summary:${NC}"
echo -e "✅ Fixed imports in ${#FILES_TO_FIX[@]} files"
echo -e "✅ Created temporary type definitions"
echo -e "✅ Updated API call patterns"
echo -e "✅ Generated fix report"

echo -e "\n${YELLOW}📖 Next Steps:${NC}"
echo -e "1. Test the application: ${BLUE}docker restart betbet-web${NC}"
echo -e "2. Check for errors in browser console"
echo -e "3. Update API calls to match unified client methods"
echo -e "4. Replace temporary types with proper types"

echo -e "\n${GREEN}🚀 Your imports are now using the unified API client!${NC}"
