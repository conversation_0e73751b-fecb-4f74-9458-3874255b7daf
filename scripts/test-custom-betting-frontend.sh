#!/bin/bash

# Test script to debug custom betting frontend issues
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo ""
print_status "🔧 Testing Custom Betting Frontend Issues"
echo ""

# Test 1: Check if API Gateway is accessible
print_status "Testing API Gateway custom betting endpoint..."
if curl -f -s http://localhost:8000/api/custom-betting/markets > /dev/null; then
    market_count=$(curl -s http://localhost:8000/api/custom-betting/markets | jq '. | length' 2>/dev/null || echo "0")
    print_success "API Gateway working (${market_count} markets available)"
else
    print_error "API Gateway custom betting endpoint failed"
    exit 1
fi

# Test 2: Check if frontend is running
print_status "Testing frontend accessibility..."
if curl -f -s http://localhost:3000 > /dev/null; then
    print_success "Frontend is accessible"
else
    print_error "Frontend is not accessible"
    exit 1
fi

# Test 3: Test frontend API proxy route
print_status "Testing frontend API proxy route..."
response=$(curl -s -w "%{http_code}" http://localhost:3000/api/custom-betting/markets -o /tmp/frontend_api_response.json)
if [ "$response" = "200" ]; then
    if [ -f /tmp/frontend_api_response.json ]; then
        content_type=$(file /tmp/frontend_api_response.json | grep -o "JSON" || echo "NOT_JSON")
        if [ "$content_type" = "JSON" ]; then
            market_count=$(jq '. | length' /tmp/frontend_api_response.json 2>/dev/null || echo "0")
            print_success "Frontend API proxy working (${market_count} markets)"
        else
            print_warning "Frontend API proxy returned non-JSON response"
            echo "Response content (first 200 chars):"
            head -c 200 /tmp/frontend_api_response.json
        fi
    else
        print_warning "Frontend API proxy response file not found"
    fi
elif [ "$response" = "404" ]; then
    print_error "Frontend API proxy route not found (404)"
    print_status "Checking if route file exists..."
    if [ -f "frontend/web/src/app/api/custom-betting/markets/route.ts" ]; then
        print_success "Route file exists"
        print_status "Checking Next.js compilation..."
        docker-compose logs web-frontend --tail=10 | grep -E "(error|Error|ERROR|404|compiled)" || echo "No compilation errors found"
    else
        print_error "Route file missing"
    fi
else
    print_error "Frontend API proxy failed with status: $response"
fi

# Test 4: Test direct API call with CORS headers
print_status "Testing direct API call with CORS..."
cors_response=$(curl -s -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: GET" -X OPTIONS http://localhost:8000/api/custom-betting/markets -w "%{http_code}")
if [[ "$cors_response" == *"200"* ]] || [[ "$cors_response" == *"204"* ]]; then
    print_success "CORS preflight successful"
    
    # Try actual GET request
    api_response=$(curl -s -H "Origin: http://localhost:3000" http://localhost:8000/api/custom-betting/markets | jq '. | length' 2>/dev/null || echo "FAILED")
    if [ "$api_response" != "FAILED" ]; then
        print_success "Direct API call with CORS headers working (${api_response} markets)"
    else
        print_warning "Direct API call failed despite CORS preflight success"
    fi
else
    print_warning "CORS preflight failed or not supported"
fi

# Test 5: Check environment variables
print_status "Checking environment variables..."
if grep -q "NEXT_PUBLIC_API_BASE_URL" .env; then
    api_url=$(grep "NEXT_PUBLIC_API_BASE_URL" .env | cut -d'=' -f2)
    print_success "NEXT_PUBLIC_API_BASE_URL is set to: $api_url"
else
    print_warning "NEXT_PUBLIC_API_BASE_URL not found in .env"
fi

# Test 6: Check if custom betting page loads
print_status "Testing custom betting page..."
page_response=$(curl -s -w "%{http_code}" http://localhost:3000/custom-betting -o /tmp/custom_betting_page.html)
if [ "$page_response" = "200" ]; then
    if grep -q "Custom Betting Markets" /tmp/custom_betting_page.html; then
        print_success "Custom betting page loads correctly"
    else
        print_warning "Custom betting page loads but content may be incorrect"
    fi
else
    print_error "Custom betting page failed to load (status: $page_response)"
fi

echo ""
print_status "🎯 Diagnosis Summary:"
echo ""

# Provide diagnosis based on test results
if [ "$response" = "404" ]; then
    echo "❌ Issue: Frontend API proxy route not working"
    echo ""
    echo "🔧 Possible Solutions:"
    echo "1. Check if Next.js has compiled the new route:"
    echo "   docker-compose logs web-frontend --tail=20"
    echo ""
    echo "2. Restart the frontend to ensure route is loaded:"
    echo "   docker-compose restart web-frontend"
    echo ""
    echo "3. Alternative: Use direct API Gateway calls with proper CORS"
    echo "   Update custom-betting-api.ts to use http://localhost:8000"
    echo ""
elif [ "$market_count" -gt 0 ]; then
    echo "✅ API Gateway is working with ${market_count} markets"
    echo "✅ The issue might be in the frontend component logic"
    echo ""
    echo "🔧 Next Steps:"
    echo "1. Check browser console for JavaScript errors"
    echo "2. Verify MarketBrowser component is calling the API correctly"
    echo "3. Check if authentication is required"
    echo ""
else
    echo "⚠️ Mixed results - some components working, others not"
    echo ""
    echo "🔧 Recommended Actions:"
    echo "1. Check all service logs for errors"
    echo "2. Verify database connectivity"
    echo "3. Test with browser developer tools"
fi

echo ""
print_status "🚀 Quick Fix Commands:"
echo "  Restart frontend:     docker-compose restart web-frontend"
echo "  Check frontend logs:  docker-compose logs web-frontend --tail=20"
echo "  Test API directly:    curl http://localhost:8000/api/custom-betting/markets"
echo "  Test frontend proxy:  curl http://localhost:3000/api/custom-betting/markets"
echo ""

# Cleanup
rm -f /tmp/frontend_api_response.json /tmp/custom_betting_page.html
