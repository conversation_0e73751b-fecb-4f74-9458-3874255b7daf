#!/usr/bin/env python3
"""
Simple Chess Data Seeding Script
================================

Inserts test users and chess games into the existing database tables.
"""

import asyncio
import asyncpg
import os

# Database connection settings
DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://postgres:123Bubblegums@localhost:5432/betbet_db')

async def seed_data():
    """Seed chess users and games"""
    print("🏁 Seeding Chess Data")
    print("=" * 30)
    
    try:
        # Connect to database
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected to database")
        
        # Insert test users
        print("\n👥 Inserting test users...")
        users_data = [
            ('550e8400-e29b-41d4-a716-446655440001', 'ChessMaster2024', '<EMAIL>', 1650, 150, 90, 45, 15),
            ('550e8400-e29b-41d4-a716-446655440002', 'QueenGambit2024', '<EMAIL>', 1820, 200, 130, 50, 20),
            ('550e8400-e29b-41d4-a716-446655440003', 'KnightRider2024', '<EMAIL>', 1790, 180, 110, 55, 15),
            ('550e8400-e29b-41d4-a716-446655440004', 'PawnStorm2024', '<EMAIL>', 1450, 100, 45, 40, 15)
        ]
        
        # Clear existing data first
        await conn.execute("DELETE FROM chess_engine.chess_games")
        await conn.execute("DELETE FROM chess_engine.users")

        for user_data in users_data:
            try:
                await conn.execute("""
                    INSERT INTO chess_engine.users (id, username, email, rating, games_played, wins, losses, draws)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """, *user_data)
                print(f"✅ Created user: {user_data[1]} (Rating: {user_data[3]})")
            except Exception as e:
                print(f"❌ Failed to create user {user_data[1]}: {e}")

        # Verify users were created
        users_count = await conn.fetchval("SELECT COUNT(*) FROM chess_engine.users")
        print(f"📊 Users in database: {users_count}")
        
        # Insert test chess games
        print("\n♟️ Inserting chess games...")
        games_data = [
            (
                '550e8400-e29b-41d4-a716-446655441001',  # id
                'standard',  # variant
                'blitz',  # time_control
                300,  # initial_time_seconds
                3,  # increment_seconds
                '550e8400-e29b-41d4-a716-446655440001',  # white_player_id (ChessMaster)
                None,  # black_player_id (waiting)
                'waiting',  # status
                True,  # is_rated
                False,  # is_private
                True,  # allow_spectators
                0,  # wager_amount
                'Quick blitz game - all welcome!',  # description
                300,  # white_time_remaining
                300,  # black_time_remaining
                0  # move_count
            ),
            (
                '550e8400-e29b-41d4-a716-446655441002',  # id
                'standard',  # variant
                'rapid',  # time_control
                900,  # initial_time_seconds
                10,  # increment_seconds
                '550e8400-e29b-41d4-a716-446655440002',  # white_player_id (QueenGambit)
                '550e8400-e29b-41d4-a716-446655440003',  # black_player_id (KnightRider)
                'active',  # status
                True,  # is_rated
                False,  # is_private
                True,  # allow_spectators
                50,  # wager_amount
                'Rapid game with wager',  # description
                850,  # white_time_remaining
                890,  # black_time_remaining
                8  # move_count
            ),
            (
                '550e8400-e29b-41d4-a716-446655441003',  # id
                'chess960',  # variant
                'classical',  # time_control
                1800,  # initial_time_seconds
                30,  # increment_seconds
                '550e8400-e29b-41d4-a716-446655440004',  # white_player_id (PawnStorm)
                None,  # black_player_id (waiting)
                'waiting',  # status
                False,  # is_rated
                False,  # is_private
                True,  # allow_spectators
                25,  # wager_amount
                'Chess960 variant - casual game',  # description
                1800,  # white_time_remaining
                1800,  # black_time_remaining
                0  # move_count
            )
        ]
        
        for game_data in games_data:
            try:
                # Verify the white player exists
                white_player_exists = await conn.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM chess_engine.users WHERE id = $1)",
                    game_data[5]
                )
                if not white_player_exists:
                    print(f"❌ White player {game_data[5]} does not exist!")
                    continue

                # Verify the black player exists (if not None)
                if game_data[6]:
                    black_player_exists = await conn.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM chess_engine.users WHERE id = $1)",
                        game_data[6]
                    )
                    if not black_player_exists:
                        print(f"❌ Black player {game_data[6]} does not exist!")
                        continue

                await conn.execute("""
                    INSERT INTO chess_engine.chess_games (
                        id, variant, time_control, initial_time_seconds, increment_seconds,
                        white_player_id, black_player_id, status, is_rated, is_private,
                        allow_spectators, wager_amount, description, white_time_remaining,
                        black_time_remaining, move_count
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                """, *game_data)
                
                # Get player names for display
                white_player = next(u[1] for u in users_data if u[0] == game_data[5])
                black_player = next((u[1] for u in users_data if u[0] == game_data[6]), None) if game_data[6] else None
                
                status_emoji = "⏳" if game_data[7] == 'waiting' else "♟️"
                opponent = f"vs {black_player}" if black_player else "waiting for opponent"
                
                print(f"{status_emoji} Created chess game: {white_player} {opponent}")
                print(f"   {game_data[1]} • {game_data[2]} • ${game_data[11]}")
                
            except Exception as e:
                print(f"❌ Failed to create chess game: {e}")
        
        print(f"\n🎉 Successfully seeded chess data!")
        print(f"   • {len(users_data)} users created")
        print(f"   • {len(games_data)} chess games created")
        
        # Show game URLs for testing
        print("\n🔗 Game URLs for testing:")
        for game_data in games_data:
            print(f"   http://localhost:3000/chess/{game_data[0]}")
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        return False
    finally:
        if 'conn' in locals():
            await conn.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(seed_data())
    if success:
        print("\n✅ Chess data seeding completed successfully!")
    else:
        print("\n❌ Chess data seeding failed!")
        exit(1)
