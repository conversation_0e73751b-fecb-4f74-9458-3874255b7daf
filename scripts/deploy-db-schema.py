#!/usr/bin/env python3
"""
BetBet Platform - Railway Database Schema Deployment
===================================================

Deploy database schema to Railway PostgreSQL instance.
"""

import asyncio
import asyncpg
import os
import sys
from pathlib import Path

async def execute_sql_file(conn, file_path: Path):
    """Execute SQL file against database connection"""
    print(f"Executing {file_path.name}...")
    
    try:
        with open(file_path, 'r') as f:
            sql_content = f.read()
        
        # Split into individual statements (basic approach)
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement.startswith('--') or not statement:
                continue
                
            try:
                await conn.execute(statement)
                print(f"  Statement {i+1} executed successfully")
            except Exception as e:
                print(f"  Error in statement {i+1}: {e}")
                print(f"  Statement: {statement[:100]}...")
                # Continue with other statements
        
        print(f"✅ {file_path.name} executed successfully")
        
    except Exception as e:
        print(f"❌ Error executing {file_path.name}: {e}")
        raise

async def deploy_schema():
    """Deploy database schema to Railway"""
    
    # Get database URL from environment
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL environment variable not set")
        print("For Railway deployment, this should be automatically set")
        return False
    
    print(f"🔗 Connecting to database...")
    print(f"   URL: {database_url[:50]}...")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        print("✅ Connected to database successfully")
        
        # Get current directory
        base_dir = Path(__file__).parent.parent
        schema_dir = base_dir / "database" / "schemas"
        
        print(f"📁 Schema directory: {schema_dir}")
        
        # Check if schema files exist
        main_schema = schema_dir / "gaming_engine.sql"
        indexes_schema = schema_dir / "gaming_engine_indexes.sql"
        functions_schema = schema_dir / "gaming_engine_functions.sql"
        
        if not main_schema.exists():
            print(f"❌ Main schema file not found: {main_schema}")
            return False
        
        print("🚀 Starting database schema deployment...")
        
        # Execute main schema
        await execute_sql_file(conn, main_schema)
        
        # Execute indexes if exists
        if indexes_schema.exists():
            await execute_sql_file(conn, indexes_schema)
        else:
            print(f"⚠️  Indexes file not found: {indexes_schema}")
        
        # Execute functions if exists
        if functions_schema.exists():
            await execute_sql_file(conn, functions_schema)
        else:
            print(f"⚠️  Functions file not found: {functions_schema}")
        
        # Verify deployment
        print("🔍 Verifying schema deployment...")
        
        # Check if gaming_engine schema exists
        result = await conn.fetchval(
            "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'gaming_engine'"
        )
        
        if result:
            print("✅ gaming_engine schema created successfully")
        else:
            print("❌ gaming_engine schema not found")
            return False
        
        # Check tables
        tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine'"
        )
        
        print(f"✅ Found {len(tables)} tables in gaming_engine schema:")
        for table in tables:
            print(f"   - {table['table_name']}")
        
        # Close connection
        await conn.close()
        print("✅ Database schema deployment completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database deployment failed: {e}")
        return False

async def main():
    """Main entry point"""
    print("🎮 BetBet Gaming Engine - Database Schema Deployment")
    print("=" * 55)
    
    success = await deploy_schema()
    
    if success:
        print("\n🎉 Database schema deployment successful!")
        print("The gaming engine database is ready for use.")
        sys.exit(0)
    else:
        print("\n💥 Database schema deployment failed!")
        print("Please check the errors above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())