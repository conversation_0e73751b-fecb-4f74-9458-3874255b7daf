#!/usr/bin/env python3
"""
Quick test to add data directly via Railway deployment endpoint
"""

import requests
import json
import uuid

def test_deploy_schema():
    """Test the deploy schema endpoint on Railway"""
    try:
        response = requests.post("https://gaming-engine-production.up.railway.app/admin/deploy-schema")
        print(f"Deploy schema response: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error deploying schema: {e}")
        return False

def test_games_api():
    """Test the games API endpoint"""
    try:
        response = requests.get("https://gaming-engine-production.up.railway.app/api/v1/gaming/games")
        print(f"Games API response: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing games API: {e}")
        return False

if __name__ == "__main__":
    print("🎮 Testing BetBet Gaming Engine API")
    print("=" * 50)
    
    # Test schema deployment
    print("\n1. Testing schema deployment...")
    schema_ok = test_deploy_schema()
    
    # Test games API
    print("\n2. Testing games API...")
    api_ok = test_games_api()
    
    print(f"\n📊 Results:")
    print(f"  Schema deployment: {'✅' if schema_ok else '❌'}")
    print(f"  Games API: {'✅' if api_ok else '❌'}")