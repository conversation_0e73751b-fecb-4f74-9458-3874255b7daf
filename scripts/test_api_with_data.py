#!/usr/bin/env python3
"""
Test script to add sample data and test the Gaming Engine API
"""

import asyncio
import asyncpg
import uuid
from decimal import Decimal
from datetime import datetime

# Database connection
DATABASE_URL = "postgresql://postgres:<EMAIL>:11613/railway"

async def create_sample_data():
    """Create sample games and test the API"""
    
    # Connect to Railway database
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Create a test user first
        user_id = str(uuid.uuid4())
        await conn.execute("""
            INSERT INTO public.users (id, username, email, password_hash, balance, created_by, updated_by)
            VALUES ($1, 'testuser', '<EMAIL>', 'hashed_password', 1000.00, $1, $1)
            ON CONFLICT (email) DO NOTHING
        """, user_id)
        
        # Create sample games
        games = [
            {
                'id': str(uuid.uuid4()),
                'name': 'Quick Trivia Challenge',
                'slug': 'quick-trivia-challenge',
                'category': 'trivia',
                'description': 'Fast-paced trivia game for 2-8 players',
                'min_players': 2,
                'max_players': 8,
                'estimated_duration_minutes': 5,
                'popularity_score': Decimal('85.5'),
                'created_by': user_id,
                'updated_by': user_id
            },
            {
                'id': str(uuid.uuid4()),
                'name': 'Reaction Time Master',
                'slug': 'reaction-time-master',
                'category': 'reaction_time',
                'description': 'Test your reflexes in this fast-paced reaction game',
                'min_players': 2,
                'max_players': 6,
                'estimated_duration_minutes': 3,
                'popularity_score': Decimal('92.3'),
                'created_by': user_id,
                'updated_by': user_id
            },
            {
                'id': str(uuid.uuid4()),
                'name': 'Strategy Showdown',
                'slug': 'strategy-showdown',
                'category': 'strategy',
                'description': 'Deep strategic gameplay for serious competitors',
                'min_players': 2,
                'max_players': 4,
                'estimated_duration_minutes': 15,
                'popularity_score': Decimal('78.9'),
                'is_featured': True,
                'created_by': user_id,
                'updated_by': user_id
            }
        ]
        
        for game in games:
            await conn.execute("""
                INSERT INTO gaming_engine.games (
                    id, name, slug, category, description, min_players, max_players,
                    estimated_duration_minutes, popularity_score, is_featured,
                    created_by, updated_by
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                ON CONFLICT (slug) DO NOTHING
            """, 
                game['id'], game['name'], game['slug'], game['category'], 
                game['description'], game['min_players'], game['max_players'],
                game['estimated_duration_minutes'], game['popularity_score'], 
                game.get('is_featured', False), game['created_by'], game['updated_by']
            )
        
        print("✅ Sample data created successfully!")
        
        # Test query
        games_result = await conn.fetch("""
            SELECT id, name, slug, category, is_active, popularity_score 
            FROM gaming_engine.games 
            WHERE deleted_at IS NULL
            ORDER BY popularity_score DESC
        """)
        
        print(f"\n📊 Found {len(games_result)} games in database:")
        for game in games_result:
            print(f"  - {game['name']} ({game['category']}) - Score: {game['popularity_score']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_sample_data())