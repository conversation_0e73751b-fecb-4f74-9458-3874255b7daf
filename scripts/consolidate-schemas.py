#!/usr/bin/env python3
"""
BetBet Platform - Schema Consolidation Script
============================================

This script consolidates duplicate schema files and ensures consistency
across all database schemas and models.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Set

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SchemaConsolidator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.duplicates_found: Dict[str, List[Path]] = {}
        self.schemas_to_keep: Dict[str, Path] = {}
        self.schemas_to_remove: List[Path] = []
        
    def find_schema_files(self) -> List[Path]:
        """Find all schema-related files in the project"""
        schema_patterns = [
            "**/*schema*.sql",
            "**/migrations/*.sql",
            "**/database/schemas/*.sql",
            "**/db-migrations/**/*.sql",
            "**/models.py",
            "**/models/*.py"
        ]
        
        schema_files = []
        for pattern in schema_patterns:
            schema_files.extend(self.project_root.glob(pattern))
        
        return schema_files
    
    def analyze_duplicates(self, schema_files: List[Path]) -> None:
        """Analyze schema files for duplicates and conflicts"""
        logger.info("🔍 Analyzing schema files for duplicates...")
        
        # Group files by service/schema name
        schema_groups: Dict[str, List[Path]] = {}
        
        for file_path in schema_files:
            # Extract schema identifier from path
            schema_name = self.extract_schema_name(file_path)
            if schema_name not in schema_groups:
                schema_groups[schema_name] = []
            schema_groups[schema_name].append(file_path)
        
        # Find duplicates
        for schema_name, files in schema_groups.items():
            if len(files) > 1:
                self.duplicates_found[schema_name] = files
                logger.warning(f"📋 Found {len(files)} files for {schema_name}:")
                for file_path in files:
                    logger.warning(f"   - {file_path}")
    
    def extract_schema_name(self, file_path: Path) -> str:
        """Extract schema name from file path"""
        file_name = file_path.name.lower()
        
        # Remove common prefixes/suffixes
        schema_name = file_name.replace('.sql', '').replace('.py', '')
        schema_name = schema_name.replace('_schema', '').replace('schema_', '')
        schema_name = schema_name.replace('models', '').replace('model', '')
        
        # Extract service name from path
        path_parts = file_path.parts
        for part in path_parts:
            if any(service in part.lower() for service in [
                'gaming', 'wallet', 'expert', 'sports', 'leaderboard', 
                'betting', 'trading', 'custom-betting'
            ]):
                return part.lower()
        
        return schema_name
    
    def determine_canonical_schemas(self) -> None:
        """Determine which schema files to keep as canonical"""
        logger.info("📝 Determining canonical schema files...")
        
        # Priority order for schema files (highest to lowest)
        priority_paths = [
            "database/schemas/",
            "services/db-migrations/schemas/",
            "database/migrations/",
            "services/*/database/models.py",
            "backend/shared/models/"
        ]
        
        for schema_name, files in self.duplicates_found.items():
            canonical_file = None
            highest_priority = -1
            
            for file_path in files:
                priority = self.get_file_priority(file_path, priority_paths)
                if priority > highest_priority:
                    highest_priority = priority
                    canonical_file = file_path
            
            if canonical_file:
                self.schemas_to_keep[schema_name] = canonical_file
                # Mark others for removal
                for file_path in files:
                    if file_path != canonical_file:
                        self.schemas_to_remove.append(file_path)
                
                logger.info(f"✅ Canonical for {schema_name}: {canonical_file}")
    
    def get_file_priority(self, file_path: Path, priority_paths: List[str]) -> int:
        """Get priority score for a file based on its path"""
        path_str = str(file_path)
        
        for i, priority_path in enumerate(priority_paths):
            if priority_path.replace('*', '') in path_str:
                return len(priority_paths) - i
        
        return 0
    
    def create_backup(self) -> None:
        """Create backup of files before removal"""
        backup_dir = self.project_root / "backup" / "schema-consolidation"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"💾 Creating backup in {backup_dir}")
        
        for file_path in self.schemas_to_remove:
            relative_path = file_path.relative_to(self.project_root)
            backup_path = backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(file_path, backup_path)
            logger.info(f"   Backed up: {relative_path}")
    
    def remove_duplicates(self, dry_run: bool = True) -> None:
        """Remove duplicate schema files"""
        if dry_run:
            logger.info("🧪 DRY RUN - Files that would be removed:")
            for file_path in self.schemas_to_remove:
                logger.info(f"   - {file_path}")
            return
        
        logger.info("🗑️  Removing duplicate schema files...")
        for file_path in self.schemas_to_remove:
            try:
                file_path.unlink()
                logger.info(f"   Removed: {file_path}")
            except Exception as e:
                logger.error(f"   Failed to remove {file_path}: {e}")
    
    def generate_consolidation_report(self) -> str:
        """Generate a report of the consolidation process"""
        report = []
        report.append("# BetBet Schema Consolidation Report")
        report.append("=" * 50)
        report.append("")
        
        report.append("## Duplicates Found:")
        for schema_name, files in self.duplicates_found.items():
            report.append(f"### {schema_name}")
            for file_path in files:
                status = "KEEP" if file_path == self.schemas_to_keep.get(schema_name) else "REMOVE"
                report.append(f"- [{status}] {file_path}")
            report.append("")
        
        report.append("## Canonical Schema Files:")
        for schema_name, file_path in self.schemas_to_keep.items():
            report.append(f"- {schema_name}: {file_path}")
        
        report.append("")
        report.append(f"## Summary:")
        report.append(f"- Total duplicates found: {len(self.duplicates_found)}")
        report.append(f"- Files to remove: {len(self.schemas_to_remove)}")
        report.append(f"- Canonical files: {len(self.schemas_to_keep)}")
        
        return "\n".join(report)
    
    def run_consolidation(self, dry_run: bool = True) -> None:
        """Run the complete consolidation process"""
        logger.info("🚀 Starting schema consolidation...")
        
        # Find all schema files
        schema_files = self.find_schema_files()
        logger.info(f"📁 Found {len(schema_files)} schema-related files")
        
        # Analyze for duplicates
        self.analyze_duplicates(schema_files)
        
        if not self.duplicates_found:
            logger.info("✅ No duplicates found!")
            return
        
        # Determine canonical schemas
        self.determine_canonical_schemas()
        
        # Create backup
        if not dry_run:
            self.create_backup()
        
        # Remove duplicates
        self.remove_duplicates(dry_run)
        
        # Generate report
        report = self.generate_consolidation_report()
        report_path = self.project_root / "schema-consolidation-report.md"
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"📊 Report saved to: {report_path}")
        logger.info("✅ Schema consolidation complete!")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Consolidate BetBet schema files")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--execute", action="store_true", help="Execute changes (default is dry run)")
    
    args = parser.parse_args()
    
    consolidator = SchemaConsolidator(args.project_root)
    consolidator.run_consolidation(dry_run=not args.execute)

if __name__ == "__main__":
    main()
