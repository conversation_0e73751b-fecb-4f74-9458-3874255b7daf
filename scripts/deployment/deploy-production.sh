#!/bin/bash
# BetBet Platform - Production Deployment Script
# ==============================================
# Enterprise-grade deployment with safety checks

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
ENVIRONMENT="production"
NAMESPACE="betbet-prod"
REGISTRY="ghcr.io/betbet"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Cleanup function
cleanup() {
    if [[ -f "${PROJECT_ROOT}/.env.production.tmp" ]]; then
        rm -f "${PROJECT_ROOT}/.env.production.tmp"
    fi
}

trap cleanup EXIT

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required tools
    command -v docker >/dev/null 2>&1 || error_exit "Docker is required but not installed"
    command -v kubectl >/dev/null 2>&1 || error_exit "kubectl is required but not installed"
    command -v helm >/dev/null 2>&1 || error_exit "Helm is required but not installed"
    
    # Check Kubernetes connection
    kubectl cluster-info >/dev/null 2>&1 || error_exit "Cannot connect to Kubernetes cluster"
    
    # Check Docker registry access
    docker info >/dev/null 2>&1 || error_exit "Cannot connect to Docker daemon"
    
    # Verify environment file
    if [[ ! -f "${PROJECT_ROOT}/.env.production" ]]; then
        error_exit "Production environment file not found. Copy .env.production.template to .env.production and configure it."
    fi
    
    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading production environment variables..."
    
    # Source environment file
    set -a
    source "${PROJECT_ROOT}/.env.production"
    set +a
    
    # Validate required variables
    local required_vars=(
        "DATABASE_PASSWORD"
        "JWT_SECRET"
        "GRAFANA_ADMIN_PASSWORD"
        "DEPLOYMENT_VERSION"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Required environment variable $var is not set"
        fi
    done
    
    log_success "Environment variables loaded and validated"
}

# Pre-deployment safety checks
run_safety_checks() {
    log_info "Running pre-deployment safety checks..."
    
    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
        log_info "Creating namespace $NAMESPACE..."
        kubectl create namespace "$NAMESPACE"
    fi
    
    # Validate Kubernetes manifests
    log_info "Validating Kubernetes manifests..."
    kubectl apply --dry-run=client -f "${PROJECT_ROOT}/infrastructure/kubernetes/overlays/production/" >/dev/null 2>&1 || 
        error_exit "Invalid Kubernetes manifests"
    
    # Check resource quotas
    log_info "Checking resource availability..."
    # Add resource availability checks here
    
    # Verify database connectivity
    log_info "Verifying database connectivity..."
    # Add database connectivity check here
    
    log_success "Safety checks completed"
}

# Build and push container images
build_and_push_images() {
    log_info "Building and pushing container images..."
    
    local services=("frontend" "gaming-engine" "websocket-manager")
    local version="${DEPLOYMENT_VERSION:-latest}"
    
    for service in "${services[@]}"; do
        log_info "Building $service image..."
        
        local context_dir
        case $service in
            "frontend")
                context_dir="${PROJECT_ROOT}/frontend/web"
                ;;
            "gaming-engine")
                context_dir="${PROJECT_ROOT}/services/gaming-engine"
                ;;
            "websocket-manager")
                context_dir="${PROJECT_ROOT}/services/websocket-manager"
                ;;
        esac
        
        # Build image
        docker build \
            -f "${context_dir}/Dockerfile.production" \
            -t "${REGISTRY}/${service}:${version}" \
            -t "${REGISTRY}/${service}:latest" \
            "$context_dir" || error_exit "Failed to build $service image"
        
        # Push image
        log_info "Pushing $service image..."
        docker push "${REGISTRY}/${service}:${version}" || error_exit "Failed to push $service image"
        docker push "${REGISTRY}/${service}:latest" || error_exit "Failed to push $service image"
        
        log_success "$service image built and pushed successfully"
    done
}

# Deploy database and infrastructure
deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Deploy PostgreSQL
    log_info "Deploying PostgreSQL..."
    helm upgrade --install postgresql \
        "${PROJECT_ROOT}/infrastructure/helm/postgresql" \
        --namespace "$NAMESPACE" \
        --values "${PROJECT_ROOT}/infrastructure/helm/postgresql/values-production.yaml" \
        --wait --timeout=10m || error_exit "Failed to deploy PostgreSQL"
    
    # Deploy Redis
    log_info "Deploying Redis..."
    helm upgrade --install redis \
        "${PROJECT_ROOT}/infrastructure/helm/redis" \
        --namespace "$NAMESPACE" \
        --values "${PROJECT_ROOT}/infrastructure/helm/redis/values-production.yaml" \
        --wait --timeout=5m || error_exit "Failed to deploy Redis"
    
    # Deploy monitoring stack
    log_info "Deploying monitoring stack..."
    helm upgrade --install monitoring \
        "${PROJECT_ROOT}/infrastructure/helm/monitoring" \
        --namespace "$NAMESPACE" \
        --values "${PROJECT_ROOT}/infrastructure/helm/monitoring/values-production.yaml" \
        --wait --timeout=10m || error_exit "Failed to deploy monitoring"
    
    log_success "Infrastructure components deployed successfully"
}

# Deploy application services
deploy_applications() {
    log_info "Deploying application services..."
    
    # Update image tags in manifests
    local version="${DEPLOYMENT_VERSION:-latest}"
    find "${PROJECT_ROOT}/infrastructure/kubernetes/overlays/production/" -name "*.yaml" -exec \
        sed -i.bak "s|ghcr.io/betbet/\([^:]*\):.*|ghcr.io/betbet/\1:${version}|g" {} \;
    
    # Apply Kubernetes manifests
    kubectl apply -f "${PROJECT_ROOT}/infrastructure/kubernetes/overlays/production/" || 
        error_exit "Failed to apply Kubernetes manifests"
    
    # Wait for deployments to be ready
    local deployments=("frontend" "gaming-engine" "websocket-manager")
    for deployment in "${deployments[@]}"; do
        log_info "Waiting for $deployment deployment to be ready..."
        kubectl rollout status deployment/"$deployment" -n "$NAMESPACE" --timeout=600s || 
            error_exit "Deployment $deployment failed to become ready"
    done
    
    # Restore original manifests
    find "${PROJECT_ROOT}/infrastructure/kubernetes/overlays/production/" -name "*.yaml.bak" -exec \
        bash -c 'mv "$1" "${1%.bak}"' _ {} \;
    
    log_success "Application services deployed successfully"
}

# Run post-deployment tests
run_post_deployment_tests() {
    log_info "Running post-deployment tests..."
    
    # Health check tests
    log_info "Running health checks..."
    kubectl apply -f "${PROJECT_ROOT}/tests/e2e/k8s-health-check-job.yaml" -n "$NAMESPACE" || 
        error_exit "Failed to create health check job"
    
    kubectl wait --for=condition=complete job/health-check-tests -n "$NAMESPACE" --timeout=300s || 
        error_exit "Health check tests failed"
    
    # Smoke tests
    log_info "Running smoke tests..."
    kubectl apply -f "${PROJECT_ROOT}/tests/e2e/k8s-smoke-test-job.yaml" -n "$NAMESPACE" || 
        error_exit "Failed to create smoke test job"
    
    kubectl wait --for=condition=complete job/smoke-tests -n "$NAMESPACE" --timeout=600s || 
        error_exit "Smoke tests failed"
    
    # Performance validation
    log_info "Running performance validation..."
    # Add performance test execution here
    
    log_success "Post-deployment tests completed successfully"
}

# Configure monitoring and alerting
configure_monitoring() {
    log_info "Configuring monitoring and alerting..."
    
    # Apply Prometheus rules
    kubectl apply -f "${PROJECT_ROOT}/monitoring/prometheus/alerts/" -n "$NAMESPACE" || 
        error_exit "Failed to apply Prometheus alert rules"
    
    # Configure Grafana dashboards
    kubectl create configmap grafana-dashboards \
        --from-file="${PROJECT_ROOT}/monitoring/grafana/dashboards/" \
        -n "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f - || 
        error_exit "Failed to configure Grafana dashboards"
    
    # Restart Grafana to load new dashboards
    kubectl rollout restart deployment/grafana -n "$NAMESPACE" || 
        error_exit "Failed to restart Grafana"
    
    log_success "Monitoring and alerting configured successfully"
}

# Backup current state
backup_current_state() {
    log_info "Creating backup of current deployment state..."
    
    local backup_dir="${PROJECT_ROOT}/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Export current Kubernetes resources
    kubectl get all -n "$NAMESPACE" -o yaml > "${backup_dir}/kubernetes-resources.yaml"
    kubectl get configmaps,secrets -n "$NAMESPACE" -o yaml > "${backup_dir}/kubernetes-configs.yaml"
    
    # Database backup (if applicable)
    # Add database backup logic here
    
    log_success "Backup created at $backup_dir"
}

# Main deployment function
main() {
    log_info "Starting BetBet Platform production deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Namespace: $NAMESPACE"
    log_info "Registry: $REGISTRY"
    
    # Execute deployment steps
    check_prerequisites
    load_environment
    backup_current_state
    run_safety_checks
    build_and_push_images
    deploy_infrastructure
    deploy_applications
    configure_monitoring
    run_post_deployment_tests
    
    log_success "\n🎉 BetBet Platform production deployment completed successfully!"
    log_info "\nDeployment Summary:"
    log_info "- Environment: $ENVIRONMENT"
    log_info "- Version: ${DEPLOYMENT_VERSION:-latest}"
    log_info "- Namespace: $NAMESPACE"
    log_info "- Services: frontend, gaming-engine, websocket-manager"
    log_info "\nNext steps:"
    log_info "1. Monitor the deployment: kubectl get pods -n $NAMESPACE"
    log_info "2. Check service health: kubectl get svc -n $NAMESPACE"
    log_info "3. View application logs: kubectl logs -f deployment/frontend -n $NAMESPACE"
    log_info "4. Access monitoring: https://grafana.betbet.com"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi