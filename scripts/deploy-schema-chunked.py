#!/usr/bin/env python3
"""
BetBet Platform - Chunked Schema Deployment
==========================================

Deploys the Gaming Engine database schema in smaller chunks to handle
complex SQL statements properly.
"""

import asyncio
import os
import sys
from pathlib import Path
import structlog
import re

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


def split_sql_statements(sql_content: str):
    """Split SQL content into individual statements"""
    # Remove comments
    sql_content = re.sub(r'--.*?\n', '\n', sql_content)
    
    # Split by semicolons, but be careful with function definitions
    statements = []
    current_statement = ""
    in_function = False
    
    for line in sql_content.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        current_statement += line + '\n'
        
        # Check for function start
        if 'CREATE OR REPLACE FUNCTION' in line.upper() or 'CREATE FUNCTION' in line.upper():
            in_function = True
        
        # Check for function end
        if in_function and line.upper().startswith('$$ LANGUAGE'):
            in_function = False
            statements.append(current_statement.strip())
            current_statement = ""
        elif not in_function and line.endswith(';'):
            statements.append(current_statement.strip())
            current_statement = ""
    
    # Add any remaining statement
    if current_statement.strip():
        statements.append(current_statement.strip())
    
    return [stmt for stmt in statements if stmt and stmt != ';']


async def deploy_schema_chunked():
    """Deploy schema in smaller, manageable chunks"""
    try:
        from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
        
        # Create database settings
        settings = DatabaseSettings()
        
        # Initialize database manager
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        
        logger.info("🔗 Connected to Railway PostgreSQL")
        
        # Phase 1: Core schema setup
        logger.info("📄 Phase 1: Setting up extensions and schema...")
        
        core_setup = """
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        CREATE SCHEMA IF NOT EXISTS gaming_engine;
        """
        
        await db_manager.execute_query(core_setup)
        logger.info("✅ Extensions and schema created")
        
        # Phase 2: Create users table if not exists
        logger.info("📄 Phase 2: Creating users table...")
        
        users_table = """
        CREATE TABLE IF NOT EXISTS public.users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            avatar_url TEXT,
            balance DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT true,
            is_verified BOOLEAN DEFAULT false,
            roles TEXT[] DEFAULT ARRAY['user']::TEXT[],
            permissions TEXT[] DEFAULT ARRAY[]::TEXT[],
            failed_login_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP WITH TIME ZONE,
            mfa_secret VARCHAR(255),
            mfa_enabled BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_by UUID,
            updated_by UUID,
            version INTEGER DEFAULT 1,
            deleted_at TIMESTAMP WITH TIME ZONE,
            deleted_by UUID
        );
        """
        
        await db_manager.execute_query(users_table)
        logger.info("✅ Users table created")
        
        # Phase 3: System tables
        logger.info("📄 Phase 3: Creating system tables...")
        
        system_tables = [
            # Audit logs
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.audit_logs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                table_name VARCHAR(100) NOT NULL,
                record_id UUID NOT NULL,
                action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
                old_values JSONB,
                new_values JSONB,
                changed_fields TEXT[],
                user_id UUID REFERENCES public.users(id),
                ip_address INET,
                user_agent TEXT,
                session_id VARCHAR(255),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """,
            
            # Transactions
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.transactions (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                transaction_type VARCHAR(50) NOT NULL,
                user_id UUID NOT NULL REFERENCES public.users(id),
                amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
                currency VARCHAR(3) DEFAULT 'USD',
                description TEXT NOT NULL,
                reference_id UUID,
                debit_account VARCHAR(100) NOT NULL,
                credit_account VARCHAR(100) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed')),
                external_transaction_id VARCHAR(255),
                payment_method VARCHAR(50),
                metadata JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                deleted_at TIMESTAMP WITH TIME ZONE,
                deleted_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Events
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.events (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                event_type VARCHAR(50) NOT NULL,
                event_source VARCHAR(50) NOT NULL,
                event_target VARCHAR(50),
                user_id UUID REFERENCES public.users(id),
                session_id VARCHAR(255),
                event_data JSONB NOT NULL,
                processed BOOLEAN DEFAULT false,
                processed_at TIMESTAMP WITH TIME ZONE,
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Performance metrics
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.performance_metrics (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                metric_name VARCHAR(100) NOT NULL,
                metric_value DECIMAL(15,6) NOT NULL,
                metric_unit VARCHAR(20),
                metric_tags JSONB,
                user_id UUID REFERENCES public.users(id),
                session_id VARCHAR(255),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """
        ]
        
        for table_sql in system_tables:
            await db_manager.execute_query(table_sql)
        
        logger.info("✅ System tables created")
        
        # Phase 4: Gaming tables
        logger.info("📄 Phase 4: Creating gaming tables...")
        
        gaming_tables = [
            # Games table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.games (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name VARCHAR(200) NOT NULL,
                slug VARCHAR(100) UNIQUE NOT NULL,
                category VARCHAR(50) NOT NULL CHECK (category IN ('trivia', 'reaction_time', 'strategy', 'sports', 'puzzle', 'custom')),
                description TEXT,
                game_config JSONB NOT NULL DEFAULT '{}',
                rules_text TEXT,
                instructions TEXT,
                min_players INTEGER NOT NULL DEFAULT 2 CHECK (min_players > 0),
                max_players INTEGER NOT NULL DEFAULT 10 CHECK (max_players >= min_players),
                min_skill_level INTEGER DEFAULT 1 CHECK (min_skill_level BETWEEN 1 AND 10),
                estimated_duration_minutes INTEGER DEFAULT 10 CHECK (estimated_duration_minutes > 0),
                scoring_system VARCHAR(20) DEFAULT 'points' CHECK (scoring_system IN ('points', 'time', 'accuracy', 'custom')),
                has_spectator_betting BOOLEAN DEFAULT true,
                allows_practice_mode BOOLEAN DEFAULT true,
                is_active BOOLEAN DEFAULT true,
                is_featured BOOLEAN DEFAULT false,
                is_beta BOOLEAN DEFAULT false,
                plugin_name VARCHAR(100),
                plugin_version VARCHAR(20),
                plugin_config JSONB DEFAULT '{}',
                total_sessions_played INTEGER DEFAULT 0,
                average_session_duration DECIMAL(8,2),
                average_players_per_session DECIMAL(4,2),
                popularity_score DECIMAL(6,2) DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                deleted_at TIMESTAMP WITH TIME ZONE,
                deleted_by UUID REFERENCES public.users(id)
            );
            """,
            
            # Game sessions table
            """
            CREATE TABLE IF NOT EXISTS gaming_engine.game_sessions (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                game_id UUID NOT NULL REFERENCES gaming_engine.games(id),
                session_name VARCHAR(200),
                session_code VARCHAR(20) UNIQUE,
                session_type VARCHAR(20) NOT NULL DEFAULT 'casual' CHECK (session_type IN ('casual', 'tournament', 'challenge', 'practice')),
                game_mode VARCHAR(20) DEFAULT 'standard' CHECK (game_mode IN ('standard', 'blitz', 'endurance', 'custom')),
                state VARCHAR(20) NOT NULL DEFAULT 'waiting' CHECK (state IN ('waiting', 'starting', 'active', 'paused', 'completed', 'cancelled', 'aborted')),
                max_participants INTEGER NOT NULL CHECK (max_participants > 0),
                current_participants INTEGER NOT NULL DEFAULT 0 CHECK (current_participants >= 0),
                min_participants_to_start INTEGER DEFAULT 2 CHECK (min_participants_to_start > 0),
                entry_fee DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (entry_fee >= 0),
                prize_pool DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (prize_pool >= 0),
                platform_fee_percentage DECIMAL(5,4) DEFAULT 0.05 CHECK (platform_fee_percentage BETWEEN 0 AND 1),
                scheduled_start_at TIMESTAMP WITH TIME ZONE,
                actual_started_at TIMESTAMP WITH TIME ZONE,
                estimated_end_at TIMESTAMP WITH TIME ZONE,
                actual_ended_at TIMESTAMP WITH TIME ZONE,
                auto_start_when_full BOOLEAN DEFAULT true,
                session_config JSONB DEFAULT '{}',
                custom_rules JSONB DEFAULT '{}',
                allows_spectators BOOLEAN DEFAULT true,
                allows_spectator_betting BOOLEAN DEFAULT true,
                allows_late_joining BOOLEAN DEFAULT false,
                is_private BOOLEAN DEFAULT false,
                password_hash VARCHAR(255),
                invite_only BOOLEAN DEFAULT false,
                winner_id UUID REFERENCES public.users(id),
                final_results JSONB,
                session_statistics JSONB,
                host_id UUID REFERENCES public.users(id),
                tournament_id UUID,
                tournament_round INTEGER,
                tournament_match_number INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_by UUID REFERENCES public.users(id),
                updated_by UUID REFERENCES public.users(id),
                version INTEGER DEFAULT 1,
                deleted_at TIMESTAMP WITH TIME ZONE,
                deleted_by UUID REFERENCES public.users(id)
            );
            """
        ]
        
        for table_sql in gaming_tables:
            await db_manager.execute_query(table_sql)
        
        logger.info("✅ Gaming tables created")
        
        # Phase 5: Key indexes
        logger.info("📄 Phase 5: Creating essential indexes...")
        
        essential_indexes = [
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_games_category_active ON gaming_engine.games(category, is_active) WHERE deleted_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_sessions_state_game ON gaming_engine.game_sessions(state, game_id) WHERE deleted_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_user_type ON gaming_engine.transactions(user_id, transaction_type, created_at DESC) WHERE deleted_at IS NULL;"
        ]
        
        for index_sql in essential_indexes:
            try:
                await db_manager.execute_query(index_sql)
            except Exception as e:
                logger.warning("Index creation warning", index=index_sql[:50], error=str(e))
        
        logger.info("✅ Essential indexes created")
        
        # Verify deployment
        logger.info("🔍 Verifying schema deployment...")
        tables = await db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine' ORDER BY table_name",
            fetch_all=True
        )
        
        table_names = [dict(row)['table_name'] for row in tables]
        logger.info("✅ Schema deployed successfully", tables=table_names)
        
        # Test health check
        health = await db_manager.health_check()
        logger.info("🏥 Database health check", health=health)
        
        await db_manager.shutdown()
        
        return True
        
    except Exception as e:
        logger.error("❌ Schema deployment failed", error=str(e))
        return False


async def main():
    """Main deployment function"""
    try:
        logger.info("🚀 Starting chunked Gaming Engine schema deployment")
        
        success = await deploy_schema_chunked()
        
        if success:
            logger.info("🎉 Schema deployment successful!")
            
            # Test the API health
            import aiohttp
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get("https://gaming-engine-production.up.railway.app/ready") as response:
                        if response.status == 200:
                            ready_data = await response.json()
                            logger.info("🔍 API readiness check", data=ready_data)
                            
                            if ready_data.get('checks', {}).get('database') == 'healthy':
                                logger.info("🎉 SUCCESS! Database is now healthy!")
                                logger.info("💡 Next steps:")
                                logger.info("  ✅ Test endpoints: https://gaming-engine-production.up.railway.app/api/v1/gaming/games")
                                logger.info("  ✅ View docs: https://gaming-engine-production.up.railway.app/docs")
                                return True
                            else:
                                logger.warning("⚠️ Database health check still failing")
            except Exception as e:
                logger.warning("Could not verify API health", error=str(e))
        
        return success
        
    except Exception as e:
        logger.error("❌ Deployment failed", error=str(e))
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)