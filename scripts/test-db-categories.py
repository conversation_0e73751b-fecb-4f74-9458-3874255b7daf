#!/usr/bin/env python3
import asyncio
import sys
from pathlib import Path

# Add the services directory to path
services_dir = Path(__file__).parent.parent / 'services'
sys.path.insert(0, str(services_dir))

from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

async def test_categories():
    # Direct database connection - use postgres container hostname when running from host
    DATABASE_URL = "postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db"
    engine = create_async_engine(DATABASE_URL, echo=True)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        # First, let's check what's in the table
        result = await session.execute(text("SELECT COUNT(*) FROM custom_betting.market_categories"))
        count = result.scalar()
        print(f"\nTotal categories in database: {count}")
        
        if count == 0:
            print("\nInserting test category...")
            await session.execute(
                text("""
                    INSERT INTO custom_betting.market_categories (id, name, description, slug, is_active)
                    VALUES (gen_random_uuid(), 'Test Category', 'Test Description', 'test-category', true)
                """)
            )
            await session.commit()
            print("Category inserted!")
        
        # Now fetch all categories
        result = await session.execute(
            text("SELECT id, name, slug FROM custom_betting.market_categories")
        )
        categories = result.fetchall()
        
        print("\nCategories in database:")
        for cat in categories:
            print(f"  - {cat.name} (slug: {cat.slug})")
    
    await engine.dispose()

if __name__ == "__main__":
    asyncio.run(test_categories())