#!/bin/bash

# BetBet Platform Status Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_container_status() {
    local container_name=$1
    local status=$(docker-compose ps -q $container_name 2>/dev/null)
    
    if [ -z "$status" ]; then
        echo "❌ Not running"
    else
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$status" 2>/dev/null || echo "unknown")
        local state=$(docker inspect --format='{{.State.Status}}' "$status" 2>/dev/null || echo "unknown")
        
        case $state in
            "running")
                case $health in
                    "healthy")
                        echo "✅ Running (Healthy)"
                        ;;
                    "unhealthy")
                        echo "🔴 Running (Unhealthy)"
                        ;;
                    "starting")
                        echo "🟡 Running (Starting)"
                        ;;
                    *)
                        echo "✅ Running"
                        ;;
                esac
                ;;
            "exited")
                echo "❌ Exited"
                ;;
            "restarting")
                echo "🔄 Restarting"
                ;;
            *)
                echo "❓ $state"
                ;;
        esac
    fi
}

check_service_health() {
    local service_name=$1
    local port=$2
    local endpoint=${3:-/health}
    
    if curl -f -s --max-time 5 "http://localhost:$port$endpoint" > /dev/null 2>&1; then
        echo "✅ Healthy"
    else
        echo "❌ Unhealthy"
    fi
}

show_container_status() {
    echo ""
    print_status "📦 Container Status"
    echo "─────────────────────────────────────────"
    
    services=(
        "postgres"
        "redis"
        "api-gateway"
        "websocket-manager"
        "gaming-engine"
        "betting-service"
        "odds-exchange"
        "expert-analysis"
        "sports-analysis"
        "leaderboards-service"
        "web-frontend"
        "admin-frontend"
        "pgadmin"
    )
    
    for service in "${services[@]}"; do
        status=$(check_container_status $service)
        printf "%-20s %s\n" "$service" "$status"
    done
}

show_service_health() {
    echo ""
    print_status "🏥 Service Health Checks"
    echo "─────────────────────────────────────────"
    
    health_checks=(
        "API Gateway:8000"
        "WebSocket Manager:8080"
        "Gaming Engine:8001"
        "Betting Service:8002"
        "Odds Exchange:8003"
        "Expert Analysis:8004"
        "Sports Analysis:8005"
        "Leaderboards:8006"
        "Web Frontend:3000:/api/health"
        "Admin Frontend:3001:/api/health"
    )
    
    for check in "${health_checks[@]}"; do
        name=$(echo $check | cut -d: -f1)
        port=$(echo $check | cut -d: -f2)
        endpoint=$(echo $check | cut -d: -f3)
        
        if [ -z "$endpoint" ]; then
            endpoint="/health"
        fi
        
        health=$(check_service_health "$name" "$port" "$endpoint")
        printf "%-20s %s\n" "$name" "$health"
    done
}

show_port_status() {
    echo ""
    print_status "🔌 Port Status"
    echo "─────────────────────────────────────────"
    
    ports=(
        "3000:Web Frontend"
        "3001:Admin Frontend"
        "5432:PostgreSQL"
        "6379:Redis"
        "5050:PgAdmin"
        "8000:API Gateway"
        "8001:Gaming Engine"
        "8002:Betting Service"
        "8003:Odds Exchange"
        "8004:Expert Analysis"
        "8005:Sports Analysis"
        "8006:Leaderboards"
        "8080:WebSocket Manager"
    )
    
    for port_info in "${ports[@]}"; do
        port=$(echo $port_info | cut -d: -f1)
        service=$(echo $port_info | cut -d: -f2)
        
        if nc -z localhost $port 2>/dev/null; then
            status="✅ Open"
        else
            status="❌ Closed"
        fi
        
        printf "%-6s %-20s %s\n" "$port" "$service" "$status"
    done
}

show_resource_usage() {
    echo ""
    print_status "💻 Resource Usage"
    echo "─────────────────────────────────────────"
    
    # Show Docker stats for running containers
    if command -v docker-compose &> /dev/null; then
        docker-compose ps --format "table {{.Name}}\t{{.Image}}\t{{.Status}}" 2>/dev/null || echo "No containers running"
    fi
}

show_logs() {
    local service=$1
    local lines=${2:-50}
    
    if [ -z "$service" ]; then
        echo ""
        print_status "Available services for logs:"
        docker-compose ps --services
        return
    fi
    
    echo ""
    print_status "📄 Last $lines lines of $service logs:"
    echo "─────────────────────────────────────────"
    docker-compose logs --tail=$lines $service
}

show_quick_links() {
    echo ""
    print_status "🔗 Quick Access Links"
    echo "─────────────────────────────────────────"
    echo "Web App:        http://localhost:3000"
    echo "Admin Panel:    http://localhost:3001"
    echo "API Gateway:    http://localhost:8000"
    echo "PgAdmin:        http://localhost:5050"
    echo "WebSocket:      ws://localhost:8080/ws"
    echo ""
    echo "Health Check:   curl http://localhost:8000/health"
    echo "API Stats:      curl http://localhost:8080/stats"
}

show_usage() {
    echo "Usage: $0 [OPTION] [SERVICE]"
    echo ""
    echo "Options:"
    echo "  (no option)    Show full status report"
    echo "  --containers   Show container status only"
    echo "  --health       Show health checks only"
    echo "  --ports        Show port status only"
    echo "  --resources    Show resource usage"
    echo "  --logs [service] [lines]  Show logs for service"
    echo "  --links        Show quick access links"
    echo "  --help         Show this help message"
}

main() {
    echo ""
    print_status "📊 BetBet Platform Status 📊"
    
    case "${1:-}" in
        --containers)
            show_container_status
            ;;
        --health)
            show_service_health
            ;;
        --ports)
            show_port_status
            ;;
        --resources)
            show_resource_usage
            ;;
        --logs)
            show_logs $2 $3
            ;;
        --links)
            show_quick_links
            ;;
        --help)
            show_usage
            exit 0
            ;;
        "")
            show_container_status
            show_service_health
            show_port_status
            show_quick_links
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
}

main "$@"