#!/usr/bin/env python3
"""
Add sample games via Railway API
"""

import requests
import json

def add_sample_games():
    """Add sample games directly via API"""
    base_url = "https://gaming-engine-production.up.railway.app"
    
    sample_games = [
        {
            "name": "Quick Trivia Challenge",
            "slug": "quick-trivia-challenge",
            "category": "trivia",
            "description": "Fast-paced trivia game for 2-8 players",
            "min_players": 2,
            "max_players": 8,
            "estimated_duration_minutes": 5,
            "game_config": {"questions_per_round": 10, "time_per_question": 30},
            "is_featured": False
        },
        {
            "name": "Reaction Time Master",
            "slug": "reaction-time-master", 
            "category": "reaction_time",
            "description": "Test your reflexes in this fast-paced reaction game",
            "min_players": 2,
            "max_players": 6,
            "estimated_duration_minutes": 3,
            "game_config": {"rounds": 5, "difficulty": "medium"},
            "is_featured": True
        },
        {
            "name": "Strategy Showdown",
            "slug": "strategy-showdown",
            "category": "strategy", 
            "description": "Deep strategic gameplay for serious competitors",
            "min_players": 2,
            "max_players": 4,
            "estimated_duration_minutes": 15,
            "game_config": {"complexity": "high", "board_size": "large"},
            "is_featured": True
        }
    ]
    
    for game in sample_games:
        try:
            response = requests.post(
                f"{base_url}/api/v1/gaming/games",
                json=game,
                headers={"Content-Type": "application/json"}
            )
            print(f"Creating '{game['name']}': {response.status_code}")
            if response.status_code != 201:
                print(f"  Error: {response.text}")
            else:
                print(f"  ✅ Success!")
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    print("🎮 Adding sample games to BetBet Gaming Engine")
    print("=" * 50)
    add_sample_games()