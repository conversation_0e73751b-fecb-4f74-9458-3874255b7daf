// Test the exact API call the frontend is making
const axios = require('axios');

async function testFrontendAPI() {
    try {
        console.log('Testing frontend API call to get markets...');
        
        // This mimics what the frontend does
        const response = await axios.get('http://localhost:8000/api/custom-betting/markets', {
            params: {
                page: 1,
                limit: 12
            },
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ API call successful');
        console.log('Status:', response.status);
        console.log('Data type:', Array.isArray(response.data) ? 'Array' : typeof response.data);
        console.log('Markets count:', response.data.length);
        
        if (response.data.length > 0) {
            console.log('First market:', {
                id: response.data[0].id,
                title: response.data[0].title,
                status: response.data[0].status
            });
        }
        
        // Transform to frontend format (what the API client does)
        const frontendFormat = {
            bets: response.data,
            total: response.data.length,
            page: 1,
            limit: 12,
            has_next: response.data.length === 12,
            has_prev: false
        };
        
        console.log('Frontend format:', {
            betsCount: frontendFormat.bets.length,
            total: frontendFormat.total,
            has_next: frontendFormat.has_next
        });
        
    } catch (error) {
        console.error('❌ API call failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testFrontendAPI();