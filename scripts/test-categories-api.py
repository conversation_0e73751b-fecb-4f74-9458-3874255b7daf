#!/usr/bin/env python3
import asyncio
import httpx

async def test_api():
    async with httpx.AsyncClient() as client:
        # Test direct service
        print("Testing direct service...")
        resp = await client.get("http://localhost:8002/api/v1/custom-betting/categories")
        print(f"Direct service response: {resp.status_code}")
        print(f"Categories: {len(resp.json())} found")
        if resp.json():
            print("First category:", resp.json()[0])
        
        print("\nTesting via API Gateway...")
        resp = await client.get("http://localhost:8000/api/custom-betting/categories")
        print(f"API Gateway response: {resp.status_code}")
        print(f"Categories: {len(resp.json())} found")

asyncio.run(test_api())