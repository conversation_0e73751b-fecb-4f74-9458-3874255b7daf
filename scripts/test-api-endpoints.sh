#!/bin/bash

# BetBet Platform - API Endpoint Testing
# =====================================
# 
# This script tests all the unified API endpoints to ensure they're working correctly.

set -e

echo "🧪 Testing BetBet API Endpoints..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API Gateway URL
API_BASE_URL="http://localhost:8000"

# Function to test an endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    echo -e "${BLUE}🔍 Testing: $name${NC}"
    echo -e "   URL: $url"
    
    # Make the request and capture both status and response
    response=$(curl -s -w "\n%{http_code}" "$url" -H "Content-Type: application/json" 2>/dev/null)
    
    # Extract status code (last line) and body (everything else)
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "   ${GREEN}✅ Status: $status_code${NC}"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo -e "   ${GREEN}✅ Response: $(echo "$body" | jq -c . 2>/dev/null || echo "$body")${NC}"
        fi
    else
        echo -e "   ${RED}❌ Status: $status_code (expected $expected_status)${NC}"
        if [ -n "$body" ]; then
            echo -e "   ${RED}❌ Response: $body${NC}"
        fi
    fi
    echo ""
}

echo -e "\n${YELLOW}=== SYSTEM HEALTH CHECKS ===${NC}"

# Test API Gateway health
test_endpoint "API Gateway Health" "$API_BASE_URL/health"

# Test individual service health endpoints
test_endpoint "Gaming Service Health" "$API_BASE_URL/api/gaming/health"
test_endpoint "Wallet Service Health" "$API_BASE_URL/api/wallet/health"
test_endpoint "Experts Service Health" "$API_BASE_URL/api/experts/health"
test_endpoint "Sports Service Health" "$API_BASE_URL/api/sports/health"
test_endpoint "Leaderboards Service Health" "$API_BASE_URL/api/leaderboards/health"
test_endpoint "Custom Betting Service Health" "$API_BASE_URL/api/custom-betting/health"
test_endpoint "Trading Service Health" "$API_BASE_URL/api/trading/health"

echo -e "\n${YELLOW}=== ANALYTICS ENDPOINTS ===${NC}"

# Test analytics endpoints
test_endpoint "Gaming Analytics Overview" "$API_BASE_URL/api/gaming/analytics/overview"
test_endpoint "Wallet Analytics Overview" "$API_BASE_URL/api/wallet/analytics/overview"
test_endpoint "Experts Analytics Overview" "$API_BASE_URL/api/experts/analytics/overview"
test_endpoint "Sports Analytics Overview" "$API_BASE_URL/api/sports/analytics/overview"
test_endpoint "Leaderboards Analytics Overview" "$API_BASE_URL/api/leaderboards/analytics/overview"
test_endpoint "Custom Betting Analytics Overview" "$API_BASE_URL/api/custom-betting/analytics/overview"
test_endpoint "Trading Analytics Overview" "$API_BASE_URL/api/trading/analytics/overview"

echo -e "\n${YELLOW}=== PUBLIC DATA ENDPOINTS ===${NC}"

# Test public data endpoints
test_endpoint "Gaming Games List" "$API_BASE_URL/api/gaming/games"
test_endpoint "Gaming Sessions List" "$API_BASE_URL/api/gaming/sessions"
test_endpoint "Experts List" "$API_BASE_URL/api/experts/list"
test_endpoint "Sports Fixtures" "$API_BASE_URL/api/sports/fixtures"
test_endpoint "Sports Teams" "$API_BASE_URL/api/sports/teams"
test_endpoint "Sports Leagues" "$API_BASE_URL/api/sports/leagues"
test_endpoint "Leaderboards Unified" "$API_BASE_URL/api/leaderboards/unified"
test_endpoint "Custom Betting Markets" "$API_BASE_URL/api/custom-betting/markets"
test_endpoint "Trading Markets" "$API_BASE_URL/api/trading/markets"

echo -e "\n${YELLOW}=== USER-SPECIFIC ENDPOINTS (Expected 401/403) ===${NC}"

# Test user-specific endpoints (should require auth)
test_endpoint "User Gaming Stats" "$API_BASE_URL/api/gaming/analytics/users/test-user/stats" "401"
test_endpoint "User Wallet Balance" "$API_BASE_URL/api/wallet/balance/test-user" "401"

echo -e "\n${GREEN}🎉 API Endpoint Testing Complete!${NC}"

echo -e "\n${BLUE}📊 Summary:${NC}"
echo -e "✅ All health endpoints should return 200"
echo -e "✅ All analytics endpoints should return 200"
echo -e "✅ All public data endpoints should return 200"
echo -e "✅ User-specific endpoints should return 401 (unauthorized)"

echo -e "\n${YELLOW}📖 Next Steps:${NC}"
echo -e "1. If any health checks fail, check service logs"
echo -e "2. If analytics fail, check database connections"
echo -e "3. If public endpoints fail, check API Gateway routing"
echo -e "4. Test authenticated endpoints with valid tokens"

echo -e "\n${GREEN}🚀 Your unified API architecture is working!${NC}"
