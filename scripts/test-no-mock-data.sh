#!/bin/bash

# Test script to verify NO mock data fallbacks exist
# This ensures real errors are shown instead of fake data masking problems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo ""
print_status "🚫 Testing for Mock Data Elimination"
print_status "Ensuring real errors are shown instead of fake data"
echo ""

# Test 1: Check for mock data keywords in codebase
print_status "Scanning codebase for dangerous mock data patterns..."

mock_patterns=(
    "generateMock"
    "fallbackData"
    "sampleData"
    "Math.random.*[0-9]"
    "totalUsers.*1247"
    "totalVolume.*2847392"
    "activeUsers.*892"
    "currentUsage.*1247"
    "total_predictions.*1247"
    "userCount.*1247"
)

found_mock_data=false

for pattern in "${mock_patterns[@]}"; do
    # Exclude comments, removed code markers, and legitimate uses
    if grep -r "$pattern" frontend/admin/src/ 2>/dev/null | grep -v "test-no-mock-data.sh" | grep -v ".git" | grep -v "// REMOVED" | grep -v "# REMOVED" | grep -v "session.*Math.random" | grep -v "gradient.*Math.random" | grep -v "id.*Math.random"; then
        print_error "Found dangerous mock data pattern: $pattern"
        found_mock_data=true
    fi
done

if [ "$found_mock_data" = false ]; then
    print_success "No dangerous mock data patterns found in codebase"
else
    print_error "Mock data patterns still exist - these mask real errors!"
fi

# Test 2: Test User Errors API (should return empty, not fake data)
print_status "Testing user errors API for mock data..."
user_errors_response=$(curl -s http://localhost:3001/api/user-errors)
error_count=$(echo "$user_errors_response" | jq '.errors | length' 2>/dev/null || echo "0")

if [ "$error_count" -eq 0 ]; then
    print_success "User errors API returns empty array (no fake errors)"
else
    print_error "User errors API still returning fake data ($error_count errors)"
fi

# Test 3: Test Analytics API error handling
print_status "Testing analytics API error transparency..."

# Temporarily break the health API to test error handling
print_status "Testing what happens when health API fails..."

# Test with a non-existent endpoint to trigger error handling
health_error_response=$(curl -s http://localhost:3001/api/health/nonexistent 2>&1 || echo "error")

if [[ "$health_error_response" == *"error"* ]] || [[ "$health_error_response" == *"404"* ]]; then
    print_success "Health API properly returns errors (no fake success data)"
else
    print_warning "Health API response unclear: $health_error_response"
fi

# Test 4: Check platform analytics for real vs fake data
print_status "Testing platform analytics for real data only..."

analytics_response=$(curl -s http://localhost:3001/api/betting/overview)
total_markets=$(echo "$analytics_response" | jq -r '.market_stats.total_markets' 2>/dev/null || echo "0")
total_volume=$(echo "$analytics_response" | jq -r '.market_stats.total_volume' 2>/dev/null || echo "0")

# Check if we're getting real data (4 markets) vs fake data (156 markets)
if [ "$total_markets" -eq 4 ]; then
    print_success "Platform analytics showing real data (4 markets)"
elif [ "$total_markets" -eq 156 ]; then
    print_error "Platform analytics showing fake data (156 markets)"
elif [ "$total_markets" -eq 0 ]; then
    print_success "Platform analytics showing real empty state (0 markets)"
else
    print_warning "Platform analytics showing unexpected data ($total_markets markets)"
fi

# Test 5: Verify error states are shown properly
print_status "Testing error state transparency..."

# Check if system monitoring shows real error states
system_response=$(curl -s http://localhost:3001/system 2>/dev/null || echo "error")

if [[ "$system_response" == *"monitoring data unavailable"* ]] || [[ "$system_response" == *"not implemented"* ]]; then
    print_success "System monitoring shows real error state (not fake data)"
else
    print_warning "System monitoring response unclear"
fi

echo ""
print_status "🎯 Mock Data Elimination Summary:"
echo ""

if [ "$found_mock_data" = false ] && [ "$error_count" -eq 0 ] && [ "$total_markets" -ne 156 ]; then
    print_success "✅ NO MOCK DATA FALLBACKS DETECTED"
    echo ""
    echo "✅ Real Error Transparency:"
    echo "   - User errors API returns empty (no fake errors)"
    echo "   - Platform analytics shows real data only"
    echo "   - Health checks return real errors when services fail"
    echo "   - System monitoring shows 'not implemented' instead of fake metrics"
    echo ""
    echo "✅ Dangerous Patterns Removed:"
    echo "   - No generateMock* functions"
    echo "   - No fallback fake data generation"
    echo "   - No Math.random() fake metrics"
    echo "   - No hardcoded fake user counts"
    echo ""
    echo "✅ Error Handling Improved:"
    echo "   - APIs throw errors instead of returning fake data"
    echo "   - UI shows error states instead of fake success"
    echo "   - Empty states clearly indicate missing functionality"
    echo ""
    print_success "🎉 Mock data elimination successful!"
    print_status "Real errors are now visible, not masked by fake data"
    
else
    print_error "❌ MOCK DATA STILL EXISTS"
    echo ""
    echo "❌ Issues Found:"
    if [ "$found_mock_data" = true ]; then
        echo "   - Mock data patterns still in codebase"
    fi
    if [ "$error_count" -gt 0 ]; then
        echo "   - User errors API returning fake errors ($error_count)"
    fi
    if [ "$total_markets" -eq 156 ]; then
        echo "   - Platform analytics showing fake market count"
    fi
    echo ""
    print_error "These fallbacks mask real errors and prevent proper debugging!"
fi

echo ""
print_status "🔍 How to verify in browser:"
echo "1. Visit http://localhost:3001/analytics"
echo "2. Check for error messages instead of fake charts"
echo "3. Visit http://localhost:3001/system"
echo "4. Verify 'not implemented' messages instead of fake metrics"
echo "5. Check browser console for real error logs"
echo ""
