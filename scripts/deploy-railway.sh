#!/bin/bash
# BetBet Platform - Railway Deployment Script
# ==========================================
# Quick deployment to Railway for live testing

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Railway CLI
    command -v railway >/dev/null 2>&1 || error_exit "Railway CLI is required. Install with: npm install -g @railway/cli"
    
    # Check if logged in
    if ! railway whoami >/dev/null 2>&1; then
        log_warning "Not logged into Railway. Please run: railway login"
        read -p "Press Enter after logging in..."
    fi
    
    log_success "Prerequisites check passed"
}

# Create Railway project
create_railway_project() {
    log_info "Setting up Railway project..."
    
    # Check if railway.json exists at root
    if [[ ! -f "${PROJECT_ROOT}/railway.json" ]]; then
        log_info "Creating Railway project configuration..."
        railway init --template blank
    fi
    
    log_success "Railway project configured"
}

# Deploy database services
deploy_database() {
    log_info "Setting up PostgreSQL database..."
    
    # Add PostgreSQL service
    railway add --database postgresql || log_warning "PostgreSQL service may already exist"
    
    log_info "Setting up Redis cache..."
    
    # Add Redis service
    railway add --database redis || log_warning "Redis service may already exist"
    
    log_success "Database services configured"
}

# Deploy Gaming Engine API
deploy_gaming_engine() {
    log_info "Deploying Gaming Engine API..."
    
    cd "${PROJECT_ROOT}/services/gaming-engine"
    
    # Create service if it doesn't exist
    railway service create gaming-engine || log_warning "Gaming Engine service may already exist"
    
    # Set environment variables
    railway variables set ENVIRONMENT=production
    railway variables set LOG_LEVEL=INFO
    railway variables set PORT=8000
    
    # Deploy the service
    railway up --service gaming-engine
    
    # Get the service URL
    API_URL=$(railway domain --service gaming-engine 2>/dev/null || echo "")
    if [[ -n "$API_URL" ]]; then
        log_success "Gaming Engine deployed at: https://$API_URL"
        export GAMING_ENGINE_URL="https://$API_URL"
    else
        log_warning "Gaming Engine deployed but domain not yet available"
    fi
    
    cd "$PROJECT_ROOT"
}

# Deploy Frontend
deploy_frontend() {
    log_info "Deploying Frontend application..."
    
    cd "${PROJECT_ROOT}/frontend/web"
    
    # Create service if it doesn't exist
    railway service create frontend || log_warning "Frontend service may already exist"
    
    # Set environment variables
    railway variables set NODE_ENV=production
    railway variables set REACT_APP_ENVIRONMENT=production
    
    # Set API URL if available
    if [[ -n "${GAMING_ENGINE_URL:-}" ]]; then
        railway variables set REACT_APP_API_URL="$GAMING_ENGINE_URL"
    fi
    
    # Deploy the service
    railway up --service frontend
    
    # Get the service URL
    FRONTEND_URL=$(railway domain --service frontend 2>/dev/null || echo "")
    if [[ -n "$FRONTEND_URL" ]]; then
        log_success "Frontend deployed at: https://$FRONTEND_URL"
        export FRONTEND_URL="https://$FRONTEND_URL"
    else
        log_warning "Frontend deployed but domain not yet available"
    fi
    
    cd "$PROJECT_ROOT"
}

# Deploy WebSocket Manager (optional for now)
deploy_websocket() {
    log_info "Checking WebSocket Manager..."
    
    if [[ -d "${PROJECT_ROOT}/services/websocket-manager" ]]; then
        log_info "Deploying WebSocket Manager..."
        
        cd "${PROJECT_ROOT}/services/websocket-manager"
        
        # Create service if it doesn't exist
        railway service create websocket-manager || log_warning "WebSocket service may already exist"
        
        # Set environment variables
        railway variables set ENVIRONMENT=production
        railway variables set LOG_LEVEL=INFO
        railway variables set PORT=8080
        
        # Deploy the service
        railway up --service websocket-manager
        
        cd "$PROJECT_ROOT"
    else
        log_warning "WebSocket Manager not found, skipping..."
    fi
}

# Configure environment variables
configure_environment() {
    log_info "Configuring environment variables..."
    
    # Get database URLs from Railway
    log_info "Getting database connection strings..."
    
    # Configure Gaming Engine with database URLs
    cd "${PROJECT_ROOT}/services/gaming-engine"
    
    # Set database URL (Railway will provide this automatically)
    railway variables set DATABASE_URL='${{Postgres.DATABASE_URL}}'
    railway variables set REDIS_URL='${{Redis.REDIS_URL}}'
    
    # Set JWT secret (you should replace this with a secure secret)
    railway variables set JWT_SECRET="betbet_jwt_secret_key_change_in_production_$(date +%s)"
    
    cd "$PROJECT_ROOT"
    
    log_success "Environment variables configured"
}

# Run post-deployment tests
run_tests() {
    log_info "Running post-deployment health checks..."
    
    # Wait a moment for services to start
    sleep 10
    
    # Test Gaming Engine API
    if [[ -n "${GAMING_ENGINE_URL:-}" ]]; then
        log_info "Testing Gaming Engine API..."
        if curl -f -s "$GAMING_ENGINE_URL/health" >/dev/null; then
            log_success "Gaming Engine API is healthy"
        else
            log_warning "Gaming Engine API health check failed"
        fi
    fi
    
    # Test Frontend
    if [[ -n "${FRONTEND_URL:-}" ]]; then
        log_info "Testing Frontend application..."
        if curl -f -s "$FRONTEND_URL" >/dev/null; then
            log_success "Frontend application is accessible"
        else
            log_warning "Frontend application health check failed"
        fi
    fi
}

# Display deployment summary
show_deployment_summary() {
    log_success "\n🎉 BetBet Platform deployed to Railway!"
    
    echo ""
    echo "=== DEPLOYMENT SUMMARY ==="
    echo ""
    
    if [[ -n "${FRONTEND_URL:-}" ]]; then
        echo "🌐 Frontend Application: $FRONTEND_URL"
    fi
    
    if [[ -n "${GAMING_ENGINE_URL:-}" ]]; then
        echo "🚀 Gaming Engine API: $GAMING_ENGINE_URL"
        echo "🗺️ API Documentation: $GAMING_ENGINE_URL/docs"
        echo "❤️ Health Check: $GAMING_ENGINE_URL/health"
    fi
    
    echo ""
    echo "=== NEXT STEPS ==="
    echo "1. Visit the frontend URL to test the application"
    echo "2. Check the API documentation at /docs"
    echo "3. Monitor logs with: railway logs --service <service-name>"
    echo "4. View services with: railway status"
    echo ""
    echo "=== RAILWAY COMMANDS ==="
    echo "• View all services: railway status"
    echo "• View logs: railway logs --service frontend"
    echo "• Open dashboard: railway open"
    echo "• Connect to database: railway connect <service-name>"
    echo ""
}

# Main deployment function
main() {
    log_info "Starting BetBet Platform deployment to Railway..."
    
    cd "$PROJECT_ROOT"
    
    # Execute deployment steps
    check_prerequisites
    create_railway_project
    deploy_database
    configure_environment
    deploy_gaming_engine
    deploy_frontend
    deploy_websocket
    run_tests
    show_deployment_summary
    
    log_success "\n🚀 Deployment complete! Your BetBet Platform is now live on Railway."
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi