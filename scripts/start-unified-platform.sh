#!/bin/bash

# BetBet Unified Platform Startup Script
# =======================================
# 
# Starts all integrated platform services including API Gateway,
# WebSocket Manager, and all microservices in the correct order.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[BetBet Platform]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=${3:-30}
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s $url >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to cleanup background processes on exit
cleanup() {
    print_status "Shutting down platform services..."
    
    # Kill background processes
    for pid in "${bg_pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            print_status "Stopping process $pid"
            kill $pid
        fi
    done
    
    # Stop Docker services
    if [ "$DOCKER_SERVICES_STARTED" = "true" ]; then
        print_status "Stopping Docker services..."
        docker-compose -f services/api-gateway/docker-compose.yml down
        docker-compose -f services/websocket-manager/docker-compose.yml down
    fi
    
    print_success "Platform shutdown complete"
    exit 0
}

# Set up cleanup trap
trap cleanup INT TERM EXIT

# Array to track background processes
bg_pids=()

print_status "🚀 Starting BetBet Unified Platform..."
print_status "======================================"

# Change to project root directory
cd "$(dirname "$0")/.."

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "services" ]; then
    print_error "Please run this script from the BetBet project root directory"
    exit 1
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Step 1: Start Infrastructure Services
print_status "Step 1: Starting Infrastructure Services"
print_status "========================================"

# Start API Gateway
print_status "Starting API Gateway..."
cd services/api-gateway
if [ ! -f "docker-compose.yml" ]; then
    print_error "API Gateway docker-compose.yml not found"
    exit 1
fi

docker-compose up -d
DOCKER_SERVICES_STARTED=true
cd ../..

# Wait for API Gateway to be ready
wait_for_service "API Gateway" "http://localhost:8000/health" 60

# Start WebSocket Manager
print_status "Starting WebSocket Manager..."
cd services/websocket-manager
if [ ! -f "docker-compose.yml" ]; then
    print_error "WebSocket Manager docker-compose.yml not found"
    exit 1
fi

docker-compose up -d
cd ../..

# Wait for WebSocket Manager to be ready
wait_for_service "WebSocket Manager" "http://localhost:8080/health" 30

print_success "Infrastructure services started successfully"

# Step 2: Start Microservices
print_status "Step 2: Starting Microservices"
print_status "==============================="

# List of services to start
services=(
    "gaming-engine:8001"
    "custom-betting:8002"
    "expert-analysts:8003"
    "sports-analysis:8004"
    "odds-exchange:8005"
    "leaderboards:8006"
)

# Start each microservice
for service_info in "${services[@]}"; do
    IFS=':' read -r service port <<< "$service_info"
    
    print_status "Starting $service on port $port..."
    
    # Check if port is already in use
    if check_port $port; then
        print_warning "$service already running on port $port"
        continue
    fi
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        print_warning "Service directory services/$service not found, skipping..."
        continue
    fi
    
    # Start the service
    cd "services/$service"
    
    if [ -f "requirements.txt" ]; then
        # Python service
        if [ ! -d "venv" ]; then
            print_status "Creating virtual environment for $service..."
            python3 -m venv venv
            source venv/bin/activate
            pip install -r requirements.txt
        else
            source venv/bin/activate
        fi
        
        # Start the service
        if [ -f "main.py" ]; then
            python main.py &
        elif [ -f "app/main.py" ]; then
            python -m app.main &
        else
            print_warning "No main.py found for $service, skipping..."
            cd ../..
            continue
        fi
        
        bg_pids+=($!)
        deactivate
    else
        print_warning "No requirements.txt found for $service, skipping..."
    fi
    
    cd ../..
done

# Wait for microservices to be ready
print_status "Waiting for microservices to start..."
sleep 10

# Check microservice health
for service_info in "${services[@]}"; do
    IFS=':' read -r service port <<< "$service_info"
    
    if check_port $port; then
        print_success "$service is running on port $port"
    else
        print_warning "$service may not have started correctly on port $port"
    fi
done

# Step 3: Start Frontend Applications
print_status "Step 3: Starting Frontend Applications"
print_status "======================================"

print_status "Starting Web Frontend..."
cd frontend/web

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Start the frontend
print_status "Starting Next.js development server..."
npm run dev &
bg_pids+=($!)

cd ../..

# Wait for frontend to be ready
wait_for_service "Web Frontend" "http://localhost:3000" 60

print_success "Frontend started successfully"

# Step 4: Display Platform Status
print_status "Step 4: Platform Status"
print_status "======================="

print_success "🎉 BetBet Unified Platform is now running!"
echo ""
print_status "Access Points:"
echo "  🌐 Web Application:     http://localhost:3000"
echo "  🔧 API Gateway:         http://localhost:8000"
echo "  🔌 WebSocket Manager:   ws://localhost:8080"
echo "  📊 Kong Admin:          http://localhost:8001"
echo "  📈 Prometheus:          http://localhost:9090"
echo ""
print_status "Microservices:"
echo "  🎮 Gaming Engine:       http://localhost:8001"
echo "  🎯 Custom Betting:      http://localhost:8002"
echo "  👑 Expert Analysts:     http://localhost:8003"
echo "  📊 Sports Analysis:     http://localhost:8004"
echo "  📈 Trading Exchange:    http://localhost:8005"
echo "  🏆 Leaderboards:        http://localhost:8006"
echo ""
print_status "Development Tools:"
echo "  📝 API Documentation:   http://localhost:8000/docs"
echo "  🔍 Redis Commander:     http://localhost:8082"
echo "  📊 Grafana:             http://localhost:3001 (admin/admin)"
echo ""

# Step 5: Health Check
print_status "Step 5: Platform Health Check"
print_status "=============================="

health_check_urls=(
    "API Gateway:http://localhost:8000/health"
    "WebSocket Manager:http://localhost:8080/health"
    "Gaming Engine:http://localhost:8001/health"
    "Custom Betting:http://localhost:8002/health"
    "Expert Analysts:http://localhost:8003/health"
    "Sports Analysis:http://localhost:8004/health"
    "Trading Exchange:http://localhost:8005/health"
    "Leaderboards:http://localhost:8006/health"
)

print_status "Performing health checks..."
echo ""

for check in "${health_check_urls[@]}"; do
    IFS=':' read -r service url <<< "$check"
    
    if curl -f -s "$url" >/dev/null 2>&1; then
        print_success "$service: Healthy"
    else
        print_warning "$service: Health check failed"
    fi
done

echo ""
print_status "🎯 Platform Integration Status:"

# Check if services can communicate through API Gateway
if curl -f -s "http://localhost:8000/api/v1/gaming/health" >/dev/null 2>&1; then
    print_success "API Gateway → Gaming Engine: ✓"
else
    print_warning "API Gateway → Gaming Engine: ✗"
fi

if curl -f -s "http://localhost:8000/api/v1/leaderboards/health" >/dev/null 2>&1; then
    print_success "API Gateway → Leaderboards: ✓"
else
    print_warning "API Gateway → Leaderboards: ✗"
fi

# Check WebSocket Manager stats
if curl -f -s "http://localhost:8080/stats" >/dev/null 2>&1; then
    print_success "WebSocket Manager: Operational"
    
    # Display connection stats
    stats=$(curl -s "http://localhost:8080/stats" 2>/dev/null || echo '{"total_connections":0}')
    echo "    📊 WebSocket Stats: $stats"
else
    print_warning "WebSocket Manager: Stats unavailable"
fi

echo ""
print_success "✅ BetBet Unified Platform is fully operational!"
print_status "💡 The platform is now ready for development and testing"
echo ""
print_status "📱 Next Steps:"
echo "  1. Open http://localhost:3000 to access the web application"
echo "  2. Test cross-module integration through the unified interface"
echo "  3. Monitor real-time updates via WebSocket connections"
echo "  4. Use the mobile app (cd mobile && npm run ios/android)"
echo ""
print_status "🛑 Press Ctrl+C to shut down all services"

# Keep script running and monitor services
while true; do
    sleep 30
    
    # Check if critical services are still running
    if ! curl -f -s "http://localhost:8000/health" >/dev/null 2>&1; then
        print_error "API Gateway appears to be down!"
    fi
    
    if ! curl -f -s "http://localhost:8080/health" >/dev/null 2>&1; then
        print_error "WebSocket Manager appears to be down!"
    fi
    
    if ! curl -f -s "http://localhost:3000" >/dev/null 2>&1; then
        print_error "Web Frontend appears to be down!"
    fi
done