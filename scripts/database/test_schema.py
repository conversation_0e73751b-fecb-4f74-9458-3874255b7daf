#!/usr/bin/env python3
"""
BetBet Platform - Database Schema Integration Test
==================================================
Test the gaming engine database schema with shared libraries
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import asyncpg
from decimal import Decimal

# Database connection settings
DATABASE_URL = "postgresql://postgres:123Bubblegums@localhost:5432/betbet_db"

async def test_database_connection():
    """Test basic database connectivity"""
    print("🔌 Testing database connection...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        result = await conn.fetchval("SELECT 1")
        await conn.close()
        
        if result == 1:
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

async def test_schema_exists():
    """Test that gaming_engine schema exists"""
    print("🏗️  Testing schema existence...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check if gaming_engine schema exists
        schema_exists = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'gaming_engine')"
        )
        
        if schema_exists:
            print("✅ gaming_engine schema exists")
        else:
            print("❌ gaming_engine schema missing")
            await conn.close()
            return False
        
        # Check core tables
        tables_to_check = [
            'games', 'game_sessions', 'session_participants', 
            'game_states', 'spectator_bets', 'tournaments',
            'tournament_participants', 'audit_logs', 'events',
            'performance_metrics', 'transactions'
        ]
        
        for table in tables_to_check:
            table_exists = await conn.fetchval(
                f"SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'gaming_engine' AND table_name = '{table}')"
            )
            
            if table_exists:
                print(f"✅ Table gaming_engine.{table} exists")
            else:
                print(f"❌ Table gaming_engine.{table} missing")
                await conn.close()
                return False
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Schema test error: {e}")
        return False

async def test_functions_exist():
    """Test that helper functions exist"""
    print("⚙️  Testing helper functions...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        functions_to_check = [
            'gaming_engine.create_game_session',
            'gaming_engine.join_game_session',
            'gaming_engine.start_game_session',
            'gaming_engine.complete_game_session',
            'gaming_engine.place_spectator_bet',
            'gaming_engine.get_user_game_stats',
            'gaming_engine.health_check'
        ]
        
        for function in functions_to_check:
            function_exists = await conn.fetchval(f"""
                SELECT EXISTS(
                    SELECT 1 FROM information_schema.routines 
                    WHERE routine_schema = 'gaming_engine' 
                    AND routine_name = '{function.split('.')[-1]}'
                    AND routine_type = 'FUNCTION'
                )
            """)
            
            if function_exists:
                print(f"✅ Function {function} exists")
            else:
                print(f"❌ Function {function} missing")
                await conn.close()
                return False
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Functions test error: {e}")
        return False

async def test_sample_data():
    """Test basic data operations"""
    print("📊 Testing data operations...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test basic queries
        games_count = await conn.fetchval("SELECT COUNT(*) FROM gaming_engine.games WHERE deleted_at IS NULL")
        sessions_count = await conn.fetchval("SELECT COUNT(*) FROM gaming_engine.game_sessions WHERE deleted_at IS NULL")
        users_count = await conn.fetchval("SELECT COUNT(*) FROM public.users WHERE deleted_at IS NULL")
        
        print(f"✅ Found {games_count} games")
        print(f"✅ Found {sessions_count} game sessions") 
        print(f"✅ Found {users_count} users")
        
        # Test a simple function call
        health_check_result = await conn.fetch("SELECT * FROM gaming_engine.health_check()")
        print(f"✅ Health check returned {len(health_check_result)} checks")
        
        for check in health_check_result:
            print(f"   {check['check_name']}: {check['status']} - {check['details']}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Data operations test error: {e}")
        return False

async def test_performance():
    """Test query performance"""
    print("⚡ Testing query performance...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        import time
        
        # Test game lookup (should be <5ms)
        start_time = time.time()
        games = await conn.fetch("""
            SELECT * FROM gaming_engine.games 
            WHERE is_active = true AND deleted_at IS NULL
            ORDER BY popularity_score DESC LIMIT 10
        """)
        query_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        print(f"✅ Games query took {query_time:.2f}ms (target: <5ms)")
        print(f"   Found {len(games)} active games")
        
        # Test session lookup
        start_time = time.time()
        sessions = await conn.fetch("""
            SELECT gs.*, g.name as game_name 
            FROM gaming_engine.game_sessions gs
            JOIN gaming_engine.games g ON gs.game_id = g.id
            WHERE gs.state = 'waiting' AND gs.deleted_at IS NULL
            LIMIT 10
        """)
        query_time = (time.time() - start_time) * 1000
        
        print(f"✅ Sessions query took {query_time:.2f}ms (target: <5ms)")
        print(f"   Found {len(sessions)} waiting sessions")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

async def main():
    """Run all database tests"""
    print("🎮 BetBet Gaming Engine Database Schema Test")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Database Connection", test_database_connection),
        ("Schema Structure", test_schema_exists),
        ("Helper Functions", test_functions_exist),
        ("Data Operations", test_sample_data),
        ("Query Performance", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"🏆 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Database schema is ready for API development.")
        print("\n🚀 Next steps:")
        print("   1. Hand off to Claude-API for FastAPI implementation")
        print("   2. Implement gaming engine API endpoints")
        print("   3. Add WebSocket real-time features")
        print("   4. Integrate with shared libraries")
        return True
    else:
        print("⚠️  Some tests failed. Please check the database setup.")
        return False

if __name__ == "__main__":
    asyncio.run(main())