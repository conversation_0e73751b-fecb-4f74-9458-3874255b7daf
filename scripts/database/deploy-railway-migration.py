#!/usr/bin/env python3
"""
Railway Database Migration Deployment
=====================================

Deploy Gaming Engine schema to Railway PostgreSQL using the established
project migration system from database/migrations/001_create_gaming_engine.sql
"""

import asyncio
import asyncpg
import os
import sys
from pathlib import Path
import structlog

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


async def connect_to_railway_db():
    """Connect to Railway PostgreSQL using environment DATABASE_URL"""
    try:
        # Use the shared database connection system
        from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
        
        settings = DatabaseSettings()
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        
        logger.info("✅ Connected to Railway PostgreSQL via shared connection manager")
        return db_manager
        
    except Exception as e:
        logger.error("❌ Failed to connect to Railway PostgreSQL", error=str(e))
        raise


async def check_migration_status(db_manager):
    """Check if the gaming engine migration has already been applied"""
    try:
        # Check if migration_log table exists
        migration_log_exists = await db_manager.execute_query(
            "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'migration_log' AND table_schema = 'public')",
            fetch_one=True
        )
        
        if not migration_log_exists or not migration_log_exists[0]:
            logger.info("📋 Migration log table does not exist - first migration")
            return False
        
        # Check if gaming engine migration is already applied
        migration_status = await db_manager.execute_query(
            "SELECT status FROM public.migration_log WHERE migration_id = '001'",
            fetch_one=True
        )
        
        if migration_status and migration_status[0] == 'completed':
            logger.info("✅ Gaming Engine migration already completed")
            return True
        elif migration_status:
            logger.warning(f"⚠️ Gaming Engine migration exists with status: {migration_status[0]}")
            return False
        else:
            logger.info("📋 Gaming Engine migration not found - needs deployment")
            return False
            
    except Exception as e:
        logger.error("❌ Error checking migration status", error=str(e))
        return False


async def execute_schema_files(db_manager):
    """Execute the schema files in the correct order"""
    try:
        schema_files = [
            project_root / "database" / "schemas" / "gaming_engine.sql",
            project_root / "database" / "schemas" / "gaming_engine_indexes.sql", 
            project_root / "database" / "schemas" / "gaming_engine_functions.sql"
        ]
        
        for schema_file in schema_files:
            if not schema_file.exists():
                logger.error(f"❌ Schema file not found: {schema_file}")
                continue
                
            logger.info(f"📄 Executing schema file: {schema_file.name}")
            
            sql_content = schema_file.read_text()
            
            # Execute the schema
            async with db_manager.get_raw_connection() as conn:
                await conn.execute(sql_content)
                
            logger.info(f"✅ Successfully executed: {schema_file.name}")
        
        return True
        
    except Exception as e:
        logger.error("❌ Error executing schema files", error=str(e))
        return False


async def execute_migration_manually(db_manager):
    """Execute the gaming engine migration manually (without file includes)"""
    try:
        logger.info("🚀 Starting manual migration execution...")
        
        # Create migration log table first
        await db_manager.execute_query("""
            CREATE TABLE IF NOT EXISTS public.migration_log (
                id SERIAL PRIMARY KEY,
                migration_id VARCHAR(50) UNIQUE NOT NULL,
                migration_name VARCHAR(200) NOT NULL,
                started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                completed_at TIMESTAMP WITH TIME ZONE,
                rollback_at TIMESTAMP WITH TIME ZONE,
                status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'rolled_back'))
            );
        """)
        
        # Record migration start
        await db_manager.execute_query("""
            INSERT INTO public.migration_log (migration_id, migration_name, started_at)
            VALUES ('001', 'create_gaming_engine', NOW())
            ON CONFLICT (migration_id) DO UPDATE SET 
                started_at = NOW(), 
                status = 'running';
        """)
        
        logger.info("📝 Migration started and logged")
        
        # Execute schema files
        schema_success = await execute_schema_files(db_manager)
        
        if not schema_success:
            raise Exception("Schema execution failed")
        
        # Create admin user
        await db_manager.execute_query("""
            INSERT INTO public.users (
                id, username, email, password_hash, first_name, last_name, balance,
                is_active, is_verified, roles, permissions, created_by, updated_by
            ) VALUES (
                '00000000-0000-0000-0000-000000000001', 'admin', '<EMAIL>',
                '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK',
                'System', 'Administrator', 0.00, true, true,
                ARRAY['admin', 'user']::TEXT[],
                ARRAY['admin', 'user_management', 'game_management', 'financial_management']::TEXT[],
                '00000000-0000-0000-0000-000000000001',
                '00000000-0000-0000-0000-000000000001'
            ) ON CONFLICT (id) DO NOTHING;
        """)
        
        # Create test user
        await db_manager.execute_query("""
            INSERT INTO public.users (
                id, username, email, password_hash, first_name, last_name, balance,
                is_active, is_verified, created_by, updated_by
            ) VALUES (
                '00000000-0000-0000-0000-000000000002', 'testuser', '<EMAIL>',
                '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewwWwLJKW.1wH0fK',
                'Test', 'User', 1000.00, true, true,
                '00000000-0000-0000-0000-000000000001',
                '00000000-0000-0000-0000-000000000001'
            ) ON CONFLICT (id) DO NOTHING;
        """)
        
        # Update migration status
        await db_manager.execute_query("""
            UPDATE public.migration_log 
            SET completed_at = NOW(), status = 'completed'
            WHERE migration_id = '001';
        """)
        
        logger.info("✅ Migration completed successfully")
        return True
        
    except Exception as e:
        # Mark migration as failed
        try:
            await db_manager.execute_query("""
                UPDATE public.migration_log 
                SET status = 'failed'
                WHERE migration_id = '001';
            """)
        except:
            pass
            
        logger.error("❌ Migration execution failed", error=str(e))
        return False


async def verify_deployment(db_manager):
    """Verify that the schema was deployed correctly"""
    try:
        logger.info("🔍 Verifying schema deployment...")
        
        # Check gaming_engine schema exists
        schema_exists = await db_manager.execute_query(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'gaming_engine')",
            fetch_one=True
        )
        
        if not schema_exists or not schema_exists[0]:
            logger.error("❌ gaming_engine schema not found")
            return False
        
        # Check all expected tables
        expected_tables = [
            'games', 'game_sessions', 'session_participants', 
            'game_states', 'spectator_bets', 'tournaments',
            'tournament_participants', 'audit_logs', 'events',
            'performance_metrics', 'transactions'
        ]
        
        tables = await db_manager.execute_query(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'gaming_engine'",
            fetch_all=True
        )
        
        found_tables = [dict(row)['table_name'] for row in tables] if tables else []
        missing_tables = [t for t in expected_tables if t not in found_tables]
        
        if missing_tables:
            logger.error(f"❌ Missing tables: {missing_tables}")
            return False
        
        logger.info(f"✅ All {len(expected_tables)} tables verified")
        
        # Check users exist
        user_count = await db_manager.execute_query(
            "SELECT COUNT(*) FROM public.users",
            fetch_one=True
        )
        
        if user_count and user_count[0] >= 2:
            logger.info(f"✅ Users created: {user_count[0]} users found")
        else:
            logger.warning("⚠️ Expected users not found")
        
        logger.info("🎉 Schema deployment verification successful!")
        return True
        
    except Exception as e:
        logger.error("❌ Schema verification failed", error=str(e))
        return False


async def main():
    """Main deployment function"""
    try:
        logger.info("🎯 Starting Railway PostgreSQL migration deployment")
        logger.info("📋 Using established project migration system")
        
        # Connect to Railway database
        db_manager = await connect_to_railway_db()
        
        # Check if migration already applied
        migration_applied = await check_migration_status(db_manager)
        
        if migration_applied:
            logger.info("✅ Gaming Engine migration already completed")
            
            # Still verify deployment
            verification_success = await verify_deployment(db_manager)
            await db_manager.shutdown()
            
            return verification_success
        
        # Execute migration
        logger.info("🚀 Deploying Gaming Engine migration to Railway...")
        migration_success = await execute_migration_manually(db_manager)
        
        if not migration_success:
            await db_manager.shutdown()
            return False
        
        # Verify deployment
        verification_success = await verify_deployment(db_manager)
        
        await db_manager.shutdown()
        
        if verification_success:
            logger.info("🎉 RAILWAY DEPLOYMENT COMPLETE!")
            logger.info("✅ Gaming Engine schema successfully deployed to Railway PostgreSQL")
            logger.info("🔗 Database ready for API integration")
            logger.info("👉 Next: Claude-API can proceed with full confidence")
            
        return verification_success
        
    except Exception as e:
        logger.error("❌ Railway deployment failed", error=str(e))
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)