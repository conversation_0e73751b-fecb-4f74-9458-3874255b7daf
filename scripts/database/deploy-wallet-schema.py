#!/usr/bin/env python3
"""
BetBet Platform - Wallet Schema Database Deployment Script
==========================================================

This script deploys the wallet schema to the betbet_db database,
handling all financial transactions, user balances, and payment processing.

Components deployed:
1. Wallet schema with all tables
2. Indexes for performance optimization
3. Triggers for data integrity
4. Proper error handling and rollback capabilities

Author: Claude Code
Date: 2025-01-23
Template Compliance: BetBet deployment patterns
"""

import os
import sys
import json
import time
import logging
import argparse
from pathlib import Path
from typing import Dict, Optional, Tuple
from datetime import datetime

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wallet_schema_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WalletSchemaDeployment:
    """
    Wallet schema database deployment manager.
    Handles complete schema deployment with error handling and rollback capabilities.
    """
    
    def __init__(self, db_config: Dict[str, str], dry_run: bool = False):
        """Initialize deployment manager."""
        self.db_config = db_config
        self.dry_run = dry_run
        self.deployment_start = datetime.now()
        
        # File paths
        self.base_path = Path(__file__).parent.parent.parent
        self.schema_file = self.base_path / "database" / "schemas" / "wallet.sql"
        
        self.connection = None
        self.deployment_log = []
    
    def connect_database(self) -> bool:
        """Establish database connection with proper error handling."""
        try:
            logger.info("Connecting to database...")
            
            # Create connection string
            conn_string = (
                f"host={self.db_config['host']} "
                f"port={self.db_config['port']} "
                f"dbname={self.db_config['database']} "
                f"user={self.db_config['user']} "
                f"password={self.db_config['password']}"
            )
            
            self.connection = psycopg2.connect(conn_string)
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            # Test connection
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                logger.info(f"Connected to PostgreSQL: {version}")
            
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met for deployment."""
        logger.info("Checking deployment prerequisites...")
        
        try:
            with self.connection.cursor() as cursor:
                # Check PostgreSQL version (minimum 12.0)
                cursor.execute("SELECT version();")
                version_str = cursor.fetchone()[0]
                if not any(v in version_str for v in ['PostgreSQL 12', 'PostgreSQL 13', 'PostgreSQL 14', 'PostgreSQL 15', 'PostgreSQL 16']):
                    logger.warning(f"PostgreSQL version may not be fully supported: {version_str}")
                
                # Check required extensions availability
                required_extensions = ['uuid-ossp', 'pgcrypto']
                for ext in required_extensions:
                    cursor.execute("""
                        SELECT 1 FROM pg_available_extensions 
                        WHERE name = %s;
                    """, (ext,))
                    
                    if cursor.fetchone():
                        logger.info(f"✓ Extension {ext} is available")
                    else:
                        logger.error(f"✗ Required extension {ext} not available")
                        return False
                
                # Check if wallet schema already exists
                cursor.execute("""
                    SELECT 1 FROM information_schema.schemata 
                    WHERE schema_name = 'wallet';
                """)
                
                if cursor.fetchone():
                    logger.warning("⚠ Schema 'wallet' already exists - this may be a re-deployment")
                    return self.confirm_continue("Continue with re-deployment?")
                
                logger.info("✓ All prerequisites met")
                return True
                
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            return False
    
    def confirm_continue(self, message: str) -> bool:
        """Ask for user confirmation if not in automated mode."""
        if self.dry_run:
            logger.info(f"DRY RUN: Would ask - {message}")
            return True
        
        if os.getenv('AUTOMATED_DEPLOYMENT') == 'true':
            logger.info(f"Automated deployment - proceeding with: {message}")
            return True
        
        response = input(f"{message} (y/N): ").lower().strip()
        return response in ['y', 'yes']
    
    def execute_sql_file(self, file_path: Path, description: str, timeout: int = 300) -> Tuple[bool, Optional[str]]:
        """Execute a SQL file with timeout and error handling."""
        logger.info(f"Executing: {description}")
        
        if not file_path.exists():
            error_msg = f"SQL file not found: {file_path}"
            logger.error(error_msg)
            return False, error_msg
        
        if self.dry_run:
            logger.info(f"DRY RUN: Would execute {file_path}")
            return True, None
        
        start_time = time.time()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            logger.info(f"File size: {len(sql_content)} characters")
            
            # Execute with timeout
            with self.connection.cursor() as cursor:
                logger.info(f"Executing SQL (timeout: {timeout}s)...")
                cursor.execute(sql_content)
                
                # Log any notices or warnings
                if cursor.connection.notices:
                    for notice in cursor.connection.notices:
                        logger.info(f"NOTICE: {notice.strip()}")
                    cursor.connection.notices.clear()
            
            execution_time = time.time() - start_time
            logger.info(f"✓ Completed in {execution_time:.2f}s: {description}")
            
            return True, None
            
        except psycopg2.Error as e:
            execution_time = time.time() - start_time  
            error_msg = f"SQL execution failed after {execution_time:.2f}s: {e}"
            logger.error(error_msg)
            return False, error_msg
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Unexpected error after {execution_time:.2f}s: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def run_health_checks(self) -> bool:
        """Run comprehensive health checks after deployment."""
        logger.info("Running post-deployment health checks...")
        
        if self.dry_run:
            logger.info("DRY RUN: Would run health checks")
            return True
        
        try:
            with self.connection.cursor() as cursor:
                # Check if wallet schema exists
                cursor.execute("""
                    SELECT 1 FROM information_schema.schemata 
                    WHERE schema_name = 'wallet';
                """)
                
                if not cursor.fetchone():
                    logger.error("✗ Wallet schema not found")
                    return False
                logger.info("✓ Wallet schema exists")
                
                # Check expected tables
                expected_tables = ['user_balances', 'transactions', 'payment_methods', 'withdrawal_requests']
                for table in expected_tables:
                    cursor.execute("""
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = 'wallet' AND table_name = %s;
                    """, (table,))
                    
                    if cursor.fetchone():
                        logger.info(f"✓ Table wallet.{table} exists")
                    else:
                        logger.error(f"✗ Table wallet.{table} not found")
                        return False
                
                # Check indexes
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM pg_indexes 
                    WHERE schemaname = 'wallet';
                """)
                index_count = cursor.fetchone()[0]
                logger.info(f"✓ Performance indexes: {index_count} indexes created")
                
                # Check functions/triggers
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.routines 
                    WHERE routine_schema = 'wallet';
                """)
                function_count = cursor.fetchone()[0]
                logger.info(f"✓ Functions: {function_count} functions available")
                
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.triggers 
                    WHERE trigger_schema = 'wallet';
                """)
                trigger_count = cursor.fetchone()[0]
                logger.info(f"✓ Triggers: {trigger_count} triggers created")
                
                # Test basic functionality - create a test user balance record
                try:
                    test_user_id = '00000000-0000-0000-0000-000000000000'
                    cursor.execute("""
                        INSERT INTO wallet.user_balances (user_id, available_balance) 
                        VALUES (%s, 100.00) 
                        ON CONFLICT (user_id) DO UPDATE SET available_balance = 100.00;
                    """, (test_user_id,))
                    
                    cursor.execute("""
                        SELECT available_balance FROM wallet.user_balances 
                        WHERE user_id = %s;
                    """, (test_user_id,))
                    
                    balance = cursor.fetchone()
                    if balance and balance[0] == 100.00:
                        logger.info("✓ Basic wallet functionality test passed")
                        
                        # Clean up test data
                        cursor.execute("""
                            DELETE FROM wallet.user_balances WHERE user_id = %s;
                        """, (test_user_id,))
                    else:
                        logger.error("✗ Basic wallet functionality test failed")
                        return False
                        
                except Exception as e:
                    logger.error(f"✗ Basic wallet functionality test failed: {e}")
                    return False
                
                logger.info("✓ All health checks passed")
                return True
                
        except Exception as e:
            logger.error(f"Health checks failed: {e}")
            return False
    
    def create_deployment_summary(self) -> Dict:
        """Create a summary of the deployment for logging."""
        deployment_time = datetime.now() - self.deployment_start
        
        summary = {
            'module': 'Wallet Schema',
            'deployment_id': f"wallet_schema_{int(self.deployment_start.timestamp())}",
            'start_time': self.deployment_start.isoformat(),
            'duration_seconds': deployment_time.total_seconds(),
            'dry_run': self.dry_run,
            'database': {
                'host': self.db_config['host'],
                'port': self.db_config['port'],
                'database': self.db_config['database']
            },
            'schema_file': str(self.schema_file),
            'status': 'unknown'
        }
        
        return summary
    
    def deploy(self) -> bool:
        """Execute the complete deployment process."""
        logger.info("="*80)
        logger.info("BETBET WALLET SCHEMA DEPLOYMENT")
        logger.info("="*80)
        logger.info(f"Start time: {self.deployment_start}")
        logger.info(f"Mode: {'DRY RUN' if self.dry_run else 'PRODUCTION'}")
        logger.info(f"Database: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        
        try:
            # Step 1: Connect to database
            if not self.connect_database():
                return False
            
            # Step 2: Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Step 3: Deploy wallet schema
            logger.info("Deploying wallet schema...")
            
            success, error = self.execute_sql_file(
                self.schema_file, 
                "Wallet schema with all tables, indexes, and triggers", 
                600  # 10 minutes timeout
            )
            
            if not success:
                logger.error(f"Wallet schema deployment failed: {error}")
                return False
            
            # Step 4: Run health checks
            if not self.run_health_checks():
                logger.error("Health checks failed")
                return False
            
            # Step 5: Create deployment summary
            summary = self.create_deployment_summary()
            summary['status'] = 'success'
            
            # Save deployment summary
            summary_file = f"wallet_schema_deployment_summary_{int(self.deployment_start.timestamp())}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            logger.info(f"Deployment summary saved to: {summary_file}")
            
            deployment_time = datetime.now() - self.deployment_start
            logger.info("="*80)
            logger.info("WALLET SCHEMA DEPLOYMENT COMPLETED SUCCESSFULLY")
            logger.info(f"Total time: {deployment_time.total_seconds():.2f} seconds")
            logger.info("="*80)
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed with unexpected error: {e}")
            return False
        
        finally:
            if self.connection:
                self.connection.close()
                logger.info("Database connection closed")
    
    def rollback(self) -> bool:
        """Execute rollback of the wallet schema."""
        logger.warning("="*80)
        logger.warning("BETBET WALLET SCHEMA ROLLBACK")
        logger.warning("="*80)
        
        if not self.confirm_continue("Are you sure you want to rollback the Wallet schema? This will remove all wallet data."):
            logger.info("Rollback cancelled by user")
            return False
        
        try:
            if not self.connect_database():
                return False
            
            with self.connection.cursor() as cursor:
                # Drop wallet schema and all its contents
                logger.info("Dropping wallet schema...")
                cursor.execute("DROP SCHEMA IF EXISTS wallet CASCADE;")
                logger.info("✓ Wallet schema rollback completed successfully")
                return True
                
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return False
        
        finally:
            if self.connection:
                self.connection.close()

def main():
    """Main deployment script entry point."""
    parser = argparse.ArgumentParser(description='Deploy BetBet Wallet Schema')
    
    # Database connection arguments
    parser.add_argument('--host', default=os.getenv('DB_HOST', 'localhost'), help='Database host')
    parser.add_argument('--port', default=os.getenv('DB_PORT', '5432'), help='Database port')
    parser.add_argument('--database', default=os.getenv('DB_NAME', 'betbet_db'), help='Database name')
    parser.add_argument('--user', default=os.getenv('DB_USER', 'postgres'), help='Database user')
    parser.add_argument('--password', default=os.getenv('DB_PASSWORD', ''), help='Database password')
    
    # Deployment options
    parser.add_argument('--dry-run', action='store_true', help='Simulate deployment without executing')
    parser.add_argument('--rollback', action='store_true', help='Rollback the deployment')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Prepare database configuration
    db_config = {
        'host': args.host,
        'port': args.port,
        'database': args.database,
        'user': args.user,
        'password': args.password or os.getenv('DB_PASSWORD', '')
    }
    
    # Validate configuration
    if not db_config['password']:
        logger.error("Database password is required. Set DB_PASSWORD environment variable or use --password")
        sys.exit(1)
    
    # Create deployment manager
    deployment = WalletSchemaDeployment(db_config, dry_run=args.dry_run)
    
    try:
        if args.rollback:
            # Execute rollback
            success = deployment.rollback()
        else:
            # Execute deployment
            success = deployment.deploy()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.warning("Deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Deployment script failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()