#!/usr/bin/env python3
"""
BetBet Platform - Sports Analysis & AI Chat Module Database Deployment Script
=============================================================================

This script deploys the complete Sports Analysis & AI Chat module database
following the BetBet enterprise deployment patterns.

Components deployed:
1. Main schema (sports_analysis.sql)
2. Performance indexes (sports_analysis_indexes.sql) 
3. Security policies (sports_analysis_security.sql)
4. Migration tracking (004_create_sports_analysis.sql)

Author: Claude-DB
Date: 2025-01-21
Template Compliance: Gaming Engine deployment patterns
"""

import os
import sys
import json
import time
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sports_analysis_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SportsAnalysisDeployment:
    """
    Sports Analysis & AI Chat module database deployment manager.
    Handles complete schema deployment with rollback capabilities.
    """
    
    def __init__(self, db_config: Dict[str, str], dry_run: bool = False):
        """Initialize deployment manager."""
        self.db_config = db_config
        self.dry_run = dry_run
        self.deployment_start = datetime.now()
        
        # File paths
        self.base_path = Path(__file__).parent.parent.parent
        self.schema_path = self.base_path / "database" / "schemas"
        self.migration_path = self.base_path / "database" / "migrations"
        
        # Deployment files in dependency order
        self.deployment_files = [
            {
                'file': self.migration_path / "004_create_sports_analysis.sql",
                'description': 'Migration framework and template tables',
                'critical': True,
                'timeout': 300  # 5 minutes
            },
            {
                'file': self.schema_path / "sports_analysis.sql", 
                'description': 'Main schema with all business tables',
                'critical': True,
                'timeout': 600  # 10 minutes
            },
            {
                'file': self.schema_path / "sports_analysis_indexes.sql",
                'description': 'Performance-optimized indexes',
                'critical': True,
                'timeout': 1800  # 30 minutes
            },
            {
                'file': self.schema_path / "sports_analysis_security.sql",
                'description': 'Row Level Security policies',
                'critical': False,
                'timeout': 300  # 5 minutes
            }
        ]
        
        self.connection = None
        self.deployment_log = []
    
    def connect_database(self) -> bool:
        """Establish database connection with proper error handling."""
        try:
            logger.info("Connecting to database...")
            
            # Create connection string
            conn_string = (
                f"host={self.db_config['host']} "
                f"port={self.db_config['port']} "
                f"dbname={self.db_config['database']} "
                f"user={self.db_config['user']} "
                f"password={self.db_config['password']}"
            )
            
            self.connection = psycopg2.connect(conn_string)
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            # Test connection
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                logger.info(f"Connected to PostgreSQL: {version}")
            
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met for deployment."""
        logger.info("Checking deployment prerequisites...")
        
        try:
            with self.connection.cursor() as cursor:
                # Check PostgreSQL version (minimum 12.0)
                cursor.execute("SELECT version();")
                version_str = cursor.fetchone()[0]
                if not any(v in version_str for v in ['PostgreSQL 12', 'PostgreSQL 13', 'PostgreSQL 14', 'PostgreSQL 15', 'PostgreSQL 16']):
                    logger.warning(f"PostgreSQL version may not be fully supported: {version_str}")
                
                # Check required extensions availability
                required_extensions = ['uuid-ossp', 'pgcrypto', 'pgvector', 'pg_trgm']
                for ext in required_extensions:
                    cursor.execute("""
                        SELECT 1 FROM pg_available_extensions 
                        WHERE name = %s;
                    """, (ext,))
                    
                    if cursor.fetchone():
                        logger.info(f"✓ Extension {ext} is available")
                    else:
                        if ext == 'pgvector':
                            logger.warning(f"⚠ Extension {ext} not available - RAG features will be limited")
                        else:
                            logger.error(f"✗ Required extension {ext} not available")
                            return False
                
                # Check if public.users table exists
                cursor.execute("""
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = 'users';
                """)
                
                if cursor.fetchone():
                    logger.info("✓ Public users table exists")
                else:
                    logger.error("✗ Required table public.users does not exist")
                    return False
                
                # Check if migrations table exists
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS public.migrations (
                        id SERIAL PRIMARY KEY,
                        version VARCHAR(10) UNIQUE NOT NULL,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        applied_by VARCHAR(100),
                        completed_at TIMESTAMP WITH TIME ZONE,
                        status VARCHAR(20) DEFAULT 'applied',
                        health_check_results JSONB
                    );
                """)
                logger.info("✓ Migrations table ready")
                
                # Check if schema already exists
                cursor.execute("""
                    SELECT 1 FROM information_schema.schemata 
                    WHERE schema_name = 'sports_analysis';
                """)
                
                if cursor.fetchone():
                    logger.warning("⚠ Schema 'sports_analysis' already exists - this may be a re-deployment")
                    
                    # Check existing migration
                    cursor.execute("""
                        SELECT status, applied_at FROM public.migrations 
                        WHERE version = '004';
                    """)
                    migration = cursor.fetchone()
                    if migration:
                        logger.warning(f"⚠ Migration 004 already exists with status: {migration[0]}")
                        return self.confirm_continue("Continue with re-deployment?")
                
                logger.info("✓ All prerequisites met")
                return True
                
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            return False
    
    def confirm_continue(self, message: str) -> bool:
        """Ask for user confirmation if not in automated mode."""
        if self.dry_run:
            logger.info(f"DRY RUN: Would ask - {message}")
            return True
        
        if os.getenv('AUTOMATED_DEPLOYMENT') == 'true':
            logger.info(f"Automated deployment - proceeding with: {message}")
            return True
        
        response = input(f"{message} (y/N): ").lower().strip()
        return response in ['y', 'yes']
    
    def execute_sql_file(self, file_path: Path, description: str, timeout: int = 300) -> Tuple[bool, Optional[str]]:
        """Execute a SQL file with timeout and error handling."""
        logger.info(f"Executing: {description}")
        
        if not file_path.exists():
            error_msg = f"SQL file not found: {file_path}"
            logger.error(error_msg)
            return False, error_msg
        
        if self.dry_run:
            logger.info(f"DRY RUN: Would execute {file_path}")
            return True, None
        
        start_time = time.time()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            logger.info(f"File size: {len(sql_content)} characters")
            
            # Execute with timeout
            with self.connection.cursor() as cursor:
                logger.info(f"Executing SQL (timeout: {timeout}s)...")
                cursor.execute(sql_content)
                
                # Log any notices or warnings
                if cursor.connection.notices:
                    for notice in cursor.connection.notices:
                        logger.info(f"NOTICE: {notice.strip()}")
                    cursor.connection.notices.clear()
            
            execution_time = time.time() - start_time
            logger.info(f"✓ Completed in {execution_time:.2f}s: {description}")
            
            return True, None
            
        except psycopg2.Error as e:
            execution_time = time.time() - start_time  
            error_msg = f"SQL execution failed after {execution_time:.2f}s: {e}"
            logger.error(error_msg)
            return False, error_msg
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Unexpected error after {execution_time:.2f}s: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def run_health_checks(self) -> bool:
        """Run comprehensive health checks after deployment."""
        logger.info("Running post-deployment health checks...")
        
        if self.dry_run:
            logger.info("DRY RUN: Would run health checks")
            return True
        
        try:
            with self.connection.cursor() as cursor:
                # Run the migration health check function
                cursor.execute("""
                    SELECT check_name, status, details 
                    FROM sports_analysis.migration_health_check();
                """)
                
                checks = cursor.fetchall()
                healthy_count = 0
                total_count = len(checks)
                
                logger.info(f"Health Check Results ({total_count} checks):")
                for check_name, status, details in checks:
                    status_icon = "✓" if status == "healthy" else "⚠" if status == "warning" else "✗"
                    logger.info(f"  {status_icon} {check_name}: {details}")
                    
                    if status in ["healthy", "warning"]:
                        healthy_count += 1
                
                # Additional custom checks
                self.run_custom_health_checks(cursor)
                
                success_rate = (healthy_count / total_count) * 100 if total_count > 0 else 0
                logger.info(f"Health check success rate: {success_rate:.1f}% ({healthy_count}/{total_count})")
                
                return success_rate >= 80  # 80% success rate required
                
        except Exception as e:
            logger.error(f"Health checks failed: {e}")
            return False
    
    def run_custom_health_checks(self, cursor) -> None:
        """Run additional custom health checks."""
        try:
            # Check table counts
            cursor.execute("""
                SELECT schemaname, COUNT(*) as table_count
                FROM pg_tables 
                WHERE schemaname = 'sports_analysis'
                GROUP BY schemaname;
            """)
            result = cursor.fetchone()
            if result:
                logger.info(f"✓ Schema tables: {result[1]} tables in sports_analysis schema")
            else:
                logger.warning("⚠ No tables found in sports_analysis schema")
            
            # Check indexes
            cursor.execute("""
                SELECT COUNT(*) 
                FROM pg_indexes 
                WHERE schemaname = 'sports_analysis';
            """)
            index_count = cursor.fetchone()[0]
            logger.info(f"✓ Performance indexes: {index_count} indexes created")
            
            # Check functions
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.routines 
                WHERE routine_schema = 'sports_analysis';
            """)
            function_count = cursor.fetchone()[0]
            logger.info(f"✓ Functions: {function_count} functions available")
            
            # Check if vector extension is working (if available)
            try:
                cursor.execute("SELECT 1 FROM pg_extension WHERE extname = 'pgvector';")
                if cursor.fetchone():
                    cursor.execute("SELECT '[1,2,3]'::vector;")
                    logger.info("✓ Vector extension: pgvector working correctly")
            except:
                logger.warning("⚠ Vector extension: pgvector not fully functional")
                
        except Exception as e:
            logger.warning(f"Custom health checks encountered issues: {e}")
    
    def create_deployment_summary(self) -> Dict:
        """Create a summary of the deployment for logging."""
        deployment_time = datetime.now() - self.deployment_start
        
        summary = {
            'module': 'Sports Analysis & AI Chat',
            'deployment_id': f"sports_analysis_{int(self.deployment_start.timestamp())}",
            'start_time': self.deployment_start.isoformat(),
            'duration_seconds': deployment_time.total_seconds(),
            'dry_run': self.dry_run,
            'database': {
                'host': self.db_config['host'],
                'port': self.db_config['port'],
                'database': self.db_config['database']
            },
            'files_deployed': [],
            'status': 'unknown'
        }
        
        # Add file deployment details
        for entry in self.deployment_log:
            summary['files_deployed'].append({
                'file': entry['file'],
                'description': entry['description'],
                'success': entry['success'],
                'error': entry.get('error'),
                'execution_time': entry.get('execution_time', 0)
            })
        
        return summary
    
    def deploy(self) -> bool:
        """Execute the complete deployment process."""
        logger.info("="*80)
        logger.info("BETBET SPORTS ANALYSIS & AI CHAT MODULE DEPLOYMENT")
        logger.info("="*80)
        logger.info(f"Start time: {self.deployment_start}")
        logger.info(f"Mode: {'DRY RUN' if self.dry_run else 'PRODUCTION'}")
        logger.info(f"Database: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        
        try:
            # Step 1: Connect to database
            if not self.connect_database():
                return False
            
            # Step 2: Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Step 3: Deploy files in order
            logger.info(f"Deploying {len(self.deployment_files)} components...")
            
            for i, file_config in enumerate(self.deployment_files, 1):
                file_path = file_config['file']
                description = file_config['description']
                timeout = file_config['timeout']
                critical = file_config['critical']
                
                logger.info(f"[{i}/{len(self.deployment_files)}] {description}")
                
                success, error = self.execute_sql_file(file_path, description, timeout)
                
                # Log the deployment step
                log_entry = {
                    'step': i,
                    'file': str(file_path),
                    'description': description,
                    'success': success,
                    'critical': critical
                }
                
                if error:
                    log_entry['error'] = error
                
                self.deployment_log.append(log_entry)
                
                if not success:
                    if critical:
                        logger.error(f"Critical deployment step failed: {description}")
                        return False
                    else:
                        logger.warning(f"Non-critical deployment step failed: {description}")
            
            # Step 4: Run health checks
            if not self.run_health_checks():
                logger.error("Health checks failed")
                return False
            
            # Step 5: Create deployment summary
            summary = self.create_deployment_summary()
            summary['status'] = 'success'
            
            # Save deployment summary
            summary_file = f"sports_analysis_deployment_summary_{int(self.deployment_start.timestamp())}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            logger.info(f"Deployment summary saved to: {summary_file}")
            
            deployment_time = datetime.now() - self.deployment_start
            logger.info("="*80)
            logger.info("DEPLOYMENT COMPLETED SUCCESSFULLY")
            logger.info(f"Total time: {deployment_time.total_seconds():.2f} seconds")
            logger.info("="*80)
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed with unexpected error: {e}")
            return False
        
        finally:
            if self.connection:
                self.connection.close()
                logger.info("Database connection closed")
    
    def rollback(self) -> bool:
        """Execute rollback of the sports analysis module."""
        logger.warning("="*80)
        logger.warning("BETBET SPORTS ANALYSIS MODULE ROLLBACK")
        logger.warning("="*80)
        
        if not self.confirm_continue("Are you sure you want to rollback the Sports Analysis module? This will remove all data."):
            logger.info("Rollback cancelled by user")
            return False
        
        try:
            if not self.connect_database():
                return False
            
            with self.connection.cursor() as cursor:
                # Execute the rollback function
                cursor.execute("SELECT sports_analysis.rollback_migration_004();")
                logger.info("✓ Rollback completed successfully")
                return True
                
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return False
        
        finally:
            if self.connection:
                self.connection.close()

def main():
    """Main deployment script entry point."""
    parser = argparse.ArgumentParser(description='Deploy BetBet Sports Analysis & AI Chat Module')
    
    # Database connection arguments
    parser.add_argument('--host', default=os.getenv('DB_HOST', 'localhost'), help='Database host')
    parser.add_argument('--port', default=os.getenv('DB_PORT', '5432'), help='Database port')
    parser.add_argument('--database', default=os.getenv('DB_NAME', 'betbet'), help='Database name')
    parser.add_argument('--user', default=os.getenv('DB_USER', 'postgres'), help='Database user')
    parser.add_argument('--password', default=os.getenv('DB_PASSWORD', ''), help='Database password')
    
    # Deployment options
    parser.add_argument('--dry-run', action='store_true', help='Simulate deployment without executing')
    parser.add_argument('--rollback', action='store_true', help='Rollback the deployment')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Prepare database configuration
    db_config = {
        'host': args.host,
        'port': args.port,
        'database': args.database,
        'user': args.user,
        'password': args.password or os.getenv('DB_PASSWORD', '')
    }
    
    # Validate configuration
    if not db_config['password']:
        logger.error("Database password is required. Set DB_PASSWORD environment variable or use --password")
        sys.exit(1)
    
    # Create deployment manager
    deployment = SportsAnalysisDeployment(db_config, dry_run=args.dry_run)
    
    try:
        if args.rollback:
            # Execute rollback
            success = deployment.rollback()
        else:
            # Execute deployment
            success = deployment.deploy()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.warning("Deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Deployment script failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()