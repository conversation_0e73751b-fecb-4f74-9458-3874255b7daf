#!/bin/bash

# BetBet Platform - Production Deployment Script
# This script deploys the BetBet platform in production mode with security checks

set -euo pipefail  # Exit on error, undefined variables, pipe failures

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.production"
BACKUP_DIR="/var/backups/betbet"
LOG_FILE="/var/log/betbet-deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}" | tee -a "$LOG_FILE"
}

# Security checks
check_security() {
    log "Running security checks..."
    
    # Check if production environment file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Production environment file $ENV_FILE not found. Copy from .env.production.example"
    fi
    
    # Check for default/weak passwords
    local weak_patterns=("admin123" "password" "123456" "change_me" "REPLACE_WITH")
    for pattern in "${weak_patterns[@]}"; do
        if grep -q "$pattern" "$ENV_FILE"; then
            error "Weak/default password detected in $ENV_FILE. Please use secure credentials."
        fi
    done
    
    # Check JWT secret length
    local jwt_secret
    jwt_secret=$(grep "JWT_SECRET_KEY=" "$ENV_FILE" | cut -d'=' -f2)
    if [[ ${#jwt_secret} -lt 64 ]]; then
        error "JWT secret is too short. Must be at least 64 characters."
    fi
    
    # Check if .env file is in gitignore
    if ! grep -q "^\.env$" .gitignore 2>/dev/null; then
        warning "Ensure .env files are in .gitignore to prevent credential leaks"
    fi
    
    success "Security checks passed"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if docker and docker-compose are available
    command -v docker >/dev/null 2>&1 || error "Docker is not installed"
    command -v docker-compose >/dev/null 2>&1 || error "Docker Compose is not installed"
    
    # Check if compose file exists
    [[ -f "$COMPOSE_FILE" ]] || error "Docker Compose file $COMPOSE_FILE not found"
    
    # Check if all Dockerfile.prod files exist
    local services=("api-gateway" "websocket-manager" "gaming-engine" "betting-service" "odds-exchange" "expert-analysis" "sports-analysis" "leaderboards-service")
    for service in "${services[@]}"; do
        if [[ ! -f "services/$service/Dockerfile.prod" ]]; then
            error "Production Dockerfile not found for service: $service"
        fi
    done
    
    # Check frontend Dockerfiles
    [[ -f "frontend/web/Dockerfile.prod" ]] || error "Web frontend production Dockerfile not found"
    [[ -f "frontend/admin/Dockerfile.prod" ]] || error "Admin frontend production Dockerfile not found"
    
    success "Pre-deployment checks passed"
}

# Backup current data
backup_data() {
    log "Creating backup..."
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if running
    if docker ps | grep -q "betbet-postgres"; then
        local backup_file="$BACKUP_DIR/postgres-backup-$(date +%Y%m%d_%H%M%S).sql"
        docker exec betbet-postgres pg_dumpall -U betbet_user > "$backup_file"
        success "Database backup created: $backup_file"
    fi
    
    # Backup Redis if running
    if docker ps | grep -q "betbet-redis"; then
        local redis_backup="$BACKUP_DIR/redis-backup-$(date +%Y%m%d_%H%M%S).rdb"
        docker exec betbet-redis redis-cli --rdb "$redis_backup"
        success "Redis backup created: $redis_backup"
    fi
}

# Deploy application
deploy() {
    log "Deploying BetBet platform in production mode..."
    
    # Pull latest images (if using registry)
    # docker-compose -f "$COMPOSE_FILE" pull
    
    # Build production images
    log "Building production images..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    
    # Start services with health checks
    log "Starting production services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        local healthy_services
        healthy_services=$(docker-compose -f "$COMPOSE_FILE" ps | grep "healthy" | wc -l)
        local total_services
        total_services=$(docker-compose -f "$COMPOSE_FILE" ps | grep -E "(up|healthy)" | wc -l)
        
        if [[ $healthy_services -eq $total_services ]] && [[ $total_services -gt 0 ]]; then
            success "All services are healthy"
            break
        fi
        
        log "Waiting for services... ($((attempt + 1))/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -eq $max_attempts ]]; then
        error "Services did not become healthy within expected time"
    fi
}

# Post-deployment verification
verify_deployment() {
    log "Verifying deployment..."
    
    # Check service endpoints
    local endpoints=(
        "http://localhost:8000/health"
        "http://localhost:8080/health"
        "http://localhost:3000"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f -s "$endpoint" > /dev/null; then
            success "✓ $endpoint is responding"
        else
            error "✗ $endpoint is not responding"
        fi
    done
    
    # Check container status
    log "Container status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    success "Deployment verification completed"
}

# Cleanup old images
cleanup() {
    log "Cleaning up old Docker images..."
    docker image prune -f
    docker volume prune -f
    success "Cleanup completed"
}

# Main execution
main() {
    log "Starting BetBet production deployment..."
    
    # Check if running as root in production
    if [[ $EUID -eq 0 ]] && [[ "${ALLOW_ROOT:-}" != "true" ]]; then
        warning "Running as root. Consider using a dedicated user for production."
    fi
    
    check_security
    pre_deployment_checks
    backup_data
    deploy
    verify_deployment
    cleanup
    
    success "BetBet platform deployed successfully!"
    log "Access URLs:"
    log "  Web App: http://localhost:3000"
    log "  Admin Panel: http://localhost:3001 (localhost only)"
    log "  API Gateway: http://localhost:8000"
    log ""
    log "Remember to:"
    log "  - Configure reverse proxy (Nginx/Apache) for HTTPS"
    log "  - Set up monitoring and log aggregation"
    log "  - Configure automated backups"
    log "  - Review security settings"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy"|"")
        main
        ;;
    "check")
        check_security
        pre_deployment_checks
        ;;
    "backup")
        backup_data
        ;;
    "verify")
        verify_deployment
        ;;
    *)
        echo "Usage: $0 [deploy|check|backup|verify]"
        echo "  deploy  - Full deployment (default)"
        echo "  check   - Run security and pre-deployment checks"
        echo "  backup  - Create backup of current data"
        echo "  verify  - Verify current deployment"
        exit 1
        ;;
esac