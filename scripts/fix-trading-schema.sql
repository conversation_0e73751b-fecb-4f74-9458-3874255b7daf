-- Fix Trading Service Database Schema
-- ===================================
-- 
-- This script fixes the mismatch between the trading service code
-- and the database schema by adding missing columns and renaming existing ones.

\c betbet_db;

-- Connect to the trading database schema
SET search_path TO odds_exchange;

-- Add missing columns to markets table
ALTER TABLE odds_exchange.markets 
ADD COLUMN IF NOT EXISTS event_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS title VARCHAR(500),
ADD COLUMN IF NOT EXISTS total_matched DECIMAL(15,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS closes_at TIMESTAMP WITH TIME ZONE;

-- Update existing data to populate new columns
UPDATE odds_exchange.markets 
SET 
    title = name,
    event_id = COALESCE(category, 'default-event'),
    total_matched = 0,
    closes_at = settlement_date
WHERE title IS NULL;

-- Create some sample markets for testing
INSERT INTO odds_exchange.markets (
    event_id, 
    market_type, 
    title, 
    status, 
    total_matched, 
    total_volume, 
    created_at, 
    closes_at
) VALUES 
(
    'sports-001', 
    'match_winner', 
    'Manchester United vs Liverpool - Match Winner', 
    'active', 
    15000.00, 
    25000.00, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP + INTERVAL '7 days'
),
(
    'sports-002', 
    'total_goals', 
    'Chelsea vs Arsenal - Total Goals Over/Under 2.5', 
    'active', 
    8500.00, 
    12000.00, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP + INTERVAL '5 days'
),
(
    'crypto-001', 
    'price_prediction', 
    'Bitcoin Price Above $100k by End of Year', 
    'active', 
    45000.00, 
    67000.00, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP + INTERVAL '30 days'
),
(
    'politics-001', 
    'election_outcome', 
    'US Presidential Election 2024 Winner', 
    'active', 
    125000.00, 
    200000.00, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP + INTERVAL '90 days'
),
(
    'esports-001', 
    'tournament_winner', 
    'League of Legends World Championship Winner', 
    'active', 
    32000.00, 
    48000.00, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP + INTERVAL '14 days'
)
ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_markets_event_id ON odds_exchange.markets(event_id);
CREATE INDEX IF NOT EXISTS idx_markets_status ON odds_exchange.markets(status);
CREATE INDEX IF NOT EXISTS idx_markets_total_volume ON odds_exchange.markets(total_volume DESC);
CREATE INDEX IF NOT EXISTS idx_markets_closes_at ON odds_exchange.markets(closes_at);

-- Update the orders table to ensure it references the correct market structure
-- (The orders table should already be compatible)

COMMIT;

-- Verify the schema
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'odds_exchange' 
  AND table_name = 'markets'
ORDER BY ordinal_position;
