# BetBet Platform - Production Deployment Pipeline
# ================================================
# Enterprise-grade CI/CD with quality gates and security scanning

name: Production Deployment

on:
  push:
    branches:
      - main
    tags:
      - 'v*'
  pull_request:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Quality Gates - Code Quality
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/web/package-lock.json
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install Frontend Dependencies
        working-directory: frontend/web
        run: npm ci
      
      - name: Install Python Dependencies
        working-directory: services/gaming-engine
        run: |
          pip install -r requirements.txt
          pip install black isort flake8 mypy
      
      - name: Frontend Linting
        working-directory: frontend/web
        run: |
          npm run lint
          npm run type-check
      
      - name: Python Code Quality
        working-directory: services/gaming-engine
        run: |
          black --check .
          isort --check-only .
          flake8 .
          mypy app/

  # Quality Gates - Testing
  testing:
    name: Comprehensive Testing
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: betbet_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/web/package-lock.json
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install Frontend Dependencies
        working-directory: frontend/web
        run: npm ci
      
      - name: Install Python Dependencies
        working-directory: services/gaming-engine
        run: pip install -r requirements.txt
      
      - name: Frontend Tests
        working-directory: frontend/web
        run: |
          npm run test -- --coverage --watchAll=false
          npm run test:performance
          npm run test:security
        env:
          CI: true
      
      - name: Backend Tests
        working-directory: services/gaming-engine
        run: |
          pytest --cov=app --cov-report=xml --cov-report=html
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/betbet_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_secret_key_for_ci
      
      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          files: ./frontend/web/coverage/lcov.info,./services/gaming-engine/coverage.xml
          flags: unittests
          name: betbet-coverage
          fail_ci_if_error: true

  # Quality Gates - Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/web/package-lock.json
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install Dependencies
        run: |
          cd frontend/web && npm ci
          cd ../../services/gaming-engine && pip install -r requirements.txt
      
      - name: Frontend Security Audit
        working-directory: frontend/web
        run: |
          npm audit --audit-level high
          npx audit-ci --high
      
      - name: Python Security Scan
        working-directory: services/gaming-engine
        run: |
          pip install bandit safety
          bandit -r app/ -f json -o bandit-report.json
          safety check --json --output safety-report.json
      
      - name: SAST with CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: javascript,python
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
      
      - name: Upload Security Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            services/gaming-engine/bandit-report.json
            services/gaming-engine/safety-report.json

  # Quality Gates - Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [testing, security]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: betbet_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Build and Start Services
        run: |
          docker-compose -f docker-compose.yml up -d --build
          sleep 30  # Wait for services to be ready
      
      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
      
      - name: Run Performance Tests
        run: |
          k6 run tests/performance/load_tests/api_load_test.js
          k6 run tests/performance/stress_tests/stress_test.js
      
      - name: Upload Performance Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-reports
          path: performance-results/

  # Build and Push Container Images
  build-images:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: [testing, security, performance]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v'))
    
    strategy:
      matrix:
        service:
          - name: frontend
            context: ./frontend/web
            dockerfile: Dockerfile.production
          - name: gaming-engine
            context: ./services/gaming-engine
            dockerfile: Dockerfile.production
          - name: websocket-manager
            context: ./services/websocket-manager
            dockerfile: Dockerfile.production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service.name }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
      
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.service.context }}
          file: ${{ matrix.service.context }}/${{ matrix.service.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Production Deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://betbet.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: Configure Kubernetes
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
      
      - name: Deploy to Production
        run: |
          export KUBECONFIG=kubeconfig
          kubectl set image deployment/frontend frontend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }}
          kubectl set image deployment/gaming-engine gaming-engine=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/gaming-engine:${{ github.sha }}
          kubectl set image deployment/websocket-manager websocket-manager=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/websocket-manager:${{ github.sha }}
          kubectl rollout status deployment/frontend
          kubectl rollout status deployment/gaming-engine
          kubectl rollout status deployment/websocket-manager
      
      - name: Run Post-Deployment Tests
        run: |
          export KUBECONFIG=kubeconfig
          kubectl apply -f tests/e2e/k8s-test-job.yaml
          kubectl wait --for=condition=complete job/post-deployment-tests --timeout=300s
      
      - name: Cleanup
        if: always()
        run: rm -f kubeconfig

  # Notification
  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: deploy-production
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.deploy-production.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: 'Production deployment successful! 🚀'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      
      - name: Notify Failure
        if: needs.deploy-production.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: 'Production deployment failed! 🚨'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}