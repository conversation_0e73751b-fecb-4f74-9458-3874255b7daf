#!/bin/bash

# BetBet Platform - Railway Deployment Script
# ==========================================

set -e  # Exit on any error

echo "🚀 BetBet Platform Railway Deployment"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}❌ Railway CLI not found. Please install it first:"
    echo -e "${YELLOW}npm install -g @railway/cli${NC}"
    exit 1
fi

# Check if user is logged in
if ! railway whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  Please log in to Railway first:"
    echo -e "${BLUE}railway login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Railway CLI ready${NC}"

# Function to deploy a service
deploy_service() {
    local service_name=$1
    local service_path=$2
    local port=$3
    
    echo -e "\n${BLUE}📦 Deploying $service_name...${NC}"
    
    if [ -d "$service_path" ]; then
        cd "$service_path"
        
        # Set environment variables
        echo "Setting environment variables for $service_name..."
        railway variables set PORT=$port
        railway variables set DATABASE_URL=$DATABASE_URL
        railway variables set CORS_ORIGINS='["https://'$FRONTEND_URL'","https://'$API_GATEWAY_URL'"]'
        
        # Deploy
        railway up --detach
        
        echo -e "${GREEN}✅ $service_name deployed${NC}"
        cd - > /dev/null
    else
        echo -e "${RED}❌ Service path not found: $service_path${NC}"
    fi
}

# Get database URL
echo -e "\n${BLUE}🗄️  Setting up database...${NC}"
echo "Please ensure you have a PostgreSQL database addon in your Railway project."
echo "Copy your DATABASE_URL from Railway dashboard:"
read -p "DATABASE_URL: " DATABASE_URL

if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ DATABASE_URL is required${NC}"
    exit 1
fi

# Get domain URLs (will be used for CORS)
echo -e "\n${BLUE}🌐 Domain configuration...${NC}"
echo "These will be set automatically after deployment, but you can customize:"
read -p "Frontend domain (leave empty for auto): " FRONTEND_URL
read -p "API Gateway domain (leave empty for auto): " API_GATEWAY_URL

# Use defaults if empty
FRONTEND_URL=${FRONTEND_URL:-"betbet-frontend-production.up.railway.app"}
API_GATEWAY_URL=${API_GATEWAY_URL:-"betbet-api-gateway-production.up.railway.app"}

echo -e "\n${YELLOW}📋 Deployment Plan:${NC}"
echo "1. API Gateway (port 8000)"
echo "2. Gaming Engine (port 8001)"
echo "3. Custom Betting (port 8002)"
echo "4. Odds Exchange (port 8003)"
echo "5. Expert Analysis (port 8004)"
echo "6. Sports Analysis (port 8005)"
echo "7. Leaderboards (port 8006)"
echo "8. Frontend (port 3000)"

read -p "Continue with deployment? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Deploy services
echo -e "\n${GREEN}🚀 Starting deployment...${NC}"

# Deploy API Gateway first (other services depend on it)
deploy_service "API Gateway" "services/api-gateway" 8000

# Deploy backend services
deploy_service "Gaming Engine" "services/gaming-engine" 8001
deploy_service "Custom Betting" "services/custom-betting" 8002
deploy_service "Odds Exchange" "services/odds-exchange" 8003
deploy_service "Expert Analysis" "services/expert-analysts" 8004
deploy_service "Sports Analysis" "services/sports-analysis" 8005
deploy_service "Leaderboards" "services/leaderboards" 8006

# Deploy frontend last
echo -e "\n${BLUE}📦 Deploying Frontend...${NC}"
cd frontend/web

# Set frontend environment variables
railway variables set NODE_ENV=production
railway variables set NEXT_PUBLIC_API_BASE_URL="https://$API_GATEWAY_URL"
railway variables set NEXT_PUBLIC_WS_URL="wss://$API_GATEWAY_URL"
railway variables set PORT=3000

# Deploy frontend
railway up --detach

cd - > /dev/null

echo -e "\n${GREEN}🎉 Deployment completed!${NC}"
echo -e "\n${BLUE}📍 Your services should be available at:${NC}"
echo "• Frontend: https://$FRONTEND_URL"
echo "• API Gateway: https://$API_GATEWAY_URL"
echo ""
echo -e "${YELLOW}⚠️  Next steps:${NC}"
echo "1. Update Clerk authentication URLs in your Clerk dashboard"
echo "2. Test the deployment by visiting your frontend URL"
echo "3. Check service health at https://$API_GATEWAY_URL/health"
echo ""
echo -e "${BLUE}📊 Monitor your deployment:${NC}"
echo "railway logs"
echo "railway status"