#!/bin/bash

# BetBet Leaderboards Setup Script
# ================================
# Sets up the complete leaderboards system with real data

echo "🚀 Setting up BetBet Leaderboards with Real Data..."

# Build and start the user sync service
echo "📦 Building user sync service..."
docker-compose build user-sync

# Build and start the leaderboards service
echo "📦 Building leaderboards service..."
docker-compose build leaderboards-service

# Start the services
echo "🔄 Starting services..."
docker-compose up -d user-sync leaderboards-service

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
docker-compose ps user-sync leaderboards-service

# Sync Clerk users to platform database
echo "👥 Syncing Clerk users to platform database..."
curl -X POST http://localhost:8005/api/v1/sync/users || echo "User sync failed - continuing anyway"

# Wait a bit for sync to complete
sleep 5

# Populate leaderboard data
echo "📊 Populating leaderboard data..."
docker exec betbet-user-sync python populate_leaderboards.py || echo "Data population failed - continuing anyway"

# Check sync status
echo "📈 Checking sync status..."
curl -s http://localhost:8005/api/v1/sync/status | jq '.' || echo "Status check failed"

# Test leaderboard API
echo "🧪 Testing leaderboard API..."
curl -s http://localhost:8006/api/v1/leaderboards/unified-leaderboards?limit=5 | jq '.data.leaderboard[0:2]' || echo "API test failed"

echo "✅ Leaderboards setup complete!"
echo ""
echo "🌐 Services available at:"
echo "   - User Sync Service: http://localhost:8005"
echo "   - Leaderboards Service: http://localhost:8006"
echo "   - Frontend: http://localhost:3000/leaderboard"
echo ""
echo "📋 Next steps:"
echo "   1. Visit http://localhost:3000/leaderboard to see the leaderboards"
echo "   2. Sign in with Clerk to see your personal performance"
echo "   3. Check the leaderboard components are populated with real data"
