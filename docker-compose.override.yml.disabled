# Docker Compose Override for Containerized Database
# ==================================================
# This configuration ensures all services use the containerized PostgreSQL
# while providing external access for development tools

services:
  # Ensure all services use containerized PostgreSQL
  db-migrations:
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432

  api-gateway:
    environment:
      - DATABASE_URL=*************************************************/betbet_db

  websocket-manager:
    environment:
      - DATABASE_URL=*************************************************/betbet_db

  gaming-engine:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  custom-betting:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  odds-exchange:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  expert-analysis:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  sports-analysis:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  leaderboards-service:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db

  wallet-service:
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db