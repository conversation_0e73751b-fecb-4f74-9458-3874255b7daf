# BetBet Schema Consolidation Report
==================================================

## Duplicates Found:
### 02-core-schema
- [KEEP] database/init/02-core-schema.sql
- [REMOVE] services/db-migrations/02-core-schema.sql
- [REMOVE] services/db-migrations/02-core-schema.sql

### 04-betting-schema.sql
- [KEEP] database/init/04-betting-schema.sql
- [REMOVE] services/db-migrations/04-betting-schema.sql
- [REMOVE] services/db-migrations/04-betting-schema.sql

### 07-sports-schema.sql
- [KEEP] database/init/07-sports-schema.sql
- [REMOVE] services/db-migrations/07-sports-schema.sql
- [REMOVE] services/db-migrations/07-sports-schema.sql

### 06-experts-schema.sql
- [KEEP] database/init/06-experts-schema.sql
- [REMOVE] services/db-migrations/06-experts-schema.sql
- [REMOVE] services/db-migrations/06-experts-schema.sql

### 08-leaderboards-schema.sql
- [KEEP] database/init/08-leaderboards-schema.sql
- [REMOVE] services/db-migrations/08-leaderboards-schema.sql
- [REMOVE] services/db-migrations/08-leaderboards-schema.sql

### 03-gaming-schema.sql
- [KEEP] database/init/03-gaming-schema.sql
- [REMOVE] services/db-migrations/03-gaming-schema.sql
- [REMOVE] services/db-migrations/03-gaming-schema.sql

### 05-trading-schema.sql
- [KEEP] database/init/05-trading-schema.sql
- [REMOVE] services/db-migrations/05-trading-schema.sql
- [REMOVE] services/db-migrations/05-trading-schema.sql

### 005_create_chess
- [KEEP] database/migrations/005_create_chess_schema.sql
- [KEEP] database/migrations/005_create_chess_schema.sql

### create_events
- [KEEP] database/migrations/create_events_schema.sql
- [KEEP] database/migrations/create_events_schema.sql

### fix-leaderboards-schema.sql
- [KEEP] services/db-migrations/fix-leaderboards-schema.sql
- [KEEP] services/db-migrations/fix-leaderboards-schema.sql

### create-leaderboards-schema.sql
- [KEEP] services/db-migrations/create-leaderboards-schema.sql
- [KEEP] services/db-migrations/create-leaderboards-schema.sql

### fix-expert-analysis-schema.sql
- [KEEP] services/db-migrations/fix-expert-analysis-schema.sql
- [KEEP] services/db-migrations/fix-expert-analysis-schema.sql

### fix-custom-betting-schema.sql
- [KEEP] services/db-migrations/fix-custom-betting-schema.sql
- [KEEP] services/db-migrations/fix-custom-betting-schema.sql

### create-betting-schema.sql
- [KEEP] services/db-migrations/create-betting-schema.sql
- [KEEP] services/db-migrations/create-betting-schema.sql

### fix-sports-analysis-schema.sql
- [KEEP] services/db-migrations/fix-sports-analysis-schema.sql
- [KEEP] services/db-migrations/fix-sports-analysis-schema.sql

### create-sports-schema.sql
- [KEEP] services/db-migrations/create-sports-schema.sql
- [KEEP] services/db-migrations/create-sports-schema.sql

### create-expert-schema.sql
- [KEEP] services/db-migrations/create-expert-schema.sql
- [KEEP] services/db-migrations/create-expert-schema.sql

### create-trading-schema.sql
- [KEEP] services/db-migrations/create-trading-schema.sql
- [KEEP] services/db-migrations/create-trading-schema.sql

### fix-odds-exchange-schema
- [KEEP] services/db-migrations/fix-odds-exchange-schema.sql
- [KEEP] services/db-migrations/fix-odds-exchange-schema.sql

### expert-analysts
- [KEEP] services/expert-analysts/app/database/migrations/001_create_expert_analyst_schema.sql
- [REMOVE] services/expert-analysts/app/database/migrations/002_create_expert_analyst_indexes.sql
- [REMOVE] services/expert-analysts/app/database/migrations/003_create_expert_analyst_security.sql
- [KEEP] services/expert-analysts/app/database/migrations/001_create_expert_analyst_schema.sql
- [REMOVE] services/expert-analysts/app/database/models.py

### 001_create_custom_betting_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_custom_betting_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_custom_betting_schema.sql

### 001_create_expert_analyst_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_expert_analyst_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_expert_analyst_schema.sql

### 001_create_leaderboards_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_leaderboards_schema.sql
- [KEEP] services/db-migrations/service-schemas/001_create_leaderboards_schema.sql

### 001_create_gaming_engine.sql
- [KEEP] database/migrations/001_create_gaming_engine.sql
- [REMOVE] services/db-migrations/versions/001_create_gaming_engine.sql

### 004_create_sports_analysis.sql
- [KEEP] database/migrations/004_create_sports_analysis.sql
- [REMOVE] services/db-migrations/versions/004_create_sports_analysis.sql

### gaming-engine
- [KEEP] services/gaming-engine/app/database/migrations/001_add_betting_tables.sql
- [REMOVE] services/gaming-engine/app/database/models.py
- [REMOVE] services/gaming-engine/app/models/chess.py
- [REMOVE] services/gaming-engine/app/models/sessions.py
- [REMOVE] services/gaming-engine/app/models/chess_tournaments.py
- [REMOVE] services/gaming-engine/app/models/__init__.py
- [REMOVE] services/gaming-engine/app/models/games.py
- [REMOVE] services/gaming-engine/app/models/betting.py
- [REMOVE] services/gaming-engine/app/models/tournaments.py
- [REMOVE] services/gaming-engine/app/models/spectator_bets.py

### gaming_engine_indexes.sql
- [KEEP] database/schemas/gaming_engine_indexes.sql
- [REMOVE] services/db-migrations/schemas/gaming_engine_indexes.sql

### odds_exchange_indexes
- [KEEP] database/schemas/odds_exchange_indexes.sql
- [REMOVE] services/db-migrations/schemas/odds_exchange_indexes.sql

### gaming_engine_functions.sql
- [KEEP] database/schemas/gaming_engine_functions.sql
- [REMOVE] services/db-migrations/schemas/gaming_engine_functions.sql

### odds_exchange_security
- [KEEP] database/schemas/odds_exchange_security.sql
- [REMOVE] services/db-migrations/schemas/odds_exchange_security.sql

### odds_exchange
- [KEEP] database/schemas/odds_exchange.sql
- [REMOVE] services/db-migrations/schemas/odds_exchange.sql

### gaming_engine.sql
- [KEEP] database/schemas/gaming_engine.sql
- [REMOVE] services/db-migrations/schemas/gaming_engine.sql

### sports_analysis_security.sql
- [KEEP] database/schemas/sports_analysis_security.sql
- [REMOVE] services/db-migrations/schemas/sports_analysis_security.sql

### odds_exchange_functions
- [KEEP] database/schemas/odds_exchange_functions.sql
- [REMOVE] services/db-migrations/schemas/odds_exchange_functions.sql

### sports_analysis.sql
- [KEEP] database/schemas/sports_analysis.sql
- [REMOVE] services/db-migrations/schemas/sports_analysis.sql

### sports_analysis_indexes.sql
- [KEEP] database/schemas/sports_analysis_indexes.sql
- [REMOVE] services/db-migrations/schemas/sports_analysis_indexes.sql

## Canonical Schema Files:
- 02-core-schema: database/init/02-core-schema.sql
- 04-betting-schema.sql: database/init/04-betting-schema.sql
- 07-sports-schema.sql: database/init/07-sports-schema.sql
- 06-experts-schema.sql: database/init/06-experts-schema.sql
- 08-leaderboards-schema.sql: database/init/08-leaderboards-schema.sql
- 03-gaming-schema.sql: database/init/03-gaming-schema.sql
- 05-trading-schema.sql: database/init/05-trading-schema.sql
- 005_create_chess: database/migrations/005_create_chess_schema.sql
- create_events: database/migrations/create_events_schema.sql
- fix-leaderboards-schema.sql: services/db-migrations/fix-leaderboards-schema.sql
- create-leaderboards-schema.sql: services/db-migrations/create-leaderboards-schema.sql
- fix-expert-analysis-schema.sql: services/db-migrations/fix-expert-analysis-schema.sql
- fix-custom-betting-schema.sql: services/db-migrations/fix-custom-betting-schema.sql
- create-betting-schema.sql: services/db-migrations/create-betting-schema.sql
- fix-sports-analysis-schema.sql: services/db-migrations/fix-sports-analysis-schema.sql
- create-sports-schema.sql: services/db-migrations/create-sports-schema.sql
- create-expert-schema.sql: services/db-migrations/create-expert-schema.sql
- create-trading-schema.sql: services/db-migrations/create-trading-schema.sql
- fix-odds-exchange-schema: services/db-migrations/fix-odds-exchange-schema.sql
- expert-analysts: services/expert-analysts/app/database/migrations/001_create_expert_analyst_schema.sql
- 001_create_custom_betting_schema.sql: services/db-migrations/service-schemas/001_create_custom_betting_schema.sql
- 001_create_expert_analyst_schema.sql: services/db-migrations/service-schemas/001_create_expert_analyst_schema.sql
- 001_create_leaderboards_schema.sql: services/db-migrations/service-schemas/001_create_leaderboards_schema.sql
- 001_create_gaming_engine.sql: database/migrations/001_create_gaming_engine.sql
- 004_create_sports_analysis.sql: database/migrations/004_create_sports_analysis.sql
- gaming-engine: services/gaming-engine/app/database/migrations/001_add_betting_tables.sql
- gaming_engine_indexes.sql: database/schemas/gaming_engine_indexes.sql
- odds_exchange_indexes: database/schemas/odds_exchange_indexes.sql
- gaming_engine_functions.sql: database/schemas/gaming_engine_functions.sql
- odds_exchange_security: database/schemas/odds_exchange_security.sql
- odds_exchange: database/schemas/odds_exchange.sql
- gaming_engine.sql: database/schemas/gaming_engine.sql
- sports_analysis_security.sql: database/schemas/sports_analysis_security.sql
- odds_exchange_functions: database/schemas/odds_exchange_functions.sql
- sports_analysis.sql: database/schemas/sports_analysis.sql
- sports_analysis_indexes.sql: database/schemas/sports_analysis_indexes.sql

## Summary:
- Total duplicates found: 36
- Files to remove: 38
- Canonical files: 36