services:
  # Database Services
  postgres:
    image: postgres:15
    container_name: betbet-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_MULTIPLE_DATABASES: betbet_gaming,betbet_betting,betbet_trading,betbet_experts,betbet_sports,betbet_leaderboards
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/migrations:/migrations
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - betbet-network

  redis:
    image: redis:7-alpine
    container_name: betbet-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - betbet-network

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: betbet-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - betbet-network

  grafana:
    image: grafana/grafana:latest
    container_name: betbet-grafana
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - betbet-network
    depends_on:
      - prometheus

  # Database Migration Service
  db-migrations:
    build:
      context: ./services/db-migrations
      dockerfile: Dockerfile
    container_name: betbet-db-migrations
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - betbet-network
    restart: "no"  # Run once and exit

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    container_name: betbet-api-gateway
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - PORT=8000
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - JWT_SECRET=${JWT_SECRET_KEY}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - CORS_ORIGIN=http://localhost:3000,http://localhost:3001
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/api-gateway:/app
      - /app/node_modules
    networks:
      - betbet-network

  # WebSocket Manager
  websocket-manager:
    build:
      context: ./services/websocket-manager
      dockerfile: Dockerfile
    container_name: betbet-websocket
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - JWT_SECRET=${JWT_SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/websocket-manager:/app
      - /app/node_modules
    networks:
      - betbet-network

  # Event Service
  event-service:
    build:
      context: ./services
      dockerfile: event-service/Dockerfile
    container_name: betbet-events
    ports:
      - "8008:8008"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/event-service:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Gaming Engine Service
  gaming-engine:
    build:
      context: ./services
      dockerfile: gaming-engine/Dockerfile
    container_name: betbet-gaming
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    # volumes:
      # - ./services/gaming-engine:/app
      # - ./services/shared:/app/shared
    networks:
      - betbet-network

  # Custom Betting Service
  custom-betting:
    build:
      context: ./services
      dockerfile: custom-betting/Dockerfile
    container_name: betbet-custom-betting
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:123Bubblegums@postgres:5432/betbet_db
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=betbet_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=123Bubblegums
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=local-development-secret-key-change-in-production
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/custom-betting:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Odds Exchange Service
  odds-exchange:
    build:
      context: ./services
      dockerfile: odds-exchange/Dockerfile
    container_name: betbet-trading
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/odds-exchange:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Expert Analysis Service
  expert-analysis:
    build:
      context: ./services
      dockerfile: expert-analysts/Dockerfile
    container_name: betbet-experts
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/expert-analysts:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Sports Analysis Service
  sports-analysis:
    build:
      context: ./services
      dockerfile: sports-analysis/Dockerfile
    container_name: betbet-sports
    ports:
      - "8005:8005"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
      - SPORTS_API_KEY=${SPORTS_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/sports-analysis:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # User Sync Service
  user-sync:
    build:
      context: ./services/user-sync
    container_name: betbet-user-sync
    ports:
      - "8009:8009"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - SERVICE_PORT=8009
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./services/user-sync:/app
    networks:
      - betbet-network

  # Leaderboards Service
  leaderboards-service:
    build:
      context: ./services
      dockerfile: leaderboards/Dockerfile.production
    container_name: betbet-leaderboards
    ports:
      - "8006:8006"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - WEBSOCKET_URL=ws://websocket-manager:8080
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/leaderboards:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Wallet Service
  wallet-service:
    build:
      context: ./services
      dockerfile: wallet-service/Dockerfile
    container_name: betbet-wallet
    ports:
      - "8007:8007"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betbet_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET_KEY}
      - API_GATEWAY_URL=http://api-gateway:8000
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./services/wallet-service:/app
      - ./services/shared:/app/shared:ro
    networks:
      - betbet-network

  # Frontend Applications
  web-frontend:
    build:
      context: ./frontend/web
      dockerfile: Dockerfile.dev
    container_name: betbet-web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8080
      - NEXT_PUBLIC_GAMING_ENGINE_WS_URL=ws://localhost:8080
      - NEXT_PUBLIC_GAMING_ENGINE_URL=http://localhost:8000
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
    depends_on:
      - api-gateway
      - websocket-manager
    volumes:
      - ./frontend/web:/app
      - /app/node_modules
      - /app/.next
    networks:
      - betbet-network

  admin-dashboard:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile.dev
    container_name: betbet-admin
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NEXT_PUBLIC_APP_URL=http://localhost:3001
    depends_on:
      - api-gateway
      - websocket-manager
    volumes:
      - ./frontend/admin:/app
      - /app/node_modules
      - /app/.next
    networks:
      - betbet-network

  # Optional: Database Management UI
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: betbet-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
      PGADMIN_LISTEN_PORT: 5050
    ports:
      - "5050:5050"
    depends_on:
      - postgres
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - betbet-network

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: betbet-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - betbet-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  betbet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16