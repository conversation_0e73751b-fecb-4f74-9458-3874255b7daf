#!/usr/bin/env python3
"""
BetBet Platform - Centralized Test Runner
==========================================

Master test runner for the entire BetBet platform with centralized test directory structure.
Supports running tests by type, service, or specific test files.
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path
from typing import List, Optional

class BetBetTestRunner:
    """Centralized test runner for BetBet platform."""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.tests_dir = self.root_dir / "tests"
        
    def run_unit_tests(self, service: Optional[str] = None) -> int:
        """Run unit tests for all services or specific service."""
        cmd = ["python", "-m", "pytest", "tests/unit/"]
        
        if service:
            service_dir = self.tests_dir / "unit" / service
            if not service_dir.exists():
                print(f"❌ Service '{service}' not found in unit tests")
                return 1
            cmd[-1] = str(service_dir)
            
        cmd.extend(["-m", "unit", "--tb=short"])
        return self._run_command(cmd, "Unit Tests")
    
    def run_integration_tests(self, service: Optional[str] = None) -> int:
        """Run integration tests for all services or specific service."""
        cmd = ["python", "-m", "pytest", "tests/integration/"]
        
        if service:
            service_dir = self.tests_dir / "integration" / service
            if not service_dir.exists():
                print(f"❌ Service '{service}' not found in integration tests")
                return 1
            cmd[-1] = str(service_dir)
            
        cmd.extend(["-m", "integration", "--tb=short"])
        return self._run_command(cmd, "Integration Tests")
    
    def run_e2e_tests(self, service: Optional[str] = None) -> int:
        """Run end-to-end tests."""
        if service == "frontend" or service == "frontend-web":
            # Run Playwright tests for frontend
            cmd = ["npx", "playwright", "test", "--config", "tests/config/playwright.config.ts"]
            return self._run_command(cmd, "Frontend E2E Tests", cwd="frontend/web")
        else:
            # Run Python E2E tests
            cmd = ["python", "-m", "pytest", "tests/e2e/"]
            if service:
                service_dir = self.tests_dir / "e2e" / service
                if not service_dir.exists():
                    print(f"❌ Service '{service}' not found in e2e tests")
                    return 1
                cmd[-1] = str(service_dir)
            cmd.extend(["-m", "e2e", "--tb=short"])
            return self._run_command(cmd, "E2E Tests")
    
    def run_performance_tests(self, service: Optional[str] = None) -> int:
        """Run performance tests."""
        cmd = ["python", "-m", "pytest", "tests/performance/"]
        
        if service:
            service_dir = self.tests_dir / "performance" / service
            if not service_dir.exists():
                print(f"❌ Service '{service}' not found in performance tests")
                return 1
            cmd[-1] = str(service_dir)
            
        cmd.extend(["-m", "performance", "--tb=short"])
        return self._run_command(cmd, "Performance Tests")
    
    def run_all_tests(self) -> int:
        """Run all tests in sequence."""
        results = []
        
        print("🧪 Running All BetBet Platform Tests\n")
        
        # Unit tests
        print("1️⃣  Running Unit Tests...")
        results.append(self.run_unit_tests())
        
        # Integration tests
        print("\n2️⃣  Running Integration Tests...")
        results.append(self.run_integration_tests())
        
        # E2E tests (Python)
        print("\n3️⃣  Running E2E Tests...")
        results.append(self.run_e2e_tests())
        
        # Performance tests
        print("\n4️⃣  Running Performance Tests...")
        results.append(self.run_performance_tests())
        
        # Frontend tests
        print("\n5️⃣  Running Frontend Tests...")
        results.append(self.run_frontend_tests())
        
        # Summary
        failed_count = sum(1 for result in results if result != 0)
        if failed_count == 0:
            print("\n✅ All test suites passed!")
            return 0
        else:
            print(f"\n❌ {failed_count} test suite(s) failed")
            return 1
    
    def run_frontend_tests(self) -> int:
        """Run frontend Jest and Playwright tests."""
        results = []
        
        # Jest tests
        cmd = ["npm", "test", "--", "--watchAll=false", "--coverage"]
        results.append(self._run_command(cmd, "Frontend Jest Tests", cwd="frontend/web"))
        
        # Playwright tests
        cmd = ["npx", "playwright", "test"]
        results.append(self._run_command(cmd, "Frontend Playwright Tests", cwd="frontend/web"))
        
        return max(results) if results else 1
    
    def run_specific_test(self, test_path: str) -> int:
        """Run a specific test file or directory."""
        if test_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
            # Frontend test
            cmd = ["npm", "test", test_path]
            return self._run_command(cmd, f"Test: {test_path}", cwd="frontend/web")
        else:
            # Python test
            cmd = ["python", "-m", "pytest", test_path, "-v"]
            return self._run_command(cmd, f"Test: {test_path}")
    
    def list_available_tests(self):
        """List all available tests by category."""
        print("📋 Available Tests in BetBet Platform\n")
        
        categories = [
            ("Unit Tests", "tests/unit"),
            ("Integration Tests", "tests/integration"),
            ("E2E Tests", "tests/e2e"),
            ("Performance Tests", "tests/performance")
        ]
        
        for category, path in categories:
            print(f"🔹 {category}:")
            test_dir = self.tests_dir / path.split("/")[1]
            if test_dir.exists():
                for service_dir in sorted(test_dir.iterdir()):
                    if service_dir.is_dir():
                        test_files = list(service_dir.glob("test_*.py")) + list(service_dir.glob("*.test.*"))
                        if test_files:
                            print(f"   📁 {service_dir.name}: {len(test_files)} test files")
            print()
    
    def _run_command(self, cmd: List[str], description: str, cwd: Optional[str] = None) -> int:
        """Run a command and return exit code."""
        print(f"🚀 {description}")
        print(f"   Command: {' '.join(cmd)}")
        
        if cwd:
            full_cwd = self.root_dir / cwd
            print(f"   Working Directory: {full_cwd}")
        else:
            full_cwd = self.root_dir
            
        try:
            result = subprocess.run(
                cmd,
                cwd=full_cwd,
                capture_output=False,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ {description} - PASSED\n")
            else:
                print(f"❌ {description} - FAILED (exit code: {result.returncode})\n")
                
            return result.returncode
            
        except FileNotFoundError:
            print(f"❌ Command not found: {cmd[0]}")
            return 1
        except Exception as e:
            print(f"❌ Error running {description}: {e}")
            return 1

def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(
        description="BetBet Platform Centralized Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py --all                    # Run all tests
  python run_tests.py --unit                   # Run all unit tests
  python run_tests.py --unit sports-analysis   # Run unit tests for sports-analysis
  python run_tests.py --integration            # Run all integration tests
  python run_tests.py --e2e frontend           # Run frontend E2E tests
  python run_tests.py --performance            # Run performance tests
  python run_tests.py --frontend               # Run all frontend tests
  python run_tests.py --test tests/unit/custom-betting/test_matching_engine.py
  python run_tests.py --list                   # List available tests
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--all", action="store_true", help="Run all tests")
    group.add_argument("--unit", nargs="?", const="", help="Run unit tests (optionally for specific service)")
    group.add_argument("--integration", nargs="?", const="", help="Run integration tests (optionally for specific service)")
    group.add_argument("--e2e", nargs="?", const="", help="Run E2E tests (optionally for specific service)")
    group.add_argument("--performance", nargs="?", const="", help="Run performance tests (optionally for specific service)")
    group.add_argument("--frontend", action="store_true", help="Run all frontend tests (Jest + Playwright)")
    group.add_argument("--test", help="Run specific test file or directory")
    group.add_argument("--list", action="store_true", help="List available tests")
    
    args = parser.parse_args()
    
    runner = BetBetTestRunner()
    
    if args.list:
        runner.list_available_tests()
        return 0
    elif args.all:
        return runner.run_all_tests()
    elif args.unit is not None:
        service = args.unit if args.unit else None
        return runner.run_unit_tests(service)
    elif args.integration is not None:
        service = args.integration if args.integration else None
        return runner.run_integration_tests(service)
    elif args.e2e is not None:
        service = args.e2e if args.e2e else None
        return runner.run_e2e_tests(service)
    elif args.performance is not None:
        service = args.performance if args.performance else None
        return runner.run_performance_tests(service)
    elif args.frontend:
        return runner.run_frontend_tests()
    elif args.test:
        return runner.run_specific_test(args.test)

if __name__ == "__main__":
    sys.exit(main())