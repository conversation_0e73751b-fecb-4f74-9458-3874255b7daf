# BetBet Platform - Centralized Testing Structure

This directory contains all tests for the BetBet platform, organized by test type and service. All existing test files have been moved from their original service-specific locations to this centralized structure for better organization and test management.

## 📁 Directory Structure

```
tests/
├── unit/                      # Unit tests for all services
│   ├── custom-betting/        # Custom betting service unit tests
│   ├── expert-analysts/       # Expert analysts service unit tests  
│   ├── frontend-web/          # Frontend web application unit tests
│   ├── gaming-engine/         # Gaming engine service unit tests
│   └── sports-analysis/       # Sports analysis service unit tests
├── integration/               # Integration tests
│   ├── custom-betting/        # Custom betting integration tests
│   ├── frontend-web/          # Frontend integration tests
│   └── sports-analysis/       # Sports analysis integration tests
├── e2e/                      # End-to-end tests
│   ├── frontend-web/         # Frontend E2E tests (Playwright)
│   └── sports-analysis/      # Sports analysis E2E tests
├── performance/              # Performance and load tests
│   └── sports-analysis/      # Sports analysis performance tests
├── security/                 # Security tests (infrastructure)
├── config/                   # Test configuration files
│   ├── jest.config.js        # Jest configuration
│   ├── playwright.config.ts  # Playwright configuration
│   └── setupTests.ts         # Test setup utilities
└── fixtures/                 # Test data and fixtures
```

## 🧪 Test Types

### Unit Tests
- **Location**: `tests/unit/`
- **Purpose**: Test individual functions, classes, and modules in isolation
- **Framework**: pytest (Python), Jest (JavaScript/TypeScript)
- **Coverage**: 19 test files across 5 services

### Integration Tests  
- **Location**: `tests/integration/`
- **Purpose**: Test interactions between different components and services
- **Framework**: pytest (Python), React Testing Library (Frontend)
- **Coverage**: 4 test files across 3 services

### End-to-End Tests
- **Location**: `tests/e2e/`
- **Purpose**: Test complete user workflows and system functionality
- **Framework**: Playwright (Frontend), pytest (Backend)
- **Coverage**: 3 test files across 2 services

### Performance Tests
- **Location**: `tests/performance/`
- **Purpose**: Test system performance, load capacity, and response times
- **Framework**: pytest with performance testing libraries
- **Coverage**: 1 test file (sports-analysis)

## 🚀 Running Tests

### Using the Centralized Test Runner

The platform includes a comprehensive test runner (`run_tests.py`) that supports various test execution scenarios:

```bash
# Run all tests
python run_tests.py --all

# Run specific test types
python run_tests.py --unit                    # All unit tests
python run_tests.py --integration             # All integration tests
python run_tests.py --e2e                     # All E2E tests
python run_tests.py --performance             # All performance tests
python run_tests.py --frontend                # All frontend tests

# Run tests for specific services
python run_tests.py --unit sports-analysis    # Unit tests for sports-analysis
python run_tests.py --integration frontend-web # Frontend integration tests
python run_tests.py --e2e frontend            # Frontend E2E tests

# Run specific test files
python run_tests.py --test tests/unit/custom-betting/test_matching_engine.py

# List available tests
python run_tests.py --list
```

### Using pytest Directly

```bash
# Run all Python tests
pytest

# Run specific test directories
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run tests with coverage
pytest --cov=services --cov-report=html

# Run tests with specific markers
pytest -m unit
pytest -m integration
pytest -m e2e
pytest -m performance
```

### Using npm for Frontend Tests

```bash
# From frontend/web directory
npm test                    # Jest unit tests
npx playwright test        # Playwright E2E tests
```

## 📊 Test Coverage

### Current Test Files by Service

| Service | Unit | Integration | E2E | Performance | Total |
|---------|------|-------------|-----|-------------|-------|
| Custom Betting | 3 | 1 | 0 | 0 | 4 |
| Sports Analysis | 4 | 3 | 1 | 1 | 9 |
| Expert Analysts | 2 | 0 | 0 | 0 | 2 |
| Gaming Engine | 5 | 0 | 0 | 0 | 5 |
| Frontend Web | 1 | 3 | 2 | 0 | 6 |
| **Total** | **15** | **7** | **3** | **1** | **26** |

### Services Needing Tests

- **API Gateway**: No test files currently
- **WebSocket Manager**: No test files currently
- **Odds Exchange**: Infrastructure exists but no tests
- **Leaderboards**: Configuration exists but no test files
- **Frontend Admin**: No test files currently

## 🔧 Configuration

### Root Configuration (`pytest.ini`)
- Centralized pytest configuration for all Python tests
- Coverage reporting to `tests/coverage/`
- JUnit XML output to `tests/junit.xml`
- Test markers for categorization
- Async test support

### Frontend Configuration
- **Jest**: `tests/config/jest.config.js`
- **Playwright**: `tests/config/playwright.config.ts`
- **Setup**: `tests/config/setupTests.ts`

## 📝 Test Markers

Tests are categorized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests  
- `@pytest.mark.e2e` - End-to-end tests
- `@pytest.mark.performance` - Performance tests
- `@pytest.mark.slow` - Slow running tests
- `@pytest.mark.api` - API tests
- `@pytest.mark.db` - Database tests
- `@pytest.mark.ws` - WebSocket tests
- `@pytest.mark.auth` - Authentication tests

## 🔍 Examples

### Running Unit Tests for Sports Analysis
```bash
python run_tests.py --unit sports-analysis
```

### Running All Frontend Tests
```bash
python run_tests.py --frontend
```

### Running Specific Integration Test
```bash
python run_tests.py --test tests/integration/custom-betting/test_api_integration.py
```

### Running Tests with Coverage
```bash
pytest tests/unit/ --cov=services --cov-report=html
```

## 📈 Test Reports

- **Coverage Reports**: Generated in `tests/coverage/`
- **JUnit XML**: Generated at `tests/junit.xml`
- **HTML Coverage**: Available at `tests/coverage/index.html`

## 🚧 Migration Notes

All test files have been moved from their original service directories to this centralized structure:

- **Original**: `services/{service}/tests/` → **New**: `tests/{type}/{service}/`
- **Original**: `frontend/web/src/__tests__/` → **New**: `tests/{type}/frontend-web/`

The original test files remain in place for backwards compatibility, but the centralized versions should be used going forward.

## 🎯 Best Practices

1. **Write tests for new features** in the appropriate centralized directory
2. **Use appropriate test markers** to categorize tests
3. **Follow naming conventions**: `test_*.py` for Python, `*.test.tsx` for frontend
4. **Include integration tests** for cross-service functionality
5. **Add E2E tests** for critical user workflows
6. **Use the centralized test runner** for consistent execution
7. **Maintain test configuration** in the `tests/config/` directory