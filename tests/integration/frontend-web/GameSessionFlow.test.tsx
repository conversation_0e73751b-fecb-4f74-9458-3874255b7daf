/**
 * BetBet Gaming Engine - Game Session Flow Integration Tests
 * =========================================================
 */

import { render, screen, fireEvent, waitFor } from '../utils/simple-test-utils';
import { createMockUser, createMockSession, createMockGame } from '../utils/simple-test-utils';

// Mock API responses
const mockApiResponses = {
  sessions: [
    createMockSession({ 
      id: 'session-1', 
      session_name: 'Trivia Night', 
      status: 'waiting',
      current_participants: 2,
      max_participants: 8 
    }),
    createMockSession({ 
      id: 'session-2', 
      session_name: 'Speed Challenge', 
      status: 'active',
      current_participants: 6,
      max_participants: 6 
    })
  ],
  games: [
    createMockGame({ 
      id: 'game-1', 
      name: 'Sports Trivia', 
      category: 'trivia',
      min_players: 2,
      max_players: 8 
    })
  ],
  user: createMockUser({ id: 'user-1', username: 'testuser', balance: 150 })
};

// Mock complete game session flow component
const MockGameSessionFlow = () => {
  const [currentView, setCurrentView] = React.useState<'sessions' | 'create' | 'game'>('sessions');
  const [selectedSession, setSelectedSession] = React.useState<any>(null);
  const [user] = React.useState(mockApiResponses.user);
  const [isAuthenticated] = React.useState(true);
  const [gameState, setGameState] = React.useState<any>(null);

  const handleJoinSession = (sessionId: string) => {
    const session = mockApiResponses.sessions.find(s => s.id === sessionId);
    if (session) {
      setSelectedSession(session);
      setCurrentView('game');
      
      // Simulate joining and starting a game
      setTimeout(() => {
        setGameState({
          currentRound: 1,
          totalRounds: 5,
          timeRemaining: 30,
          question: 'What is the capital of France?',
          options: ['London', 'Paris', 'Berlin', 'Madrid'],
          scores: { [user.id]: 0 },
          status: 'active'
        });
      }, 100);
    }
  };

  const handleCreateSession = (gameId: string, sessionName: string, maxPlayers: number) => {
    const newSession = createMockSession({
      id: 'new-session',
      session_name: sessionName,
      game_id: gameId,
      max_participants: maxPlayers,
      current_participants: 1,
      status: 'waiting'
    });
    
    setSelectedSession(newSession);
    setCurrentView('game');
  };

  const handleAnswerQuestion = (answerIndex: number) => {
    if (!gameState) return;
    
    const isCorrect = answerIndex === 1; // Paris is correct (index 1)
    const points = isCorrect ? 10 : 0;
    
    setGameState({
      ...gameState,
      currentRound: gameState.currentRound + 1,
      scores: {
        ...gameState.scores,
        [user.id]: gameState.scores[user.id] + points
      },
      timeRemaining: 30,
      question: gameState.currentRound < 5 ? 'Next question here...' : null
    });
  };

  if (currentView === 'sessions') {
    return (
      <div data-testid="session-browser">
        <h1>Game Sessions</h1>
        
        <button 
          data-testid="create-session-button"
          onClick={() => setCurrentView('create')}
        >
          Create Session
        </button>

        <div data-testid="session-list">
          {mockApiResponses.sessions.map((session) => (
            <div key={session.id} data-testid={`session-${session.id}`} className="session-card">
              <h3>{session.session_name}</h3>
              <div>Status: {session.status}</div>
              <div>Players: {session.current_participants}/{session.max_participants}</div>
              <div>Entry Fee: ${session.entry_fee}</div>
              
              <button 
                data-testid={`join-${session.id}`}
                onClick={() => handleJoinSession(session.id)}
                disabled={session.status !== 'waiting' || session.current_participants >= session.max_participants}
              >
                {session.status === 'waiting' ? 'Join Session' : 'In Progress'}
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (currentView === 'create') {
    return (
      <div data-testid="create-session">
        <h1>Create Game Session</h1>
        
        <form onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.target as HTMLFormElement);
          handleCreateSession(
            formData.get('gameId') as string,
            formData.get('sessionName') as string,
            parseInt(formData.get('maxPlayers') as string)
          );
        }}>
          <select name="gameId" data-testid="game-select" required>
            <option value="">Select Game</option>
            {mockApiResponses.games.map((game) => (
              <option key={game.id} value={game.id}>{game.name}</option>
            ))}
          </select>
          
          <input 
            name="sessionName" 
            data-testid="session-name-input" 
            placeholder="Session Name" 
            required 
          />
          
          <input 
            name="maxPlayers" 
            type="number" 
            data-testid="max-players-input" 
            placeholder="Max Players" 
            min="2" 
            max="20" 
            required 
          />
          
          <button type="submit" data-testid="create-submit">Create Session</button>
        </form>
        
        <button 
          data-testid="back-to-sessions"
          onClick={() => setCurrentView('sessions')}
        >
          Back to Sessions
        </button>
      </div>
    );
  }

  if (currentView === 'game' && selectedSession) {
    return (
      <div data-testid="game-interface">
        <div data-testid="session-header">
          <h1>{selectedSession.session_name}</h1>
          <div>Status: {selectedSession.status}</div>
          <div>Your Balance: ${user.balance}</div>
        </div>

        {gameState ? (
          <div data-testid="active-game">
            <div data-testid="game-progress">
              Round {gameState.currentRound} of {gameState.totalRounds}
            </div>
            
            <div data-testid="timer">
              {gameState.timeRemaining}s remaining
            </div>
            
            <div data-testid="score">
              Your Score: {gameState.scores[user.id]}
            </div>

            {gameState.question && (
              <div data-testid="question-area">
                <h2>{gameState.question}</h2>
                <div data-testid="answer-options">
                  {gameState.options.map((option: string, index: number) => (
                    <button 
                      key={index}
                      data-testid={`answer-${index}`}
                      onClick={() => handleAnswerQuestion(index)}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {gameState.currentRound > gameState.totalRounds && (
              <div data-testid="game-complete">
                <h2>Game Complete!</h2>
                <div>Final Score: {gameState.scores[user.id]}</div>
                <button 
                  data-testid="return-to-sessions"
                  onClick={() => setCurrentView('sessions')}
                >
                  Return to Sessions
                </button>
              </div>
            )}
          </div>
        ) : (
          <div data-testid="waiting-room">
            <h2>Waiting Room</h2>
            <div>Waiting for more players...</div>
            <button 
              data-testid="leave-session"
              onClick={() => setCurrentView('sessions')}
            >
              Leave Session
            </button>
          </div>
        )}
      </div>
    );
  }

  return null;
};

// Mock React
const React = { useState: jest.fn() };

describe('Game Session Flow Integration', () => {
  beforeEach(() => {
    // Reset all useState mocks
    React.useState
      .mockReturnValueOnce(['sessions', jest.fn()]) // currentView
      .mockReturnValueOnce([null, jest.fn()]) // selectedSession
      .mockReturnValueOnce([mockApiResponses.user, jest.fn()]) // user
      .mockReturnValueOnce([true, jest.fn()]) // isAuthenticated
      .mockReturnValueOnce([null, jest.fn()]); // gameState
  });

  describe('Session Browser', () => {
    it('displays available game sessions', () => {
      render(<MockGameSessionFlow />);
      
      expect(screen.getByText('Game Sessions')).toBeInTheDocument();
      expect(screen.getByTestId('session-session-1')).toBeInTheDocument();
      expect(screen.getByTestId('session-session-2')).toBeInTheDocument();
      
      expect(screen.getByText('Trivia Night')).toBeInTheDocument();
      expect(screen.getByText('Speed Challenge')).toBeInTheDocument();
    });

    it('shows session details and status', () => {
      render(<MockGameSessionFlow />);
      
      const triviaSession = screen.getByTestId('session-session-1');
      expect(triviaSession).toHaveTextContent('Status: waiting');
      expect(triviaSession).toHaveTextContent('Players: 2/8');
      expect(triviaSession).toHaveTextContent('Entry Fee: $10');
    });

    it('enables join button for waiting sessions', () => {
      render(<MockGameSessionFlow />);
      
      const joinButton = screen.getByTestId('join-session-1');
      expect(joinButton).not.toBeDisabled();
      expect(joinButton).toHaveTextContent('Join Session');
    });

    it('disables join button for active sessions', () => {
      render(<MockGameSessionFlow />);
      
      const joinButton = screen.getByTestId('join-session-2');
      expect(joinButton).toBeDisabled();
      expect(joinButton).toHaveTextContent('In Progress');
    });

    it('navigates to create session view', () => {
      const mockSetCurrentView = jest.fn();
      React.useState
        .mockReturnValueOnce(['sessions', mockSetCurrentView])
        .mockReturnValueOnce([null, jest.fn()])
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()])
        .mockReturnValueOnce([true, jest.fn()])
        .mockReturnValueOnce([null, jest.fn()]);

      render(<MockGameSessionFlow />);
      
      const createButton = screen.getByTestId('create-session-button');
      fireEvent.click(createButton);
      
      expect(mockSetCurrentView).toHaveBeenCalledWith('create');
    });
  });

  describe('Session Creation', () => {
    beforeEach(() => {
      React.useState
        .mockReturnValueOnce(['create', jest.fn()]) // currentView = 'create'
        .mockReturnValueOnce([null, jest.fn()])
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()])
        .mockReturnValueOnce([true, jest.fn()])
        .mockReturnValueOnce([null, jest.fn()]);
    });

    it('displays session creation form', () => {
      render(<MockGameSessionFlow />);
      
      expect(screen.getByText('Create Game Session')).toBeInTheDocument();
      expect(screen.getByTestId('game-select')).toBeInTheDocument();
      expect(screen.getByTestId('session-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('max-players-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-submit')).toBeInTheDocument();
    });

    it('shows available games in dropdown', () => {
      render(<MockGameSessionFlow />);
      
      const gameSelect = screen.getByTestId('game-select');
      expect(gameSelect).toHaveTextContent('Sports Trivia');
    });

    it('handles form submission', () => {
      const mockHandleCreateSession = jest.fn();
      
      render(<MockGameSessionFlow />);
      
      // Fill out form
      fireEvent.change(screen.getByTestId('game-select'), { 
        target: { value: 'game-1' } 
      });
      fireEvent.change(screen.getByTestId('session-name-input'), { 
        target: { value: 'My Test Session' } 
      });
      fireEvent.change(screen.getByTestId('max-players-input'), { 
        target: { value: '6' } 
      });
      
      // Submit form
      fireEvent.click(screen.getByTestId('create-submit'));
      
      // Form should be submittable
      expect(screen.getByTestId('create-submit')).toBeInTheDocument();
    });
  });

  describe('Game Play', () => {
    beforeEach(() => {
      const mockGameState = {
        currentRound: 1,
        totalRounds: 5,
        timeRemaining: 30,
        question: 'What is the capital of France?',
        options: ['London', 'Paris', 'Berlin', 'Madrid'],
        scores: { 'user-1': 0 },
        status: 'active'
      };

      React.useState
        .mockReturnValueOnce(['game', jest.fn()]) // currentView = 'game'
        .mockReturnValueOnce([mockApiResponses.sessions[0], jest.fn()]) // selectedSession
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()]) // user
        .mockReturnValueOnce([true, jest.fn()]) // isAuthenticated
        .mockReturnValueOnce([mockGameState, jest.fn()]); // gameState
    });

    it('displays game interface with question', () => {
      render(<MockGameSessionFlow />);
      
      expect(screen.getByTestId('game-interface')).toBeInTheDocument();
      expect(screen.getByText('Trivia Night')).toBeInTheDocument();
      expect(screen.getByText('Round 1 of 5')).toBeInTheDocument();
      expect(screen.getByText('30s remaining')).toBeInTheDocument();
      expect(screen.getByText('What is the capital of France?')).toBeInTheDocument();
    });

    it('shows answer options', () => {
      render(<MockGameSessionFlow />);
      
      expect(screen.getByTestId('answer-0')).toHaveTextContent('London');
      expect(screen.getByTestId('answer-1')).toHaveTextContent('Paris');
      expect(screen.getByTestId('answer-2')).toHaveTextContent('Berlin');
      expect(screen.getByTestId('answer-3')).toHaveTextContent('Madrid');
    });

    it('displays current score', () => {
      render(<MockGameSessionFlow />);
      
      expect(screen.getByText('Your Score: 0')).toBeInTheDocument();
    });

    it('handles answer selection', () => {
      const mockSetGameState = jest.fn();
      React.useState
        .mockReturnValueOnce(['game', jest.fn()])
        .mockReturnValueOnce([mockApiResponses.sessions[0], jest.fn()])
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()])
        .mockReturnValueOnce([true, jest.fn()])
        .mockReturnValueOnce([{
          currentRound: 1,
          totalRounds: 5,
          timeRemaining: 30,
          question: 'What is the capital of France?',
          options: ['London', 'Paris', 'Berlin', 'Madrid'],
          scores: { 'user-1': 0 },
          status: 'active'
        }, mockSetGameState]);

      render(<MockGameSessionFlow />);
      
      const correctAnswer = screen.getByTestId('answer-1'); // Paris
      fireEvent.click(correctAnswer);
      
      expect(mockSetGameState).toHaveBeenCalled();
    });
  });

  describe('Complete Game Flow', () => {
    it('completes entire session join and play flow', async () => {
      const mockSetCurrentView = jest.fn();
      const mockSetSelectedSession = jest.fn();
      const mockSetGameState = jest.fn();

      // Start with sessions view
      React.useState
        .mockReturnValueOnce(['sessions', mockSetCurrentView])
        .mockReturnValueOnce([null, mockSetSelectedSession])
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()])
        .mockReturnValueOnce([true, jest.fn()])
        .mockReturnValueOnce([null, mockSetGameState]);

      const { rerender } = render(<MockGameSessionFlow />);
      
      // 1. Should start with session browser
      expect(screen.getByText('Game Sessions')).toBeInTheDocument();
      
      // 2. Click join session
      const joinButton = screen.getByTestId('join-session-1');
      fireEvent.click(joinButton);
      
      expect(mockSetSelectedSession).toHaveBeenCalled();
      expect(mockSetCurrentView).toHaveBeenCalledWith('game');
    });
  });

  describe('Error Handling', () => {
    it('handles session join failure gracefully', () => {
      render(<MockGameSessionFlow />);
      
      // Try to join a full session
      const fullSessionJoin = screen.getByTestId('join-session-2');
      expect(fullSessionJoin).toBeDisabled();
    });

    it('shows waiting room when game not started', () => {
      React.useState
        .mockReturnValueOnce(['game', jest.fn()])
        .mockReturnValueOnce([mockApiResponses.sessions[0], jest.fn()])
        .mockReturnValueOnce([mockApiResponses.user, jest.fn()])
        .mockReturnValueOnce([true, jest.fn()])
        .mockReturnValueOnce([null, jest.fn()]); // gameState = null

      render(<MockGameSessionFlow />);
      
      expect(screen.getByTestId('waiting-room')).toBeInTheDocument();
      expect(screen.getByText('Waiting for more players...')).toBeInTheDocument();
    });
  });
});