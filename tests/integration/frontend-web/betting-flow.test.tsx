/**
 * Custom Betting Platform - Betting Flow Integration Tests
 * ========================================================
 * 
 * Integration tests for the complete betting user flow.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { BettingInterface } from '@/components/custom-betting/BettingInterface';
import { MarketHeader } from '@/components/custom-betting/MarketHeader';
import { MarketOutcomes } from '@/components/custom-betting/MarketOutcomes';
import { UserBetManager } from '@/components/custom-betting/UserBetManager';
import { mockBinaryMarket, mockMultiChoiceMarket, mockUsers } from '../mocks/custom-betting-data';
import { server } from '../setupTests';
import { rest } from 'msw';

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

// Mock market page component for integration testing
const MockMarketPage = ({ market, userParticipation }: any) => {
  const [participation, setParticipation] = React.useState(userParticipation);
  const [selectedOutcome, setSelectedOutcome] = React.useState(null);

  const handleBetPlaced = (newParticipation: any) => {
    setParticipation(newParticipation);
  };

  const handleOutcomeSelect = (outcome: any) => {
    setSelectedOutcome(outcome);
  };

  return (
    <div>
      <MarketHeader market={market} userParticipation={participation} />
      <MarketOutcomes 
        market={market} 
        userParticipation={participation}
        onOutcomeSelect={handleOutcomeSelect}
      />
      {selectedOutcome && (
        <BettingInterface
          market={market}
          userParticipation={participation}
          selectedOutcome={selectedOutcome}
          onBetPlaced={handleBetPlaced}
        />
      )}
    </div>
  );
};

describe('Betting Flow Integration', () => {
  describe('Complete Betting Flow', () => {
    it('places backing bet through full user flow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Verify market is displayed
      expect(screen.getByText(mockBinaryMarket.title)).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      
      // Select an outcome
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      // Betting interface should appear
      expect(screen.getByText('Place Your Bet')).toBeInTheDocument();
      expect(screen.getByText('Select Outcome')).toBeInTheDocument();
      
      // Outcome should be pre-selected
      expect(screen.getByText(mockBinaryMarket.outcomes[0].outcome_text)).toBeInTheDocument();
      
      // Fill betting form
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.5');
      
      // Verify calculations
      await waitFor(() => {
        expect(screen.getByText('$250.00')).toBeInTheDocument(); // Potential payout
        expect(screen.getByText('$150.00')).toBeInTheDocument(); // Potential profit
      });
      
      // Place bet
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeEnabled();
      await user.click(placeBetButton);
      
      // Should show loading state
      expect(screen.getByText('Placing Bet...')).toBeInTheDocument();
      
      // Wait for bet to be placed
      await waitFor(() => {
        expect(screen.getByText('Your Position')).toBeInTheDocument();
        expect(screen.getByText('Backing $100')).toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Market header should show user participation
      expect(screen.getByText('Your Position')).toBeInTheDocument();
      
      // Outcome card should be highlighted
      const updatedOutcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      expect(updatedOutcomeCard).toHaveClass('border-primary');
    });

    it('places laying bet through full user flow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Select outcome
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[1].id}`);
      await user.click(outcomeCard);
      
      // Switch to laying position
      const layingButton = screen.getByRole('button', { name: /lay \(against\)/i });
      await user.click(layingButton);
      
      expect(screen.getByText('You win if this outcome does NOT happen')).toBeInTheDocument();
      
      // Fill form
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '50');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '1.8');
      
      // Place laying bet
      const placeBetButton = screen.getByRole('button', { name: /place lay bet/i });
      await user.click(placeBetButton);
      
      await waitFor(() => {
        expect(screen.getByText('Laying $50')).toBeInTheDocument();
      });
    });

    it('handles multiple choice market betting flow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockMultiChoiceMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Verify multiple choice market display
      expect(screen.getByText('Multiple Choice')).toBeInTheDocument();
      expect(screen.getByText(`${mockMultiChoiceMarket.outcomes.length} outcomes`)).toBeInTheDocument();
      
      // All outcomes should be displayed
      mockMultiChoiceMarket.outcomes.forEach(outcome => {
        expect(screen.getByText(outcome.outcome_text)).toBeInTheDocument();
      });
      
      // Select second outcome
      const outcomeCard = screen.getByTestId(`outcome-${mockMultiChoiceMarket.outcomes[1].id}`);
      await user.click(outcomeCard);
      
      // Place bet on selected outcome
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '200');
      
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '2.2');
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      await waitFor(() => {
        expect(screen.getByText('Backing $200')).toBeInTheDocument();
      });
    });

    it('prevents betting on settled markets', async () => {
      const user = userEvent.setup();
      const settledMarket = {
        ...mockBinaryMarket,
        status: 'settled' as const,
        winning_outcome_id: 'outcome-1',
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          is_winning_outcome: outcome.id === 'outcome-1'
        }))
      };
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={settledMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Should show settled status
      expect(screen.getByText('Settled')).toBeInTheDocument();
      expect(screen.getByText('Winner')).toBeInTheDocument();
      
      // Outcome cards should not be clickable
      const outcomeCards = screen.getAllByTestId(/^outcome-/);
      outcomeCards.forEach(card => {
        expect(card).not.toHaveClass('cursor-pointer');
      });
      
      // Betting interface should not appear when clicking outcomes
      const winningCard = screen.getByTestId('outcome-outcome-1');
      await user.click(winningCard);
      
      expect(screen.queryByText('Place Your Bet')).not.toBeInTheDocument();
    });

    it('shows user position alert when already participating', async () => {
      const user = userEvent.setup();
      const userParticipation = {
        id: 'participation-1',
        user_id: mockUsers.user1,
        bet_id: mockBinaryMarket.id,
        outcome_id: mockBinaryMarket.outcomes[0].id,
        position_type: 'backing' as const,
        stake_amount: 100,
        desired_odds: 2.5,
        matched_amount: 100,
        status: 'active' as const,
        potential_payout: 250,
        created_at: '2024-07-15T10:30:00Z',
        updated_at: '2024-07-15T10:30:00Z',
        settled_at: null,
        payout_amount: null
      };
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={userParticipation}
          />
        </TestWrapper>
      );

      // Should show existing position in header
      expect(screen.getByText('Your Position')).toBeInTheDocument();
      expect(screen.getByText('Backing')).toBeInTheDocument();
      expect(screen.getByText('$100 stake')).toBeInTheDocument();
      
      // Should highlight user's outcome
      const userOutcomeCard = screen.getByTestId(`outcome-${userParticipation.outcome_id}`);
      expect(userOutcomeCard).toHaveClass('border-primary');
      
      // Select same outcome - should show participation alert
      await user.click(userOutcomeCard);
      
      expect(screen.getByText('You\'re already participating')).toBeInTheDocument();
      expect(screen.getByText(/You have a backing position/)).toBeInTheDocument();
    });
  });

  describe('Betting Error Handling', () => {
    it('handles API errors during bet placement', async () => {
      const user = userEvent.setup();
      
      // Mock API error
      server.use(
        rest.post('http://localhost:8000/api/v1/custom-betting/bets/:id/participate', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ error: 'Insufficient funds' })
          );
        })
      );
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Place bet
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '1000');
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      // Should show error
      await waitFor(() => {
        expect(screen.getByText('Bet placement failed')).toBeInTheDocument();
        expect(screen.getByText('Insufficient funds')).toBeInTheDocument();
      });
      
      // Should have retry button
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    });

    it('handles network errors during bet placement', async () => {
      const user = userEvent.setup();
      
      // Mock network error
      server.use(
        rest.post('http://localhost:8000/api/v1/custom-betting/bets/:id/participate', (req, res) => {
          return res.networkError('Network connection failed');
        })
      );
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      await user.click(placeBetButton);
      
      await waitFor(() => {
        expect(screen.getByText('Network error occurred')).toBeInTheDocument();
        expect(screen.getByText('Please check your connection and try again')).toBeInTheDocument();
      });
    });

    it('validates betting limits before submission', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      // Test below minimum stake
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '1'); // Below minimum of 5
      
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeDisabled();
      
      // Test above maximum stake
      await user.clear(stakeInput);
      await user.type(stakeInput, '15000'); // Above maximum of 10000
      
      expect(placeBetButton).toBeDisabled();
      
      // Test invalid odds
      const oddsInput = screen.getByPlaceholderText('2.00');
      await user.clear(oddsInput);
      await user.type(oddsInput, '1.0'); // Below minimum of 1.01
      
      await user.clear(stakeInput);
      await user.type(stakeInput, '100'); // Valid stake
      
      expect(placeBetButton).toBeDisabled();
    });

    it('handles suspended market betting attempts', async () => {
      const user = userEvent.setup();
      const suspendedMarket = {
        ...mockBinaryMarket,
        status: 'suspended' as const
      };
      
      render(
        <TestWrapper>
          <MockMarketPage 
            market={suspendedMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Should show suspended status
      expect(screen.getByText('Suspended')).toBeInTheDocument();
      expect(screen.getByText('Market Suspended')).toBeInTheDocument();
      
      // Outcomes should be visually disabled
      const outcomeCards = screen.getAllByTestId(/^outcome-/);
      outcomeCards.forEach(card => {
        expect(card).toHaveClass('opacity-75');
      });
    });
  });

  describe('Real-time Updates During Betting', () => {
    it('updates odds during betting process', async () => {
      const user = userEvent.setup();
      
      const { rerender } = render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Start betting process
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      // Initial odds should be displayed
      expect(screen.getByDisplayValue('2.5')).toBeInTheDocument();
      
      // Simulate odds update
      const updatedMarket = {
        ...mockBinaryMarket,
        outcomes: mockBinaryMarket.outcomes.map(outcome => ({
          ...outcome,
          current_odds: outcome.id === 'outcome-1' ? 2.3 : outcome.current_odds
        }))
      };
      
      rerender(
        <TestWrapper>
          <MockMarketPage 
            market={updatedMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );
      
      // Updated odds should be reflected
      expect(screen.getByText('2.3x')).toBeInTheDocument();
    });

    it('handles market status changes during betting', async () => {
      const user = userEvent.setup();
      
      const { rerender } = render(
        <TestWrapper>
          <MockMarketPage 
            market={mockBinaryMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );

      // Start betting
      const outcomeCard = screen.getByTestId(`outcome-${mockBinaryMarket.outcomes[0].id}`);
      await user.click(outcomeCard);
      
      const stakeInput = screen.getByPlaceholderText('0.00');
      await user.type(stakeInput, '100');
      
      // Market gets suspended while user is betting
      const suspendedMarket = {
        ...mockBinaryMarket,
        status: 'suspended' as const
      };
      
      rerender(
        <TestWrapper>
          <MockMarketPage 
            market={suspendedMarket} 
            userParticipation={null}
          />
        </TestWrapper>
      );
      
      // Betting should be disabled
      const placeBetButton = screen.getByRole('button', { name: /place back bet/i });
      expect(placeBetButton).toBeDisabled();
      
      // Should show suspension notice
      expect(screen.getByText('Market Suspended')).toBeInTheDocument();
    });
  });

  describe('User Bet Management Integration', () => {
    it('manages multiple user bets across different markets', async () => {
      const user = userEvent.setup();
      const userBets = [
        {
          id: 'participation-1',
          user_id: mockUsers.user1,
          bet_id: 'market-1',
          outcome_id: 'outcome-1',
          position_type: 'backing' as const,
          stake_amount: 100,
          desired_odds: 2.5,
          matched_amount: 100,
          status: 'active' as const,
          potential_payout: 250,
          created_at: '2024-07-15T10:30:00Z',
          updated_at: '2024-07-15T10:30:00Z',
          settled_at: null,
          payout_amount: null,
          market_title: 'Bitcoin $100k Market'
        },
        {
          id: 'participation-2',
          user_id: mockUsers.user1,
          bet_id: 'market-2',
          outcome_id: 'outcome-3',
          position_type: 'laying' as const,
          stake_amount: 50,
          desired_odds: 2.1,
          matched_amount: 25,
          status: 'pending' as const,
          potential_payout: 50,
          created_at: '2024-07-20T15:45:00Z',
          updated_at: '2024-07-20T15:45:00Z',
          settled_at: null,
          payout_amount: null,
          market_title: 'US Election 2024'
        }
      ];
      
      render(
        <TestWrapper>
          <UserBetManager userBets={userBets} />
        </TestWrapper>
      );

      // Should show all user bets
      expect(screen.getByText('Bitcoin $100k Market')).toBeInTheDocument();
      expect(screen.getByText('US Election 2024')).toBeInTheDocument();
      
      // Should show different statuses
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
      
      // Should show position types
      expect(screen.getByText('Backing')).toBeInTheDocument();
      expect(screen.getByText('Laying')).toBeInTheDocument();
      
      // Should allow cancelling pending bets
      const cancelButton = screen.getByRole('button', { name: /cancel bet/i });
      expect(cancelButton).toBeInTheDocument();
      
      await user.click(cancelButton);
      
      // Should show confirmation dialog
      expect(screen.getByText('Cancel Bet')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to cancel this bet?')).toBeInTheDocument();
    });

    it('updates bet status in real-time', async () => {
      const user = userEvent.setup();
      const initialBet = {
        id: 'participation-1',
        user_id: mockUsers.user1,
        bet_id: 'market-1',
        outcome_id: 'outcome-1',
        position_type: 'backing' as const,
        stake_amount: 100,
        desired_odds: 2.5,
        matched_amount: 50,
        status: 'pending' as const,
        potential_payout: 250,
        created_at: '2024-07-15T10:30:00Z',
        updated_at: '2024-07-15T10:30:00Z',
        settled_at: null,
        payout_amount: null,
        market_title: 'Test Market'
      };
      
      const { rerender } = render(
        <TestWrapper>
          <UserBetManager userBets={[initialBet]} />
        </TestWrapper>
      );

      // Initially pending
      expect(screen.getByText('Pending')).toBeInTheDocument();
      expect(screen.getByText('$50 matched of $100')).toBeInTheDocument();
      
      // Simulate bet getting fully matched
      const matchedBet = {
        ...initialBet,
        matched_amount: 100,
        status: 'active' as const
      };
      
      rerender(
        <TestWrapper>
          <UserBetManager userBets={[matchedBet]} />
        </TestWrapper>
      );
      
      // Should show as active and fully matched
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('$100 matched')).toBeInTheDocument();
    });
  });
});