/**
 * Custom Betting Platform - Market Creation Flow Integration Tests
 * ==============================================================
 * 
 * Integration tests for the complete market creation user flow.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { CreateBetWizard } from '@/components/custom-betting/CreateBetWizard';
import { MarketBrowser } from '@/components/custom-betting/MarketBrowser';
import { mockMarkets } from '../mocks/custom-betting-data';
import { server } from '../setupTests';
import { rest } from 'msw';

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Market Creation Flow Integration', () => {
  let mockOnComplete: jest.Mock;
  let mockOnCancel: jest.Mock;

  beforeEach(() => {
    mockOnComplete = jest.fn();
    mockOnCancel = jest.fn();
  });

  describe('Complete Market Creation Flow', () => {
    it('creates binary market through full wizard flow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Step 1: Basic Information
      await user.type(screen.getByLabelText('Market Title'), 'Will Tesla stock reach $300 by end of 2024?');
      await user.type(
        screen.getByLabelText('Description'), 
        'A binary prediction market on whether Tesla (TSLA) stock will reach or exceed $300 USD by December 31, 2024. Settlement will be based on closing price.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Business'));
      
      const tagsInput = screen.getByPlaceholderText('Add tags (press Enter)');
      await user.type(tagsInput, 'tesla{enter}stocks{enter}ev{enter}');
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Step 2: Define Outcomes (Binary - should have defaults)
      expect(screen.getByText('Step 2 of 5: Define Outcomes')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Yes')).toBeInTheDocument();
      expect(screen.getByDisplayValue('No')).toBeInTheDocument();
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Step 3: Market Settings
      expect(screen.getByText('Step 3 of 5: Market Settings')).toBeInTheDocument();
      
      const minStakeInput = screen.getByLabelText('Minimum Stake');
      await user.clear(minStakeInput);
      await user.type(minStakeInput, '10');
      
      const maxStakeInput = screen.getByLabelText('Maximum Stake');
      await user.clear(maxStakeInput);
      await user.type(maxStakeInput, '5000');
      
      // Set deadline to future date
      const deadlineInput = screen.getByLabelText('Market Deadline');
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 6);
      const dateString = futureDate.toISOString().slice(0, 16);
      await user.clear(deadlineInput);
      await user.type(deadlineInput, dateString);
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Step 4: Validation Criteria
      expect(screen.getByText('Step 4 of 5: Validation Criteria')).toBeInTheDocument();
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput, 
        'Tesla (TSLA) stock must reach or exceed $300.00 per share during regular trading hours on any trading day before the deadline. Price will be verified using Yahoo Finance and Google Finance closing prices. If there are discrepancies, the higher reliable source will be used.'
      );
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Step 5: Review & Submit
      expect(screen.getByText('Step 5 of 5: Review & Submit')).toBeInTheDocument();
      expect(screen.getByText('Will Tesla stock reach $300 by end of 2024?')).toBeInTheDocument();
      expect(screen.getByText('Business')).toBeInTheDocument();
      expect(screen.getByText('$10 - $5,000')).toBeInTheDocument();
      
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      expect(screen.getByText('Creating Market...')).toBeInTheDocument();
      
      // Wait for completion
      await waitFor(() => {
        expect(mockOnComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            id: expect.any(String),
            title: 'Will Tesla stock reach $300 by end of 2024?'
          })
        );
      }, { timeout: 5000 });
    });

    it('creates multiple choice market through full wizard flow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Step 1: Basic Information
      await user.type(screen.getByLabelText('Market Title'), 'Which team will win the 2024 World Cup?');
      await user.type(
        screen.getByLabelText('Description'), 
        'Prediction market for the winner of the 2024 FIFA World Cup tournament. Settlement based on official FIFA results.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Sports'));
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Step 2: Define Outcomes - Select Multiple Choice
      const multipleChoiceOption = screen.getByRole('button', { name: /multiple choice/i });
      await user.click(multipleChoiceOption);
      
      const outcomeInputs = screen.getAllByPlaceholderText('Enter outcome option');
      await user.clear(outcomeInputs[0]);
      await user.type(outcomeInputs[0], 'Brazil');
      await user.clear(outcomeInputs[1]);
      await user.type(outcomeInputs[1], 'Argentina');
      await user.clear(outcomeInputs[2]);
      await user.type(outcomeInputs[2], 'France');
      
      // Add another outcome
      const addOutcomeButton = screen.getByRole('button', { name: /add outcome/i });
      await user.click(addOutcomeButton);
      
      const newOutcomeInputs = screen.getAllByPlaceholderText('Enter outcome option');
      await user.type(newOutcomeInputs[3], 'Germany');
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Complete remaining steps quickly
      await user.click(screen.getByRole('button', { name: /continue/i })); // Settings with defaults
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput,
        'The winning team will be determined by the official FIFA World Cup 2024 final results. Settlement will occur within 24 hours of the final match conclusion based on FIFA official announcements.'
      );
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Review and submit
      expect(screen.getByText('Which team will win the 2024 World Cup?')).toBeInTheDocument();
      expect(screen.getByText('Brazil')).toBeInTheDocument();
      expect(screen.getByText('Argentina')).toBeInTheDocument();
      expect(screen.getByText('France')).toBeInTheDocument();
      expect(screen.getByText('Germany')).toBeInTheDocument();
      
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(mockOnComplete).toHaveBeenCalled();
      });
    });

    it('validates form data across all steps', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Try to proceed without filling required fields
      const continueButton = screen.getByRole('button', { name: /continue/i });
      expect(continueButton).toBeDisabled();
      
      // Fill minimum title but invalid description
      await user.type(screen.getByLabelText('Market Title'), 'Test');
      await user.type(screen.getByLabelText('Description'), 'Too short');
      
      expect(screen.getByText('Description must be at least 50 characters')).toBeInTheDocument();
      expect(continueButton).toBeDisabled();
      
      // Fix description but no category
      await user.clear(screen.getByLabelText('Description'));
      await user.type(
        screen.getByLabelText('Description'), 
        'This is a valid description that meets the minimum character requirements for market creation.'
      );
      
      expect(continueButton).toBeDisabled();
      
      // Add category - should enable continue
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Other'));
      
      expect(continueButton).toBeEnabled();
      
      // Proceed to step 2
      await user.click(continueButton);
      await user.click(screen.getByRole('button', { name: /continue/i })); // Step 2 with defaults
      
      // Step 3: Test invalid stake limits
      const minStakeInput = screen.getByLabelText('Minimum Stake');
      const maxStakeInput = screen.getByLabelText('Maximum Stake');
      
      await user.clear(minStakeInput);
      await user.type(minStakeInput, '100');
      await user.clear(maxStakeInput);
      await user.type(maxStakeInput, '50'); // Max < Min
      
      const step3ContinueButton = screen.getByRole('button', { name: /continue/i });
      expect(step3ContinueButton).toBeDisabled();
      expect(screen.getByText('Maximum stake must be greater than minimum stake')).toBeInTheDocument();
      
      // Fix stake limits
      await user.clear(maxStakeInput);
      await user.type(maxStakeInput, '1000');
      
      expect(step3ContinueButton).toBeEnabled();
    });

    it('handles wizard navigation and data persistence', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Fill step 1
      await user.type(screen.getByLabelText('Market Title'), 'Navigation Test Market');
      await user.type(
        screen.getByLabelText('Description'), 
        'Testing navigation and data persistence in the market creation wizard.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Crypto'));
      
      const tagsInput = screen.getByPlaceholderText('Add tags (press Enter)');
      await user.type(tagsInput, 'test{enter}navigation{enter}');
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Go to step 3
      await user.click(screen.getByRole('button', { name: /continue/i }));
      expect(screen.getByText('Step 3 of 5: Market Settings')).toBeInTheDocument();
      
      // Fill some data in step 3
      const minStakeInput = screen.getByLabelText('Minimum Stake');
      await user.clear(minStakeInput);
      await user.type(minStakeInput, '25');
      
      // Go back to step 1
      const backButton = screen.getByRole('button', { name: /back/i });
      await user.click(backButton); // Step 2
      await user.click(screen.getByRole('button', { name: /back/i })); // Step 1
      
      // Verify data is preserved
      expect(screen.getByDisplayValue('Navigation Test Market')).toBeInTheDocument();
      expect(screen.getByText('Crypto')).toBeInTheDocument();
      expect(screen.getByText('#test')).toBeInTheDocument();
      expect(screen.getByText('#navigation')).toBeInTheDocument();
      
      // Navigate back to step 3
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // Verify step 3 data is preserved
      expect(screen.getByDisplayValue('25')).toBeInTheDocument();
    });

    it('handles wizard cancellation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Fill some data
      await user.type(screen.getByLabelText('Market Title'), 'Cancellation Test');
      
      // Cancel wizard
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
      expect(mockOnComplete).not.toHaveBeenCalled();
    });
  });

  describe('Market Creation Error Handling', () => {
    it('handles API errors during market creation', async () => {
      const user = userEvent.setup();
      
      // Mock API error
      server.use(
        rest.post('http://localhost:8000/api/v1/custom-betting/bets', (req, res, ctx) => {
          return res(
            ctx.status(500),
            ctx.json({ error: 'Internal server error' })
          );
        })
      );
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Complete wizard quickly
      await user.type(screen.getByLabelText('Market Title'), 'Error Test Market');
      await user.type(
        screen.getByLabelText('Description'), 
        'Testing error handling during market creation process with API failures.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Other'));
      
      await user.click(screen.getByRole('button', { name: /continue/i })); // Step 2
      await user.click(screen.getByRole('button', { name: /continue/i })); // Step 3
      await user.click(screen.getByRole('button', { name: /continue/i })); // Step 4
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput,
        'This is a test market designed to trigger API errors for testing error handling in the creation process.'
      );
      await user.click(screen.getByRole('button', { name: /continue/i })); // Step 5
      
      // Try to create market
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      // Should show error state
      await waitFor(() => {
        expect(screen.getByText('Failed to create market')).toBeInTheDocument();
        expect(screen.getByText('Please try again or contact support if the problem persists.')).toBeInTheDocument();
      });
      
      // Should have retry button
      const retryButton = screen.getByRole('button', { name: /retry/i });
      expect(retryButton).toBeInTheDocument();
      
      expect(mockOnComplete).not.toHaveBeenCalled();
    });

    it('handles network errors during market creation', async () => {
      const user = userEvent.setup();
      
      // Mock network error
      server.use(
        rest.post('http://localhost:8000/api/v1/custom-betting/bets', (req, res) => {
          return res.networkError('Network connection failed');
        })
      );
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Complete wizard and submit
      await user.type(screen.getByLabelText('Market Title'), 'Network Error Test');
      await user.type(
        screen.getByLabelText('Description'), 
        'Testing network error handling during market creation with connection failures.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Science'));
      
      // Navigate through steps quickly
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput,
        'This test market will trigger network errors to verify proper error handling in the creation wizard.'
      );
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      // Should show network error
      await waitFor(() => {
        expect(screen.getByText('Network error occurred')).toBeInTheDocument();
        expect(screen.getByText('Please check your connection and try again.')).toBeInTheDocument();
      });
    });

    it('retries market creation after error', async () => {
      const user = userEvent.setup();
      
      // Mock error first, then success
      let callCount = 0;
      server.use(
        rest.post('http://localhost:8000/api/v1/custom-betting/bets', (req, res, ctx) => {
          callCount++;
          if (callCount === 1) {
            return res(ctx.status(500), ctx.json({ error: 'Server error' }));
          }
          return res(
            ctx.status(201),
            ctx.json({
              id: 'retry-test-market',
              title: 'Retry Test Market'
            })
          );
        })
      );
      
      render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnComplete}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      // Complete wizard
      await user.type(screen.getByLabelText('Market Title'), 'Retry Test Market');
      await user.type(
        screen.getByLabelText('Description'), 
        'Testing retry functionality after initial market creation failure.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Entertainment'));
      
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput,
        'Testing retry functionality for market creation after server errors occur during the submission process.'
      );
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      // First attempt - should fail
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to create market')).toBeInTheDocument();
      });
      
      // Retry - should succeed
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(mockOnComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'retry-test-market',
            title: 'Retry Test Market'
          })
        );
      });
    });
  });

  describe('Market Creation and Display Integration', () => {
    it('creates market and verifies it appears in browser', async () => {
      const user = userEvent.setup();
      let createdMarket: any = null;
      
      const mockOnCompleteWithCapture = (market: any) => {
        createdMarket = market;
        mockOnComplete(market);
      };
      
      // Create market
      const { unmount } = render(
        <TestWrapper>
          <CreateBetWizard 
            onComplete={mockOnCompleteWithCapture}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      await user.type(screen.getByLabelText('Market Title'), 'Integration Test Market');
      await user.type(
        screen.getByLabelText('Description'), 
        'Testing integration between market creation and display in the market browser.'
      );
      
      const categorySelect = screen.getByText('Select Category');
      await user.click(categorySelect);
      await user.click(screen.getByText('Politics'));
      
      // Complete creation flow
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const criteriaInput = screen.getByLabelText('Event Criteria');
      await user.type(
        criteriaInput,
        'Integration test market for verifying end-to-end functionality from creation to display in browser.'
      );
      await user.click(screen.getByRole('button', { name: /continue/i }));
      
      const createButton = screen.getByRole('button', { name: /create market/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(createdMarket).toBeTruthy();
      });
      
      unmount();
      
      // Now render market browser and verify the market appears
      render(
        <TestWrapper>
          <MarketBrowser />
        </TestWrapper>
      );
      
      // Should show the created market
      await waitFor(() => {
        expect(screen.getByText('Integration Test Market')).toBeInTheDocument();
        expect(screen.getByText('Politics')).toBeInTheDocument();
      });
    });
  });
});