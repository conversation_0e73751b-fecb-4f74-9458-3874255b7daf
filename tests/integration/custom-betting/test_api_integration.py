"""
Custom Betting Platform - API Integration Tests
==============================================

Integration tests for API endpoints with database operations.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import patch

from tests.conftest import create_mock_token_data


class TestCustomBetsAPIIntegration:
    """Integration tests for Custom Bets API."""
    
    @pytest.mark.asyncio
    async def test_full_bet_creation_workflow(self, test_client, sample_user_data, sample_bet_data):
        """Test complete bet creation workflow with database persistence."""
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            # Create bet
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=sample_bet_data
            )
            
            assert response.status_code == 201
            bet_data = response.json()
            bet_id = bet_data["id"]
            
            # Verify bet can be retrieved
            get_response = await test_client.get(f"/api/v1/custom-bets/{bet_id}")
            assert get_response.status_code == 200
            
            retrieved_bet = get_response.json()
            assert retrieved_bet["id"] == bet_id
            assert retrieved_bet["title"] == sample_bet_data["title"]
            assert len(retrieved_bet["outcomes"]) == 2
    
    @pytest.mark.asyncio
    async def test_bet_to_participant_workflow(self, test_client, sample_user_data, sample_bet_data, sample_participant_data):
        """Test workflow from bet creation to participant joining."""
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            # Create bet
            bet_response = await test_client.post(
                "/api/v1/custom-bets/",
                json=sample_bet_data
            )
            bet_id = bet_response.json()["id"]
            
            # Get bet to find outcome ID
            bet_details = await test_client.get(f"/api/v1/custom-bets/{bet_id}")
            outcome_id = bet_details.json()["outcomes"][0]["id"]
            
            # Join as participant
            participant_response = await test_client.post(
                f"/api/v1/custom-bets/{bet_id}/participants",
                json={
                    **sample_participant_data,
                    "outcome_id": outcome_id
                }
            )
            
            assert participant_response.status_code == 201
            participant_data = participant_response.json()
            assert participant_data["custom_bet_id"] == bet_id
            assert participant_data["outcome_id"] == outcome_id
    
    @pytest.mark.asyncio
    async def test_matching_workflow_integration(self, test_client, sample_user_data, sample_bet_data):
        """Test P2P matching workflow integration."""
        # Create two different users
        user1_data = sample_user_data
        user2_data = {**sample_user_data, "user_id": str(uuid4()), "username": "user2"}
        
        # User 1 creates bet
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(user1_data)
            
            bet_response = await test_client.post(
                "/api/v1/custom-bets/",
                json=sample_bet_data
            )
            bet_id = bet_response.json()["id"]
            
            # Get outcome ID
            bet_details = await test_client.get(f"/api/v1/custom-bets/{bet_id}")
            outcome_id = bet_details.json()["outcomes"][0]["id"]
        
        # User 1 joins as backer
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(user1_data)
            
            await test_client.post(
                f"/api/v1/custom-bets/{bet_id}/participants",
                json={
                    "outcome_id": outcome_id,
                    "stake_amount": "100.00",
                    "position_type": "backing",
                    "desired_odds": "2.0"
                }
            )
        
        # User 2 joins as layer with matching odds
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(user2_data)
            
            layer_response = await test_client.post(
                f"/api/v1/custom-bets/{bet_id}/participants",
                json={
                    "outcome_id": outcome_id,
                    "stake_amount": "100.00",
                    "position_type": "laying",
                    "desired_odds": "2.0"
                }
            )
            
            assert layer_response.status_code == 201
        
        # Check if match was created
        matches_response = await test_client.get(f"/api/v1/custom-bets/{bet_id}/matches")
        assert matches_response.status_code == 200
        
        matches_data = matches_response.json()
        # Should have at least one match if matching engine processed immediately
        # Note: This depends on whether matching is synchronous or asynchronous
    
    @pytest.mark.asyncio
    async def test_settlement_workflow_integration(self, test_client, sample_admin_data, sample_user_data):
        """Test complete settlement workflow."""
        # Create bet that's ready for settlement
        past_deadline = (datetime.now() - timedelta(hours=1)).isoformat()
        past_verification = (datetime.now() - timedelta(minutes=30)).isoformat()
        
        bet_data = {
            "title": "Settlement Test Bet",
            "description": "Ready for settlement",
            "category": "test",
            "bet_type": "binary",
            "minimum_stake": "10.00",
            "maximum_stake": "1000.00",
            "deadline": past_deadline,
            "verification_deadline": past_verification,
            "outcomes": [
                {"outcome_text": "Option A", "initial_odds": "2.0"},
                {"outcome_text": "Option B", "initial_odds": "2.0"}
            ]
        }
        
        # Create bet
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            bet_response = await test_client.post(
                "/api/v1/custom-bets/",
                json=bet_data
            )
            bet_id = bet_response.json()["id"]
            outcome_id = bet_response.json()["outcomes"][0]["id"]
        
        # Submit outcome verification
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            verification_response = await test_client.post(
                f"/api/v1/settlements/verify/{bet_id}",
                json={
                    "winning_outcome_id": outcome_id,
                    "evidence_description": "Clear evidence",
                    "evidence_urls": ["https://example.com/evidence"],
                    "confidence_score": 0.95
                }
            )
            
            assert verification_response.status_code == 201
        
        # Process settlement as admin
        with patch("app.api.dependencies.get_admin_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_admin_data)
            
            settlement_response = await test_client.post(
                f"/api/v1/settlements/settle/{bet_id}",
                json={
                    "force_settlement": False
                }
            )
            
            assert settlement_response.status_code == 200
            settlement_data = settlement_response.json()
            assert settlement_data["bet_id"] == bet_id
            assert "settlement_status" in settlement_data
    
    @pytest.mark.asyncio
    async def test_dispute_workflow_integration(self, test_client, sample_admin_data, sample_user_data):
        """Test dispute filing and resolution workflow."""
        # Setup settled bet scenario (similar to settlement test)
        past_deadline = (datetime.now() - timedelta(hours=1)).isoformat()
        past_verification = (datetime.now() - timedelta(minutes=30)).isoformat()
        
        bet_data = {
            "title": "Dispute Test Bet",
            "description": "Will be disputed",
            "category": "test",
            "bet_type": "binary",
            "minimum_stake": "10.00",
            "maximum_stake": "1000.00",
            "deadline": past_deadline,
            "verification_deadline": past_verification,
            "outcomes": [
                {"outcome_text": "Option A", "initial_odds": "2.0"},
                {"outcome_text": "Option B", "initial_odds": "2.0"}
            ]
        }
        
        # Create and verify bet
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            bet_response = await test_client.post(
                "/api/v1/custom-bets/",
                json=bet_data
            )
            bet_id = bet_response.json()["id"]
            outcome_id = bet_response.json()["outcomes"][0]["id"]
            
            # Submit verification
            await test_client.post(
                f"/api/v1/settlements/verify/{bet_id}",
                json={
                    "winning_outcome_id": outcome_id,
                    "evidence_description": "Initial evidence",
                    "evidence_urls": [],
                    "confidence_score": 0.8
                }
            )
        
        # File dispute
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            dispute_response = await test_client.post(
                f"/api/v1/settlements/dispute/{bet_id}",
                json={
                    "dispute_type": "incorrect_outcome",
                    "dispute_reason": "Evidence shows different result",
                    "evidence_description": "Counter-evidence",
                    "evidence_urls": ["https://example.com/counter"]
                }
            )
            
            assert dispute_response.status_code == 201
            dispute_data = dispute_response.json()
            dispute_id = dispute_data["id"]
        
        # Resolve dispute as admin
        with patch("app.api.dependencies.get_admin_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_admin_data)
            
            resolution_response = await test_client.post(
                f"/api/v1/settlements/dispute/{dispute_id}/resolve",
                json={
                    "resolution_method": "admin_review",
                    "resolution_decision": "upheld",
                    "final_outcome": outcome_id,
                    "refund_amount": 0
                }
            )
            
            assert resolution_response.status_code == 200
            resolution_data = resolution_response.json()
            assert resolution_data["dispute_id"] == dispute_id
    
    @pytest.mark.asyncio
    async def test_market_discovery_integration(self, test_client, sample_user_data):
        """Test market discovery and search functionality."""
        # Create multiple bets with different categories
        bet_categories = ["politics", "sports", "entertainment"]
        created_bets = []
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            for i, category in enumerate(bet_categories):
                bet_data = {
                    "title": f"Test {category.title()} Bet {i}",
                    "description": f"A {category} betting market",
                    "category": category,
                    "bet_type": "binary",
                    "minimum_stake": "10.00",
                    "maximum_stake": "1000.00",
                    "deadline": (datetime.now() + timedelta(days=30)).isoformat(),
                    "verification_deadline": (datetime.now() + timedelta(days=35)).isoformat(),
                    "outcomes": [
                        {"outcome_text": "Yes", "initial_odds": "2.0"},
                        {"outcome_text": "No", "initial_odds": "2.0"}
                    ],
                    "is_featured": (i == 0)  # Make first bet featured
                }
                
                response = await test_client.post(
                    "/api/v1/custom-bets/",
                    json=bet_data
                )
                created_bets.append(response.json())
        
        # Test market browsing
        browse_response = await test_client.get("/api/v1/markets/browse")
        assert browse_response.status_code == 200
        browse_data = browse_response.json()
        assert len(browse_data["bets"]) == 3
        
        # Test category filtering
        sports_response = await test_client.get("/api/v1/markets/browse?category=sports")
        assert sports_response.status_code == 200
        sports_data = sports_response.json()
        assert len(sports_data["bets"]) == 1
        assert sports_data["bets"][0]["category"] == "sports"
        
        # Test search functionality
        search_response = await test_client.get("/api/v1/markets/search?q=politics")
        assert search_response.status_code == 200
        search_data = search_response.json()
        assert len(search_data["bets"]) >= 1
        
        # Test featured markets
        # Note: This endpoint might need to be implemented based on requirements
    
    @pytest.mark.asyncio
    async def test_user_permissions_integration(self, test_client, sample_user_data, sample_admin_data):
        """Test user permission enforcement across endpoints."""
        # Create bet as regular user
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            bet_response = await test_client.post(
                "/api/v1/custom-bets/",
                json={
                    "title": "Permission Test Bet",
                    "description": "Testing permissions",
                    "category": "test",
                    "bet_type": "binary",
                    "minimum_stake": "10.00",
                    "maximum_stake": "1000.00",
                    "deadline": (datetime.now() + timedelta(days=1)).isoformat(),
                    "verification_deadline": (datetime.now() + timedelta(days=2)).isoformat(),
                    "outcomes": [
                        {"outcome_text": "Yes", "initial_odds": "2.0"},
                        {"outcome_text": "No", "initial_odds": "2.0"}
                    ]
                }
            )
            bet_id = bet_response.json()["id"]
        
        # Try admin-only endpoint as regular user (should fail)
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            admin_response = await test_client.get("/api/v1/settlements/pending")
            assert admin_response.status_code == 403  # Forbidden
        
        # Try same endpoint as admin (should succeed)
        with patch("app.api.dependencies.get_admin_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_admin_data)
            
            admin_response = await test_client.get("/api/v1/settlements/pending")
            assert admin_response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, test_client, sample_user_data):
        """Test error handling across API endpoints."""
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            # Test 404 for non-existent bet
            fake_id = str(uuid4())
            response = await test_client.get(f"/api/v1/custom-bets/{fake_id}")
            assert response.status_code == 404
            
            # Test validation error for invalid data
            invalid_bet = {
                "title": "",  # Empty title
                "category": "invalid",
                "bet_type": "unknown_type"
            }
            
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=invalid_bet
            )
            assert response.status_code == 422  # Validation error
            
            # Test unauthorized access
            # (Remove auth mock to simulate unauthorized request)
        
        # Test unauthenticated request
        response = await test_client.get("/api/v1/custom-bets/")
        assert response.status_code == 401  # Unauthorized


class TestWebSocketIntegration:
    """Integration tests for WebSocket functionality."""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_lifecycle(self, test_client):
        """Test WebSocket connection establishment and cleanup."""
        # Note: WebSocket testing with httpx/FastAPI can be complex
        # This is a placeholder for WebSocket integration tests
        
        # Test would involve:
        # 1. Establishing WebSocket connection
        # 2. Sending/receiving messages
        # 3. Testing real-time updates
        # 4. Verifying connection cleanup
        
        # For now, we'll test the HTTP endpoints that support WebSocket functionality
        fake_bet_id = str(uuid4())
        fake_user_id = str(uuid4())
        
        # Test that WebSocket routes are registered (would return 400 for HTTP request)
        response = await test_client.get(f"/ws/custom-betting/{fake_bet_id}")
        # Expected: Method not allowed or similar for HTTP request to WebSocket endpoint
        assert response.status_code in [405, 400, 426]  # Various "wrong protocol" responses


class TestDatabaseConsistency:
    """Integration tests for database consistency and transactions."""
    
    @pytest.mark.asyncio
    async def test_transaction_rollback_on_error(self, test_client, sample_user_data):
        """Test that database transactions are rolled back on errors."""
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            # This test would need to simulate a scenario where an error occurs
            # mid-transaction to verify rollback behavior
            
            # For example, create a bet that should fail validation during processing
            # and verify that no partial data is left in the database
            
            initial_count_response = await test_client.get("/api/v1/custom-bets/")
            initial_count = len(initial_count_response.json().get("bets", []))
            
            # Attempt operation that should fail
            invalid_data = {
                "title": "Test Bet",
                "description": "Will fail",
                "category": "test",
                "bet_type": "binary",
                "minimum_stake": "10.00",
                "maximum_stake": "5.00",  # Max less than min - should fail
                "deadline": (datetime.now() + timedelta(days=1)).isoformat(),
                "verification_deadline": (datetime.now() + timedelta(days=2)).isoformat(),
                "outcomes": [
                    {"outcome_text": "Yes", "initial_odds": "2.0"},
                    {"outcome_text": "No", "initial_odds": "2.0"}
                ]
            }
            
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=invalid_data
            )
            
            # Should fail validation
            assert response.status_code == 422
            
            # Verify no bet was created
            final_count_response = await test_client.get("/api/v1/custom-bets/")
            final_count = len(final_count_response.json().get("bets", []))
            
            assert final_count == initial_count  # No change in count