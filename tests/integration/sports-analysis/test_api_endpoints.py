"""
Integration tests for API endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from datetime import datetime
from uuid import uuid4

from main import app


class TestChatAPIEndpoints:
    """Test chat API endpoints integration."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "service" in data
        assert "version" in data
        assert "timestamp" in data
        assert data["status"] == "healthy"
        assert data["service"] == "sports-analysis-ai-chat"
    
    def test_ready_endpoint(self, client):
        """Test readiness check endpoint.""" 
        response = client.get("/ready")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "checks" in data
        assert data["status"] == "ready"
        
        # Check component status
        checks = data["checks"]
        assert "database" in checks
        assert "api_football" in checks
        assert "rag_engine" in checks
        assert "websocket" in checks
    
    def test_root_endpoint(self, client):
        """Test root endpoint with service information."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "service" in data
        assert "version" in data
        assert "api" in data
        assert "websocket" in data
        assert "features" in data
        
        # Check API endpoints listed
        api_endpoints = data["api"]
        assert "analysis" in api_endpoints
        assert "chat" in api_endpoints
        assert "streaming" in api_endpoints
        assert "predictions" in api_endpoints
        
        # Check WebSocket endpoints listed
        ws_endpoints = data["websocket"]
        assert "live_matches" in ws_endpoints
        assert "chat_sessions" in ws_endpoints
        
        # Check features
        features = data["features"]
        assert features["api_football_integration"] is True
        assert features["rag_processing"] is True
        assert features["vector_search"] is True


class TestStreamingAPIEndpoints:
    """Test streaming API endpoints integration."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_streaming_health_endpoint(self, client):
        """Test streaming service health check."""
        response = client.get("/streaming/health")
        
        # Should return health status even if some components are mocked
        assert response.status_code in [200, 503]
        data = response.json()
        
        assert "status" in data
        assert "service" in data
        assert "components" in data
        assert data["service"] == "sports-streaming"
        
        if response.status_code == 200:
            assert data["status"] == "healthy"
        else:
            assert data["status"] == "unhealthy"


class TestPredictionsAPIEndpoints:
    """Test predictions API endpoints integration."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_predictions_health_endpoint(self, client):
        """Test predictions service health check."""
        response = client.get("/predictions/health")
        
        # Should return health status even if some components are mocked
        assert response.status_code in [200, 503]
        data = response.json()
        
        assert "status" in data
        assert "service" in data
        assert "components" in data
        assert data["service"] == "sports-predictions"
        
        if response.status_code == 200:
            assert data["status"] == "healthy"
        else:
            assert data["status"] == "unhealthy"


class TestAPIDocumentation:
    """Test API documentation endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_openapi_schema(self, client):
        """Test OpenAPI schema generation."""
        response = client.get("/openapi.json")
        
        assert response.status_code == 200
        schema = response.json()
        
        # Check OpenAPI schema structure
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        assert "components" in schema
        
        # Check service info
        info = schema["info"]
        assert info["title"] == "BetBet Sports Analysis & AI Chat"
        assert "version" in info
        
        # Check that main endpoints are documented
        paths = schema["paths"]
        assert "/" in paths
        assert "/health" in paths
        assert "/ready" in paths
    
    def test_swagger_ui(self, client):
        """Test Swagger UI accessibility."""
        response = client.get("/docs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # Should contain Swagger UI content
        content = response.text
        assert "swagger-ui" in content.lower()
    
    def test_redoc_ui(self, client):
        """Test ReDoc UI accessibility."""
        response = client.get("/redoc")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # Should contain ReDoc content
        content = response.text
        assert "redoc" in content.lower()


class TestCORSAndSecurity:
    """Test CORS and security middleware."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_cors_headers(self, client):
        """Test CORS headers are present."""
        # Test preflight request
        response = client.options(
            "/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET"
            }
        )
        
        # Should handle CORS
        assert response.status_code in [200, 405]  # 405 is OK for OPTIONS if not specifically handled
    
    def test_trusted_host_middleware(self, client):
        """Test trusted host middleware."""
        # Test with allowed host
        response = client.get("/health", headers={"Host": "localhost"})
        assert response.status_code == 200
    
    def test_security_headers(self, client):
        """Test security headers in responses."""
        response = client.get("/health")
        
        # Should have security-related headers
        headers = response.headers
        
        # Check for timing header (added by middleware)
        assert "x-process-time" in headers


class TestErrorHandling:
    """Test error handling and exception responses."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_404_error_handling(self, client):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        
        # Should have error structure
        assert "detail" in data
    
    def test_method_not_allowed(self, client):
        """Test method not allowed error."""
        response = client.delete("/health")  # DELETE not allowed on health endpoint
        
        assert response.status_code == 405
        data = response.json()
        
        assert "detail" in data


class TestRequestLogging:
    """Test request logging middleware."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_request_timing_header(self, client):
        """Test that requests include timing header."""
        response = client.get("/health")
        
        assert response.status_code == 200
        assert "x-process-time" in response.headers
        
        # Should be a valid timing value
        process_time = float(response.headers["x-process-time"])
        assert process_time >= 0
        assert process_time < 10  # Should be less than 10 seconds for health check