"""
Integration tests for Chat API endpoints.
"""

import pytest
import asyncio
from datetime import datetime
from uuid import uuid4
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from main import app


class TestChatEndpoints:
    """Test cases for chat API endpoints."""
    
    @pytest.fixture
    async def client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer mock-jwt-token"}
    
    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return {
            'id': uuid4(),
            'username': 'testuser',
            'email': '<EMAIL>'
        }
    
    @pytest.mark.asyncio
    async def test_create_chat_session(self, client, auth_headers, mock_user):
        """Test creating a new chat session."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db:
            
            # Mock database session
            mock_db.return_value.__aenter__.return_value.add = AsyncMock()
            mock_db.return_value.__aenter__.return_value.commit = AsyncMock()
            mock_db.return_value.__aenter__.return_value.refresh = AsyncMock()
            
            session_data = {
                "title": "Premier League Analysis",
                "session_type": "sports_analysis",
                "rag_enabled": True,
                "ai_model": "gpt-4",
                "temperature": 0.7
            }
            
            response = await client.post(
                "/chat/sessions",
                json=session_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert 'id' in data
            assert data['title'] == session_data['title']
            assert data['session_type'] == session_data['session_type']
            assert data['status'] == 'active'
            assert 'configuration' in data
    
    @pytest.mark.asyncio
    async def test_get_user_chat_sessions(self, client, auth_headers, mock_user):
        """Test getting user's chat sessions."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db:
            
            # Mock database query
            mock_sessions = [
                {
                    'id': uuid4(),
                    'session_title': 'Session 1',
                    'session_type': 'sports_analysis',
                    'status': 'active',
                    'message_count': 5,
                    'created_at': datetime.utcnow(),
                    'last_activity_at': datetime.utcnow()
                },
                {
                    'id': uuid4(),
                    'session_title': 'Session 2',
                    'session_type': 'general',
                    'status': 'active',
                    'message_count': 10,
                    'created_at': datetime.utcnow(),
                    'last_activity_at': datetime.utcnow()
                }
            ]
            
            mock_query = AsyncMock()
            mock_query.count.return_value = 2
            mock_query.all.return_value = mock_sessions
            mock_db.return_value.__aenter__.return_value.query.return_value = mock_query
            
            response = await client.get(
                "/chat/sessions?page=1&limit=10",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert 'sessions' in data
            assert len(data['sessions']) == 2
            assert data['total_count'] == 2
            assert data['page'] == 1
    
    @pytest.mark.asyncio
    async def test_send_chat_message(self, client, auth_headers, mock_user):
        """Test sending a chat message."""
        session_id = uuid4()
        
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Mock chat session
            mock_session = AsyncMock()
            mock_session.id = session_id
            mock_session.user_id = mock_user['id']
            mock_session.status = 'active'
            mock_session.rag_enabled = True
            mock_session.message_count = 0
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            
            # Mock chat processor
            mock_chat_result = {
                'response': 'Arsenal and Chelsea are the main London teams playing this Saturday.',
                'response_type': 'informative',
                'rag_used': True,
                'retrieval_results': [
                    {
                        'content': 'Arsenal vs Newcastle - Saturday 3PM',
                        'similarity': 0.85
                    }
                ],
                'similarity_scores': [0.85],
                'avg_similarity_score': 0.85,
                'tokens_used': 150,
                'cost': 0.003,
                'confidence': 0.9,
                'entities': ['Arsenal', 'Chelsea'],
                'topics': ['Premier League', 'London teams'],
                'follow_ups': ['Would you like to see the full fixture list?']
            }
            
            mock_processor.return_value.process_message = AsyncMock(return_value=mock_chat_result)
            
            message_data = {
                "message": "Which London teams play this Saturday?",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/message",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data['user_message'] == message_data['message']
            assert 'ai_response' in data
            assert data['rag_used'] is True
            assert len(data['retrieval_sources']) > 0
            assert data['confidence_score'] > 0.5
    
    @pytest.mark.asyncio
    async def test_stream_chat_response(self, client, auth_headers, mock_user):
        """Test streaming chat response."""
        session_id = uuid4()
        
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db, \
             patch('app.core.chat_processor.get_chat_processor') as mock_processor:
            
            # Mock chat session
            mock_session = AsyncMock()
            mock_session.id = session_id
            mock_session.user_id = mock_user['id']
            mock_session.status = 'active'
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_session
            
            # Mock streaming response
            async def mock_stream():
                chunks = [
                    {'type': 'thinking', 'content': 'Analyzing your question...'},
                    {'type': 'retrieval', 'content': 'Found relevant information'},
                    {'type': 'response', 'content': 'Arsenal plays at home'},
                    {'type': 'response', 'content': ' this Saturday.'},
                    {'type': 'complete', 'content': ''}
                ]
                for chunk in chunks:
                    yield chunk
            
            mock_processor.return_value.stream_message = mock_stream
            
            message_data = {
                "message": "Does Arsenal play at home?",
                "context": {}
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/stream",
                json=message_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            assert response.headers['content-type'] == 'text/plain; charset=utf-8'
    
    @pytest.mark.asyncio
    async def test_submit_message_feedback(self, client, auth_headers, mock_user):
        """Test submitting feedback for AI response."""
        session_id = uuid4()
        message_id = uuid4()
        
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies.get_database_session_dep') as mock_db:
            
            # Mock AI response
            mock_response = AsyncMock()
            mock_response.id = message_id
            mock_response.session_id = session_id
            mock_response.user_id = mock_user['id']
            
            mock_db.return_value.__aenter__.return_value.query.return_value.filter.return_value.first.return_value = mock_response
            
            feedback_data = {
                "rating": 5,
                "comment": "Very helpful and accurate information!"
            }
            
            response = await client.post(
                f"/chat/sessions/{session_id}/messages/{message_id}/feedback",
                json=feedback_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data['message'] == "Feedback submitted successfully"
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, client, auth_headers, mock_user):
        """Test rate limiting on chat endpoints."""
        with patch('app.api.dependencies.require_user_id', return_value=mock_user['id']), \
             patch('app.api.dependencies.get_current_user', return_value=mock_user), \
             patch('app.api.dependencies._rate_limiter.is_rate_limited', return_value=True):
            
            response = await client.post(
                "/chat/sessions",
                json={"title": "Test"},
                headers=auth_headers
            )
            
            assert response.status_code == 429
            assert "Rate limit exceeded" in response.json()['detail']
    
    @pytest.mark.asyncio
    async def test_chat_health_check(self, client):
        """Test chat service health check."""
        with patch('app.core.chat_processor.get_chat_processor') as mock_processor, \
             patch('app.core.rag_engine.get_rag_engine') as mock_rag:
            
            mock_processor.return_value.health_check = AsyncMock(return_value="healthy")
            mock_rag.return_value.health_check = AsyncMock(return_value="healthy")
            
            response = await client.get("/chat/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data['status'] == "healthy"
            assert data['service'] == "sports-chat"
            assert 'components' in data
            assert data['components']['chat_processor'] == "healthy"
            assert data['components']['rag_engine'] == "healthy"