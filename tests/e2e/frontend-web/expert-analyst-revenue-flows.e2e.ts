/**
 * Expert Analyst Revenue-Critical E2E Tests
 * ========================================
 * 
 * End-to-end tests for the most critical revenue-generating user journeys
 * in the Expert Analyst marketplace. These tests ensure the subscription
 * and payment flows work flawlessly across different scenarios.
 */

import { test, expect, Page } from '@playwright/test';
import { mockExperts, mockPicks, mockSubscriptions } from '../expert-analyst/mocks/expert-analyst-data';

// Test environment configuration
const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.PLAYWRIGHT_API_BASE_URL || 'http://localhost:8003';

// Mock user data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

const testPaymentInfo = {
  cardNumber: '****************',
  expiry: '12/25',
  cvc: '123',
  name: 'Test User',
  zip: '12345'
};

test.describe('Expert Analyst Revenue Flows', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route(`${API_BASE_URL}/api/v1/experts`, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          experts: Object.values(mockExperts),
          total_count: Object.values(mockExperts).length,
          page: 1,
          limit: 20,
          has_next: false,
          has_prev: false
        })
      });
    });

    await page.route(`${API_BASE_URL}/api/v1/experts/expert-1`, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockExperts.topPerformer)
      });
    });

    await page.route(`${API_BASE_URL}/api/v1/subscriptions`, async (route) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            subscriptions: [],
            total_count: 0
          })
        });
      }
    });
  });

  test.describe('Expert Discovery to Subscription Flow', () => {
    test('should complete full subscription journey for new user', async ({ page, context }) => {
      // Enable geolocation for better UX testing
      await context.grantPermissions(['geolocation']);

      // Step 1: Navigate to expert marketplace
      await page.goto(`${BASE_URL}/experts`);
      
      // Verify marketplace loads
      await expect(page.locator('h1')).toContainText('Expert Analysts Marketplace');
      
      // Verify expert cards are displayed
      await expect(page.locator('[data-testid="expert-card"]')).toHaveCount(4);
      
      // Find and verify Michael Johnson's card
      const michaelCard = page.locator('[data-testid="expert-card"]').filter({
        hasText: 'Michael Johnson'
      });
      
      await expect(michaelCard).toBeVisible();
      await expect(michaelCard.locator('.win-rate')).toContainText('72%');
      await expect(michaelCard.locator('.roi')).toContainText('18.5%');
      await expect(michaelCard.locator('.streak')).toContainText('12 win streak');

      // Step 2: Click to view expert details
      await michaelCard.click();
      
      // Wait for navigation and page load
      await page.waitForURL('**/experts/expert-1');
      await expect(page.locator('h1')).toContainText('Michael Johnson');
      
      // Verify expert profile details
      await expect(page.locator('[data-testid="verification-badge"]')).toBeVisible();
      await expect(page.locator('.expert-rating')).toContainText('4.9');
      await expect(page.locator('.subscriber-count')).toContainText('3,450');

      // Step 3: Examine subscription tiers
      const subscriptionTiers = page.locator('[data-testid="subscription-tier"]');
      await expect(subscriptionTiers).toHaveCount(3);
      
      // Verify tier pricing and features
      const premiumTier = subscriptionTiers.filter({ hasText: 'Premium' });
      await expect(premiumTier.locator('.price')).toContainText('$149.99');
      await expect(premiumTier.locator('.features')).toContainText('Real-time notifications');
      
      // Step 4: Start subscription process (Premium tier)
      await premiumTier.locator('button', { hasText: 'Subscribe' }).click();
      
      // Handle authentication requirement
      await expect(page.locator('[data-testid="auth-modal"]')).toBeVisible();
      
      // Mock successful authentication
      await page.route('**/api/auth/signin', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            user: { id: 'user-test', email: testUser.email },
            token: 'mock-jwt-token'
          })
        });
      });
      
      // Fill auth form
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.click('[data-testid="signin-button"]');
      
      // Step 5: Payment flow
      // Mock payment intent creation
      await page.route(`${API_BASE_URL}/api/v1/payments/create-intent`, async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            client_secret: 'pi_test_secret_123',
            payment_intent_id: 'pi_test_123',
            amount: 14999,
            currency: 'usd'
          })
        });
      });
      
      // Wait for payment modal
      await expect(page.locator('[data-testid="payment-modal"]')).toBeVisible();
      await expect(page.locator('.subscription-summary')).toContainText('Premium - $149.99/month');
      
      // Fill payment details
      const cardFrame = page.frameLocator('[data-testid="stripe-card-frame"]');
      await cardFrame.fill('[data-testid="card-number"]', testPaymentInfo.cardNumber);
      await cardFrame.fill('[data-testid="card-expiry"]', testPaymentInfo.expiry);
      await cardFrame.fill('[data-testid="card-cvc"]', testPaymentInfo.cvc);
      
      await page.fill('[data-testid="cardholder-name"]', testPaymentInfo.name);
      await page.fill('[data-testid="billing-email"]', testUser.email);
      
      // Mock successful payment
      await page.route(`${API_BASE_URL}/api/v1/payments/confirm/*`, async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            subscription: {
              id: 'sub-new',
              user_id: 'user-test',
              expert_id: 'expert-1',
              tier_id: 'tier-1-premium',
              status: 'active'
            },
            status: 'succeeded'
          })
        });
      });
      
      // Submit payment
      await page.click('[data-testid="pay-button"]');
      
      // Step 6: Verify success
      await expect(page.locator('[data-testid="success-modal"]')).toBeVisible();
      await expect(page.locator('.success-title')).toContainText('Subscription Successful!');
      await expect(page.locator('.success-message')).toContainText('Welcome to Premium');
      
      // Navigate to picks feed
      await page.click('[data-testid="view-picks-button"]');
      await page.waitForURL('**/picks');
      
      // Verify picks feed loads with subscription
      await expect(page.locator('h1')).toContainText('Expert Picks Feed');
      await expect(page.locator('.active-subscriptions')).toContainText('Michael Johnson - Premium');
    });

    test('should handle payment failures gracefully', async ({ page }) => {
      await page.goto(`${BASE_URL}/experts/expert-1`);
      
      // Mock user already signed in
      await page.addInitScript(() => {
        localStorage.setItem('auth_token', 'mock-token');
        localStorage.setItem('user_id', 'test-user');
      });
      
      // Click subscribe on basic tier
      const basicTier = page.locator('[data-testid="subscription-tier"]').filter({
        hasText: 'Basic'
      });
      
      await basicTier.locator('button', { hasText: 'Subscribe' }).click();
      
      // Fill payment details
      await page.waitForSelector('[data-testid="payment-modal"]');
      await page.fill('[data-testid="cardholder-name"]', testPaymentInfo.name);
      
      // Mock payment failure
      await page.route(`${API_BASE_URL}/api/v1/payments/confirm/*`, async (route) => {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'card_declined',
            message: 'Your card was declined.'
          })
        });
      });
      
      await page.click('[data-testid="pay-button"]');
      
      // Verify error handling
      await expect(page.locator('[data-testid="payment-error"]')).toBeVisible();
      await expect(page.locator('.error-message')).toContainText('Your card was declined');
      await expect(page.locator('[data-testid="retry-payment"]')).toBeVisible();
      
      // Verify modal stays open for retry
      await expect(page.locator('[data-testid="payment-modal"]')).toBeVisible();
    });

    test('should prevent duplicate subscriptions', async ({ page }) => {
      // Mock existing subscription
      await page.route(`${API_BASE_URL}/api/v1/subscriptions`, async (route) => {
        if (route.request().method() === 'GET') {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              subscriptions: [{
                id: 'existing-sub',
                expert_id: 'expert-1',
                tier_id: 'tier-1-basic',
                status: 'active'
              }],
              total_count: 1
            })
          });
        }
      });
      
      await page.goto(`${BASE_URL}/experts/expert-1`);
      
      // Verify active subscription display
      await expect(page.locator('[data-testid="active-subscription"]')).toBeVisible();
      await expect(page.locator('.subscription-status')).toContainText('Basic Plan');
      
      // Verify subscribe buttons are replaced with upgrade options
      await expect(page.locator('button', { hasText: 'Subscribe' })).toHaveCount(0);
      await expect(page.locator('button', { hasText: 'Upgrade' })).toHaveCount(2);
      
      // Verify manage subscription link
      await expect(page.locator('[data-testid="manage-subscription"]')).toBeVisible();
    });
  });

  test.describe('Pick Consumption Flow', () => {
    test.beforeEach(async ({ page }) => {
      // Mock active subscriptions
      await page.route(`${API_BASE_URL}/api/v1/subscriptions`, async (route) => {
        if (route.request().method() === 'GET') {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              subscriptions: mockSubscriptions,
              total_count: mockSubscriptions.length
            })
          });
        }
      });
      
      // Mock picks feed
      await page.route(`${API_BASE_URL}/api/v1/picks*`, async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            picks: mockPicks,
            total_count: mockPicks.length
          })
        });
      });
      
      // Mock user dashboard
      await page.route(`${API_BASE_URL}/api/v1/analytics/dashboard`, async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            subscriptions: mockSubscriptions,
            recent_picks: mockPicks.slice(0, 3),
            performance_summary: {
              total_invested: 10000,
              total_profit: 1850,
              roi: 0.185,
              win_rate: 0.68
            }
          })
        });
      });
      
      // Mock authentication
      await page.addInitScript(() => {
        localStorage.setItem('auth_token', 'mock-token');
        localStorage.setItem('user_id', 'user-123');
      });
    });

    test('should display picks feed with active subscriptions', async ({ page }) => {
      await page.goto(`${BASE_URL}/picks`);
      
      // Verify page loads
      await expect(page.locator('h1')).toContainText('Expert Picks Feed');
      
      // Verify active subscriptions display
      await expect(page.locator('[data-testid="active-subscriptions"]')).toBeVisible();
      await expect(page.locator('.subscription-item')).toHaveCount(2);
      
      // Verify picks display
      await expect(page.locator('[data-testid="pick-card"]')).toHaveCount(6);
      
      // Verify pick details
      const firstPick = page.locator('[data-testid="pick-card"]').first();
      await expect(firstPick.locator('.pick-title')).toBeVisible();
      await expect(firstPick.locator('.expert-name')).toBeVisible();
      await expect(firstPick.locator('.confidence-level')).toBeVisible();
      await expect(firstPick.locator('.pick-category')).toBeVisible();
      
      // Verify performance summary
      await expect(page.locator('[data-testid="performance-summary"]')).toBeVisible();
      await expect(page.locator('.roi-display')).toContainText('18.5%');
      await expect(page.locator('.win-rate-display')).toContainText('68%');
    });

    test('should filter and sort picks correctly', async ({ page }) => {
      await page.goto(`${BASE_URL}/picks`);
      
      // Test expert filter
      await page.selectOption('[data-testid="expert-filter"]', 'expert-1');
      
      // Verify API call is made
      await page.waitForRequest(request => 
        request.url().includes('/api/v1/picks') && 
        request.url().includes('expert_id=expert-1')
      );
      
      // Test category filter
      await page.selectOption('[data-testid="category-filter"]', 'NFL');
      
      await page.waitForRequest(request => 
        request.url().includes('category=NFL')
      );
      
      // Test status filter
      await page.selectOption('[data-testid="status-filter"]', 'pending');
      
      await page.waitForRequest(request => 
        request.url().includes('status=pending')
      );
      
      // Test sorting
      await page.selectOption('[data-testid="sort-select"]', 'confidence');
      
      await page.waitForRequest(request => 
        request.url().includes('sort_by=confidence')
      );
    });

    test('should open pick details modal', async ({ page }) => {
      await page.goto(`${BASE_URL}/picks`);
      
      // Click on first pick
      const firstPick = page.locator('[data-testid="pick-card"]').first();
      await firstPick.click();
      
      // Verify modal opens
      await expect(page.locator('[data-testid="pick-details-modal"]')).toBeVisible();
      await expect(page.locator('.modal-title')).toContainText('Pick Details');
      
      // Verify pick information
      await expect(page.locator('.pick-description')).toBeVisible();
      await expect(page.locator('.stake-recommendation')).toBeVisible();
      await expect(page.locator('.odds-display')).toBeVisible();
      
      // Verify action buttons
      await expect(page.locator('[data-testid="favorite-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="share-button"]')).toBeVisible();
      
      // Close modal
      await page.click('[data-testid="close-modal"]');
      await expect(page.locator('[data-testid="pick-details-modal"]')).not.toBeVisible();
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should work correctly on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE
      
      await page.goto(`${BASE_URL}/experts`);
      
      // Verify mobile layout
      await expect(page.locator('[data-testid="mobile-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible();
      
      // Verify expert cards adapt to mobile
      const expertCard = page.locator('[data-testid="expert-card"]').first();
      await expect(expertCard).toBeVisible();
      
      // Test mobile navigation
      await page.click('[data-testid="mobile-menu-toggle"]');
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
      
      // Navigate to expert detail
      await expertCard.click();
      await page.waitForURL('**/experts/**');
      
      // Verify mobile expert page
      await expect(page.locator('.expert-profile')).toBeVisible();
      
      // Test mobile subscription flow
      const subscribeButton = page.locator('button', { hasText: 'Subscribe' }).first();
      await subscribeButton.click();
      
      // Verify mobile payment modal
      await expect(page.locator('[data-testid="mobile-payment-modal"]')).toBeVisible();
    });

    test('should handle touch interactions properly', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }); // iPad
      
      await page.goto(`${BASE_URL}/picks`);
      
      // Test swipe gestures on pick cards
      const pickCard = page.locator('[data-testid="pick-card"]').first();
      
      // Simulate touch start
      await pickCard.dispatchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 }]
      });
      
      // Simulate swipe
      await pickCard.dispatchEvent('touchmove', {
        touches: [{ clientX: 200, clientY: 100 }]
      });
      
      await pickCard.dispatchEvent('touchend', {});
      
      // Verify swipe action (e.g., showing action buttons)
      await expect(page.locator('[data-testid="swipe-actions"]')).toBeVisible();
    });
  });

  test.describe('Performance and Loading', () => {
    test('should load pages within performance thresholds', async ({ page }) => {
      // Measure marketplace page load
      const marketplaceStart = Date.now();
      await page.goto(`${BASE_URL}/experts`);
      await page.waitForLoadState('networkidle');
      const marketplaceLoadTime = Date.now() - marketplaceStart;
      
      // Verify load time is under 2 seconds
      expect(marketplaceLoadTime).toBeLessThan(2000);
      
      // Measure expert detail page load
      const expertStart = Date.now();
      await page.click('[data-testid="expert-card"]');
      await page.waitForLoadState('networkidle');
      const expertLoadTime = Date.now() - expertStart;
      
      expect(expertLoadTime).toBeLessThan(2000);
      
      // Verify no console errors
      const consoleErrors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      expect(consoleErrors).toHaveLength(0);
    });

    test('should handle slow network conditions', async ({ page, context }) => {
      // Simulate slow 3G
      await context.route('**/*', route => {
        setTimeout(() => route.continue(), 200);
      });
      
      await page.goto(`${BASE_URL}/experts`);
      
      // Verify loading states appear
      await expect(page.locator('[data-testid="loading-skeleton"]')).toBeVisible();
      
      // Wait for content to load
      await page.waitForLoadState('networkidle');
      
      // Verify content appears
      await expect(page.locator('[data-testid="expert-card"]')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API error
      await page.route(`${API_BASE_URL}/api/v1/experts`, async (route) => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Internal Server Error'
          })
        });
      });
      
      await page.goto(`${BASE_URL}/experts`);
      
      // Verify error state
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
      
      // Test retry functionality
      await page.route(`${API_BASE_URL}/api/v1/experts`, async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            experts: Object.values(mockExperts),
            total_count: Object.values(mockExperts).length
          })
        });
      });
      
      await page.click('[data-testid="retry-button"]');
      
      // Verify recovery
      await expect(page.locator('[data-testid="expert-card"]')).toBeVisible();
    });

    test('should handle network timeouts', async ({ page }) => {
      // Mock timeout
      await page.route(`${API_BASE_URL}/api/v1/experts`, async (route) => {
        await new Promise(() => {}); // Never resolve
      });
      
      await page.goto(`${BASE_URL}/experts`);
      
      // Wait for timeout error
      await expect(page.locator('[data-testid="timeout-error"]')).toBeVisible({
        timeout: 10000
      });
      
      await expect(page.locator('.error-message')).toContainText('Request timed out');
    });
  });
});

// Helper function to generate test user data
function generateTestUser() {
  const timestamp = Date.now();
  return {
    email: `test.user.${timestamp}@betbet.com`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: `User${timestamp}`
  };
}