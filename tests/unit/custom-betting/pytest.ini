[tool:pytest]
# Pytest configuration for Custom Betting Platform

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow-running tests
    websocket: WebSocket-related tests
    database: Database-dependent tests

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=90

# Minimum Python version
minversion = 3.9

# Filter warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning