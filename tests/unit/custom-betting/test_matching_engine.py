"""
Custom Betting Platform - Matching Engine Unit Tests
===================================================

Unit tests for P2P bet matching algorithms and queue management.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, patch

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.matching_engine import MatchingEngine
from app.database.models import CustomBet, BetOutcome, BetParticipant, BetMatchingQueue, BetMatch
from tests.conftest import create_test_bet, create_test_participant


class TestMatchingEngine:
    """Test suite for MatchingEngine business logic."""
    
    @pytest.fixture
    async def matching_engine(self):
        """Create MatchingEngine instance for testing."""
        return MatchingEngine()
    
    @pytest.fixture
    async def test_bet_with_outcomes(self, test_db_session, sample_user_data):
        """Create a test bet with outcomes for matching tests."""
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        # Get the outcomes
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetOutcome).where(BetOutcome.custom_bet_id == bet.id)
        )
        outcomes = result.scalars().all()
        
        return bet, outcomes
    
    @pytest.mark.asyncio
    async def test_add_to_queue_backing_position(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test adding a backing position to matching queue."""
        bet, outcomes = test_bet_with_outcomes
        user_id = uuid4()
        
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.1")
        )
        
        assert success is True
        
        # Verify queue entry was created
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatchingQueue).where(
                BetMatchingQueue.custom_bet_id == bet.id,
                BetMatchingQueue.user_id == user_id
            )
        )
        queue_entry = result.scalar_one_or_none()
        
        assert queue_entry is not None
        assert queue_entry.position_type == "backing"
        assert queue_entry.stake_amount == Decimal("100.00")
        assert queue_entry.desired_odds == Decimal("2.1")
        assert queue_entry.status == "pending"
    
    @pytest.mark.asyncio
    async def test_add_to_queue_laying_position(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test adding a laying position to matching queue."""
        bet, outcomes = test_bet_with_outcomes
        user_id = uuid4()
        
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("1.9")
        )
        
        assert success is True
        
        # Verify queue entry was created with correct laying parameters
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatchingQueue).where(
                BetMatchingQueue.custom_bet_id == bet.id,
                BetMatchingQueue.user_id == user_id
            )
        )
        queue_entry = result.scalar_one_or_none()
        
        assert queue_entry is not None
        assert queue_entry.position_type == "laying"
        assert queue_entry.desired_odds == Decimal("1.9")
    
    @pytest.mark.asyncio
    async def test_simple_match_exact_odds(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test simple matching with exact odds match."""
        bet, outcomes = test_bet_with_outcomes
        
        # Add backing position
        backer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Add laying position with same odds
        layer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=layer_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Run matching
        matches_created = await matching_engine.process_matches(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert matches_created > 0
        
        # Verify match was created
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatch).where(BetMatch.custom_bet_id == bet.id)
        )
        matches = result.scalars().all()
        
        assert len(matches) == 1
        match = matches[0]
        assert match.matched_stake == Decimal("100.00")
        assert match.agreed_odds == Decimal("2.0")
    
    @pytest.mark.asyncio
    async def test_partial_match_different_stakes(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test partial matching with different stake amounts."""
        bet, outcomes = test_bet_with_outcomes
        
        # Add large backing position
        backer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer_id,
            position_type="backing",
            stake_amount=Decimal("200.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Add smaller laying position
        layer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=layer_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Run matching
        matches_created = await matching_engine.process_matches(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert matches_created > 0
        
        # Verify partial match
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatch).where(BetMatch.custom_bet_id == bet.id)
        )
        matches = result.scalars().all()
        
        assert len(matches) == 1
        match = matches[0]
        assert match.matched_stake == Decimal("100.00")  # Smaller amount
        
        # Verify remaining backing position in queue
        result = await test_db_session.execute(
            select(BetMatchingQueue).where(
                BetMatchingQueue.custom_bet_id == bet.id,
                BetMatchingQueue.user_id == backer_id,
                BetMatchingQueue.status == "pending"
            )
        )
        remaining_backing = result.scalar_one_or_none()
        
        assert remaining_backing is not None
        assert remaining_backing.stake_amount == Decimal("100.00")  # Remaining amount
    
    @pytest.mark.asyncio
    async def test_price_time_priority_matching(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test price-time priority matching algorithm."""
        bet, outcomes = test_bet_with_outcomes
        
        # Add backing positions with different odds (higher odds first)
        backer1_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer1_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.2")  # Higher odds, should match first
        )
        
        # Add second backing position with lower odds
        backer2_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer2_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")  # Lower odds
        )
        
        # Add laying position that can match the higher odds
        layer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=layer_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.2")  # Matches higher odds backer
        )
        
        # Run matching
        matches_created = await matching_engine.process_matches(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert matches_created > 0
        
        # Verify the higher odds backer was matched first
        from sqlalchemy import select, and_
        result = await test_db_session.execute(
            select(BetMatch).where(
                and_(
                    BetMatch.custom_bet_id == bet.id,
                    BetMatch.agreed_odds == Decimal("2.2")
                )
            )
        )
        match = result.scalar_one_or_none()
        
        assert match is not None
        # Note: Would need to verify which specific backer was matched
        # This requires additional tracking in the matching logic
    
    @pytest.mark.asyncio
    async def test_no_match_incompatible_odds(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test no matching when odds are incompatible."""
        bet, outcomes = test_bet_with_outcomes
        
        # Add backing position wanting high odds
        backer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("3.0")  # High odds
        )
        
        # Add laying position offering low odds
        layer_id = uuid4()
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=layer_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("1.5")  # Much lower odds
        )
        
        # Run matching
        matches_created = await matching_engine.process_matches(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert matches_created == 0
        
        # Verify no matches were created
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatch).where(BetMatch.custom_bet_id == bet.id)
        )
        matches = result.scalars().all()
        
        assert len(matches) == 0
    
    @pytest.mark.asyncio
    async def test_remove_from_queue(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test removing position from matching queue."""
        bet, outcomes = test_bet_with_outcomes
        user_id = uuid4()
        
        # Add position to queue
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        assert success is True
        
        # Remove from queue
        removed = await matching_engine.remove_from_queue(
            db=test_db_session,
            bet_id=bet.id,
            user_id=user_id,
            outcome_id=outcomes[0].id
        )
        
        assert removed is True
        
        # Verify position was removed
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetMatchingQueue).where(
                BetMatchingQueue.custom_bet_id == bet.id,
                BetMatchingQueue.user_id == user_id,
                BetMatchingQueue.status == "pending"
            )
        )
        queue_entry = result.scalar_one_or_none()
        
        assert queue_entry is None
    
    @pytest.mark.asyncio
    async def test_get_queue_status(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test getting queue status for a bet."""
        bet, outcomes = test_bet_with_outcomes
        
        # Add multiple positions to queue
        for i in range(3):
            user_id = uuid4()
            await matching_engine.add_to_queue(
                db=test_db_session,
                bet_id=bet.id,
                outcome_id=outcomes[0].id,
                user_id=user_id,
                position_type="backing",
                stake_amount=Decimal("100.00"),
                desired_odds=Decimal("2.0")
            )
        
        # Get queue status
        status = await matching_engine.get_queue_status(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert "total_pending" in status
        assert "backing_positions" in status
        assert "laying_positions" in status
        assert status["total_pending"] == 3
        assert status["backing_positions"] == 3
        assert status["laying_positions"] == 0
    
    @pytest.mark.asyncio
    async def test_odds_calculation_after_match(self, matching_engine, test_db_session, test_bet_with_outcomes):
        """Test odds recalculation after successful matches."""
        bet, outcomes = test_bet_with_outcomes
        
        # Create match scenario
        backer_id = uuid4()
        layer_id = uuid4()
        
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=backer_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.5")
        )
        
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=layer_id,
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.5")
        )
        
        # Get initial odds
        initial_odds = outcomes[0].current_odds
        
        # Process match
        matches_created = await matching_engine.process_matches(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert matches_created > 0
        
        # Check if odds were updated
        await test_db_session.refresh(outcomes[0])
        updated_odds = outcomes[0].current_odds
        
        # Odds should reflect market activity
        # (Implementation depends on specific odds calculation algorithm)
        assert updated_odds is not None


class TestMatchingValidation:
    """Test suite for matching validation and edge cases."""
    
    @pytest.mark.asyncio
    async def test_prevent_self_matching(self, test_db_session, test_bet_with_outcomes):
        """Test that users cannot match with themselves."""
        matching_engine = MatchingEngine()
        bet, outcomes = test_bet_with_outcomes
        user_id = uuid4()
        
        # Add backing position
        await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Try to add laying position with same user
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,  # Same user
            position_type="laying",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        # Should prevent self-matching
        assert success is False
    
    @pytest.mark.asyncio
    async def test_minimum_stake_validation(self, test_db_session, test_bet_with_outcomes):
        """Test minimum stake validation."""
        matching_engine = MatchingEngine()
        bet, outcomes = test_bet_with_outcomes
        user_id = uuid4()
        
        # Try to add position below minimum stake
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=user_id,
            position_type="backing",
            stake_amount=Decimal("1.00"),  # Below minimum
            desired_odds=Decimal("2.0")
        )
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_expired_bet_no_matching(self, test_db_session):
        """Test that expired bets cannot be matched."""
        matching_engine = MatchingEngine()
        
        # Create expired bet
        from app.database.models import CustomBet, BetOutcome
        
        expired_bet = CustomBet(
            creator_user_id=uuid4(),
            title="Expired Bet",
            description="Test",
            category="test",
            bet_type="binary",
            minimum_stake=Decimal("10.00"),
            maximum_stake=Decimal("1000.00"),
            deadline=datetime.now() - timedelta(hours=1),  # Already expired
            verification_deadline=datetime.now() + timedelta(days=1),
            status="expired"
        )
        
        test_db_session.add(expired_bet)
        await test_db_session.flush()
        
        outcome = BetOutcome(
            custom_bet_id=expired_bet.id,
            outcome_text="Test Outcome",
            initial_odds=Decimal("2.0"),
            current_odds=Decimal("2.0")
        )
        test_db_session.add(outcome)
        await test_db_session.commit()
        
        # Try to add position to expired bet
        success = await matching_engine.add_to_queue(
            db=test_db_session,
            bet_id=expired_bet.id,
            outcome_id=outcome.id,
            user_id=uuid4(),
            position_type="backing",
            stake_amount=Decimal("100.00"),
            desired_odds=Decimal("2.0")
        )
        
        assert success is False