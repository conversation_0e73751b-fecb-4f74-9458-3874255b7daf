"""
Custom Betting Platform - Test Configuration
===========================================

Pytest configuration and fixtures for testing the Custom Betting Platform.
"""

import asyncio
import pytest
import pytest_asyncio
from decimal import Decimal
from datetime import datetime, timed<PERSON><PERSON>
from typing import AsyncGenerator, Dict, Any
from uuid import uuid4, UUID

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.database.models import Base
from app.database.connection import get_database_session
from app.core.config import Settings


# Test Database Configuration
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def test_db_engine():
    """Create test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Clean up
    await engine.dispose()


@pytest_asyncio.fixture
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session = async_sessionmaker(
        test_db_engine, 
        class_=AsyncSession, 
        expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session


@pytest_asyncio.fixture
async def test_client(test_db_session):
    """Create test client with database override."""
    async def override_get_database_session():
        yield test_db_session
    
    app.dependency_overrides[get_database_session] = override_get_database_session
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_data() -> Dict[str, Any]:
    """Sample user data for testing."""
    return {
        "user_id": str(uuid4()),
        "username": "testuser",
        "email": "<EMAIL>",
        "roles": ["user"]
    }


@pytest.fixture
def sample_admin_data() -> Dict[str, Any]:
    """Sample admin user data for testing."""
    return {
        "user_id": str(uuid4()),
        "username": "admin",
        "email": "<EMAIL>",
        "roles": ["admin", "user"]
    }


@pytest.fixture
def sample_bet_data() -> Dict[str, Any]:
    """Sample bet data for testing."""
    return {
        "title": "Test Election Bet",
        "description": "Who will win the test election?",
        "category": "politics",
        "bet_type": "binary",
        "minimum_stake": "10.00",
        "maximum_stake": "1000.00",
        "deadline": (datetime.now() + timedelta(days=30)).isoformat(),
        "verification_deadline": (datetime.now() + timedelta(days=35)).isoformat(),
        "participant_limit": 100,
        "event_criteria": "Official election results",
        "outcomes": [
            {
                "outcome_text": "Candidate A wins",
                "initial_odds": "2.0"
            },
            {
                "outcome_text": "Candidate B wins", 
                "initial_odds": "2.0"
            }
        ],
        "tags": ["election", "politics", "test"],
        "is_public": True,
        "is_featured": False
    }


@pytest.fixture
def sample_participant_data() -> Dict[str, Any]:
    """Sample participant data for testing."""
    return {
        "stake_amount": "50.00",
        "position_type": "backing",
        "desired_odds": "2.1"
    }


@pytest.fixture
def sample_verification_data() -> Dict[str, Any]:
    """Sample outcome verification data for testing."""
    return {
        "evidence_description": "Official election results published",
        "evidence_urls": ["https://example.com/results"],
        "confidence_score": 0.95
    }


@pytest.fixture
def sample_dispute_data() -> Dict[str, Any]:
    """Sample dispute data for testing."""
    return {
        "dispute_type": "incorrect_outcome",
        "dispute_reason": "The declared outcome is incorrect based on official results",
        "evidence_description": "Counter-evidence showing different results",
        "evidence_urls": ["https://example.com/counter-evidence"]
    }


# Helper functions for tests
def create_mock_token_data(user_data: Dict[str, Any]):
    """Create mock token data for authentication."""
    from app.api.dependencies import TokenData
    
    return TokenData(
        user_id=user_data["user_id"],
        username=user_data["username"],
        email=user_data["email"],
        roles=user_data["roles"]
    )


async def create_test_bet(session: AsyncSession, creator_id: UUID, bet_data: Dict[str, Any] = None):
    """Create a test bet in the database."""
    from app.database.models import CustomBet, BetOutcome
    
    if bet_data is None:
        bet_data = {
            "title": "Test Bet",
            "description": "Test description",
            "category": "test",
            "bet_type": "binary",
            "minimum_stake": Decimal("10.00"),
            "maximum_stake": Decimal("1000.00"),
            "deadline": datetime.now() + timedelta(days=30),
            "verification_deadline": datetime.now() + timedelta(days=35),
            "participant_limit": 100,
            "event_criteria": "Test criteria"
        }
    
    # Create bet
    bet = CustomBet(
        creator_user_id=creator_id,
        **bet_data
    )
    session.add(bet)
    await session.flush()
    
    # Create outcomes
    outcome1 = BetOutcome(
        custom_bet_id=bet.id,
        outcome_text="Option A",
        initial_odds=Decimal("2.0"),
        current_odds=Decimal("2.0")
    )
    outcome2 = BetOutcome(
        custom_bet_id=bet.id,
        outcome_text="Option B",
        initial_odds=Decimal("2.0"),
        current_odds=Decimal("2.0")
    )
    
    session.add_all([outcome1, outcome2])
    await session.commit()
    
    return bet


async def create_test_participant(session: AsyncSession, bet_id: UUID, outcome_id: UUID, user_id: UUID):
    """Create a test participant in the database."""
    from app.database.models import BetParticipant
    
    participant = BetParticipant(
        custom_bet_id=bet_id,
        outcome_id=outcome_id,
        user_id=user_id,
        stake_amount=Decimal("50.00"),
        position_type="backing",
        desired_odds=Decimal("2.1"),
        status="active"
    )
    
    session.add(participant)
    await session.commit()
    
    return participant


# Settings override for testing
@pytest.fixture
def test_settings():
    """Override settings for testing."""
    return Settings(
        database_url=TEST_DATABASE_URL,
        secret_key="test-secret-key",
        algorithm="HS256",
        access_token_expire_minutes=30,
        environment="test"
    )