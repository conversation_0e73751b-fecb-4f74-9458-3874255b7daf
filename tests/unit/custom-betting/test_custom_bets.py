"""
Custom Betting Platform - Custom Bets Unit Tests
===============================================

Unit tests for custom bets CRUD operations and business logic.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timed<PERSON>ta
from uuid import uuid4, UUI<PERSON>
from unittest.mock import AsyncMock, patch

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

from app.database.models import CustomBet, BetOutcome
from tests.conftest import create_test_bet, create_mock_token_data


class TestCustomBetsAPI:
    """Test suite for Custom Bets API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_bet_success(self, test_client, sample_user_data, sample_bet_data):
        """Test successful bet creation."""
        # Mock authentication
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=sample_bet_data
            )
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == sample_bet_data["title"]
        assert data["bet_type"] == sample_bet_data["bet_type"]
        assert data["status"] == "open"
        assert len(data["outcomes"]) == 2
    
    @pytest.mark.asyncio
    async def test_create_bet_invalid_data(self, test_client, sample_user_data):
        """Test bet creation with invalid data."""
        invalid_data = {
            "title": "",  # Empty title
            "description": "Test",
            "category": "test",
            "bet_type": "invalid_type",  # Invalid type
            "minimum_stake": "-10.00",  # Negative stake
            "deadline": "invalid_date"  # Invalid date
        }
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=invalid_data
            )
        
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_get_bet_success(self, test_client, test_db_session, sample_user_data):
        """Test successful bet retrieval."""
        # Create test bet
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        response = await test_client.get(f"/api/v1/custom-bets/{bet.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(bet.id)
        assert data["title"] == bet.title
    
    @pytest.mark.asyncio
    async def test_get_bet_not_found(self, test_client):
        """Test bet retrieval with non-existent ID."""
        fake_id = str(uuid4())
        
        response = await test_client.get(f"/api/v1/custom-bets/{fake_id}")
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_list_bets_with_filters(self, test_client, test_db_session, sample_user_data):
        """Test bet listing with filters."""
        # Create multiple test bets
        creator_id = UUID(sample_user_data["user_id"])
        
        bet1 = await create_test_bet(test_db_session, creator_id, {
            "title": "Politics Bet",
            "category": "politics",
            "bet_type": "binary"
        })
        
        bet2 = await create_test_bet(test_db_session, creator_id, {
            "title": "Sports Bet",
            "category": "sports", 
            "bet_type": "multiple_choice"
        })
        
        # Test category filter
        response = await test_client.get("/api/v1/custom-bets/?category=politics")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["bets"]) == 1
        assert data["bets"][0]["category"] == "politics"
    
    @pytest.mark.asyncio
    async def test_update_bet_success(self, test_client, test_db_session, sample_user_data):
        """Test successful bet update."""
        # Create test bet
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        update_data = {
            "title": "Updated Bet Title",
            "description": "Updated description"
        }
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            response = await test_client.patch(
                f"/api/v1/custom-bets/{bet.id}",
                json=update_data
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["description"] == update_data["description"]
    
    @pytest.mark.asyncio
    async def test_update_bet_unauthorized(self, test_client, test_db_session, sample_user_data):
        """Test bet update by non-creator."""
        # Create test bet
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        # Different user tries to update
        other_user = {
            **sample_user_data,
            "user_id": str(uuid4())
        }
        
        update_data = {"title": "Unauthorized Update"}
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(other_user)
            
            response = await test_client.patch(
                f"/api/v1/custom-bets/{bet.id}",
                json=update_data
            )
        
        assert response.status_code == 403  # Forbidden
    
    @pytest.mark.asyncio
    async def test_delete_bet_success(self, test_client, test_db_session, sample_user_data):
        """Test successful bet deletion (soft delete)."""
        # Create test bet
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            response = await test_client.delete(f"/api/v1/custom-bets/{bet.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Bet deleted successfully"
    
    @pytest.mark.asyncio
    async def test_bet_lifecycle_status_transitions(self, test_client, test_db_session, sample_user_data):
        """Test bet status transitions through lifecycle."""
        # Create test bet
        creator_id = UUID(sample_user_data["user_id"])
        bet = await create_test_bet(test_db_session, creator_id)
        
        # Initially should be 'open'
        assert bet.status == "open"
        
        # TODO: Add status transition tests when participants join
        # TODO: Add status transition tests for deadline reached
        # TODO: Add status transition tests for settlement
    
    @pytest.mark.asyncio
    async def test_bet_validation_business_rules(self, test_client, sample_user_data):
        """Test business rule validations."""
        # Test deadline in the past
        invalid_data = {
            "title": "Test Bet",
            "description": "Test description",
            "category": "test",
            "bet_type": "binary",
            "minimum_stake": "10.00",
            "maximum_stake": "1000.00",
            "deadline": (datetime.now() - timedelta(days=1)).isoformat(),  # Past deadline
            "verification_deadline": (datetime.now() + timedelta(days=1)).isoformat(),
            "outcomes": [
                {"outcome_text": "Option A", "initial_odds": "2.0"},
                {"outcome_text": "Option B", "initial_odds": "2.0"}
            ]
        }
        
        with patch("app.api.dependencies.get_verified_user") as mock_auth:
            mock_auth.return_value = create_mock_token_data(sample_user_data)
            
            response = await test_client.post(
                "/api/v1/custom-bets/",
                json=invalid_data
            )
        
        assert response.status_code == 422  # Validation error


class TestCustomBetModel:
    """Test suite for CustomBet model business logic."""
    
    def test_bet_creation_with_valid_data(self):
        """Test CustomBet model creation with valid data."""
        bet = CustomBet(
            creator_user_id=uuid4(),
            title="Test Bet",
            description="Test description",
            category="test",
            bet_type="binary",
            minimum_stake=Decimal("10.00"),
            maximum_stake=Decimal("1000.00"),
            deadline=datetime.now() + timedelta(days=30),
            verification_deadline=datetime.now() + timedelta(days=35)
        )
        
        assert bet.title == "Test Bet"
        assert bet.bet_type == "binary"
        assert bet.status == "open"  # Default status
        assert bet.total_participants == 0
        assert bet.total_stakes == Decimal("0.00")
    
    def test_bet_outcome_creation(self):
        """Test BetOutcome model creation."""
        outcome = BetOutcome(
            custom_bet_id=uuid4(),
            outcome_text="Test Outcome",
            initial_odds=Decimal("2.0"),
            current_odds=Decimal("2.0")
        )
        
        assert outcome.outcome_text == "Test Outcome"
        assert outcome.initial_odds == Decimal("2.0")
        assert outcome.current_odds == Decimal("2.0")
        assert outcome.is_winning_outcome is False  # Default
    
    def test_bet_string_representation(self):
        """Test model string representations."""
        bet = CustomBet(
            creator_user_id=uuid4(),
            title="Test Bet",
            description="Test description",
            category="test",
            bet_type="binary"
        )
        
        assert "Test Bet" in str(bet)
        assert "binary" in str(bet)


class TestBetValidation:
    """Test suite for bet validation logic."""
    
    def test_deadline_validation(self):
        """Test deadline validation business rules."""
        # Test that deadline must be in the future
        past_deadline = datetime.now() - timedelta(days=1)
        
        with pytest.raises(ValueError, match="Deadline must be in the future"):
            CustomBet.validate_deadline(past_deadline)
    
    def test_stake_validation(self):
        """Test stake amount validation."""
        # Test minimum stake validation
        with pytest.raises(ValueError, match="Minimum stake must be positive"):
            CustomBet.validate_stakes(Decimal("-10.00"), Decimal("100.00"))
        
        # Test maximum stake must be greater than minimum
        with pytest.raises(ValueError, match="Maximum stake must be greater than minimum"):
            CustomBet.validate_stakes(Decimal("100.00"), Decimal("50.00"))
    
    def test_outcome_validation(self):
        """Test outcome validation rules."""
        # Test binary bet must have exactly 2 outcomes
        outcomes_data = [
            {"outcome_text": "Option A", "initial_odds": "2.0"},
            {"outcome_text": "Option B", "initial_odds": "2.0"},
            {"outcome_text": "Option C", "initial_odds": "2.0"}  # Too many for binary
        ]
        
        with pytest.raises(ValueError, match="Binary bets must have exactly 2 outcomes"):
            CustomBet.validate_outcomes("binary", outcomes_data)
    
    def test_odds_validation(self):
        """Test odds validation."""
        # Test odds must be positive
        with pytest.raises(ValueError, match="Odds must be positive"):
            BetOutcome.validate_odds(Decimal("-1.5"))
        
        # Test odds must be reasonable (not too low)
        with pytest.raises(ValueError, match="Odds too low"):
            BetOutcome.validate_odds(Decimal("0.5"))