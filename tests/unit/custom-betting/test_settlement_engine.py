"""
Custom Betting Platform - Settlement Engine Unit Tests
=====================================================

Unit tests for bet settlement, dispute resolution, and payout distribution.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, patch

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.settlement_engine import SettlementEngine
from app.database.models import CustomBet, BetOutcome, BetParticipant, SettlementDispute
from tests.conftest import create_test_bet, create_test_participant


class TestSettlementEngine:
    """Test suite for SettlementEngine business logic."""
    
    @pytest.fixture
    async def settlement_engine(self):
        """Create SettlementEngine instance for testing."""
        return SettlementEngine()
    
    @pytest.fixture
    async def settled_bet_scenario(self, test_db_session, sample_user_data):
        """Create a bet scenario ready for settlement."""
        creator_id = UUID(sample_user_data["user_id"])
        
        # Create bet with outcomes
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "Test Settlement Bet",
            "status": "settling",
            "deadline": datetime.now() - timedelta(hours=1),  # Past deadline
            "verification_deadline": datetime.now() - timedelta(minutes=30)  # Past verification
        })
        
        # Get outcomes
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetOutcome).where(BetOutcome.custom_bet_id == bet.id)
        )
        outcomes = result.scalars().all()
        
        # Create participants
        participants = []
        for i, outcome in enumerate(outcomes):
            user_id = uuid4()
            participant = await create_test_participant(
                test_db_session, bet.id, outcome.id, user_id
            )
            participants.append(participant)
        
        # Mark one outcome as winning
        outcomes[0].is_winning_outcome = True
        await test_db_session.commit()
        
        return bet, outcomes, participants
    
    @pytest.mark.asyncio
    async def test_submit_outcome_verification_success(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test successful outcome verification submission."""
        bet, outcomes, participants = settled_bet_scenario
        submitter_id = uuid4()
        
        success = await settlement_engine.submit_outcome_verification(
            db=test_db_session,
            bet_id=bet.id,
            winning_outcome_id=outcomes[0].id,
            submitted_by=submitter_id,
            evidence_description="Clear evidence of outcome",
            evidence_urls=["https://example.com/evidence"],
            confidence_score=0.95
        )
        
        assert success is True
        
        # Verify outcome verification was recorded
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetOutcome).where(BetOutcome.id == outcomes[0].id)
        )
        outcome = result.scalar_one()
        
        assert outcome.is_winning_outcome is True
        # Additional verification fields would be checked if they exist
    
    @pytest.mark.asyncio
    async def test_process_settlement_success(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test successful bet settlement processing."""
        bet, outcomes, participants = settled_bet_scenario
        admin_id = uuid4()
        
        # Process settlement
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        assert "bet_id" in result
        assert "settlement_status" in result
        assert "winners_count" in result
        assert "total_payout" in result
        
        # Verify bet status was updated
        await test_db_session.refresh(bet)
        assert bet.status == "settled"
    
    @pytest.mark.asyncio
    async def test_process_settlement_with_disputes(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test settlement processing when disputes exist."""
        bet, outcomes, participants = settled_bet_scenario
        admin_id = uuid4()
        disputer_id = uuid4()
        
        # File a dispute first
        dispute_id = await settlement_engine.file_dispute(
            db=test_db_session,
            bet_id=bet.id,
            disputing_user_id=disputer_id,
            dispute_type="incorrect_outcome",
            dispute_reason="Evidence shows different outcome",
            evidence_description="Counter-evidence",
            evidence_urls=["https://example.com/counter-evidence"]
        )
        
        assert dispute_id is not None
        
        # Try to settle with active dispute (should require force)
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        # Should indicate disputes need resolution
        assert "disputes_pending" in result or result.get("settlement_status") == "disputed"
    
    @pytest.mark.asyncio
    async def test_file_dispute_success(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test successful dispute filing."""
        bet, outcomes, participants = settled_bet_scenario
        disputer_id = uuid4()
        
        dispute_id = await settlement_engine.file_dispute(
            db=test_db_session,
            bet_id=bet.id,
            disputing_user_id=disputer_id,
            dispute_type="incorrect_outcome",
            dispute_reason="Evidence contradicts declared outcome",
            evidence_description="Official source shows different result",
            evidence_urls=["https://official-source.com/result"]
        )
        
        assert dispute_id is not None
        
        # Verify dispute was created
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(SettlementDispute).where(SettlementDispute.id == dispute_id)
        )
        dispute = result.scalar_one()
        
        assert dispute.custom_bet_id == bet.id
        assert dispute.disputing_user_id == disputer_id
        assert dispute.dispute_type == "incorrect_outcome"
        assert dispute.status == "open"
    
    @pytest.mark.asyncio
    async def test_resolve_dispute_success(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test successful dispute resolution."""
        bet, outcomes, participants = settled_bet_scenario
        disputer_id = uuid4()
        admin_id = uuid4()
        
        # File dispute first
        dispute_id = await settlement_engine.file_dispute(
            db=test_db_session,
            bet_id=bet.id,
            disputing_user_id=disputer_id,
            dispute_type="incorrect_outcome",
            dispute_reason="Test dispute",
            evidence_description="Test evidence",
            evidence_urls=[]
        )
        
        # Resolve dispute
        success = await settlement_engine.resolve_dispute(
            db=test_db_session,
            dispute_id=dispute_id,
            resolution_method="admin_review",
            resolution_decision="upheld",
            final_outcome=outcomes[1].id,  # Different outcome
            resolved_by=admin_id,
            refund_amount=None
        )
        
        assert success is True
        
        # Verify dispute was resolved
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(SettlementDispute).where(SettlementDispute.id == dispute_id)
        )
        dispute = result.scalar_one()
        
        assert dispute.status == "resolved"
        assert dispute.resolution_method == "admin_review"
        assert dispute.resolved_by == admin_id
    
    @pytest.mark.asyncio
    async def test_cancel_bet_with_refunds(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test bet cancellation with participant refunds."""
        bet, outcomes, participants = settled_bet_scenario
        admin_id = uuid4()
        
        result = await settlement_engine.cancel_bet(
            db=test_db_session,
            bet_id=bet.id,
            cancellation_reason="Event cancelled by organizer",
            cancelled_by=admin_id
        )
        
        assert "bet_id" in result
        assert "cancellation_status" in result
        assert "refunds_processed" in result
        
        # Verify bet was cancelled
        await test_db_session.refresh(bet)
        assert bet.status == "cancelled"
        
        # Verify refunds were processed
        refunds_count = result.get("refunds_processed", 0)
        assert refunds_count == len(participants)
    
    @pytest.mark.asyncio
    async def test_get_settlement_status(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test getting settlement status information."""
        bet, outcomes, participants = settled_bet_scenario
        
        status = await settlement_engine.get_settlement_status(
            db=test_db_session,
            bet_id=bet.id
        )
        
        assert "bet_id" in status
        assert "current_status" in status
        assert "winning_outcome" in status
        assert "total_participants" in status
        assert "total_volume" in status
        assert "disputes_count" in status
    
    @pytest.mark.asyncio
    async def test_community_voting_process(self, settlement_engine, test_db_session, settled_bet_scenario):
        """Test community voting on disputed outcomes."""
        bet, outcomes, participants = settled_bet_scenario
        voter_id = uuid4()
        
        success = await settlement_engine.process_community_vote(
            db=test_db_session,
            bet_id=bet.id,
            voter_id=voter_id,
            voted_outcome_id=outcomes[0].id,
            vote_confidence=0.8
        )
        
        assert success is True
        
        # Additional verification of vote recording would go here
        # depending on how community voting is implemented
    
    @pytest.mark.asyncio
    async def test_payout_calculation_accuracy(self, settlement_engine, test_db_session):
        """Test accuracy of payout calculations."""
        # Create a more complex scenario with known expected payouts
        creator_id = uuid4()
        
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "Payout Test Bet",
            "status": "settling",
            "minimum_stake": Decimal("10.00"),
            "maximum_stake": Decimal("1000.00")
        })
        
        # Get outcomes
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetOutcome).where(BetOutcome.custom_bet_id == bet.id)
        )
        outcomes = result.scalars().all()
        
        # Create participants with known stakes
        winner_id = uuid4()
        loser_id = uuid4()
        
        # Winner stakes 100 at 2.0 odds - should win 100 profit
        winner_participant = BetParticipant(
            custom_bet_id=bet.id,
            outcome_id=outcomes[0].id,
            user_id=winner_id,
            stake_amount=Decimal("100.00"),
            position_type="backing",
            desired_odds=Decimal("2.0"),
            status="active"
        )
        
        # Loser stakes 100 - should lose their stake
        loser_participant = BetParticipant(
            custom_bet_id=bet.id,
            outcome_id=outcomes[1].id,
            user_id=loser_id,
            stake_amount=Decimal("100.00"),
            position_type="backing",
            desired_odds=Decimal("2.0"),
            status="active"
        )
        
        test_db_session.add_all([winner_participant, loser_participant])
        
        # Mark winning outcome
        outcomes[0].is_winning_outcome = True
        await test_db_session.commit()
        
        # Process settlement
        admin_id = uuid4()
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        # Verify payout calculations
        assert "total_payout" in result
        total_payout = Decimal(str(result["total_payout"]))
        
        # Winner should get back their stake (100) plus winnings
        # Exact calculation depends on platform fee and payout algorithm
        assert total_payout > Decimal("100.00")  # At least the winner's stake back
    
    @pytest.mark.asyncio
    async def test_settlement_deadline_validation(self, settlement_engine, test_db_session, sample_user_data):
        """Test settlement deadline validation."""
        creator_id = UUID(sample_user_data["user_id"])
        
        # Create bet that hasn't reached settlement deadline
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "Early Settlement Test",
            "status": "active",
            "deadline": datetime.now() + timedelta(hours=1),  # Future deadline
            "verification_deadline": datetime.now() + timedelta(hours=2)
        })
        
        admin_id = uuid4()
        
        # Try to settle before deadline
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        # Should indicate that settlement is premature
        assert "error" in result or result.get("settlement_status") == "premature"
    
    @pytest.mark.asyncio
    async def test_dispute_time_window_validation(self, settlement_engine, test_db_session):
        """Test dispute filing time window validation."""
        creator_id = uuid4()
        
        # Create bet that's well past dispute window
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "Old Settlement",
            "status": "settled",
            "deadline": datetime.now() - timedelta(days=10),
            "verification_deadline": datetime.now() - timedelta(days=9),
            "settlement_timestamp": datetime.now() - timedelta(days=8)
        })
        
        disputer_id = uuid4()
        
        # Try to file dispute outside time window
        dispute_id = await settlement_engine.file_dispute(
            db=test_db_session,
            bet_id=bet.id,
            disputing_user_id=disputer_id,
            dispute_type="incorrect_outcome",
            dispute_reason="Late dispute",
            evidence_description="Evidence",
            evidence_urls=[]
        )
        
        # Should reject late dispute
        assert dispute_id is None


class TestSettlementValidation:
    """Test suite for settlement validation and edge cases."""
    
    @pytest.mark.asyncio
    async def test_prevent_double_settlement(self, test_db_session):
        """Test prevention of double settlement."""
        settlement_engine = SettlementEngine()
        creator_id = uuid4()
        
        # Create already settled bet
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "Already Settled",
            "status": "settled"
        })
        
        admin_id = uuid4()
        
        # Try to settle again
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        # Should reject double settlement
        assert "error" in result or result.get("settlement_status") == "already_settled"
    
    @pytest.mark.asyncio
    async def test_no_participants_settlement(self, test_db_session):
        """Test settlement of bet with no participants."""
        settlement_engine = SettlementEngine()
        creator_id = uuid4()
        
        # Create bet with no participants
        bet = await create_test_bet(test_db_session, creator_id, {
            "title": "No Participants",
            "status": "settling"
        })
        
        admin_id = uuid4()
        
        result = await settlement_engine.process_settlement(
            db=test_db_session,
            bet_id=bet.id,
            settlement_admin_id=admin_id,
            force_settlement=False
        )
        
        # Should handle gracefully (possibly cancel or void)
        assert "settlement_status" in result
        assert result.get("winners_count", 0) == 0
    
    @pytest.mark.asyncio
    async def test_invalid_outcome_verification(self, test_db_session):
        """Test invalid outcome verification submission."""
        settlement_engine = SettlementEngine()
        creator_id = uuid4()
        
        bet = await create_test_bet(test_db_session, creator_id)
        
        # Get outcomes
        from sqlalchemy import select
        result = await test_db_session.execute(
            select(BetOutcome).where(BetOutcome.custom_bet_id == bet.id)
        )
        outcomes = result.scalars().all()
        
        submitter_id = uuid4()
        fake_outcome_id = uuid4()  # Non-existent outcome
        
        # Try to verify non-existent outcome
        success = await settlement_engine.submit_outcome_verification(
            db=test_db_session,
            bet_id=bet.id,
            winning_outcome_id=fake_outcome_id,
            submitted_by=submitter_id,
            evidence_description="Invalid verification",
            evidence_urls=[],
            confidence_score=0.9
        )
        
        assert success is False