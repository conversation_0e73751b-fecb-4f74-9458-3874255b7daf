[tool:pytest]
# Pytest configuration for Expert Analyst Marketplace
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --asyncio-mode=auto
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=90
    -v
    --tb=short
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
markers =
    unit: Unit tests
    integration: Integration tests
    api: API endpoint tests
    websocket: WebSocket tests
    slow: Slow running tests
    auth: Authentication tests
    subscription: Subscription related tests
    expert: Expert management tests
    picks: Expert picks tests
    analytics: Analytics tests
    revenue: Revenue management tests
    community: Community features tests