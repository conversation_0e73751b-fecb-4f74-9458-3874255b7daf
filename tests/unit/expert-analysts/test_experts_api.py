"""
Expert Management API Tests
============================

Comprehensive tests for expert management endpoints.

Author: Claude-API
Date: 2025-07-21
"""

import pytest
from httpx import AsyncClient
from uuid import uuid4

from app.database.models import Expert

class TestExpertManagement:
    """Test expert management endpoints."""
    
    async def test_get_experts_list(self, async_client: AsyncClient, sample_expert: Expert):
        """Test getting list of experts."""
        response = await async_client.get("/api/v1/experts/")
        
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "pagination" in data
        assert len(data["data"]) > 0
        
        # Check first expert data structure
        expert = data["data"][0]
        assert "id" in expert
        assert "expert_name" in expert
        assert "slug" in expert
        assert "specialty" in expert
        assert "verification_status" in expert
    
    async def test_get_expert_discovery(self, async_client: AsyncClient, sample_expert: <PERSON>pert):
        """Test expert discovery endpoint."""
        response = await async_client.get("/api/v1/experts/discovery")
        
        assert response.status_code == 200
        data = response.json()
        assert "experts" in data
        assert "featured_experts" in data
        assert "top_performers" in data
        assert "trending_experts" in data
        assert "categories" in data
    
    async def test_search_experts(self, async_client: AsyncClient, sample_expert: Expert):
        """Test expert search functionality."""
        # Search by specialty
        response = await async_client.get(
            "/api/v1/experts/search",
            params={"specialty": "NBA", "sort_by": "average_rating"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        
        # Search by query
        response = await async_client.get(
            "/api/v1/experts/search",
            params={"query": "Test Expert"}
        )
        
        assert response.status_code == 200
    
    async def test_create_expert_profile(self, async_client: AsyncClient, auth_headers):
        """Test creating expert profile."""
        expert_data = {
            "expert_name": "New Test Expert",
            "slug": "new-test-expert",
            "specialty": "NFL",
            "bio": "New expert bio",
            "years_experience": 5,
            "risk_tolerance": "moderate"
        }
        
        response = await async_client.post(
            "/api/v1/experts/",
            json=expert_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "message" in data
    
    async def test_create_expert_profile_duplicate_slug(
        self, async_client: AsyncClient, auth_headers, sample_expert: Expert
    ):
        """Test creating expert profile with duplicate slug."""
        expert_data = {
            "expert_name": "Another Expert",
            "slug": "test-expert",  # Same as sample_expert
            "specialty": "MLB",
            "bio": "Another expert bio"
        }
        
        response = await async_client.post(
            "/api/v1/experts/",
            json=expert_data,
            headers=auth_headers
        )
        
        assert response.status_code == 409
        data = response.json()
        assert "error" in data
    
    async def test_get_my_expert_profile(
        self, async_client: AsyncClient, auth_headers, sample_expert: Expert
    ):
        """Test getting current user's expert profile."""
        response = await async_client.get(
            "/api/v1/experts/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "expert" in data
        assert data["expert"]["id"] == str(sample_expert.id)
    
    async def test_update_my_expert_profile(
        self, async_client: AsyncClient, auth_headers, sample_expert: Expert
    ):
        """Test updating current user's expert profile."""
        update_data = {
            "bio": "Updated bio",
            "years_experience": 10
        }
        
        response = await async_client.put(
            "/api/v1/experts/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["expert"]["bio"] == "Updated bio"
        assert data["expert"]["years_experience"] == 10
    
    async def test_get_expert_by_id(self, async_client: AsyncClient, sample_expert: Expert):
        """Test getting expert by ID (public view)."""
        response = await async_client.get(f"/api/v1/experts/{sample_expert.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(sample_expert.id)
        assert data["expert_name"] == sample_expert.expert_name
        assert data["specialty"] == sample_expert.specialty
        # Should not include sensitive financial data
        assert "total_revenue" not in data
    
    async def test_get_expert_by_slug(self, async_client: AsyncClient, sample_expert: Expert):
        """Test getting expert by slug."""
        response = await async_client.get(f"/api/v1/experts/slug/{sample_expert.slug}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["slug"] == sample_expert.slug
        assert data["expert_name"] == sample_expert.expert_name
    
    async def test_get_nonexistent_expert(self, async_client: AsyncClient):
        """Test getting nonexistent expert."""
        fake_id = uuid4()
        response = await async_client.get(f"/api/v1/experts/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert "error" in data

class TestExpertFiltering:
    """Test expert filtering and search functionality."""
    
    async def test_filter_by_specialty(self, async_client: AsyncClient, sample_expert: Expert):
        """Test filtering experts by specialty."""
        response = await async_client.get(
            "/api/v1/experts/",
            params={"specialty": "NBA"}
        )
        
        assert response.status_code == 200
        data = response.json()
        for expert in data["data"]:
            assert expert["specialty"] == "NBA"
    
    async def test_filter_by_rating(self, async_client: AsyncClient, sample_expert: Expert):
        """Test filtering experts by minimum rating."""
        response = await async_client.get(
            "/api/v1/experts/search",
            params={"min_rating": 4.0}
        )
        
        assert response.status_code == 200
        data = response.json()
        for expert in data["data"]:
            if expert["average_rating"]:
                assert expert["average_rating"] >= 4.0
    
    async def test_filter_by_featured(self, async_client: AsyncClient):
        """Test filtering featured experts."""
        response = await async_client.get(
            "/api/v1/experts/search",
            params={"is_featured": True}
        )
        
        assert response.status_code == 200
        data = response.json()
        for expert in data["data"]:
            assert expert["is_featured"] is True

class TestExpertPagination:
    """Test expert pagination functionality."""
    
    async def test_pagination_default(self, async_client: AsyncClient, sample_expert: Expert):
        """Test default pagination."""
        response = await async_client.get("/api/v1/experts/")
        
        assert response.status_code == 200
        data = response.json()
        assert "pagination" in data
        
        pagination = data["pagination"]
        assert pagination["page"] == 1
        assert pagination["limit"] == 20
        assert "total" in pagination
        assert "total_pages" in pagination
        assert "has_next" in pagination
        assert "has_previous" in pagination
    
    async def test_pagination_custom(self, async_client: AsyncClient, sample_expert: Expert):
        """Test custom pagination parameters."""
        response = await async_client.get(
            "/api/v1/experts/",
            params={"page": 1, "limit": 5}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        pagination = data["pagination"]
        assert pagination["page"] == 1
        assert pagination["limit"] == 5
        assert len(data["data"]) <= 5

class TestExpertValidation:
    """Test expert data validation."""
    
    async def test_create_expert_invalid_slug(self, async_client: AsyncClient, auth_headers):
        """Test creating expert with invalid slug."""
        expert_data = {
            "expert_name": "Test Expert",
            "slug": "invalid slug with spaces",  # Invalid slug
            "specialty": "NBA",
            "bio": "Test bio"
        }
        
        response = await async_client.post(
            "/api/v1/experts/",
            json=expert_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    async def test_create_expert_missing_required_fields(
        self, async_client: AsyncClient, auth_headers
    ):
        """Test creating expert with missing required fields."""
        expert_data = {
            "expert_name": "Test Expert",
            # Missing slug and specialty
            "bio": "Test bio"
        }
        
        response = await async_client.post(
            "/api/v1/experts/",
            json=expert_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error