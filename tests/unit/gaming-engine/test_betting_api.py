#!/usr/bin/env python3
"""
Test betting API functionality
"""

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>


def test_betting_api():
    """Test betting API endpoints"""
    print("🧪 Testing Betting API Functionality")
    print("=" * 50)
    
    base_url = "http://localhost:8002"
    
    # Test 1: Check API health
    print("\n1️⃣ Checking API health...")
    response = requests.get(f"{base_url}/health")
    if response.status_code == 200:
        print(f"✅ API is healthy: {response.json()}")
    else:
        print(f"❌ API health check failed: {response.status_code}")
        return
    
    # Test 2: Check database connectivity via test endpoint
    print("\n2️⃣ Testing database connectivity...")
    response = requests.get(f"{base_url}/admin/test-query")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Database connected: {data}")
    else:
        print(f"❌ Database test failed: {response.status_code}")
    
    # Test 3: Test betting endpoints (without auth for now)
    print("\n3️⃣ Testing betting endpoints...")
    
    # Try to get betting markets for a session (will fail without auth)
    test_session_id = "550e8400-e29b-41d4-a716-446655440000"
    
    print(f"\n   Testing GET /sessions/{test_session_id}/betting-markets")
    response = requests.get(f"{base_url}/api/v1/gaming/sessions/{test_session_id}/betting-markets")
    print(f"   Status: {response.status_code}")
    if response.status_code == 404:
        print("   ℹ️  Session not found (expected)")
    elif response.status_code == 200:
        print(f"   ✅ Response: {response.json()}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test 4: Test creating a betting market (will require auth)
    print("\n4️⃣ Testing betting market creation (without auth)...")
    
    market_data = {
        "session_id": test_session_id,
        "name": "Test Betting Market",
        "description": "Testing betting functionality",
        "market_type": "winner",
        "status": "active",
        "outcomes": [
            {
                "name": "Player 1",
                "odds": 2.5,
                "implied_probability": 40.0
            },
            {
                "name": "Player 2", 
                "odds": 1.8,
                "implied_probability": 55.56
            }
        ]
    }
    
    response = requests.post(
        f"{base_url}/api/v1/gaming/betting-markets",
        json=market_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 401:
        print("   ✅ Authentication required (expected)")
        print(f"   Response: {response.json()}")
    else:
        print(f"   Response: {response.text}")
    
    # Test 5: Check all registered betting endpoints
    print("\n5️⃣ Checking registered betting endpoints...")
    response = requests.get(f"{base_url}/openapi.json")
    if response.status_code == 200:
        openapi = response.json()
        betting_endpoints = [
            path for path in openapi['paths'].keys() 
            if 'betting' in path.lower() or 'bet' in path.lower()
        ]
        print(f"✅ Found {len(betting_endpoints)} betting endpoints:")
        for endpoint in betting_endpoints:
            methods = list(openapi['paths'][endpoint].keys())
            print(f"   - {', '.join(m.upper() for m in methods)} {endpoint}")
    
    print("\n✅ Betting API test complete!")
    print("\n📝 Next steps:")
    print("1. Configure Clerk authentication")
    print("2. Create test users with admin role")
    print("3. Test with valid auth tokens")
    print("4. Create sample game sessions")
    print("5. Test full betting flow")


if __name__ == "__main__":
    test_betting_api()