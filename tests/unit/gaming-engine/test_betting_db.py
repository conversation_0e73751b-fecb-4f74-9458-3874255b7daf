#!/usr/bin/env python3
"""
Test betting database connectivity and queries
"""

import asyncio
import sys
from pathlib import Path
from uuid import uuid4

# Add project path
sys.path.append(str(Path(__file__).parent.parent.parent))

from services.shared.core.database.connection import _db_manager
from app.database.models import (
    BettingMarket, BettingOutcome, UserBet, BettingMarketOddsHistory,
    GameSession, User
)
from sqlalchemy import select, func


async def test_betting_tables():
    """Test betting table connectivity and basic queries"""
    print("🧪 Testing Betting Database Connectivity...")
    
    try:
        db_manager = _db_manager
        
        # Test 1: Check if betting tables exist
        print("\n1️⃣ Checking if betting tables exist...")
        tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'gaming_engine' 
        AND table_name LIKE 'betting_%'
        ORDER BY table_name
        """
        
        tables = await db_manager.execute_query(tables_query, fetch_all=True)
        if tables:
            print("✅ Betting tables found:")
            for table in tables:
                print(f"   - {dict(table)['table_name']}")
        else:
            print("❌ No betting tables found!")
            return False
        
        # Test 2: Count records in each table
        print("\n2️⃣ Counting records in betting tables...")
        table_names = ['betting_markets', 'betting_outcomes', 'user_bets', 'betting_market_odds_history']
        
        for table_name in table_names:
            count_query = f"SELECT COUNT(*) as count FROM gaming_engine.{table_name}"
            result = await db_manager.execute_query(count_query, fetch_all=True)
            count = dict(result[0])['count'] if result else 0
            print(f"   - {table_name}: {count} records")
        
        # Test 3: Test SQLAlchemy models
        print("\n3️⃣ Testing SQLAlchemy betting models...")
        
        async with db_manager.get_session() as session:
            # Count betting markets
            markets_count = await session.scalar(
                select(func.count(BettingMarket.id))
            )
            print(f"   - BettingMarket model: {markets_count} records")
            
            # Count betting outcomes  
            outcomes_count = await session.scalar(
                select(func.count(BettingOutcome.id))
            )
            print(f"   - BettingOutcome model: {outcomes_count} records")
            
            # Count user bets
            bets_count = await session.scalar(
                select(func.count(UserBet.id))
            )
            print(f"   - UserBet model: {bets_count} records")
        
        # Test 4: Check foreign key relationships
        print("\n4️⃣ Checking foreign key relationships...")
        fk_query = """
        SELECT 
            tc.table_name, 
            kcu.column_name, 
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name 
        FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'gaming_engine'
        AND tc.table_name LIKE 'betting_%'
        ORDER BY tc.table_name, kcu.column_name
        """
        
        fks = await db_manager.execute_query(fk_query, fetch_all=True)
        if fks:
            print("✅ Foreign key relationships:")
            for fk in fks:
                fk_dict = dict(fk)
                print(f"   - {fk_dict['table_name']}.{fk_dict['column_name']} -> {fk_dict['foreign_table_name']}.{fk_dict['foreign_column_name']}")
        
        print("\n✅ All betting database tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_test_data():
    """Create test betting data"""
    print("\n📝 Creating test betting data...")
    
    try:
        db_manager = _db_manager
        
        async with db_manager.get_session() as session:
            # Check if we have any game sessions
            sessions = await session.execute(
                select(GameSession).limit(1)
            )
            game_session = sessions.scalar_one_or_none()
            
            if not game_session:
                print("⚠️  No game sessions found. Creating a test session...")
                # Would need to create a test game and session first
                return False
            
            print(f"✅ Using game session: {game_session.id}")
            
            # Create a test betting market
            test_market = BettingMarket(
                session_id=game_session.id,
                name="Test Winner Market",
                description="Who will win this test game?",
                market_type="winner",
                status="active",
                created_by=uuid4(),  # Would use real user ID
                updated_by=uuid4()
            )
            
            session.add(test_market)
            await session.flush()
            
            # Create test outcomes
            outcomes = [
                BettingOutcome(
                    market_id=test_market.id,
                    name="Player 1",
                    odds=2.5,
                    implied_probability=40.0,
                    created_by=test_market.created_by,
                    updated_by=test_market.updated_by
                ),
                BettingOutcome(
                    market_id=test_market.id,
                    name="Player 2", 
                    odds=1.8,
                    implied_probability=55.56,
                    created_by=test_market.created_by,
                    updated_by=test_market.updated_by
                )
            ]
            
            for outcome in outcomes:
                session.add(outcome)
            
            await session.commit()
            
            print(f"✅ Created test betting market: {test_market.id}")
            print(f"✅ Created {len(outcomes)} outcomes")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        return False


async def main():
    """Main test function"""
    print("🎲 BetBet Betting Database Test Suite")
    print("=" * 50)
    
    # Test database connectivity
    if await test_betting_tables():
        # Optionally create test data
        # await create_test_data()
        pass
    
    print("\n✅ Testing complete!")


if __name__ == "__main__":
    asyncio.run(main())