#!/usr/bin/env python3
"""
Direct test of betting functionality bypassing API issues
"""

import asyncio
import sys
from pathlib import Path
from uuid import uuid4
from datetime import datetime, timedelta

# Add project path
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import after path setup
from services.shared.core.database.connection import DatabaseManager, DatabaseSettings
from app.database.models import (
    BettingMarket, BettingOutcome, UserBet, 
    GameSession, Game, User
)
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession


async def test_betting_system():
    """Direct test of betting system"""
    print("🎲 Testing Betting System Directly")
    print("=" * 50)
    
    # Initialize database
    db_settings = DatabaseSettings(
        DATABASE_URL="postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_db"
    )
    db_manager = DatabaseManager(db_settings)
    await db_manager.initialize()
    
    async with db_manager.get_write_session() as session:
        # 1. Check betting tables
        print("\n1️⃣ Checking betting tables...")
        markets_count = await session.scalar(select(func.count(BettingMarket.id)))
        outcomes_count = await session.scalar(select(func.count(BettingOutcome.id)))
        bets_count = await session.scalar(select(func.count(UserBet.id)))
        
        print(f"✅ Betting tables found:")
        print(f"   - Markets: {markets_count}")
        print(f"   - Outcomes: {outcomes_count}")
        print(f"   - Bets: {bets_count}")
        
        # 2. Get or create test data
        print("\n2️⃣ Getting test data...")
        
        # Get a game
        game = await session.scalar(select(Game).limit(1))
        if not game:
            print("❌ No games found! Run /admin/add-test-data first")
            return
        print(f"✅ Using game: {game.name} ({game.id})")
        
        # Get a user
        user = await session.scalar(select(User).limit(1))
        if not user:
            print("❌ No users found!")
            return
        print(f"✅ Using user: {user.username} ({user.id})")
        
        # 3. Create a test game session
        print("\n3️⃣ Creating test game session...")
        test_session = GameSession(
            game_id=game.id,
            session_name="Test Betting Session",
            session_code=f"TEST{uuid4().hex[:6].upper()}",
            max_participants=4,
            current_participants=2,
            state="active",
            entry_fee=10.0,
            prize_pool=40.0,
            host_id=user.id,
            created_by=user.id,
            updated_by=user.id,
            actual_started_at=datetime.utcnow()
        )
        session.add(test_session)
        await session.flush()
        print(f"✅ Created session: {test_session.session_code} ({test_session.id})")
        
        # 4. Create betting markets
        print("\n4️⃣ Creating betting markets...")
        
        # Winner market
        winner_market = BettingMarket(
            session_id=test_session.id,
            name="Winner",
            description="Who will win this game?",
            market_type="winner",
            status="active",
            opens_at=datetime.utcnow(),
            closes_at=datetime.utcnow() + timedelta(hours=1),
            created_by=user.id,
            updated_by=user.id
        )
        session.add(winner_market)
        await session.flush()
        
        # Create outcomes
        outcomes = []
        for i in range(1, 3):
            outcome = BettingOutcome(
                market_id=winner_market.id,
                name=f"Player {i}",
                odds=2.0 + (i * 0.5),
                implied_probability=100 / (2.0 + (i * 0.5)),
                created_by=user.id,
                updated_by=user.id
            )
            outcomes.append(outcome)
            session.add(outcome)
        
        await session.flush()
        print(f"✅ Created market: {winner_market.name} with {len(outcomes)} outcomes")
        
        # 5. Place a test bet
        print("\n5️⃣ Placing test bet...")
        test_bet = UserBet(
            user_id=user.id,
            market_id=winner_market.id,
            outcome_id=outcomes[0].id,
            bet_amount=5.0,
            odds=outcomes[0].odds,
            potential_return=5.0 * outcomes[0].odds,
            status="pending",
            created_by=user.id,
            updated_by=user.id
        )
        session.add(test_bet)
        
        # Update outcome stats
        outcomes[0].total_bet_amount += test_bet.bet_amount
        outcomes[0].bet_count += 1
        
        # Update market stats
        winner_market.total_pool += test_bet.bet_amount
        winner_market.total_bets += 1
        
        await session.commit()
        print(f"✅ Placed bet: ${test_bet.bet_amount} on {outcomes[0].name} @ {test_bet.odds}")
        
        # 6. Verify everything
        print("\n6️⃣ Verifying betting data...")
        
        # Get market with outcomes
        market_query = select(BettingMarket).where(
            BettingMarket.id == winner_market.id
        )
        verified_market = await session.scalar(market_query)
        
        print(f"✅ Market verified:")
        print(f"   - Name: {verified_market.name}")
        print(f"   - Total pool: ${verified_market.total_pool}")
        print(f"   - Total bets: {verified_market.total_bets}")
        
        # Get bet
        bet_query = select(UserBet).where(UserBet.id == test_bet.id)
        verified_bet = await session.scalar(bet_query)
        
        print(f"✅ Bet verified:")
        print(f"   - Amount: ${verified_bet.bet_amount}")
        print(f"   - Potential return: ${verified_bet.potential_return}")
        print(f"   - Status: {verified_bet.status}")
        
        print("\n✅ All betting functionality working correctly!")
        print(f"\n📝 Test data created:")
        print(f"   - Session ID: {test_session.id}")
        print(f"   - Session Code: {test_session.session_code}")
        print(f"   - Market ID: {winner_market.id}")
        print(f"   - Bet ID: {test_bet.id}")
        
        return {
            "session_id": str(test_session.id),
            "market_id": str(winner_market.id),
            "outcome_ids": [str(o.id) for o in outcomes],
            "bet_id": str(test_bet.id)
        }
    
    await db_manager.shutdown()


async def main():
    """Run the test"""
    try:
        test_data = await test_betting_system()
        
        if test_data:
            print("\n🎯 Use this data for API testing:")
            print(f"export TEST_SESSION_ID={test_data['session_id']}")
            print(f"export TEST_MARKET_ID={test_data['market_id']}")
            print(f"export TEST_BET_ID={test_data['bet_id']}")
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())