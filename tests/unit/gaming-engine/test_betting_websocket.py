#!/usr/bin/env python3
"""
Test betting WebSocket functionality
"""

import asyncio
import websockets
import json


async def test_betting_websocket():
    """Test betting WebSocket endpoints"""
    print("🔌 Testing Betting WebSocket Functionality")
    print("=" * 50)
    
    # Test market ID (would be real in production)
    test_market_id = "550e8400-e29b-41d4-a716-446655440000"
    ws_url = f"ws://localhost:8002/ws/betting/{test_market_id}"
    
    print(f"\n1️⃣ Connecting to WebSocket: {ws_url}")
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ Connected to betting WebSocket")
            
            # Wait for initial connection message
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                data = json.loads(message)
                print(f"📨 Received: {data}")
                
                if data.get("type") == "connection":
                    print("✅ Connection established")
                elif data.get("type") == "error":
                    print(f"⚠️  Error: {data.get('message')}")
                
            except asyncio.TimeoutError:
                print("⏱️  No initial message received (timeout)")
            
            # Test sending a message
            print("\n2️⃣ Sending test message...")
            test_message = {
                "type": "subscribe",
                "market_id": test_market_id
            }
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                data = json.loads(response)
                print(f"📨 Received: {data}")
            except asyncio.TimeoutError:
                print("⏱️  No response received (timeout)")
            
            print("\n✅ WebSocket test complete")
            
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket connection failed: {e}")
        if e.status_code == 404:
            print("   Endpoint not found - check if betting WebSocket is registered")
        elif e.status_code == 403:
            print("   Access forbidden - authentication may be required")
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
    
    # Also test the general WebSocket endpoints
    print("\n3️⃣ Checking other WebSocket endpoints...")
    
    endpoints = [
        ("Session WebSocket", f"ws://localhost:8002/ws/sessions/{test_market_id}"),
        ("Spectate WebSocket", f"ws://localhost:8002/ws/spectate/{test_market_id}"),
        ("Tournament WebSocket", f"ws://localhost:8002/ws/tournaments/{test_market_id}")
    ]
    
    for name, url in endpoints:
        try:
            async with websockets.connect(url) as ws:
                print(f"✅ {name} is accessible")
                await ws.close()
        except websockets.exceptions.InvalidStatusCode as e:
            print(f"❌ {name} returned status {e.status_code}")
        except Exception as e:
            print(f"❌ {name} error: {type(e).__name__}")


async def main():
    """Run WebSocket tests"""
    await test_betting_websocket()
    
    print("\n📝 WebSocket Testing Summary:")
    print("1. Betting WebSocket endpoint is registered at /ws/betting/{market_id}")
    print("2. Real-time updates will flow when bets are placed or odds change")
    print("3. Authentication may be required for production use")
    print("4. Frontend should establish WebSocket connection for live updates")


if __name__ == "__main__":
    asyncio.run(main())