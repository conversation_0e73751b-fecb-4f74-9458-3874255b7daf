"""
Unit tests for RAG engine functionality.
"""

import pytest
import asyncio
from datetime import datetime
from uuid import uuid4
from unittest.mock import Mock, AsyncMock, patch
import numpy as np

from app.core.rag_engine import RAGEngine, initialize_rag_engine, get_rag_engine, health_check


class TestRAGEngine:
    """Test cases for RAG engine."""
    
    @pytest.fixture
    def mock_vector_store(self):
        """Mock vector store for testing."""
        mock = AsyncMock()
        mock.search.return_value = [
            {
                'id': 'doc1',
                'content': 'Arsenal will play Chelsea on Saturday at Emirates Stadium',
                'metadata': {'type': 'fixture', 'date': '2025-01-25'},
                'similarity': 0.85
            },
            {
                'id': 'doc2',
                'content': 'Liverpool faces Manchester City in a crucial match',
                'metadata': {'type': 'fixture', 'date': '2025-01-26'},
                'similarity': 0.75
            }
        ]
        return mock
    
    @pytest.fixture
    def mock_embeddings(self):
        """Mock embeddings generator."""
        mock = Mock()
        mock.generate.return_value = np.random.rand(768).tolist()
        return mock
    
    @pytest.fixture
    def rag_engine(self, mock_vector_store, mock_embeddings):
        """Create RAG engine with mocked dependencies."""
        engine = RAGEngine()
        engine.vector_store = mock_vector_store
        engine.embeddings = mock_embeddings
        engine.initialized = True
        return engine
    
    @pytest.mark.asyncio
    async def test_process_query_basic(self, rag_engine):
        """Test basic query processing."""
        query = "Which London teams play on Saturday?"
        
        result = await rag_engine.process_query(query)
        
        assert result['query'] == query
        assert result['success'] is True
        assert len(result['results']) == 2
        assert result['results'][0]['similarity'] > result['results'][1]['similarity']
        assert 'processing_time' in result
        assert 'metadata' in result
    
    @pytest.mark.asyncio
    async def test_process_query_with_filters(self, rag_engine):
        """Test query processing with filters."""
        query = "Show me Arsenal matches"
        filters = {
            'team': 'Arsenal',
            'date_from': '2025-01-20',
            'date_to': '2025-01-31'
        }
        
        result = await rag_engine.process_query(query, filters=filters)
        
        assert result['success'] is True
        assert result['metadata']['filters'] == filters
        rag_engine.vector_store.search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_query_with_context(self, rag_engine):
        """Test query processing with additional context."""
        query = "What about their recent form?"
        context = {
            'previous_query': 'Tell me about Arsenal',
            'session_id': 'session123'
        }
        
        result = await rag_engine.process_query(query, context=context)
        
        assert result['success'] is True
        assert result['metadata']['context'] == context
    
    @pytest.mark.asyncio
    async def test_semantic_search(self, rag_engine):
        """Test semantic search functionality."""
        query = "Premier League fixtures this weekend"
        top_k = 5
        
        results = await rag_engine.semantic_search(query, top_k=top_k)
        
        assert len(results) <= top_k
        assert all('content' in r for r in results)
        assert all('similarity' in r for r in results)
        assert all(0 <= r['similarity'] <= 1 for r in results)
    
    @pytest.mark.asyncio
    async def test_add_document(self, rag_engine):
        """Test adding document to vector store."""
        content = "Manchester United plays Tottenham on Sunday at Old Trafford"
        metadata = {
            'type': 'fixture',
            'teams': ['Manchester United', 'Tottenham'],
            'date': '2025-01-26',
            'venue': 'Old Trafford'
        }
        
        doc_id = await rag_engine.add_document(content, metadata)
        
        assert doc_id is not None
        rag_engine.embeddings.generate.assert_called_once_with(content)
        rag_engine.vector_store.add.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_add_documents(self, rag_engine):
        """Test batch adding documents."""
        documents = [
            {
                'content': 'Chelsea vs Liverpool match preview',
                'metadata': {'type': 'preview', 'teams': ['Chelsea', 'Liverpool']}
            },
            {
                'content': 'Arsenal injury update before big match',
                'metadata': {'type': 'news', 'team': 'Arsenal'}
            }
        ]
        
        doc_ids = await rag_engine.batch_add_documents(documents)
        
        assert len(doc_ids) == len(documents)
        assert rag_engine.embeddings.generate.call_count == len(documents)
        rag_engine.vector_store.batch_add.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_document(self, rag_engine):
        """Test updating existing document."""
        doc_id = 'doc123'
        new_content = "Updated: Arsenal match postponed due to weather"
        new_metadata = {'type': 'update', 'reason': 'weather'}
        
        success = await rag_engine.update_document(doc_id, new_content, new_metadata)
        
        assert success is True
        rag_engine.vector_store.update.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_document(self, rag_engine):
        """Test deleting document."""
        doc_id = 'doc123'
        
        success = await rag_engine.delete_document(doc_id)
        
        assert success is True
        rag_engine.vector_store.delete.assert_called_once_with(doc_id)
    
    @pytest.mark.asyncio
    async def test_generate_answer(self, rag_engine):
        """Test answer generation from retrieved context."""
        query = "When does Arsenal play next?"
        context = [
            "Arsenal will play Chelsea on Saturday at Emirates Stadium",
            "The match starts at 3 PM GMT"
        ]
        
        with patch('app.core.rag_engine.generate_ai_response') as mock_ai:
            mock_ai.return_value = "Arsenal's next match is against Chelsea on Saturday at 3 PM GMT at Emirates Stadium."
            
            answer = await rag_engine.generate_answer(query, context)
            
            assert answer is not None
            assert "Arsenal" in answer
            assert "Chelsea" in answer
            mock_ai.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extract_entities(self, rag_engine):
        """Test entity extraction from text."""
        text = "Arsenal faces Chelsea at Emirates Stadium on Saturday. Mohamed Salah scored for Liverpool."
        
        entities = await rag_engine.extract_entities(text)
        
        assert 'teams' in entities
        assert 'Arsenal' in entities['teams']
        assert 'Chelsea' in entities['teams']
        assert 'Liverpool' in entities['teams']
        assert 'venues' in entities
        assert 'Emirates Stadium' in entities['venues']
        assert 'players' in entities
        assert 'Mohamed Salah' in entities['players']
    
    @pytest.mark.asyncio
    async def test_similarity_threshold(self, rag_engine):
        """Test similarity threshold filtering."""
        query = "London derby matches"
        threshold = 0.8
        
        # Mock vector store to return results with varying similarities
        rag_engine.vector_store.search.return_value = [
            {'content': 'Arsenal vs Chelsea derby', 'similarity': 0.9},
            {'content': 'Liverpool match', 'similarity': 0.7},
            {'content': 'Tottenham vs Arsenal derby', 'similarity': 0.85}
        ]
        
        results = await rag_engine.semantic_search(query, similarity_threshold=threshold)
        
        assert all(r['similarity'] >= threshold for r in results)
        assert len(results) == 2  # Only results above threshold
    
    @pytest.mark.asyncio
    async def test_health_check(self, rag_engine):
        """Test RAG engine health check."""
        status = await rag_engine.health_check()
        
        assert status == "healthy"
        
        # Test unhealthy state
        rag_engine.initialized = False
        status = await rag_engine.health_check()
        assert status == "not_initialized"
    
    @pytest.mark.asyncio
    async def test_reindex_documents(self, rag_engine):
        """Test reindexing all documents."""
        rag_engine.vector_store.get_all_documents.return_value = [
            {'id': 'doc1', 'content': 'Content 1', 'metadata': {}},
            {'id': 'doc2', 'content': 'Content 2', 'metadata': {}}
        ]
        
        count = await rag_engine.reindex_documents()
        
        assert count == 2
        assert rag_engine.embeddings.generate.call_count == 2
    
    @pytest.mark.asyncio
    async def test_clear_index(self, rag_engine):
        """Test clearing vector index."""
        success = await rag_engine.clear_index()
        
        assert success is True
        rag_engine.vector_store.clear.assert_called_once()


class TestRAGEngineIntegration:
    """Integration tests for RAG engine with actual components."""
    
    @pytest.mark.asyncio
    async def test_natural_language_queries(self):
        """Test various natural language query patterns."""
        # This would be an integration test with actual components
        # For unit tests, we'll mock the behavior
        
        queries = [
            "Which London teams play at home this Saturday?",
            "Show me teams with the best recent away form",
            "What are the odds for Arsenal vs Chelsea?",
            "Compare Liverpool's attack to Manchester City's defense"
        ]
        
        with patch('app.core.rag_engine.get_rag_engine') as mock_get:
            mock_engine = AsyncMock()
            mock_engine.process_query.return_value = {
                'success': True,
                'results': [{'content': 'Mock result'}],
                'answer': 'Mock answer'
            }
            mock_get.return_value = mock_engine
            
            for query in queries:
                result = await mock_engine.process_query(query)
                assert result['success'] is True
                assert 'answer' in result