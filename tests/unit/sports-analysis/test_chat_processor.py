"""
Unit tests for Chat Processor.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.core.chat_processor import ChatProcessor, get_chat_processor


class TestChatProcessor:
    """Test cases for ChatProcessor class."""
    
    @pytest.fixture
    def chat_processor(self):
        """Create ChatProcessor instance for testing."""
        return ChatProcessor()
    
    @pytest.mark.asyncio
    async def test_chat_processor_initialization(self, chat_processor):
        """Test chat processor initializes correctly."""
        assert chat_processor is not None
        assert hasattr(chat_processor, 'conversation_contexts')
        assert hasattr(chat_processor, 'metrics')
        assert chat_processor.metrics['messages_processed'] == 0
    
    @pytest.mark.asyncio
    async def test_get_conversation_context(self, chat_processor):
        """Test getting conversation context."""
        session_id = uuid4()
        
        context = await chat_processor._get_conversation_context(session_id)
        
        assert context is not None
        assert 'created_at' in context
        assert 'message_count' in context
        assert 'recent_messages' in context
        assert context['message_count'] == 0
        assert len(context['recent_messages']) == 0
    
    @pytest.mark.asyncio
    async def test_update_conversation_context(self, chat_processor):
        """Test updating conversation context."""
        session_id = uuid4()
        user_message = "Which teams play this weekend?"
        ai_response = "Several Premier League teams are playing this weekend."
        query_analysis = {'intent': 'fixture_inquiry', 'topics': ['fixtures']}
        
        await chat_processor._update_conversation_context(
            session_id, user_message, ai_response, query_analysis
        )
        
        context = await chat_processor._get_conversation_context(session_id)
        
        assert context['message_count'] == 1
        assert len(context['recent_messages']) == 1
        assert context['recent_messages'][0]['user_message'] == user_message
        assert context['recent_messages'][0]['ai_response'] == ai_response
        assert context['recent_messages'][0]['intent'] == 'fixture_inquiry'
    
    @pytest.mark.asyncio
    async def test_build_system_prompt(self, chat_processor):
        """Test system prompt building."""
        intent = 'team_analysis'
        prompt = chat_processor._build_system_prompt(intent)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 100  # Should be a substantial prompt
        assert 'team performance' in prompt.lower() or 'team' in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_extract_response_metadata(self, chat_processor):
        """Test response metadata extraction."""
        response = "Arsenal has been performing well this season with 15 wins."
        query_analysis = {'intent': 'team_analysis', 'topics': ['Arsenal']}
        
        metadata = chat_processor._extract_response_metadata(response, query_analysis)
        
        assert 'tokens_used' in metadata
        assert 'cost' in metadata
        assert 'confidence' in metadata
        assert 'entities' in metadata
        assert 'topics' in metadata
        assert 'follow_ups' in metadata
        
        assert metadata['tokens_used'] > 0
        assert metadata['cost'] >= 0
        assert 0 <= metadata['confidence'] <= 1
        assert isinstance(metadata['follow_ups'], list)
    
    @pytest.mark.asyncio
    async def test_generate_follow_ups(self, chat_processor):
        """Test follow-up question generation."""
        follow_ups = chat_processor._generate_follow_ups('fixture_inquiry')
        
        assert isinstance(follow_ups, list)
        assert len(follow_ups) > 0
        
        # Test different intents
        team_follow_ups = chat_processor._generate_follow_ups('team_analysis')
        assert isinstance(team_follow_ups, list)
        assert len(team_follow_ups) > 0
    
    @pytest.mark.asyncio 
    async def test_health_check(self, chat_processor):
        """Test chat processor health check."""
        # Mock the OpenAI client for testing
        class MockOpenAIClient:
            class ChatCompletions:
                async def create(self, **kwargs):
                    class MockResponse:
                        choices = [
                            type('obj', (object,), {
                                'message': type('obj', (object,), {
                                    'content': 'Hello! How can I help you with sports analysis today?'
                                })()
                            })()
                        ]
                    return MockResponse()
            
            chat = ChatCompletions()
        
        chat_processor.openai_client = MockOpenAIClient()
        
        status = await chat_processor.health_check()
        assert status == "healthy"
    
    def test_get_metrics(self, chat_processor):
        """Test getting performance metrics."""
        metrics = chat_processor.get_metrics()
        
        assert isinstance(metrics, dict)
        assert 'messages_processed' in metrics
        assert 'average_response_time' in metrics
        assert 'active_conversations' in metrics
        assert 'uptime' in metrics
        
        assert metrics['messages_processed'] >= 0
        assert metrics['active_conversations'] == 0  # No active conversations yet
    
    def test_singleton_pattern(self):
        """Test that get_chat_processor returns the same instance."""
        processor1 = get_chat_processor()
        processor2 = get_chat_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, ChatProcessor)


class TestChatProcessorIntegration:
    """Integration tests for ChatProcessor with mocked dependencies."""
    
    @pytest.mark.asyncio
    async def test_process_message_flow(self):
        """Test the complete message processing flow."""
        processor = get_chat_processor()
        
        # Mock the query processor
        class MockQueryProcessor:
            async def analyze_query(self, query):
                return {
                    'intent': 'fixture_inquiry',
                    'requires_data': True,
                    'topics': ['Premier League']
                }
        
        # Mock the RAG engine
        class MockRAGEngine:
            async def query(self, **kwargs):
                return {
                    'results': [
                        {
                            'content': 'Arsenal plays Chelsea this Saturday at 3 PM',
                            'source': 'fixture_database',
                            'confidence': 0.95
                        }
                    ],
                    'similarity_scores': [0.95]
                }
        
        # Mock the OpenAI client
        class MockOpenAIClient:
            class ChatCompletions:
                async def create(self, **kwargs):
                    class MockResponse:
                        choices = [
                            type('obj', (object,), {
                                'message': type('obj', (object,), {
                                    'content': 'Based on the fixture data, Arsenal plays Chelsea this Saturday at 3 PM at the Emirates Stadium.'
                                })()
                            })()
                        ]
                    return MockResponse()
            
            chat = ChatCompletions()
        
        # Inject mocks
        processor.query_processor = MockQueryProcessor()
        processor.rag_engine = MockRAGEngine()
        processor.openai_client = MockOpenAIClient()
        
        # Test message processing
        session_id = uuid4()
        user_id = uuid4()
        message = "When does Arsenal play next?"
        
        result = await processor.process_message(
            session_id=session_id,
            user_message=message,
            user_id=user_id,
            rag_enabled=True
        )
        
        # Verify result structure
        assert isinstance(result, dict)
        assert 'response' in result
        assert 'response_type' in result
        assert 'rag_used' in result
        assert 'retrieval_results' in result
        assert 'confidence' in result
        assert 'processing_time_ms' in result
        
        # Verify content
        assert result['rag_used'] is True
        assert len(result['retrieval_results']) > 0
        assert result['processing_time_ms'] > 0
        assert 'Arsenal' in result['response'] or 'Chelsea' in result['response']
        
        # Verify metrics updated
        assert processor.metrics['messages_processed'] == 1
        assert processor.metrics['rag_queries'] == 1
    
    @pytest.mark.asyncio
    async def test_stream_message_flow(self):
        """Test streaming message processing."""
        processor = get_chat_processor()
        
        # Mock dependencies (same as above)
        class MockQueryProcessor:
            async def analyze_query(self, query):
                return {
                    'intent': 'team_analysis',
                    'requires_data': True,
                    'topics': ['Manchester United']
                }
        
        class MockRAGEngine:
            async def query(self, **kwargs):
                return {
                    'results': [
                        {'content': 'Manchester United has won 15 games this season'}
                    ],
                    'similarity_scores': [0.88]
                }
        
        processor.query_processor = MockQueryProcessor()
        processor.rag_engine = MockRAGEngine()
        
        # Mock streaming response
        async def mock_stream_ai_response(self, **kwargs):
            yield {
                'type': 'content',
                'content': 'Manchester United has been performing',
                'metadata': {'is_streaming': True}
            }
            yield {
                'type': 'content',
                'content': ' well this season with strong form.',
                'metadata': {'is_streaming': True}
            }
            yield {
                'type': 'content_complete',
                'content': '',
                'metadata': {'is_streaming': False, 'complete_response': 'Manchester United has been performing well this season with strong form.'}
            }
        
        processor._stream_ai_response = mock_stream_ai_response
        
        # Test streaming
        session_id = uuid4()
        user_id = uuid4()
        message = "How is Manchester United performing?"
        
        chunks = []
        async for chunk in processor.stream_message(
            session_id=session_id,
            user_message=message,
            user_id=user_id,
            rag_enabled=True
        ):
            chunks.append(chunk)
        
        # Verify streaming chunks
        assert len(chunks) > 0
        
        # Check for expected chunk types
        chunk_types = [chunk['type'] for chunk in chunks]
        assert 'status' in chunk_types
        assert 'analysis' in chunk_types
        assert 'complete' in chunk_types
        
        # Verify final completion
        final_chunk = chunks[-1]
        assert final_chunk['type'] == 'complete'
        assert 'rag_used' in final_chunk['metadata']