"""
Unit tests for Prediction Engine.
"""

import pytest
from datetime import datetime, date
from uuid import uuid4

from app.core.prediction_engine import PredictionEngine, get_prediction_engine


class TestPredictionEngine:
    """Test cases for PredictionEngine class."""
    
    @pytest.fixture
    def prediction_engine(self):
        """Create PredictionEngine instance for testing."""
        return PredictionEngine()
    
    @pytest.mark.asyncio
    async def test_prediction_engine_initialization(self, prediction_engine):
        """Test prediction engine initializes correctly."""
        assert prediction_engine is not None
        assert hasattr(prediction_engine, 'models')
        assert hasattr(prediction_engine, 'prediction_cache')
        assert 'match_outcome' in prediction_engine.models
        assert 'team_performance' in prediction_engine.models
        assert 'player_performance' in prediction_engine.models
    
    @pytest.mark.asyncio
    async def test_get_team_strength(self, prediction_engine):
        """Test team strength calculation."""
        team_id = 1
        strength = await prediction_engine._get_team_strength(team_id)
        
        assert isinstance(strength, float)
        assert 1.0 <= strength <= 10.0
        
        # Test consistency - same team should return same strength
        strength2 = await prediction_engine._get_team_strength(team_id)
        assert strength == strength2
    
    def test_predict_score(self, prediction_engine):
        """Test score prediction."""
        home_strength = 7.5
        away_strength = 6.2
        
        predicted_score = prediction_engine._predict_score(home_strength, away_strength)
        
        assert isinstance(predicted_score, str)
        assert '-' in predicted_score
        
        # Parse score
        home_goals, away_goals = predicted_score.split('-')
        assert home_goals.isdigit()
        assert away_goals.isdigit()
        assert int(home_goals) >= 0
        assert int(away_goals) >= 0
    
    @pytest.mark.asyncio
    async def test_generate_match_prediction(self, prediction_engine, mock_fixture_data):
        """Test single match prediction generation."""
        prediction = await prediction_engine._generate_match_prediction(
            fixture=mock_fixture_data,
            prediction_type="outcome"
        )
        
        assert prediction is not None
        assert isinstance(prediction, dict)
        
        # Check required fields
        required_fields = [
            'fixture_id', 'home_team_name', 'away_team_name',
            'predicted_outcome', 'home_win_probability', 'draw_probability',
            'away_win_probability', 'confidence_score', 'key_factors'
        ]
        
        for field in required_fields:
            assert field in prediction, f"Missing field: {field}"
        
        # Validate probability values
        assert 0 <= prediction['home_win_probability'] <= 1
        assert 0 <= prediction['draw_probability'] <= 1
        assert 0 <= prediction['away_win_probability'] <= 1
        
        # Probabilities should sum to approximately 1
        total_prob = (
            prediction['home_win_probability'] +
            prediction['draw_probability'] +
            prediction['away_win_probability']
        )
        assert 0.99 <= total_prob <= 1.01
        
        # Confidence should be reasonable
        assert 0 <= prediction['confidence_score'] <= 1
        
        # Predicted outcome should match highest probability
        probs = {
            'home_win': prediction['home_win_probability'],
            'draw': prediction['draw_probability'],
            'away_win': prediction['away_win_probability']
        }
        max_outcome = max(probs.keys(), key=lambda x: probs[x])
        assert prediction['predicted_outcome'] == max_outcome
    
    @pytest.mark.asyncio
    async def test_get_match_predictions(self, prediction_engine):
        """Test getting multiple match predictions."""
        # Mock API-Football service
        class MockAPIFootball:
            async def get_fixtures(self, **kwargs):
                return [
                    {
                        'fixture': {'id': 1, 'date': '2025-01-25T15:00:00Z'},
                        'teams': {
                            'home': {'id': 1, 'name': 'Arsenal'},
                            'away': {'id': 2, 'name': 'Chelsea'}
                        },
                        'league': {'name': 'Premier League'}
                    },
                    {
                        'fixture': {'id': 2, 'date': '2025-01-26T17:30:00Z'},
                        'teams': {
                            'home': {'id': 3, 'name': 'Liverpool'},
                            'away': {'id': 4, 'name': 'Manchester City'}
                        },
                        'league': {'name': 'Premier League'}
                    }
                ]
        
        prediction_engine.api_football = MockAPIFootball()
        
        result = await prediction_engine.get_match_predictions(
            league_id=39,
            confidence_threshold=0.5,
            page=1,
            limit=10
        )
        
        assert isinstance(result, dict)
        assert 'predictions' in result
        assert 'total_count' in result
        assert isinstance(result['predictions'], list)
        assert result['total_count'] >= 0
        
        # Check prediction structure if any predictions returned
        if result['predictions']:
            prediction = result['predictions'][0]
            assert 'fixture_id' in prediction
            assert 'confidence_score' in prediction
            assert prediction['confidence_score'] >= 0.5  # Should meet threshold
    
    @pytest.mark.asyncio
    async def test_get_match_prediction_detail(self, prediction_engine):
        """Test detailed match prediction."""
        fixture_id = 12345
        
        # Mock API-Football service
        class MockAPIFootball:
            async def get_fixture(self, fixture_id):
                return {
                    'fixture': {'id': fixture_id, 'date': '2025-01-25T15:00:00Z'},
                    'teams': {
                        'home': {'id': 1, 'name': 'Arsenal'},
                        'away': {'id': 2, 'name': 'Chelsea'}
                    },
                    'league': {'name': 'Premier League'}
                }
        
        prediction_engine.api_football = MockAPIFootball()
        
        detailed_prediction = await prediction_engine.get_match_prediction_detail(
            fixture_id=fixture_id,
            include_analysis=True
        )
        
        assert detailed_prediction is not None
        assert isinstance(detailed_prediction, dict)
        
        # Should have additional analysis fields when include_analysis=True
        analysis_fields = ['home_team_form', 'away_team_form', 'head_to_head', 'expected_goals']
        for field in analysis_fields:
            assert field in detailed_prediction
        
        # Test without detailed analysis
        basic_prediction = await prediction_engine.get_match_prediction_detail(
            fixture_id=fixture_id,
            include_analysis=False
        )
        
        assert basic_prediction is not None
        # Should have basic prediction without extended analysis
        assert 'predicted_outcome' in basic_prediction
        assert 'confidence_score' in basic_prediction
    
    @pytest.mark.asyncio
    async def test_generate_custom_prediction(self, prediction_engine):
        """Test custom prediction generation."""
        fixture_id = 12345
        user_id = uuid4()
        
        # Mock API-Football service
        class MockAPIFootball:
            async def get_fixture(self, fixture_id):
                return {
                    'fixture': {'id': fixture_id, 'date': '2025-01-25T15:00:00Z'},
                    'teams': {
                        'home': {'id': 1, 'name': 'Arsenal'},
                        'away': {'id': 2, 'name': 'Chelsea'}
                    },
                    'league': {'name': 'Premier League'}
                }
        
        prediction_engine.api_football = MockAPIFootball()
        
        custom_result = await prediction_engine.generate_custom_prediction(
            fixture_id=fixture_id,
            prediction_type='outcome',
            model_weights={'form': 0.4, 'h2h': 0.3, 'strength': 0.3},
            feature_preferences={'focus': 'recent_form'},
            risk_tolerance='conservative',
            user_id=user_id
        )
        
        assert isinstance(custom_result, dict)
        
        required_fields = [
            'prediction_id', 'result', 'confidence', 'risk_assessment',
            'model_weights', 'feature_preferences', 'insights', 'recommendation'
        ]
        
        for field in required_fields:
            assert field in custom_result
        
        # Validate custom parameters were stored
        assert custom_result['model_weights']['form'] == 0.4
        assert custom_result['feature_preferences']['focus'] == 'recent_form'
        assert custom_result['risk_assessment'] in ['low', 'medium', 'high']
        assert isinstance(custom_result['insights'], list)
        assert len(custom_result['insights']) > 0
    
    @pytest.mark.asyncio
    async def test_get_team_performance_prediction(self, prediction_engine):
        """Test team performance prediction."""
        team_id = 1
        
        # Mock API-Football service
        class MockAPIFootball:
            async def get_team_info(self, team_id):
                return {'id': team_id, 'name': 'Arsenal'}
        
        prediction_engine.api_football = MockAPIFootball()
        
        team_prediction = await prediction_engine.get_team_performance_prediction(
            team_id=team_id,
            prediction_period="next_5_games"
        )
        
        assert team_prediction is not None
        assert isinstance(team_prediction, dict)
        
        # Check required fields
        required_fields = [
            'team_name', 'expected_points', 'win_probability',
            'expected_goals_for', 'expected_goals_against',
            'form_trend', 'confidence_score'
        ]
        
        for field in required_fields:
            assert field in team_prediction
        
        # Validate probability values
        assert 0 <= team_prediction['win_probability'] <= 1
        assert team_prediction['expected_points'] >= 0
        assert team_prediction['expected_goals_for'] >= 0
        assert team_prediction['expected_goals_against'] >= 0
    
    @pytest.mark.asyncio
    async def test_get_player_performance_prediction(self, prediction_engine):
        """Test player performance prediction."""
        player_id = 123
        
        player_prediction = await prediction_engine.get_player_performance_prediction(
            player_id=player_id,
            prediction_type="performance",
            upcoming_fixtures=5
        )
        
        assert player_prediction is not None
        assert isinstance(player_prediction, dict)
        
        # Check required fields
        required_fields = [
            'player_name', 'team_name', 'position',
            'expected_goals', 'expected_assists', 'expected_minutes',
            'current_form', 'confidence_score'
        ]
        
        for field in required_fields:
            assert field in player_prediction
        
        # Validate numerical values
        assert player_prediction['expected_goals'] >= 0
        assert player_prediction['expected_assists'] >= 0
        assert player_prediction['expected_minutes'] >= 0
        assert 0 <= player_prediction['confidence_score'] <= 1
    
    @pytest.mark.asyncio
    async def test_get_model_performance(self, prediction_engine):
        """Test model performance statistics."""
        performances = await prediction_engine.get_model_performance(
            time_period="last_30_days"
        )
        
        assert isinstance(performances, list)
        assert len(performances) > 0
        
        for performance in performances:
            required_fields = [
                'model_name', 'model_version', 'overall_accuracy',
                'precision', 'recall', 'f1_score', 'total_predictions'
            ]
            
            for field in required_fields:
                assert field in performance
            
            # Validate accuracy metrics
            assert 0 <= performance['overall_accuracy'] <= 1
            assert 0 <= performance['precision'] <= 1
            assert 0 <= performance['recall'] <= 1
            assert 0 <= performance['f1_score'] <= 1
            assert performance['total_predictions'] >= 0
    
    def test_apply_custom_parameters(self, prediction_engine):
        """Test custom parameter application."""
        base_prediction = {
            'predicted_outcome': 'home_win',
            'confidence_score': 0.75,
            'home_win_probability': 0.6
        }
        
        model_weights = {'form': 0.5, 'strength': 0.5}
        feature_preferences = {'focus': 'recent_form'}
        
        # Test conservative risk tolerance
        result = prediction_engine._apply_custom_parameters(
            base_prediction=base_prediction,
            model_weights=model_weights,
            feature_preferences=feature_preferences,
            risk_tolerance='conservative'
        )
        
        assert result['confidence_score'] <= base_prediction['confidence_score']
        
        # Test aggressive risk tolerance
        result = prediction_engine._apply_custom_parameters(
            base_prediction=base_prediction,
            model_weights=model_weights,
            feature_preferences=feature_preferences,
            risk_tolerance='aggressive'
        )
        
        assert result['confidence_score'] >= base_prediction['confidence_score']
    
    def test_assess_custom_risk(self, prediction_engine):
        """Test custom risk assessment."""
        high_confidence_prediction = {'confidence_score': 0.9}
        low_confidence_prediction = {'confidence_score': 0.4}
        
        # Conservative risk tolerance
        conservative_high = prediction_engine._assess_custom_risk(
            high_confidence_prediction, 'conservative'
        )
        conservative_low = prediction_engine._assess_custom_risk(
            low_confidence_prediction, 'conservative'
        )
        
        # Aggressive risk tolerance
        aggressive_high = prediction_engine._assess_custom_risk(
            high_confidence_prediction, 'aggressive'
        )
        aggressive_low = prediction_engine._assess_custom_risk(
            low_confidence_prediction, 'aggressive'
        )
        
        # Validate risk levels
        assert conservative_high in ['low', 'medium', 'high']
        assert conservative_low in ['low', 'medium', 'high']
        assert aggressive_high in ['low', 'medium', 'high']
        assert aggressive_low in ['low', 'medium', 'high']
    
    def test_generate_recommendation(self, prediction_engine):
        """Test recommendation generation."""
        high_confidence_prediction = {
            'predicted_outcome': 'home_win',
            'confidence_score': 0.85
        }
        
        low_confidence_prediction = {
            'predicted_outcome': 'draw',
            'confidence_score': 0.45
        }
        
        high_rec = prediction_engine._generate_recommendation(high_confidence_prediction)
        low_rec = prediction_engine._generate_recommendation(low_confidence_prediction)
        
        assert isinstance(high_rec, str)
        assert isinstance(low_rec, str)
        assert len(high_rec) > 10
        assert len(low_rec) > 10
        
        # High confidence should mention strong confidence
        assert 'strong' in high_rec.lower() or 'high' in high_rec.lower()
        
        # Low confidence should mention caution
        assert 'caution' in low_rec.lower() or 'low' in low_rec.lower()
    
    @pytest.mark.asyncio
    async def test_health_check(self, prediction_engine):
        """Test prediction engine health check."""
        status = await prediction_engine.health_check()
        assert status in ["healthy", "unhealthy", "error"]
    
    def test_singleton_pattern(self):
        """Test that get_prediction_engine returns the same instance."""
        engine1 = get_prediction_engine()
        engine2 = get_prediction_engine()
        
        assert engine1 is engine2
        assert isinstance(engine1, PredictionEngine)