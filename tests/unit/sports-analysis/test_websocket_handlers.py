"""
Unit tests for WebSocket handlers.
"""

import pytest
import asyncio
from datetime import datetime
from uuid import uuid4
from unittest.mock import Mock, AsyncMock, patch

from app.websocket.handlers import (
    LiveMatch<PERSON>andler,
    ChatSessionHandler,
    DocumentProcessingHandler,
    PremiumAlertsHandler
)


class TestLiveMatchHandler:
    """Test cases for LiveMatchHandler."""
    
    @pytest.fixture
    def handler(self):
        return LiveMatchHandler()
    
    @pytest.mark.asyncio
    async def test_initialize(self, handler):
        """Test handler initialization."""
        with patch('app.websocket.handlers.get_api_football_service') as mock_service:
            mock_service.return_value = Mock()
            await handler.initialize()
            assert handler.api_football is not None
    
    @pytest.mark.asyncio
    async def test_get_live_matches_from_cache(self, handler):
        """Test getting live matches from cache."""
        # Setup cache
        cache_data = [
            {'id': 1, 'home': 'Arsenal', 'away': 'Chelsea', 'status': 'live'},
            {'id': 2, 'home': 'Liverpool', 'away': 'City', 'status': 'live'}
        ]
        handler.cache['live_matches'] = {
            'data': cache_data,
            'timestamp': datetime.utcnow().timestamp()
        }
        
        # Get matches
        matches = await handler.get_live_matches()
        assert matches == cache_data
    
    @pytest.mark.asyncio
    async def test_get_live_matches_from_api(self, handler):
        """Test getting live matches from API when cache is invalid."""
        # Mock API service
        mock_api = AsyncMock()
        mock_matches = [
            {'id': 3, 'home': 'United', 'away': 'Spurs', 'status': 'live'}
        ]
        mock_api.get_live_matches.return_value = mock_matches
        handler.api_football = mock_api
        
        # Get matches (cache is empty)
        matches = await handler.get_live_matches()
        assert matches == mock_matches
        assert 'live_matches' in handler.cache
        
    @pytest.mark.asyncio
    async def test_get_match_data(self, handler):
        """Test getting specific match data."""
        match_id = "12345"
        mock_api = AsyncMock()
        mock_match = {
            'id': match_id,
            'home': 'Arsenal',
            'away': 'Chelsea',
            'score': {'home': 2, 'away': 1}
        }
        mock_api.get_match_by_id.return_value = mock_match
        handler.api_football = mock_api
        
        match_data = await handler.get_match_data(match_id)
        assert match_data == mock_match
        mock_api.get_match_by_id.assert_called_once_with(match_id)
    
    @pytest.mark.asyncio
    async def test_health_check(self, handler):
        """Test handler health check."""
        with patch.object(handler, 'get_live_matches', return_value=[]):
            status = await handler.health_check()
            assert status == "healthy"


class TestChatSessionHandler:
    """Test cases for ChatSessionHandler."""
    
    @pytest.fixture
    def handler(self):
        return ChatSessionHandler()
    
    @pytest.mark.asyncio
    async def test_handle_typing_start(self, handler):
        """Test handling typing start message."""
        session_id = "session123"
        user_id = "user456"
        message_data = {
            'type': 'typing_start',
            'user_id': user_id
        }
        
        response = await handler.handle_message(session_id, message_data)
        
        assert response['type'] == 'typing_update'
        assert response['user_id'] == user_id
        assert response['typing'] is True
        assert user_id in handler.typing_users.get(session_id, [])
    
    @pytest.mark.asyncio
    async def test_handle_typing_stop(self, handler):
        """Test handling typing stop message."""
        session_id = "session123"
        user_id = "user456"
        
        # First set user as typing
        handler.typing_users[session_id] = [user_id]
        
        message_data = {
            'type': 'typing_stop',
            'user_id': user_id
        }
        
        response = await handler.handle_message(session_id, message_data)
        
        assert response['type'] == 'typing_update'
        assert response['user_id'] == user_id
        assert response['typing'] is False
        assert user_id not in handler.typing_users.get(session_id, [])
    
    @pytest.mark.asyncio
    async def test_handle_message_sent(self, handler):
        """Test handling message sent event."""
        session_id = "session123"
        message_data = {
            'type': 'message_sent',
            'message_id': 'msg789',
            'user_id': 'user456',
            'content': 'Hello, what are the latest match predictions?'
        }
        
        response = await handler.handle_message(session_id, message_data)
        
        assert response['type'] == 'message_received'
        assert response['session_id'] == session_id
        assert response['message']['content'] == message_data['content']
        assert 'timestamp' in response['message']
    
    @pytest.mark.asyncio
    async def test_get_session_activity(self, handler):
        """Test getting session activity."""
        session_id = "session123"
        handler.typing_users[session_id] = ['user1', 'user2']
        
        activity = await handler.get_session_activity(session_id)
        
        assert activity['session_id'] == session_id
        assert activity['typing_users'] == ['user1', 'user2']
        assert activity['active_users'] == 2
        assert 'last_activity' in activity


class TestDocumentProcessingHandler:
    """Test cases for DocumentProcessingHandler."""
    
    @pytest.fixture
    def handler(self):
        return DocumentProcessingHandler()
    
    @pytest.mark.asyncio
    async def test_update_processing_status(self, handler):
        """Test updating document processing status."""
        document_id = "doc123"
        status = "processing"
        progress = 50
        message = "Processing page 5 of 10"
        
        result = await handler.update_processing_status(
            document_id=document_id,
            status=status,
            progress_percentage=progress,
            message=message
        )
        
        assert result['document_id'] == document_id
        assert result['status'] == status
        assert result['progress_percentage'] == progress
        assert result['message'] == message
        assert document_id in handler.processing_status
    
    @pytest.mark.asyncio
    async def test_handle_processing_started(self, handler):
        """Test handling processing started event."""
        document_id = "doc123"
        filename = "fixtures.pdf"
        file_size = 1024 * 1024  # 1MB
        
        result = await handler.handle_processing_started(
            document_id=document_id,
            filename=filename,
            file_size=file_size
        )
        
        assert result['status'] == 'processing'
        assert result['progress_percentage'] == 0
        assert 'Started processing' in result['message']
        assert result['metadata']['filename'] == filename
        assert result['metadata']['file_size'] == file_size
    
    @pytest.mark.asyncio
    async def test_handle_processing_completed(self, handler):
        """Test handling processing completed event."""
        document_id = "doc123"
        extracted_data = {
            'fixtures': [
                {'home': 'Arsenal', 'away': 'Chelsea', 'date': '2025-01-25'}
            ],
            'teams': ['Arsenal', 'Chelsea'],
            'content_length': 5000
        }
        processing_time = 2.5
        
        result = await handler.handle_processing_completed(
            document_id=document_id,
            extracted_data=extracted_data,
            processing_time_seconds=processing_time
        )
        
        assert result['status'] == 'completed'
        assert result['progress_percentage'] == 100
        assert 'completed successfully' in result['message']
        assert result['metadata']['extracted_data_summary']['fixtures_found'] == 1
        assert result['metadata']['extracted_data_summary']['teams_found'] == 2
    
    @pytest.mark.asyncio
    async def test_cleanup_old_status(self, handler):
        """Test cleanup of old processing status entries."""
        # Add old and new entries
        old_document_id = "old_doc"
        new_document_id = "new_doc"
        
        old_timestamp = datetime.utcnow().timestamp() - (25 * 3600)  # 25 hours ago
        handler.processing_status[old_document_id] = {
            'updated_at': datetime.fromtimestamp(old_timestamp).isoformat()
        }
        handler.processing_status[new_document_id] = {
            'updated_at': datetime.utcnow().isoformat()
        }
        
        await handler.cleanup_old_status(older_than_hours=24)
        
        assert old_document_id not in handler.processing_status
        assert new_document_id in handler.processing_status


class TestPremiumAlertsHandler:
    """Test cases for PremiumAlertsHandler."""
    
    @pytest.fixture
    def handler(self):
        return PremiumAlertsHandler()
    
    @pytest.mark.asyncio
    async def test_send_alert(self, handler):
        """Test sending alert to premium user."""
        user_id = "user123"
        alert_type = "match_update"
        title = "Match Started"
        message = "Arsenal vs Chelsea has kicked off"
        priority = "high"
        
        alert = await handler.send_alert(
            user_id=user_id,
            alert_type=alert_type,
            title=title,
            message=message,
            priority=priority
        )
        
        assert alert['user_id'] == user_id
        assert alert['type'] == alert_type
        assert alert['title'] == title
        assert alert['message'] == message
        assert alert['priority'] == priority
        assert alert['read'] is False
        assert user_id in handler.alert_history
        assert len(handler.alert_history[user_id]) == 1
    
    @pytest.mark.asyncio
    async def test_send_match_alert(self, handler):
        """Test sending match-specific alert."""
        user_id = "user123"
        match_id = "match456"
        team1 = "Arsenal"
        team2 = "Chelsea"
        event_type = "Goal"
        event_details = "Arsenal scores in the 25th minute"
        
        alert = await handler.send_match_alert(
            user_id=user_id,
            match_id=match_id,
            team1=team1,
            team2=team2,
            event_type=event_type,
            event_details=event_details
        )
        
        assert alert['type'] == 'match_event'
        assert alert['title'] == f"{team1} vs {team2}"
        assert event_type in alert['message']
        assert alert['priority'] == 'high'
        assert alert['metadata']['match_id'] == match_id
    
    @pytest.mark.asyncio
    async def test_get_user_alerts(self, handler):
        """Test getting user alerts."""
        user_id = "user123"
        
        # Add some alerts
        for i in range(5):
            await handler.send_alert(
                user_id=user_id,
                alert_type="test",
                title=f"Alert {i}",
                message=f"Test alert {i}",
                priority="medium"
            )
        
        # Mark one as read
        handler.alert_history[user_id][0]['read'] = True
        
        # Get all alerts
        all_alerts = await handler.get_user_alerts(user_id, limit=10)
        assert len(all_alerts) == 5
        
        # Get unread only
        unread_alerts = await handler.get_user_alerts(user_id, limit=10, unread_only=True)
        assert len(unread_alerts) == 4
    
    @pytest.mark.asyncio
    async def test_mark_alert_read(self, handler):
        """Test marking alert as read."""
        user_id = "user123"
        
        # Add an alert
        alert = await handler.send_alert(
            user_id=user_id,
            alert_type="test",
            title="Test Alert",
            message="Test message",
            priority="low"
        )
        
        alert_id = alert['id']
        
        # Mark as read
        success = await handler.mark_alert_read(user_id, alert_id)
        assert success is True
        
        # Check it's marked as read
        alerts = await handler.get_user_alerts(user_id)
        assert alerts[0]['read'] is True
        assert 'read_at' in alerts[0]