# BetBet Platform - Production Environment Variables
# =================================================
# Copy this file to .env.production and fill in the values

# Database Configuration
DATABASE_NAME=betbet_db
DATABASE_USER=postgres
DATABASE_PASSWORD=YOUR_SECURE_DATABASE_PASSWORD
DATABASE_HOST=postgres
DATABASE_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=YOUR_SECURE_REDIS_PASSWORD

# Security Configuration
JWT_SECRET=YOUR_SECURE_JWT_SECRET_KEY_MINIMUM_32_CHARACTERS
ENCRYPTION_KEY=YOUR_SECURE_ENCRYPTION_KEY_32_CHARACTERS
SESSION_SECRET=YOUR_SECURE_SESSION_SECRET_32_CHARACTERS

# API Configuration
API_URL=https://api.betbet.com
WS_URL=wss://ws.betbet.com
FRONTEND_URL=https://betbet.com

# Service Ports
FRONTEND_PORT=3000
GAMING_ENGINE_PORT=8001
WEBSOCKET_PORT=8080
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Monitoring and Logging
SENTRY_DSN=YOUR_SENTRY_DSN_FOR_ERROR_TRACKING
LOG_LEVEL=INFO

# Grafana Configuration
GRAFANA_ADMIN_PASSWORD=YOUR_SECURE_GRAFANA_PASSWORD
GRAFANA_SECRET_KEY=YOUR_SECURE_GRAFANA_SECRET_KEY
GRAFANA_ROOT_URL=https://grafana.betbet.com

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/nginx/ssl/betbet.crt
SSL_KEY_PATH=/etc/nginx/ssl/betbet.key

# Performance Configuration
MAX_WORKERS=4
WORKER_CONNECTIONS=1000
CONNECTION_POOL_SIZE=20
MAX_OVERFLOW=30

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=YOUR_EMAIL_PASSWORD
EMAIL_USE_TLS=true

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Security Headers
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;"
HSTS_MAX_AGE=31536000

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_MONITORING=true

# External Services
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_STRIPE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY=sk_live_YOUR_STRIPE_SECRET_KEY

# CDN Configuration
CDN_URL=https://cdn.betbet.com
STATIC_URL=https://static.betbet.com

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Deployment Configuration
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_COMMIT_SHA=YOUR_GIT_COMMIT_SHA