# BetBet Platform - Production Environment Configuration
# WARNING: This file contains sensitive information for production deployment
# Copy this file to .env.production and replace all placeholder values with secure credentials

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARN
NODE_ENV=production

# CRITICAL: Database Configuration - Use secure, randomly generated credentials
POSTGRES_DB=betbet_platform
POSTGRES_USER=betbet_prod_user
POSTGRES_PASSWORD=REPLACE_WITH_SECURE_64_CHAR_PASSWORD
POSTGRES_HOST=postgres
POSTGRES_PORT=5433

# Database URLs (automatically generated from above)
DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
DATABASE_READ_URL=${DATABASE_URL}

# CRITICAL: Redis Configuration - Use secure password
REDIS_PASSWORD=REPLACE_WITH_SECURE_REDIS_PASSWORD
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# CRITICAL: JWT Configuration - Use cryptographically secure secret (128+ characters)
JWT_SECRET_KEY=REPLACE_WITH_CRYPTOGRAPHICALLY_SECURE_JWT_SECRET_128_CHARS_MIN
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15  # Shorter in production
REFRESH_TOKEN_EXPIRE_DAYS=7

# Production Authentication - Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_PRODUCTION_CLERK_KEY
CLERK_SECRET_KEY=sk_live_YOUR_PRODUCTION_CLERK_SECRET

# Frontend URLs - Replace with your actual production domains
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://ws.yourdomain.com
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_ADMIN_URL=https://admin.yourdomain.com

# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# External API Keys - Replace with production values
OPENAI_API_KEY=sk-YOUR_PRODUCTION_OPENAI_API_KEY
SPORTS_API_KEY=YOUR_PRODUCTION_SPORTS_API_KEY
ODDS_API_KEY=YOUR_PRODUCTION_ODDS_API_KEY

# Security Settings - Production optimized
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION_MINUTES=60
MFA_ENABLED=true
MFA_ISSUER=BetBet Platform

# Performance Settings - Production optimized
API_TIMEOUT_SECONDS=15
DB_QUERY_TIMEOUT_SECONDS=5
CACHE_TTL_SECONDS=600
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
REDIS_MAX_CONNECTIONS=20

# WebSocket Configuration
WS_MAX_CONNECTIONS_PER_USER=3
WS_HEARTBEAT_INTERVAL=60
WS_CONNECTION_TIMEOUT=180

# Gaming Configuration
GAME_SESSION_TIMEOUT_MINUTES=15
MAX_PARTICIPANTS_PER_SESSION=8
PLATFORM_FEE_PERCENTAGE=0.05
MIN_BET_AMOUNT=1.00
MAX_BET_AMOUNT=5000.00

# Rate Limiting - Stricter in production
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=50

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Health Checks
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# SSL/TLS (if using HTTPS internally)
SSL_CERT_PATH=/certs/cert.pem
SSL_KEY_PATH=/certs/key.pem

# Email Configuration (for notifications)
SMTP_HOST=smtp.yourmailprovider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=YOUR_EMAIL_APP_PASSWORD

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Feature Flags - Production settings
ENABLE_MOBILE_APP=true
ENABLE_ADMIN_PANEL=true
ENABLE_TRADING=true
ENABLE_EXPERT_ANALYSIS=true
ENABLE_SPORTS_ANALYSIS=true
ENABLE_LEADERBOARDS=true

# SECURITY WARNING:
# - Ensure all REPLACE_WITH_* values are replaced with secure credentials
# - Never commit this file with real credentials to version control
# - Use a secrets management system in production (AWS Secrets Manager, HashiCorp Vault, etc.)
# - Rotate all credentials regularly
# - Use HTTPS/TLS for all external communications